name: Draft FNOL Intake Python3 CI/CD

on:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
      - closed
    branches: 
      - main
    paths:
      - src/nirvana/claims/lambdas/draft-fnol-intake/**

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  id-token: write

defaults:
  run:
    shell: bash

env:
  AWS_ACCOUNT_ID: ************
  ECR_REPO: draft-fnol-email-streamline
  LAMBDA_ARN: arn:aws:lambda:us-east-2:************:function:draft-fnol-email-streamline-py
  CODE_DIRECTORY: src/nirvana/claims/lambdas/draft-fnol-intake

jobs:
  build:
    if: github.event.action != 'closed' || (github.event.action == 'closed' && github.event.pull_request.merged)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            ${{ env.CODE_DIRECTORY }}
            .github
      
      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials
        with:
          aws-account-id: ${{ env.AWS_ACCOUNT_ID }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build image and export
        uses: docker/build-push-action@v6
        with:
          tags: draft-fnol-test:latest
          outputs: type=docker,dest=/tmp/draft-fnol-test.tar
          context: ${{ env.CODE_DIRECTORY }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: draft-fnol-test
          path: /tmp/draft-fnol-test.tar

  lint:
    if: github.event.action != 'closed' || (github.event.action == 'closed' && github.event.pull_request.merged)
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v5
        with:
          name: draft-fnol-test
          path: /tmp

      - name: Load image
        run: |
          docker load --input /tmp/draft-fnol-test.tar

      - name: Lint
        run: |
          docker run --rm --entrypoint python  draft-fnol-test -m ruff check

  push:
    if: github.event.pull_request.merged
    runs-on: ubuntu-latest
    needs: build
    outputs:
      url: ${{ steps.push-image.outputs.url }}
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v5
        with:
          name: draft-fnol-test
          path: /tmp

      - name: Load image
        run: |
          docker load --input /tmp/draft-fnol-test.tar

      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials
        with:
          aws-account-id: ${{ env.AWS_ACCOUNT_ID }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Build, tag, and push docker image to Amazon ECR
        id: push-image
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: ${{ env.ECR_REPO }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker tag draft-fnol-test $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG

          echo "url=$REGISTRY/$REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

  re-deploy:
    if: github.event.pull_request.merged
    runs-on: ubuntu-latest
    needs: push
    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            .github

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials
        with:
          aws-account-id: ${{ env.AWS_ACCOUNT_ID }}

      - name: Re-deploy AWS Lambda
        run: |
          aws lambda update-function-code \
            --function-name ${{ env.LAMBDA_ARN }} \
            --image-uri ${{ needs.push.outputs.url }} > /dev/null
          
      - name: Wait until AWS Lambda to become active
        run: |
          state="unknown"
          while [[ $state != "Successful" ]]
          do
            state=$(
              aws lambda get-function --function-name ${{ env.LAMBDA_ARN }} |\
              jq -r '.Configuration.LastUpdateStatus'
            )
            echo "Latest state = $state"
          done

      - name: Send Slack notification
        if: ${{ success() }}
        uses: slackapi/slack-github-action@v2.1.0
        with:
          webhook: ${{ secrets.INSURED_ENG_NOTIFICATION_SLACK_WEBHOOK_URL }}
          webhook-type: webhook-trigger
          payload: |
            status: "✅ Success"
            app_name: "Finola James Lambda"
            commit_hash: "${{ github.sha }}"
            branch_name: "${{ github.ref }}"

      - name: Send PagerDuty alert
        if: ${{ failure() }}
        shell: bash
        run: |
          curl --request 'POST' \
          --url 'https://events.pagerduty.com/v2/enqueue' \
          --header 'Content-Type: application/json' \
          --data '{
            "payload": {
                "summary": "❌ Failed to deploy Finola James Lambda Service",
                "severity": "critical",
                "source": "github-actions"
            },
            "routing_key": ${{ secrets.PAGERDUTY_CLAIMS_ROUTING_KEY }},
            "event_action": "trigger"
          }'
