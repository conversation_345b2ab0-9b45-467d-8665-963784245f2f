package converters

import (
	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/business-auto/enums"
	ba_proto "nirvanatech.com/nirvana/business-auto/model/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	nirvanaapp_enums "nirvanatech.com/nirvana/nirvanaapp/enums"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

// ConvertIndustryType converts IndustryClassification to ba_proto.IndustryType
// Always returns a valid value, falling back to UNSPECIFIED for unknown values
func ConvertIndustryType(classification *enums.IndustryClassification) ba_proto.IndustryType {
	if classification == nil {
		return ba_proto.IndustryType_INDUSTRY_TYPE_UNSPECIFIED
	}

	switch *classification {
	case enums.IndustryClassificationInvalid:
		return ba_proto.IndustryType_INDUSTRY_TYPE_UNSPECIFIED
	case enums.IndustryClassificationContractorTrucks:
		return ba_proto.IndustryType_INDUSTRY_TYPE_CONTRACTOR_TRUCKS
	case enums.IndustryClassificationSpecializedDeliveryVehicles:
		return ba_proto.IndustryType_INDUSTRY_TYPE_SPECIALIZED_DELIVERY_VEHICLES
	case enums.IndustryClassificationCourierServiceVehicles:
		return ba_proto.IndustryType_INDUSTRY_TYPE_COURIER_SERVICE_VEHICLES
	case enums.IndustryClassificationFoodDeliveryTrucks:
		return ba_proto.IndustryType_INDUSTRY_TYPE_FOOD_DELIVERY_TRUCKS
	case enums.IndustryClassificationWasteDisposalTrucks:
		return ba_proto.IndustryType_INDUSTRY_TYPE_WASTE_DISPOSAL_TRUCKS
	case enums.IndustryClassificationWasteOilLiquidWasteTransporter:
		return ba_proto.IndustryType_INDUSTRY_TYPE_WASTE_OIL_AND_OTHER_LIQUID_WASTE_TRANSPORTATION
	case enums.IndustryClassificationFarmerTrucks:
		return ba_proto.IndustryType_INDUSTRY_TYPE_FARMER_TRUCKS
	case enums.IndustryClassificationCementMixers:
		return ba_proto.IndustryType_INDUSTRY_TYPE_CEMENT_MIXERS
	case enums.IndustryClassificationHouseMovers:
		return ba_proto.IndustryType_INDUSTRY_TYPE_HOUSE_MOVING
	case enums.IndustryClassificationMovingOperations:
		return ba_proto.IndustryType_INDUSTRY_TYPE_MOVING_OPERATIONS
	case enums.IndustryClassificationLawnTreeServiceTrucks:
		return ba_proto.IndustryType_INDUSTRY_TYPE_LAWN_TREE_SERVICE_TRUCKS
	case enums.IndustryClassificationCatererVehicles:
		return ba_proto.IndustryType_INDUSTRY_TYPE_CATERER_VEHICLES
	case enums.IndustryClassificationMobileConcessionTrucks:
		return ba_proto.IndustryType_INDUSTRY_TYPE_MOBILE_CONCESSION_TRUCKS
	case enums.IndustryClassificationWholesalersManufacturers:
		return ba_proto.IndustryType_INDUSTRY_TYPE_WHOLESALERS_MANUFACTURERS
	case enums.IndustryClassificationSalvageHaulers:
		return ba_proto.IndustryType_INDUSTRY_TYPE_SALVAGE_HAULERS
	case enums.IndustryClassificationGasOilHaulersNotForHire:
		return ba_proto.IndustryType_INDUSTRY_TYPE_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE
	case enums.IndustryClassificationCarCarriersNotForHire:
		return ba_proto.IndustryType_INDUSTRY_TYPE_CAR_CARRIERS_NOT_FOR_HIRE
	case enums.IndustryClassificationNotOtherwiseClassifiedTruck:
		return ba_proto.IndustryType_INDUSTRY_TYPE_NOT_OTHERWISE_CLASSIFIED_TRUCK
	case enums.IndustryClassificationLoggingTrucksForHire:
		return ba_proto.IndustryType_INDUSTRY_TYPE_LOGGING_TRUCKS_FOR_HIRE
	case enums.IndustryClassificationLoggingTrucksNotForHire:
		return ba_proto.IndustryType_INDUSTRY_TYPE_LOGGING_TRUCKS_NOT_FOR_HIRE
	case enums.IndustryClassificationTowTrucksIncidentalUse:
		return ba_proto.IndustryType_INDUSTRY_TYPE_TOW_TRUCKS_INCIDENTAL_USE
	case enums.IndustryClassificationTowTrucksFullTime:
		return ba_proto.IndustryType_INDUSTRY_TYPE_TOW_TRUCKS_FULL_TIME
	case enums.IndustryClassificationDumpingOperationsForHire:
		return ba_proto.IndustryType_INDUSTRY_TYPE_DUMPING_OPERATIONS_FOR_HIRE
	case enums.IndustryClassificationDumpingOperationsNotForHire:
		return ba_proto.IndustryType_INDUSTRY_TYPE_DUMPING_OPERATIONS_NOT_FOR_HIRE
	case enums.IndustryClassificationServiceUse:
		return ba_proto.IndustryType_INDUSTRY_TYPE_SERVICE_USE
	default:
		return ba_proto.IndustryType_INDUSTRY_TYPE_UNSPECIFIED
	}
}

// ConvertIndustryTypeSlice converts a slice of IndustryClassification to a slice of ba_proto.IndustryType
func ConvertIndustryTypeSlice(classifications []enums.IndustryClassification) []ba_proto.IndustryType {
	return slice_utils.Map(classifications, func(classification enums.IndustryClassification) ba_proto.IndustryType {
		return ConvertIndustryType(&classification)
	})
}

// ConvertBusinessUse converts BusinessUse to ba_proto.BusinessUse
// Always returns a valid value, falling back to UNSPECIFIED for unknown values
func ConvertBusinessUse(businessUse enums.BusinessUse) ba_proto.BusinessUse {
	switch businessUse {
	case enums.BusinessUseInvalid:
		return ba_proto.BusinessUse_BUSINESS_USE_UNSPECIFIED
	case enums.BusinessUseCommercial:
		return ba_proto.BusinessUse_BUSINESS_USE_COMMERCIAL
	case enums.BusinessUseRetail:
		return ba_proto.BusinessUse_BUSINESS_USE_RETAIL
	case enums.BusinessUseService:
		return ba_proto.BusinessUse_BUSINESS_USE_SERVICE
	default:
		return ba_proto.BusinessUse_BUSINESS_USE_UNSPECIFIED
	}
}

// ConvertBusinessUseWithError converts BusinessUse to ba_proto.BusinessUse with explicit error handling
func ConvertBusinessUseWithError(businessUse enums.BusinessUse) (ba_proto.BusinessUse, error) {
	switch businessUse {
	case enums.BusinessUseService:
		return ba_proto.BusinessUse_BUSINESS_USE_SERVICE, nil
	case enums.BusinessUseRetail:
		return ba_proto.BusinessUse_BUSINESS_USE_RETAIL, nil
	case enums.BusinessUseCommercial:
		return ba_proto.BusinessUse_BUSINESS_USE_COMMERCIAL, nil
	case enums.BusinessUseInvalid:
		return ba_proto.BusinessUse_BUSINESS_USE_UNSPECIFIED, errors.Newf("unsupported business use: %v", businessUse)
	default:
		return ba_proto.BusinessUse_BUSINESS_USE_UNSPECIFIED, errors.Newf("unsupported business use: %v", businessUse)
	}
}

// ConvertVehicleUse converts VehicleUse to ba_proto.VehicleUse
// Always returns a valid value, falling back to UNSPECIFIED for unknown values
func ConvertVehicleUse(vehicleUse enums.VehicleUse) ba_proto.VehicleUse {
	switch vehicleUse {
	case enums.VehicleUseInvalid:
		return ba_proto.VehicleUse_VEHICLE_USE_UNSPECIFIED
	case enums.VehicleUseTowingOperations:
		return ba_proto.VehicleUse_VEHICLE_USE_TOWING_OPERATIONS
	case enums.VehicleUseDumpingOperations:
		return ba_proto.VehicleUse_VEHICLE_USE_DUMPING_OPERATIONS
	case enums.VehicleUseLoggingOperations:
		return ba_proto.VehicleUse_VEHICLE_USE_LOGGING_OPERATIONS
	case enums.VehicleUseOtherOperations:
		return ba_proto.VehicleUse_VEHICLE_USE_OTHER_OPERATIONS
	default:
		return ba_proto.VehicleUse_VEHICLE_USE_UNSPECIFIED
	}
}

// ConvertWeightClass converts WeightClass to ba_proto.VehicleWeightClass
// Always returns a valid value, falling back to UNSPECIFIED for unknown values
func ConvertWeightClass(weightClass enums.WeightClass) ba_proto.VehicleWeightClass {
	switch weightClass {
	case enums.WeightClassInvalid:
		return ba_proto.VehicleWeightClass_VEHICLE_WEIGHT_CLASS_UNSPECIFIED
	case enums.WeightClassLight:
		return ba_proto.VehicleWeightClass_VEHICLE_WEIGHT_CLASS_LIGHT
	case enums.WeightClassMedium:
		return ba_proto.VehicleWeightClass_VEHICLE_WEIGHT_CLASS_MEDIUM
	case enums.WeightClassHeavy:
		return ba_proto.VehicleWeightClass_VEHICLE_WEIGHT_CLASS_HEAVY
	case enums.WeightClassExtraHeavy:
		return ba_proto.VehicleWeightClass_VEHICLE_WEIGHT_CLASS_EXTRA_HEAVY
	default:
		return ba_proto.VehicleWeightClass_VEHICLE_WEIGHT_CLASS_UNSPECIFIED
	}
}

// ConvertWeightClassToVehicleSize converts WeightClass to ba_proto.VehicleSize with explicit error handling
func ConvertWeightClassToVehicleSize(weightClass enums.WeightClass) (ba_proto.VehicleSize, error) {
	switch weightClass {
	case enums.WeightClassLight:
		return ba_proto.VehicleSize_VEHICLE_SIZE_LIGHT_TRUCK, nil
	case enums.WeightClassMedium:
		return ba_proto.VehicleSize_VEHICLE_SIZE_MEDIUM_TRUCK, nil
	case enums.WeightClassHeavy:
		return ba_proto.VehicleSize_VEHICLE_SIZE_HEAVY_TRUCK, nil
	case enums.WeightClassExtraHeavy:
		return ba_proto.VehicleSize_VEHICLE_SIZE_EXTRA_HEAVY_TRUCK, nil
	case enums.WeightClassInvalid:
		return ba_proto.VehicleSize_VEHICLE_SIZE_UNSPECIFIED, errors.Newf("unsupported weight class: %v", weightClass)
		// TODO: cover new enums as well
	default:
		return ba_proto.VehicleSize_VEHICLE_SIZE_UNSPECIFIED, errors.Newf("unsupported weight class: %v", weightClass)
	}
}

// ConvertStateUsage converts StateUsage to ba_proto.StateUsage
// Always returns a valid value, falling back to UNSPECIFIED for unknown values
func ConvertStateUsage(stateUsage enums.StateUsage) ba_proto.StateUsage {
	switch stateUsage {
	case enums.StateUsageInvalid:
		return ba_proto.StateUsage_STATE_USAGE_UNSPECIFIED
	case enums.StateUsageIntrastate:
		return ba_proto.StateUsage_STATE_USAGE_INTRASTATE
	case enums.StateUsageInterstate:
		return ba_proto.StateUsage_STATE_USAGE_INTERSTATE
	default:
		return ba_proto.StateUsage_STATE_USAGE_UNSPECIFIED
	}
}

// ConvertStateUsageWithError converts StateUsage to ba_proto.StateUsage with explicit error handling
func ConvertStateUsageWithError(stateUsage enums.StateUsage) (ba_proto.StateUsage, error) {
	switch stateUsage {
	case enums.StateUsageIntrastate:
		return ba_proto.StateUsage_STATE_USAGE_INTRASTATE, nil
	case enums.StateUsageInterstate:
		return ba_proto.StateUsage_STATE_USAGE_INTERSTATE, nil
	case enums.StateUsageInvalid:
		return ba_proto.StateUsage_STATE_USAGE_UNSPECIFIED, errors.Newf("unsupported state usage: %v", stateUsage)
	default:
		return ba_proto.StateUsage_STATE_USAGE_UNSPECIFIED, errors.Newf("unsupported state usage: %v", stateUsage)
	}
}

// ConvertRadiusClassification converts RadiusClassification to ba_proto.RadiusOfOperation
// Always returns a valid value, falling back to UNSPECIFIED for unknown values
func ConvertRadiusClassification(radiusClassification enums.RadiusClassification) ba_proto.RadiusOfOperation {
	switch radiusClassification {
	case enums.RadiusClassificationInvalid:
		return ba_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_UNSPECIFIED
	case enums.RadiusClassification0To100:
		return ba_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_O_TO_100_MILES
	case enums.RadiusClassification101To300:
		return ba_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_101_TO_300_MILES
	case enums.RadiusClassificationGreaterThan301:
		return ba_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_301_PLUS_MILES
	default:
		return ba_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_UNSPECIFIED
	}
}

// ConvertRadiusClassificationWithError converts RadiusClassification to ba_proto.RadiusOfOperation with explicit error handling
func ConvertRadiusClassificationWithError(radiusClassification enums.RadiusClassification) (ba_proto.RadiusOfOperation, error) {
	switch radiusClassification {
	case enums.RadiusClassification0To100:
		return ba_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_O_TO_100_MILES, nil
	case enums.RadiusClassification101To300:
		return ba_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_101_TO_300_MILES, nil
	case enums.RadiusClassificationGreaterThan301:
		return ba_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_301_PLUS_MILES, nil
	case enums.RadiusClassificationInvalid:
		return ba_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_UNSPECIFIED, errors.Newf("unsupported radius classification: %v", radiusClassification)
	default:
		return ba_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_UNSPECIFIED, errors.Newf("unsupported radius classification: %v", radiusClassification)
	}
}

// ConvertSpecialtyVehicleType converts SpecialtyVehicleType to ba_proto.VehicleSpecialtyType
func ConvertSpecialtyVehicleType(specialtyVehicleType enums.SpecialtyVehicleType) ba_proto.VehicleSpecialtyType {
	var result ba_proto.VehicleSpecialtyType
	switch specialtyVehicleType {
	case enums.SpecialtyVehicleTypeBoomTruck0To49Feet:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_0_TO_49_FEET
	case enums.SpecialtyVehicleTypeBoomTruck50To75Feet:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_50_TO_75_FEET
	case enums.SpecialtyVehicleTypeBoomTruck76To100Feet:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_76_TO_100_FEET
	case enums.SpecialtyVehicleTypeBoomTruck101To120Feet:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_101_TO_120_FEET
	case enums.SpecialtyVehicleTypeBoomTruckOver120Feet:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_121_PLUS_FEET
	case enums.SpecialtyVehicleTypeCableTelecomUtilityContractors:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_CABLE_TELECOM_AND_OTHER_UTILITY_CONTRACTORS
	case enums.SpecialtyVehicleTypeExcavatingDrillingLightMediumHeavyTrucks:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_EXCAVATING_DRILLING_AND_LAND_GRADING_CONTRACTORS_LIGHT_MEDIUM_AND_HEAVY_TRUCKS
	case enums.SpecialtyVehicleTypeExcavatingDrillingAllOtherUnits:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_EXCAVATING_DRILLING_AND_LAND_GRADING_CONTRACTORS_ALL_OTHER_UNITS
	case enums.SpecialtyVehicleTypePetroleumDistributionContractors:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_PETROLEUM_DISTRIBUTION_CONTRACTORS
	case enums.SpecialtyVehicleTypeHotOilLiquidAsphaltTrucksOilField:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_HOT_OIL_LIQUID_ASPHALT_TRUCKS_OIL_FIELD_OR_ENERGY
	case enums.SpecialtyVehicleTypeAllOtherOilFieldBodyTypes:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_OIL_FIELD_OR_ENERGY_BODY_TYPES
	case enums.SpecialtyVehicleTypeSepticTankServiceTrucksIndividual:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_SEPTIC_TANK_SERVICE_TRUCKS_INDIVIDUAL_NAMED_INSURED
	case enums.SpecialtyVehicleTypeSepticTankServiceTrucksAllOther:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_SEPTIC_TANK_SERVICE_TRUCKS_ALL_OTHER_NAMED_INSURED
	case enums.SpecialtyVehicleTypeWeldersMetalworkingContractors:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_WELDERS_AND_METALWORKING_CONTRACTORS
	case enums.SpecialtyVehicleTypeAllOtherContractorBodyTypes:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_CONTRACTOR_BODY_TYPES
	case enums.SpecialtyVehicleTypeHotOilLiquidAsphaltTrucksAllOther:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_HOT_OIL_LIQUID_ASPHALT_TRUCKS_ALL_OTHER_CONTRACTORS
	case enums.SpecialtyVehicleTypeSpecializedDeliveryVehicles:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_SPECIALIZED_DELIVERY_VEHICLES
	case enums.SpecialtyVehicleTypeCourierServiceVehicles:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_COURIER_SERVICE_VEHICLES
	case enums.SpecialtyVehicleTypeFoodDeliveryTrucks:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_FOOD_DELIVERY_TRUCKS
	case enums.SpecialtyVehicleTypeWasteDisposalTrucks:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_WASTE_DISPOSAL_TRUCKS
	case enums.SpecialtyVehicleTypeWasteOilLiquidWasteTransporters:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_WASTE_OIL_AND_LIQUID_WASTE_TRANSPORTERS
	case enums.SpecialtyVehicleTypeHarvesterGoatTrucks:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_HARVESTER_GOAT_TRUCKS_FARMER_TRUCKS
	case enums.SpecialtyVehicleTypeAllOtherFarmerTrucks:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_FARMER_TRUCKS
	case enums.SpecialtyVehicleTypeHouseMovers:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_HOUSE_MOVERS
	case enums.SpecialtyVehicleTypeMovingOperations:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_MOVING_OPERATIONS
	case enums.SpecialtyVehicleTypeLawnTreeServiceTrucks:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_LAWN_TREE_SERVICE_TRUCKS
	case enums.SpecialtyVehicleTypeCatererVehicles:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_CATERER_VEHICLES
	case enums.SpecialtyVehicleTypeMobileConcessionTruckInVehicleVending:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_IN_VEHICLE_VENDING
	case enums.SpecialtyVehicleTypeMobileConcessionTruckOtherFoodVending:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_OTHER_FOOD_VENDING
	case enums.SpecialtyVehicleTypeMobileConcessionTruckNoFoodSales:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_NO_FOOD_SALES
	case enums.SpecialtyVehicleTypeWholesalersManufacturers:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_WHOLESALERS_AND_MANUFACTURERS
	case enums.SpecialtyVehicleTypeGasOilLPGPropaneBottled:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_LPG_PROPANE_HAULERS_BOTTLED
	case enums.SpecialtyVehicleTypeGasOilLPGPropaneBulk:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_LPG_PROPANE_HAULERS_BULK
	case enums.SpecialtyVehicleTypeGasOilCrudeOilHaulers:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_CRUDE_OIL_HAULERS
	case enums.SpecialtyVehicleTypeGasOilFuelOilHaulers:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_FUEL_OIL_HAULERS
	case enums.SpecialtyVehicleTypeGasOilAllOtherGasHaulers:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_ALL_OTHER_GAS_HAULERS
	case enums.SpecialtyVehicleTypeSalvageHaulers:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_SALVAGE_HAULERS
	case enums.SpecialtyVehicleTypeCarCarriersNotForHire:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_CAR_CARRIERS_NOT_FOR_HIRE
	case enums.SpecialtyVehicleTypeServiceUseVehicles:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_SERVICE_USE_VEHICLES
	case enums.SpecialtyVehicleTypeFireworkHaulersNotForHire:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_FIREWORK_HAULERS_NOT_FOR_HIRE
	case enums.SpecialtyVehicleTypeCustomHarvesters:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_CUSTOM_HARVESTERS
	case enums.SpecialtyVehicleTypeDriverTrainingTrucksTractors:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_DRIVER_TRAINING_TRUCKS_TRACTORS
	case enums.SpecialtyVehicleTypeStreetSweepers:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_STREET_SWEEPERS
	case enums.SpecialtyVehicleTypeRentalEquipmentProvider:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_RENTAL_EQUIPMENT_PROVIDER
	case enums.SpecialtyVehicleTypeNotOtherwiseClassifiedTrucks:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_NOT_OTHERWISE_CLASSIFIED_TRUCKS
	case enums.SpecialtyVehicleTypeArtisanContractors:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ARTISAN_CONTRACTORS
	case enums.SpecialtyVehicleTypeCarpentryContractors:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_CARPENTRY_CONTRACTORS
	case enums.SpecialtyVehicleTypeExteriorBuildingConstructionContractors:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_EXTERIOR_BUILDING_WORK_AND_CONSTRUCTION_CONTRACTORS
	case enums.SpecialtyVehicleTypeRoadConstructionContractors:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ROAD_CONSTRUCTION_CONTRACTORS
	case enums.SpecialtyVehicleTypeTrafficControlContractors:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_TRAFFIC_CONTROL_CONTRACTORS
	case enums.SpecialtyVehicleTypeMobileMechanicContractors:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_MECHANIC_CONTRACTORS
	case enums.SpecialtyVehicleTypeAllOtherUnits:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_UNITS
	default:
		result = ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_TYPE_UNSPECIFIED
	}
	return result
}

// ConvertTrailerType converts TrailerType to ba_proto.TrailerType
// Always returns a valid value, falling back to UNSPECIFIED for unknown values
func ConvertTrailerType(trailerType *enums.TrailerType) *ba_proto.TrailerType {
	if trailerType == nil {
		return nil
	}

	var result ba_proto.TrailerType
	switch *trailerType {
	case enums.TrailerTypeInvalid:
		result = ba_proto.TrailerType_TRAILER_TYPE_UNSPECIFIED
	case enums.TrailerTypeSemiTrailers:
		result = ba_proto.TrailerType_TRAILER_TYPE_SEMI_TRAILER
	case enums.TrailerTypeFullTrailers:
		result = ba_proto.TrailerType_TRAILER_TYPE_FULL_TRAILER
	case enums.TrailerTypeServiceUtilityTrailers:
		result = ba_proto.TrailerType_TRAILER_TYPE_SERVICE_UTILITY_TRAILER
	case enums.TrailerTypeCustomHarvesterTrailers:
		result = ba_proto.TrailerType_TRAILER_TYPE_CUSTOM_HARVESTER_TRAILER
	default:
		result = ba_proto.TrailerType_TRAILER_TYPE_UNSPECIFIED
	}
	return &result
}

// ConvertQualityRatingGrade converts QualityRatingGrade to ba_proto.QualityRatingGrade
// Always returns a valid value, falling back to UNSPECIFIED for unknown values
func ConvertQualityRatingGrade(grade enums.QualityRatingGrade) ba_proto.QualityRatingGrade {
	switch grade {
	case enums.QualityRatingGradeInvalid:
		return ba_proto.QualityRatingGrade_QUALITY_RATING_GRADE_UNSPECIFIED
	case enums.QualityRatingGradeA:
		return ba_proto.QualityRatingGrade_QUALITY_RATING_GRADE_A
	case enums.QualityRatingGradeB:
		return ba_proto.QualityRatingGrade_QUALITY_RATING_GRADE_B
	case enums.QualityRatingGradeC:
		return ba_proto.QualityRatingGrade_QUALITY_RATING_GRADE_C
	case enums.QualityRatingGradeD:
		return ba_proto.QualityRatingGrade_QUALITY_RATING_GRADE_D
	case enums.QualityRatingGradeE:
		return ba_proto.QualityRatingGrade_QUALITY_RATING_GRADE_E
	case enums.QualityRatingGradeF:
		return ba_proto.QualityRatingGrade_QUALITY_RATING_GRADE_F
	default:
		return ba_proto.QualityRatingGrade_QUALITY_RATING_GRADE_UNSPECIFIED
	}
}

// Pricing-specific converters with explicit error handling

// ConvertVehicleTypeWithError converts nirvanaapp_enums.VehicleType and optional TrailerType to ba_proto.VehicleType with explicit error handling
// This handles the mapping from the product's two-level hierarchy (VehicleType + TrailerType) to the rating system's flat enum
func ConvertVehicleTypeWithError(vehicleType nirvanaapp_enums.VehicleType, trailerType *enums.TrailerType) (ba_proto.VehicleType, error) {
	// NOTE: The rating service uses a flat vehicle-type enum. We map the product's
	// two-level hierarchy (VehicleType + optional TrailerType) into that flat list.
	// Where the mapping is not yet agreed upon we surface an explicit error so we
	// don't silently mis-classify a vehicle. Follow-up items:
	//   1. How should Pickup trucks be represented?  Options discussed so far:
	//      a) map to TRUCK, b) map to a future PRIVATE_PASSENGER value, or
	//      c) extend the rating enum with a dedicated PICKUP value.
	//   2. FULL_TRAILER and CUSTOM_HARVESTER_TRAILER sub-types currently default
	//      to the generic TRAILER value.  Confirm with actuarial / pricing if a
	//      more granular mapping is required.
	//   3. The product model doesn't have a "Private Passenger" vehicle type but
	//      the rating enum does.  Decide whether we need a back-mapping in the
	//      other direction.
	switch vehicleType {
	case nirvanaapp_enums.VehicleTypeTruck:
		return ba_proto.VehicleType_VEHICLE_TYPE_TRUCK, nil
	case nirvanaapp_enums.VehicleTypeTractor:
		return ba_proto.VehicleType_VEHICLE_TYPE_TRACTOR, nil
	case nirvanaapp_enums.VehicleTypeTrailer:
		// For trailers, we need to check the sub-type
		if trailerType == nil {
			// Generic trailer with no sub-type
			return ba_proto.VehicleType_VEHICLE_TYPE_TRAILER, nil
		}

		switch *trailerType {
		case enums.TrailerTypeSemiTrailers:
			return ba_proto.VehicleType_VEHICLE_TYPE_SEMI_TRAILER, nil
		case enums.TrailerTypeServiceUtilityTrailers:
			return ba_proto.VehicleType_VEHICLE_TYPE_SERVICE_UTILITY_TRAILER, nil
		case enums.TrailerTypeFullTrailers, enums.TrailerTypeCustomHarvesterTrailers:
			// Map to generic trailer for now - these may need special handling via VehicleSize
			return ba_proto.VehicleType_VEHICLE_TYPE_TRAILER, nil
		case enums.TrailerTypeInvalid:
			return ba_proto.VehicleType_VEHICLE_TYPE_UNSPECIFIED, errors.Newf("invalid trailer type: %v", *trailerType)
		default:
			return ba_proto.VehicleType_VEHICLE_TYPE_UNSPECIFIED, errors.Newf("unsupported trailer type: %v", *trailerType)
		}
	case nirvanaapp_enums.VehicleTypePickup:
		// TODO: Clarify mapping - should this be TRUCK, PRIVATE_PASSENGER, or should RateML add PICKUP?
		return ba_proto.VehicleType_VEHICLE_TYPE_UNSPECIFIED, errors.Newf("vehicle type Pickup mapping needs clarification - should this map to TRUCK, PRIVATE_PASSENGER, or should rating add a dedicated PICKUP value?")
	case nirvanaapp_enums.VehicleTypeInvalid:
		return ba_proto.VehicleType_VEHICLE_TYPE_UNSPECIFIED, errors.Newf("invalid vehicle type: %v", vehicleType)
	default:
		return ba_proto.VehicleType_VEHICLE_TYPE_UNSPECIFIED, errors.Newf("unsupported vehicle type: %v", vehicleType)
	}
}

// ConvertSpecialtyVehicleTypeWithError converts SpecialtyVehicleType to ba_proto.VehicleSpecialtyType with explicit error handling
func ConvertSpecialtyVehicleTypeWithError(specialtyType enums.SpecialtyVehicleType) (ba_proto.VehicleSpecialtyType, error) {
	switch specialtyType {
	// Boom Trucks
	case enums.SpecialtyVehicleTypeBoomTruck0To49Feet:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_0_TO_49_FEET, nil
	case enums.SpecialtyVehicleTypeBoomTruck50To75Feet:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_50_TO_75_FEET, nil
	case enums.SpecialtyVehicleTypeBoomTruck76To100Feet:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_76_TO_100_FEET, nil
	case enums.SpecialtyVehicleTypeBoomTruck101To120Feet:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_101_TO_120_FEET, nil
	case enums.SpecialtyVehicleTypeBoomTruckOver120Feet:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_121_PLUS_FEET, nil

	// Contractor Types
	case enums.SpecialtyVehicleTypeCableTelecomUtilityContractors:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_CABLE_TELECOM_AND_OTHER_UTILITY_CONTRACTORS, nil
	case enums.SpecialtyVehicleTypeExcavatingDrillingLightMediumHeavyTrucks:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_EXCAVATING_DRILLING_AND_LAND_GRADING_CONTRACTORS_LIGHT_MEDIUM_AND_HEAVY_TRUCKS, nil
	case enums.SpecialtyVehicleTypeExcavatingDrillingAllOtherUnits:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_EXCAVATING_DRILLING_AND_LAND_GRADING_CONTRACTORS_ALL_OTHER_UNITS, nil
	case enums.SpecialtyVehicleTypePetroleumDistributionContractors:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_PETROLEUM_DISTRIBUTION_CONTRACTORS, nil
	case enums.SpecialtyVehicleTypeWeldersMetalworkingContractors:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_WELDERS_AND_METALWORKING_CONTRACTORS, nil
	case enums.SpecialtyVehicleTypeAllOtherContractorBodyTypes:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_CONTRACTOR_BODY_TYPES, nil
	case enums.SpecialtyVehicleTypeArtisanContractors:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ARTISAN_CONTRACTORS, nil
	case enums.SpecialtyVehicleTypeCarpentryContractors:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_CARPENTRY_CONTRACTORS, nil
	case enums.SpecialtyVehicleTypeExteriorBuildingConstructionContractors:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_EXTERIOR_BUILDING_WORK_AND_CONSTRUCTION_CONTRACTORS, nil
	case enums.SpecialtyVehicleTypeRoadConstructionContractors:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ROAD_CONSTRUCTION_CONTRACTORS, nil
	case enums.SpecialtyVehicleTypeTrafficControlContractors:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_TRAFFIC_CONTROL_CONTRACTORS, nil
	case enums.SpecialtyVehicleTypeMobileMechanicContractors:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_MECHANIC_CONTRACTORS, nil

	// Oil Field and Liquid Transport
	case enums.SpecialtyVehicleTypeHotOilLiquidAsphaltTrucksOilField:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_HOT_OIL_LIQUID_ASPHALT_TRUCKS_OIL_FIELD_OR_ENERGY, nil
	case enums.SpecialtyVehicleTypeAllOtherOilFieldBodyTypes:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_OIL_FIELD_OR_ENERGY_BODY_TYPES, nil
	case enums.SpecialtyVehicleTypeHotOilLiquidAsphaltTrucksAllOther:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_HOT_OIL_LIQUID_ASPHALT_TRUCKS_ALL_OTHER_CONTRACTORS, nil
	case enums.SpecialtyVehicleTypeSepticTankServiceTrucksIndividual:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_SEPTIC_TANK_SERVICE_TRUCKS_INDIVIDUAL_NAMED_INSURED, nil
	case enums.SpecialtyVehicleTypeSepticTankServiceTrucksAllOther:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_SEPTIC_TANK_SERVICE_TRUCKS_ALL_OTHER_NAMED_INSURED, nil
	case enums.SpecialtyVehicleTypeWasteOilLiquidWasteTransporters:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_WASTE_OIL_AND_LIQUID_WASTE_TRANSPORTERS, nil

	// Delivery and Transport
	case enums.SpecialtyVehicleTypeSpecializedDeliveryVehicles:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_SPECIALIZED_DELIVERY_VEHICLES, nil
	case enums.SpecialtyVehicleTypeCourierServiceVehicles:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_COURIER_SERVICE_VEHICLES, nil
	case enums.SpecialtyVehicleTypeFoodDeliveryTrucks:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_FOOD_DELIVERY_TRUCKS, nil
	case enums.SpecialtyVehicleTypeWasteDisposalTrucks:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_WASTE_DISPOSAL_TRUCKS, nil
	case enums.SpecialtyVehicleTypeWholesalersManufacturers:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_WHOLESALERS_AND_MANUFACTURERS, nil

	// Agricultural and Farm
	case enums.SpecialtyVehicleTypeHarvesterGoatTrucks:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_HARVESTER_GOAT_TRUCKS_FARMER_TRUCKS, nil
	case enums.SpecialtyVehicleTypeAllOtherFarmerTrucks:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_FARMER_TRUCKS, nil
	case enums.SpecialtyVehicleTypeCustomHarvesters:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_CUSTOM_HARVESTERS, nil

	// Moving and Transportation
	case enums.SpecialtyVehicleTypeHouseMovers:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_HOUSE_MOVERS, nil
	case enums.SpecialtyVehicleTypeMovingOperations:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_MOVING_OPERATIONS, nil
	case enums.SpecialtyVehicleTypeCarCarriersNotForHire:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_CAR_CARRIERS_NOT_FOR_HIRE, nil
	case enums.SpecialtyVehicleTypeSalvageHaulers:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_SALVAGE_HAULERS, nil

	// Service and Maintenance
	case enums.SpecialtyVehicleTypeLawnTreeServiceTrucks:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_LAWN_TREE_SERVICE_TRUCKS, nil
	case enums.SpecialtyVehicleTypeServiceUseVehicles:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_SERVICE_USE_VEHICLES, nil
	case enums.SpecialtyVehicleTypeStreetSweepers:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_STREET_SWEEPERS, nil
	case enums.SpecialtyVehicleTypeRentalEquipmentProvider:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_RENTAL_EQUIPMENT_PROVIDER, nil

	// Food and Catering
	case enums.SpecialtyVehicleTypeCatererVehicles:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_CATERER_VEHICLES, nil
	case enums.SpecialtyVehicleTypeMobileConcessionTruckInVehicleVending:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_IN_VEHICLE_VENDING, nil
	case enums.SpecialtyVehicleTypeMobileConcessionTruckOtherFoodVending:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_OTHER_FOOD_VENDING, nil
	case enums.SpecialtyVehicleTypeMobileConcessionTruckNoFoodSales:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_NO_FOOD_SALES, nil

	// Gas and Oil Haulers
	case enums.SpecialtyVehicleTypeGasOilLPGPropaneBottled:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_LPG_PROPANE_HAULERS_BOTTLED, nil
	case enums.SpecialtyVehicleTypeGasOilLPGPropaneBulk:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_LPG_PROPANE_HAULERS_BULK, nil
	case enums.SpecialtyVehicleTypeGasOilCrudeOilHaulers:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_CRUDE_OIL_HAULERS, nil
	case enums.SpecialtyVehicleTypeGasOilFuelOilHaulers:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_FUEL_OIL_HAULERS, nil
	case enums.SpecialtyVehicleTypeGasOilAllOtherGasHaulers:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_ALL_OTHER_GAS_HAULERS, nil

	// Specialized Operations
	case enums.SpecialtyVehicleTypeFireworkHaulersNotForHire:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_FIREWORK_HAULERS_NOT_FOR_HIRE, nil
	case enums.SpecialtyVehicleTypeDriverTrainingTrucksTractors:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_DRIVER_TRAINING_TRUCKS_TRACTORS, nil
	case enums.SpecialtyVehicleTypeNotOtherwiseClassifiedTrucks:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_NOT_OTHERWISE_CLASSIFIED_TRUCKS, nil
	case enums.SpecialtyVehicleTypeAllOtherUnits:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_UNITS, nil

	case enums.SpecialtyVehicleTypeInvalid:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_TYPE_UNSPECIFIED, errors.Newf("unsupported specialty vehicle type: %v", specialtyType)
	default:
		return ba_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_TYPE_UNSPECIFIED, errors.Newf("unsupported specialty vehicle type: %v", specialtyType)
	}
}

// ConvertCoverageToSubCoverageType converts app_enums.Coverage to ptypes.SubCoverageType
func ConvertCoverageToSubCoverageType(coverage app_enums.Coverage) (ptypes.SubCoverageType, error) {
	// This function now handles all sub-coverages returned by GetSubCoverageFromPrimaryCoverage
	// as well as primary coverages that map to themselves.
	switch coverage {
	// Sub-coverages for Auto Liability (from GetSubCoverageFromPrimaryCoverage)
	case app_enums.CoverageBodilyInjury:
		return ptypes.SubCoverageType_SubCoverageType_BodilyInjury, nil
	case app_enums.CoveragePropertyDamage:
		return ptypes.SubCoverageType_SubCoverageType_PropertyDamage, nil

	// Sub-coverages for Auto Physical Damage (from GetSubCoverageFromPrimaryCoverage)
	case app_enums.CoverageCollision:
		return ptypes.SubCoverageType_SubCoverageType_Collision, nil
	case app_enums.CoverageComprehensive:
		return ptypes.SubCoverageType_SubCoverageType_Comprehensive, nil

	// Medical Payments and Uninsured/Underinsured Motorist coverages
	case app_enums.CoverageMedicalPayments:
		return ptypes.SubCoverageType_SubCoverageType_MedicalPayments, nil
	case app_enums.CoverageUninsuredMotoristBodilyInjury:
		return ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury, nil
	case app_enums.CoverageUnderinsuredMotoristBodilyInjury:
		return ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury, nil
	case app_enums.CoverageUninsuredMotoristPropertyDamage:
		return ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage, nil
	case app_enums.CoverageUnderinsuredMotoristPropertyDamage:
		return ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage, nil
	case app_enums.CoverageUMUIM:
		return ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury, nil

	// Ancillary coverages
	case app_enums.CoverageHiredAuto:
		return ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability, nil
	case app_enums.CoverageNonOwnedAuto:
		return ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle, nil

	// Primary coverages that map to themselves (from GetSubCoverageFromPrimaryCoverage)
	case app_enums.CoverageGeneralLiability:
		return ptypes.SubCoverageType_SubCoverageType_GeneralLiability, nil
	case app_enums.CoverageMotorTruckCargo:
		return ptypes.SubCoverageType_SubCoverageType_Cargo, nil

	case app_enums.CoverageRentalReimbursement:
		return ptypes.SubCoverageType_SubCoverageType_RentalReimbursement, nil
	case app_enums.CoveragePersonalInjuryProtection:
		return ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection, nil
	case app_enums.CoverageTowingLaborAndStorage:
		return ptypes.SubCoverageType_SubCoverageType_Towing, nil

	// PIP sub-coverages
	case app_enums.CoverageMedicalExpenseBenefits:
		return ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits, nil
	case app_enums.CoverageFuneralExpenseBenefits:
		return ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits, nil
	case app_enums.CoverageWorkLossBenefits:
		return ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits, nil
	case app_enums.CoverageEssentialServiceExpenses:
		return ptypes.SubCoverageType_SubCoverageType_EssentialServiceExpenses, nil

	default:
		return ptypes.SubCoverageType_SubCoverageType_Unspecified, errors.Newf("unsupported coverage type: %v", coverage)
	}
}
