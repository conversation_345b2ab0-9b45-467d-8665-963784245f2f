package coverage

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
)

func TestValidateCoverageRules_IL_RequiredCoverage(t *testing.T) {
	app := createTestApp(us_states.IL)

	// Remove UMBI from coverages (should fail validation)
	app.CoveragesInfo.PrimaryCoverages = []model.PrimaryCoverage{
		{
			ID:             app_enums.CoverageAutoLiability,
			SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageBodilyInjury, app_enums.CoveragePropertyDamage},
		},
	}

	result := ValidateCoverageRules(app, []app_enums.Coverage{}, map[app_enums.Coverage]float64{})

	assert.False(t, result.IsValid)
	assert.Len(t, result.Errors, 1)
	assert.Contains(t, result.Errors[0].Message, "Uninsured Motorist Bodily Injury (UMBI) coverage is required in Illinois")
}

func TestValidateCoverageRules_IL_MutuallyExclusive(t *testing.T) {
	app := createTestApp(us_states.IL)

	// Add both APD and UMPD (should fail validation)
	app.CoveragesInfo.PrimaryCoverages = []model.PrimaryCoverage{
		{
			ID: app_enums.CoverageAutoLiability,
			SubCoverageIDs: []app_enums.Coverage{
				app_enums.CoverageBodilyInjury,
				app_enums.CoveragePropertyDamage,
				app_enums.CoverageUninsuredMotoristBodilyInjury,
				app_enums.CoverageAutoPhysicalDamage,
				app_enums.CoverageUninsuredMotoristPropertyDamage,
			},
		},
	}

	result := ValidateCoverageRules(app, []app_enums.Coverage{}, map[app_enums.Coverage]float64{})

	assert.False(t, result.IsValid)
	assert.Len(t, result.Errors, 1)
	assert.Contains(t, result.Errors[0].Message, "Uninsured Motorist Property Damage (UMPD) cannot be selected when APD coverage is present in Illinois")
}

func TestValidateCoverageRules_IN_RequiredWithCoverage(t *testing.T) {
	app := createTestApp(us_states.IN)

	// Add UMPD without UMBI (should fail validation)
	app.CoveragesInfo.PrimaryCoverages = []model.PrimaryCoverage{
		{
			ID: app_enums.CoverageAutoLiability,
			SubCoverageIDs: []app_enums.Coverage{
				app_enums.CoverageBodilyInjury,
				app_enums.CoveragePropertyDamage,
				app_enums.CoverageUninsuredMotoristPropertyDamage,
			},
		},
	}

	result := ValidateCoverageRules(app, []app_enums.Coverage{}, map[app_enums.Coverage]float64{})

	assert.False(t, result.IsValid)
	assert.Len(t, result.Errors, 1)
	assert.Contains(t, result.Errors[0].Message, "Uninsured Motorist Bodily Injury (UMBI) must be selected when Uninsured Motorist Property Damage (UMPD) is selected in Indiana")
}

func TestValidateCoverageRules_IN_SameLimit(t *testing.T) {
	app := createTestApp(us_states.IN)

	// Add both UMBI and UMPD with different limits (should fail validation)
	app.CoveragesInfo.PrimaryCoverages = []model.PrimaryCoverage{
		{
			ID: app_enums.CoverageAutoLiability,
			SubCoverageIDs: []app_enums.Coverage{
				app_enums.CoverageBodilyInjury,
				app_enums.CoveragePropertyDamage,
				app_enums.CoverageUninsuredMotoristBodilyInjury,
				app_enums.CoverageUninsuredMotoristPropertyDamage,
			},
		},
	}

	app.CoveragesInfo.Limits = []model.Limit{
		{
			SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUninsuredMotoristBodilyInjury},
			Amount:         300000.0,
			Type:           pointer_utils.ToPointer(model.LimitTypePerOccurrence),
		},
		{
			SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUninsuredMotoristPropertyDamage},
			Amount:         500000.0, // Different limit
			Type:           pointer_utils.ToPointer(model.LimitTypePerOccurrence),
		},
	}

	// Pass different limits via requestLimits
	requestLimits := map[app_enums.Coverage]float64{
		app_enums.CoverageUninsuredMotoristBodilyInjury:   300000.0,
		app_enums.CoverageUninsuredMotoristPropertyDamage: 500000.0,
	}

	result := ValidateCoverageRules(app, []app_enums.Coverage{}, requestLimits)

	assert.False(t, result.IsValid)
	assert.Len(t, result.Errors, 1)
	assert.Contains(t, result.Errors[0].Message, "Uninsured Motorist Bodily Injury (UMBI) and Uninsured Motorist Property Damage (UMPD) must have the same limit in Indiana")
}

func TestValidateCoverageRules_OH_RequiredWithCoverage(t *testing.T) {
	app := createTestApp(us_states.OH)

	// Add UMPD without UMBI (should fail validation)
	app.CoveragesInfo.PrimaryCoverages = []model.PrimaryCoverage{
		{
			ID: app_enums.CoverageAutoLiability,
			SubCoverageIDs: []app_enums.Coverage{
				app_enums.CoverageBodilyInjury,
				app_enums.CoveragePropertyDamage,
				app_enums.CoverageUninsuredMotoristPropertyDamage,
			},
		},
	}

	result := ValidateCoverageRules(app, []app_enums.Coverage{}, map[app_enums.Coverage]float64{})

	assert.False(t, result.IsValid)
	assert.Len(t, result.Errors, 1)
	assert.Contains(t, result.Errors[0].Message, "Uninsured Motorist Bodily Injury (UMBI) must be selected when Uninsured Motorist Property Damage (UMPD) is selected in Ohio")
}

func TestValidateCoverageRules_NoRulesState(t *testing.T) {
	app := createTestApp(us_states.CA) // California - no rules defined

	result := ValidateCoverageRules(app, []app_enums.Coverage{}, map[app_enums.Coverage]float64{})

	assert.True(t, result.IsValid)
	assert.Len(t, result.Errors, 0)
}

func TestValidateCoverageRules_WithAncillaryCoverages(t *testing.T) {
	app := createTestApp(us_states.IN)

	// Remove UMBI from base coverages to properly test the validation
	app.CoveragesInfo.PrimaryCoverages = []model.PrimaryCoverage{
		{
			ID: app_enums.CoverageAutoLiability,
			SubCoverageIDs: []app_enums.Coverage{
				app_enums.CoverageBodilyInjury,
				app_enums.CoveragePropertyDamage,
				// Note: UMBI is NOT included here
			},
		},
	}

	// Test adding UMPD without UMBI via ancillary coverages
	ancillaryCoverages := []app_enums.Coverage{app_enums.CoverageUninsuredMotoristPropertyDamage}
	requestLimits := map[app_enums.Coverage]float64{
		app_enums.CoverageUninsuredMotoristPropertyDamage: 300000.0,
	}

	result := ValidateCoverageRules(app, ancillaryCoverages, requestLimits)

	assert.False(t, result.IsValid)
	assert.Len(t, result.Errors, 1)
	assert.Contains(t, result.Errors[0].Message, "Uninsured Motorist Bodily Injury (UMBI) must be selected when Uninsured Motorist Property Damage (UMPD) is selected in Indiana")
}

func TestValidateRuleConfiguration(t *testing.T) {
	// Test by temporarily adding invalid rules to ValidationConfig
	originalConfig := ValidationConfig[us_states.WA]

	// Add invalid rule configurations
	ValidationConfig[us_states.WA] = StateValidationConfig{
		Rules: []ValidationRule{
			{
				Type:               RuleTypeRequiredWithCoverage,
				Coverage:           app_enums.CoverageComprehensive,
				ForbiddenCoverages: []app_enums.Coverage{app_enums.CoverageUninsuredMotoristPropertyDamage}, // Invalid for this rule type
				ErrorMessage:       "Should fail",
			},
			{
				Type:              RuleTypeMutuallyExclusive,
				Coverage:          app_enums.CoverageComprehensive,
				RequiredCoverages: []app_enums.Coverage{app_enums.CoverageCollision}, // Invalid for this rule type
				ErrorMessage:      "Should fail",
			},
		},
	}

	app := createTestApp(us_states.WA)
	result := ValidateCoverageRules(app, []app_enums.Coverage{}, map[app_enums.Coverage]float64{})

	assert.False(t, result.IsValid)
	assert.Len(t, result.Errors, 2)
	assert.Contains(t, result.Errors[0].Message, "Invalid rule configuration")
	assert.Contains(t, result.Errors[1].Message, "Invalid rule configuration")

	// Restore original config
	ValidationConfig[us_states.WA] = originalConfig
}

// Helper function to create a test app
func createTestApp(state us_states.USState) *model.BusinessAutoApp {
	return &model.BusinessAutoApp{
		CompanyInfo: model.CompanyInfo{
			USState: state,
		},
		CoveragesInfo: &model.CoveragesInfo{
			PrimaryCoverages: []model.PrimaryCoverage{
				{
					ID: app_enums.CoverageAutoLiability,
					SubCoverageIDs: []app_enums.Coverage{
						app_enums.CoverageBodilyInjury,
						app_enums.CoveragePropertyDamage,
						app_enums.CoverageUninsuredMotoristBodilyInjury,
					},
				},
			},
			Limits: []model.Limit{
				{
					SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUninsuredMotoristBodilyInjury},
					Amount:         300000.0,
					Type:           pointer_utils.ToPointer(model.LimitTypePerOccurrence),
				},
			},
		},
	}
}

// TX Tests

func TestValidateCoverageRules_TX_MedPayPIP_MutuallyExclusive(t *testing.T) {
	// Create app for TX
	app := createTestApp(us_states.TX)

	// Test with both MedPay and PIP in ancillary coverages
	ancillaryCoverages := []app_enums.Coverage{
		app_enums.CoverageMedicalPayments,
		app_enums.CoveragePersonalInjuryProtection,
	}

	result := ValidateCoverageRules(app, ancillaryCoverages, map[app_enums.Coverage]float64{})

	assert.False(t, result.IsValid, "Should be invalid when both MedPay and PIP are selected")
	assert.Len(t, result.Errors, 2, "Should have two errors (one for each mutual exclusion rule)")

	errorMessages := make([]string, len(result.Errors))
	for i, err := range result.Errors {
		errorMessages[i] = err.Message
	}
	assert.Contains(t, strings.Join(errorMessages, " "), "mutually exclusive", "Should mention mutual exclusion")
}

func TestValidateCoverageRules_TX_MedPayOnly_Valid(t *testing.T) {
	// Create app for TX
	app := createTestApp(us_states.TX)

	// Test with only MedPay in ancillary coverages
	ancillaryCoverages := []app_enums.Coverage{
		app_enums.CoverageMedicalPayments,
	}

	result := ValidateCoverageRules(app, ancillaryCoverages, map[app_enums.Coverage]float64{})

	assert.True(t, result.IsValid, "Should be valid when only MedPay is selected")
	assert.Empty(t, result.Errors, "Should have no errors")
}

func TestValidateCoverageRules_TX_PIPOnly_Valid(t *testing.T) {
	// Create app for TX
	app := createTestApp(us_states.TX)

	// Test with only PIP in ancillary coverages
	ancillaryCoverages := []app_enums.Coverage{
		app_enums.CoveragePersonalInjuryProtection,
	}

	result := ValidateCoverageRules(app, ancillaryCoverages, map[app_enums.Coverage]float64{})

	assert.True(t, result.IsValid, "Should be valid when only PIP is selected")
	assert.Empty(t, result.Errors, "Should have no errors")
}

func TestValidateCoverageRules_TX_NeitherMedPayNorPIP_Valid(t *testing.T) {
	// Create app for TX
	app := createTestApp(us_states.TX)

	// Test with neither MedPay nor PIP in ancillary coverages
	ancillaryCoverages := []app_enums.Coverage{}

	result := ValidateCoverageRules(app, ancillaryCoverages, map[app_enums.Coverage]float64{})

	assert.True(t, result.IsValid, "Should be valid when neither MedPay nor PIP is selected")
	assert.Empty(t, result.Errors, "Should have no errors")
}
