load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "coverage",
    srcs = [
        "coverage.go",
        "deductibles.go",
        "expansion.go",
        "limits.go",
        "ruletype_enumer.go",
        "validations.go",
    ],
    importpath = "nirvanatech.com/nirvana/business-auto/coverage",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/business-auto/model",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-core/coverage",
        "//nirvana/quoting/ancillary_coverages",
        "//nirvana/rating/rtypes",
        "@com_github_cockroachdb_errors//:errors",
    ],
)

go_test(
    name = "coverage_test",
    srcs = [
        "coverage_test.go",
        "validations_test.go",
    ],
    embed = [":coverage"],
    deps = [
        "//nirvana/business-auto/model",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/insurance-bundle/model",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//suite",
    ],
)
