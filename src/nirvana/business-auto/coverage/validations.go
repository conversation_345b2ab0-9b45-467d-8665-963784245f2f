package coverage

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
)

// ValidationError represents a coverage validation error
type ValidationError struct {
	Message string
	Field   string
}

func (ve ValidationError) Error() string {
	return ve.Message
}

// ValidationResult holds the result of coverage validation
type ValidationResult struct {
	IsValid bool
	Errors  []ValidationError
}

// RuleType defines the type of validation rule
//
//go:generate go run github.com/dmarkham/enumer -type=RuleType -json -text -trimprefix=RuleType
type RuleType int

const (
	RuleTypeRequiredCoverage RuleType = iota
	RuleTypeMutuallyExclusive
	RuleTypeRequiredWithCoverage
	RuleTypeSameLimit
)

// ValidationRule defines a single validation rule
type ValidationRule struct {
	Type               RuleType
	Coverage           app_enums.Coverage   // The main coverage this rule applies to
	RequiredCoverages  []app_enums.Coverage // List of coverages required when the main coverage is selected
	ForbiddenCoverages []app_enums.Coverage // List of coverages that cannot be selected with the main coverage
	ErrorMessage       string
}

// StateValidationConfig holds all validation rules for a specific state
type StateValidationConfig struct {
	Rules []ValidationRule
}

// ValidationConfig holds validation rules for all states
var ValidationConfig = map[us_states.USState]StateValidationConfig{
	us_states.IL: {
		Rules: []ValidationRule{
			{
				Type:         RuleTypeRequiredCoverage,
				Coverage:     app_enums.CoverageUninsuredMotoristBodilyInjury,
				ErrorMessage: "Uninsured Motorist Bodily Injury (UMBI) coverage is required in Illinois",
			},
			{
				Type:               RuleTypeMutuallyExclusive,
				Coverage:           app_enums.CoverageAutoPhysicalDamage,
				ForbiddenCoverages: []app_enums.Coverage{app_enums.CoverageUninsuredMotoristPropertyDamage},
				ErrorMessage:       "Uninsured Motorist Property Damage (UMPD) cannot be selected when APD coverage is present in Illinois",
			},
		},
	},
	us_states.IN: {
		Rules: []ValidationRule{
			{
				Type:              RuleTypeRequiredWithCoverage,
				Coverage:          app_enums.CoverageUninsuredMotoristPropertyDamage,
				RequiredCoverages: []app_enums.Coverage{app_enums.CoverageUninsuredMotoristBodilyInjury},
				ErrorMessage:      "Uninsured Motorist Bodily Injury (UMBI) must be selected when Uninsured Motorist Property Damage (UMPD) is selected in Indiana",
			},
			{
				Type:              RuleTypeSameLimit,
				Coverage:          app_enums.CoverageUninsuredMotoristBodilyInjury,
				RequiredCoverages: []app_enums.Coverage{app_enums.CoverageUninsuredMotoristPropertyDamage},
				ErrorMessage:      "Uninsured Motorist Bodily Injury (UMBI) and Uninsured Motorist Property Damage (UMPD) must have the same limit in Indiana",
			},
		},
	},
	us_states.OH: {
		Rules: []ValidationRule{
			{
				Type:              RuleTypeRequiredWithCoverage,
				Coverage:          app_enums.CoverageUninsuredMotoristPropertyDamage,
				RequiredCoverages: []app_enums.Coverage{app_enums.CoverageUninsuredMotoristBodilyInjury},
				ErrorMessage:      "Uninsured Motorist Bodily Injury (UMBI) must be selected when Uninsured Motorist Property Damage (UMPD) is selected in Ohio",
			},
			{
				Type:              RuleTypeSameLimit,
				Coverage:          app_enums.CoverageUnderinsuredMotoristBodilyInjury,
				RequiredCoverages: []app_enums.Coverage{app_enums.CoverageUninsuredMotoristBodilyInjury},
				ErrorMessage:      "Underinsured Motorist Bodily Injury (UIMBI) and Uninsured Motorist Bodily Injury (UMBI) must have the same limit in Ohio",
			},
		},
	},
	us_states.TX: {
		Rules: []ValidationRule{
			{
				Type:               RuleTypeMutuallyExclusive,
				Coverage:           app_enums.CoverageMedicalPayments,
				ForbiddenCoverages: []app_enums.Coverage{app_enums.CoveragePersonalInjuryProtection},
				ErrorMessage:       "Medical Payments (MedPay) and Personal Injury Protection (PIP) are mutually exclusive in Texas",
			},
			{
				Type:               RuleTypeMutuallyExclusive,
				Coverage:           app_enums.CoveragePersonalInjuryProtection,
				ForbiddenCoverages: []app_enums.Coverage{app_enums.CoverageMedicalPayments},
				ErrorMessage:       "Personal Injury Protection (PIP) and Medical Payments (MedPay) are mutually exclusive in Texas",
			},
		},
	},
}

// validateStateRules validates rules for a specific state using the configuration
func validateStateRules(
	selectedCoverages []app_enums.Coverage,
	coverageLimits map[app_enums.Coverage]float64,
	state us_states.USState,
	result *ValidationResult,
) {
	stateConfig, exists := ValidationConfig[state]
	if !exists {
		return // No validation rules for this state
	}

	for _, rule := range stateConfig.Rules {
		// Validate rule configuration
		if err := validateRuleConfiguration(rule); err != nil {
			result.IsValid = false
			result.Errors = append(result.Errors, ValidationError{
				Message: "Invalid rule configuration: " + err.Error(),
				Field:   "configuration",
			})
			continue
		}

		switch rule.Type {
		case RuleTypeRequiredCoverage:
			if !containsCoverage(rule.Coverage, selectedCoverages) {
				result.IsValid = false
				result.Errors = append(result.Errors, ValidationError{
					Message: rule.ErrorMessage,
					Field:   "coverages",
				})
			}

		case RuleTypeMutuallyExclusive:
			hasCoverage := containsCoverage(rule.Coverage, selectedCoverages)

			if hasCoverage {
				// Check if any of the forbidden coverages are present
				for _, forbiddenCoverage := range rule.ForbiddenCoverages {
					if containsCoverage(forbiddenCoverage, selectedCoverages) {
						result.IsValid = false
						result.Errors = append(result.Errors, ValidationError{
							Message: rule.ErrorMessage,
							Field:   "coverages",
						})
						break // Only add the error once
					}
				}
			}

		case RuleTypeRequiredWithCoverage:
			hasCoverage := containsCoverage(rule.Coverage, selectedCoverages)

			if hasCoverage {
				// Check if all required coverages are present
				for _, requiredCoverage := range rule.RequiredCoverages {
					if !containsCoverage(requiredCoverage, selectedCoverages) {
						result.IsValid = false
						result.Errors = append(result.Errors, ValidationError{
							Message: rule.ErrorMessage,
							Field:   "coverages",
						})
						break // Only add the error once
					}
				}
			}

		case RuleTypeSameLimit:
			hasCoverage := containsCoverage(rule.Coverage, selectedCoverages)

			if hasCoverage {
				coverageLimit, coverageExists := coverageLimits[rule.Coverage]

				if coverageExists {
					// Check if all required coverages have the same limit
					for _, requiredCoverage := range rule.RequiredCoverages {
						if containsCoverage(requiredCoverage, selectedCoverages) {
							requiredLimit, requiredExists := coverageLimits[requiredCoverage]

							if requiredExists && coverageLimit != requiredLimit {
								result.IsValid = false
								result.Errors = append(result.Errors, ValidationError{
									Message: rule.ErrorMessage,
									Field:   "coverages",
								})
								break // Only add the error once
							}
						}
					}
				}
			}
		}
	}
}

// validateRuleConfiguration validates that a rule is configured correctly
func validateRuleConfiguration(rule ValidationRule) error {
	switch rule.Type {
	case RuleTypeRequiredCoverage:
		if len(rule.RequiredCoverages) > 0 {
			return errors.New("RuleTypeRequiredCoverage should not have RequiredCoverages field set")
		}
		if len(rule.ForbiddenCoverages) > 0 {
			return errors.New("RuleTypeRequiredCoverage should not have ForbiddenCoverages field set")
		}

	case RuleTypeMutuallyExclusive:
		if len(rule.ForbiddenCoverages) == 0 {
			return errors.New("RuleTypeMutuallyExclusive must have ForbiddenCoverages field set")
		}
		if len(rule.RequiredCoverages) > 0 {
			return errors.New("RuleTypeMutuallyExclusive should not have RequiredCoverages field set")
		}

	case RuleTypeRequiredWithCoverage:
		if len(rule.RequiredCoverages) == 0 {
			return errors.New("RuleTypeRequiredWithCoverage must have RequiredCoverages field set")
		}
		if len(rule.ForbiddenCoverages) > 0 {
			return errors.New("RuleTypeRequiredWithCoverage should not have ForbiddenCoverages field set")
		}

	case RuleTypeSameLimit:
		if len(rule.RequiredCoverages) == 0 {
			return errors.New("RuleTypeSameLimit must have RequiredCoverages field set")
		}
		if len(rule.ForbiddenCoverages) > 0 {
			return errors.New("RuleTypeSameLimit should not have ForbiddenCoverages field set")
		}
	}

	return nil
}

// containsCoverage checks if a coverage exists in the list
func containsCoverage(coverage app_enums.Coverage, coverages []app_enums.Coverage) bool {
	for _, c := range coverages {
		if c == coverage {
			return true
		}
	}
	return false
}

// ValidateCoverageRules validates coverage rules for a request with ancillary coverages
func ValidateCoverageRules(
	app *model.BusinessAutoApp,
	ancillaryCoverages []app_enums.Coverage,
	requestLimits map[app_enums.Coverage]float64,
) *ValidationResult {
	result := &ValidationResult{
		IsValid: true,
		Errors:  make([]ValidationError, 0),
	}

	if app == nil {
		return result
	}

	// Get base selected coverages from the app
	selectedCoverages := app.CoveragesInfo.GetAllPrimaryAndAncCoverages()

	// Add ancillary coverages to the selection
	selectedCoverages = append(selectedCoverages, ancillaryCoverages...)

	// Use request limits if provided, otherwise fall back to app limits
	coverageLimits := app.CoveragesInfo.GetLimitForAllSubCoverages()
	for coverage, limit := range requestLimits {
		coverageLimits[coverage] = limit
	}

	state := app.CompanyInfo.USState

	// Run validation rules for the state
	validateStateRules(selectedCoverages, coverageLimits, state, result)

	return result
}
