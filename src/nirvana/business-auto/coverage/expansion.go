package coverage

import (
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	ib_model "nirvanatech.com/nirvana/insurance-bundle/model"
)

// GetExpansionMapping returns the expected sub-coverages and grouping for a parent coverage in a given state.
// ok=false means no expansion defined for this state+parent combination.
func GetExpansionMapping(state us_states.USState, parent app_enums.Coverage) ([]app_enums.Coverage, ib_model.LimitGrouping, bool) {
	switch state {
	case us_states.TX:
		switch parent {
		case app_enums.CoveragePersonalInjuryProtection:
			return []app_enums.Coverage{
				app_enums.CoverageMedicalExpenseBenefits,
				app_enums.CoverageFuneralExpenseBenefits,
				app_enums.CoverageWorkLossBenefits,
				app_enums.CoverageEssentialServiceExpenses,
			}, ib_model.LimitGrouping_LimitGrouping_Combined, true
		case app_enums.CoverageUMUIM:
			return []app_enums.Coverage{
				app_enums.CoverageUninsuredMotoristBodilyInjury,
				app_enums.CoverageUnderinsuredMotoristBodilyInjury,
				app_enums.CoverageUninsuredMotoristPropertyDamage,
				app_enums.CoverageUnderinsuredMotoristPropertyDamage,
			}, ib_model.LimitGrouping_LimitGrouping_Combined, true
		default:
			return nil, ib_model.LimitGrouping_LimitGrouping_Single, false
		}
	default:
		return nil, ib_model.LimitGrouping_LimitGrouping_Single, false
	}
}
