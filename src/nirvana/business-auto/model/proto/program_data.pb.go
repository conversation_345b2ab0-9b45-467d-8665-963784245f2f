// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: business_auto/model/program_data.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	proto "nirvanatech.com/nirvana/insurance-core/proto"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VehicleWeightClass int32

const (
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_UNSPECIFIED VehicleWeightClass = 0
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_LIGHT       VehicleWeightClass = 1
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_MEDIUM      VehicleWeightClass = 2
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_HEAVY       VehicleWeightClass = 3
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_EXTRA_HEAVY VehicleWeightClass = 4
)

// Enum value maps for VehicleWeightClass.
var (
	VehicleWeightClass_name = map[int32]string{
		0: "VEHICLE_WEIGHT_CLASS_UNSPECIFIED",
		1: "VEHICLE_WEIGHT_CLASS_LIGHT",
		2: "VEHICLE_WEIGHT_CLASS_MEDIUM",
		3: "VEHICLE_WEIGHT_CLASS_HEAVY",
		4: "VEHICLE_WEIGHT_CLASS_EXTRA_HEAVY",
	}
	VehicleWeightClass_value = map[string]int32{
		"VEHICLE_WEIGHT_CLASS_UNSPECIFIED": 0,
		"VEHICLE_WEIGHT_CLASS_LIGHT":       1,
		"VEHICLE_WEIGHT_CLASS_MEDIUM":      2,
		"VEHICLE_WEIGHT_CLASS_HEAVY":       3,
		"VEHICLE_WEIGHT_CLASS_EXTRA_HEAVY": 4,
	}
)

func (x VehicleWeightClass) Enum() *VehicleWeightClass {
	p := new(VehicleWeightClass)
	*p = x
	return p
}

func (x VehicleWeightClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleWeightClass) Descriptor() protoreflect.EnumDescriptor {
	return file_business_auto_model_program_data_proto_enumTypes[0].Descriptor()
}

func (VehicleWeightClass) Type() protoreflect.EnumType {
	return &file_business_auto_model_program_data_proto_enumTypes[0]
}

func (x VehicleWeightClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleWeightClass.Descriptor instead.
func (VehicleWeightClass) EnumDescriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{0}
}

type VehicleSpecialtyType int32

const (
	VehicleSpecialtyType_VEHICLE_SPECIALTY_TYPE_UNSPECIFIED                                                               VehicleSpecialtyType = 0
	VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_0_TO_49_FEET                                                       VehicleSpecialtyType = 1
	VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_50_TO_75_FEET                                                      VehicleSpecialtyType = 2
	VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_76_TO_100_FEET                                                     VehicleSpecialtyType = 3
	VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_101_TO_120_FEET                                                    VehicleSpecialtyType = 4
	VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_121_PLUS_FEET                                                      VehicleSpecialtyType = 5
	VehicleSpecialtyType_VEHICLE_SPECIALTY_CABLE_TELECOM_AND_OTHER_UTILITY_CONTRACTORS                                    VehicleSpecialtyType = 6
	VehicleSpecialtyType_VEHICLE_SPECIALTY_EXCAVATING_DRILLING_AND_LAND_GRADING_CONTRACTORS_LIGHT_MEDIUM_AND_HEAVY_TRUCKS VehicleSpecialtyType = 7
	VehicleSpecialtyType_VEHICLE_SPECIALTY_EXCAVATING_DRILLING_AND_LAND_GRADING_CONTRACTORS_ALL_OTHER_UNITS               VehicleSpecialtyType = 8
	VehicleSpecialtyType_VEHICLE_SPECIALTY_PETROLEUM_DISTRIBUTION_CONTRACTORS                                             VehicleSpecialtyType = 9
	VehicleSpecialtyType_VEHICLE_SPECIALTY_HOT_OIL_LIQUID_ASPHALT_TRUCKS_OIL_FIELD_OR_ENERGY                              VehicleSpecialtyType = 10
	VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_OIL_FIELD_OR_ENERGY_BODY_TYPES                                       VehicleSpecialtyType = 11
	VehicleSpecialtyType_VEHICLE_SPECIALTY_SEPTIC_TANK_SERVICE_TRUCKS_INDIVIDUAL_NAMED_INSURED                            VehicleSpecialtyType = 12
	VehicleSpecialtyType_VEHICLE_SPECIALTY_SEPTIC_TANK_SERVICE_TRUCKS_ALL_OTHER_NAMED_INSURED                             VehicleSpecialtyType = 13
	VehicleSpecialtyType_VEHICLE_SPECIALTY_WELDERS_AND_METALWORKING_CONTRACTORS                                           VehicleSpecialtyType = 14
	VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_CONTRACTOR_BODY_TYPES                                                VehicleSpecialtyType = 15
	VehicleSpecialtyType_VEHICLE_SPECIALTY_HOT_OIL_LIQUID_ASPHALT_TRUCKS_ALL_OTHER_CONTRACTORS                            VehicleSpecialtyType = 16
	VehicleSpecialtyType_VEHICLE_SPECIALTY_SPECIALIZED_DELIVERY_VEHICLES                                                  VehicleSpecialtyType = 17
	VehicleSpecialtyType_VEHICLE_SPECIALTY_COURIER_SERVICE_VEHICLES                                                       VehicleSpecialtyType = 18
	VehicleSpecialtyType_VEHICLE_SPECIALTY_FOOD_DELIVERY_TRUCKS                                                           VehicleSpecialtyType = 19
	VehicleSpecialtyType_VEHICLE_SPECIALTY_WASTE_DISPOSAL_TRUCKS                                                          VehicleSpecialtyType = 20
	VehicleSpecialtyType_VEHICLE_SPECIALTY_WASTE_OIL_AND_LIQUID_WASTE_TRANSPORTERS                                        VehicleSpecialtyType = 21
	VehicleSpecialtyType_VEHICLE_SPECIALTY_HARVESTER_GOAT_TRUCKS_FARMER_TRUCKS                                            VehicleSpecialtyType = 22
	VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_FARMER_TRUCKS                                                        VehicleSpecialtyType = 23
	VehicleSpecialtyType_VEHICLE_SPECIALTY_HOUSE_MOVERS                                                                   VehicleSpecialtyType = 24
	VehicleSpecialtyType_VEHICLE_SPECIALTY_MOVING_OPERATIONS                                                              VehicleSpecialtyType = 25
	VehicleSpecialtyType_VEHICLE_SPECIALTY_LAWN_TREE_SERVICE_TRUCKS                                                       VehicleSpecialtyType = 26
	VehicleSpecialtyType_VEHICLE_SPECIALTY_CATERER_VEHICLES                                                               VehicleSpecialtyType = 27
	VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_IN_VEHICLE_VENDING                                     VehicleSpecialtyType = 28
	VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_OTHER_FOOD_VENDING                                     VehicleSpecialtyType = 29
	VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_NO_FOOD_SALES                                          VehicleSpecialtyType = 30
	VehicleSpecialtyType_VEHICLE_SPECIALTY_WHOLESALERS_AND_MANUFACTURERS                                                  VehicleSpecialtyType = 31
	VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_LPG_PROPANE_HAULERS_BOTTLED             VehicleSpecialtyType = 32
	VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_LPG_PROPANE_HAULERS_BULK                VehicleSpecialtyType = 33
	VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_CRUDE_OIL_HAULERS                       VehicleSpecialtyType = 34
	VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_FUEL_OIL_HAULERS                        VehicleSpecialtyType = 35
	VehicleSpecialtyType_VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_ALL_OTHER_GAS_HAULERS                   VehicleSpecialtyType = 36
	VehicleSpecialtyType_VEHICLE_SPECIALTY_SALVAGE_HAULERS                                                                VehicleSpecialtyType = 37
	VehicleSpecialtyType_VEHICLE_SPECIALTY_CAR_CARRIERS_NOT_FOR_HIRE                                                      VehicleSpecialtyType = 38
	VehicleSpecialtyType_VEHICLE_SPECIALTY_SERVICE_USE_VEHICLES                                                           VehicleSpecialtyType = 39
	VehicleSpecialtyType_VEHICLE_SPECIALTY_FIREWORK_HAULERS_NOT_FOR_HIRE                                                  VehicleSpecialtyType = 40
	VehicleSpecialtyType_VEHICLE_SPECIALTY_CUSTOM_HARVESTERS                                                              VehicleSpecialtyType = 41
	VehicleSpecialtyType_VEHICLE_SPECIALTY_DRIVER_TRAINING_TRUCKS_TRACTORS                                                VehicleSpecialtyType = 42
	VehicleSpecialtyType_VEHICLE_SPECIALTY_STREET_SWEEPERS                                                                VehicleSpecialtyType = 43
	VehicleSpecialtyType_VEHICLE_SPECIALTY_RENTAL_EQUIPMENT_PROVIDER                                                      VehicleSpecialtyType = 44
	VehicleSpecialtyType_VEHICLE_SPECIALTY_NOT_OTHERWISE_CLASSIFIED_TRUCKS                                                VehicleSpecialtyType = 45
	VehicleSpecialtyType_VEHICLE_SPECIALTY_ARTISAN_CONTRACTORS                                                            VehicleSpecialtyType = 46
	VehicleSpecialtyType_VEHICLE_SPECIALTY_CARPENTRY_CONTRACTORS                                                          VehicleSpecialtyType = 47
	VehicleSpecialtyType_VEHICLE_SPECIALTY_EXTERIOR_BUILDING_WORK_AND_CONSTRUCTION_CONTRACTORS                            VehicleSpecialtyType = 48
	VehicleSpecialtyType_VEHICLE_SPECIALTY_ROAD_CONSTRUCTION_CONTRACTORS                                                  VehicleSpecialtyType = 49
	VehicleSpecialtyType_VEHICLE_SPECIALTY_TRAFFIC_CONTROL_CONTRACTORS                                                    VehicleSpecialtyType = 50
	VehicleSpecialtyType_VEHICLE_SPECIALTY_MOBILE_MECHANIC_CONTRACTORS                                                    VehicleSpecialtyType = 51
	VehicleSpecialtyType_VEHICLE_SPECIALTY_ALL_OTHER_UNITS                                                                VehicleSpecialtyType = 52
)

// Enum value maps for VehicleSpecialtyType.
var (
	VehicleSpecialtyType_name = map[int32]string{
		0:  "VEHICLE_SPECIALTY_TYPE_UNSPECIFIED",
		1:  "VEHICLE_SPECIALTY_BOOM_TRUCKS_0_TO_49_FEET",
		2:  "VEHICLE_SPECIALTY_BOOM_TRUCKS_50_TO_75_FEET",
		3:  "VEHICLE_SPECIALTY_BOOM_TRUCKS_76_TO_100_FEET",
		4:  "VEHICLE_SPECIALTY_BOOM_TRUCKS_101_TO_120_FEET",
		5:  "VEHICLE_SPECIALTY_BOOM_TRUCKS_121_PLUS_FEET",
		6:  "VEHICLE_SPECIALTY_CABLE_TELECOM_AND_OTHER_UTILITY_CONTRACTORS",
		7:  "VEHICLE_SPECIALTY_EXCAVATING_DRILLING_AND_LAND_GRADING_CONTRACTORS_LIGHT_MEDIUM_AND_HEAVY_TRUCKS",
		8:  "VEHICLE_SPECIALTY_EXCAVATING_DRILLING_AND_LAND_GRADING_CONTRACTORS_ALL_OTHER_UNITS",
		9:  "VEHICLE_SPECIALTY_PETROLEUM_DISTRIBUTION_CONTRACTORS",
		10: "VEHICLE_SPECIALTY_HOT_OIL_LIQUID_ASPHALT_TRUCKS_OIL_FIELD_OR_ENERGY",
		11: "VEHICLE_SPECIALTY_ALL_OTHER_OIL_FIELD_OR_ENERGY_BODY_TYPES",
		12: "VEHICLE_SPECIALTY_SEPTIC_TANK_SERVICE_TRUCKS_INDIVIDUAL_NAMED_INSURED",
		13: "VEHICLE_SPECIALTY_SEPTIC_TANK_SERVICE_TRUCKS_ALL_OTHER_NAMED_INSURED",
		14: "VEHICLE_SPECIALTY_WELDERS_AND_METALWORKING_CONTRACTORS",
		15: "VEHICLE_SPECIALTY_ALL_OTHER_CONTRACTOR_BODY_TYPES",
		16: "VEHICLE_SPECIALTY_HOT_OIL_LIQUID_ASPHALT_TRUCKS_ALL_OTHER_CONTRACTORS",
		17: "VEHICLE_SPECIALTY_SPECIALIZED_DELIVERY_VEHICLES",
		18: "VEHICLE_SPECIALTY_COURIER_SERVICE_VEHICLES",
		19: "VEHICLE_SPECIALTY_FOOD_DELIVERY_TRUCKS",
		20: "VEHICLE_SPECIALTY_WASTE_DISPOSAL_TRUCKS",
		21: "VEHICLE_SPECIALTY_WASTE_OIL_AND_LIQUID_WASTE_TRANSPORTERS",
		22: "VEHICLE_SPECIALTY_HARVESTER_GOAT_TRUCKS_FARMER_TRUCKS",
		23: "VEHICLE_SPECIALTY_ALL_OTHER_FARMER_TRUCKS",
		24: "VEHICLE_SPECIALTY_HOUSE_MOVERS",
		25: "VEHICLE_SPECIALTY_MOVING_OPERATIONS",
		26: "VEHICLE_SPECIALTY_LAWN_TREE_SERVICE_TRUCKS",
		27: "VEHICLE_SPECIALTY_CATERER_VEHICLES",
		28: "VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_IN_VEHICLE_VENDING",
		29: "VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_OTHER_FOOD_VENDING",
		30: "VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_NO_FOOD_SALES",
		31: "VEHICLE_SPECIALTY_WHOLESALERS_AND_MANUFACTURERS",
		32: "VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_LPG_PROPANE_HAULERS_BOTTLED",
		33: "VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_LPG_PROPANE_HAULERS_BULK",
		34: "VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_CRUDE_OIL_HAULERS",
		35: "VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_FUEL_OIL_HAULERS",
		36: "VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_ALL_OTHER_GAS_HAULERS",
		37: "VEHICLE_SPECIALTY_SALVAGE_HAULERS",
		38: "VEHICLE_SPECIALTY_CAR_CARRIERS_NOT_FOR_HIRE",
		39: "VEHICLE_SPECIALTY_SERVICE_USE_VEHICLES",
		40: "VEHICLE_SPECIALTY_FIREWORK_HAULERS_NOT_FOR_HIRE",
		41: "VEHICLE_SPECIALTY_CUSTOM_HARVESTERS",
		42: "VEHICLE_SPECIALTY_DRIVER_TRAINING_TRUCKS_TRACTORS",
		43: "VEHICLE_SPECIALTY_STREET_SWEEPERS",
		44: "VEHICLE_SPECIALTY_RENTAL_EQUIPMENT_PROVIDER",
		45: "VEHICLE_SPECIALTY_NOT_OTHERWISE_CLASSIFIED_TRUCKS",
		46: "VEHICLE_SPECIALTY_ARTISAN_CONTRACTORS",
		47: "VEHICLE_SPECIALTY_CARPENTRY_CONTRACTORS",
		48: "VEHICLE_SPECIALTY_EXTERIOR_BUILDING_WORK_AND_CONSTRUCTION_CONTRACTORS",
		49: "VEHICLE_SPECIALTY_ROAD_CONSTRUCTION_CONTRACTORS",
		50: "VEHICLE_SPECIALTY_TRAFFIC_CONTROL_CONTRACTORS",
		51: "VEHICLE_SPECIALTY_MOBILE_MECHANIC_CONTRACTORS",
		52: "VEHICLE_SPECIALTY_ALL_OTHER_UNITS",
	}
	VehicleSpecialtyType_value = map[string]int32{
		"VEHICLE_SPECIALTY_TYPE_UNSPECIFIED":                                                               0,
		"VEHICLE_SPECIALTY_BOOM_TRUCKS_0_TO_49_FEET":                                                       1,
		"VEHICLE_SPECIALTY_BOOM_TRUCKS_50_TO_75_FEET":                                                      2,
		"VEHICLE_SPECIALTY_BOOM_TRUCKS_76_TO_100_FEET":                                                     3,
		"VEHICLE_SPECIALTY_BOOM_TRUCKS_101_TO_120_FEET":                                                    4,
		"VEHICLE_SPECIALTY_BOOM_TRUCKS_121_PLUS_FEET":                                                      5,
		"VEHICLE_SPECIALTY_CABLE_TELECOM_AND_OTHER_UTILITY_CONTRACTORS":                                    6,
		"VEHICLE_SPECIALTY_EXCAVATING_DRILLING_AND_LAND_GRADING_CONTRACTORS_LIGHT_MEDIUM_AND_HEAVY_TRUCKS": 7,
		"VEHICLE_SPECIALTY_EXCAVATING_DRILLING_AND_LAND_GRADING_CONTRACTORS_ALL_OTHER_UNITS":               8,
		"VEHICLE_SPECIALTY_PETROLEUM_DISTRIBUTION_CONTRACTORS":                                             9,
		"VEHICLE_SPECIALTY_HOT_OIL_LIQUID_ASPHALT_TRUCKS_OIL_FIELD_OR_ENERGY":                              10,
		"VEHICLE_SPECIALTY_ALL_OTHER_OIL_FIELD_OR_ENERGY_BODY_TYPES":                                       11,
		"VEHICLE_SPECIALTY_SEPTIC_TANK_SERVICE_TRUCKS_INDIVIDUAL_NAMED_INSURED":                            12,
		"VEHICLE_SPECIALTY_SEPTIC_TANK_SERVICE_TRUCKS_ALL_OTHER_NAMED_INSURED":                             13,
		"VEHICLE_SPECIALTY_WELDERS_AND_METALWORKING_CONTRACTORS":                                           14,
		"VEHICLE_SPECIALTY_ALL_OTHER_CONTRACTOR_BODY_TYPES":                                                15,
		"VEHICLE_SPECIALTY_HOT_OIL_LIQUID_ASPHALT_TRUCKS_ALL_OTHER_CONTRACTORS":                            16,
		"VEHICLE_SPECIALTY_SPECIALIZED_DELIVERY_VEHICLES":                                                  17,
		"VEHICLE_SPECIALTY_COURIER_SERVICE_VEHICLES":                                                       18,
		"VEHICLE_SPECIALTY_FOOD_DELIVERY_TRUCKS":                                                           19,
		"VEHICLE_SPECIALTY_WASTE_DISPOSAL_TRUCKS":                                                          20,
		"VEHICLE_SPECIALTY_WASTE_OIL_AND_LIQUID_WASTE_TRANSPORTERS":                                        21,
		"VEHICLE_SPECIALTY_HARVESTER_GOAT_TRUCKS_FARMER_TRUCKS":                                            22,
		"VEHICLE_SPECIALTY_ALL_OTHER_FARMER_TRUCKS":                                                        23,
		"VEHICLE_SPECIALTY_HOUSE_MOVERS":                                                                   24,
		"VEHICLE_SPECIALTY_MOVING_OPERATIONS":                                                              25,
		"VEHICLE_SPECIALTY_LAWN_TREE_SERVICE_TRUCKS":                                                       26,
		"VEHICLE_SPECIALTY_CATERER_VEHICLES":                                                               27,
		"VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_IN_VEHICLE_VENDING":                                     28,
		"VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_OTHER_FOOD_VENDING":                                     29,
		"VEHICLE_SPECIALTY_MOBILE_CONCESSION_TRUCK_NO_FOOD_SALES":                                          30,
		"VEHICLE_SPECIALTY_WHOLESALERS_AND_MANUFACTURERS":                                                  31,
		"VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_LPG_PROPANE_HAULERS_BOTTLED":             32,
		"VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_LPG_PROPANE_HAULERS_BULK":                33,
		"VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_CRUDE_OIL_HAULERS":                       34,
		"VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_FUEL_OIL_HAULERS":                        35,
		"VEHICLE_SPECIALTY_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE_ALL_OTHER_GAS_HAULERS":                   36,
		"VEHICLE_SPECIALTY_SALVAGE_HAULERS":                                                                37,
		"VEHICLE_SPECIALTY_CAR_CARRIERS_NOT_FOR_HIRE":                                                      38,
		"VEHICLE_SPECIALTY_SERVICE_USE_VEHICLES":                                                           39,
		"VEHICLE_SPECIALTY_FIREWORK_HAULERS_NOT_FOR_HIRE":                                                  40,
		"VEHICLE_SPECIALTY_CUSTOM_HARVESTERS":                                                              41,
		"VEHICLE_SPECIALTY_DRIVER_TRAINING_TRUCKS_TRACTORS":                                                42,
		"VEHICLE_SPECIALTY_STREET_SWEEPERS":                                                                43,
		"VEHICLE_SPECIALTY_RENTAL_EQUIPMENT_PROVIDER":                                                      44,
		"VEHICLE_SPECIALTY_NOT_OTHERWISE_CLASSIFIED_TRUCKS":                                                45,
		"VEHICLE_SPECIALTY_ARTISAN_CONTRACTORS":                                                            46,
		"VEHICLE_SPECIALTY_CARPENTRY_CONTRACTORS":                                                          47,
		"VEHICLE_SPECIALTY_EXTERIOR_BUILDING_WORK_AND_CONSTRUCTION_CONTRACTORS":                            48,
		"VEHICLE_SPECIALTY_ROAD_CONSTRUCTION_CONTRACTORS":                                                  49,
		"VEHICLE_SPECIALTY_TRAFFIC_CONTROL_CONTRACTORS":                                                    50,
		"VEHICLE_SPECIALTY_MOBILE_MECHANIC_CONTRACTORS":                                                    51,
		"VEHICLE_SPECIALTY_ALL_OTHER_UNITS":                                                                52,
	}
)

func (x VehicleSpecialtyType) Enum() *VehicleSpecialtyType {
	p := new(VehicleSpecialtyType)
	*p = x
	return p
}

func (x VehicleSpecialtyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleSpecialtyType) Descriptor() protoreflect.EnumDescriptor {
	return file_business_auto_model_program_data_proto_enumTypes[1].Descriptor()
}

func (VehicleSpecialtyType) Type() protoreflect.EnumType {
	return &file_business_auto_model_program_data_proto_enumTypes[1]
}

func (x VehicleSpecialtyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleSpecialtyType.Descriptor instead.
func (VehicleSpecialtyType) EnumDescriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{1}
}

type VehicleUse int32

const (
	VehicleUse_VEHICLE_USE_UNSPECIFIED        VehicleUse = 0
	VehicleUse_VEHICLE_USE_TOWING_OPERATIONS  VehicleUse = 1
	VehicleUse_VEHICLE_USE_DUMPING_OPERATIONS VehicleUse = 2
	VehicleUse_VEHICLE_USE_LOGGING_OPERATIONS VehicleUse = 3
	VehicleUse_VEHICLE_USE_OTHER_OPERATIONS   VehicleUse = 4
)

// Enum value maps for VehicleUse.
var (
	VehicleUse_name = map[int32]string{
		0: "VEHICLE_USE_UNSPECIFIED",
		1: "VEHICLE_USE_TOWING_OPERATIONS",
		2: "VEHICLE_USE_DUMPING_OPERATIONS",
		3: "VEHICLE_USE_LOGGING_OPERATIONS",
		4: "VEHICLE_USE_OTHER_OPERATIONS",
	}
	VehicleUse_value = map[string]int32{
		"VEHICLE_USE_UNSPECIFIED":        0,
		"VEHICLE_USE_TOWING_OPERATIONS":  1,
		"VEHICLE_USE_DUMPING_OPERATIONS": 2,
		"VEHICLE_USE_LOGGING_OPERATIONS": 3,
		"VEHICLE_USE_OTHER_OPERATIONS":   4,
	}
)

func (x VehicleUse) Enum() *VehicleUse {
	p := new(VehicleUse)
	*p = x
	return p
}

func (x VehicleUse) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleUse) Descriptor() protoreflect.EnumDescriptor {
	return file_business_auto_model_program_data_proto_enumTypes[2].Descriptor()
}

func (VehicleUse) Type() protoreflect.EnumType {
	return &file_business_auto_model_program_data_proto_enumTypes[2]
}

func (x VehicleUse) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleUse.Descriptor instead.
func (VehicleUse) EnumDescriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{2}
}

type BusinessUse int32

const (
	BusinessUse_BUSINESS_USE_UNSPECIFIED BusinessUse = 0
	BusinessUse_BUSINESS_USE_SERVICE     BusinessUse = 1
	BusinessUse_BUSINESS_USE_RETAIL      BusinessUse = 2
	BusinessUse_BUSINESS_USE_COMMERCIAL  BusinessUse = 3
)

// Enum value maps for BusinessUse.
var (
	BusinessUse_name = map[int32]string{
		0: "BUSINESS_USE_UNSPECIFIED",
		1: "BUSINESS_USE_SERVICE",
		2: "BUSINESS_USE_RETAIL",
		3: "BUSINESS_USE_COMMERCIAL",
	}
	BusinessUse_value = map[string]int32{
		"BUSINESS_USE_UNSPECIFIED": 0,
		"BUSINESS_USE_SERVICE":     1,
		"BUSINESS_USE_RETAIL":      2,
		"BUSINESS_USE_COMMERCIAL":  3,
	}
)

func (x BusinessUse) Enum() *BusinessUse {
	p := new(BusinessUse)
	*p = x
	return p
}

func (x BusinessUse) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessUse) Descriptor() protoreflect.EnumDescriptor {
	return file_business_auto_model_program_data_proto_enumTypes[3].Descriptor()
}

func (BusinessUse) Type() protoreflect.EnumType {
	return &file_business_auto_model_program_data_proto_enumTypes[3]
}

func (x BusinessUse) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessUse.Descriptor instead.
func (BusinessUse) EnumDescriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{3}
}

type StateUsage int32

const (
	StateUsage_STATE_USAGE_UNSPECIFIED StateUsage = 0
	StateUsage_STATE_USAGE_INTRASTATE  StateUsage = 1
	StateUsage_STATE_USAGE_INTERSTATE  StateUsage = 2
)

// Enum value maps for StateUsage.
var (
	StateUsage_name = map[int32]string{
		0: "STATE_USAGE_UNSPECIFIED",
		1: "STATE_USAGE_INTRASTATE",
		2: "STATE_USAGE_INTERSTATE",
	}
	StateUsage_value = map[string]int32{
		"STATE_USAGE_UNSPECIFIED": 0,
		"STATE_USAGE_INTRASTATE":  1,
		"STATE_USAGE_INTERSTATE":  2,
	}
)

func (x StateUsage) Enum() *StateUsage {
	p := new(StateUsage)
	*p = x
	return p
}

func (x StateUsage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StateUsage) Descriptor() protoreflect.EnumDescriptor {
	return file_business_auto_model_program_data_proto_enumTypes[4].Descriptor()
}

func (StateUsage) Type() protoreflect.EnumType {
	return &file_business_auto_model_program_data_proto_enumTypes[4]
}

func (x StateUsage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StateUsage.Descriptor instead.
func (StateUsage) EnumDescriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{4}
}

type QualityRatingGrade int32

const (
	QualityRatingGrade_QUALITY_RATING_GRADE_UNSPECIFIED QualityRatingGrade = 0
	QualityRatingGrade_QUALITY_RATING_GRADE_A           QualityRatingGrade = 1
	QualityRatingGrade_QUALITY_RATING_GRADE_B           QualityRatingGrade = 2
	QualityRatingGrade_QUALITY_RATING_GRADE_C           QualityRatingGrade = 3
	QualityRatingGrade_QUALITY_RATING_GRADE_D           QualityRatingGrade = 4
	QualityRatingGrade_QUALITY_RATING_GRADE_E           QualityRatingGrade = 5
	QualityRatingGrade_QUALITY_RATING_GRADE_F           QualityRatingGrade = 6
)

// Enum value maps for QualityRatingGrade.
var (
	QualityRatingGrade_name = map[int32]string{
		0: "QUALITY_RATING_GRADE_UNSPECIFIED",
		1: "QUALITY_RATING_GRADE_A",
		2: "QUALITY_RATING_GRADE_B",
		3: "QUALITY_RATING_GRADE_C",
		4: "QUALITY_RATING_GRADE_D",
		5: "QUALITY_RATING_GRADE_E",
		6: "QUALITY_RATING_GRADE_F",
	}
	QualityRatingGrade_value = map[string]int32{
		"QUALITY_RATING_GRADE_UNSPECIFIED": 0,
		"QUALITY_RATING_GRADE_A":           1,
		"QUALITY_RATING_GRADE_B":           2,
		"QUALITY_RATING_GRADE_C":           3,
		"QUALITY_RATING_GRADE_D":           4,
		"QUALITY_RATING_GRADE_E":           5,
		"QUALITY_RATING_GRADE_F":           6,
	}
)

func (x QualityRatingGrade) Enum() *QualityRatingGrade {
	p := new(QualityRatingGrade)
	*p = x
	return p
}

func (x QualityRatingGrade) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QualityRatingGrade) Descriptor() protoreflect.EnumDescriptor {
	return file_business_auto_model_program_data_proto_enumTypes[5].Descriptor()
}

func (QualityRatingGrade) Type() protoreflect.EnumType {
	return &file_business_auto_model_program_data_proto_enumTypes[5]
}

func (x QualityRatingGrade) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QualityRatingGrade.Descriptor instead.
func (QualityRatingGrade) EnumDescriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{5}
}

type VehicleType int32

const (
	VehicleType_VEHICLE_TYPE_UNSPECIFIED              VehicleType = 0
	VehicleType_VEHICLE_TYPE_TRUCK                    VehicleType = 1
	VehicleType_VEHICLE_TYPE_TRACTOR                  VehicleType = 2
	VehicleType_VEHICLE_TYPE_TRAILER                  VehicleType = 3
	VehicleType_VEHICLE_TYPE_SEMI_TRAILER             VehicleType = 4
	VehicleType_VEHICLE_TYPE_SERVICE_UTILITY_TRAILER  VehicleType = 5
	VehicleType_VEHICLE_TYPE_PRIVATE_PASSENGER        VehicleType = 6
	VehicleType_VEHICLE_TYPE_CUSTOM_HARVESTER_TRAILER VehicleType = 7
)

// Enum value maps for VehicleType.
var (
	VehicleType_name = map[int32]string{
		0: "VEHICLE_TYPE_UNSPECIFIED",
		1: "VEHICLE_TYPE_TRUCK",
		2: "VEHICLE_TYPE_TRACTOR",
		3: "VEHICLE_TYPE_TRAILER",
		4: "VEHICLE_TYPE_SEMI_TRAILER",
		5: "VEHICLE_TYPE_SERVICE_UTILITY_TRAILER",
		6: "VEHICLE_TYPE_PRIVATE_PASSENGER",
		7: "VEHICLE_TYPE_CUSTOM_HARVESTER_TRAILER",
	}
	VehicleType_value = map[string]int32{
		"VEHICLE_TYPE_UNSPECIFIED":              0,
		"VEHICLE_TYPE_TRUCK":                    1,
		"VEHICLE_TYPE_TRACTOR":                  2,
		"VEHICLE_TYPE_TRAILER":                  3,
		"VEHICLE_TYPE_SEMI_TRAILER":             4,
		"VEHICLE_TYPE_SERVICE_UTILITY_TRAILER":  5,
		"VEHICLE_TYPE_PRIVATE_PASSENGER":        6,
		"VEHICLE_TYPE_CUSTOM_HARVESTER_TRAILER": 7,
	}
)

func (x VehicleType) Enum() *VehicleType {
	p := new(VehicleType)
	*p = x
	return p
}

func (x VehicleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleType) Descriptor() protoreflect.EnumDescriptor {
	return file_business_auto_model_program_data_proto_enumTypes[6].Descriptor()
}

func (VehicleType) Type() protoreflect.EnumType {
	return &file_business_auto_model_program_data_proto_enumTypes[6]
}

func (x VehicleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleType.Descriptor instead.
func (VehicleType) EnumDescriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{6}
}

type VehicleSize int32

const (
	VehicleSize_VEHICLE_SIZE_UNSPECIFIED              VehicleSize = 0
	VehicleSize_VEHICLE_SIZE_LIGHT_TRUCK              VehicleSize = 1
	VehicleSize_VEHICLE_SIZE_MEDIUM_TRUCK             VehicleSize = 2
	VehicleSize_VEHICLE_SIZE_HEAVY_TRUCK              VehicleSize = 3
	VehicleSize_VEHICLE_SIZE_EXTRA_HEAVY_TRUCK        VehicleSize = 4
	VehicleSize_VEHICLE_SIZE_HEAVY_TRACTOR            VehicleSize = 5
	VehicleSize_VEHICLE_SIZE_EXTRA_HEAVY_TRACTOR      VehicleSize = 6
	VehicleSize_VEHICLE_SIZE_SEMI_TRAILER             VehicleSize = 7
	VehicleSize_VEHICLE_SIZE_FULL_TRAILER             VehicleSize = 8
	VehicleSize_VEHICLE_SIZE_SERVICE_UTILITY_TRAILER  VehicleSize = 9
	VehicleSize_VEHICLE_SIZE_CUSTOM_HARVESTER_TRAILER VehicleSize = 10
	VehicleSize_VEHICLE_SIZE_PRIVATE_PASSENGER        VehicleSize = 11
)

// Enum value maps for VehicleSize.
var (
	VehicleSize_name = map[int32]string{
		0:  "VEHICLE_SIZE_UNSPECIFIED",
		1:  "VEHICLE_SIZE_LIGHT_TRUCK",
		2:  "VEHICLE_SIZE_MEDIUM_TRUCK",
		3:  "VEHICLE_SIZE_HEAVY_TRUCK",
		4:  "VEHICLE_SIZE_EXTRA_HEAVY_TRUCK",
		5:  "VEHICLE_SIZE_HEAVY_TRACTOR",
		6:  "VEHICLE_SIZE_EXTRA_HEAVY_TRACTOR",
		7:  "VEHICLE_SIZE_SEMI_TRAILER",
		8:  "VEHICLE_SIZE_FULL_TRAILER",
		9:  "VEHICLE_SIZE_SERVICE_UTILITY_TRAILER",
		10: "VEHICLE_SIZE_CUSTOM_HARVESTER_TRAILER",
		11: "VEHICLE_SIZE_PRIVATE_PASSENGER",
	}
	VehicleSize_value = map[string]int32{
		"VEHICLE_SIZE_UNSPECIFIED":              0,
		"VEHICLE_SIZE_LIGHT_TRUCK":              1,
		"VEHICLE_SIZE_MEDIUM_TRUCK":             2,
		"VEHICLE_SIZE_HEAVY_TRUCK":              3,
		"VEHICLE_SIZE_EXTRA_HEAVY_TRUCK":        4,
		"VEHICLE_SIZE_HEAVY_TRACTOR":            5,
		"VEHICLE_SIZE_EXTRA_HEAVY_TRACTOR":      6,
		"VEHICLE_SIZE_SEMI_TRAILER":             7,
		"VEHICLE_SIZE_FULL_TRAILER":             8,
		"VEHICLE_SIZE_SERVICE_UTILITY_TRAILER":  9,
		"VEHICLE_SIZE_CUSTOM_HARVESTER_TRAILER": 10,
		"VEHICLE_SIZE_PRIVATE_PASSENGER":        11,
	}
)

func (x VehicleSize) Enum() *VehicleSize {
	p := new(VehicleSize)
	*p = x
	return p
}

func (x VehicleSize) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleSize) Descriptor() protoreflect.EnumDescriptor {
	return file_business_auto_model_program_data_proto_enumTypes[7].Descriptor()
}

func (VehicleSize) Type() protoreflect.EnumType {
	return &file_business_auto_model_program_data_proto_enumTypes[7]
}

func (x VehicleSize) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleSize.Descriptor instead.
func (VehicleSize) EnumDescriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{7}
}

type RadiusOfOperation int32

const (
	RadiusOfOperation_MAX_RADIUS_OF_OPERATION_UNSPECIFIED      RadiusOfOperation = 0
	RadiusOfOperation_MAX_RADIUS_OF_OPERATION_O_TO_100_MILES   RadiusOfOperation = 1
	RadiusOfOperation_MAX_RADIUS_OF_OPERATION_101_TO_300_MILES RadiusOfOperation = 2
	RadiusOfOperation_MAX_RADIUS_OF_OPERATION_301_PLUS_MILES   RadiusOfOperation = 3
)

// Enum value maps for RadiusOfOperation.
var (
	RadiusOfOperation_name = map[int32]string{
		0: "MAX_RADIUS_OF_OPERATION_UNSPECIFIED",
		1: "MAX_RADIUS_OF_OPERATION_O_TO_100_MILES",
		2: "MAX_RADIUS_OF_OPERATION_101_TO_300_MILES",
		3: "MAX_RADIUS_OF_OPERATION_301_PLUS_MILES",
	}
	RadiusOfOperation_value = map[string]int32{
		"MAX_RADIUS_OF_OPERATION_UNSPECIFIED":      0,
		"MAX_RADIUS_OF_OPERATION_O_TO_100_MILES":   1,
		"MAX_RADIUS_OF_OPERATION_101_TO_300_MILES": 2,
		"MAX_RADIUS_OF_OPERATION_301_PLUS_MILES":   3,
	}
)

func (x RadiusOfOperation) Enum() *RadiusOfOperation {
	p := new(RadiusOfOperation)
	*p = x
	return p
}

func (x RadiusOfOperation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RadiusOfOperation) Descriptor() protoreflect.EnumDescriptor {
	return file_business_auto_model_program_data_proto_enumTypes[8].Descriptor()
}

func (RadiusOfOperation) Type() protoreflect.EnumType {
	return &file_business_auto_model_program_data_proto_enumTypes[8]
}

func (x RadiusOfOperation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RadiusOfOperation.Descriptor instead.
func (RadiusOfOperation) EnumDescriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{8}
}

type IndustryType int32

const (
	IndustryType_INDUSTRY_TYPE_UNSPECIFIED                                     IndustryType = 0
	IndustryType_INDUSTRY_TYPE_CONTRACTOR_TRUCKS                               IndustryType = 1
	IndustryType_INDUSTRY_TYPE_SPECIALIZED_DELIVERY_VEHICLES                   IndustryType = 2
	IndustryType_INDUSTRY_TYPE_COURIER_SERVICE_VEHICLES                        IndustryType = 3
	IndustryType_INDUSTRY_TYPE_FOOD_DELIVERY_TRUCKS                            IndustryType = 4
	IndustryType_INDUSTRY_TYPE_WASTE_DISPOSAL_TRUCKS                           IndustryType = 5
	IndustryType_INDUSTRY_TYPE_WASTE_OIL_AND_OTHER_LIQUID_WASTE_TRANSPORTATION IndustryType = 6
	IndustryType_INDUSTRY_TYPE_FARMER_TRUCKS                                   IndustryType = 7
	IndustryType_INDUSTRY_TYPE_CEMENT_MIXERS                                   IndustryType = 8
	IndustryType_INDUSTRY_TYPE_HOUSE_MOVING                                    IndustryType = 9
	IndustryType_INDUSTRY_TYPE_MOVING_OPERATIONS                               IndustryType = 10
	IndustryType_INDUSTRY_TYPE_LAWN_TREE_SERVICE_TRUCKS                        IndustryType = 11
	IndustryType_INDUSTRY_TYPE_CATERER_VEHICLES                                IndustryType = 12
	IndustryType_INDUSTRY_TYPE_MOBILE_CONCESSION_TRUCKS                        IndustryType = 13
	IndustryType_INDUSTRY_TYPE_WHOLESALERS_MANUFACTURERS                       IndustryType = 14
	IndustryType_INDUSTRY_TYPE_SALVAGE_HAULERS                                 IndustryType = 15
	IndustryType_INDUSTRY_TYPE_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE          IndustryType = 16
	IndustryType_INDUSTRY_TYPE_CAR_CARRIERS_NOT_FOR_HIRE                       IndustryType = 17
	IndustryType_INDUSTRY_TYPE_NOT_OTHERWISE_CLASSIFIED_TRUCK                  IndustryType = 18
	IndustryType_INDUSTRY_TYPE_LOGGING_TRUCKS_FOR_HIRE                         IndustryType = 19
	IndustryType_INDUSTRY_TYPE_LOGGING_TRUCKS_NOT_FOR_HIRE                     IndustryType = 20
	IndustryType_INDUSTRY_TYPE_TOW_TRUCKS_INCIDENTAL_USE                       IndustryType = 21
	IndustryType_INDUSTRY_TYPE_TOW_TRUCKS_FULL_TIME                            IndustryType = 22
	IndustryType_INDUSTRY_TYPE_DUMPING_OPERATIONS_FOR_HIRE                     IndustryType = 23
	IndustryType_INDUSTRY_TYPE_DUMPING_OPERATIONS_NOT_FOR_HIRE                 IndustryType = 24
	IndustryType_INDUSTRY_TYPE_SERVICE_USE                                     IndustryType = 25
)

// Enum value maps for IndustryType.
var (
	IndustryType_name = map[int32]string{
		0:  "INDUSTRY_TYPE_UNSPECIFIED",
		1:  "INDUSTRY_TYPE_CONTRACTOR_TRUCKS",
		2:  "INDUSTRY_TYPE_SPECIALIZED_DELIVERY_VEHICLES",
		3:  "INDUSTRY_TYPE_COURIER_SERVICE_VEHICLES",
		4:  "INDUSTRY_TYPE_FOOD_DELIVERY_TRUCKS",
		5:  "INDUSTRY_TYPE_WASTE_DISPOSAL_TRUCKS",
		6:  "INDUSTRY_TYPE_WASTE_OIL_AND_OTHER_LIQUID_WASTE_TRANSPORTATION",
		7:  "INDUSTRY_TYPE_FARMER_TRUCKS",
		8:  "INDUSTRY_TYPE_CEMENT_MIXERS",
		9:  "INDUSTRY_TYPE_HOUSE_MOVING",
		10: "INDUSTRY_TYPE_MOVING_OPERATIONS",
		11: "INDUSTRY_TYPE_LAWN_TREE_SERVICE_TRUCKS",
		12: "INDUSTRY_TYPE_CATERER_VEHICLES",
		13: "INDUSTRY_TYPE_MOBILE_CONCESSION_TRUCKS",
		14: "INDUSTRY_TYPE_WHOLESALERS_MANUFACTURERS",
		15: "INDUSTRY_TYPE_SALVAGE_HAULERS",
		16: "INDUSTRY_TYPE_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE",
		17: "INDUSTRY_TYPE_CAR_CARRIERS_NOT_FOR_HIRE",
		18: "INDUSTRY_TYPE_NOT_OTHERWISE_CLASSIFIED_TRUCK",
		19: "INDUSTRY_TYPE_LOGGING_TRUCKS_FOR_HIRE",
		20: "INDUSTRY_TYPE_LOGGING_TRUCKS_NOT_FOR_HIRE",
		21: "INDUSTRY_TYPE_TOW_TRUCKS_INCIDENTAL_USE",
		22: "INDUSTRY_TYPE_TOW_TRUCKS_FULL_TIME",
		23: "INDUSTRY_TYPE_DUMPING_OPERATIONS_FOR_HIRE",
		24: "INDUSTRY_TYPE_DUMPING_OPERATIONS_NOT_FOR_HIRE",
		25: "INDUSTRY_TYPE_SERVICE_USE",
	}
	IndustryType_value = map[string]int32{
		"INDUSTRY_TYPE_UNSPECIFIED":                                     0,
		"INDUSTRY_TYPE_CONTRACTOR_TRUCKS":                               1,
		"INDUSTRY_TYPE_SPECIALIZED_DELIVERY_VEHICLES":                   2,
		"INDUSTRY_TYPE_COURIER_SERVICE_VEHICLES":                        3,
		"INDUSTRY_TYPE_FOOD_DELIVERY_TRUCKS":                            4,
		"INDUSTRY_TYPE_WASTE_DISPOSAL_TRUCKS":                           5,
		"INDUSTRY_TYPE_WASTE_OIL_AND_OTHER_LIQUID_WASTE_TRANSPORTATION": 6,
		"INDUSTRY_TYPE_FARMER_TRUCKS":                                   7,
		"INDUSTRY_TYPE_CEMENT_MIXERS":                                   8,
		"INDUSTRY_TYPE_HOUSE_MOVING":                                    9,
		"INDUSTRY_TYPE_MOVING_OPERATIONS":                               10,
		"INDUSTRY_TYPE_LAWN_TREE_SERVICE_TRUCKS":                        11,
		"INDUSTRY_TYPE_CATERER_VEHICLES":                                12,
		"INDUSTRY_TYPE_MOBILE_CONCESSION_TRUCKS":                        13,
		"INDUSTRY_TYPE_WHOLESALERS_MANUFACTURERS":                       14,
		"INDUSTRY_TYPE_SALVAGE_HAULERS":                                 15,
		"INDUSTRY_TYPE_GAS_AND_CRUDE_OIL_HAULERS_NOT_FOR_HIRE":          16,
		"INDUSTRY_TYPE_CAR_CARRIERS_NOT_FOR_HIRE":                       17,
		"INDUSTRY_TYPE_NOT_OTHERWISE_CLASSIFIED_TRUCK":                  18,
		"INDUSTRY_TYPE_LOGGING_TRUCKS_FOR_HIRE":                         19,
		"INDUSTRY_TYPE_LOGGING_TRUCKS_NOT_FOR_HIRE":                     20,
		"INDUSTRY_TYPE_TOW_TRUCKS_INCIDENTAL_USE":                       21,
		"INDUSTRY_TYPE_TOW_TRUCKS_FULL_TIME":                            22,
		"INDUSTRY_TYPE_DUMPING_OPERATIONS_FOR_HIRE":                     23,
		"INDUSTRY_TYPE_DUMPING_OPERATIONS_NOT_FOR_HIRE":                 24,
		"INDUSTRY_TYPE_SERVICE_USE":                                     25,
	}
)

func (x IndustryType) Enum() *IndustryType {
	p := new(IndustryType)
	*p = x
	return p
}

func (x IndustryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IndustryType) Descriptor() protoreflect.EnumDescriptor {
	return file_business_auto_model_program_data_proto_enumTypes[9].Descriptor()
}

func (IndustryType) Type() protoreflect.EnumType {
	return &file_business_auto_model_program_data_proto_enumTypes[9]
}

func (x IndustryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IndustryType.Descriptor instead.
func (IndustryType) EnumDescriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{9}
}

type TrailerType int32

const (
	TrailerType_TRAILER_TYPE_UNSPECIFIED              TrailerType = 0
	TrailerType_TRAILER_TYPE_SEMI_TRAILER             TrailerType = 1
	TrailerType_TRAILER_TYPE_FULL_TRAILER             TrailerType = 2
	TrailerType_TRAILER_TYPE_SERVICE_UTILITY_TRAILER  TrailerType = 3
	TrailerType_TRAILER_TYPE_CUSTOM_HARVESTER_TRAILER TrailerType = 4
)

// Enum value maps for TrailerType.
var (
	TrailerType_name = map[int32]string{
		0: "TRAILER_TYPE_UNSPECIFIED",
		1: "TRAILER_TYPE_SEMI_TRAILER",
		2: "TRAILER_TYPE_FULL_TRAILER",
		3: "TRAILER_TYPE_SERVICE_UTILITY_TRAILER",
		4: "TRAILER_TYPE_CUSTOM_HARVESTER_TRAILER",
	}
	TrailerType_value = map[string]int32{
		"TRAILER_TYPE_UNSPECIFIED":              0,
		"TRAILER_TYPE_SEMI_TRAILER":             1,
		"TRAILER_TYPE_FULL_TRAILER":             2,
		"TRAILER_TYPE_SERVICE_UTILITY_TRAILER":  3,
		"TRAILER_TYPE_CUSTOM_HARVESTER_TRAILER": 4,
	}
)

func (x TrailerType) Enum() *TrailerType {
	p := new(TrailerType)
	*p = x
	return p
}

func (x TrailerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrailerType) Descriptor() protoreflect.EnumDescriptor {
	return file_business_auto_model_program_data_proto_enumTypes[10].Descriptor()
}

func (TrailerType) Type() protoreflect.EnumType {
	return &file_business_auto_model_program_data_proto_enumTypes[10]
}

func (x TrailerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrailerType.Descriptor instead.
func (TrailerType) EnumDescriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{10}
}

type ProgramData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CompanyInfo           *CompanyInfo           `protobuf:"bytes,1,opt,name=companyInfo,proto3" json:"companyInfo,omitempty"`
	Vehicles              []*VehicleData         `protobuf:"bytes,2,rep,name=vehicles,proto3" json:"vehicles,omitempty"`
	FilingsInfo           *FilingsInfo           `protobuf:"bytes,3,opt,name=filingsInfo,proto3,oneof" json:"filingsInfo,omitempty"`
	UnderwritingOverrides *UnderwritingOverrides `protobuf:"bytes,4,opt,name=underwritingOverrides,proto3,oneof" json:"underwritingOverrides,omitempty"`
}

func (x *ProgramData) Reset() {
	*x = ProgramData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_business_auto_model_program_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgramData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgramData) ProtoMessage() {}

func (x *ProgramData) ProtoReflect() protoreflect.Message {
	mi := &file_business_auto_model_program_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgramData.ProtoReflect.Descriptor instead.
func (*ProgramData) Descriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{0}
}

func (x *ProgramData) GetCompanyInfo() *CompanyInfo {
	if x != nil {
		return x.CompanyInfo
	}
	return nil
}

func (x *ProgramData) GetVehicles() []*VehicleData {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

func (x *ProgramData) GetFilingsInfo() *FilingsInfo {
	if x != nil {
		return x.FilingsInfo
	}
	return nil
}

func (x *ProgramData) GetUnderwritingOverrides() *UnderwritingOverrides {
	if x != nil {
		return x.UnderwritingOverrides
	}
	return nil
}

type CompanyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber                        *int32         `protobuf:"varint,1,opt,name=dotNumber,proto3,oneof" json:"dotNumber,omitempty"`
	Fein                             *string        `protobuf:"bytes,2,opt,name=fein,proto3,oneof" json:"fein,omitempty"`
	NoOfPowerUnits                   int32          `protobuf:"varint,3,opt,name=noOfPowerUnits,proto3" json:"noOfPowerUnits,omitempty"`
	HasIndividualNamedInsured        bool           `protobuf:"varint,4,opt,name=hasIndividualNamedInsured,proto3" json:"hasIndividualNamedInsured,omitempty"`
	NoOfEmployees                    int32          `protobuf:"varint,5,opt,name=noOfEmployees,proto3" json:"noOfEmployees,omitempty"`
	PerOfEmployeesOperatingOwnAutos  float32        `protobuf:"fixed32,6,opt,name=perOfEmployeesOperatingOwnAutos,proto3" json:"perOfEmployeesOperatingOwnAutos,omitempty"`
	PrimaryIndustryClassification    IndustryType   `protobuf:"varint,7,opt,name=primaryIndustryClassification,proto3,enum=business_auto_model.IndustryType" json:"primaryIndustryClassification,omitempty"`
	SecondaryIndustryClassifications []IndustryType `protobuf:"varint,8,rep,packed,name=secondaryIndustryClassifications,proto3,enum=business_auto_model.IndustryType" json:"secondaryIndustryClassifications,omitempty"`
	AnnualCostOfHire                 *float32       `protobuf:"fixed32,9,opt,name=annualCostOfHire,proto3,oneof" json:"annualCostOfHire,omitempty"`
}

func (x *CompanyInfo) Reset() {
	*x = CompanyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_business_auto_model_program_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyInfo) ProtoMessage() {}

func (x *CompanyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_business_auto_model_program_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyInfo.ProtoReflect.Descriptor instead.
func (*CompanyInfo) Descriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{1}
}

func (x *CompanyInfo) GetDotNumber() int32 {
	if x != nil && x.DotNumber != nil {
		return *x.DotNumber
	}
	return 0
}

func (x *CompanyInfo) GetFein() string {
	if x != nil && x.Fein != nil {
		return *x.Fein
	}
	return ""
}

func (x *CompanyInfo) GetNoOfPowerUnits() int32 {
	if x != nil {
		return x.NoOfPowerUnits
	}
	return 0
}

func (x *CompanyInfo) GetHasIndividualNamedInsured() bool {
	if x != nil {
		return x.HasIndividualNamedInsured
	}
	return false
}

func (x *CompanyInfo) GetNoOfEmployees() int32 {
	if x != nil {
		return x.NoOfEmployees
	}
	return 0
}

func (x *CompanyInfo) GetPerOfEmployeesOperatingOwnAutos() float32 {
	if x != nil {
		return x.PerOfEmployeesOperatingOwnAutos
	}
	return 0
}

func (x *CompanyInfo) GetPrimaryIndustryClassification() IndustryType {
	if x != nil {
		return x.PrimaryIndustryClassification
	}
	return IndustryType_INDUSTRY_TYPE_UNSPECIFIED
}

func (x *CompanyInfo) GetSecondaryIndustryClassifications() []IndustryType {
	if x != nil {
		return x.SecondaryIndustryClassifications
	}
	return nil
}

func (x *CompanyInfo) GetAnnualCostOfHire() float32 {
	if x != nil && x.AnnualCostOfHire != nil {
		return *x.AnnualCostOfHire
	}
	return 0
}

type VehicleData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin                              string               `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	Year                             int32                `protobuf:"varint,2,opt,name=year,proto3" json:"year,omitempty"`
	Make                             string               `protobuf:"bytes,3,opt,name=make,proto3" json:"make,omitempty"`
	Model                            string               `protobuf:"bytes,4,opt,name=model,proto3" json:"model,omitempty"`
	VehicleType                      proto.VehicleType    `protobuf:"varint,5,opt,name=vehicleType,proto3,enum=insurance_core.VehicleType" json:"vehicleType,omitempty"`
	WeightClass                      VehicleWeightClass   `protobuf:"varint,6,opt,name=weightClass,proto3,enum=business_auto_model.VehicleWeightClass" json:"weightClass,omitempty"`
	SpecialtyVehicleType             VehicleSpecialtyType `protobuf:"varint,7,opt,name=specialtyVehicleType,proto3,enum=business_auto_model.VehicleSpecialtyType" json:"specialtyVehicleType,omitempty"`
	VehicleUse                       VehicleUse           `protobuf:"varint,8,opt,name=vehicleUse,proto3,enum=business_auto_model.VehicleUse" json:"vehicleUse,omitempty"`
	BusinessUse                      BusinessUse          `protobuf:"varint,9,opt,name=businessUse,proto3,enum=business_auto_model.BusinessUse" json:"businessUse,omitempty"`
	TrailerType                      *TrailerType         `protobuf:"varint,10,opt,name=trailerType,proto3,enum=business_auto_model.TrailerType,oneof" json:"trailerType,omitempty"`
	StateUsage                       StateUsage           `protobuf:"varint,11,opt,name=stateUsage,proto3,enum=business_auto_model.StateUsage" json:"stateUsage,omitempty"`
	RadiusClassification             RadiusOfOperation    `protobuf:"varint,12,opt,name=radiusClassification,proto3,enum=business_auto_model.RadiusOfOperation" json:"radiusClassification,omitempty"`
	StatedValue                      *float32             `protobuf:"fixed32,13,opt,name=statedValue,proto3,oneof" json:"statedValue,omitempty"`
	ApdDeductible                    *int64               `protobuf:"varint,14,opt,name=apdDeductible,proto3,oneof" json:"apdDeductible,omitempty"`
	PrincipalGaragingLocationZipCode string               `protobuf:"bytes,15,opt,name=principalGaragingLocationZipCode,proto3" json:"principalGaragingLocationZipCode,omitempty"`
	IsGlassLinedTankTruckOrTrailer   *bool                `protobuf:"varint,16,opt,name=isGlassLinedTankTruckOrTrailer,proto3,oneof" json:"isGlassLinedTankTruckOrTrailer,omitempty"`
	IsRefrigeratedTruckOrTrailer     *bool                `protobuf:"varint,17,opt,name=isRefrigeratedTruckOrTrailer,proto3,oneof" json:"isRefrigeratedTruckOrTrailer,omitempty"`
	IsDoubleTrailer                  *bool                `protobuf:"varint,18,opt,name=isDoubleTrailer,proto3,oneof" json:"isDoubleTrailer,omitempty"`
	HasAntiLockBrakes                *bool                `protobuf:"varint,19,opt,name=hasAntiLockBrakes,proto3,oneof" json:"hasAntiLockBrakes,omitempty"`
}

func (x *VehicleData) Reset() {
	*x = VehicleData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_business_auto_model_program_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleData) ProtoMessage() {}

func (x *VehicleData) ProtoReflect() protoreflect.Message {
	mi := &file_business_auto_model_program_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleData.ProtoReflect.Descriptor instead.
func (*VehicleData) Descriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{2}
}

func (x *VehicleData) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *VehicleData) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *VehicleData) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *VehicleData) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *VehicleData) GetVehicleType() proto.VehicleType {
	if x != nil {
		return x.VehicleType
	}
	return proto.VehicleType(0)
}

func (x *VehicleData) GetWeightClass() VehicleWeightClass {
	if x != nil {
		return x.WeightClass
	}
	return VehicleWeightClass_VEHICLE_WEIGHT_CLASS_UNSPECIFIED
}

func (x *VehicleData) GetSpecialtyVehicleType() VehicleSpecialtyType {
	if x != nil {
		return x.SpecialtyVehicleType
	}
	return VehicleSpecialtyType_VEHICLE_SPECIALTY_TYPE_UNSPECIFIED
}

func (x *VehicleData) GetVehicleUse() VehicleUse {
	if x != nil {
		return x.VehicleUse
	}
	return VehicleUse_VEHICLE_USE_UNSPECIFIED
}

func (x *VehicleData) GetBusinessUse() BusinessUse {
	if x != nil {
		return x.BusinessUse
	}
	return BusinessUse_BUSINESS_USE_UNSPECIFIED
}

func (x *VehicleData) GetTrailerType() TrailerType {
	if x != nil && x.TrailerType != nil {
		return *x.TrailerType
	}
	return TrailerType_TRAILER_TYPE_UNSPECIFIED
}

func (x *VehicleData) GetStateUsage() StateUsage {
	if x != nil {
		return x.StateUsage
	}
	return StateUsage_STATE_USAGE_UNSPECIFIED
}

func (x *VehicleData) GetRadiusClassification() RadiusOfOperation {
	if x != nil {
		return x.RadiusClassification
	}
	return RadiusOfOperation_MAX_RADIUS_OF_OPERATION_UNSPECIFIED
}

func (x *VehicleData) GetStatedValue() float32 {
	if x != nil && x.StatedValue != nil {
		return *x.StatedValue
	}
	return 0
}

func (x *VehicleData) GetApdDeductible() int64 {
	if x != nil && x.ApdDeductible != nil {
		return *x.ApdDeductible
	}
	return 0
}

func (x *VehicleData) GetPrincipalGaragingLocationZipCode() string {
	if x != nil {
		return x.PrincipalGaragingLocationZipCode
	}
	return ""
}

func (x *VehicleData) GetIsGlassLinedTankTruckOrTrailer() bool {
	if x != nil && x.IsGlassLinedTankTruckOrTrailer != nil {
		return *x.IsGlassLinedTankTruckOrTrailer
	}
	return false
}

func (x *VehicleData) GetIsRefrigeratedTruckOrTrailer() bool {
	if x != nil && x.IsRefrigeratedTruckOrTrailer != nil {
		return *x.IsRefrigeratedTruckOrTrailer
	}
	return false
}

func (x *VehicleData) GetIsDoubleTrailer() bool {
	if x != nil && x.IsDoubleTrailer != nil {
		return *x.IsDoubleTrailer
	}
	return false
}

func (x *VehicleData) GetHasAntiLockBrakes() bool {
	if x != nil && x.HasAntiLockBrakes != nil {
		return *x.HasAntiLockBrakes
	}
	return false
}

type RadiusBucket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinRadiusInMiles float32  `protobuf:"fixed32,1,opt,name=minRadiusInMiles,proto3" json:"minRadiusInMiles,omitempty"`
	MaxRadiusInMiles *float32 `protobuf:"fixed32,2,opt,name=maxRadiusInMiles,proto3,oneof" json:"maxRadiusInMiles,omitempty"`
	Percentage       float32  `protobuf:"fixed32,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *RadiusBucket) Reset() {
	*x = RadiusBucket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_business_auto_model_program_data_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RadiusBucket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RadiusBucket) ProtoMessage() {}

func (x *RadiusBucket) ProtoReflect() protoreflect.Message {
	mi := &file_business_auto_model_program_data_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RadiusBucket.ProtoReflect.Descriptor instead.
func (*RadiusBucket) Descriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{3}
}

func (x *RadiusBucket) GetMinRadiusInMiles() float32 {
	if x != nil {
		return x.MinRadiusInMiles
	}
	return 0
}

func (x *RadiusBucket) GetMaxRadiusInMiles() float32 {
	if x != nil && x.MaxRadiusInMiles != nil {
		return *x.MaxRadiusInMiles
	}
	return 0
}

func (x *RadiusBucket) GetPercentage() float32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type FilingsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HasMultiStateFilings  bool `protobuf:"varint,1,opt,name=hasMultiStateFilings,proto3" json:"hasMultiStateFilings,omitempty"`
	HasSingleStateFilings bool `protobuf:"varint,2,opt,name=hasSingleStateFilings,proto3" json:"hasSingleStateFilings,omitempty"`
	HasFMCSAFilings       bool `protobuf:"varint,3,opt,name=hasFMCSAFilings,proto3" json:"hasFMCSAFilings,omitempty"`
	HasDOTFilings         bool `protobuf:"varint,4,opt,name=hasDOTFilings,proto3" json:"hasDOTFilings,omitempty"`
}

func (x *FilingsInfo) Reset() {
	*x = FilingsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_business_auto_model_program_data_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilingsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilingsInfo) ProtoMessage() {}

func (x *FilingsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_business_auto_model_program_data_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilingsInfo.ProtoReflect.Descriptor instead.
func (*FilingsInfo) Descriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{4}
}

func (x *FilingsInfo) GetHasMultiStateFilings() bool {
	if x != nil {
		return x.HasMultiStateFilings
	}
	return false
}

func (x *FilingsInfo) GetHasSingleStateFilings() bool {
	if x != nil {
		return x.HasSingleStateFilings
	}
	return false
}

func (x *FilingsInfo) GetHasFMCSAFilings() bool {
	if x != nil {
		return x.HasFMCSAFilings
	}
	return false
}

func (x *FilingsInfo) GetHasDOTFilings() bool {
	if x != nil {
		return x.HasDOTFilings
	}
	return false
}

type UnderwritingOverrides struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoveragesWithLossFreeCredits []string            `protobuf:"bytes,1,rep,name=coveragesWithLossFreeCredits,proto3" json:"coveragesWithLossFreeCredits,omitempty"`
	LossFreeCreditPercentage     *float32            `protobuf:"fixed32,2,opt,name=lossFreeCreditPercentage,proto3,oneof" json:"lossFreeCreditPercentage,omitempty"`
	DriverFactor                 *DriverFactor       `protobuf:"bytes,3,opt,name=driverFactor,proto3,oneof" json:"driverFactor,omitempty"`
	ScheduleMods                 *ScheduleMod        `protobuf:"bytes,4,opt,name=scheduleMods,proto3,oneof" json:"scheduleMods,omitempty"`
	ExperienceMod                *ExperienceMod      `protobuf:"bytes,5,opt,name=experienceMod,proto3,oneof" json:"experienceMod,omitempty"`
	QualityRatingGrade           *QualityRatingGrade `protobuf:"varint,6,opt,name=qualityRatingGrade,proto3,enum=business_auto_model.QualityRatingGrade,oneof" json:"qualityRatingGrade,omitempty"`
}

func (x *UnderwritingOverrides) Reset() {
	*x = UnderwritingOverrides{}
	if protoimpl.UnsafeEnabled {
		mi := &file_business_auto_model_program_data_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnderwritingOverrides) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnderwritingOverrides) ProtoMessage() {}

func (x *UnderwritingOverrides) ProtoReflect() protoreflect.Message {
	mi := &file_business_auto_model_program_data_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnderwritingOverrides.ProtoReflect.Descriptor instead.
func (*UnderwritingOverrides) Descriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{5}
}

func (x *UnderwritingOverrides) GetCoveragesWithLossFreeCredits() []string {
	if x != nil {
		return x.CoveragesWithLossFreeCredits
	}
	return nil
}

func (x *UnderwritingOverrides) GetLossFreeCreditPercentage() float32 {
	if x != nil && x.LossFreeCreditPercentage != nil {
		return *x.LossFreeCreditPercentage
	}
	return 0
}

func (x *UnderwritingOverrides) GetDriverFactor() *DriverFactor {
	if x != nil {
		return x.DriverFactor
	}
	return nil
}

func (x *UnderwritingOverrides) GetScheduleMods() *ScheduleMod {
	if x != nil {
		return x.ScheduleMods
	}
	return nil
}

func (x *UnderwritingOverrides) GetExperienceMod() *ExperienceMod {
	if x != nil {
		return x.ExperienceMod
	}
	return nil
}

func (x *UnderwritingOverrides) GetQualityRatingGrade() QualityRatingGrade {
	if x != nil && x.QualityRatingGrade != nil {
		return *x.QualityRatingGrade
	}
	return QualityRatingGrade_QUALITY_RATING_GRADE_UNSPECIFIED
}

type DriverFactor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Factor *float32 `protobuf:"fixed32,1,opt,name=factor,proto3,oneof" json:"factor,omitempty"`
}

func (x *DriverFactor) Reset() {
	*x = DriverFactor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_business_auto_model_program_data_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DriverFactor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverFactor) ProtoMessage() {}

func (x *DriverFactor) ProtoReflect() protoreflect.Message {
	mi := &file_business_auto_model_program_data_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverFactor.ProtoReflect.Descriptor instead.
func (*DriverFactor) Descriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{6}
}

func (x *DriverFactor) GetFactor() float32 {
	if x != nil && x.Factor != nil {
		return *x.Factor
	}
	return 0
}

type ScheduleMod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mods map[string]float32 `protobuf:"bytes,1,rep,name=mods,proto3" json:"mods,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *ScheduleMod) Reset() {
	*x = ScheduleMod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_business_auto_model_program_data_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleMod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleMod) ProtoMessage() {}

func (x *ScheduleMod) ProtoReflect() protoreflect.Message {
	mi := &file_business_auto_model_program_data_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleMod.ProtoReflect.Descriptor instead.
func (*ScheduleMod) Descriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{7}
}

func (x *ScheduleMod) GetMods() map[string]float32 {
	if x != nil {
		return x.Mods
	}
	return nil
}

type ExperienceMod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mods map[string]float32 `protobuf:"bytes,1,rep,name=mods,proto3" json:"mods,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
}

func (x *ExperienceMod) Reset() {
	*x = ExperienceMod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_business_auto_model_program_data_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExperienceMod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExperienceMod) ProtoMessage() {}

func (x *ExperienceMod) ProtoReflect() protoreflect.Message {
	mi := &file_business_auto_model_program_data_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExperienceMod.ProtoReflect.Descriptor instead.
func (*ExperienceMod) Descriptor() ([]byte, []int) {
	return file_business_auto_model_program_data_proto_rawDescGZIP(), []int{8}
}

func (x *ExperienceMod) GetMods() map[string]float32 {
	if x != nil {
		return x.Mods
	}
	return nil
}

var File_business_auto_model_program_data_proto protoreflect.FileDescriptor

var file_business_auto_model_program_data_proto_rawDesc = []byte{
	0x0a, 0x26, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x1c, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe9, 0x02, 0x0a, 0x0b,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x0b, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x3c, 0x0a, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x12, 0x47, 0x0a,
	0x0b, 0x66, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a, 0x15, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x77,
	0x72, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x55, 0x6e, 0x64, 0x65,
	0x72, 0x77, 0x72, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x48, 0x01, 0x52, 0x15, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x69, 0x6e,
	0x67, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x66, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x18, 0x0a,
	0x16, 0x5f, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x22, 0xd4, 0x04, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x09, 0x64, 0x6f,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x66, 0x65,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x66, 0x65, 0x69, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0e, 0x6e, 0x6f, 0x4f, 0x66, 0x50, 0x6f, 0x77, 0x65, 0x72,
	0x55, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6e, 0x6f, 0x4f,
	0x66, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x19, 0x68,
	0x61, 0x73, 0x49, 0x6e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x64, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19,
	0x68, 0x61, 0x73, 0x49, 0x6e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x64, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x6f, 0x4f,
	0x66, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0d, 0x6e, 0x6f, 0x4f, 0x66, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73, 0x12,
	0x48, 0x0a, 0x1f, 0x70, 0x65, 0x72, 0x4f, 0x66, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65,
	0x73, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x4f, 0x77, 0x6e, 0x41, 0x75, 0x74,
	0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1f, 0x70, 0x65, 0x72, 0x4f, 0x66, 0x45,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e,
	0x67, 0x4f, 0x77, 0x6e, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x12, 0x67, 0x0a, 0x1d, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x21, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x1d, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x64, 0x75,
	0x73, 0x74, 0x72, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x6d, 0x0a, 0x20, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x49,
	0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x20, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74,
	0x72, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x2f, 0x0a, 0x10, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x4f,
	0x66, 0x48, 0x69, 0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x48, 0x02, 0x52, 0x10, 0x61,
	0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x66, 0x65, 0x69, 0x6e, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x61, 0x6e,
	0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x22, 0xe7,
	0x09, 0x0a, 0x0b, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10,
	0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x79, 0x65, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x3d,
	0x0a, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a,
	0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x27, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0b, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x5d, 0x0a, 0x14, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x61, 0x6c, 0x74, 0x79, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x14, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x74, 0x79, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x55, 0x73, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x52, 0x0a, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x52,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0b,
	0x74, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5a, 0x0a, 0x14, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x61, 0x64, 0x69, 0x75,
	0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x72, 0x61,
	0x64, 0x69, 0x75, 0x73, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0d, 0x61, 0x70, 0x64,
	0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x02, 0x52, 0x0d, 0x61, 0x70, 0x64, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x20, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61,
	0x6c, 0x47, 0x61, 0x72, 0x61, 0x67, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x20,
	0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x47, 0x61, 0x72, 0x61, 0x67, 0x69, 0x6e,
	0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x4b, 0x0a, 0x1e, 0x69, 0x73, 0x47, 0x6c, 0x61, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x64,
	0x54, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x4f, 0x72, 0x54, 0x72, 0x61, 0x69, 0x6c,
	0x65, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x1e, 0x69, 0x73, 0x47, 0x6c,
	0x61, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x64, 0x54, 0x61, 0x6e, 0x6b, 0x54, 0x72, 0x75, 0x63,
	0x6b, 0x4f, 0x72, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x47, 0x0a,
	0x1c, 0x69, 0x73, 0x52, 0x65, 0x66, 0x72, 0x69, 0x67, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x72, 0x75, 0x63, 0x6b, 0x4f, 0x72, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x1c, 0x69, 0x73, 0x52, 0x65, 0x66, 0x72, 0x69, 0x67, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x4f, 0x72, 0x54, 0x72, 0x61, 0x69,
	0x6c, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0f, 0x69, 0x73, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x05, 0x52, 0x0f, 0x69, 0x73, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6c,
	0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x11, 0x68, 0x61, 0x73, 0x41, 0x6e, 0x74, 0x69,
	0x4c, 0x6f, 0x63, 0x6b, 0x42, 0x72, 0x61, 0x6b, 0x65, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x06, 0x52, 0x11, 0x68, 0x61, 0x73, 0x41, 0x6e, 0x74, 0x69, 0x4c, 0x6f, 0x63, 0x6b, 0x42,
	0x72, 0x61, 0x6b, 0x65, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x72, 0x61,
	0x69, 0x6c, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x61, 0x70, 0x64,
	0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x42, 0x21, 0x0a, 0x1f, 0x5f, 0x69,
	0x73, 0x47, 0x6c, 0x61, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x64, 0x54, 0x61, 0x6e, 0x6b, 0x54,
	0x72, 0x75, 0x63, 0x6b, 0x4f, 0x72, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x42, 0x1f, 0x0a,
	0x1d, 0x5f, 0x69, 0x73, 0x52, 0x65, 0x66, 0x72, 0x69, 0x67, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x72, 0x75, 0x63, 0x6b, 0x4f, 0x72, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x42, 0x12,
	0x0a, 0x10, 0x5f, 0x69, 0x73, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6c,
	0x65, 0x72, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x68, 0x61, 0x73, 0x41, 0x6e, 0x74, 0x69, 0x4c, 0x6f,
	0x63, 0x6b, 0x42, 0x72, 0x61, 0x6b, 0x65, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x0c, 0x52, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x6d, 0x69, 0x6e,
	0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x49, 0x6e, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x10, 0x6d, 0x69, 0x6e, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x49, 0x6e,
	0x4d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x10, 0x6d, 0x61, 0x78, 0x52, 0x61, 0x64, 0x69,
	0x75, 0x73, 0x49, 0x6e, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x48,
	0x00, 0x52, 0x10, 0x6d, 0x61, 0x78, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x49, 0x6e, 0x4d, 0x69,
	0x6c, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6d, 0x61, 0x78, 0x52, 0x61,
	0x64, 0x69, 0x75, 0x73, 0x49, 0x6e, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x22, 0xc7, 0x01, 0x0a, 0x0b,
	0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x14, 0x68,
	0x61, 0x73, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x53, 0x74, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x68, 0x61, 0x73, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x53, 0x74, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x34, 0x0a, 0x15, 0x68, 0x61, 0x73, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15,
	0x68, 0x61, 0x73, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x46, 0x69,
	0x6c, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x68, 0x61, 0x73, 0x46, 0x4d, 0x43, 0x53,
	0x41, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x68, 0x61, 0x73, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x24, 0x0a, 0x0d, 0x68, 0x61, 0x73, 0x44, 0x4f, 0x54, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61, 0x73, 0x44, 0x4f, 0x54, 0x46, 0x69,
	0x6c, 0x69, 0x6e, 0x67, 0x73, 0x22, 0xc8, 0x04, 0x0a, 0x15, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x77,
	0x72, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x12,
	0x42, 0x0a, 0x1c, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x57, 0x69, 0x74, 0x68,
	0x4c, 0x6f, 0x73, 0x73, 0x46, 0x72, 0x65, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1c, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73,
	0x57, 0x69, 0x74, 0x68, 0x4c, 0x6f, 0x73, 0x73, 0x46, 0x72, 0x65, 0x65, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x73, 0x12, 0x3f, 0x0a, 0x18, 0x6c, 0x6f, 0x73, 0x73, 0x46, 0x72, 0x65, 0x65, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x18, 0x6c, 0x6f, 0x73, 0x73, 0x46, 0x72, 0x65,
	0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x0c, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x48, 0x01, 0x52,
	0x0c, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x49, 0x0a, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x48, 0x02, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x73, 0x88, 0x01, 0x01, 0x12, 0x4d, 0x0a, 0x0d, 0x65,
	0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x48, 0x03, 0x52, 0x0d, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x5c, 0x0a, 0x12, 0x71, 0x75,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x47, 0x72, 0x61, 0x64, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x51, 0x75, 0x61,
	0x6c, 0x69, 0x74, 0x79, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x47, 0x72, 0x61, 0x64, 0x65, 0x48,
	0x04, 0x52, 0x12, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x47, 0x72, 0x61, 0x64, 0x65, 0x88, 0x01, 0x01, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x6c, 0x6f, 0x73,
	0x73, 0x46, 0x72, 0x65, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x73, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x78, 0x70, 0x65,
	0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x71, 0x75,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x47, 0x72, 0x61, 0x64, 0x65,
	0x22, 0x36, 0x0a, 0x0c, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x12, 0x1b, 0x0a, 0x06, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02,
	0x48, 0x00, 0x52, 0x06, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a,
	0x07, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x22, 0x86, 0x01, 0x0a, 0x0b, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x12, 0x3e, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x2e, 0x4d, 0x6f, 0x64, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x8a, 0x01, 0x0a, 0x0d, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65,
	0x4d, 0x6f, 0x64, 0x12, 0x40, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x4d, 0x6f, 0x64, 0x2e, 0x4d, 0x6f, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x04, 0x6d, 0x6f, 0x64, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0xc1,
	0x01, 0x0a, 0x12, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x20, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x56,
	0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x5f, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x56,
	0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a,
	0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43,
	0x4c, 0x41, 0x53, 0x53, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20,
	0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43,
	0x4c, 0x41, 0x53, 0x53, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59,
	0x10, 0x04, 0x2a, 0xff, 0x17, 0x0a, 0x14, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x56,
	0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2e, 0x0a, 0x2a, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x42, 0x4f, 0x4f, 0x4d, 0x5f, 0x54, 0x52,
	0x55, 0x43, 0x4b, 0x53, 0x5f, 0x30, 0x5f, 0x54, 0x4f, 0x5f, 0x34, 0x39, 0x5f, 0x46, 0x45, 0x45,
	0x54, 0x10, 0x01, 0x12, 0x2f, 0x0a, 0x2b, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x42, 0x4f, 0x4f, 0x4d, 0x5f, 0x54, 0x52,
	0x55, 0x43, 0x4b, 0x53, 0x5f, 0x35, 0x30, 0x5f, 0x54, 0x4f, 0x5f, 0x37, 0x35, 0x5f, 0x46, 0x45,
	0x45, 0x54, 0x10, 0x02, 0x12, 0x30, 0x0a, 0x2c, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x42, 0x4f, 0x4f, 0x4d, 0x5f, 0x54,
	0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x37, 0x36, 0x5f, 0x54, 0x4f, 0x5f, 0x31, 0x30, 0x30, 0x5f,
	0x46, 0x45, 0x45, 0x54, 0x10, 0x03, 0x12, 0x31, 0x0a, 0x2d, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c,
	0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x42, 0x4f, 0x4f, 0x4d,
	0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x31, 0x30, 0x31, 0x5f, 0x54, 0x4f, 0x5f, 0x31,
	0x32, 0x30, 0x5f, 0x46, 0x45, 0x45, 0x54, 0x10, 0x04, 0x12, 0x2f, 0x0a, 0x2b, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x42,
	0x4f, 0x4f, 0x4d, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x31, 0x32, 0x31, 0x5f, 0x50,
	0x4c, 0x55, 0x53, 0x5f, 0x46, 0x45, 0x45, 0x54, 0x10, 0x05, 0x12, 0x41, 0x0a, 0x3d, 0x56, 0x45,
	0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f,
	0x43, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x45, 0x4c, 0x45, 0x43, 0x4f, 0x4d, 0x5f, 0x41, 0x4e,
	0x44, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x06, 0x12, 0x64, 0x0a,
	0x60, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c,
	0x54, 0x59, 0x5f, 0x45, 0x58, 0x43, 0x41, 0x56, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x52,
	0x49, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x5f,
	0x47, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x41, 0x43, 0x54,
	0x4f, 0x52, 0x53, 0x5f, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b,
	0x53, 0x10, 0x07, 0x12, 0x56, 0x0a, 0x52, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x45, 0x58, 0x43, 0x41, 0x56, 0x41, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x44, 0x52, 0x49, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x5f, 0x47, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f,
	0x4e, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x53, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x53, 0x10, 0x08, 0x12, 0x38, 0x0a, 0x34, 0x56,
	0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59,
	0x5f, 0x50, 0x45, 0x54, 0x52, 0x4f, 0x4c, 0x45, 0x55, 0x4d, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x52,
	0x49, 0x42, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x41, 0x43, 0x54,
	0x4f, 0x52, 0x53, 0x10, 0x09, 0x12, 0x47, 0x0a, 0x43, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x48, 0x4f, 0x54, 0x5f, 0x4f,
	0x49, 0x4c, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x5f, 0x41, 0x53, 0x50, 0x48, 0x41, 0x4c,
	0x54, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4f, 0x52, 0x5f, 0x45, 0x4e, 0x45, 0x52, 0x47, 0x59, 0x10, 0x0a, 0x12, 0x3e,
	0x0a, 0x3a, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41,
	0x4c, 0x54, 0x59, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4f, 0x49,
	0x4c, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4f, 0x52, 0x5f, 0x45, 0x4e, 0x45, 0x52, 0x47,
	0x59, 0x5f, 0x42, 0x4f, 0x44, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x53, 0x10, 0x0b, 0x12, 0x49,
	0x0a, 0x45, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41,
	0x4c, 0x54, 0x59, 0x5f, 0x53, 0x45, 0x50, 0x54, 0x49, 0x43, 0x5f, 0x54, 0x41, 0x4e, 0x4b, 0x5f,
	0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x49,
	0x4e, 0x44, 0x49, 0x56, 0x49, 0x44, 0x55, 0x41, 0x4c, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x44, 0x5f,
	0x49, 0x4e, 0x53, 0x55, 0x52, 0x45, 0x44, 0x10, 0x0c, 0x12, 0x48, 0x0a, 0x44, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x53,
	0x45, 0x50, 0x54, 0x49, 0x43, 0x5f, 0x54, 0x41, 0x4e, 0x4b, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x45,
	0x44, 0x10, 0x0d, 0x12, 0x3a, 0x0a, 0x36, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x57, 0x45, 0x4c, 0x44, 0x45, 0x52, 0x53,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x57, 0x4f, 0x52, 0x4b, 0x49, 0x4e,
	0x47, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x0e, 0x12,
	0x35, 0x0a, 0x31, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x41, 0x4c, 0x54, 0x59, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x42, 0x4f, 0x44, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x53, 0x10, 0x0f, 0x12, 0x49, 0x0a, 0x45, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c,
	0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x48, 0x4f, 0x54, 0x5f,
	0x4f, 0x49, 0x4c, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x5f, 0x41, 0x53, 0x50, 0x48, 0x41,
	0x4c, 0x54, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x53, 0x10,
	0x10, 0x12, 0x33, 0x0a, 0x2f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x49, 0x5a,
	0x45, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x53, 0x10, 0x11, 0x12, 0x2e, 0x0a, 0x2a, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c,
	0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x55, 0x52,
	0x49, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x53, 0x10, 0x12, 0x12, 0x2a, 0x0a, 0x26, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c,
	0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x46, 0x4f, 0x4f, 0x44,
	0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53,
	0x10, 0x13, 0x12, 0x2b, 0x0a, 0x27, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x57, 0x41, 0x53, 0x54, 0x45, 0x5f, 0x44, 0x49,
	0x53, 0x50, 0x4f, 0x53, 0x41, 0x4c, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x10, 0x14, 0x12,
	0x3d, 0x0a, 0x39, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x41, 0x4c, 0x54, 0x59, 0x5f, 0x57, 0x41, 0x53, 0x54, 0x45, 0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x41,
	0x4e, 0x44, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x5f, 0x57, 0x41, 0x53, 0x54, 0x45, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x52, 0x53, 0x10, 0x15, 0x12, 0x39,
	0x0a, 0x35, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41,
	0x4c, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x52, 0x56, 0x45, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x47, 0x4f,
	0x41, 0x54, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x46, 0x41, 0x52, 0x4d, 0x45, 0x52,
	0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x10, 0x16, 0x12, 0x2d, 0x0a, 0x29, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x41,
	0x4c, 0x4c, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x52, 0x4d, 0x45, 0x52, 0x5f,
	0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x10, 0x17, 0x12, 0x22, 0x0a, 0x1e, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x48, 0x4f,
	0x55, 0x53, 0x45, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x52, 0x53, 0x10, 0x18, 0x12, 0x27, 0x0a, 0x23,
	0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54,
	0x59, 0x5f, 0x4d, 0x4f, 0x56, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x53, 0x10, 0x19, 0x12, 0x2e, 0x0a, 0x2a, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x4c, 0x41, 0x57, 0x4e, 0x5f,
	0x54, 0x52, 0x45, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x52, 0x55,
	0x43, 0x4b, 0x53, 0x10, 0x1a, 0x12, 0x26, 0x0a, 0x22, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x52,
	0x45, 0x52, 0x5f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x53, 0x10, 0x1b, 0x12, 0x40, 0x0a,
	0x3c, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c,
	0x54, 0x59, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x5f, 0x49, 0x4e, 0x5f, 0x56, 0x45,
	0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x1c, 0x12,
	0x40, 0x0a, 0x3c, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x41, 0x4c, 0x54, 0x59, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x43,
	0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x5f, 0x4f, 0x54, 0x48,
	0x45, 0x52, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x1d, 0x12, 0x3b, 0x0a, 0x37, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x5f, 0x4e,
	0x4f, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x5f, 0x53, 0x41, 0x4c, 0x45, 0x53, 0x10, 0x1e, 0x12, 0x33,
	0x0a, 0x2f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41,
	0x4c, 0x54, 0x59, 0x5f, 0x57, 0x48, 0x4f, 0x4c, 0x45, 0x53, 0x41, 0x4c, 0x45, 0x52, 0x53, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x46, 0x41, 0x43, 0x54, 0x55, 0x52, 0x45, 0x52,
	0x53, 0x10, 0x1f, 0x12, 0x58, 0x0a, 0x54, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x47, 0x41, 0x53, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x43, 0x52, 0x55, 0x44, 0x45, 0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x52, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x48, 0x49, 0x52, 0x45, 0x5f,
	0x4c, 0x50, 0x47, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x41, 0x4e, 0x45, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x52, 0x53, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4c, 0x45, 0x44, 0x10, 0x20, 0x12, 0x55, 0x0a,
	0x51, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c,
	0x54, 0x59, 0x5f, 0x47, 0x41, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x52, 0x55, 0x44, 0x45,
	0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x52, 0x53, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x48, 0x49, 0x52, 0x45, 0x5f, 0x4c, 0x50, 0x47, 0x5f, 0x50, 0x52,
	0x4f, 0x50, 0x41, 0x4e, 0x45, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x52, 0x53, 0x5f, 0x42, 0x55,
	0x4c, 0x4b, 0x10, 0x21, 0x12, 0x4e, 0x0a, 0x4a, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x47, 0x41, 0x53, 0x5f, 0x41, 0x4e,
	0x44, 0x5f, 0x43, 0x52, 0x55, 0x44, 0x45, 0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x52, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x48, 0x49, 0x52, 0x45,
	0x5f, 0x43, 0x52, 0x55, 0x44, 0x45, 0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x52, 0x53, 0x10, 0x22, 0x12, 0x4d, 0x0a, 0x49, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x47, 0x41, 0x53, 0x5f, 0x41, 0x4e,
	0x44, 0x5f, 0x43, 0x52, 0x55, 0x44, 0x45, 0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x52, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x48, 0x49, 0x52, 0x45,
	0x5f, 0x46, 0x55, 0x45, 0x4c, 0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x52,
	0x53, 0x10, 0x23, 0x12, 0x52, 0x0a, 0x4e, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x47, 0x41, 0x53, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x43, 0x52, 0x55, 0x44, 0x45, 0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x52, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x48, 0x49, 0x52, 0x45, 0x5f,
	0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x47, 0x41, 0x53, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x52, 0x53, 0x10, 0x24, 0x12, 0x25, 0x0a, 0x21, 0x56, 0x45, 0x48, 0x49, 0x43,
	0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x53, 0x41, 0x4c,
	0x56, 0x41, 0x47, 0x45, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x52, 0x53, 0x10, 0x25, 0x12, 0x2f,
	0x0a, 0x2b, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41,
	0x4c, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x5f, 0x43, 0x41, 0x52, 0x52, 0x49, 0x45, 0x52, 0x53,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x48, 0x49, 0x52, 0x45, 0x10, 0x26, 0x12,
	0x2a, 0x0a, 0x26, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x41, 0x4c, 0x54, 0x59, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x55, 0x53, 0x45,
	0x5f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x53, 0x10, 0x27, 0x12, 0x33, 0x0a, 0x2f, 0x56,
	0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59,
	0x5f, 0x46, 0x49, 0x52, 0x45, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x52,
	0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x48, 0x49, 0x52, 0x45, 0x10, 0x28,
	0x12, 0x27, 0x0a, 0x23, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x48, 0x41, 0x52,
	0x56, 0x45, 0x53, 0x54, 0x45, 0x52, 0x53, 0x10, 0x29, 0x12, 0x35, 0x0a, 0x31, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x44,
	0x52, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x54,
	0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x2a,
	0x12, 0x25, 0x0a, 0x21, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x45, 0x54, 0x5f, 0x53, 0x57, 0x45,
	0x45, 0x50, 0x45, 0x52, 0x53, 0x10, 0x2b, 0x12, 0x2f, 0x0a, 0x2b, 0x56, 0x45, 0x48, 0x49, 0x43,
	0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x4e,
	0x54, 0x41, 0x4c, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52,
	0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x10, 0x2c, 0x12, 0x35, 0x0a, 0x31, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x57, 0x49, 0x53, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x10, 0x2d, 0x12,
	0x29, 0x0a, 0x25, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x41, 0x4c, 0x54, 0x59, 0x5f, 0x41, 0x52, 0x54, 0x49, 0x53, 0x41, 0x4e, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x2e, 0x12, 0x2b, 0x0a, 0x27, 0x56, 0x45,
	0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f,
	0x43, 0x41, 0x52, 0x50, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x41,
	0x43, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x2f, 0x12, 0x49, 0x0a, 0x45, 0x56, 0x45, 0x48, 0x49, 0x43,
	0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x45, 0x58, 0x54,
	0x45, 0x52, 0x49, 0x4f, 0x52, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x57,
	0x4f, 0x52, 0x4b, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x53,
	0x10, 0x30, 0x12, 0x33, 0x0a, 0x2f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x52, 0x4f, 0x41, 0x44, 0x5f, 0x43, 0x4f, 0x4e,
	0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x41,
	0x43, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x31, 0x12, 0x31, 0x0a, 0x2d, 0x56, 0x45, 0x48, 0x49, 0x43,
	0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f, 0x54, 0x52, 0x41,
	0x46, 0x46, 0x49, 0x43, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x32, 0x12, 0x31, 0x0a, 0x2d, 0x56, 0x45,
	0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c, 0x54, 0x59, 0x5f,
	0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4d, 0x45, 0x43, 0x48, 0x41, 0x4e, 0x49, 0x43, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x33, 0x12, 0x25, 0x0a,
	0x21, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41, 0x4c,
	0x54, 0x59, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x49,
	0x54, 0x53, 0x10, 0x34, 0x2a, 0xb6, 0x01, 0x0a, 0x0a, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x55, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x55,
	0x53, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x21, 0x0a, 0x1d, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x5f,
	0x54, 0x4f, 0x57, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x53, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x55,
	0x53, 0x45, 0x5f, 0x44, 0x55, 0x4d, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x56, 0x45, 0x48, 0x49, 0x43,
	0x4c, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x4c, 0x4f, 0x47, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x56,
	0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52,
	0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x04, 0x2a, 0x7b, 0x0a,
	0x0b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x18,
	0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x42, 0x55,
	0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x55, 0x53, 0x45, 0x5f, 0x52, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x12, 0x1b, 0x0a,
	0x17, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x43, 0x4f,
	0x4d, 0x4d, 0x45, 0x52, 0x43, 0x49, 0x41, 0x4c, 0x10, 0x03, 0x2a, 0x61, 0x0a, 0x0a, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55,
	0x53, 0x41, 0x47, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x41, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10,
	0x01, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x41, 0x47, 0x45,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x02, 0x2a, 0xe2, 0x01,
	0x0a, 0x12, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x47,
	0x72, 0x61, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f,
	0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x51, 0x55,
	0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x41,
	0x44, 0x45, 0x5f, 0x41, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x42,
	0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x41,
	0x54, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x43, 0x10, 0x03, 0x12, 0x1a,
	0x0a, 0x16, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x44, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x51, 0x55,
	0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x41,
	0x44, 0x45, 0x5f, 0x45, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x46,
	0x10, 0x06, 0x2a, 0x8f, 0x02, 0x0a, 0x0b, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x16, 0x0a, 0x12, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52,
	0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19,
	0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4d,
	0x49, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x56,
	0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x49,
	0x4c, 0x45, 0x52, 0x10, 0x05, 0x12, 0x22, 0x0a, 0x1e, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x41,
	0x53, 0x53, 0x45, 0x4e, 0x47, 0x45, 0x52, 0x10, 0x06, 0x12, 0x29, 0x0a, 0x25, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x5f, 0x48, 0x41, 0x52, 0x56, 0x45, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c,
	0x45, 0x52, 0x10, 0x07, 0x2a, 0xa7, 0x03, 0x0a, 0x0b, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f,
	0x53, 0x49, 0x5a, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x49,
	0x5a, 0x45, 0x5f, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x10, 0x01,
	0x12, 0x1d, 0x0a, 0x19, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x5a, 0x45,
	0x5f, 0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x10, 0x02, 0x12,
	0x1c, 0x0a, 0x18, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f,
	0x48, 0x45, 0x41, 0x56, 0x59, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x10, 0x03, 0x12, 0x22, 0x0a,
	0x1e, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f, 0x45, 0x58,
	0x54, 0x52, 0x41, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x10,
	0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x5a,
	0x45, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10,
	0x05, 0x12, 0x24, 0x0a, 0x20, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x5a,
	0x45, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x5f, 0x54, 0x52,
	0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x45, 0x48, 0x49, 0x43,
	0x4c, 0x45, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f, 0x53, 0x45, 0x4d, 0x49, 0x5f, 0x54, 0x52, 0x41,
	0x49, 0x4c, 0x45, 0x52, 0x10, 0x07, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c,
	0x45, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x49,
	0x4c, 0x45, 0x52, 0x10, 0x08, 0x12, 0x28, 0x0a, 0x24, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x55, 0x54,
	0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x10, 0x09, 0x12,
	0x29, 0x0a, 0x25, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x48, 0x41, 0x52, 0x56, 0x45, 0x53, 0x54, 0x45, 0x52,
	0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x10, 0x0a, 0x12, 0x22, 0x0a, 0x1e, 0x56, 0x45,
	0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41,
	0x54, 0x45, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x4e, 0x47, 0x45, 0x52, 0x10, 0x0b, 0x2a, 0xc2,
	0x01, 0x0a, 0x11, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x23, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x41, 0x44, 0x49,
	0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a,
	0x26, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x5f, 0x54, 0x4f, 0x5f, 0x31, 0x30,
	0x30, 0x5f, 0x4d, 0x49, 0x4c, 0x45, 0x53, 0x10, 0x01, 0x12, 0x2c, 0x0a, 0x28, 0x4d, 0x41, 0x58,
	0x5f, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x31, 0x30, 0x31, 0x5f, 0x54, 0x4f, 0x5f, 0x33, 0x30, 0x30, 0x5f,
	0x4d, 0x49, 0x4c, 0x45, 0x53, 0x10, 0x02, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x41, 0x58, 0x5f, 0x52,
	0x41, 0x44, 0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x33, 0x30, 0x31, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x5f, 0x4d, 0x49, 0x4c, 0x45,
	0x53, 0x10, 0x03, 0x2a, 0xdf, 0x08, 0x0a, 0x0c, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f,
	0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x10, 0x01, 0x12, 0x2f, 0x0a, 0x2b, 0x49, 0x4e, 0x44, 0x55,
	0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41,
	0x4c, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x56,
	0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x53, 0x10, 0x02, 0x12, 0x2a, 0x0a, 0x26, 0x49, 0x4e, 0x44,
	0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x55, 0x52, 0x49,
	0x45, 0x52, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x56, 0x45, 0x48, 0x49, 0x43,
	0x4c, 0x45, 0x53, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52,
	0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49,
	0x56, 0x45, 0x52, 0x59, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x10, 0x04, 0x12, 0x27, 0x0a,
	0x23, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57,
	0x41, 0x53, 0x54, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4f, 0x53, 0x41, 0x4c, 0x5f, 0x54, 0x52,
	0x55, 0x43, 0x4b, 0x53, 0x10, 0x05, 0x12, 0x41, 0x0a, 0x3d, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54,
	0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x41, 0x53, 0x54, 0x45, 0x5f, 0x4f, 0x49,
	0x4c, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x51, 0x55,
	0x49, 0x44, 0x5f, 0x57, 0x41, 0x53, 0x54, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f,
	0x52, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e, 0x44,
	0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x41, 0x52, 0x4d, 0x45,
	0x52, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e,
	0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x45, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x4d, 0x49, 0x58, 0x45, 0x52, 0x53, 0x10, 0x08, 0x12, 0x1e, 0x0a, 0x1a, 0x49,
	0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x4f, 0x55,
	0x53, 0x45, 0x5f, 0x4d, 0x4f, 0x56, 0x49, 0x4e, 0x47, 0x10, 0x09, 0x12, 0x23, 0x0a, 0x1f, 0x49,
	0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f, 0x56,
	0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x0a,
	0x12, 0x2a, 0x0a, 0x26, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4c, 0x41, 0x57, 0x4e, 0x5f, 0x54, 0x52, 0x45, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x10, 0x0b, 0x12, 0x22, 0x0a, 0x1e,
	0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x52, 0x45, 0x52, 0x5f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x53, 0x10, 0x0c,
	0x12, 0x2a, 0x0a, 0x26, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x43, 0x45, 0x53, 0x53,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x10, 0x0d, 0x12, 0x2b, 0x0a, 0x27,
	0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x48,
	0x4f, 0x4c, 0x45, 0x53, 0x41, 0x4c, 0x45, 0x52, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x46, 0x41,
	0x43, 0x54, 0x55, 0x52, 0x45, 0x52, 0x53, 0x10, 0x0e, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x4e, 0x44,
	0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x41, 0x4c, 0x56, 0x41,
	0x47, 0x45, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x52, 0x53, 0x10, 0x0f, 0x12, 0x38, 0x0a, 0x34,
	0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x41,
	0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x52, 0x55, 0x44, 0x45, 0x5f, 0x4f, 0x49, 0x4c, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x52, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x48, 0x49, 0x52, 0x45, 0x10, 0x10, 0x12, 0x2b, 0x0a, 0x27, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54,
	0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x5f, 0x43, 0x41, 0x52, 0x52,
	0x49, 0x45, 0x52, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x48, 0x49, 0x52,
	0x45, 0x10, 0x11, 0x12, 0x30, 0x0a, 0x2c, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x57, 0x49,
	0x53, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x54, 0x52,
	0x55, 0x43, 0x4b, 0x10, 0x12, 0x12, 0x29, 0x0a, 0x25, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52,
	0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x47, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x54,
	0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x48, 0x49, 0x52, 0x45, 0x10, 0x13,
	0x12, 0x2d, 0x0a, 0x29, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4c, 0x4f, 0x47, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x48, 0x49, 0x52, 0x45, 0x10, 0x14, 0x12,
	0x2b, 0x0a, 0x27, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x54, 0x4f, 0x57, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x49, 0x4e, 0x43, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x41, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x15, 0x12, 0x26, 0x0a, 0x22,
	0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x4f,
	0x57, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x53, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x10, 0x16, 0x12, 0x2d, 0x0a, 0x29, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x55, 0x4d, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x48, 0x49, 0x52,
	0x45, 0x10, 0x17, 0x12, 0x31, 0x0a, 0x2d, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x55, 0x4d, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x50, 0x45,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x48, 0x49, 0x52, 0x45, 0x10, 0x18, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54,
	0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x55, 0x53, 0x45, 0x10, 0x19, 0x2a, 0xbe, 0x01, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4d, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52,
	0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x10,
	0x02, 0x12, 0x28, 0x0a, 0x24, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x54,
	0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x4d, 0x5f, 0x48, 0x41, 0x52, 0x56, 0x45, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x52, 0x41,
	0x49, 0x4c, 0x45, 0x52, 0x10, 0x04, 0x42, 0x33, 0x5a, 0x31, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e,
	0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e,
	0x61, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2d, 0x61, 0x75, 0x74, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_business_auto_model_program_data_proto_rawDescOnce sync.Once
	file_business_auto_model_program_data_proto_rawDescData = file_business_auto_model_program_data_proto_rawDesc
)

func file_business_auto_model_program_data_proto_rawDescGZIP() []byte {
	file_business_auto_model_program_data_proto_rawDescOnce.Do(func() {
		file_business_auto_model_program_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_business_auto_model_program_data_proto_rawDescData)
	})
	return file_business_auto_model_program_data_proto_rawDescData
}

var file_business_auto_model_program_data_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_business_auto_model_program_data_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_business_auto_model_program_data_proto_goTypes = []interface{}{
	(VehicleWeightClass)(0),       // 0: business_auto_model.VehicleWeightClass
	(VehicleSpecialtyType)(0),     // 1: business_auto_model.VehicleSpecialtyType
	(VehicleUse)(0),               // 2: business_auto_model.VehicleUse
	(BusinessUse)(0),              // 3: business_auto_model.BusinessUse
	(StateUsage)(0),               // 4: business_auto_model.StateUsage
	(QualityRatingGrade)(0),       // 5: business_auto_model.QualityRatingGrade
	(VehicleType)(0),              // 6: business_auto_model.VehicleType
	(VehicleSize)(0),              // 7: business_auto_model.VehicleSize
	(RadiusOfOperation)(0),        // 8: business_auto_model.RadiusOfOperation
	(IndustryType)(0),             // 9: business_auto_model.IndustryType
	(TrailerType)(0),              // 10: business_auto_model.TrailerType
	(*ProgramData)(nil),           // 11: business_auto_model.ProgramData
	(*CompanyInfo)(nil),           // 12: business_auto_model.CompanyInfo
	(*VehicleData)(nil),           // 13: business_auto_model.VehicleData
	(*RadiusBucket)(nil),          // 14: business_auto_model.RadiusBucket
	(*FilingsInfo)(nil),           // 15: business_auto_model.FilingsInfo
	(*UnderwritingOverrides)(nil), // 16: business_auto_model.UnderwritingOverrides
	(*DriverFactor)(nil),          // 17: business_auto_model.DriverFactor
	(*ScheduleMod)(nil),           // 18: business_auto_model.ScheduleMod
	(*ExperienceMod)(nil),         // 19: business_auto_model.ExperienceMod
	nil,                           // 20: business_auto_model.ScheduleMod.ModsEntry
	nil,                           // 21: business_auto_model.ExperienceMod.ModsEntry
	(proto.VehicleType)(0),        // 22: insurance_core.VehicleType
}
var file_business_auto_model_program_data_proto_depIdxs = []int32{
	12, // 0: business_auto_model.ProgramData.companyInfo:type_name -> business_auto_model.CompanyInfo
	13, // 1: business_auto_model.ProgramData.vehicles:type_name -> business_auto_model.VehicleData
	15, // 2: business_auto_model.ProgramData.filingsInfo:type_name -> business_auto_model.FilingsInfo
	16, // 3: business_auto_model.ProgramData.underwritingOverrides:type_name -> business_auto_model.UnderwritingOverrides
	9,  // 4: business_auto_model.CompanyInfo.primaryIndustryClassification:type_name -> business_auto_model.IndustryType
	9,  // 5: business_auto_model.CompanyInfo.secondaryIndustryClassifications:type_name -> business_auto_model.IndustryType
	22, // 6: business_auto_model.VehicleData.vehicleType:type_name -> insurance_core.VehicleType
	0,  // 7: business_auto_model.VehicleData.weightClass:type_name -> business_auto_model.VehicleWeightClass
	1,  // 8: business_auto_model.VehicleData.specialtyVehicleType:type_name -> business_auto_model.VehicleSpecialtyType
	2,  // 9: business_auto_model.VehicleData.vehicleUse:type_name -> business_auto_model.VehicleUse
	3,  // 10: business_auto_model.VehicleData.businessUse:type_name -> business_auto_model.BusinessUse
	10, // 11: business_auto_model.VehicleData.trailerType:type_name -> business_auto_model.TrailerType
	4,  // 12: business_auto_model.VehicleData.stateUsage:type_name -> business_auto_model.StateUsage
	8,  // 13: business_auto_model.VehicleData.radiusClassification:type_name -> business_auto_model.RadiusOfOperation
	17, // 14: business_auto_model.UnderwritingOverrides.driverFactor:type_name -> business_auto_model.DriverFactor
	18, // 15: business_auto_model.UnderwritingOverrides.scheduleMods:type_name -> business_auto_model.ScheduleMod
	19, // 16: business_auto_model.UnderwritingOverrides.experienceMod:type_name -> business_auto_model.ExperienceMod
	5,  // 17: business_auto_model.UnderwritingOverrides.qualityRatingGrade:type_name -> business_auto_model.QualityRatingGrade
	20, // 18: business_auto_model.ScheduleMod.mods:type_name -> business_auto_model.ScheduleMod.ModsEntry
	21, // 19: business_auto_model.ExperienceMod.mods:type_name -> business_auto_model.ExperienceMod.ModsEntry
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_business_auto_model_program_data_proto_init() }
func file_business_auto_model_program_data_proto_init() {
	if File_business_auto_model_program_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_business_auto_model_program_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgramData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_business_auto_model_program_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_business_auto_model_program_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_business_auto_model_program_data_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RadiusBucket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_business_auto_model_program_data_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilingsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_business_auto_model_program_data_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnderwritingOverrides); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_business_auto_model_program_data_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DriverFactor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_business_auto_model_program_data_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScheduleMod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_business_auto_model_program_data_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExperienceMod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_business_auto_model_program_data_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_business_auto_model_program_data_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_business_auto_model_program_data_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_business_auto_model_program_data_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_business_auto_model_program_data_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_business_auto_model_program_data_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_business_auto_model_program_data_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_business_auto_model_program_data_proto_goTypes,
		DependencyIndexes: file_business_auto_model_program_data_proto_depIdxs,
		EnumInfos:         file_business_auto_model_program_data_proto_enumTypes,
		MessageInfos:      file_business_auto_model_program_data_proto_msgTypes,
	}.Build()
	File_business_auto_model_program_data_proto = out.File
	file_business_auto_model_program_data_proto_rawDesc = nil
	file_business_auto_model_program_data_proto_goTypes = nil
	file_business_auto_model_program_data_proto_depIdxs = nil
}
