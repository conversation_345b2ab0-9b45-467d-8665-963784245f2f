// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: nonfleet/model/endorsement/change_data.proto

package endorsement

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	proto "nirvanatech.com/nirvana/common-go/proto"
	proto1 "nirvanatech.com/nirvana/insurance-core/proto"
	model "nirvanatech.com/nirvana/nonfleet/model"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NonFleetChangeType int32

const (
	NonFleetChangeType_NonFleetChangeType_Invalid           NonFleetChangeType = 0
	NonFleetChangeType_NonFleetChangeType_Driver            NonFleetChangeType = 1
	NonFleetChangeType_NonFleetChangeType_Vehicle           NonFleetChangeType = 2
	NonFleetChangeType_NonFleetChangeType_MailingAddress    NonFleetChangeType = 3
	NonFleetChangeType_NonFleetChangeType_TerminalAddress   NonFleetChangeType = 4
	NonFleetChangeType_NonFleetChangeType_LossPayee         NonFleetChangeType = 5
	NonFleetChangeType_NonFleetChangeType_Commodities       NonFleetChangeType = 6
	NonFleetChangeType_NonFleetChangeType_RadiusOfOperation NonFleetChangeType = 7
	NonFleetChangeType_NonFleetChangeType_CompanyName       NonFleetChangeType = 8
	NonFleetChangeType_NonFleetChangeType_BusinessOwner     NonFleetChangeType = 9
)

// Enum value maps for NonFleetChangeType.
var (
	NonFleetChangeType_name = map[int32]string{
		0: "NonFleetChangeType_Invalid",
		1: "NonFleetChangeType_Driver",
		2: "NonFleetChangeType_Vehicle",
		3: "NonFleetChangeType_MailingAddress",
		4: "NonFleetChangeType_TerminalAddress",
		5: "NonFleetChangeType_LossPayee",
		6: "NonFleetChangeType_Commodities",
		7: "NonFleetChangeType_RadiusOfOperation",
		8: "NonFleetChangeType_CompanyName",
		9: "NonFleetChangeType_BusinessOwner",
	}
	NonFleetChangeType_value = map[string]int32{
		"NonFleetChangeType_Invalid":           0,
		"NonFleetChangeType_Driver":            1,
		"NonFleetChangeType_Vehicle":           2,
		"NonFleetChangeType_MailingAddress":    3,
		"NonFleetChangeType_TerminalAddress":   4,
		"NonFleetChangeType_LossPayee":         5,
		"NonFleetChangeType_Commodities":       6,
		"NonFleetChangeType_RadiusOfOperation": 7,
		"NonFleetChangeType_CompanyName":       8,
		"NonFleetChangeType_BusinessOwner":     9,
	}
)

func (x NonFleetChangeType) Enum() *NonFleetChangeType {
	p := new(NonFleetChangeType)
	*p = x
	return p
}

func (x NonFleetChangeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NonFleetChangeType) Descriptor() protoreflect.EnumDescriptor {
	return file_nonfleet_model_endorsement_change_data_proto_enumTypes[0].Descriptor()
}

func (NonFleetChangeType) Type() protoreflect.EnumType {
	return &file_nonfleet_model_endorsement_change_data_proto_enumTypes[0]
}

func (x NonFleetChangeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NonFleetChangeType.Descriptor instead.
func (NonFleetChangeType) EnumDescriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{0}
}

type NonFleetChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChangeType NonFleetChangeType `protobuf:"varint,1,opt,name=changeType,proto3,enum=nonfleet_endorsement.NonFleetChangeType" json:"changeType,omitempty"`
	// Types that are assignable to Data:
	//
	//	*NonFleetChange_DriverChange
	//	*NonFleetChange_VehicleChange
	//	*NonFleetChange_MailingAddressChange
	//	*NonFleetChange_TerminalAddressChange
	//	*NonFleetChange_LossPayeeChange
	//	*NonFleetChange_CommoditiesChange
	//	*NonFleetChange_RadiusOfOperationChange
	//	*NonFleetChange_CompanyNameChange
	//	*NonFleetChange_BusinessOwnerChange
	Data isNonFleetChange_Data `protobuf_oneof:"data"`
}

func (x *NonFleetChange) Reset() {
	*x = NonFleetChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleetChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleetChange) ProtoMessage() {}

func (x *NonFleetChange) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleetChange.ProtoReflect.Descriptor instead.
func (*NonFleetChange) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{0}
}

func (x *NonFleetChange) GetChangeType() NonFleetChangeType {
	if x != nil {
		return x.ChangeType
	}
	return NonFleetChangeType_NonFleetChangeType_Invalid
}

func (m *NonFleetChange) GetData() isNonFleetChange_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *NonFleetChange) GetDriverChange() *DriverChange {
	if x, ok := x.GetData().(*NonFleetChange_DriverChange); ok {
		return x.DriverChange
	}
	return nil
}

func (x *NonFleetChange) GetVehicleChange() *VehicleChange {
	if x, ok := x.GetData().(*NonFleetChange_VehicleChange); ok {
		return x.VehicleChange
	}
	return nil
}

func (x *NonFleetChange) GetMailingAddressChange() *MailingAddressChange {
	if x, ok := x.GetData().(*NonFleetChange_MailingAddressChange); ok {
		return x.MailingAddressChange
	}
	return nil
}

func (x *NonFleetChange) GetTerminalAddressChange() *TerminalAddressChange {
	if x, ok := x.GetData().(*NonFleetChange_TerminalAddressChange); ok {
		return x.TerminalAddressChange
	}
	return nil
}

func (x *NonFleetChange) GetLossPayeeChange() *LossPayeeChange {
	if x, ok := x.GetData().(*NonFleetChange_LossPayeeChange); ok {
		return x.LossPayeeChange
	}
	return nil
}

func (x *NonFleetChange) GetCommoditiesChange() *CommoditiesChange {
	if x, ok := x.GetData().(*NonFleetChange_CommoditiesChange); ok {
		return x.CommoditiesChange
	}
	return nil
}

func (x *NonFleetChange) GetRadiusOfOperationChange() *RadiusOfOperationChange {
	if x, ok := x.GetData().(*NonFleetChange_RadiusOfOperationChange); ok {
		return x.RadiusOfOperationChange
	}
	return nil
}

func (x *NonFleetChange) GetCompanyNameChange() *CompanyNameChange {
	if x, ok := x.GetData().(*NonFleetChange_CompanyNameChange); ok {
		return x.CompanyNameChange
	}
	return nil
}

func (x *NonFleetChange) GetBusinessOwnerChange() *BusinessOwnerChange {
	if x, ok := x.GetData().(*NonFleetChange_BusinessOwnerChange); ok {
		return x.BusinessOwnerChange
	}
	return nil
}

type isNonFleetChange_Data interface {
	isNonFleetChange_Data()
}

type NonFleetChange_DriverChange struct {
	DriverChange *DriverChange `protobuf:"bytes,2,opt,name=driverChange,proto3,oneof"`
}

type NonFleetChange_VehicleChange struct {
	VehicleChange *VehicleChange `protobuf:"bytes,3,opt,name=vehicleChange,proto3,oneof"`
}

type NonFleetChange_MailingAddressChange struct {
	MailingAddressChange *MailingAddressChange `protobuf:"bytes,4,opt,name=mailingAddressChange,proto3,oneof"`
}

type NonFleetChange_TerminalAddressChange struct {
	TerminalAddressChange *TerminalAddressChange `protobuf:"bytes,5,opt,name=terminalAddressChange,proto3,oneof"`
}

type NonFleetChange_LossPayeeChange struct {
	LossPayeeChange *LossPayeeChange `protobuf:"bytes,6,opt,name=lossPayeeChange,proto3,oneof"`
}

type NonFleetChange_CommoditiesChange struct {
	CommoditiesChange *CommoditiesChange `protobuf:"bytes,7,opt,name=commoditiesChange,proto3,oneof"`
}

type NonFleetChange_RadiusOfOperationChange struct {
	RadiusOfOperationChange *RadiusOfOperationChange `protobuf:"bytes,8,opt,name=radiusOfOperationChange,proto3,oneof"`
}

type NonFleetChange_CompanyNameChange struct {
	CompanyNameChange *CompanyNameChange `protobuf:"bytes,9,opt,name=companyNameChange,proto3,oneof"`
}

type NonFleetChange_BusinessOwnerChange struct {
	BusinessOwnerChange *BusinessOwnerChange `protobuf:"bytes,10,opt,name=businessOwnerChange,proto3,oneof"`
}

func (*NonFleetChange_DriverChange) isNonFleetChange_Data() {}

func (*NonFleetChange_VehicleChange) isNonFleetChange_Data() {}

func (*NonFleetChange_MailingAddressChange) isNonFleetChange_Data() {}

func (*NonFleetChange_TerminalAddressChange) isNonFleetChange_Data() {}

func (*NonFleetChange_LossPayeeChange) isNonFleetChange_Data() {}

func (*NonFleetChange_CommoditiesChange) isNonFleetChange_Data() {}

func (*NonFleetChange_RadiusOfOperationChange) isNonFleetChange_Data() {}

func (*NonFleetChange_CompanyNameChange) isNonFleetChange_Data() {}

func (*NonFleetChange_BusinessOwnerChange) isNonFleetChange_Data() {}

type DriverChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Add    []*model.NFAdmittedDriverDataV1 `protobuf:"bytes,1,rep,name=add,proto3" json:"add,omitempty"`
	Remove []string                        `protobuf:"bytes,2,rep,name=remove,proto3" json:"remove,omitempty"`
	Update []*model.NFAdmittedDriverDataV1 `protobuf:"bytes,3,rep,name=update,proto3" json:"update,omitempty"`
}

func (x *DriverChange) Reset() {
	*x = DriverChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DriverChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverChange) ProtoMessage() {}

func (x *DriverChange) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverChange.ProtoReflect.Descriptor instead.
func (*DriverChange) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{1}
}

func (x *DriverChange) GetAdd() []*model.NFAdmittedDriverDataV1 {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *DriverChange) GetRemove() []string {
	if x != nil {
		return x.Remove
	}
	return nil
}

func (x *DriverChange) GetUpdate() []*model.NFAdmittedDriverDataV1 {
	if x != nil {
		return x.Update
	}
	return nil
}

type VehicleChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Add    []*model.NFAdmittedVehicleDataV1 `protobuf:"bytes,1,rep,name=add,proto3" json:"add,omitempty"`
	Remove []string                         `protobuf:"bytes,2,rep,name=remove,proto3" json:"remove,omitempty"`
	Update []*model.NFAdmittedVehicleDataV1 `protobuf:"bytes,3,rep,name=update,proto3" json:"update,omitempty"`
}

func (x *VehicleChange) Reset() {
	*x = VehicleChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleChange) ProtoMessage() {}

func (x *VehicleChange) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleChange.ProtoReflect.Descriptor instead.
func (*VehicleChange) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{2}
}

func (x *VehicleChange) GetAdd() []*model.NFAdmittedVehicleDataV1 {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *VehicleChange) GetRemove() []string {
	if x != nil {
		return x.Remove
	}
	return nil
}

func (x *VehicleChange) GetUpdate() []*model.NFAdmittedVehicleDataV1 {
	if x != nil {
		return x.Update
	}
	return nil
}

type MailingAddressChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewAddress *proto.Address `protobuf:"bytes,1,opt,name=newAddress,proto3" json:"newAddress,omitempty"`
}

func (x *MailingAddressChange) Reset() {
	*x = MailingAddressChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MailingAddressChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MailingAddressChange) ProtoMessage() {}

func (x *MailingAddressChange) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MailingAddressChange.ProtoReflect.Descriptor instead.
func (*MailingAddressChange) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{3}
}

func (x *MailingAddressChange) GetNewAddress() *proto.Address {
	if x != nil {
		return x.NewAddress
	}
	return nil
}

type TerminalAddressChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewAddress *proto.Address `protobuf:"bytes,1,opt,name=newAddress,proto3" json:"newAddress,omitempty"`
}

func (x *TerminalAddressChange) Reset() {
	*x = TerminalAddressChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TerminalAddressChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalAddressChange) ProtoMessage() {}

func (x *TerminalAddressChange) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalAddressChange.ProtoReflect.Descriptor instead.
func (*TerminalAddressChange) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{4}
}

func (x *TerminalAddressChange) GetNewAddress() *proto.Address {
	if x != nil {
		return x.NewAddress
	}
	return nil
}

type LossPayeeChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Add    []*AddLossPayee    `protobuf:"bytes,1,rep,name=add,proto3" json:"add,omitempty"`
	Remove []*RemoveLossPayee `protobuf:"bytes,2,rep,name=remove,proto3" json:"remove,omitempty"`
}

func (x *LossPayeeChange) Reset() {
	*x = LossPayeeChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LossPayeeChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LossPayeeChange) ProtoMessage() {}

func (x *LossPayeeChange) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LossPayeeChange.ProtoReflect.Descriptor instead.
func (*LossPayeeChange) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{5}
}

func (x *LossPayeeChange) GetAdd() []*AddLossPayee {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *LossPayeeChange) GetRemove() []*RemoveLossPayee {
	if x != nil {
		return x.Remove
	}
	return nil
}

type AddLossPayee struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin       string          `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	LossPayee *proto1.Insured `protobuf:"bytes,2,opt,name=lossPayee,proto3" json:"lossPayee,omitempty"`
}

func (x *AddLossPayee) Reset() {
	*x = AddLossPayee{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLossPayee) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLossPayee) ProtoMessage() {}

func (x *AddLossPayee) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLossPayee.ProtoReflect.Descriptor instead.
func (*AddLossPayee) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{6}
}

func (x *AddLossPayee) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *AddLossPayee) GetLossPayee() *proto1.Insured {
	if x != nil {
		return x.LossPayee
	}
	return nil
}

type RemoveLossPayee struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin string `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
}

func (x *RemoveLossPayee) Reset() {
	*x = RemoveLossPayee{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveLossPayee) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveLossPayee) ProtoMessage() {}

func (x *RemoveLossPayee) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveLossPayee.ProtoReflect.Descriptor instead.
func (*RemoveLossPayee) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{7}
}

func (x *RemoveLossPayee) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

type CommoditiesChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommodityDetails *model.NFAdmittedCommodityDetailsV1 `protobuf:"bytes,1,opt,name=commodityDetails,proto3" json:"commodityDetails,omitempty"`
}

func (x *CommoditiesChange) Reset() {
	*x = CommoditiesChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommoditiesChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommoditiesChange) ProtoMessage() {}

func (x *CommoditiesChange) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommoditiesChange.ProtoReflect.Descriptor instead.
func (*CommoditiesChange) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{8}
}

func (x *CommoditiesChange) GetCommodityDetails() *model.NFAdmittedCommodityDetailsV1 {
	if x != nil {
		return x.CommodityDetails
	}
	return nil
}

type RadiusOfOperationChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxRadiusOfOperation model.MaxRadiusOfOperation `protobuf:"varint,1,opt,name=maxRadiusOfOperation,proto3,enum=nonfleet_model.MaxRadiusOfOperation" json:"maxRadiusOfOperation,omitempty"`
}

func (x *RadiusOfOperationChange) Reset() {
	*x = RadiusOfOperationChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RadiusOfOperationChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RadiusOfOperationChange) ProtoMessage() {}

func (x *RadiusOfOperationChange) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RadiusOfOperationChange.ProtoReflect.Descriptor instead.
func (*RadiusOfOperationChange) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{9}
}

func (x *RadiusOfOperationChange) GetMaxRadiusOfOperation() model.MaxRadiusOfOperation {
	if x != nil {
		return x.MaxRadiusOfOperation
	}
	return model.MaxRadiusOfOperation(0)
}

type CompanyNameChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewCompanyName string `protobuf:"bytes,1,opt,name=newCompanyName,proto3" json:"newCompanyName,omitempty"`
}

func (x *CompanyNameChange) Reset() {
	*x = CompanyNameChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyNameChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyNameChange) ProtoMessage() {}

func (x *CompanyNameChange) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyNameChange.ProtoReflect.Descriptor instead.
func (*CompanyNameChange) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{10}
}

func (x *CompanyNameChange) GetNewCompanyName() string {
	if x != nil {
		return x.NewCompanyName
	}
	return ""
}

type BusinessOwnerChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewBusinessOwner *model.NFBusinessOwnerV1 `protobuf:"bytes,1,opt,name=newBusinessOwner,proto3" json:"newBusinessOwner,omitempty"`
}

func (x *BusinessOwnerChange) Reset() {
	*x = BusinessOwnerChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessOwnerChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOwnerChange) ProtoMessage() {}

func (x *BusinessOwnerChange) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_endorsement_change_data_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOwnerChange.ProtoReflect.Descriptor instead.
func (*BusinessOwnerChange) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{11}
}

func (x *BusinessOwnerChange) GetNewBusinessOwner() *model.NFBusinessOwnerV1 {
	if x != nil {
		return x.NewBusinessOwner
	}
	return nil
}

var File_nonfleet_model_endorsement_change_data_proto protoreflect.FileDescriptor

var file_nonfleet_model_endorsement_change_data_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14,
	0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x21, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8f, 0x07, 0x0a,
	0x0e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x48, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c,
	0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x0c, 0x64, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x4b, 0x0a, 0x0d, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6e, 0x6f, 0x6e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48,
	0x00, 0x52, 0x0d, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x60, 0x0a, 0x14, 0x6d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x14, 0x6d, 0x61,
	0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x63, 0x0a, 0x15, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00,
	0x52, 0x15, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x51, 0x0a, 0x0f, 0x6c, 0x6f, 0x73, 0x73, 0x50,
	0x61, 0x79, 0x65, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x6f, 0x73, 0x73, 0x50, 0x61, 0x79, 0x65,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x0f, 0x6c, 0x6f, 0x73, 0x73, 0x50,
	0x61, 0x79, 0x65, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x57, 0x0a, 0x11, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00,
	0x52, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x69, 0x0a, 0x17, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x61, 0x64, 0x69,
	0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x17, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x57,
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6e, 0x6f, 0x6e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x48, 0x00, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x5d, 0x0a, 0x13, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48,
	0x00, 0x52, 0x13, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa0,
	0x01, 0x0a, 0x0c, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x38, 0x0a, 0x03, 0x61, 0x64, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6e,
	0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x46,
	0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x56, 0x31, 0x52, 0x03, 0x61, 0x64, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x12, 0x3e, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x22, 0xa3, 0x01, 0x0a, 0x0d, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x03, 0x61, 0x64, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x03, 0x61, 0x64, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x3f, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74,
	0x65, 0x64, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x52,
	0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0x47, 0x0a, 0x14, 0x4d, 0x61, 0x69, 0x6c, 0x69,
	0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x2f, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x0a, 0x6e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x22, 0x48, 0x0a, 0x15, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x0a, 0x6e, 0x65, 0x77,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0a,
	0x6e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x86, 0x01, 0x0a, 0x0f, 0x4c,
	0x6f, 0x73, 0x73, 0x50, 0x61, 0x79, 0x65, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x34,
	0x0a, 0x03, 0x61, 0x64, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6e, 0x6f,
	0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x73, 0x73, 0x50, 0x61, 0x79, 0x65, 0x65, 0x52,
	0x03, 0x61, 0x64, 0x64, 0x12, 0x3d, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x4c, 0x6f, 0x73, 0x73, 0x50, 0x61, 0x79, 0x65, 0x65, 0x52, 0x06, 0x72, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x22, 0x57, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x73, 0x73, 0x50, 0x61,
	0x79, 0x65, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x76, 0x69, 0x6e, 0x12, 0x35, 0x0a, 0x09, 0x6c, 0x6f, 0x73, 0x73, 0x50, 0x61, 0x79,
	0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x52, 0x09, 0x6c, 0x6f, 0x73, 0x73, 0x50, 0x61, 0x79, 0x65, 0x65, 0x22, 0x23, 0x0a, 0x0f,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4c, 0x6f, 0x73, 0x73, 0x50, 0x61, 0x79, 0x65, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69,
	0x6e, 0x22, 0x6d, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x58, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64,
	0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x64, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56, 0x31, 0x52, 0x10,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0x73, 0x0a, 0x17, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x58, 0x0a, 0x14, 0x6d,
	0x61, 0x78, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6e, 0x6f, 0x6e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x78, 0x52, 0x61,
	0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x14, 0x6d, 0x61, 0x78, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3b, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x6e, 0x65,
	0x77, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x6e, 0x65, 0x77, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x64, 0x0a, 0x13, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x10, 0x6e, 0x65, 0x77,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f,
	0x77, 0x6e, 0x65, 0x72, 0x56, 0x31, 0x52, 0x10, 0x6e, 0x65, 0x77, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x2a, 0xfc, 0x02, 0x0a, 0x12, 0x4e, 0x6f, 0x6e,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1e, 0x0a, 0x1a, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12,
	0x1d, 0x0a, 0x19, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x10, 0x01, 0x12, 0x1e,
	0x0a, 0x1a, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x10, 0x02, 0x12, 0x25,
	0x0a, 0x21, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x10, 0x04, 0x12, 0x20, 0x0a,
	0x1c, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x4c, 0x6f, 0x73, 0x73, 0x50, 0x61, 0x79, 0x65, 0x65, 0x10, 0x05, 0x12,
	0x22, 0x0a, 0x1e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x10, 0x06, 0x12, 0x28, 0x0a, 0x24, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73,
	0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x07, 0x12, 0x22, 0x0a,
	0x1e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x10,
	0x08, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x10, 0x09, 0x42, 0x34, 0x5a, 0x32, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x2f, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_nonfleet_model_endorsement_change_data_proto_rawDescOnce sync.Once
	file_nonfleet_model_endorsement_change_data_proto_rawDescData = file_nonfleet_model_endorsement_change_data_proto_rawDesc
)

func file_nonfleet_model_endorsement_change_data_proto_rawDescGZIP() []byte {
	file_nonfleet_model_endorsement_change_data_proto_rawDescOnce.Do(func() {
		file_nonfleet_model_endorsement_change_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_nonfleet_model_endorsement_change_data_proto_rawDescData)
	})
	return file_nonfleet_model_endorsement_change_data_proto_rawDescData
}

var file_nonfleet_model_endorsement_change_data_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_nonfleet_model_endorsement_change_data_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_nonfleet_model_endorsement_change_data_proto_goTypes = []interface{}{
	(NonFleetChangeType)(0),                    // 0: nonfleet_endorsement.NonFleetChangeType
	(*NonFleetChange)(nil),                     // 1: nonfleet_endorsement.NonFleetChange
	(*DriverChange)(nil),                       // 2: nonfleet_endorsement.DriverChange
	(*VehicleChange)(nil),                      // 3: nonfleet_endorsement.VehicleChange
	(*MailingAddressChange)(nil),               // 4: nonfleet_endorsement.MailingAddressChange
	(*TerminalAddressChange)(nil),              // 5: nonfleet_endorsement.TerminalAddressChange
	(*LossPayeeChange)(nil),                    // 6: nonfleet_endorsement.LossPayeeChange
	(*AddLossPayee)(nil),                       // 7: nonfleet_endorsement.AddLossPayee
	(*RemoveLossPayee)(nil),                    // 8: nonfleet_endorsement.RemoveLossPayee
	(*CommoditiesChange)(nil),                  // 9: nonfleet_endorsement.CommoditiesChange
	(*RadiusOfOperationChange)(nil),            // 10: nonfleet_endorsement.RadiusOfOperationChange
	(*CompanyNameChange)(nil),                  // 11: nonfleet_endorsement.CompanyNameChange
	(*BusinessOwnerChange)(nil),                // 12: nonfleet_endorsement.BusinessOwnerChange
	(*model.NFAdmittedDriverDataV1)(nil),       // 13: nonfleet_model.NFAdmittedDriverDataV1
	(*model.NFAdmittedVehicleDataV1)(nil),      // 14: nonfleet_model.NFAdmittedVehicleDataV1
	(*proto.Address)(nil),                      // 15: common.Address
	(*proto1.Insured)(nil),                     // 16: insurance_core.Insured
	(*model.NFAdmittedCommodityDetailsV1)(nil), // 17: nonfleet_model.NFAdmittedCommodityDetailsV1
	(model.MaxRadiusOfOperation)(0),            // 18: nonfleet_model.MaxRadiusOfOperation
	(*model.NFBusinessOwnerV1)(nil),            // 19: nonfleet_model.NFBusinessOwnerV1
}
var file_nonfleet_model_endorsement_change_data_proto_depIdxs = []int32{
	0,  // 0: nonfleet_endorsement.NonFleetChange.changeType:type_name -> nonfleet_endorsement.NonFleetChangeType
	2,  // 1: nonfleet_endorsement.NonFleetChange.driverChange:type_name -> nonfleet_endorsement.DriverChange
	3,  // 2: nonfleet_endorsement.NonFleetChange.vehicleChange:type_name -> nonfleet_endorsement.VehicleChange
	4,  // 3: nonfleet_endorsement.NonFleetChange.mailingAddressChange:type_name -> nonfleet_endorsement.MailingAddressChange
	5,  // 4: nonfleet_endorsement.NonFleetChange.terminalAddressChange:type_name -> nonfleet_endorsement.TerminalAddressChange
	6,  // 5: nonfleet_endorsement.NonFleetChange.lossPayeeChange:type_name -> nonfleet_endorsement.LossPayeeChange
	9,  // 6: nonfleet_endorsement.NonFleetChange.commoditiesChange:type_name -> nonfleet_endorsement.CommoditiesChange
	10, // 7: nonfleet_endorsement.NonFleetChange.radiusOfOperationChange:type_name -> nonfleet_endorsement.RadiusOfOperationChange
	11, // 8: nonfleet_endorsement.NonFleetChange.companyNameChange:type_name -> nonfleet_endorsement.CompanyNameChange
	12, // 9: nonfleet_endorsement.NonFleetChange.businessOwnerChange:type_name -> nonfleet_endorsement.BusinessOwnerChange
	13, // 10: nonfleet_endorsement.DriverChange.add:type_name -> nonfleet_model.NFAdmittedDriverDataV1
	13, // 11: nonfleet_endorsement.DriverChange.update:type_name -> nonfleet_model.NFAdmittedDriverDataV1
	14, // 12: nonfleet_endorsement.VehicleChange.add:type_name -> nonfleet_model.NFAdmittedVehicleDataV1
	14, // 13: nonfleet_endorsement.VehicleChange.update:type_name -> nonfleet_model.NFAdmittedVehicleDataV1
	15, // 14: nonfleet_endorsement.MailingAddressChange.newAddress:type_name -> common.Address
	15, // 15: nonfleet_endorsement.TerminalAddressChange.newAddress:type_name -> common.Address
	7,  // 16: nonfleet_endorsement.LossPayeeChange.add:type_name -> nonfleet_endorsement.AddLossPayee
	8,  // 17: nonfleet_endorsement.LossPayeeChange.remove:type_name -> nonfleet_endorsement.RemoveLossPayee
	16, // 18: nonfleet_endorsement.AddLossPayee.lossPayee:type_name -> insurance_core.Insured
	17, // 19: nonfleet_endorsement.CommoditiesChange.commodityDetails:type_name -> nonfleet_model.NFAdmittedCommodityDetailsV1
	18, // 20: nonfleet_endorsement.RadiusOfOperationChange.maxRadiusOfOperation:type_name -> nonfleet_model.MaxRadiusOfOperation
	19, // 21: nonfleet_endorsement.BusinessOwnerChange.newBusinessOwner:type_name -> nonfleet_model.NFBusinessOwnerV1
	22, // [22:22] is the sub-list for method output_type
	22, // [22:22] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_nonfleet_model_endorsement_change_data_proto_init() }
func file_nonfleet_model_endorsement_change_data_proto_init() {
	if File_nonfleet_model_endorsement_change_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleetChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DriverChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MailingAddressChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TerminalAddressChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LossPayeeChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLossPayee); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveLossPayee); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommoditiesChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RadiusOfOperationChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyNameChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_endorsement_change_data_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessOwnerChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_nonfleet_model_endorsement_change_data_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*NonFleetChange_DriverChange)(nil),
		(*NonFleetChange_VehicleChange)(nil),
		(*NonFleetChange_MailingAddressChange)(nil),
		(*NonFleetChange_TerminalAddressChange)(nil),
		(*NonFleetChange_LossPayeeChange)(nil),
		(*NonFleetChange_CommoditiesChange)(nil),
		(*NonFleetChange_RadiusOfOperationChange)(nil),
		(*NonFleetChange_CompanyNameChange)(nil),
		(*NonFleetChange_BusinessOwnerChange)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_nonfleet_model_endorsement_change_data_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_nonfleet_model_endorsement_change_data_proto_goTypes,
		DependencyIndexes: file_nonfleet_model_endorsement_change_data_proto_depIdxs,
		EnumInfos:         file_nonfleet_model_endorsement_change_data_proto_enumTypes,
		MessageInfos:      file_nonfleet_model_endorsement_change_data_proto_msgTypes,
	}.Build()
	File_nonfleet_model_endorsement_change_data_proto = out.File
	file_nonfleet_model_endorsement_change_data_proto_rawDesc = nil
	file_nonfleet_model_endorsement_change_data_proto_goTypes = nil
	file_nonfleet_model_endorsement_change_data_proto_depIdxs = nil
}
