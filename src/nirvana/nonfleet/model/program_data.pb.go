// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: nonfleet/model/program_data.proto

package model

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	proto1 "nirvanatech.com/nirvana/common-go/proto"
	proto "nirvanatech.com/nirvana/insurance-core/proto"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommodityCategory int32

const (
	CommodityCategory_COMMODITY_CATEGORY_UNSPECIFIED                                  CommodityCategory = 0
	CommodityCategory_COMMODITY_CATEGORY_BAKED_GOODS                                  CommodityCategory = 1
	CommodityCategory_COMMODITY_CATEGORY_BEER_WINE_NO_LIQUOR                          CommodityCategory = 2
	CommodityCategory_COMMODITY_CATEGORY_BEVERAGES                                    CommodityCategory = 3
	CommodityCategory_COMMODITY_CATEGORY_CANNED_GOODS                                 CommodityCategory = 4
	CommodityCategory_COMMODITY_CATEGORY_DAIRY                                        CommodityCategory = 5
	CommodityCategory_COMMODITY_CATEGORY_EGGS                                         CommodityCategory = 6
	CommodityCategory_COMMODITY_CATEGORY_FLOUR                                        CommodityCategory = 7
	CommodityCategory_COMMODITY_CATEGORY_FOOD_FROZEN_NOT_SEAFOOD                      CommodityCategory = 8
	CommodityCategory_COMMODITY_CATEGORY_FRUITS                                       CommodityCategory = 9
	CommodityCategory_COMMODITY_CATEGORY_MEATS_DRESSED_POULTRY                        CommodityCategory = 10
	CommodityCategory_COMMODITY_CATEGORY_OILS_EDIBLE                                  CommodityCategory = 11
	CommodityCategory_COMMODITY_CATEGORY_SALT                                         CommodityCategory = 12
	CommodityCategory_COMMODITY_CATEGORY_FRESH_SEAFOOD                                CommodityCategory = 13
	CommodityCategory_COMMODITY_CATEGORY_FROZEN_SEAFOOD                               CommodityCategory = 14
	CommodityCategory_COMMODITY_CATEGORY_TEA_COFFEE_SPICES                            CommodityCategory = 15
	CommodityCategory_COMMODITY_CATEGORY_VEGETABLES                                   CommodityCategory = 16
	CommodityCategory_COMMODITY_CATEGORY_OTHER_FOOD_AND_BEVERAGES                     CommodityCategory = 17
	CommodityCategory_COMMODITY_CATEGORY_COTTON_NON_GINNED                            CommodityCategory = 18
	CommodityCategory_COMMODITY_CATEGORY_TEXTILES_SKINS_FURS                          CommodityCategory = 19
	CommodityCategory_COMMODITY_CATEGORY_FEED_FERTILIZER                              CommodityCategory = 20
	CommodityCategory_COMMODITY_CATEGORY_GRAIN_HAY                                    CommodityCategory = 21
	CommodityCategory_COMMODITY_CATEGORY_MULCH_TOP_SOIL_AND_FILL                      CommodityCategory = 22
	CommodityCategory_COMMODITY_CATEGORY_PLANTS_SHRUBS_TREES_NOT_TEMP_CONTROLLED      CommodityCategory = 23
	CommodityCategory_COMMODITY_CATEGORY_PLANTS_SHRUBS_TREES_TEMP_CONTROLLED          CommodityCategory = 24
	CommodityCategory_COMMODITY_CATEGORY_SEEDS                                        CommodityCategory = 25
	CommodityCategory_COMMODITY_CATEGORY_OTHER_FARMING_AGRICULTURE                    CommodityCategory = 26
	CommodityCategory_COMMODITY_CATEGORY_LIVESTOCK                                    CommodityCategory = 27
	CommodityCategory_COMMODITY_CATEGORY_TOBACCO_LEAF_RAW                             CommodityCategory = 28
	CommodityCategory_COMMODITY_CATEGORY_CHEMICALS_NON_HAZARDOUS                      CommodityCategory = 29
	CommodityCategory_COMMODITY_CATEGORY_CLEANING_SUPPLIES_AND_COMPOUNDS              CommodityCategory = 30
	CommodityCategory_COMMODITY_CATEGORY_DYES_INK_AND_PAINTS_NON_HAZARDOUS            CommodityCategory = 31
	CommodityCategory_COMMODITY_CATEGORY_LIQUIDS_NON_CHEMICAL_OR_NON_PETROLEUM        CommodityCategory = 32
	CommodityCategory_COMMODITY_CATEGORY_OTHER_CHEMICALS                              CommodityCategory = 33
	CommodityCategory_COMMODITY_CATEGORY_ALUMINUM                                     CommodityCategory = 34
	CommodityCategory_COMMODITY_CATEGORY_COAL                                         CommodityCategory = 35
	CommodityCategory_COMMODITY_CATEGORY_IRON_AND_ORE                                 CommodityCategory = 36
	CommodityCategory_COMMODITY_CATEGORY_METAL_PRODUCTS                               CommodityCategory = 37
	CommodityCategory_COMMODITY_CATEGORY_STEEL                                        CommodityCategory = 38
	CommodityCategory_COMMODITY_CATEGORY_ZINC                                         CommodityCategory = 39
	CommodityCategory_COMMODITY_CATEGORY_OTHER_METALS_MINERALS_COAL                   CommodityCategory = 40
	CommodityCategory_COMMODITY_CATEGORY_COILED_STEEL                                 CommodityCategory = 41
	CommodityCategory_COMMODITY_CATEGORY_SCRAP_METAL                                  CommodityCategory = 42
	CommodityCategory_COMMODITY_CATEGORY_ASPHALT_CEMENT                               CommodityCategory = 43
	CommodityCategory_COMMODITY_CATEGORY_AGGREGATES_GRAVEL_ROCK_SAND_STONE            CommodityCategory = 44
	CommodityCategory_COMMODITY_CATEGORY_MARBLE_GRANITE_OTHER_STONE_SLABS             CommodityCategory = 45
	CommodityCategory_COMMODITY_CATEGORY_OTHER_CONSTRUCTION_MATERIALS                 CommodityCategory = 46
	CommodityCategory_COMMODITY_CATEGORY_LOGS                                         CommodityCategory = 47
	CommodityCategory_COMMODITY_CATEGORY_BOTTLES_PLASTIC                              CommodityCategory = 48
	CommodityCategory_COMMODITY_CATEGORY_CONTAINERIZED_FREIGHT                        CommodityCategory = 49
	CommodityCategory_COMMODITY_CATEGORY_GLASS_PRODUCTS                               CommodityCategory = 50
	CommodityCategory_COMMODITY_CATEGORY_PACKING_MATERIALS_AND_SUPPLIES               CommodityCategory = 51
	CommodityCategory_COMMODITY_CATEGORY_PAPER_AND_PAPER_PRODUCTS                     CommodityCategory = 52
	CommodityCategory_COMMODITY_CATEGORY_PLASTIC_PRODUCTS                             CommodityCategory = 53
	CommodityCategory_COMMODITY_CATEGORY_PRINTED_MATERIAL                             CommodityCategory = 54
	CommodityCategory_COMMODITY_CATEGORY_RUBBER_PRODUCTS_NOT_TIRES                    CommodityCategory = 55
	CommodityCategory_COMMODITY_CATEGORY_OTHER_PAPER_PLASTIC_GLASS                    CommodityCategory = 56
	CommodityCategory_COMMODITY_CATEGORY_RECYCLING_MATERIALS                          CommodityCategory = 57
	CommodityCategory_COMMODITY_CATEGORY_APPLIANCES                                   CommodityCategory = 58
	CommodityCategory_COMMODITY_CATEGORY_CARPET_NOT_ORIENTAL                          CommodityCategory = 59
	CommodityCategory_COMMODITY_CATEGORY_CASKETS                                      CommodityCategory = 60
	CommodityCategory_COMMODITY_CATEGORY_CDS_DVD_VIDEO_GAMES_TAPES                    CommodityCategory = 61
	CommodityCategory_COMMODITY_CATEGORY_CLOTHING_AND_SHOES_NON_DESIGNER              CommodityCategory = 62
	CommodityCategory_COMMODITY_CATEGORY_COSMETICS_PERFUME                            CommodityCategory = 63
	CommodityCategory_COMMODITY_CATEGORY_FURNITURE_NEW                                CommodityCategory = 64
	CommodityCategory_COMMODITY_CATEGORY_MUSICAL_INSTRUMENTS                          CommodityCategory = 65
	CommodityCategory_COMMODITY_CATEGORY_OFFICE_EQUIPMENT                             CommodityCategory = 66
	CommodityCategory_COMMODITY_CATEGORY_PHARMACEUTICALS_OVER_THE_COUNTER             CommodityCategory = 67
	CommodityCategory_COMMODITY_CATEGORY_SPAS_HOT_TUBS                                CommodityCategory = 68
	CommodityCategory_COMMODITY_CATEGORY_SPORTING_GOODS                               CommodityCategory = 69
	CommodityCategory_COMMODITY_CATEGORY_TOYS                                         CommodityCategory = 70
	CommodityCategory_COMMODITY_CATEGORY_OTHER_CONSUMER_GOODS                         CommodityCategory = 71
	CommodityCategory_COMMODITY_CATEGORY_HOUSEHOLD_GOODS_MOVER                        CommodityCategory = 72
	CommodityCategory_COMMODITY_CATEGORY_AIRCRAFT_ENGINES                             CommodityCategory = 73
	CommodityCategory_COMMODITY_CATEGORY_AIRCRAFT_PARTS_NOT_ENGINES                   CommodityCategory = 74
	CommodityCategory_COMMODITY_CATEGORY_AUTO_ACCESSORIES_PARTS_NOT_TIRES             CommodityCategory = 75
	CommodityCategory_COMMODITY_CATEGORY_AUTOMOBILES                                  CommodityCategory = 76
	CommodityCategory_COMMODITY_CATEGORY_BOATS_GOLF_CARTS_RV_TRAVEL_TRAILERS          CommodityCategory = 77
	CommodityCategory_COMMODITY_CATEGORY_TIRES                                        CommodityCategory = 78
	CommodityCategory_COMMODITY_CATEGORY_OTHER_AUTOS_AIRCRAFTS                        CommodityCategory = 79
	CommodityCategory_COMMODITY_CATEGORY_CONSTRUCTION_EQUIPMENT                       CommodityCategory = 80
	CommodityCategory_COMMODITY_CATEGORY_ELECTRICAL_COMMUNICATIONS_EQUIPMENT          CommodityCategory = 81
	CommodityCategory_COMMODITY_CATEGORY_MACHINERY                                    CommodityCategory = 82
	CommodityCategory_COMMODITY_CATEGORY_MEDICAL_AND_SCIENTIFIC_EQUIPMENT             CommodityCategory = 83
	CommodityCategory_COMMODITY_CATEGORY_OILFIELD_EQUIPMENT                           CommodityCategory = 84
	CommodityCategory_COMMODITY_CATEGORY_OTHER_MACHINERY_EQUIPMENT                    CommodityCategory = 85
	CommodityCategory_COMMODITY_CATEGORY_ELECTRICAL_SUPPLIES                          CommodityCategory = 86
	CommodityCategory_COMMODITY_CATEGORY_LUMBER                                       CommodityCategory = 87
	CommodityCategory_COMMODITY_CATEGORY_PIPE                                         CommodityCategory = 88
	CommodityCategory_COMMODITY_CATEGORY_PLUMBING_SUPPLIES                            CommodityCategory = 89
	CommodityCategory_COMMODITY_CATEGORY_TOOLS                                        CommodityCategory = 90
	CommodityCategory_COMMODITY_CATEGORY_WIRE                                         CommodityCategory = 91
	CommodityCategory_COMMODITY_CATEGORY_WOOD_PRODUCTS_NOT_FURNITURE_AND_CASKETS      CommodityCategory = 92
	CommodityCategory_COMMODITY_CATEGORY_OTHER_BUILDING_SUPPLIES                      CommodityCategory = 93
	CommodityCategory_COMMODITY_CATEGORY_MOBILE_MODULAR_HOMES                         CommodityCategory = 94
	CommodityCategory_COMMODITY_CATEGORY_ANIMAL_BY_PRODUCTS                           CommodityCategory = 95
	CommodityCategory_COMMODITY_CATEGORY_RESINS                                       CommodityCategory = 96
	CommodityCategory_COMMODITY_CATEGORY_OTHER_MISCELLANEOUS                          CommodityCategory = 97
	CommodityCategory_COMMODITY_CATEGORY_GARBAGE                                      CommodityCategory = 98
	CommodityCategory_COMMODITY_CATEGORY_BUSINESS_DOCUMENTS_NON_NEGOTIABLE_SECURITIES CommodityCategory = 99
)

// Enum value maps for CommodityCategory.
var (
	CommodityCategory_name = map[int32]string{
		0:  "COMMODITY_CATEGORY_UNSPECIFIED",
		1:  "COMMODITY_CATEGORY_BAKED_GOODS",
		2:  "COMMODITY_CATEGORY_BEER_WINE_NO_LIQUOR",
		3:  "COMMODITY_CATEGORY_BEVERAGES",
		4:  "COMMODITY_CATEGORY_CANNED_GOODS",
		5:  "COMMODITY_CATEGORY_DAIRY",
		6:  "COMMODITY_CATEGORY_EGGS",
		7:  "COMMODITY_CATEGORY_FLOUR",
		8:  "COMMODITY_CATEGORY_FOOD_FROZEN_NOT_SEAFOOD",
		9:  "COMMODITY_CATEGORY_FRUITS",
		10: "COMMODITY_CATEGORY_MEATS_DRESSED_POULTRY",
		11: "COMMODITY_CATEGORY_OILS_EDIBLE",
		12: "COMMODITY_CATEGORY_SALT",
		13: "COMMODITY_CATEGORY_FRESH_SEAFOOD",
		14: "COMMODITY_CATEGORY_FROZEN_SEAFOOD",
		15: "COMMODITY_CATEGORY_TEA_COFFEE_SPICES",
		16: "COMMODITY_CATEGORY_VEGETABLES",
		17: "COMMODITY_CATEGORY_OTHER_FOOD_AND_BEVERAGES",
		18: "COMMODITY_CATEGORY_COTTON_NON_GINNED",
		19: "COMMODITY_CATEGORY_TEXTILES_SKINS_FURS",
		20: "COMMODITY_CATEGORY_FEED_FERTILIZER",
		21: "COMMODITY_CATEGORY_GRAIN_HAY",
		22: "COMMODITY_CATEGORY_MULCH_TOP_SOIL_AND_FILL",
		23: "COMMODITY_CATEGORY_PLANTS_SHRUBS_TREES_NOT_TEMP_CONTROLLED",
		24: "COMMODITY_CATEGORY_PLANTS_SHRUBS_TREES_TEMP_CONTROLLED",
		25: "COMMODITY_CATEGORY_SEEDS",
		26: "COMMODITY_CATEGORY_OTHER_FARMING_AGRICULTURE",
		27: "COMMODITY_CATEGORY_LIVESTOCK",
		28: "COMMODITY_CATEGORY_TOBACCO_LEAF_RAW",
		29: "COMMODITY_CATEGORY_CHEMICALS_NON_HAZARDOUS",
		30: "COMMODITY_CATEGORY_CLEANING_SUPPLIES_AND_COMPOUNDS",
		31: "COMMODITY_CATEGORY_DYES_INK_AND_PAINTS_NON_HAZARDOUS",
		32: "COMMODITY_CATEGORY_LIQUIDS_NON_CHEMICAL_OR_NON_PETROLEUM",
		33: "COMMODITY_CATEGORY_OTHER_CHEMICALS",
		34: "COMMODITY_CATEGORY_ALUMINUM",
		35: "COMMODITY_CATEGORY_COAL",
		36: "COMMODITY_CATEGORY_IRON_AND_ORE",
		37: "COMMODITY_CATEGORY_METAL_PRODUCTS",
		38: "COMMODITY_CATEGORY_STEEL",
		39: "COMMODITY_CATEGORY_ZINC",
		40: "COMMODITY_CATEGORY_OTHER_METALS_MINERALS_COAL",
		41: "COMMODITY_CATEGORY_COILED_STEEL",
		42: "COMMODITY_CATEGORY_SCRAP_METAL",
		43: "COMMODITY_CATEGORY_ASPHALT_CEMENT",
		44: "COMMODITY_CATEGORY_AGGREGATES_GRAVEL_ROCK_SAND_STONE",
		45: "COMMODITY_CATEGORY_MARBLE_GRANITE_OTHER_STONE_SLABS",
		46: "COMMODITY_CATEGORY_OTHER_CONSTRUCTION_MATERIALS",
		47: "COMMODITY_CATEGORY_LOGS",
		48: "COMMODITY_CATEGORY_BOTTLES_PLASTIC",
		49: "COMMODITY_CATEGORY_CONTAINERIZED_FREIGHT",
		50: "COMMODITY_CATEGORY_GLASS_PRODUCTS",
		51: "COMMODITY_CATEGORY_PACKING_MATERIALS_AND_SUPPLIES",
		52: "COMMODITY_CATEGORY_PAPER_AND_PAPER_PRODUCTS",
		53: "COMMODITY_CATEGORY_PLASTIC_PRODUCTS",
		54: "COMMODITY_CATEGORY_PRINTED_MATERIAL",
		55: "COMMODITY_CATEGORY_RUBBER_PRODUCTS_NOT_TIRES",
		56: "COMMODITY_CATEGORY_OTHER_PAPER_PLASTIC_GLASS",
		57: "COMMODITY_CATEGORY_RECYCLING_MATERIALS",
		58: "COMMODITY_CATEGORY_APPLIANCES",
		59: "COMMODITY_CATEGORY_CARPET_NOT_ORIENTAL",
		60: "COMMODITY_CATEGORY_CASKETS",
		61: "COMMODITY_CATEGORY_CDS_DVD_VIDEO_GAMES_TAPES",
		62: "COMMODITY_CATEGORY_CLOTHING_AND_SHOES_NON_DESIGNER",
		63: "COMMODITY_CATEGORY_COSMETICS_PERFUME",
		64: "COMMODITY_CATEGORY_FURNITURE_NEW",
		65: "COMMODITY_CATEGORY_MUSICAL_INSTRUMENTS",
		66: "COMMODITY_CATEGORY_OFFICE_EQUIPMENT",
		67: "COMMODITY_CATEGORY_PHARMACEUTICALS_OVER_THE_COUNTER",
		68: "COMMODITY_CATEGORY_SPAS_HOT_TUBS",
		69: "COMMODITY_CATEGORY_SPORTING_GOODS",
		70: "COMMODITY_CATEGORY_TOYS",
		71: "COMMODITY_CATEGORY_OTHER_CONSUMER_GOODS",
		72: "COMMODITY_CATEGORY_HOUSEHOLD_GOODS_MOVER",
		73: "COMMODITY_CATEGORY_AIRCRAFT_ENGINES",
		74: "COMMODITY_CATEGORY_AIRCRAFT_PARTS_NOT_ENGINES",
		75: "COMMODITY_CATEGORY_AUTO_ACCESSORIES_PARTS_NOT_TIRES",
		76: "COMMODITY_CATEGORY_AUTOMOBILES",
		77: "COMMODITY_CATEGORY_BOATS_GOLF_CARTS_RV_TRAVEL_TRAILERS",
		78: "COMMODITY_CATEGORY_TIRES",
		79: "COMMODITY_CATEGORY_OTHER_AUTOS_AIRCRAFTS",
		80: "COMMODITY_CATEGORY_CONSTRUCTION_EQUIPMENT",
		81: "COMMODITY_CATEGORY_ELECTRICAL_COMMUNICATIONS_EQUIPMENT",
		82: "COMMODITY_CATEGORY_MACHINERY",
		83: "COMMODITY_CATEGORY_MEDICAL_AND_SCIENTIFIC_EQUIPMENT",
		84: "COMMODITY_CATEGORY_OILFIELD_EQUIPMENT",
		85: "COMMODITY_CATEGORY_OTHER_MACHINERY_EQUIPMENT",
		86: "COMMODITY_CATEGORY_ELECTRICAL_SUPPLIES",
		87: "COMMODITY_CATEGORY_LUMBER",
		88: "COMMODITY_CATEGORY_PIPE",
		89: "COMMODITY_CATEGORY_PLUMBING_SUPPLIES",
		90: "COMMODITY_CATEGORY_TOOLS",
		91: "COMMODITY_CATEGORY_WIRE",
		92: "COMMODITY_CATEGORY_WOOD_PRODUCTS_NOT_FURNITURE_AND_CASKETS",
		93: "COMMODITY_CATEGORY_OTHER_BUILDING_SUPPLIES",
		94: "COMMODITY_CATEGORY_MOBILE_MODULAR_HOMES",
		95: "COMMODITY_CATEGORY_ANIMAL_BY_PRODUCTS",
		96: "COMMODITY_CATEGORY_RESINS",
		97: "COMMODITY_CATEGORY_OTHER_MISCELLANEOUS",
		98: "COMMODITY_CATEGORY_GARBAGE",
		99: "COMMODITY_CATEGORY_BUSINESS_DOCUMENTS_NON_NEGOTIABLE_SECURITIES",
	}
	CommodityCategory_value = map[string]int32{
		"COMMODITY_CATEGORY_UNSPECIFIED":                                  0,
		"COMMODITY_CATEGORY_BAKED_GOODS":                                  1,
		"COMMODITY_CATEGORY_BEER_WINE_NO_LIQUOR":                          2,
		"COMMODITY_CATEGORY_BEVERAGES":                                    3,
		"COMMODITY_CATEGORY_CANNED_GOODS":                                 4,
		"COMMODITY_CATEGORY_DAIRY":                                        5,
		"COMMODITY_CATEGORY_EGGS":                                         6,
		"COMMODITY_CATEGORY_FLOUR":                                        7,
		"COMMODITY_CATEGORY_FOOD_FROZEN_NOT_SEAFOOD":                      8,
		"COMMODITY_CATEGORY_FRUITS":                                       9,
		"COMMODITY_CATEGORY_MEATS_DRESSED_POULTRY":                        10,
		"COMMODITY_CATEGORY_OILS_EDIBLE":                                  11,
		"COMMODITY_CATEGORY_SALT":                                         12,
		"COMMODITY_CATEGORY_FRESH_SEAFOOD":                                13,
		"COMMODITY_CATEGORY_FROZEN_SEAFOOD":                               14,
		"COMMODITY_CATEGORY_TEA_COFFEE_SPICES":                            15,
		"COMMODITY_CATEGORY_VEGETABLES":                                   16,
		"COMMODITY_CATEGORY_OTHER_FOOD_AND_BEVERAGES":                     17,
		"COMMODITY_CATEGORY_COTTON_NON_GINNED":                            18,
		"COMMODITY_CATEGORY_TEXTILES_SKINS_FURS":                          19,
		"COMMODITY_CATEGORY_FEED_FERTILIZER":                              20,
		"COMMODITY_CATEGORY_GRAIN_HAY":                                    21,
		"COMMODITY_CATEGORY_MULCH_TOP_SOIL_AND_FILL":                      22,
		"COMMODITY_CATEGORY_PLANTS_SHRUBS_TREES_NOT_TEMP_CONTROLLED":      23,
		"COMMODITY_CATEGORY_PLANTS_SHRUBS_TREES_TEMP_CONTROLLED":          24,
		"COMMODITY_CATEGORY_SEEDS":                                        25,
		"COMMODITY_CATEGORY_OTHER_FARMING_AGRICULTURE":                    26,
		"COMMODITY_CATEGORY_LIVESTOCK":                                    27,
		"COMMODITY_CATEGORY_TOBACCO_LEAF_RAW":                             28,
		"COMMODITY_CATEGORY_CHEMICALS_NON_HAZARDOUS":                      29,
		"COMMODITY_CATEGORY_CLEANING_SUPPLIES_AND_COMPOUNDS":              30,
		"COMMODITY_CATEGORY_DYES_INK_AND_PAINTS_NON_HAZARDOUS":            31,
		"COMMODITY_CATEGORY_LIQUIDS_NON_CHEMICAL_OR_NON_PETROLEUM":        32,
		"COMMODITY_CATEGORY_OTHER_CHEMICALS":                              33,
		"COMMODITY_CATEGORY_ALUMINUM":                                     34,
		"COMMODITY_CATEGORY_COAL":                                         35,
		"COMMODITY_CATEGORY_IRON_AND_ORE":                                 36,
		"COMMODITY_CATEGORY_METAL_PRODUCTS":                               37,
		"COMMODITY_CATEGORY_STEEL":                                        38,
		"COMMODITY_CATEGORY_ZINC":                                         39,
		"COMMODITY_CATEGORY_OTHER_METALS_MINERALS_COAL":                   40,
		"COMMODITY_CATEGORY_COILED_STEEL":                                 41,
		"COMMODITY_CATEGORY_SCRAP_METAL":                                  42,
		"COMMODITY_CATEGORY_ASPHALT_CEMENT":                               43,
		"COMMODITY_CATEGORY_AGGREGATES_GRAVEL_ROCK_SAND_STONE":            44,
		"COMMODITY_CATEGORY_MARBLE_GRANITE_OTHER_STONE_SLABS":             45,
		"COMMODITY_CATEGORY_OTHER_CONSTRUCTION_MATERIALS":                 46,
		"COMMODITY_CATEGORY_LOGS":                                         47,
		"COMMODITY_CATEGORY_BOTTLES_PLASTIC":                              48,
		"COMMODITY_CATEGORY_CONTAINERIZED_FREIGHT":                        49,
		"COMMODITY_CATEGORY_GLASS_PRODUCTS":                               50,
		"COMMODITY_CATEGORY_PACKING_MATERIALS_AND_SUPPLIES":               51,
		"COMMODITY_CATEGORY_PAPER_AND_PAPER_PRODUCTS":                     52,
		"COMMODITY_CATEGORY_PLASTIC_PRODUCTS":                             53,
		"COMMODITY_CATEGORY_PRINTED_MATERIAL":                             54,
		"COMMODITY_CATEGORY_RUBBER_PRODUCTS_NOT_TIRES":                    55,
		"COMMODITY_CATEGORY_OTHER_PAPER_PLASTIC_GLASS":                    56,
		"COMMODITY_CATEGORY_RECYCLING_MATERIALS":                          57,
		"COMMODITY_CATEGORY_APPLIANCES":                                   58,
		"COMMODITY_CATEGORY_CARPET_NOT_ORIENTAL":                          59,
		"COMMODITY_CATEGORY_CASKETS":                                      60,
		"COMMODITY_CATEGORY_CDS_DVD_VIDEO_GAMES_TAPES":                    61,
		"COMMODITY_CATEGORY_CLOTHING_AND_SHOES_NON_DESIGNER":              62,
		"COMMODITY_CATEGORY_COSMETICS_PERFUME":                            63,
		"COMMODITY_CATEGORY_FURNITURE_NEW":                                64,
		"COMMODITY_CATEGORY_MUSICAL_INSTRUMENTS":                          65,
		"COMMODITY_CATEGORY_OFFICE_EQUIPMENT":                             66,
		"COMMODITY_CATEGORY_PHARMACEUTICALS_OVER_THE_COUNTER":             67,
		"COMMODITY_CATEGORY_SPAS_HOT_TUBS":                                68,
		"COMMODITY_CATEGORY_SPORTING_GOODS":                               69,
		"COMMODITY_CATEGORY_TOYS":                                         70,
		"COMMODITY_CATEGORY_OTHER_CONSUMER_GOODS":                         71,
		"COMMODITY_CATEGORY_HOUSEHOLD_GOODS_MOVER":                        72,
		"COMMODITY_CATEGORY_AIRCRAFT_ENGINES":                             73,
		"COMMODITY_CATEGORY_AIRCRAFT_PARTS_NOT_ENGINES":                   74,
		"COMMODITY_CATEGORY_AUTO_ACCESSORIES_PARTS_NOT_TIRES":             75,
		"COMMODITY_CATEGORY_AUTOMOBILES":                                  76,
		"COMMODITY_CATEGORY_BOATS_GOLF_CARTS_RV_TRAVEL_TRAILERS":          77,
		"COMMODITY_CATEGORY_TIRES":                                        78,
		"COMMODITY_CATEGORY_OTHER_AUTOS_AIRCRAFTS":                        79,
		"COMMODITY_CATEGORY_CONSTRUCTION_EQUIPMENT":                       80,
		"COMMODITY_CATEGORY_ELECTRICAL_COMMUNICATIONS_EQUIPMENT":          81,
		"COMMODITY_CATEGORY_MACHINERY":                                    82,
		"COMMODITY_CATEGORY_MEDICAL_AND_SCIENTIFIC_EQUIPMENT":             83,
		"COMMODITY_CATEGORY_OILFIELD_EQUIPMENT":                           84,
		"COMMODITY_CATEGORY_OTHER_MACHINERY_EQUIPMENT":                    85,
		"COMMODITY_CATEGORY_ELECTRICAL_SUPPLIES":                          86,
		"COMMODITY_CATEGORY_LUMBER":                                       87,
		"COMMODITY_CATEGORY_PIPE":                                         88,
		"COMMODITY_CATEGORY_PLUMBING_SUPPLIES":                            89,
		"COMMODITY_CATEGORY_TOOLS":                                        90,
		"COMMODITY_CATEGORY_WIRE":                                         91,
		"COMMODITY_CATEGORY_WOOD_PRODUCTS_NOT_FURNITURE_AND_CASKETS":      92,
		"COMMODITY_CATEGORY_OTHER_BUILDING_SUPPLIES":                      93,
		"COMMODITY_CATEGORY_MOBILE_MODULAR_HOMES":                         94,
		"COMMODITY_CATEGORY_ANIMAL_BY_PRODUCTS":                           95,
		"COMMODITY_CATEGORY_RESINS":                                       96,
		"COMMODITY_CATEGORY_OTHER_MISCELLANEOUS":                          97,
		"COMMODITY_CATEGORY_GARBAGE":                                      98,
		"COMMODITY_CATEGORY_BUSINESS_DOCUMENTS_NON_NEGOTIABLE_SECURITIES": 99,
	}
)

func (x CommodityCategory) Enum() *CommodityCategory {
	p := new(CommodityCategory)
	*p = x
	return p
}

func (x CommodityCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommodityCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_nonfleet_model_program_data_proto_enumTypes[0].Descriptor()
}

func (CommodityCategory) Type() protoreflect.EnumType {
	return &file_nonfleet_model_program_data_proto_enumTypes[0]
}

func (x CommodityCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommodityCategory.Descriptor instead.
func (CommodityCategory) EnumDescriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{0}
}

type USDOTScore int32

const (
	USDOTScore_US_DOT_SCORE_UNSPECIFIED USDOTScore = 0
	USDOTScore_US_DOT_SCORE_X           USDOTScore = 1
	USDOTScore_US_DOT_SCORE_A01         USDOTScore = 2
	USDOTScore_US_DOT_SCORE_A02         USDOTScore = 3
	USDOTScore_US_DOT_SCORE_A03         USDOTScore = 4
	USDOTScore_US_DOT_SCORE_A04         USDOTScore = 5
	USDOTScore_US_DOT_SCORE_A05         USDOTScore = 6
	USDOTScore_US_DOT_SCORE_A06         USDOTScore = 7
	USDOTScore_US_DOT_SCORE_A07         USDOTScore = 8
	USDOTScore_US_DOT_SCORE_A08         USDOTScore = 9
	USDOTScore_US_DOT_SCORE_A09         USDOTScore = 10
	USDOTScore_US_DOT_SCORE_A10         USDOTScore = 11
	USDOTScore_US_DOT_SCORE_A11         USDOTScore = 12
	USDOTScore_US_DOT_SCORE_A12         USDOTScore = 13
	USDOTScore_US_DOT_SCORE_A13         USDOTScore = 14
	USDOTScore_US_DOT_SCORE_B01         USDOTScore = 15
	USDOTScore_US_DOT_SCORE_B02         USDOTScore = 16
	USDOTScore_US_DOT_SCORE_B03         USDOTScore = 17
	USDOTScore_US_DOT_SCORE_B04         USDOTScore = 18
	USDOTScore_US_DOT_SCORE_B05         USDOTScore = 19
	USDOTScore_US_DOT_SCORE_B06         USDOTScore = 20
	USDOTScore_US_DOT_SCORE_B07         USDOTScore = 21
	USDOTScore_US_DOT_SCORE_B08         USDOTScore = 22
	USDOTScore_US_DOT_SCORE_B09         USDOTScore = 23
	USDOTScore_US_DOT_SCORE_B10         USDOTScore = 24
	USDOTScore_US_DOT_SCORE_B11         USDOTScore = 25
	USDOTScore_US_DOT_SCORE_B12         USDOTScore = 26
	USDOTScore_US_DOT_SCORE_B13         USDOTScore = 27
	USDOTScore_US_DOT_SCORE_Z93         USDOTScore = 28
	USDOTScore_US_DOT_SCORE_Z94         USDOTScore = 29
	USDOTScore_US_DOT_SCORE_Z95         USDOTScore = 30
	USDOTScore_US_DOT_SCORE_Z97         USDOTScore = 31
	USDOTScore_US_DOT_SCORE_Z98         USDOTScore = 32
	USDOTScore_US_DOT_SCORE_Z99         USDOTScore = 33
	USDOTScore_US_DOT_SCORE_Z92         USDOTScore = 34
)

// Enum value maps for USDOTScore.
var (
	USDOTScore_name = map[int32]string{
		0:  "US_DOT_SCORE_UNSPECIFIED",
		1:  "US_DOT_SCORE_X",
		2:  "US_DOT_SCORE_A01",
		3:  "US_DOT_SCORE_A02",
		4:  "US_DOT_SCORE_A03",
		5:  "US_DOT_SCORE_A04",
		6:  "US_DOT_SCORE_A05",
		7:  "US_DOT_SCORE_A06",
		8:  "US_DOT_SCORE_A07",
		9:  "US_DOT_SCORE_A08",
		10: "US_DOT_SCORE_A09",
		11: "US_DOT_SCORE_A10",
		12: "US_DOT_SCORE_A11",
		13: "US_DOT_SCORE_A12",
		14: "US_DOT_SCORE_A13",
		15: "US_DOT_SCORE_B01",
		16: "US_DOT_SCORE_B02",
		17: "US_DOT_SCORE_B03",
		18: "US_DOT_SCORE_B04",
		19: "US_DOT_SCORE_B05",
		20: "US_DOT_SCORE_B06",
		21: "US_DOT_SCORE_B07",
		22: "US_DOT_SCORE_B08",
		23: "US_DOT_SCORE_B09",
		24: "US_DOT_SCORE_B10",
		25: "US_DOT_SCORE_B11",
		26: "US_DOT_SCORE_B12",
		27: "US_DOT_SCORE_B13",
		28: "US_DOT_SCORE_Z93",
		29: "US_DOT_SCORE_Z94",
		30: "US_DOT_SCORE_Z95",
		31: "US_DOT_SCORE_Z97",
		32: "US_DOT_SCORE_Z98",
		33: "US_DOT_SCORE_Z99",
		34: "US_DOT_SCORE_Z92",
	}
	USDOTScore_value = map[string]int32{
		"US_DOT_SCORE_UNSPECIFIED": 0,
		"US_DOT_SCORE_X":           1,
		"US_DOT_SCORE_A01":         2,
		"US_DOT_SCORE_A02":         3,
		"US_DOT_SCORE_A03":         4,
		"US_DOT_SCORE_A04":         5,
		"US_DOT_SCORE_A05":         6,
		"US_DOT_SCORE_A06":         7,
		"US_DOT_SCORE_A07":         8,
		"US_DOT_SCORE_A08":         9,
		"US_DOT_SCORE_A09":         10,
		"US_DOT_SCORE_A10":         11,
		"US_DOT_SCORE_A11":         12,
		"US_DOT_SCORE_A12":         13,
		"US_DOT_SCORE_A13":         14,
		"US_DOT_SCORE_B01":         15,
		"US_DOT_SCORE_B02":         16,
		"US_DOT_SCORE_B03":         17,
		"US_DOT_SCORE_B04":         18,
		"US_DOT_SCORE_B05":         19,
		"US_DOT_SCORE_B06":         20,
		"US_DOT_SCORE_B07":         21,
		"US_DOT_SCORE_B08":         22,
		"US_DOT_SCORE_B09":         23,
		"US_DOT_SCORE_B10":         24,
		"US_DOT_SCORE_B11":         25,
		"US_DOT_SCORE_B12":         26,
		"US_DOT_SCORE_B13":         27,
		"US_DOT_SCORE_Z93":         28,
		"US_DOT_SCORE_Z94":         29,
		"US_DOT_SCORE_Z95":         30,
		"US_DOT_SCORE_Z97":         31,
		"US_DOT_SCORE_Z98":         32,
		"US_DOT_SCORE_Z99":         33,
		"US_DOT_SCORE_Z92":         34,
	}
)

func (x USDOTScore) Enum() *USDOTScore {
	p := new(USDOTScore)
	*p = x
	return p
}

func (x USDOTScore) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (USDOTScore) Descriptor() protoreflect.EnumDescriptor {
	return file_nonfleet_model_program_data_proto_enumTypes[1].Descriptor()
}

func (USDOTScore) Type() protoreflect.EnumType {
	return &file_nonfleet_model_program_data_proto_enumTypes[1]
}

func (x USDOTScore) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use USDOTScore.Descriptor instead.
func (USDOTScore) EnumDescriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{1}
}

type CreditScore int32

const (
	CreditScore_CREDIT_SCORE_UNSPECIFIED CreditScore = 0
	CreditScore_CREDIT_SCORE_C0          CreditScore = 1
	CreditScore_CREDIT_SCORE_A1          CreditScore = 2
	CreditScore_CREDIT_SCORE_A2          CreditScore = 3
	CreditScore_CREDIT_SCORE_A3          CreditScore = 4
	CreditScore_CREDIT_SCORE_B0          CreditScore = 5
	CreditScore_CREDIT_SCORE_B1          CreditScore = 6
	CreditScore_CREDIT_SCORE_B2          CreditScore = 7
	CreditScore_CREDIT_SCORE_B3          CreditScore = 8
	CreditScore_CREDIT_SCORE_C1          CreditScore = 9
	CreditScore_CREDIT_SCORE_C2          CreditScore = 10
	CreditScore_CREDIT_SCORE_C3          CreditScore = 11
	CreditScore_CREDIT_SCORE_D0          CreditScore = 12
	CreditScore_CREDIT_SCORE_D1          CreditScore = 13
	CreditScore_CREDIT_SCORE_D2          CreditScore = 14
	CreditScore_CREDIT_SCORE_E0          CreditScore = 15
	CreditScore_CREDIT_SCORE_E1          CreditScore = 16
	CreditScore_CREDIT_SCORE_E2          CreditScore = 17
	CreditScore_CREDIT_SCORE_I1          CreditScore = 18
	CreditScore_CREDIT_SCORE_NA          CreditScore = 19
	CreditScore_CREDIT_SCORE_O1          CreditScore = 20
	CreditScore_CREDIT_SCORE_P1          CreditScore = 21
	CreditScore_CREDIT_SCORE_Q1          CreditScore = 22
	CreditScore_CREDIT_SCORE_T1          CreditScore = 23
	CreditScore_CREDIT_SCORE_T3          CreditScore = 24
	CreditScore_CREDIT_SCORE_T4          CreditScore = 25
	CreditScore_CREDIT_SCORE_T5          CreditScore = 26
	CreditScore_CREDIT_SCORE_X1          CreditScore = 27
	CreditScore_CREDIT_SCORE_X3          CreditScore = 28
	CreditScore_CREDIT_SCORE_X4          CreditScore = 29
	CreditScore_CREDIT_SCORE_X5          CreditScore = 30
	CreditScore_CREDIT_SCORE_XX          CreditScore = 31
)

// Enum value maps for CreditScore.
var (
	CreditScore_name = map[int32]string{
		0:  "CREDIT_SCORE_UNSPECIFIED",
		1:  "CREDIT_SCORE_C0",
		2:  "CREDIT_SCORE_A1",
		3:  "CREDIT_SCORE_A2",
		4:  "CREDIT_SCORE_A3",
		5:  "CREDIT_SCORE_B0",
		6:  "CREDIT_SCORE_B1",
		7:  "CREDIT_SCORE_B2",
		8:  "CREDIT_SCORE_B3",
		9:  "CREDIT_SCORE_C1",
		10: "CREDIT_SCORE_C2",
		11: "CREDIT_SCORE_C3",
		12: "CREDIT_SCORE_D0",
		13: "CREDIT_SCORE_D1",
		14: "CREDIT_SCORE_D2",
		15: "CREDIT_SCORE_E0",
		16: "CREDIT_SCORE_E1",
		17: "CREDIT_SCORE_E2",
		18: "CREDIT_SCORE_I1",
		19: "CREDIT_SCORE_NA",
		20: "CREDIT_SCORE_O1",
		21: "CREDIT_SCORE_P1",
		22: "CREDIT_SCORE_Q1",
		23: "CREDIT_SCORE_T1",
		24: "CREDIT_SCORE_T3",
		25: "CREDIT_SCORE_T4",
		26: "CREDIT_SCORE_T5",
		27: "CREDIT_SCORE_X1",
		28: "CREDIT_SCORE_X3",
		29: "CREDIT_SCORE_X4",
		30: "CREDIT_SCORE_X5",
		31: "CREDIT_SCORE_XX",
	}
	CreditScore_value = map[string]int32{
		"CREDIT_SCORE_UNSPECIFIED": 0,
		"CREDIT_SCORE_C0":          1,
		"CREDIT_SCORE_A1":          2,
		"CREDIT_SCORE_A2":          3,
		"CREDIT_SCORE_A3":          4,
		"CREDIT_SCORE_B0":          5,
		"CREDIT_SCORE_B1":          6,
		"CREDIT_SCORE_B2":          7,
		"CREDIT_SCORE_B3":          8,
		"CREDIT_SCORE_C1":          9,
		"CREDIT_SCORE_C2":          10,
		"CREDIT_SCORE_C3":          11,
		"CREDIT_SCORE_D0":          12,
		"CREDIT_SCORE_D1":          13,
		"CREDIT_SCORE_D2":          14,
		"CREDIT_SCORE_E0":          15,
		"CREDIT_SCORE_E1":          16,
		"CREDIT_SCORE_E2":          17,
		"CREDIT_SCORE_I1":          18,
		"CREDIT_SCORE_NA":          19,
		"CREDIT_SCORE_O1":          20,
		"CREDIT_SCORE_P1":          21,
		"CREDIT_SCORE_Q1":          22,
		"CREDIT_SCORE_T1":          23,
		"CREDIT_SCORE_T3":          24,
		"CREDIT_SCORE_T4":          25,
		"CREDIT_SCORE_T5":          26,
		"CREDIT_SCORE_X1":          27,
		"CREDIT_SCORE_X3":          28,
		"CREDIT_SCORE_X4":          29,
		"CREDIT_SCORE_X5":          30,
		"CREDIT_SCORE_XX":          31,
	}
)

func (x CreditScore) Enum() *CreditScore {
	p := new(CreditScore)
	*p = x
	return p
}

func (x CreditScore) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreditScore) Descriptor() protoreflect.EnumDescriptor {
	return file_nonfleet_model_program_data_proto_enumTypes[2].Descriptor()
}

func (CreditScore) Type() protoreflect.EnumType {
	return &file_nonfleet_model_program_data_proto_enumTypes[2]
}

func (x CreditScore) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreditScore.Descriptor instead.
func (CreditScore) EnumDescriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{2}
}

type PaymentPlan int32

const (
	PaymentPlan_PAYMENT_PLAN_UNSPECIFIED          PaymentPlan = 0
	PaymentPlan_PAYMENT_PLAN_INSTALLMENT_WITH_EFT PaymentPlan = 1
	PaymentPlan_PAYMENT_PLAN_PAID_IN_FULL         PaymentPlan = 2
)

// Enum value maps for PaymentPlan.
var (
	PaymentPlan_name = map[int32]string{
		0: "PAYMENT_PLAN_UNSPECIFIED",
		1: "PAYMENT_PLAN_INSTALLMENT_WITH_EFT",
		2: "PAYMENT_PLAN_PAID_IN_FULL",
	}
	PaymentPlan_value = map[string]int32{
		"PAYMENT_PLAN_UNSPECIFIED":          0,
		"PAYMENT_PLAN_INSTALLMENT_WITH_EFT": 1,
		"PAYMENT_PLAN_PAID_IN_FULL":         2,
	}
)

func (x PaymentPlan) Enum() *PaymentPlan {
	p := new(PaymentPlan)
	*p = x
	return p
}

func (x PaymentPlan) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentPlan) Descriptor() protoreflect.EnumDescriptor {
	return file_nonfleet_model_program_data_proto_enumTypes[3].Descriptor()
}

func (PaymentPlan) Type() protoreflect.EnumType {
	return &file_nonfleet_model_program_data_proto_enumTypes[3]
}

func (x PaymentPlan) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentPlan.Descriptor instead.
func (PaymentPlan) EnumDescriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{3}
}

type MaxRadiusOfOperation int32

const (
	MaxRadiusOfOperation_MAX_RADIUS_OF_OPERATION_UNSPECIFIED  MaxRadiusOfOperation = 0
	MaxRadiusOfOperation_MAX_RADIUS_OF_OPERATION_50MILES      MaxRadiusOfOperation = 1
	MaxRadiusOfOperation_MAX_RADIUS_OF_OPERATION_100MILES     MaxRadiusOfOperation = 2
	MaxRadiusOfOperation_MAX_RADIUS_OF_OPERATION_200MILES     MaxRadiusOfOperation = 3
	MaxRadiusOfOperation_MAX_RADIUS_OF_OPERATION_300MILES     MaxRadiusOfOperation = 4
	MaxRadiusOfOperation_MAX_RADIUS_OF_OPERATION_500MILES     MaxRadiusOfOperation = 5
	MaxRadiusOfOperation_MAX_RADIUS_OF_OPERATION_999PLUSMILES MaxRadiusOfOperation = 6
)

// Enum value maps for MaxRadiusOfOperation.
var (
	MaxRadiusOfOperation_name = map[int32]string{
		0: "MAX_RADIUS_OF_OPERATION_UNSPECIFIED",
		1: "MAX_RADIUS_OF_OPERATION_50MILES",
		2: "MAX_RADIUS_OF_OPERATION_100MILES",
		3: "MAX_RADIUS_OF_OPERATION_200MILES",
		4: "MAX_RADIUS_OF_OPERATION_300MILES",
		5: "MAX_RADIUS_OF_OPERATION_500MILES",
		6: "MAX_RADIUS_OF_OPERATION_999PLUSMILES",
	}
	MaxRadiusOfOperation_value = map[string]int32{
		"MAX_RADIUS_OF_OPERATION_UNSPECIFIED":  0,
		"MAX_RADIUS_OF_OPERATION_50MILES":      1,
		"MAX_RADIUS_OF_OPERATION_100MILES":     2,
		"MAX_RADIUS_OF_OPERATION_200MILES":     3,
		"MAX_RADIUS_OF_OPERATION_300MILES":     4,
		"MAX_RADIUS_OF_OPERATION_500MILES":     5,
		"MAX_RADIUS_OF_OPERATION_999PLUSMILES": 6,
	}
)

func (x MaxRadiusOfOperation) Enum() *MaxRadiusOfOperation {
	p := new(MaxRadiusOfOperation)
	*p = x
	return p
}

func (x MaxRadiusOfOperation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MaxRadiusOfOperation) Descriptor() protoreflect.EnumDescriptor {
	return file_nonfleet_model_program_data_proto_enumTypes[4].Descriptor()
}

func (MaxRadiusOfOperation) Type() protoreflect.EnumType {
	return &file_nonfleet_model_program_data_proto_enumTypes[4]
}

func (x MaxRadiusOfOperation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MaxRadiusOfOperation.Descriptor instead.
func (MaxRadiusOfOperation) EnumDescriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{4}
}

type VehicleClass int32

const (
	VehicleClass_VEHICLE_CLASS_UNSPECIFIED    VehicleClass = 0
	VehicleClass_VEHICLE_CLASS_AGRICULTURAL   VehicleClass = 1
	VehicleClass_VEHICLE_CLASS_AUTO_HAULER    VehicleClass = 2
	VehicleClass_VEHICLE_CLASS_BULK_COMMODITY VehicleClass = 3
	VehicleClass_VEHICLE_CLASS_DRY_FREIGHT    VehicleClass = 4
	VehicleClass_VEHICLE_CLASS_DUMP           VehicleClass = 5
	VehicleClass_VEHICLE_CLASS_FLATBED        VehicleClass = 6
	VehicleClass_VEHICLE_CLASS_FRONT_LOADER   VehicleClass = 7
	VehicleClass_VEHICLE_CLASS_GARBAGE        VehicleClass = 8
	VehicleClass_VEHICLE_CLASS_LIVESTOCK      VehicleClass = 9
	VehicleClass_VEHICLE_CLASS_LOGGING        VehicleClass = 10
	VehicleClass_VEHICLE_CLASS_LOWBOY         VehicleClass = 11
	VehicleClass_VEHICLE_CLASS_PICKUP_TRUCK   VehicleClass = 12
	VehicleClass_VEHICLE_CLASS_POLE           VehicleClass = 13
	VehicleClass_VEHICLE_CLASS_RAG_TOP        VehicleClass = 14
	VehicleClass_VEHICLE_CLASS_REEFER         VehicleClass = 15
	VehicleClass_VEHICLE_CLASS_STAKE          VehicleClass = 16
	VehicleClass_VEHICLE_CLASS_STRAIGHT       VehicleClass = 17
	VehicleClass_VEHICLE_CLASS_TANK           VehicleClass = 18
	VehicleClass_VEHICLE_CLASS_TILT           VehicleClass = 19
	VehicleClass_VEHICLE_CLASS_TRUCK_TRACTOR  VehicleClass = 20
	VehicleClass_VEHICLE_CLASS_UTILITY        VehicleClass = 21
	VehicleClass_VEHICLE_CLASS_UNIDENTIFIED   VehicleClass = 22
	VehicleClass_VEHICLE_CLASS_OTHER          VehicleClass = 23
)

// Enum value maps for VehicleClass.
var (
	VehicleClass_name = map[int32]string{
		0:  "VEHICLE_CLASS_UNSPECIFIED",
		1:  "VEHICLE_CLASS_AGRICULTURAL",
		2:  "VEHICLE_CLASS_AUTO_HAULER",
		3:  "VEHICLE_CLASS_BULK_COMMODITY",
		4:  "VEHICLE_CLASS_DRY_FREIGHT",
		5:  "VEHICLE_CLASS_DUMP",
		6:  "VEHICLE_CLASS_FLATBED",
		7:  "VEHICLE_CLASS_FRONT_LOADER",
		8:  "VEHICLE_CLASS_GARBAGE",
		9:  "VEHICLE_CLASS_LIVESTOCK",
		10: "VEHICLE_CLASS_LOGGING",
		11: "VEHICLE_CLASS_LOWBOY",
		12: "VEHICLE_CLASS_PICKUP_TRUCK",
		13: "VEHICLE_CLASS_POLE",
		14: "VEHICLE_CLASS_RAG_TOP",
		15: "VEHICLE_CLASS_REEFER",
		16: "VEHICLE_CLASS_STAKE",
		17: "VEHICLE_CLASS_STRAIGHT",
		18: "VEHICLE_CLASS_TANK",
		19: "VEHICLE_CLASS_TILT",
		20: "VEHICLE_CLASS_TRUCK_TRACTOR",
		21: "VEHICLE_CLASS_UTILITY",
		22: "VEHICLE_CLASS_UNIDENTIFIED",
		23: "VEHICLE_CLASS_OTHER",
	}
	VehicleClass_value = map[string]int32{
		"VEHICLE_CLASS_UNSPECIFIED":    0,
		"VEHICLE_CLASS_AGRICULTURAL":   1,
		"VEHICLE_CLASS_AUTO_HAULER":    2,
		"VEHICLE_CLASS_BULK_COMMODITY": 3,
		"VEHICLE_CLASS_DRY_FREIGHT":    4,
		"VEHICLE_CLASS_DUMP":           5,
		"VEHICLE_CLASS_FLATBED":        6,
		"VEHICLE_CLASS_FRONT_LOADER":   7,
		"VEHICLE_CLASS_GARBAGE":        8,
		"VEHICLE_CLASS_LIVESTOCK":      9,
		"VEHICLE_CLASS_LOGGING":        10,
		"VEHICLE_CLASS_LOWBOY":         11,
		"VEHICLE_CLASS_PICKUP_TRUCK":   12,
		"VEHICLE_CLASS_POLE":           13,
		"VEHICLE_CLASS_RAG_TOP":        14,
		"VEHICLE_CLASS_REEFER":         15,
		"VEHICLE_CLASS_STAKE":          16,
		"VEHICLE_CLASS_STRAIGHT":       17,
		"VEHICLE_CLASS_TANK":           18,
		"VEHICLE_CLASS_TILT":           19,
		"VEHICLE_CLASS_TRUCK_TRACTOR":  20,
		"VEHICLE_CLASS_UTILITY":        21,
		"VEHICLE_CLASS_UNIDENTIFIED":   22,
		"VEHICLE_CLASS_OTHER":          23,
	}
)

func (x VehicleClass) Enum() *VehicleClass {
	p := new(VehicleClass)
	*p = x
	return p
}

func (x VehicleClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleClass) Descriptor() protoreflect.EnumDescriptor {
	return file_nonfleet_model_program_data_proto_enumTypes[5].Descriptor()
}

func (VehicleClass) Type() protoreflect.EnumType {
	return &file_nonfleet_model_program_data_proto_enumTypes[5]
}

func (x VehicleClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleClass.Descriptor instead.
func (VehicleClass) EnumDescriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{5}
}

type VehicleType int32

const (
	VehicleType_VEHICLE_TYPE_UNSPECIFIED            VehicleType = 0
	VehicleType_VEHICLE_TYPE_TRUCK                  VehicleType = 1
	VehicleType_VEHICLE_TYPE_TRACTOR                VehicleType = 2
	VehicleType_VEHICLE_TYPE_TRAILER                VehicleType = 3
	VehicleType_VEHICLE_TYPE_SEMI_TRAILER           VehicleType = 4
	VehicleType_VEHICLE_TYPE_PICKUP                 VehicleType = 5
	VehicleType_VEHICLE_TYPE_SPARE_SEMI_TRAILER     VehicleType = 6
	VehicleType_VEHICLE_TYPE_NON_OWNED_SEMI_TRAILER VehicleType = 7
)

// Enum value maps for VehicleType.
var (
	VehicleType_name = map[int32]string{
		0: "VEHICLE_TYPE_UNSPECIFIED",
		1: "VEHICLE_TYPE_TRUCK",
		2: "VEHICLE_TYPE_TRACTOR",
		3: "VEHICLE_TYPE_TRAILER",
		4: "VEHICLE_TYPE_SEMI_TRAILER",
		5: "VEHICLE_TYPE_PICKUP",
		6: "VEHICLE_TYPE_SPARE_SEMI_TRAILER",
		7: "VEHICLE_TYPE_NON_OWNED_SEMI_TRAILER",
	}
	VehicleType_value = map[string]int32{
		"VEHICLE_TYPE_UNSPECIFIED":            0,
		"VEHICLE_TYPE_TRUCK":                  1,
		"VEHICLE_TYPE_TRACTOR":                2,
		"VEHICLE_TYPE_TRAILER":                3,
		"VEHICLE_TYPE_SEMI_TRAILER":           4,
		"VEHICLE_TYPE_PICKUP":                 5,
		"VEHICLE_TYPE_SPARE_SEMI_TRAILER":     6,
		"VEHICLE_TYPE_NON_OWNED_SEMI_TRAILER": 7,
	}
)

func (x VehicleType) Enum() *VehicleType {
	p := new(VehicleType)
	*p = x
	return p
}

func (x VehicleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleType) Descriptor() protoreflect.EnumDescriptor {
	return file_nonfleet_model_program_data_proto_enumTypes[6].Descriptor()
}

func (VehicleType) Type() protoreflect.EnumType {
	return &file_nonfleet_model_program_data_proto_enumTypes[6]
}

func (x VehicleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleType.Descriptor instead.
func (VehicleType) EnumDescriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{6}
}

type VehicleWeightClass int32

const (
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_UNSPECIFIED VehicleWeightClass = 0
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_A           VehicleWeightClass = 1
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_B           VehicleWeightClass = 2
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_C           VehicleWeightClass = 3
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_D           VehicleWeightClass = 4
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_E           VehicleWeightClass = 5
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_F           VehicleWeightClass = 6
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_G           VehicleWeightClass = 7
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_H           VehicleWeightClass = 8
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_3           VehicleWeightClass = 9
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_4           VehicleWeightClass = 10
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_5           VehicleWeightClass = 11
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_6           VehicleWeightClass = 12
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_7           VehicleWeightClass = 13
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_8           VehicleWeightClass = 14
)

// Enum value maps for VehicleWeightClass.
var (
	VehicleWeightClass_name = map[int32]string{
		0:  "VEHICLE_WEIGHT_CLASS_UNSPECIFIED",
		1:  "VEHICLE_WEIGHT_CLASS_A",
		2:  "VEHICLE_WEIGHT_CLASS_B",
		3:  "VEHICLE_WEIGHT_CLASS_C",
		4:  "VEHICLE_WEIGHT_CLASS_D",
		5:  "VEHICLE_WEIGHT_CLASS_E",
		6:  "VEHICLE_WEIGHT_CLASS_F",
		7:  "VEHICLE_WEIGHT_CLASS_G",
		8:  "VEHICLE_WEIGHT_CLASS_H",
		9:  "VEHICLE_WEIGHT_CLASS_3",
		10: "VEHICLE_WEIGHT_CLASS_4",
		11: "VEHICLE_WEIGHT_CLASS_5",
		12: "VEHICLE_WEIGHT_CLASS_6",
		13: "VEHICLE_WEIGHT_CLASS_7",
		14: "VEHICLE_WEIGHT_CLASS_8",
	}
	VehicleWeightClass_value = map[string]int32{
		"VEHICLE_WEIGHT_CLASS_UNSPECIFIED": 0,
		"VEHICLE_WEIGHT_CLASS_A":           1,
		"VEHICLE_WEIGHT_CLASS_B":           2,
		"VEHICLE_WEIGHT_CLASS_C":           3,
		"VEHICLE_WEIGHT_CLASS_D":           4,
		"VEHICLE_WEIGHT_CLASS_E":           5,
		"VEHICLE_WEIGHT_CLASS_F":           6,
		"VEHICLE_WEIGHT_CLASS_G":           7,
		"VEHICLE_WEIGHT_CLASS_H":           8,
		"VEHICLE_WEIGHT_CLASS_3":           9,
		"VEHICLE_WEIGHT_CLASS_4":           10,
		"VEHICLE_WEIGHT_CLASS_5":           11,
		"VEHICLE_WEIGHT_CLASS_6":           12,
		"VEHICLE_WEIGHT_CLASS_7":           13,
		"VEHICLE_WEIGHT_CLASS_8":           14,
	}
)

func (x VehicleWeightClass) Enum() *VehicleWeightClass {
	p := new(VehicleWeightClass)
	*p = x
	return p
}

func (x VehicleWeightClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleWeightClass) Descriptor() protoreflect.EnumDescriptor {
	return file_nonfleet_model_program_data_proto_enumTypes[7].Descriptor()
}

func (VehicleWeightClass) Type() protoreflect.EnumType {
	return &file_nonfleet_model_program_data_proto_enumTypes[7]
}

func (x VehicleWeightClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleWeightClass.Descriptor instead.
func (VehicleWeightClass) EnumDescriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{7}
}

type NFAdmittedProgramDataV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Operations       *NFAdmittedOperationDataV1        `protobuf:"bytes,1,opt,name=operations,proto3" json:"operations,omitempty"`
	Drivers          []*NFAdmittedDriverDataV1         `protobuf:"bytes,2,rep,name=drivers,proto3" json:"drivers,omitempty"`
	Vehicles         []*NFAdmittedVehicleDataV1        `protobuf:"bytes,3,rep,name=vehicles,proto3" json:"vehicles,omitempty"`
	CommodityDetails *NFAdmittedCommodityDetailsV1     `protobuf:"bytes,4,opt,name=commodityDetails,proto3" json:"commodityDetails,omitempty"`
	UnderwriterInput *NFAdmittedUnderwriterInputDataV1 `protobuf:"bytes,6,opt,name=underwriterInput,proto3" json:"underwriterInput,omitempty"`
	CompanyInfo      *NFCompanyInfoV1                  `protobuf:"bytes,7,opt,name=companyInfo,proto3" json:"companyInfo,omitempty"`
}

func (x *NFAdmittedProgramDataV1) Reset() {
	*x = NFAdmittedProgramDataV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_program_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFAdmittedProgramDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFAdmittedProgramDataV1) ProtoMessage() {}

func (x *NFAdmittedProgramDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_program_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFAdmittedProgramDataV1.ProtoReflect.Descriptor instead.
func (*NFAdmittedProgramDataV1) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{0}
}

func (x *NFAdmittedProgramDataV1) GetOperations() *NFAdmittedOperationDataV1 {
	if x != nil {
		return x.Operations
	}
	return nil
}

func (x *NFAdmittedProgramDataV1) GetDrivers() []*NFAdmittedDriverDataV1 {
	if x != nil {
		return x.Drivers
	}
	return nil
}

func (x *NFAdmittedProgramDataV1) GetVehicles() []*NFAdmittedVehicleDataV1 {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

func (x *NFAdmittedProgramDataV1) GetCommodityDetails() *NFAdmittedCommodityDetailsV1 {
	if x != nil {
		return x.CommodityDetails
	}
	return nil
}

func (x *NFAdmittedProgramDataV1) GetUnderwriterInput() *NFAdmittedUnderwriterInputDataV1 {
	if x != nil {
		return x.UnderwriterInput
	}
	return nil
}

func (x *NFAdmittedProgramDataV1) GetCompanyInfo() *NFCompanyInfoV1 {
	if x != nil {
		return x.CompanyInfo
	}
	return nil
}

type NFAdmittedCommodityDetailsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Commodities      []*NFAdmittedCommodityDataV1 `protobuf:"bytes,1,rep,name=commodities,proto3" json:"commodities,omitempty"`
	PrimaryCategory  string                       `protobuf:"bytes,2,opt,name=primaryCategory,proto3" json:"primaryCategory,omitempty"`
	PrimaryCommodity CommodityCategory            `protobuf:"varint,3,opt,name=primaryCommodity,proto3,enum=nonfleet_model.CommodityCategory" json:"primaryCommodity,omitempty"`
}

func (x *NFAdmittedCommodityDetailsV1) Reset() {
	*x = NFAdmittedCommodityDetailsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_program_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFAdmittedCommodityDetailsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFAdmittedCommodityDetailsV1) ProtoMessage() {}

func (x *NFAdmittedCommodityDetailsV1) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_program_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFAdmittedCommodityDetailsV1.ProtoReflect.Descriptor instead.
func (*NFAdmittedCommodityDetailsV1) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{1}
}

func (x *NFAdmittedCommodityDetailsV1) GetCommodities() []*NFAdmittedCommodityDataV1 {
	if x != nil {
		return x.Commodities
	}
	return nil
}

func (x *NFAdmittedCommodityDetailsV1) GetPrimaryCategory() string {
	if x != nil {
		return x.PrimaryCategory
	}
	return ""
}

func (x *NFAdmittedCommodityDetailsV1) GetPrimaryCommodity() CommodityCategory {
	if x != nil {
		return x.PrimaryCommodity
	}
	return CommodityCategory_COMMODITY_CATEGORY_UNSPECIFIED
}

type NFAdmittedUnderwriterInputDataV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UsDotScore   USDOTScore                   `protobuf:"varint,1,opt,name=usDotScore,proto3,enum=nonfleet_model.USDOTScore" json:"usDotScore,omitempty"`
	CreditScore  CreditScore                  `protobuf:"varint,2,opt,name=creditScore,proto3,enum=nonfleet_model.CreditScore" json:"creditScore,omitempty"`
	PaymentPlan  PaymentPlan                  `protobuf:"varint,3,opt,name=paymentPlan,proto3,enum=nonfleet_model.PaymentPlan" json:"paymentPlan,omitempty"`
	ScheduleMods *NFAdmittedScheduleModDataV1 `protobuf:"bytes,4,opt,name=scheduleMods,proto3" json:"scheduleMods,omitempty"`
}

func (x *NFAdmittedUnderwriterInputDataV1) Reset() {
	*x = NFAdmittedUnderwriterInputDataV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_program_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFAdmittedUnderwriterInputDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFAdmittedUnderwriterInputDataV1) ProtoMessage() {}

func (x *NFAdmittedUnderwriterInputDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_program_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFAdmittedUnderwriterInputDataV1.ProtoReflect.Descriptor instead.
func (*NFAdmittedUnderwriterInputDataV1) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{2}
}

func (x *NFAdmittedUnderwriterInputDataV1) GetUsDotScore() USDOTScore {
	if x != nil {
		return x.UsDotScore
	}
	return USDOTScore_US_DOT_SCORE_UNSPECIFIED
}

func (x *NFAdmittedUnderwriterInputDataV1) GetCreditScore() CreditScore {
	if x != nil {
		return x.CreditScore
	}
	return CreditScore_CREDIT_SCORE_UNSPECIFIED
}

func (x *NFAdmittedUnderwriterInputDataV1) GetPaymentPlan() PaymentPlan {
	if x != nil {
		return x.PaymentPlan
	}
	return PaymentPlan_PAYMENT_PLAN_UNSPECIFIED
}

func (x *NFAdmittedUnderwriterInputDataV1) GetScheduleMods() *NFAdmittedScheduleModDataV1 {
	if x != nil {
		return x.ScheduleMods
	}
	return nil
}

type NFAdmittedScheduleModDataV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LiabScheduleMod float64 `protobuf:"fixed64,1,opt,name=liabScheduleMod,proto3" json:"liabScheduleMod,omitempty"`
	ApdScheduleMod  float64 `protobuf:"fixed64,2,opt,name=apdScheduleMod,proto3" json:"apdScheduleMod,omitempty"`
	GlScheduleMod   float64 `protobuf:"fixed64,3,opt,name=glScheduleMod,proto3" json:"glScheduleMod,omitempty"`
	MtcScheduleMod  float64 `protobuf:"fixed64,4,opt,name=mtcScheduleMod,proto3" json:"mtcScheduleMod,omitempty"`
	AllCovSafetyMod float64 `protobuf:"fixed64,5,opt,name=allCovSafetyMod,proto3" json:"allCovSafetyMod,omitempty"`
}

func (x *NFAdmittedScheduleModDataV1) Reset() {
	*x = NFAdmittedScheduleModDataV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_program_data_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFAdmittedScheduleModDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFAdmittedScheduleModDataV1) ProtoMessage() {}

func (x *NFAdmittedScheduleModDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_program_data_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFAdmittedScheduleModDataV1.ProtoReflect.Descriptor instead.
func (*NFAdmittedScheduleModDataV1) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{3}
}

func (x *NFAdmittedScheduleModDataV1) GetLiabScheduleMod() float64 {
	if x != nil {
		return x.LiabScheduleMod
	}
	return 0
}

func (x *NFAdmittedScheduleModDataV1) GetApdScheduleMod() float64 {
	if x != nil {
		return x.ApdScheduleMod
	}
	return 0
}

func (x *NFAdmittedScheduleModDataV1) GetGlScheduleMod() float64 {
	if x != nil {
		return x.GlScheduleMod
	}
	return 0
}

func (x *NFAdmittedScheduleModDataV1) GetMtcScheduleMod() float64 {
	if x != nil {
		return x.MtcScheduleMod
	}
	return 0
}

func (x *NFAdmittedScheduleModDataV1) GetAllCovSafetyMod() float64 {
	if x != nil {
		return x.AllCovSafetyMod
	}
	return 0
}

type NFAdmittedOperationDataV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxRadiusOfOperation MaxRadiusOfOperation `protobuf:"varint,1,opt,name=maxRadiusOfOperation,proto3,enum=nonfleet_model.MaxRadiusOfOperation" json:"maxRadiusOfOperation,omitempty"`
	NumberOfPowerUnits   int64                `protobuf:"varint,2,opt,name=numberOfPowerUnits,proto3" json:"numberOfPowerUnits,omitempty"`
}

func (x *NFAdmittedOperationDataV1) Reset() {
	*x = NFAdmittedOperationDataV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_program_data_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFAdmittedOperationDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFAdmittedOperationDataV1) ProtoMessage() {}

func (x *NFAdmittedOperationDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_program_data_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFAdmittedOperationDataV1.ProtoReflect.Descriptor instead.
func (*NFAdmittedOperationDataV1) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{4}
}

func (x *NFAdmittedOperationDataV1) GetMaxRadiusOfOperation() MaxRadiusOfOperation {
	if x != nil {
		return x.MaxRadiusOfOperation
	}
	return MaxRadiusOfOperation_MAX_RADIUS_OF_OPERATION_UNSPECIFIED
}

func (x *NFAdmittedOperationDataV1) GetNumberOfPowerUnits() int64 {
	if x != nil {
		return x.NumberOfPowerUnits
	}
	return 0
}

type NFAdmittedDriverDataV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstName                 string                     `protobuf:"bytes,1,opt,name=firstName,proto3" json:"firstName,omitempty"`
	LastName                  string                     `protobuf:"bytes,2,opt,name=lastName,proto3" json:"lastName,omitempty"`
	DateOfBirth               *timestamppb.Timestamp     `protobuf:"bytes,3,opt,name=dateOfBirth,proto3" json:"dateOfBirth,omitempty"`
	DateOfHire                *timestamppb.Timestamp     `protobuf:"bytes,4,opt,name=dateOfHire,proto3" json:"dateOfHire,omitempty"`
	YearsOfExperience         int32                      `protobuf:"varint,5,opt,name=yearsOfExperience,proto3" json:"yearsOfExperience,omitempty"`
	LicenseNumber             string                     `protobuf:"bytes,6,opt,name=licenseNumber,proto3" json:"licenseNumber,omitempty"`
	LicenseState              string                     `protobuf:"bytes,7,opt,name=licenseState,proto3" json:"licenseState,omitempty"`
	IsOutOfState              bool                       `protobuf:"varint,8,opt,name=isOutOfState,proto3" json:"isOutOfState,omitempty"`
	IsIncludedInPolicy        bool                       `protobuf:"varint,9,opt,name=isIncludedInPolicy,proto3" json:"isIncludedInPolicy,omitempty"`
	HasViolationsInLast3Years bool                       `protobuf:"varint,10,opt,name=hasViolationsInLast3Years,proto3" json:"hasViolationsInLast3Years,omitempty"`
	Violations                *NFAdmittedViolationDataV1 `protobuf:"bytes,11,opt,name=violations,proto3" json:"violations,omitempty"`
}

func (x *NFAdmittedDriverDataV1) Reset() {
	*x = NFAdmittedDriverDataV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_program_data_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFAdmittedDriverDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFAdmittedDriverDataV1) ProtoMessage() {}

func (x *NFAdmittedDriverDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_program_data_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFAdmittedDriverDataV1.ProtoReflect.Descriptor instead.
func (*NFAdmittedDriverDataV1) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{5}
}

func (x *NFAdmittedDriverDataV1) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *NFAdmittedDriverDataV1) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *NFAdmittedDriverDataV1) GetDateOfBirth() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *NFAdmittedDriverDataV1) GetDateOfHire() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfHire
	}
	return nil
}

func (x *NFAdmittedDriverDataV1) GetYearsOfExperience() int32 {
	if x != nil {
		return x.YearsOfExperience
	}
	return 0
}

func (x *NFAdmittedDriverDataV1) GetLicenseNumber() string {
	if x != nil {
		return x.LicenseNumber
	}
	return ""
}

func (x *NFAdmittedDriverDataV1) GetLicenseState() string {
	if x != nil {
		return x.LicenseState
	}
	return ""
}

func (x *NFAdmittedDriverDataV1) GetIsOutOfState() bool {
	if x != nil {
		return x.IsOutOfState
	}
	return false
}

func (x *NFAdmittedDriverDataV1) GetIsIncludedInPolicy() bool {
	if x != nil {
		return x.IsIncludedInPolicy
	}
	return false
}

func (x *NFAdmittedDriverDataV1) GetHasViolationsInLast3Years() bool {
	if x != nil {
		return x.HasViolationsInLast3Years
	}
	return false
}

func (x *NFAdmittedDriverDataV1) GetViolations() *NFAdmittedViolationDataV1 {
	if x != nil {
		return x.Violations
	}
	return nil
}

type NFAdmittedViolationDataV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ViolationPoints int32            `protobuf:"varint,1,opt,name=violationPoints,proto3" json:"violationPoints,omitempty"`
	ClassCounts     map[string]int32 `protobuf:"bytes,2,rep,name=classCounts,proto3" json:"classCounts,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *NFAdmittedViolationDataV1) Reset() {
	*x = NFAdmittedViolationDataV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_program_data_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFAdmittedViolationDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFAdmittedViolationDataV1) ProtoMessage() {}

func (x *NFAdmittedViolationDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_program_data_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFAdmittedViolationDataV1.ProtoReflect.Descriptor instead.
func (*NFAdmittedViolationDataV1) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{6}
}

func (x *NFAdmittedViolationDataV1) GetViolationPoints() int32 {
	if x != nil {
		return x.ViolationPoints
	}
	return 0
}

func (x *NFAdmittedViolationDataV1) GetClassCounts() map[string]int32 {
	if x != nil {
		return x.ClassCounts
	}
	return nil
}

type NFAdmittedVehicleDataV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin                string             `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	Make               string             `protobuf:"bytes,2,opt,name=make,proto3" json:"make,omitempty"`
	Model              string             `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`
	Year               int32              `protobuf:"varint,4,opt,name=year,proto3" json:"year,omitempty"`
	VehicleClass       VehicleClass       `protobuf:"varint,5,opt,name=vehicleClass,proto3,enum=nonfleet_model.VehicleClass" json:"vehicleClass,omitempty"`
	VehicleType        VehicleType        `protobuf:"varint,6,opt,name=vehicleType,proto3,enum=nonfleet_model.VehicleType" json:"vehicleType,omitempty"`
	VehicleWeightClass VehicleWeightClass `protobuf:"varint,7,opt,name=vehicleWeightClass,proto3,enum=nonfleet_model.VehicleWeightClass" json:"vehicleWeightClass,omitempty"`
	StatedValue        *float64           `protobuf:"fixed64,8,opt,name=statedValue,proto3,oneof" json:"statedValue,omitempty"`
	LossPayee          *proto.Insured     `protobuf:"bytes,9,opt,name=lossPayee,proto3,oneof" json:"lossPayee,omitempty"`
}

func (x *NFAdmittedVehicleDataV1) Reset() {
	*x = NFAdmittedVehicleDataV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_program_data_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFAdmittedVehicleDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFAdmittedVehicleDataV1) ProtoMessage() {}

func (x *NFAdmittedVehicleDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_program_data_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFAdmittedVehicleDataV1.ProtoReflect.Descriptor instead.
func (*NFAdmittedVehicleDataV1) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{7}
}

func (x *NFAdmittedVehicleDataV1) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *NFAdmittedVehicleDataV1) GetMake() string {
	if x != nil {
		return x.Make
	}
	return ""
}

func (x *NFAdmittedVehicleDataV1) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *NFAdmittedVehicleDataV1) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *NFAdmittedVehicleDataV1) GetVehicleClass() VehicleClass {
	if x != nil {
		return x.VehicleClass
	}
	return VehicleClass_VEHICLE_CLASS_UNSPECIFIED
}

func (x *NFAdmittedVehicleDataV1) GetVehicleType() VehicleType {
	if x != nil {
		return x.VehicleType
	}
	return VehicleType_VEHICLE_TYPE_UNSPECIFIED
}

func (x *NFAdmittedVehicleDataV1) GetVehicleWeightClass() VehicleWeightClass {
	if x != nil {
		return x.VehicleWeightClass
	}
	return VehicleWeightClass_VEHICLE_WEIGHT_CLASS_UNSPECIFIED
}

func (x *NFAdmittedVehicleDataV1) GetStatedValue() float64 {
	if x != nil && x.StatedValue != nil {
		return *x.StatedValue
	}
	return 0
}

func (x *NFAdmittedVehicleDataV1) GetLossPayee() *proto.Insured {
	if x != nil {
		return x.LossPayee
	}
	return nil
}

type NFAdmittedCommodityDataV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryType string            `protobuf:"bytes,1,opt,name=categoryType,proto3" json:"categoryType,omitempty"`
	CategoryName string            `protobuf:"bytes,2,opt,name=categoryName,proto3" json:"categoryName,omitempty"`
	Commodity    CommodityCategory `protobuf:"varint,3,opt,name=commodity,proto3,enum=nonfleet_model.CommodityCategory" json:"commodity,omitempty"`
	Percentage   float32           `protobuf:"fixed32,4,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *NFAdmittedCommodityDataV1) Reset() {
	*x = NFAdmittedCommodityDataV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_program_data_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFAdmittedCommodityDataV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFAdmittedCommodityDataV1) ProtoMessage() {}

func (x *NFAdmittedCommodityDataV1) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_program_data_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFAdmittedCommodityDataV1.ProtoReflect.Descriptor instead.
func (*NFAdmittedCommodityDataV1) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{8}
}

func (x *NFAdmittedCommodityDataV1) GetCategoryType() string {
	if x != nil {
		return x.CategoryType
	}
	return ""
}

func (x *NFAdmittedCommodityDataV1) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *NFAdmittedCommodityDataV1) GetCommodity() CommodityCategory {
	if x != nil {
		return x.Commodity
	}
	return CommodityCategory_COMMODITY_CATEGORY_UNSPECIFIED
}

func (x *NFAdmittedCommodityDataV1) GetPercentage() float32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type NFCompanyInfoV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name             string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DotNumber        int32              `protobuf:"varint,2,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	UsState          string             `protobuf:"bytes,3,opt,name=usState,proto3" json:"usState,omitempty"`
	BusinessOwner    *NFBusinessOwnerV1 `protobuf:"bytes,4,opt,name=businessOwner,proto3" json:"businessOwner,omitempty"`
	TerminalAddress  *proto1.Address    `protobuf:"bytes,5,opt,name=terminalAddress,proto3" json:"terminalAddress,omitempty"`
	MailingAddress   *proto1.Address    `protobuf:"bytes,6,opt,name=mailingAddress,proto3" json:"mailingAddress,omitempty"`
	AnnualCostOfHire *int64             `protobuf:"varint,7,opt,name=annualCostOfHire,proto3,oneof" json:"annualCostOfHire,omitempty"`
}

func (x *NFCompanyInfoV1) Reset() {
	*x = NFCompanyInfoV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_program_data_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFCompanyInfoV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFCompanyInfoV1) ProtoMessage() {}

func (x *NFCompanyInfoV1) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_program_data_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFCompanyInfoV1.ProtoReflect.Descriptor instead.
func (*NFCompanyInfoV1) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{9}
}

func (x *NFCompanyInfoV1) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NFCompanyInfoV1) GetDotNumber() int32 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *NFCompanyInfoV1) GetUsState() string {
	if x != nil {
		return x.UsState
	}
	return ""
}

func (x *NFCompanyInfoV1) GetBusinessOwner() *NFBusinessOwnerV1 {
	if x != nil {
		return x.BusinessOwner
	}
	return nil
}

func (x *NFCompanyInfoV1) GetTerminalAddress() *proto1.Address {
	if x != nil {
		return x.TerminalAddress
	}
	return nil
}

func (x *NFCompanyInfoV1) GetMailingAddress() *proto1.Address {
	if x != nil {
		return x.MailingAddress
	}
	return nil
}

func (x *NFCompanyInfoV1) GetAnnualCostOfHire() int64 {
	if x != nil && x.AnnualCostOfHire != nil {
		return *x.AnnualCostOfHire
	}
	return 0
}

type NFBusinessOwnerV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstName            string                 `protobuf:"bytes,1,opt,name=firstName,proto3" json:"firstName,omitempty"`
	LastName             string                 `protobuf:"bytes,2,opt,name=lastName,proto3" json:"lastName,omitempty"`
	DateOfBirth          *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=dateOfBirth,proto3" json:"dateOfBirth,omitempty"`
	Address              *proto1.Address        `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	DriverOnPolicy       bool                   `protobuf:"varint,5,opt,name=driverOnPolicy,proto3" json:"driverOnPolicy,omitempty"`
	EncryptedSSN         []byte                 `protobuf:"bytes,6,opt,name=encryptedSSN,proto3,oneof" json:"encryptedSSN,omitempty"`
	EncryptedSSNLastFour []byte                 `protobuf:"bytes,7,opt,name=encryptedSSNLastFour,proto3,oneof" json:"encryptedSSNLastFour,omitempty"`
}

func (x *NFBusinessOwnerV1) Reset() {
	*x = NFBusinessOwnerV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_nonfleet_model_program_data_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFBusinessOwnerV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFBusinessOwnerV1) ProtoMessage() {}

func (x *NFBusinessOwnerV1) ProtoReflect() protoreflect.Message {
	mi := &file_nonfleet_model_program_data_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFBusinessOwnerV1.ProtoReflect.Descriptor instead.
func (*NFBusinessOwnerV1) Descriptor() ([]byte, []int) {
	return file_nonfleet_model_program_data_proto_rawDescGZIP(), []int{10}
}

func (x *NFBusinessOwnerV1) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *NFBusinessOwnerV1) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *NFBusinessOwnerV1) GetDateOfBirth() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *NFBusinessOwnerV1) GetAddress() *proto1.Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *NFBusinessOwnerV1) GetDriverOnPolicy() bool {
	if x != nil {
		return x.DriverOnPolicy
	}
	return false
}

func (x *NFBusinessOwnerV1) GetEncryptedSSN() []byte {
	if x != nil {
		return x.EncryptedSSN
	}
	return nil
}

func (x *NFBusinessOwnerV1) GetEncryptedSSNLastFour() []byte {
	if x != nil {
		return x.EncryptedSSNLastFour
	}
	return nil
}

var File_nonfleet_model_program_data_proto protoreflect.FileDescriptor

var file_nonfleet_model_program_data_proto_rawDesc = []byte{
	0x0a, 0x21, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x69, 0x6e, 0x73, 0x75,
	0x72, 0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe6, 0x03, 0x0a, 0x17, 0x4e, 0x46,
	0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x44,
	0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x49, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6e, 0x6f, 0x6e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x41, 0x64, 0x6d,
	0x69, 0x74, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x56, 0x31, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x40, 0x0a, 0x07, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x07, 0x64, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x73, 0x12, 0x43, 0x0a, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x08, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x12, 0x58, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x64, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x56, 0x31, 0x52,
	0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x5c, 0x0a, 0x10, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6e, 0x6f,
	0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x41,
	0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74,
	0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x10, 0x75,
	0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12,
	0x41, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x56, 0x31, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0xe4, 0x01, 0x0a, 0x1c, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65,
	0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x56, 0x31, 0x12, 0x4b, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69,
	0x74, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74,
	0x61, 0x56, 0x31, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x28, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x4d, 0x0a, 0x10, 0x70, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x10, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x22, 0xad, 0x02, 0x0a, 0x20, 0x4e, 0x46,
	0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x3a,
	0x0a, 0x0a, 0x75, 0x73, 0x44, 0x6f, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x55, 0x53, 0x44, 0x4f, 0x54, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x0a,
	0x75, 0x73, 0x44, 0x6f, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x3d, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x0b, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x3d, 0x0a, 0x0b, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x0b, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x4f, 0x0a, 0x0c, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x52, 0x0c, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x73, 0x22, 0xe7, 0x01, 0x0a, 0x1b, 0x4e, 0x46,
	0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4d, 0x6f, 0x64, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x28, 0x0a, 0x0f, 0x6c, 0x69, 0x61,
	0x62, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0f, 0x6c, 0x69, 0x61, 0x62, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4d, 0x6f, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x70, 0x64, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x61, 0x70, 0x64,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x67,
	0x6c, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0d, 0x67, 0x6c, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f,
	0x64, 0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x74, 0x63, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4d, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6d, 0x74, 0x63, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x6c, 0x6c,
	0x43, 0x6f, 0x76, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0f, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x76, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79,
	0x4d, 0x6f, 0x64, 0x22, 0xa5, 0x01, 0x0a, 0x19, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74,
	0x65, 0x64, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x56,
	0x31, 0x12, 0x58, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x4d, 0x61, 0x78, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x6d, 0x61, 0x78, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73,
	0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f,
	0x66, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x22, 0xa1, 0x04, 0x0a, 0x16,
	0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x3c, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x3a,
	0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x79, 0x65,
	0x61, 0x72, 0x73, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x79, 0x65, 0x61, 0x72, 0x73, 0x4f, 0x66, 0x45, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x69, 0x63, 0x65,
	0x6e, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x22,
	0x0a, 0x0c, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x4f, 0x75, 0x74, 0x4f, 0x66, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x4f, 0x75, 0x74, 0x4f,
	0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x69, 0x73, 0x49, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x64, 0x49, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x49, 0x6e,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x3c, 0x0a, 0x19, 0x68, 0x61, 0x73, 0x56, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x33, 0x59, 0x65,
	0x61, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x68, 0x61, 0x73, 0x56, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x33, 0x59,
	0x65, 0x61, 0x72, 0x73, 0x12, 0x49, 0x0a, 0x0a, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69,
	0x74, 0x74, 0x65, 0x64, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x56, 0x31, 0x52, 0x0a, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0xe3, 0x01, 0x0a, 0x19, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x56, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x28, 0x0a,
	0x0f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x5c, 0x0a, 0x0b, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6e,
	0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x46,
	0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x56, 0x31, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbf, 0x03, 0x0a, 0x17, 0x4e, 0x46, 0x41, 0x64, 0x6d, 0x69,
	0x74, 0x74, 0x65, 0x64, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x56,
	0x31, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x76, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x12, 0x0a,
	0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x79, 0x65, 0x61,
	0x72, 0x12, 0x40, 0x0a, 0x0c, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x12, 0x3d, 0x0a, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x52, 0x0a, 0x12, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x57, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22,
	0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x52, 0x12, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x64,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a,
	0x09, 0x6c, 0x6f, 0x73, 0x73, 0x50, 0x61, 0x79, 0x65, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x48, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x73,
	0x73, 0x50, 0x61, 0x79, 0x65, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6c, 0x6f,
	0x73, 0x73, 0x50, 0x61, 0x79, 0x65, 0x65, 0x22, 0xc4, 0x01, 0x0a, 0x19, 0x4e, 0x46, 0x41, 0x64,
	0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x44,
	0x61, 0x74, 0x61, 0x56, 0x31, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a,
	0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x21, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x12, 0x1e,
	0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22, 0xe0,
	0x02, 0x0a, 0x0f, 0x4e, 0x46, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x56, 0x31, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x47,
	0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x46, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x56, 0x31, 0x52, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0f, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x0f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x37, 0x0a, 0x0e, 0x6d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0e, 0x6d, 0x61, 0x69,
	0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2f, 0x0a, 0x10, 0x61,
	0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x10, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43,
	0x6f, 0x73, 0x74, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11,
	0x5f, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x48, 0x69, 0x72,
	0x65, 0x22, 0xea, 0x02, 0x0a, 0x11, 0x4e, 0x46, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x56, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12,
	0x29, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x4f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x6e, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x27, 0x0a, 0x0c, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x53,
	0x53, 0x4e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x65, 0x64, 0x53, 0x53, 0x4e, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x14, 0x65,
	0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x53, 0x53, 0x4e, 0x4c, 0x61, 0x73, 0x74, 0x46,
	0x6f, 0x75, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x01, 0x52, 0x14, 0x65, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x53, 0x53, 0x4e, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75,
	0x72, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x65, 0x64, 0x53, 0x53, 0x4e, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x65, 0x64, 0x53, 0x53, 0x4e, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x2a, 0xb8,
	0x21, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x42,
	0x41, 0x4b, 0x45, 0x44, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x42, 0x45, 0x45, 0x52, 0x5f, 0x57, 0x49, 0x4e, 0x45, 0x5f, 0x4e, 0x4f, 0x5f,
	0x4c, 0x49, 0x51, 0x55, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x42,
	0x45, 0x56, 0x45, 0x52, 0x41, 0x47, 0x45, 0x53, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x45, 0x44, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x10, 0x04, 0x12,
	0x1c, 0x0a, 0x18, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x44, 0x41, 0x49, 0x52, 0x59, 0x10, 0x05, 0x12, 0x1b, 0x0a,
	0x17, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x45, 0x47, 0x47, 0x53, 0x10, 0x06, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x46, 0x4c, 0x4f, 0x55, 0x52, 0x10, 0x07, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46,
	0x4f, 0x4f, 0x44, 0x5f, 0x46, 0x52, 0x4f, 0x5a, 0x45, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53,
	0x45, 0x41, 0x46, 0x4f, 0x4f, 0x44, 0x10, 0x08, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46,
	0x52, 0x55, 0x49, 0x54, 0x53, 0x10, 0x09, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x45,
	0x41, 0x54, 0x53, 0x5f, 0x44, 0x52, 0x45, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x50, 0x4f, 0x55, 0x4c,
	0x54, 0x52, 0x59, 0x10, 0x0a, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x49, 0x4c, 0x53,
	0x5f, 0x45, 0x44, 0x49, 0x42, 0x4c, 0x45, 0x10, 0x0b, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x53, 0x41, 0x4c, 0x54, 0x10, 0x0c, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x52, 0x45,
	0x53, 0x48, 0x5f, 0x53, 0x45, 0x41, 0x46, 0x4f, 0x4f, 0x44, 0x10, 0x0d, 0x12, 0x25, 0x0a, 0x21,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x46, 0x52, 0x4f, 0x5a, 0x45, 0x4e, 0x5f, 0x53, 0x45, 0x41, 0x46, 0x4f, 0x4f,
	0x44, 0x10, 0x0e, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x45, 0x41, 0x5f, 0x43, 0x4f,
	0x46, 0x46, 0x45, 0x45, 0x5f, 0x53, 0x50, 0x49, 0x43, 0x45, 0x53, 0x10, 0x0f, 0x12, 0x21, 0x0a,
	0x1d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x56, 0x45, 0x47, 0x45, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x53, 0x10, 0x10,
	0x12, 0x2f, 0x0a, 0x2b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x46, 0x4f, 0x4f,
	0x44, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x42, 0x45, 0x56, 0x45, 0x52, 0x41, 0x47, 0x45, 0x53, 0x10,
	0x11, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x54, 0x54, 0x4f, 0x4e, 0x5f, 0x4e,
	0x4f, 0x4e, 0x5f, 0x47, 0x49, 0x4e, 0x4e, 0x45, 0x44, 0x10, 0x12, 0x12, 0x2a, 0x0a, 0x26, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x49, 0x4c, 0x45, 0x53, 0x5f, 0x53, 0x4b, 0x49, 0x4e, 0x53,
	0x5f, 0x46, 0x55, 0x52, 0x53, 0x10, 0x13, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x45,
	0x45, 0x44, 0x5f, 0x46, 0x45, 0x52, 0x54, 0x49, 0x4c, 0x49, 0x5a, 0x45, 0x52, 0x10, 0x14, 0x12,
	0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x47, 0x52, 0x41, 0x49, 0x4e, 0x5f, 0x48, 0x41, 0x59, 0x10,
	0x15, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x55, 0x4c, 0x43, 0x48, 0x5f, 0x54, 0x4f,
	0x50, 0x5f, 0x53, 0x4f, 0x49, 0x4c, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x4c, 0x10,
	0x16, 0x12, 0x3e, 0x0a, 0x3a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x54, 0x53, 0x5f, 0x53,
	0x48, 0x52, 0x55, 0x42, 0x53, 0x5f, 0x54, 0x52, 0x45, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x54, 0x45, 0x4d, 0x50, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x4c, 0x45, 0x44, 0x10,
	0x17, 0x12, 0x3a, 0x0a, 0x36, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x54, 0x53, 0x5f, 0x53,
	0x48, 0x52, 0x55, 0x42, 0x53, 0x5f, 0x54, 0x52, 0x45, 0x45, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x18, 0x12, 0x1c, 0x0a,
	0x18, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x45, 0x44, 0x53, 0x10, 0x19, 0x12, 0x30, 0x0a, 0x2c, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x52, 0x4d, 0x49, 0x4e, 0x47, 0x5f,
	0x41, 0x47, 0x52, 0x49, 0x43, 0x55, 0x4c, 0x54, 0x55, 0x52, 0x45, 0x10, 0x1a, 0x12, 0x20, 0x0a,
	0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x10, 0x1b, 0x12,
	0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x4f, 0x42, 0x41, 0x43, 0x43, 0x4f, 0x5f, 0x4c, 0x45,
	0x41, 0x46, 0x5f, 0x52, 0x41, 0x57, 0x10, 0x1c, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43,
	0x48, 0x45, 0x4d, 0x49, 0x43, 0x41, 0x4c, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x48, 0x41, 0x5a,
	0x41, 0x52, 0x44, 0x4f, 0x55, 0x53, 0x10, 0x1d, 0x12, 0x36, 0x0a, 0x32, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43,
	0x4c, 0x45, 0x41, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x53,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x1e,
	0x12, 0x38, 0x0a, 0x34, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x44, 0x59, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x4b, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x50, 0x41, 0x49, 0x4e, 0x54, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x48,
	0x41, 0x5a, 0x41, 0x52, 0x44, 0x4f, 0x55, 0x53, 0x10, 0x1f, 0x12, 0x3c, 0x0a, 0x38, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x43, 0x48, 0x45,
	0x4d, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x54,
	0x52, 0x4f, 0x4c, 0x45, 0x55, 0x4d, 0x10, 0x20, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x45, 0x4d, 0x49, 0x43, 0x41, 0x4c, 0x53, 0x10, 0x21,
	0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x4c, 0x55, 0x4d, 0x49, 0x4e, 0x55, 0x4d, 0x10,
	0x22, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x41, 0x4c, 0x10, 0x23, 0x12, 0x23,
	0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x49, 0x52, 0x4f, 0x4e, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4f, 0x52,
	0x45, 0x10, 0x24, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x5f,
	0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x25, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x53, 0x54, 0x45, 0x45, 0x4c, 0x10, 0x26, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x5a,
	0x49, 0x4e, 0x43, 0x10, 0x27, 0x12, 0x31, 0x0a, 0x2d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45,
	0x52, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x53, 0x5f, 0x4d, 0x49, 0x4e, 0x45, 0x52, 0x41, 0x4c,
	0x53, 0x5f, 0x43, 0x4f, 0x41, 0x4c, 0x10, 0x28, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43,
	0x4f, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x45, 0x45, 0x4c, 0x10, 0x29, 0x12, 0x22, 0x0a,
	0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x41, 0x50, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x10,
	0x2a, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x53, 0x50, 0x48, 0x41, 0x4c, 0x54, 0x5f,
	0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x2b, 0x12, 0x38, 0x0a, 0x34, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41,
	0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x53, 0x5f, 0x47, 0x52, 0x41, 0x56, 0x45, 0x4c,
	0x5f, 0x52, 0x4f, 0x43, 0x4b, 0x5f, 0x53, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x4f, 0x4e, 0x45,
	0x10, 0x2c, 0x12, 0x37, 0x0a, 0x33, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x41, 0x52, 0x42, 0x4c, 0x45, 0x5f,
	0x47, 0x52, 0x41, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x53, 0x54,
	0x4f, 0x4e, 0x45, 0x5f, 0x53, 0x4c, 0x41, 0x42, 0x53, 0x10, 0x2d, 0x12, 0x33, 0x0a, 0x2f, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x53, 0x10, 0x2e,
	0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4c, 0x4f, 0x47, 0x53, 0x10, 0x2f, 0x12, 0x26, 0x0a,
	0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4c, 0x45, 0x53, 0x5f, 0x50, 0x4c, 0x41, 0x53,
	0x54, 0x49, 0x43, 0x10, 0x30, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x54,
	0x41, 0x49, 0x4e, 0x45, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x46, 0x52, 0x45, 0x49, 0x47, 0x48,
	0x54, 0x10, 0x31, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x47, 0x4c, 0x41, 0x53, 0x53, 0x5f,
	0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x32, 0x12, 0x35, 0x0a, 0x31, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x50, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41,
	0x4c, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x53, 0x10,
	0x33, 0x12, 0x2f, 0x0a, 0x2b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x41, 0x50, 0x45, 0x52, 0x5f, 0x41, 0x4e,
	0x44, 0x5f, 0x50, 0x41, 0x50, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53,
	0x10, 0x34, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x53, 0x54, 0x49, 0x43,
	0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x35, 0x12, 0x27, 0x0a, 0x23, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x50, 0x52, 0x49, 0x4e, 0x54, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49,
	0x41, 0x4c, 0x10, 0x36, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x52, 0x55, 0x42, 0x42, 0x45,
	0x52, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x54,
	0x49, 0x52, 0x45, 0x53, 0x10, 0x37, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x54, 0x48,
	0x45, 0x52, 0x5f, 0x50, 0x41, 0x50, 0x45, 0x52, 0x5f, 0x50, 0x4c, 0x41, 0x53, 0x54, 0x49, 0x43,
	0x5f, 0x47, 0x4c, 0x41, 0x53, 0x53, 0x10, 0x38, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x52,
	0x45, 0x43, 0x59, 0x43, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41,
	0x4c, 0x53, 0x10, 0x39, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49,
	0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x3a, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x41,
	0x52, 0x50, 0x45, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4f, 0x52, 0x49, 0x45, 0x4e, 0x54, 0x41,
	0x4c, 0x10, 0x3b, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x53, 0x4b, 0x45, 0x54,
	0x53, 0x10, 0x3c, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x44, 0x53, 0x5f, 0x44, 0x56,
	0x44, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x47, 0x41, 0x4d, 0x45, 0x53, 0x5f, 0x54, 0x41,
	0x50, 0x45, 0x53, 0x10, 0x3d, 0x12, 0x36, 0x0a, 0x32, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x4c, 0x4f, 0x54,
	0x48, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x48, 0x4f, 0x45, 0x53, 0x5f, 0x4e,
	0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x45, 0x52, 0x10, 0x3e, 0x12, 0x28, 0x0a,
	0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x53, 0x4d, 0x45, 0x54, 0x49, 0x43, 0x53, 0x5f, 0x50, 0x45,
	0x52, 0x46, 0x55, 0x4d, 0x45, 0x10, 0x3f, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x55,
	0x52, 0x4e, 0x49, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4e, 0x45, 0x57, 0x10, 0x40, 0x12, 0x2a, 0x0a,
	0x26, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x55, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x53, 0x54,
	0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x41, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x4f, 0x46, 0x46, 0x49, 0x43, 0x45, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x42, 0x12, 0x37, 0x0a, 0x33, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x48, 0x41, 0x52, 0x4d, 0x41, 0x43,
	0x45, 0x55, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x53, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x54, 0x48,
	0x45, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x45, 0x52, 0x10, 0x43, 0x12, 0x24, 0x0a, 0x20, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x53, 0x50, 0x41, 0x53, 0x5f, 0x48, 0x4f, 0x54, 0x5f, 0x54, 0x55, 0x42, 0x53, 0x10,
	0x44, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x10, 0x45, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54,
	0x4f, 0x59, 0x53, 0x10, 0x46, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45,
	0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x55, 0x4d, 0x45, 0x52, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53,
	0x10, 0x47, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x48, 0x4f,
	0x4c, 0x44, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x52, 0x10, 0x48,
	0x12, 0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x49, 0x52, 0x43, 0x52, 0x41, 0x46, 0x54, 0x5f,
	0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x53, 0x10, 0x49, 0x12, 0x31, 0x0a, 0x2d, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x41, 0x49, 0x52, 0x43, 0x52, 0x41, 0x46, 0x54, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x53, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x53, 0x10, 0x4a, 0x12, 0x37, 0x0a, 0x33,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52,
	0x49, 0x45, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x54, 0x49,
	0x52, 0x45, 0x53, 0x10, 0x4b, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x55, 0x54, 0x4f,
	0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x53, 0x10, 0x4c, 0x12, 0x3a, 0x0a, 0x36, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x42, 0x4f, 0x41, 0x54, 0x53, 0x5f, 0x47, 0x4f, 0x4c, 0x46, 0x5f, 0x43, 0x41, 0x52, 0x54, 0x53,
	0x5f, 0x52, 0x56, 0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c,
	0x45, 0x52, 0x53, 0x10, 0x4d, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x49, 0x52, 0x45,
	0x53, 0x10, 0x4e, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f,
	0x41, 0x55, 0x54, 0x4f, 0x53, 0x5f, 0x41, 0x49, 0x52, 0x43, 0x52, 0x41, 0x46, 0x54, 0x53, 0x10,
	0x4f, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x50,
	0x12, 0x3a, 0x0a, 0x36, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x52, 0x49, 0x43, 0x41,
	0x4c, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x55, 0x4e, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53,
	0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x51, 0x12, 0x20, 0x0a, 0x1c,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x4d, 0x41, 0x43, 0x48, 0x49, 0x4e, 0x45, 0x52, 0x59, 0x10, 0x52, 0x12, 0x37,
	0x0a, 0x33, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x53, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x43, 0x5f, 0x45, 0x51, 0x55, 0x49,
	0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x53, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x49,
	0x4c, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x54, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4d,
	0x41, 0x43, 0x48, 0x49, 0x4e, 0x45, 0x52, 0x59, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x55, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x52, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x53, 0x10, 0x56,
	0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4c, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x57, 0x12,
	0x1b, 0x0a, 0x17, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x49, 0x50, 0x45, 0x10, 0x58, 0x12, 0x28, 0x0a, 0x24,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x50, 0x4c, 0x55, 0x4d, 0x42, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x55, 0x50, 0x50,
	0x4c, 0x49, 0x45, 0x53, 0x10, 0x59, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x4f, 0x4f,
	0x4c, 0x53, 0x10, 0x5a, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x57, 0x49, 0x52, 0x45, 0x10,
	0x5b, 0x12, 0x3e, 0x0a, 0x3a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x57, 0x4f, 0x4f, 0x44, 0x5f, 0x50, 0x52, 0x4f,
	0x44, 0x55, 0x43, 0x54, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x55, 0x52, 0x4e, 0x49, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x41, 0x53, 0x4b, 0x45, 0x54, 0x53, 0x10,
	0x5c, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x42, 0x55,
	0x49, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x53, 0x10,
	0x5d, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4d,
	0x4f, 0x44, 0x55, 0x4c, 0x41, 0x52, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x53, 0x10, 0x5e, 0x12, 0x29,
	0x0a, 0x25, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x4e, 0x49, 0x4d, 0x41, 0x4c, 0x5f, 0x42, 0x59, 0x5f, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x5f, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x52, 0x45, 0x53, 0x49, 0x4e, 0x53, 0x10, 0x60, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x5f, 0x4d, 0x49, 0x53, 0x43, 0x45, 0x4c, 0x4c, 0x41, 0x4e, 0x45, 0x4f,
	0x55, 0x53, 0x10, 0x61, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x47, 0x41, 0x52, 0x42, 0x41,
	0x47, 0x45, 0x10, 0x62, 0x12, 0x43, 0x0a, 0x3f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x4e, 0x4f,
	0x4e, 0x5f, 0x4e, 0x45, 0x47, 0x4f, 0x54, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x45, 0x43,
	0x55, 0x52, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x63, 0x2a, 0x94, 0x06, 0x0a, 0x0a, 0x55, 0x53,
	0x44, 0x4f, 0x54, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x55, 0x53, 0x5f, 0x44,
	0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54,
	0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x58, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53,
	0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x30, 0x31, 0x10, 0x02,
	0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x5f, 0x41, 0x30, 0x32, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54,
	0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x30, 0x33, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10,
	0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x30, 0x34,
	0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f,
	0x52, 0x45, 0x5f, 0x41, 0x30, 0x35, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44,
	0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x30, 0x36, 0x10, 0x07, 0x12, 0x14,
	0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41,
	0x30, 0x37, 0x10, 0x08, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53,
	0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x30, 0x38, 0x10, 0x09, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53,
	0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x30, 0x39, 0x10, 0x0a,
	0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x5f, 0x41, 0x31, 0x30, 0x10, 0x0b, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54,
	0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x31, 0x31, 0x10, 0x0c, 0x12, 0x14, 0x0a, 0x10,
	0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x31, 0x32,
	0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f,
	0x52, 0x45, 0x5f, 0x41, 0x31, 0x33, 0x10, 0x0e, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44,
	0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x30, 0x31, 0x10, 0x0f, 0x12, 0x14,
	0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42,
	0x30, 0x32, 0x10, 0x10, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53,
	0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x30, 0x33, 0x10, 0x11, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53,
	0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x30, 0x34, 0x10, 0x12,
	0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x5f, 0x42, 0x30, 0x35, 0x10, 0x13, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54,
	0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x30, 0x36, 0x10, 0x14, 0x12, 0x14, 0x0a, 0x10,
	0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x30, 0x37,
	0x10, 0x15, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f,
	0x52, 0x45, 0x5f, 0x42, 0x30, 0x38, 0x10, 0x16, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44,
	0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x30, 0x39, 0x10, 0x17, 0x12, 0x14,
	0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42,
	0x31, 0x30, 0x10, 0x18, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53,
	0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x31, 0x31, 0x10, 0x19, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53,
	0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x31, 0x32, 0x10, 0x1a,
	0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x5f, 0x42, 0x31, 0x33, 0x10, 0x1b, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54,
	0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x5a, 0x39, 0x33, 0x10, 0x1c, 0x12, 0x14, 0x0a, 0x10,
	0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x5a, 0x39, 0x34,
	0x10, 0x1d, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f,
	0x52, 0x45, 0x5f, 0x5a, 0x39, 0x35, 0x10, 0x1e, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44,
	0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x5a, 0x39, 0x37, 0x10, 0x1f, 0x12, 0x14,
	0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x5a,
	0x39, 0x38, 0x10, 0x20, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53,
	0x43, 0x4f, 0x52, 0x45, 0x5f, 0x5a, 0x39, 0x39, 0x10, 0x21, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x53,
	0x5f, 0x44, 0x4f, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x5a, 0x39, 0x32, 0x10, 0x22,
	0x2a, 0xb6, 0x05, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x1c, 0x0a, 0x18, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13,
	0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x43,
	0x30, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43,
	0x4f, 0x52, 0x45, 0x5f, 0x41, 0x31, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x32, 0x10, 0x03, 0x12, 0x13, 0x0a,
	0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x33,
	0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f,
	0x52, 0x45, 0x5f, 0x42, 0x30, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x31, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x32, 0x10,
	0x07, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52,
	0x45, 0x5f, 0x42, 0x33, 0x10, 0x08, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x43, 0x31, 0x10, 0x09, 0x12, 0x13, 0x0a, 0x0f, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x43, 0x32, 0x10, 0x0a,
	0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x5f, 0x43, 0x33, 0x10, 0x0b, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x44, 0x30, 0x10, 0x0c, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x44, 0x31, 0x10, 0x0d, 0x12,
	0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f,
	0x44, 0x32, 0x10, 0x0e, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53,
	0x43, 0x4f, 0x52, 0x45, 0x5f, 0x45, 0x30, 0x10, 0x0f, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45,
	0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x45, 0x31, 0x10, 0x10, 0x12, 0x13,
	0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x45,
	0x32, 0x10, 0x11, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43,
	0x4f, 0x52, 0x45, 0x5f, 0x49, 0x31, 0x10, 0x12, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x4e, 0x41, 0x10, 0x13, 0x12, 0x13, 0x0a,
	0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x4f, 0x31,
	0x10, 0x14, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f,
	0x52, 0x45, 0x5f, 0x50, 0x31, 0x10, 0x15, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x51, 0x31, 0x10, 0x16, 0x12, 0x13, 0x0a, 0x0f,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x54, 0x31, 0x10,
	0x17, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52,
	0x45, 0x5f, 0x54, 0x33, 0x10, 0x18, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x54, 0x34, 0x10, 0x19, 0x12, 0x13, 0x0a, 0x0f, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x54, 0x35, 0x10, 0x1a,
	0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x5f, 0x58, 0x31, 0x10, 0x1b, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x58, 0x33, 0x10, 0x1c, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x58, 0x34, 0x10, 0x1d, 0x12,
	0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f,
	0x58, 0x35, 0x10, 0x1e, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53,
	0x43, 0x4f, 0x52, 0x45, 0x5f, 0x58, 0x58, 0x10, 0x1f, 0x2a, 0x71, 0x0a, 0x0b, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x45, 0x46, 0x54, 0x10, 0x01, 0x12, 0x1d, 0x0a,
	0x19, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x5f, 0x50, 0x41,
	0x49, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x02, 0x2a, 0xa6, 0x02, 0x0a,
	0x14, 0x4d, 0x61, 0x78, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x23, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x41, 0x44,
	0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23,
	0x0a, 0x1f, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f,
	0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x35, 0x30, 0x4d, 0x49, 0x4c, 0x45,
	0x53, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x41, 0x44, 0x49, 0x55,
	0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x31,
	0x30, 0x30, 0x4d, 0x49, 0x4c, 0x45, 0x53, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x4d, 0x41, 0x58,
	0x5f, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x32, 0x30, 0x30, 0x4d, 0x49, 0x4c, 0x45, 0x53, 0x10, 0x03, 0x12,
	0x24, 0x0a, 0x20, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46,
	0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x33, 0x30, 0x30, 0x4d, 0x49,
	0x4c, 0x45, 0x53, 0x10, 0x04, 0x12, 0x24, 0x0a, 0x20, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x41, 0x44,
	0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x35, 0x30, 0x30, 0x4d, 0x49, 0x4c, 0x45, 0x53, 0x10, 0x05, 0x12, 0x28, 0x0a, 0x24, 0x4d,
	0x41, 0x58, 0x5f, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f, 0x50, 0x45,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x39, 0x39, 0x39, 0x50, 0x4c, 0x55, 0x53, 0x4d, 0x49,
	0x4c, 0x45, 0x53, 0x10, 0x06, 0x2a, 0xb4, 0x05, 0x0a, 0x0c, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c,
	0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x41, 0x47, 0x52, 0x49, 0x43, 0x55, 0x4c, 0x54, 0x55,
	0x52, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x52, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f,
	0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c,
	0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x44, 0x52, 0x59, 0x5f, 0x46, 0x52, 0x45, 0x49,
	0x47, 0x48, 0x54, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x44, 0x55, 0x4d, 0x50, 0x10, 0x05, 0x12, 0x19, 0x0a,
	0x15, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x46,
	0x4c, 0x41, 0x54, 0x42, 0x45, 0x44, 0x10, 0x06, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x46, 0x52, 0x4f, 0x4e, 0x54, 0x5f,
	0x4c, 0x4f, 0x41, 0x44, 0x45, 0x52, 0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x47, 0x41, 0x52, 0x42, 0x41, 0x47,
	0x45, 0x10, 0x08, 0x12, 0x1b, 0x0a, 0x17, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x43,
	0x4c, 0x41, 0x53, 0x53, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x10, 0x09,
	0x12, 0x19, 0x0a, 0x15, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x5f, 0x4c, 0x4f, 0x47, 0x47, 0x49, 0x4e, 0x47, 0x10, 0x0a, 0x12, 0x18, 0x0a, 0x14, 0x56,
	0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x4c, 0x4f, 0x57,
	0x42, 0x4f, 0x59, 0x10, 0x0b, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x5f, 0x54, 0x52,
	0x55, 0x43, 0x4b, 0x10, 0x0c, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x50, 0x4f, 0x4c, 0x45, 0x10, 0x0d, 0x12, 0x19, 0x0a,
	0x15, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x52,
	0x41, 0x47, 0x5f, 0x54, 0x4f, 0x50, 0x10, 0x0e, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x45, 0x46, 0x45, 0x52,
	0x10, 0x0f, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x4b, 0x45, 0x10, 0x10, 0x12, 0x1a, 0x0a, 0x16, 0x56,
	0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x53, 0x54, 0x52,
	0x41, 0x49, 0x47, 0x48, 0x54, 0x10, 0x11, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x45, 0x48, 0x49, 0x43,
	0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x54, 0x41, 0x4e, 0x4b, 0x10, 0x12, 0x12,
	0x16, 0x0a, 0x12, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53,
	0x5f, 0x54, 0x49, 0x4c, 0x54, 0x10, 0x13, 0x12, 0x1f, 0x0a, 0x1b, 0x56, 0x45, 0x48, 0x49, 0x43,
	0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x5f, 0x54,
	0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x14, 0x12, 0x19, 0x0a, 0x15, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x10, 0x15, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x43,
	0x4c, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x16, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x43,
	0x4c, 0x41, 0x53, 0x53, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x17, 0x2a, 0xfd, 0x01, 0x0a,
	0x0b, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18,
	0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x45,
	0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b,
	0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14,
	0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41,
	0x49, 0x4c, 0x45, 0x52, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4d, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x49,
	0x4c, 0x45, 0x52, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x10, 0x05, 0x12, 0x23,
	0x0a, 0x1f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x50, 0x41, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x4d, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45,
	0x52, 0x10, 0x06, 0x12, 0x27, 0x0a, 0x23, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x44, 0x5f, 0x53, 0x45,
	0x4d, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x10, 0x07, 0x2a, 0xc2, 0x03, 0x0a,
	0x12, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x20, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57,
	0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x5f, 0x41, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x42, 0x10,
	0x02, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49,
	0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x43, 0x10, 0x03, 0x12, 0x1a, 0x0a,
	0x16, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f,
	0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x44, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x5f, 0x45, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x46, 0x10,
	0x06, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49,
	0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x47, 0x10, 0x07, 0x12, 0x1a, 0x0a,
	0x16, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f,
	0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x48, 0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x5f, 0x33, 0x10, 0x09, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x34, 0x10,
	0x0a, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49,
	0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x35, 0x10, 0x0b, 0x12, 0x1a, 0x0a,
	0x16, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f,
	0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x36, 0x10, 0x0c, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x5f, 0x37, 0x10, 0x0d, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x38, 0x10,
	0x0e, 0x42, 0x28, 0x5a, 0x26, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x6e, 0x6f, 0x6e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_nonfleet_model_program_data_proto_rawDescOnce sync.Once
	file_nonfleet_model_program_data_proto_rawDescData = file_nonfleet_model_program_data_proto_rawDesc
)

func file_nonfleet_model_program_data_proto_rawDescGZIP() []byte {
	file_nonfleet_model_program_data_proto_rawDescOnce.Do(func() {
		file_nonfleet_model_program_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_nonfleet_model_program_data_proto_rawDescData)
	})
	return file_nonfleet_model_program_data_proto_rawDescData
}

var file_nonfleet_model_program_data_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_nonfleet_model_program_data_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_nonfleet_model_program_data_proto_goTypes = []interface{}{
	(CommodityCategory)(0),                   // 0: nonfleet_model.CommodityCategory
	(USDOTScore)(0),                          // 1: nonfleet_model.USDOTScore
	(CreditScore)(0),                         // 2: nonfleet_model.CreditScore
	(PaymentPlan)(0),                         // 3: nonfleet_model.PaymentPlan
	(MaxRadiusOfOperation)(0),                // 4: nonfleet_model.MaxRadiusOfOperation
	(VehicleClass)(0),                        // 5: nonfleet_model.VehicleClass
	(VehicleType)(0),                         // 6: nonfleet_model.VehicleType
	(VehicleWeightClass)(0),                  // 7: nonfleet_model.VehicleWeightClass
	(*NFAdmittedProgramDataV1)(nil),          // 8: nonfleet_model.NFAdmittedProgramDataV1
	(*NFAdmittedCommodityDetailsV1)(nil),     // 9: nonfleet_model.NFAdmittedCommodityDetailsV1
	(*NFAdmittedUnderwriterInputDataV1)(nil), // 10: nonfleet_model.NFAdmittedUnderwriterInputDataV1
	(*NFAdmittedScheduleModDataV1)(nil),      // 11: nonfleet_model.NFAdmittedScheduleModDataV1
	(*NFAdmittedOperationDataV1)(nil),        // 12: nonfleet_model.NFAdmittedOperationDataV1
	(*NFAdmittedDriverDataV1)(nil),           // 13: nonfleet_model.NFAdmittedDriverDataV1
	(*NFAdmittedViolationDataV1)(nil),        // 14: nonfleet_model.NFAdmittedViolationDataV1
	(*NFAdmittedVehicleDataV1)(nil),          // 15: nonfleet_model.NFAdmittedVehicleDataV1
	(*NFAdmittedCommodityDataV1)(nil),        // 16: nonfleet_model.NFAdmittedCommodityDataV1
	(*NFCompanyInfoV1)(nil),                  // 17: nonfleet_model.NFCompanyInfoV1
	(*NFBusinessOwnerV1)(nil),                // 18: nonfleet_model.NFBusinessOwnerV1
	nil,                                      // 19: nonfleet_model.NFAdmittedViolationDataV1.ClassCountsEntry
	(*timestamppb.Timestamp)(nil),            // 20: google.protobuf.Timestamp
	(*proto.Insured)(nil),                    // 21: insurance_core.Insured
	(*proto1.Address)(nil),                   // 22: common.Address
}
var file_nonfleet_model_program_data_proto_depIdxs = []int32{
	12, // 0: nonfleet_model.NFAdmittedProgramDataV1.operations:type_name -> nonfleet_model.NFAdmittedOperationDataV1
	13, // 1: nonfleet_model.NFAdmittedProgramDataV1.drivers:type_name -> nonfleet_model.NFAdmittedDriverDataV1
	15, // 2: nonfleet_model.NFAdmittedProgramDataV1.vehicles:type_name -> nonfleet_model.NFAdmittedVehicleDataV1
	9,  // 3: nonfleet_model.NFAdmittedProgramDataV1.commodityDetails:type_name -> nonfleet_model.NFAdmittedCommodityDetailsV1
	10, // 4: nonfleet_model.NFAdmittedProgramDataV1.underwriterInput:type_name -> nonfleet_model.NFAdmittedUnderwriterInputDataV1
	17, // 5: nonfleet_model.NFAdmittedProgramDataV1.companyInfo:type_name -> nonfleet_model.NFCompanyInfoV1
	16, // 6: nonfleet_model.NFAdmittedCommodityDetailsV1.commodities:type_name -> nonfleet_model.NFAdmittedCommodityDataV1
	0,  // 7: nonfleet_model.NFAdmittedCommodityDetailsV1.primaryCommodity:type_name -> nonfleet_model.CommodityCategory
	1,  // 8: nonfleet_model.NFAdmittedUnderwriterInputDataV1.usDotScore:type_name -> nonfleet_model.USDOTScore
	2,  // 9: nonfleet_model.NFAdmittedUnderwriterInputDataV1.creditScore:type_name -> nonfleet_model.CreditScore
	3,  // 10: nonfleet_model.NFAdmittedUnderwriterInputDataV1.paymentPlan:type_name -> nonfleet_model.PaymentPlan
	11, // 11: nonfleet_model.NFAdmittedUnderwriterInputDataV1.scheduleMods:type_name -> nonfleet_model.NFAdmittedScheduleModDataV1
	4,  // 12: nonfleet_model.NFAdmittedOperationDataV1.maxRadiusOfOperation:type_name -> nonfleet_model.MaxRadiusOfOperation
	20, // 13: nonfleet_model.NFAdmittedDriverDataV1.dateOfBirth:type_name -> google.protobuf.Timestamp
	20, // 14: nonfleet_model.NFAdmittedDriverDataV1.dateOfHire:type_name -> google.protobuf.Timestamp
	14, // 15: nonfleet_model.NFAdmittedDriverDataV1.violations:type_name -> nonfleet_model.NFAdmittedViolationDataV1
	19, // 16: nonfleet_model.NFAdmittedViolationDataV1.classCounts:type_name -> nonfleet_model.NFAdmittedViolationDataV1.ClassCountsEntry
	5,  // 17: nonfleet_model.NFAdmittedVehicleDataV1.vehicleClass:type_name -> nonfleet_model.VehicleClass
	6,  // 18: nonfleet_model.NFAdmittedVehicleDataV1.vehicleType:type_name -> nonfleet_model.VehicleType
	7,  // 19: nonfleet_model.NFAdmittedVehicleDataV1.vehicleWeightClass:type_name -> nonfleet_model.VehicleWeightClass
	21, // 20: nonfleet_model.NFAdmittedVehicleDataV1.lossPayee:type_name -> insurance_core.Insured
	0,  // 21: nonfleet_model.NFAdmittedCommodityDataV1.commodity:type_name -> nonfleet_model.CommodityCategory
	18, // 22: nonfleet_model.NFCompanyInfoV1.businessOwner:type_name -> nonfleet_model.NFBusinessOwnerV1
	22, // 23: nonfleet_model.NFCompanyInfoV1.terminalAddress:type_name -> common.Address
	22, // 24: nonfleet_model.NFCompanyInfoV1.mailingAddress:type_name -> common.Address
	20, // 25: nonfleet_model.NFBusinessOwnerV1.dateOfBirth:type_name -> google.protobuf.Timestamp
	22, // 26: nonfleet_model.NFBusinessOwnerV1.address:type_name -> common.Address
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_nonfleet_model_program_data_proto_init() }
func file_nonfleet_model_program_data_proto_init() {
	if File_nonfleet_model_program_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_nonfleet_model_program_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFAdmittedProgramDataV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_program_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFAdmittedCommodityDetailsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_program_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFAdmittedUnderwriterInputDataV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_program_data_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFAdmittedScheduleModDataV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_program_data_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFAdmittedOperationDataV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_program_data_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFAdmittedDriverDataV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_program_data_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFAdmittedViolationDataV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_program_data_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFAdmittedVehicleDataV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_program_data_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFAdmittedCommodityDataV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_program_data_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFCompanyInfoV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_nonfleet_model_program_data_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFBusinessOwnerV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_nonfleet_model_program_data_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_nonfleet_model_program_data_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_nonfleet_model_program_data_proto_msgTypes[10].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_nonfleet_model_program_data_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_nonfleet_model_program_data_proto_goTypes,
		DependencyIndexes: file_nonfleet_model_program_data_proto_depIdxs,
		EnumInfos:         file_nonfleet_model_program_data_proto_enumTypes,
		MessageInfos:      file_nonfleet_model_program_data_proto_msgTypes,
	}.Build()
	File_nonfleet_model_program_data_proto = out.File
	file_nonfleet_model_program_data_proto_rawDesc = nil
	file_nonfleet_model_program_data_proto_goTypes = nil
	file_nonfleet_model_program_data_proto_depIdxs = nil
}
