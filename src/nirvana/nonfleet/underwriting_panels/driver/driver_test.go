package driver

import (
	"context"
	"google.golang.org/protobuf/types/known/timestamppb"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"sort"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"
	"gotest.tools/assert"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/rating/mvr"
)

func Test_updateDriverInfoAndOverrides(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		ApplicationReviewWrapper application_review.Wrapper
		AdmittedAppWrapper       nf_app.Wrapper[*admitted_app.AdmittedApp]
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	appObj := admitted_app.MockApplicationImpl()
	err := env.AdmittedAppWrapper.InsertApp(ctx, appObj)
	require.NoError(t, err)

	subObj := admitted_app.MockSubmissionImpl()
	err = env.AdmittedAppWrapper.InsertSubmission(ctx, subObj)
	require.NoError(t, err)

	appReviewObj := &application_review.AdmittedApplicationReview{
		ApplicationID: uuid.MustParse(appObj.ID.String()),
		ID:            uuid.New(),
		SubmissionID:  uuid.MustParse(subObj.ID.String()),
	}
	err = env.ApplicationReviewWrapper.InsertApplicationReview(ctx, appReviewObj)
	require.NoError(t, err)

	driVioOverrides := DriverViolationOverrides{
		DriverViolations: &[]admitted_app.DriverViolation{
			{
				LicenseNumber:   "1",
				ViolationPoints: 2,
				ClassCounts: map[string]int64{
					"C":  1,
					"NA": 2,
				},
			},
			{
				LicenseNumber:   "2",
				ViolationPoints: 3,
				ClassCounts: map[string]int64{
					"C":  1,
					"NA": 0,
					"A":  3,
				},
			},
		},
		ViolationPoints: &map[string]int64{
			"A":  3,
			"C":  2,
			"NA": 2,
		},
		CountViolationClassA:  pointer_utils.ToPointer(int64(2)),
		CountViolationClassC:  pointer_utils.ToPointer(int64(4)),
		CountViolationClassNA: pointer_utils.ToPointer(int64(3)),
	}

	driversInfo := application_review.DriversInfo{
		Drivers: map[string]application_review.Driver{
			"1": {
				IsExcluded:   false,
				IsOutOfState: false,
				Violations: []application_review.Violation{
					{
						Code:            "421450",
						Date:            time_utils.NewDate(2023, 10, 11).ToTime(),
						ActualPoints:    1,
						IsManuallyAdded: true,
						CreatedBy:       pointer_utils.ToPointer(uuid.New()),
					},
					{
						Code:            "421470",
						Date:            time_utils.NewDate(2019, 10, 11).ToTime(),
						ActualPoints:    0,
						IsManuallyAdded: false,
						OriginalPoints:  pointer_utils.ToPointer(1),
					},
				},
				ViolationClassCounts: map[string]int64{
					"C":  1,
					"NA": 2,
				},
			},
			"2": {
				IsExcluded:   false,
				IsOutOfState: false,
				Violations: []application_review.Violation{
					{
						Code:            "531250",
						Date:            time_utils.NewDate(2023, 10, 11).ToTime(),
						ActualPoints:    1,
						IsManuallyAdded: true,
						CreatedBy:       pointer_utils.ToPointer(uuid.New()),
					},
					{
						Code:            "428310",
						Date:            time_utils.NewDate(2022, 10, 11).ToTime(),
						ActualPoints:    1,
						IsManuallyAdded: false,
						OriginalPoints:  pointer_utils.ToPointer(1),
					},
					{
						Code:            "583400",
						Date:            time_utils.NewDate(2023, 10, 11).ToTime(),
						ActualPoints:    0,
						IsManuallyAdded: false,
						OriginalPoints:  pointer_utils.ToPointer(1),
					},
				},
				ViolationClassCounts: map[string]int64{
					"C":  1,
					"NA": 0,
					"A":  3,
				},
			},
			"3": {
				IsExcluded:   true,
				IsOutOfState: false,
				Violations: []application_review.Violation{
					{
						Code:            "421450",
						Date:            time_utils.NewDate(2023, 10, 11).ToTime(),
						ActualPoints:    1,
						IsManuallyAdded: true,
						CreatedBy:       pointer_utils.ToPointer(uuid.New()),
					},
				},
				ViolationClassCounts: map[string]int64{
					"C":  1,
					"NA": 2,
				},
			},
		},
	}

	if err = updateDriverInfoAndOverridesInDB(
		ctx,
		env.ApplicationReviewWrapper,
		appReviewObj.ID,
		driVioOverrides,
		&driversInfo,
		true); err != nil {
		t.Errorf("updateDriverInfoAndOverrides() error = %v", err)
	}

	appReview, err := env.ApplicationReviewWrapper.GetAppReviewByID(ctx, appReviewObj.ID.String())
	require.NoError(t, err)
	assert.DeepEqual(t, *appReview.GetDriversInfo(), driversInfo)
	overrides := appReview.GetOverrides()
	assert.DeepEqual(t, overrides.DriverViolations, driVioOverrides.DriverViolations)
	assert.DeepEqual(t, overrides.ViolationPoints, driVioOverrides.ViolationPoints)
	assert.Equal(t, *overrides.CountViolationClassA, *driVioOverrides.CountViolationClassA)
	assert.Equal(t, *overrides.CountViolationClassC, *driVioOverrides.CountViolationClassC)
	assert.Equal(t, *overrides.CountViolationClassNA, *driVioOverrides.CountViolationClassNA)
	assert.Equal(t, overrides.CountViolationClassB, driVioOverrides.CountViolationClassB)
}

func Test_updateAppReviewDriversInfo(t *testing.T) {
	type args struct {
		effectiveDate       time.Time
		originalDriversInfo *application_review.DriversInfo
		driverRecords       []driverRecord
	}
	tests := []struct {
		name    string
		args    args
		want    *application_review.DriversInfo
		wantErr bool
	}{
		{
			name: "drivers Info is nil",
			args: args{
				effectiveDate: time.Now(),
			},
			wantErr: false,
		},
		{
			name: "drivers in drivers info is nil",
			args: args{
				effectiveDate:       time.Now(),
				originalDriversInfo: &application_review.DriversInfo{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := updateAppReviewDriversInfo(tt.args.effectiveDate, tt.args.originalDriversInfo, tt.args.driverRecords)
			if (err != nil) != tt.wantErr {
				t.Errorf("updateAppReviewDriversInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestDriversPanel_UpdateDriverWithViolations(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		ApplicationReviewWrapper application_review.Wrapper
		AdmittedAppWrapper       nf_app.Wrapper[*admitted_app.AdmittedApp]
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	appReviewObj := createAppReview(ctx, t, env.AdmittedAppWrapper, env.ApplicationReviewWrapper)

	dp := DriversPanel[nf_app.AppInfo]{
		deps: Deps[nf_app.AppInfo]{
			AppReviewWrapper: env.ApplicationReviewWrapper,
			AdmittedWrapper:  env.AdmittedAppWrapper,
		},
	}

	violationsRecords := []ViolationRecord{
		{
			Code:            "241000",
			Date:            time_utils.NewDate(2022, 0o4, 0o3).ToTime(),
			IsManuallyAdded: true,
			CreatedBy:       nil,
		},
		{
			Code:            "241000",
			Date:            time_utils.NewDate(2023, 0o7, 0o3).ToTime(),
			IsManuallyAdded: true,
			CreatedBy:       nil,
		},
		{
			Code:            "241000",
			Date:            time_utils.NewDate(2019, 0o4, 0o3).ToTime(),
			IsManuallyAdded: true,
			CreatedBy:       nil,
		},
		{
			Code:            "241000",
			Date:            time_utils.NewDate(2016, 0o4, 0o3).ToTime(),
			IsManuallyAdded: true,
			CreatedBy:       nil,
		},
		{
			Code:            "421020",
			Date:            time_utils.NewDate(2022, 0o3, 0o3).ToTime(),
			IsManuallyAdded: true,
			CreatedBy:       nil,
		},
	}

	err := dp.UpdateDriverV2(
		ctx, appReviewObj.GetID(),
		"123",
		false,
		false,
		violationsRecords)
	require.NoError(t, err)

	appReview, err := env.ApplicationReviewWrapper.GetAppReviewByID(
		ctx,
		appReviewObj.GetID().String())
	require.NoError(t, err)
	driverVios := []admitted_app.DriverViolation{
		{
			LicenseNumber:   "123",
			ViolationPoints: 28,
			ClassCounts: map[string]int64{
				"D": 3,
				"C": 1,
				"A": 2,
				"B": 2,
			},
		},
		{
			LicenseNumber:   "789",
			ViolationPoints: 3,
			ClassCounts: map[string]int64{
				"B": 1,
			},
		},
	}

	compareOverrides(t, appReview.GetOverrides(), application_review.Overrides{
		DriverViolations:      &driverVios,
		ViolationPoints:       &map[string]int64{"123": 28, "789": 3},
		CountViolationClassA:  pointer_utils.ToPointer(int64(2)),
		CountViolationClassB:  pointer_utils.ToPointer(int64(3)),
		CountViolationClassC:  pointer_utils.ToPointer(int64(1)),
		CountViolationClassD:  pointer_utils.ToPointer(int64(3)),
		CountViolationClassE:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassF:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassN:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassNA: pointer_utils.ToPointer(int64(0)),
		OutOfStateDrivers:     map[string]bool{"123": false},
		ExcludedDrivers:       map[string]bool{"123": false},
	})
}

func TestDriversPanel_IncludeDriver(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		ApplicationReviewWrapper application_review.Wrapper
		AdmittedAppWrapper       nf_app.Wrapper[*admitted_app.AdmittedApp]
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	appReviewObj := createAppReview(ctx, t, env.AdmittedAppWrapper, env.ApplicationReviewWrapper)

	dp := DriversPanel[nf_app.AppInfo]{
		deps: Deps[nf_app.AppInfo]{
			AppReviewWrapper: env.ApplicationReviewWrapper,
			AdmittedWrapper:  env.AdmittedAppWrapper,
		},
	}

	violationsRecords := []ViolationRecord{
		{
			Code:            "241000",
			Date:            time_utils.NewDate(2022, 0o4, 0o3).ToTime(),
			IsManuallyAdded: true,
			CreatedBy:       nil,
		},
	}

	err := dp.UpdateDriverV2(
		ctx, appReviewObj.GetID(),
		"456",
		false,
		true,
		violationsRecords)
	require.NoError(t, err)

	appReview, err := env.ApplicationReviewWrapper.GetAppReviewByID(
		ctx,
		appReviewObj.GetID().String())
	require.NoError(t, err)
	driverVios := []admitted_app.DriverViolation{
		{
			LicenseNumber:   "123",
			ViolationPoints: 15,
			ClassCounts: map[string]int64{
				"A": 2,
				"B": 2,
			},
		},
		{
			LicenseNumber:   "456",
			ViolationPoints: 6,
			ClassCounts: map[string]int64{
				"B": 1,
				"D": 1,
			},
		},
		{
			LicenseNumber:   "789",
			ViolationPoints: 3,
			ClassCounts: map[string]int64{
				"B": 1,
			},
		},
	}

	compareOverrides(t, appReview.GetOverrides(), application_review.Overrides{
		DriverViolations:      &driverVios,
		ViolationPoints:       &map[string]int64{"123": 15, "456": 6, "789": 3},
		CountViolationClassA:  pointer_utils.ToPointer(int64(2)),
		CountViolationClassB:  pointer_utils.ToPointer(int64(4)),
		CountViolationClassC:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassD:  pointer_utils.ToPointer(int64(1)),
		CountViolationClassE:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassF:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassN:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassNA: pointer_utils.ToPointer(int64(0)),
		OutOfStateDrivers:     map[string]bool{"456": true},
		ExcludedDrivers:       map[string]bool{"456": false},
	})
}

func TestDriversPanel_ExcludeDriverAndMarkInState(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		ApplicationReviewWrapper application_review.Wrapper
		AdmittedAppWrapper       nf_app.Wrapper[*admitted_app.AdmittedApp]
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	appReviewObj := createAppReview(ctx, t, env.AdmittedAppWrapper, env.ApplicationReviewWrapper)

	dp := DriversPanel[nf_app.AppInfo]{
		deps: Deps[nf_app.AppInfo]{
			AppReviewWrapper: env.ApplicationReviewWrapper,
			AdmittedWrapper:  env.AdmittedAppWrapper,
		},
	}

	violationsRecords := []ViolationRecord{
		{
			Code:            "241000",
			Date:            time_utils.NewDate(2022, 0o4, 0o3).ToTime(),
			IsManuallyAdded: true,
			CreatedBy:       nil,
		},
	}

	err := dp.UpdateDriverV2(
		ctx, appReviewObj.GetID(),
		"789",
		true,
		false,
		violationsRecords)
	require.NoError(t, err)

	appReview, err := env.ApplicationReviewWrapper.GetAppReviewByID(
		ctx,
		appReviewObj.GetID().String())
	require.NoError(t, err)
	driverVios := []admitted_app.DriverViolation{
		{
			LicenseNumber:   "123",
			ViolationPoints: 15,
			ClassCounts: map[string]int64{
				"A": 2,
				"B": 2,
			},
		},
	}

	compareOverrides(t, appReview.GetOverrides(), application_review.Overrides{
		DriverViolations:      &driverVios,
		ViolationPoints:       &map[string]int64{"123": 15},
		CountViolationClassA:  pointer_utils.ToPointer(int64(2)),
		CountViolationClassB:  pointer_utils.ToPointer(int64(2)),
		CountViolationClassC:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassD:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassE:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassF:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassN:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassNA: pointer_utils.ToPointer(int64(0)),
		OutOfStateDrivers:     map[string]bool{"789": false},
		ExcludedDrivers:       map[string]bool{"789": true},
	})
}

func TestDriversPanel_MarkOutOfState(t *testing.T) {
	ctx := context.Background()
	var env struct {
		fx.In
		ApplicationReviewWrapper application_review.Wrapper
		AdmittedAppWrapper       nf_app.Wrapper[*admitted_app.AdmittedApp]
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	appReviewObj := createAppReview(ctx, t, env.AdmittedAppWrapper, env.ApplicationReviewWrapper)

	dp := DriversPanel[nf_app.AppInfo]{
		deps: Deps[nf_app.AppInfo]{
			AppReviewWrapper: env.ApplicationReviewWrapper,
			AdmittedWrapper:  env.AdmittedAppWrapper,
		},
	}

	violationsRecords := []ViolationRecord{
		{
			Code:            "241000",
			Date:            time_utils.NewDate(2022, 0o4, 0o3).ToTime(),
			IsManuallyAdded: true,
			CreatedBy:       nil,
		},
	}

	err := dp.UpdateDriverV2(
		ctx, appReviewObj.GetID(),
		"123",
		false,
		true,
		violationsRecords)
	require.NoError(t, err)

	appReview, err := env.ApplicationReviewWrapper.GetAppReviewByID(
		ctx,
		appReviewObj.GetID().String())
	require.NoError(t, err)
	driverVios := []admitted_app.DriverViolation{
		{
			LicenseNumber:   "123",
			ViolationPoints: 18,
			ClassCounts: map[string]int64{
				"A": 2,
				"B": 2,
				"D": 1,
			},
		},
		{
			LicenseNumber:   "789",
			ViolationPoints: 3,
			ClassCounts: map[string]int64{
				"B": 1,
			},
		},
	}

	compareOverrides(t, appReview.GetOverrides(), application_review.Overrides{
		DriverViolations:      &driverVios,
		ViolationPoints:       &map[string]int64{"123": 18, "789": 3},
		CountViolationClassA:  pointer_utils.ToPointer(int64(2)),
		CountViolationClassB:  pointer_utils.ToPointer(int64(3)),
		CountViolationClassC:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassD:  pointer_utils.ToPointer(int64(1)),
		CountViolationClassE:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassF:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassN:  pointer_utils.ToPointer(int64(0)),
		CountViolationClassNA: pointer_utils.ToPointer(int64(0)),
		OutOfStateDrivers:     map[string]bool{"123": true},
		ExcludedDrivers:       map[string]bool{"123": false},
	})
}

func createAppReview(
	ctx context.Context,
	t *testing.T,
	admittedAppWrapper nf_app.Wrapper[*admitted_app.AdmittedApp],
	appReviewWrapper application_review.Wrapper,
) application_review.ApplicationReview {
	appObj := admitted_app.MockApplicationImpl()
	appObj.Info.DriverInfo = admitted_app.DriverInfo{
		Drivers: []admitted_app.DriverDetails{
			{
				DriverBasicDetails: nf_app.DriverBasicDetails{
					FirstName:     "test-f1",
					LastName:      "test-l1",
					LicenseState:  "CA",
					LicenseNumber: "123",
					DateOfBirth:   time_utils.NewDate(1980, 0o2, 0o5).ToTime(),
					DateOfHire:    time_utils.NewDate(2021, 0o2, 0o5).ToTime(),
				},
				YearsOfExp:   5,
				IsIncluded:   true,
				IsOutOfState: false,
			},
			{
				DriverBasicDetails: nf_app.DriverBasicDetails{
					FirstName:     "test-f2",
					LastName:      "test-l2",
					LicenseState:  "CA",
					LicenseNumber: "456",
					DateOfBirth:   time_utils.NewDate(1980, 0o2, 0o5).ToTime(),
					DateOfHire:    time_utils.NewDate(2021, 0o2, 0o5).ToTime(),
				},
				YearsOfExp:   5,
				IsIncluded:   false,
				IsOutOfState: true,
			},
			{
				DriverBasicDetails: nf_app.DriverBasicDetails{
					FirstName:     "test-f3",
					LastName:      "test-l3",
					LicenseState:  "CA",
					LicenseNumber: "789",
					DateOfBirth:   time_utils.NewDate(1980, 0o2, 0o5).ToTime(),
					DateOfHire:    time_utils.NewDate(2021, 0o2, 0o5).ToTime(),
				},
				YearsOfExp:   5,
				IsIncluded:   true,
				IsOutOfState: true,
			},
		},
	}
	err := admittedAppWrapper.InsertApp(ctx, appObj)
	require.NoError(t, err)

	subObj := admitted_app.MockSubmissionImpl()
	err = admittedAppWrapper.InsertSubmission(ctx, subObj)
	require.NoError(t, err)

	appReviewObj := &application_review.AdmittedApplicationReview{
		ApplicationID: uuid.MustParse(appObj.ID.String()),
		ID:            uuid.New(),
		SubmissionID:  uuid.MustParse(subObj.ID.String()),
		MVRPulled:     false,
		EffectiveDate: time_utils.NewDate(2023, 0o7, 29).ToTime(),
		DriversInfo: &application_review.DriversInfo{
			Drivers: map[string]application_review.Driver{
				"123": {
					IsExcluded:   false,
					IsOutOfState: false,
					Violations: []application_review.Violation{
						{
							Code:                    "111900",
							Date:                    time_utils.NewDate(2023, 0o3, 29).ToTime(),
							ActualPoints:            3,
							IsManuallyAdded:         false,
							IsExpired:               false,
							ShouldConsiderForCounts: true,
						},
						{
							Code:                    "111900",
							Date:                    time_utils.NewDate(2023, 0o4, 29).ToTime(),
							ActualPoints:            9,
							IsManuallyAdded:         false,
							IsExpired:               false,
							ShouldConsiderForCounts: true,
							OriginalPoints:          pointer_utils.ToPointer(3),
						},
						{
							Code:                    "111835",
							Date:                    time_utils.NewDate(2018, 0o4, 29).ToTime(),
							ActualPoints:            3,
							IsManuallyAdded:         false,
							IsExpired:               true,
							ShouldConsiderForCounts: false,
							OriginalPoints:          pointer_utils.ToPointer(3),
						},
						{
							Code:                    "111830",
							Date:                    time_utils.NewDate(2021, 0o4, 29).ToTime(),
							ActualPoints:            3,
							IsManuallyAdded:         false,
							IsExpired:               false,
							ShouldConsiderForCounts: true,
							OriginalPoints:          pointer_utils.ToPointer(3),
						},
						{
							Code:                    "111830",
							Date:                    time_utils.NewDate(2020, 0o4, 29).ToTime(),
							ActualPoints:            3,
							IsManuallyAdded:         false,
							IsExpired:               true,
							ShouldConsiderForCounts: true,
							OriginalPoints:          pointer_utils.ToPointer(3),
						},
					},
				},
				"456": {
					IsExcluded:   true,
					IsOutOfState: true,
					Violations: []application_review.Violation{
						{
							Code:                    "111900",
							Date:                    time_utils.NewDate(2023, 0o3, 29).ToTime(),
							ActualPoints:            3,
							IsManuallyAdded:         false,
							IsExpired:               false,
							ShouldConsiderForCounts: true,
						},
					},
				},
				"789": {
					IsExcluded:   false,
					IsOutOfState: true,
					Violations: []application_review.Violation{
						{
							Code:                    "111900",
							Date:                    time_utils.NewDate(2023, 0o3, 29).ToTime(),
							ActualPoints:            3,
							IsManuallyAdded:         false,
							IsExpired:               false,
							ShouldConsiderForCounts: true,
						},
					},
				},
			},
		},
	}
	err = appReviewWrapper.InsertApplicationReview(ctx, appReviewObj)
	require.NoError(t, err)
	return appReviewObj
}

func compareOverrides(
	t *testing.T,
	got application_review.Overrides,
	want application_review.Overrides,
) {
	assert.Equal(t, *got.CountViolationClassA, *want.CountViolationClassA)
	assert.Equal(t, *got.CountViolationClassB, *want.CountViolationClassB)
	assert.Equal(t, *got.CountViolationClassC, *want.CountViolationClassC)
	assert.Equal(t, *got.CountViolationClassD, *want.CountViolationClassD)
	assert.Equal(t, *got.CountViolationClassE, *want.CountViolationClassE)
	assert.Equal(t, *got.CountViolationClassF, *want.CountViolationClassF)
	assert.Equal(t, *got.CountViolationClassN, *want.CountViolationClassN)
	assert.Equal(t, *got.CountViolationClassNA, *want.CountViolationClassNA)
	gotDriverVios := *got.DriverViolations
	wantDriverVios := *want.DriverViolations
	sort.Slice(gotDriverVios, func(i, j int) bool {
		return gotDriverVios[i].LicenseNumber < gotDriverVios[j].LicenseNumber
	})
	sort.Slice(wantDriverVios, func(i, j int) bool {
		return wantDriverVios[i].LicenseNumber < wantDriverVios[j].LicenseNumber
	})
	assert.DeepEqual(t, gotDriverVios, wantDriverVios)
	assert.DeepEqual(t, got.ViolationPoints, want.ViolationPoints)
	assert.DeepEqual(t, got.OutOfStateDrivers, want.OutOfStateDrivers)
	assert.DeepEqual(t, got.ExcludedDrivers, want.ExcludedDrivers)
}

// Tests for the enhanced driver functionality with DriverViolationDetail

func TestGetDriverVioOverridesAndInfo_MVRReportAlreadyPulled(t *testing.T) {
	// Test that when DriverViolationDetail is provided, the method uses the pre-fetched data
	// instead of calling the external MVR service

	ctx := context.Background()
	effectiveDate := time.Now()

	driverDetails := []nf_app.DriverBasicDetails{
		{
			LicenseNumber: "TEST123",
		},
	}

	// Create pre-fetched violation details
	preViolationDetails := DriverViolationDetail{
		Records: []mvr.DriverViolationRecord{
			{
				LicenseNumber: "TEST123",
				Violations:    []mvr.ViolationData{},
			},
		},
		MVRReports: []*data_fetching.MVRReportV1{
			{
				ReportID: uuid.New().String(),
				Violations: []*data_fetching.MVRViolationV1{
					{
						AssignedViolationCode: "1",
						ViolationDate:         timestamppb.New(time.Now().Add(-5 * time_utils.Day)),
					},
					{
						AssignedViolationCode: "1",
						ViolationDate:         timestamppb.New(time.Now().Add(-5 * time_utils.Day)),
					},
				}},
		},
		Errors: []error{nil},
	}

	// Call the method with pre-fetched data
	_, records, err := GetDriverVioOverridesAndInfo(
		ctx,
		effectiveDate,
		driverDetails,
		enums.ProgramTypeNonFleetAdmitted, // programType
		preViolationDetails,
	)

	// Verify no error and that data was processed
	require.NoError(t, err)
	// Just check that we got valid results back
	assert.Assert(t, records != nil, "records should not be nil")
}
