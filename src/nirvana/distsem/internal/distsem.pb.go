// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: distsem/distsem.proto

package internal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AllocationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string               `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	ResourceId string               `protobuf:"bytes,2,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	Count      int64                `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	Priority   int64                `protobuf:"varint,4,opt,name=priority,proto3" json:"priority,omitempty"`
	Timeout    *durationpb.Duration `protobuf:"bytes,5,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *AllocationRequest) Reset() {
	*x = AllocationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllocationRequest) ProtoMessage() {}

func (x *AllocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllocationRequest.ProtoReflect.Descriptor instead.
func (*AllocationRequest) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{0}
}

func (x *AllocationRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AllocationRequest) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *AllocationRequest) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *AllocationRequest) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *AllocationRequest) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type AllocationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AllocationId   string                 `protobuf:"bytes,1,opt,name=allocationId,proto3" json:"allocationId,omitempty"`
	ResourceId     string                 `protobuf:"bytes,2,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	UserId         string                 `protobuf:"bytes,3,opt,name=userId,proto3" json:"userId,omitempty"`
	Count          int64                  `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	LastUpdateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=lastUpdateTime,proto3" json:"lastUpdateTime,omitempty"`
}

func (x *AllocationResponse) Reset() {
	*x = AllocationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllocationResponse) ProtoMessage() {}

func (x *AllocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllocationResponse.ProtoReflect.Descriptor instead.
func (*AllocationResponse) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{1}
}

func (x *AllocationResponse) GetAllocationId() string {
	if x != nil {
		return x.AllocationId
	}
	return ""
}

func (x *AllocationResponse) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *AllocationResponse) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AllocationResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *AllocationResponse) GetLastUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdateTime
	}
	return nil
}

type ReleaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	ResourceId string `protobuf:"bytes,2,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
}

func (x *ReleaseRequest) Reset() {
	*x = ReleaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseRequest) ProtoMessage() {}

func (x *ReleaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseRequest.ProtoReflect.Descriptor instead.
func (*ReleaseRequest) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{2}
}

func (x *ReleaseRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ReleaseRequest) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

type ReleaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReleaseResponse) Reset() {
	*x = ReleaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseResponse) ProtoMessage() {}

func (x *ReleaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseResponse.ProtoReflect.Descriptor instead.
func (*ReleaseResponse) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{3}
}

type GetAllocationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	ResourceId string `protobuf:"bytes,2,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
}

func (x *GetAllocationRequest) Reset() {
	*x = GetAllocationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllocationRequest) ProtoMessage() {}

func (x *GetAllocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllocationRequest.ProtoReflect.Descriptor instead.
func (*GetAllocationRequest) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{4}
}

func (x *GetAllocationRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetAllocationRequest) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

type CreateResourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceId  string `protobuf:"bytes,1,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	Total       int64  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *CreateResourceRequest) Reset() {
	*x = CreateResourceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResourceRequest) ProtoMessage() {}

func (x *CreateResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResourceRequest.ProtoReflect.Descriptor instead.
func (*CreateResourceRequest) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{5}
}

func (x *CreateResourceRequest) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *CreateResourceRequest) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *CreateResourceRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type CreateResourceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateResourceResponse) Reset() {
	*x = CreateResourceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateResourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResourceResponse) ProtoMessage() {}

func (x *CreateResourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResourceResponse.ProtoReflect.Descriptor instead.
func (*CreateResourceResponse) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{6}
}

type UpdateCapacityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceId string `protobuf:"bytes,1,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	Total      int64  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *UpdateCapacityRequest) Reset() {
	*x = UpdateCapacityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCapacityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCapacityRequest) ProtoMessage() {}

func (x *UpdateCapacityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCapacityRequest.ProtoReflect.Descriptor instead.
func (*UpdateCapacityRequest) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateCapacityRequest) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *UpdateCapacityRequest) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type UpdateCapacityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateCapacityResponse) Reset() {
	*x = UpdateCapacityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCapacityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCapacityResponse) ProtoMessage() {}

func (x *UpdateCapacityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCapacityResponse.ProtoReflect.Descriptor instead.
func (*UpdateCapacityResponse) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{8}
}

type GetResourceInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceId string `protobuf:"bytes,1,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
}

func (x *GetResourceInfoRequest) Reset() {
	*x = GetResourceInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResourceInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceInfoRequest) ProtoMessage() {}

func (x *GetResourceInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceInfoRequest.ProtoReflect.Descriptor instead.
func (*GetResourceInfoRequest) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{9}
}

func (x *GetResourceInfoRequest) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

type GetResourceInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceId  string `protobuf:"bytes,1,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	Free        int64  `protobuf:"varint,2,opt,name=free,proto3" json:"free,omitempty"`
	Total       int64  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *GetResourceInfoResponse) Reset() {
	*x = GetResourceInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResourceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceInfoResponse) ProtoMessage() {}

func (x *GetResourceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceInfoResponse.ProtoReflect.Descriptor instead.
func (*GetResourceInfoResponse) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{10}
}

func (x *GetResourceInfoResponse) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *GetResourceInfoResponse) GetFree() int64 {
	if x != nil {
		return x.Free
	}
	return 0
}

func (x *GetResourceInfoResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetResourceInfoResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type GetResourceStatsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceId       string `protobuf:"bytes,1,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	LimitAllocations *int32 `protobuf:"varint,2,opt,name=limitAllocations,proto3,oneof" json:"limitAllocations,omitempty"`
	LimitPending     *int32 `protobuf:"varint,3,opt,name=limitPending,proto3,oneof" json:"limitPending,omitempty"`
}

func (x *GetResourceStatsRequest) Reset() {
	*x = GetResourceStatsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResourceStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceStatsRequest) ProtoMessage() {}

func (x *GetResourceStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceStatsRequest.ProtoReflect.Descriptor instead.
func (*GetResourceStatsRequest) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{11}
}

func (x *GetResourceStatsRequest) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *GetResourceStatsRequest) GetLimitAllocations() int32 {
	if x != nil && x.LimitAllocations != nil {
		return *x.LimitAllocations
	}
	return 0
}

func (x *GetResourceStatsRequest) GetLimitPending() int32 {
	if x != nil && x.LimitPending != nil {
		return *x.LimitPending
	}
	return 0
}

type GetResourceStatsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceId   string                `protobuf:"bytes,1,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	Free         int64                 `protobuf:"varint,2,opt,name=free,proto3" json:"free,omitempty"`
	Total        int64                 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	CountPending int32                 `protobuf:"varint,4,opt,name=countPending,proto3" json:"countPending,omitempty"`
	Allocations  []*AllocationResponse `protobuf:"bytes,5,rep,name=allocations,proto3" json:"allocations,omitempty"`
	Pending      []*AllocationRequest  `protobuf:"bytes,6,rep,name=pending,proto3" json:"pending,omitempty"`
}

func (x *GetResourceStatsResponse) Reset() {
	*x = GetResourceStatsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResourceStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceStatsResponse) ProtoMessage() {}

func (x *GetResourceStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceStatsResponse.ProtoReflect.Descriptor instead.
func (*GetResourceStatsResponse) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{12}
}

func (x *GetResourceStatsResponse) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *GetResourceStatsResponse) GetFree() int64 {
	if x != nil {
		return x.Free
	}
	return 0
}

func (x *GetResourceStatsResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetResourceStatsResponse) GetCountPending() int32 {
	if x != nil {
		return x.CountPending
	}
	return 0
}

func (x *GetResourceStatsResponse) GetAllocations() []*AllocationResponse {
	if x != nil {
		return x.Allocations
	}
	return nil
}

func (x *GetResourceStatsResponse) GetPending() []*AllocationRequest {
	if x != nil {
		return x.Pending
	}
	return nil
}

type DeleteResourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceId string `protobuf:"bytes,1,opt,name=resourceId,proto3" json:"resourceId,omitempty"`
	Force      bool   `protobuf:"varint,2,opt,name=force,proto3" json:"force,omitempty"`
}

func (x *DeleteResourceRequest) Reset() {
	*x = DeleteResourceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteResourceRequest) ProtoMessage() {}

func (x *DeleteResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteResourceRequest.ProtoReflect.Descriptor instead.
func (*DeleteResourceRequest) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteResourceRequest) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *DeleteResourceRequest) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

type DeleteResourceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteResourceResponse) Reset() {
	*x = DeleteResourceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_distsem_distsem_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteResourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteResourceResponse) ProtoMessage() {}

func (x *DeleteResourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_distsem_distsem_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteResourceResponse.ProtoReflect.Descriptor instead.
func (*DeleteResourceResponse) Descriptor() ([]byte, []int) {
	return file_distsem_distsem_proto_rawDescGZIP(), []int{14}
}

var File_distsem_distsem_proto protoreflect.FileDescriptor

var file_distsem_distsem_proto_rawDesc = []byte{
	0x0a, 0x15, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x2f, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xb2, 0x01, 0x0a, 0x11, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22, 0xca, 0x01, 0x0a, 0x12, 0x41, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x42, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x48, 0x0a, 0x0e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x22, 0x11, 0x0a,
	0x0f, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x4e, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x22, 0x6f, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x18, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4d, 0x0a, 0x15, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x18, 0x0a, 0x16, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x38, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x22, 0x85,
	0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72,
	0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x66, 0x72, 0x65, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb9, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x2f, 0x0a, 0x10, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x10,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x50, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x0c, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x22, 0xfd, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x66, 0x72, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x66,
	0x72, 0x65, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x3d, 0x0a,
	0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x41, 0x6c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x34, 0x0a, 0x07,
	0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x70, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x22, 0x4d, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x66,
	0x6f, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x22, 0x18, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x99, 0x05, 0x0a, 0x07,
	0x44, 0x69, 0x73, 0x74, 0x53, 0x65, 0x6d, 0x12, 0x4d, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x2e, 0x64, 0x69,
	0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65,
	0x6d, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3e, 0x0a, 0x07, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x12, 0x17, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x64, 0x69, 0x73,
	0x74, 0x73, 0x65, 0x6d, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65,
	0x6d, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d,
	0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1e, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65,
	0x6d, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65,
	0x6d, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x0e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x1e, 0x2e, 0x64,
	0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x64,
	0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x56, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1f, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x20, 0x2e, 0x64, 0x69,
	0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e,
	0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x53, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x1e, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_distsem_distsem_proto_rawDescOnce sync.Once
	file_distsem_distsem_proto_rawDescData = file_distsem_distsem_proto_rawDesc
)

func file_distsem_distsem_proto_rawDescGZIP() []byte {
	file_distsem_distsem_proto_rawDescOnce.Do(func() {
		file_distsem_distsem_proto_rawDescData = protoimpl.X.CompressGZIP(file_distsem_distsem_proto_rawDescData)
	})
	return file_distsem_distsem_proto_rawDescData
}

var file_distsem_distsem_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_distsem_distsem_proto_goTypes = []interface{}{
	(*AllocationRequest)(nil),        // 0: distsem.AllocationRequest
	(*AllocationResponse)(nil),       // 1: distsem.AllocationResponse
	(*ReleaseRequest)(nil),           // 2: distsem.ReleaseRequest
	(*ReleaseResponse)(nil),          // 3: distsem.ReleaseResponse
	(*GetAllocationRequest)(nil),     // 4: distsem.GetAllocationRequest
	(*CreateResourceRequest)(nil),    // 5: distsem.CreateResourceRequest
	(*CreateResourceResponse)(nil),   // 6: distsem.CreateResourceResponse
	(*UpdateCapacityRequest)(nil),    // 7: distsem.UpdateCapacityRequest
	(*UpdateCapacityResponse)(nil),   // 8: distsem.UpdateCapacityResponse
	(*GetResourceInfoRequest)(nil),   // 9: distsem.GetResourceInfoRequest
	(*GetResourceInfoResponse)(nil),  // 10: distsem.GetResourceInfoResponse
	(*GetResourceStatsRequest)(nil),  // 11: distsem.GetResourceStatsRequest
	(*GetResourceStatsResponse)(nil), // 12: distsem.GetResourceStatsResponse
	(*DeleteResourceRequest)(nil),    // 13: distsem.DeleteResourceRequest
	(*DeleteResourceResponse)(nil),   // 14: distsem.DeleteResourceResponse
	(*durationpb.Duration)(nil),      // 15: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),    // 16: google.protobuf.Timestamp
}
var file_distsem_distsem_proto_depIdxs = []int32{
	15, // 0: distsem.AllocationRequest.timeout:type_name -> google.protobuf.Duration
	16, // 1: distsem.AllocationResponse.lastUpdateTime:type_name -> google.protobuf.Timestamp
	1,  // 2: distsem.GetResourceStatsResponse.allocations:type_name -> distsem.AllocationResponse
	0,  // 3: distsem.GetResourceStatsResponse.pending:type_name -> distsem.AllocationRequest
	0,  // 4: distsem.DistSem.UpdateAllocation:input_type -> distsem.AllocationRequest
	2,  // 5: distsem.DistSem.Release:input_type -> distsem.ReleaseRequest
	4,  // 6: distsem.DistSem.GetAllocation:input_type -> distsem.GetAllocationRequest
	5,  // 7: distsem.DistSem.CreateResource:input_type -> distsem.CreateResourceRequest
	7,  // 8: distsem.DistSem.UpdateCapacity:input_type -> distsem.UpdateCapacityRequest
	9,  // 9: distsem.DistSem.GetResourceInfo:input_type -> distsem.GetResourceInfoRequest
	11, // 10: distsem.DistSem.GetResourceStats:input_type -> distsem.GetResourceStatsRequest
	13, // 11: distsem.DistSem.DeleteResource:input_type -> distsem.DeleteResourceRequest
	1,  // 12: distsem.DistSem.UpdateAllocation:output_type -> distsem.AllocationResponse
	3,  // 13: distsem.DistSem.Release:output_type -> distsem.ReleaseResponse
	1,  // 14: distsem.DistSem.GetAllocation:output_type -> distsem.AllocationResponse
	6,  // 15: distsem.DistSem.CreateResource:output_type -> distsem.CreateResourceResponse
	8,  // 16: distsem.DistSem.UpdateCapacity:output_type -> distsem.UpdateCapacityResponse
	10, // 17: distsem.DistSem.GetResourceInfo:output_type -> distsem.GetResourceInfoResponse
	12, // 18: distsem.DistSem.GetResourceStats:output_type -> distsem.GetResourceStatsResponse
	14, // 19: distsem.DistSem.DeleteResource:output_type -> distsem.DeleteResourceResponse
	12, // [12:20] is the sub-list for method output_type
	4,  // [4:12] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_distsem_distsem_proto_init() }
func file_distsem_distsem_proto_init() {
	if File_distsem_distsem_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_distsem_distsem_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllocationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllocationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllocationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateResourceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateResourceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCapacityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCapacityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetResourceInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetResourceInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetResourceStatsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetResourceStatsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteResourceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_distsem_distsem_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteResourceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_distsem_distsem_proto_msgTypes[11].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_distsem_distsem_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_distsem_distsem_proto_goTypes,
		DependencyIndexes: file_distsem_distsem_proto_depIdxs,
		MessageInfos:      file_distsem_distsem_proto_msgTypes,
	}.Build()
	File_distsem_distsem_proto = out.File
	file_distsem_distsem_proto_rawDesc = nil
	file_distsem_distsem_proto_goTypes = nil
	file_distsem_distsem_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// DistSemClient is the client API for DistSem service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DistSemClient interface {
	UpdateAllocation(ctx context.Context, in *AllocationRequest, opts ...grpc.CallOption) (*AllocationResponse, error)
	Release(ctx context.Context, in *ReleaseRequest, opts ...grpc.CallOption) (*ReleaseResponse, error)
	GetAllocation(ctx context.Context, in *GetAllocationRequest, opts ...grpc.CallOption) (*AllocationResponse, error)
	CreateResource(ctx context.Context, in *CreateResourceRequest, opts ...grpc.CallOption) (*CreateResourceResponse, error)
	UpdateCapacity(ctx context.Context, in *UpdateCapacityRequest, opts ...grpc.CallOption) (*UpdateCapacityResponse, error)
	GetResourceInfo(ctx context.Context, in *GetResourceInfoRequest, opts ...grpc.CallOption) (*GetResourceInfoResponse, error)
	GetResourceStats(ctx context.Context, in *GetResourceStatsRequest, opts ...grpc.CallOption) (*GetResourceStatsResponse, error)
	DeleteResource(ctx context.Context, in *DeleteResourceRequest, opts ...grpc.CallOption) (*DeleteResourceResponse, error)
}

type distSemClient struct {
	cc grpc.ClientConnInterface
}

func NewDistSemClient(cc grpc.ClientConnInterface) DistSemClient {
	return &distSemClient{cc}
}

func (c *distSemClient) UpdateAllocation(ctx context.Context, in *AllocationRequest, opts ...grpc.CallOption) (*AllocationResponse, error) {
	out := new(AllocationResponse)
	err := c.cc.Invoke(ctx, "/distsem.DistSem/UpdateAllocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distSemClient) Release(ctx context.Context, in *ReleaseRequest, opts ...grpc.CallOption) (*ReleaseResponse, error) {
	out := new(ReleaseResponse)
	err := c.cc.Invoke(ctx, "/distsem.DistSem/Release", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distSemClient) GetAllocation(ctx context.Context, in *GetAllocationRequest, opts ...grpc.CallOption) (*AllocationResponse, error) {
	out := new(AllocationResponse)
	err := c.cc.Invoke(ctx, "/distsem.DistSem/GetAllocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distSemClient) CreateResource(ctx context.Context, in *CreateResourceRequest, opts ...grpc.CallOption) (*CreateResourceResponse, error) {
	out := new(CreateResourceResponse)
	err := c.cc.Invoke(ctx, "/distsem.DistSem/CreateResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distSemClient) UpdateCapacity(ctx context.Context, in *UpdateCapacityRequest, opts ...grpc.CallOption) (*UpdateCapacityResponse, error) {
	out := new(UpdateCapacityResponse)
	err := c.cc.Invoke(ctx, "/distsem.DistSem/UpdateCapacity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distSemClient) GetResourceInfo(ctx context.Context, in *GetResourceInfoRequest, opts ...grpc.CallOption) (*GetResourceInfoResponse, error) {
	out := new(GetResourceInfoResponse)
	err := c.cc.Invoke(ctx, "/distsem.DistSem/GetResourceInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distSemClient) GetResourceStats(ctx context.Context, in *GetResourceStatsRequest, opts ...grpc.CallOption) (*GetResourceStatsResponse, error) {
	out := new(GetResourceStatsResponse)
	err := c.cc.Invoke(ctx, "/distsem.DistSem/GetResourceStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *distSemClient) DeleteResource(ctx context.Context, in *DeleteResourceRequest, opts ...grpc.CallOption) (*DeleteResourceResponse, error) {
	out := new(DeleteResourceResponse)
	err := c.cc.Invoke(ctx, "/distsem.DistSem/DeleteResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DistSemServer is the server API for DistSem service.
type DistSemServer interface {
	UpdateAllocation(context.Context, *AllocationRequest) (*AllocationResponse, error)
	Release(context.Context, *ReleaseRequest) (*ReleaseResponse, error)
	GetAllocation(context.Context, *GetAllocationRequest) (*AllocationResponse, error)
	CreateResource(context.Context, *CreateResourceRequest) (*CreateResourceResponse, error)
	UpdateCapacity(context.Context, *UpdateCapacityRequest) (*UpdateCapacityResponse, error)
	GetResourceInfo(context.Context, *GetResourceInfoRequest) (*GetResourceInfoResponse, error)
	GetResourceStats(context.Context, *GetResourceStatsRequest) (*GetResourceStatsResponse, error)
	DeleteResource(context.Context, *DeleteResourceRequest) (*DeleteResourceResponse, error)
}

// UnimplementedDistSemServer can be embedded to have forward compatible implementations.
type UnimplementedDistSemServer struct {
}

func (*UnimplementedDistSemServer) UpdateAllocation(context.Context, *AllocationRequest) (*AllocationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAllocation not implemented")
}
func (*UnimplementedDistSemServer) Release(context.Context, *ReleaseRequest) (*ReleaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Release not implemented")
}
func (*UnimplementedDistSemServer) GetAllocation(context.Context, *GetAllocationRequest) (*AllocationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllocation not implemented")
}
func (*UnimplementedDistSemServer) CreateResource(context.Context, *CreateResourceRequest) (*CreateResourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateResource not implemented")
}
func (*UnimplementedDistSemServer) UpdateCapacity(context.Context, *UpdateCapacityRequest) (*UpdateCapacityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCapacity not implemented")
}
func (*UnimplementedDistSemServer) GetResourceInfo(context.Context, *GetResourceInfoRequest) (*GetResourceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResourceInfo not implemented")
}
func (*UnimplementedDistSemServer) GetResourceStats(context.Context, *GetResourceStatsRequest) (*GetResourceStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResourceStats not implemented")
}
func (*UnimplementedDistSemServer) DeleteResource(context.Context, *DeleteResourceRequest) (*DeleteResourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteResource not implemented")
}

func RegisterDistSemServer(s *grpc.Server, srv DistSemServer) {
	s.RegisterService(&_DistSem_serviceDesc, srv)
}

func _DistSem_UpdateAllocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllocationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistSemServer).UpdateAllocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/distsem.DistSem/UpdateAllocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistSemServer).UpdateAllocation(ctx, req.(*AllocationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistSem_Release_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistSemServer).Release(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/distsem.DistSem/Release",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistSemServer).Release(ctx, req.(*ReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistSem_GetAllocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllocationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistSemServer).GetAllocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/distsem.DistSem/GetAllocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistSemServer).GetAllocation(ctx, req.(*GetAllocationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistSem_CreateResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistSemServer).CreateResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/distsem.DistSem/CreateResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistSemServer).CreateResource(ctx, req.(*CreateResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistSem_UpdateCapacity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCapacityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistSemServer).UpdateCapacity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/distsem.DistSem/UpdateCapacity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistSemServer).UpdateCapacity(ctx, req.(*UpdateCapacityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistSem_GetResourceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetResourceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistSemServer).GetResourceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/distsem.DistSem/GetResourceInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistSemServer).GetResourceInfo(ctx, req.(*GetResourceInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistSem_GetResourceStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetResourceStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistSemServer).GetResourceStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/distsem.DistSem/GetResourceStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistSemServer).GetResourceStats(ctx, req.(*GetResourceStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DistSem_DeleteResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DistSemServer).DeleteResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/distsem.DistSem/DeleteResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DistSemServer).DeleteResource(ctx, req.(*DeleteResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DistSem_serviceDesc = grpc.ServiceDesc{
	ServiceName: "distsem.DistSem",
	HandlerType: (*DistSemServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateAllocation",
			Handler:    _DistSem_UpdateAllocation_Handler,
		},
		{
			MethodName: "Release",
			Handler:    _DistSem_Release_Handler,
		},
		{
			MethodName: "GetAllocation",
			Handler:    _DistSem_GetAllocation_Handler,
		},
		{
			MethodName: "CreateResource",
			Handler:    _DistSem_CreateResource_Handler,
		},
		{
			MethodName: "UpdateCapacity",
			Handler:    _DistSem_UpdateCapacity_Handler,
		},
		{
			MethodName: "GetResourceInfo",
			Handler:    _DistSem_GetResourceInfo_Handler,
		},
		{
			MethodName: "GetResourceStats",
			Handler:    _DistSem_GetResourceStats_Handler,
		},
		{
			MethodName: "DeleteResource",
			Handler:    _DistSem_DeleteResource_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "distsem/distsem.proto",
}
