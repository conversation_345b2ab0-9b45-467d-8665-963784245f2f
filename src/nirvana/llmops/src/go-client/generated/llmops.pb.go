// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: llmops/llmops.proto

package generated

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CallType int32

const (
	CallType_UNKNOWN                                 CallType = 0
	CallType_GENERATE_CLAIM_UPDATE_SUMMARY           CallType = 1
	CallType_PROCESS_FNOL_INTAKE                     CallType = 2
	CallType_PROCESS_FNOL_INTAKE_V2                  CallType = 3
	CallType_GENERATE_SNAPSHEET_CLAIM_UPDATE_SUMMARY CallType = 4
)

// Enum value maps for CallType.
var (
	CallType_name = map[int32]string{
		0: "UNKNOWN",
		1: "GENERATE_CLAIM_UPDATE_SUMMARY",
		2: "PROCESS_FNOL_INTAKE",
		3: "PROCESS_FNOL_INTAKE_V2",
		4: "GENERATE_SNAPSHEET_CLAIM_UPDATE_SUMMARY",
	}
	CallType_value = map[string]int32{
		"UNKNOWN":                                 0,
		"GENERATE_CLAIM_UPDATE_SUMMARY":           1,
		"PROCESS_FNOL_INTAKE":                     2,
		"PROCESS_FNOL_INTAKE_V2":                  3,
		"GENERATE_SNAPSHEET_CLAIM_UPDATE_SUMMARY": 4,
	}
)

func (x CallType) Enum() *CallType {
	p := new(CallType)
	*p = x
	return p
}

func (x CallType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallType) Descriptor() protoreflect.EnumDescriptor {
	return file_llmops_llmops_proto_enumTypes[0].Descriptor()
}

func (CallType) Type() protoreflect.EnumType {
	return &file_llmops_llmops_proto_enumTypes[0]
}

func (x CallType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallType.Descriptor instead.
func (CallType) EnumDescriptor() ([]byte, []int) {
	return file_llmops_llmops_proto_rawDescGZIP(), []int{0}
}

type CallRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CallType CallType `protobuf:"varint,1,opt,name=call_type,json=callType,proto3,enum=llmops.CallType" json:"call_type,omitempty"`
	// Types that are assignable to Payload:
	//
	//	*CallRequest_ClaimNotes
	//	*CallRequest_FnolEmail
	Payload isCallRequest_Payload `protobuf_oneof:"payload"`
}

func (x *CallRequest) Reset() {
	*x = CallRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_llmops_llmops_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallRequest) ProtoMessage() {}

func (x *CallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_llmops_llmops_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallRequest.ProtoReflect.Descriptor instead.
func (*CallRequest) Descriptor() ([]byte, []int) {
	return file_llmops_llmops_proto_rawDescGZIP(), []int{0}
}

func (x *CallRequest) GetCallType() CallType {
	if x != nil {
		return x.CallType
	}
	return CallType_UNKNOWN
}

func (m *CallRequest) GetPayload() isCallRequest_Payload {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (x *CallRequest) GetClaimNotes() *ClaimNotes {
	if x, ok := x.GetPayload().(*CallRequest_ClaimNotes); ok {
		return x.ClaimNotes
	}
	return nil
}

func (x *CallRequest) GetFnolEmail() *FnolEmail {
	if x, ok := x.GetPayload().(*CallRequest_FnolEmail); ok {
		return x.FnolEmail
	}
	return nil
}

type isCallRequest_Payload interface {
	isCallRequest_Payload()
}

type CallRequest_ClaimNotes struct {
	ClaimNotes *ClaimNotes `protobuf:"bytes,2,opt,name=claim_notes,json=claimNotes,proto3,oneof"`
}

type CallRequest_FnolEmail struct {
	FnolEmail *FnolEmail `protobuf:"bytes,3,opt,name=fnol_email,json=fnolEmail,proto3,oneof"`
}

func (*CallRequest_ClaimNotes) isCallRequest_Payload() {}

func (*CallRequest_FnolEmail) isCallRequest_Payload() {}

type CallResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Result:
	//
	//	*CallResponse_ClaimSummary
	//	*CallResponse_FnolIntake
	Result  isCallResponse_Result `protobuf_oneof:"result"`
	TraceId string                `protobuf:"bytes,3,opt,name=traceId,proto3" json:"traceId,omitempty"`
}

func (x *CallResponse) Reset() {
	*x = CallResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_llmops_llmops_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallResponse) ProtoMessage() {}

func (x *CallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_llmops_llmops_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallResponse.ProtoReflect.Descriptor instead.
func (*CallResponse) Descriptor() ([]byte, []int) {
	return file_llmops_llmops_proto_rawDescGZIP(), []int{1}
}

func (m *CallResponse) GetResult() isCallResponse_Result {
	if m != nil {
		return m.Result
	}
	return nil
}

func (x *CallResponse) GetClaimSummary() *ClaimSummary {
	if x, ok := x.GetResult().(*CallResponse_ClaimSummary); ok {
		return x.ClaimSummary
	}
	return nil
}

func (x *CallResponse) GetFnolIntake() *FnolIntake {
	if x, ok := x.GetResult().(*CallResponse_FnolIntake); ok {
		return x.FnolIntake
	}
	return nil
}

func (x *CallResponse) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

type isCallResponse_Result interface {
	isCallResponse_Result()
}

type CallResponse_ClaimSummary struct {
	ClaimSummary *ClaimSummary `protobuf:"bytes,1,opt,name=claim_summary,json=claimSummary,proto3,oneof"`
}

type CallResponse_FnolIntake struct {
	FnolIntake *FnolIntake `protobuf:"bytes,2,opt,name=fnol_intake,json=fnolIntake,proto3,oneof"`
}

func (*CallResponse_ClaimSummary) isCallResponse_Result() {}

func (*CallResponse_FnolIntake) isCallResponse_Result() {}

type ClaimNotes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Notes []string `protobuf:"bytes,1,rep,name=notes,proto3" json:"notes,omitempty"`
}

func (x *ClaimNotes) Reset() {
	*x = ClaimNotes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_llmops_llmops_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimNotes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimNotes) ProtoMessage() {}

func (x *ClaimNotes) ProtoReflect() protoreflect.Message {
	mi := &file_llmops_llmops_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimNotes.ProtoReflect.Descriptor instead.
func (*ClaimNotes) Descriptor() ([]byte, []int) {
	return file_llmops_llmops_proto_rawDescGZIP(), []int{2}
}

func (x *ClaimNotes) GetNotes() []string {
	if x != nil {
		return x.Notes
	}
	return nil
}

type ClaimSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Summary string `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary,omitempty"`
	Title   string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *ClaimSummary) Reset() {
	*x = ClaimSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_llmops_llmops_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimSummary) ProtoMessage() {}

func (x *ClaimSummary) ProtoReflect() protoreflect.Message {
	mi := &file_llmops_llmops_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimSummary.ProtoReflect.Descriptor instead.
func (*ClaimSummary) Descriptor() ([]byte, []int) {
	return file_llmops_llmops_proto_rawDescGZIP(), []int{3}
}

func (x *ClaimSummary) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *ClaimSummary) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type ReporterInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReporterFirstName string `protobuf:"bytes,1,opt,name=reporter_first_name,json=reporterFirstName,proto3" json:"reporter_first_name,omitempty"`
	ReporterLastName  string `protobuf:"bytes,2,opt,name=reporter_last_name,json=reporterLastName,proto3" json:"reporter_last_name,omitempty"`
	ReporterEmail     string `protobuf:"bytes,3,opt,name=reporter_email,json=reporterEmail,proto3" json:"reporter_email,omitempty"`
	ReporterPhone     string `protobuf:"bytes,4,opt,name=reporter_phone,json=reporterPhone,proto3" json:"reporter_phone,omitempty"`
	InsuredName       string `protobuf:"bytes,5,opt,name=insured_name,json=insuredName,proto3" json:"insured_name,omitempty"`
	NoticeType        string `protobuf:"bytes,6,opt,name=notice_type,json=noticeType,proto3" json:"notice_type,omitempty"`
}

func (x *ReporterInformation) Reset() {
	*x = ReporterInformation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_llmops_llmops_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReporterInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReporterInformation) ProtoMessage() {}

func (x *ReporterInformation) ProtoReflect() protoreflect.Message {
	mi := &file_llmops_llmops_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReporterInformation.ProtoReflect.Descriptor instead.
func (*ReporterInformation) Descriptor() ([]byte, []int) {
	return file_llmops_llmops_proto_rawDescGZIP(), []int{4}
}

func (x *ReporterInformation) GetReporterFirstName() string {
	if x != nil {
		return x.ReporterFirstName
	}
	return ""
}

func (x *ReporterInformation) GetReporterLastName() string {
	if x != nil {
		return x.ReporterLastName
	}
	return ""
}

func (x *ReporterInformation) GetReporterEmail() string {
	if x != nil {
		return x.ReporterEmail
	}
	return ""
}

func (x *ReporterInformation) GetReporterPhone() string {
	if x != nil {
		return x.ReporterPhone
	}
	return ""
}

func (x *ReporterInformation) GetInsuredName() string {
	if x != nil {
		return x.InsuredName
	}
	return ""
}

func (x *ReporterInformation) GetNoticeType() string {
	if x != nil {
		return x.NoticeType
	}
	return ""
}

type IncidentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateOfLoss               string   `protobuf:"bytes,1,opt,name=date_of_loss,json=dateOfLoss,proto3" json:"date_of_loss,omitempty"`
	State                    string   `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	LocationDetails          string   `protobuf:"bytes,3,opt,name=location_details,json=locationDetails,proto3" json:"location_details,omitempty"`
	PolicyNumber             string   `protobuf:"bytes,4,opt,name=policy_number,json=policyNumber,proto3" json:"policy_number,omitempty"`
	LineOfBusiness           string   `protobuf:"bytes,5,opt,name=line_of_business,json=lineOfBusiness,proto3" json:"line_of_business,omitempty"`
	OwnVehicleInvolved       bool     `protobuf:"varint,6,opt,name=own_vehicle_involved,json=ownVehicleInvolved,proto3" json:"own_vehicle_involved,omitempty"`
	OwnVehicleInvolvedVin    string   `protobuf:"bytes,7,opt,name=own_vehicle_involved_vin,json=ownVehicleInvolvedVin,proto3" json:"own_vehicle_involved_vin,omitempty"`
	OtherVehiclesInvolved    bool     `protobuf:"varint,8,opt,name=other_vehicles_involved,json=otherVehiclesInvolved,proto3" json:"other_vehicles_involved,omitempty"`
	OtherVehiclesInvolvedVin string   `protobuf:"bytes,9,opt,name=other_vehicles_involved_vin,json=otherVehiclesInvolvedVin,proto3" json:"other_vehicles_involved_vin,omitempty"`
	PoliceAtTheScene         bool     `protobuf:"varint,10,opt,name=police_at_the_scene,json=policeAtTheScene,proto3" json:"police_at_the_scene,omitempty"`
	PoliceAgencyName         string   `protobuf:"bytes,11,opt,name=police_agency_name,json=policeAgencyName,proto3" json:"police_agency_name,omitempty"`
	PoliceReportNumber       string   `protobuf:"bytes,12,opt,name=police_report_number,json=policeReportNumber,proto3" json:"police_report_number,omitempty"`
	InjuriesInvolved         bool     `protobuf:"varint,13,opt,name=injuries_involved,json=injuriesInvolved,proto3" json:"injuries_involved,omitempty"`
	IncidentDescription      string   `protobuf:"bytes,14,opt,name=incident_description,json=incidentDescription,proto3" json:"incident_description,omitempty"`
	Attachments              []string `protobuf:"bytes,15,rep,name=attachments,proto3" json:"attachments,omitempty"`
}

func (x *IncidentDetails) Reset() {
	*x = IncidentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_llmops_llmops_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncidentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncidentDetails) ProtoMessage() {}

func (x *IncidentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_llmops_llmops_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncidentDetails.ProtoReflect.Descriptor instead.
func (*IncidentDetails) Descriptor() ([]byte, []int) {
	return file_llmops_llmops_proto_rawDescGZIP(), []int{5}
}

func (x *IncidentDetails) GetDateOfLoss() string {
	if x != nil {
		return x.DateOfLoss
	}
	return ""
}

func (x *IncidentDetails) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *IncidentDetails) GetLocationDetails() string {
	if x != nil {
		return x.LocationDetails
	}
	return ""
}

func (x *IncidentDetails) GetPolicyNumber() string {
	if x != nil {
		return x.PolicyNumber
	}
	return ""
}

func (x *IncidentDetails) GetLineOfBusiness() string {
	if x != nil {
		return x.LineOfBusiness
	}
	return ""
}

func (x *IncidentDetails) GetOwnVehicleInvolved() bool {
	if x != nil {
		return x.OwnVehicleInvolved
	}
	return false
}

func (x *IncidentDetails) GetOwnVehicleInvolvedVin() string {
	if x != nil {
		return x.OwnVehicleInvolvedVin
	}
	return ""
}

func (x *IncidentDetails) GetOtherVehiclesInvolved() bool {
	if x != nil {
		return x.OtherVehiclesInvolved
	}
	return false
}

func (x *IncidentDetails) GetOtherVehiclesInvolvedVin() string {
	if x != nil {
		return x.OtherVehiclesInvolvedVin
	}
	return ""
}

func (x *IncidentDetails) GetPoliceAtTheScene() bool {
	if x != nil {
		return x.PoliceAtTheScene
	}
	return false
}

func (x *IncidentDetails) GetPoliceAgencyName() string {
	if x != nil {
		return x.PoliceAgencyName
	}
	return ""
}

func (x *IncidentDetails) GetPoliceReportNumber() string {
	if x != nil {
		return x.PoliceReportNumber
	}
	return ""
}

func (x *IncidentDetails) GetInjuriesInvolved() bool {
	if x != nil {
		return x.InjuriesInvolved
	}
	return false
}

func (x *IncidentDetails) GetIncidentDescription() string {
	if x != nil {
		return x.IncidentDescription
	}
	return ""
}

func (x *IncidentDetails) GetAttachments() []string {
	if x != nil {
		return x.Attachments
	}
	return nil
}

type FnolEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Body string `protobuf:"bytes,1,opt,name=body,proto3" json:"body,omitempty"`
}

func (x *FnolEmail) Reset() {
	*x = FnolEmail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_llmops_llmops_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FnolEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FnolEmail) ProtoMessage() {}

func (x *FnolEmail) ProtoReflect() protoreflect.Message {
	mi := &file_llmops_llmops_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FnolEmail.ProtoReflect.Descriptor instead.
func (*FnolEmail) Descriptor() ([]byte, []int) {
	return file_llmops_llmops_proto_rawDescGZIP(), []int{6}
}

func (x *FnolEmail) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

type FnolIntake struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReporterInformation *ReporterInformation `protobuf:"bytes,1,opt,name=reporter_information,json=reporterInformation,proto3" json:"reporter_information,omitempty"`
	IncidentDetails     *IncidentDetails     `protobuf:"bytes,2,opt,name=incident_details,json=incidentDetails,proto3" json:"incident_details,omitempty"`
	InvalidFnol         bool                 `protobuf:"varint,3,opt,name=invalid_fnol,json=invalidFnol,proto3" json:"invalid_fnol,omitempty"`
}

func (x *FnolIntake) Reset() {
	*x = FnolIntake{}
	if protoimpl.UnsafeEnabled {
		mi := &file_llmops_llmops_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FnolIntake) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FnolIntake) ProtoMessage() {}

func (x *FnolIntake) ProtoReflect() protoreflect.Message {
	mi := &file_llmops_llmops_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FnolIntake.ProtoReflect.Descriptor instead.
func (*FnolIntake) Descriptor() ([]byte, []int) {
	return file_llmops_llmops_proto_rawDescGZIP(), []int{7}
}

func (x *FnolIntake) GetReporterInformation() *ReporterInformation {
	if x != nil {
		return x.ReporterInformation
	}
	return nil
}

func (x *FnolIntake) GetIncidentDetails() *IncidentDetails {
	if x != nil {
		return x.IncidentDetails
	}
	return nil
}

func (x *FnolIntake) GetInvalidFnol() bool {
	if x != nil {
		return x.InvalidFnol
	}
	return false
}

var File_llmops_llmops_proto protoreflect.FileDescriptor

var file_llmops_llmops_proto_rawDesc = []byte{
	0x0a, 0x13, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2f, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x22, 0xb2, 0x01,
	0x0a, 0x0b, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a,
	0x09, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x10, 0x2e, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x0b,
	0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x4e, 0x6f, 0x74, 0x65, 0x73, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x4e, 0x6f,
	0x74, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x0a, 0x66, 0x6e, 0x6f, 0x6c, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73,
	0x2e, 0x46, 0x6e, 0x6f, 0x6c, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x09, 0x66, 0x6e,
	0x6f, 0x6c, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x22, 0xa6, 0x01, 0x0a, 0x0c, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x0d, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6c, 0x6c, 0x6d,
	0x6f, 0x70, 0x73, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x48, 0x00, 0x52, 0x0c, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x12, 0x35, 0x0a, 0x0b, 0x66, 0x6e, 0x6f, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2e, 0x46,
	0x6e, 0x6f, 0x6c, 0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x6e, 0x6f,
	0x6c, 0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49,
	0x64, 0x42, 0x08, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x22, 0x0a, 0x0a, 0x43,
	0x6c, 0x61, 0x69, 0x6d, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x22,
	0x3e, 0x0a, 0x0c, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x22,
	0x85, 0x02, 0x0a, 0x13, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x72, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x4c, 0x61, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x0a, 0x0e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x6f, 0x74,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb6, 0x05, 0x0a, 0x0f, 0x49, 0x6e, 0x63, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x6c, 0x6f, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4c, 0x6f, 0x73, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c,
	0x69, 0x6e, 0x65, 0x4f, 0x66, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x30, 0x0a,
	0x14, 0x6f, 0x77, 0x6e, 0x5f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x76,
	0x6f, 0x6c, 0x76, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x6f, 0x77, 0x6e,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x12,
	0x37, 0x0a, 0x18, 0x6f, 0x77, 0x6e, 0x5f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69,
	0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x5f, 0x76, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x6f, 0x77, 0x6e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x76,
	0x6f, 0x6c, 0x76, 0x65, 0x64, 0x56, 0x69, 0x6e, 0x12, 0x36, 0x0a, 0x17, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x5f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x76, 0x6f, 0x6c,
	0x76, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x64,
	0x12, 0x3d, 0x0a, 0x1b, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x5f, 0x69, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x5f, 0x76, 0x69, 0x6e, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x56, 0x69, 0x6e, 0x12,
	0x2d, 0x0a, 0x13, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x74, 0x5f, 0x74, 0x68, 0x65,
	0x5f, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x65, 0x41, 0x74, 0x54, 0x68, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x2c,
	0x0a, 0x12, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2b,
	0x0a, 0x11, 0x69, 0x6e, 0x6a, 0x75, 0x72, 0x69, 0x65, 0x73, 0x5f, 0x69, 0x6e, 0x76, 0x6f, 0x6c,
	0x76, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x6e, 0x6a, 0x75, 0x72,
	0x69, 0x65, 0x73, 0x49, 0x6e, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x12, 0x31, 0x0a, 0x14, 0x69,
	0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x69, 0x6e, 0x63, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0f, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0x1f, 0x0a, 0x09, 0x46, 0x6e, 0x6f, 0x6c, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a,
	0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x22, 0xc3, 0x01, 0x0a, 0x0a, 0x46, 0x6e, 0x6f, 0x6c, 0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65,
	0x12, 0x4e, 0x0a, 0x14, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x42, 0x0a, 0x10, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6c, 0x6c, 0x6d,
	0x6f, 0x70, 0x73, 0x2e, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x0f, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f,
	0x66, 0x6e, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x46, 0x6e, 0x6f, 0x6c, 0x2a, 0x9c, 0x01, 0x0a, 0x08, 0x43, 0x61, 0x6c, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x21, 0x0a, 0x1d, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4c,
	0x41, 0x49, 0x4d, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41,
	0x52, 0x59, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x46, 0x4e, 0x4f, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x41, 0x4b, 0x45, 0x10, 0x02, 0x12, 0x1a, 0x0a,
	0x16, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x4e, 0x4f, 0x4c, 0x5f, 0x49, 0x4e,
	0x54, 0x41, 0x4b, 0x45, 0x5f, 0x56, 0x32, 0x10, 0x03, 0x12, 0x2b, 0x0a, 0x27, 0x47, 0x45, 0x4e,
	0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x4e, 0x41, 0x50, 0x53, 0x48, 0x45, 0x45, 0x54, 0x5f,
	0x43, 0x4c, 0x41, 0x49, 0x4d, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x4d,
	0x4d, 0x41, 0x52, 0x59, 0x10, 0x04, 0x32, 0x44, 0x0a, 0x0d, 0x4c, 0x4c, 0x4d, 0x4f, 0x70, 0x73,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x43, 0x61, 0x6c, 0x6c, 0x12,
	0x13, 0x2e, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x6c, 0x6c, 0x6d, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x61,
	0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_llmops_llmops_proto_rawDescOnce sync.Once
	file_llmops_llmops_proto_rawDescData = file_llmops_llmops_proto_rawDesc
)

func file_llmops_llmops_proto_rawDescGZIP() []byte {
	file_llmops_llmops_proto_rawDescOnce.Do(func() {
		file_llmops_llmops_proto_rawDescData = protoimpl.X.CompressGZIP(file_llmops_llmops_proto_rawDescData)
	})
	return file_llmops_llmops_proto_rawDescData
}

var file_llmops_llmops_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_llmops_llmops_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_llmops_llmops_proto_goTypes = []interface{}{
	(CallType)(0),               // 0: llmops.CallType
	(*CallRequest)(nil),         // 1: llmops.CallRequest
	(*CallResponse)(nil),        // 2: llmops.CallResponse
	(*ClaimNotes)(nil),          // 3: llmops.ClaimNotes
	(*ClaimSummary)(nil),        // 4: llmops.ClaimSummary
	(*ReporterInformation)(nil), // 5: llmops.ReporterInformation
	(*IncidentDetails)(nil),     // 6: llmops.IncidentDetails
	(*FnolEmail)(nil),           // 7: llmops.FnolEmail
	(*FnolIntake)(nil),          // 8: llmops.FnolIntake
}
var file_llmops_llmops_proto_depIdxs = []int32{
	0, // 0: llmops.CallRequest.call_type:type_name -> llmops.CallType
	3, // 1: llmops.CallRequest.claim_notes:type_name -> llmops.ClaimNotes
	7, // 2: llmops.CallRequest.fnol_email:type_name -> llmops.FnolEmail
	4, // 3: llmops.CallResponse.claim_summary:type_name -> llmops.ClaimSummary
	8, // 4: llmops.CallResponse.fnol_intake:type_name -> llmops.FnolIntake
	5, // 5: llmops.FnolIntake.reporter_information:type_name -> llmops.ReporterInformation
	6, // 6: llmops.FnolIntake.incident_details:type_name -> llmops.IncidentDetails
	1, // 7: llmops.LLMOpsService.Call:input_type -> llmops.CallRequest
	2, // 8: llmops.LLMOpsService.Call:output_type -> llmops.CallResponse
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_llmops_llmops_proto_init() }
func file_llmops_llmops_proto_init() {
	if File_llmops_llmops_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_llmops_llmops_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_llmops_llmops_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_llmops_llmops_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimNotes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_llmops_llmops_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_llmops_llmops_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReporterInformation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_llmops_llmops_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncidentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_llmops_llmops_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FnolEmail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_llmops_llmops_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FnolIntake); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_llmops_llmops_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CallRequest_ClaimNotes)(nil),
		(*CallRequest_FnolEmail)(nil),
	}
	file_llmops_llmops_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*CallResponse_ClaimSummary)(nil),
		(*CallResponse_FnolIntake)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_llmops_llmops_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_llmops_llmops_proto_goTypes,
		DependencyIndexes: file_llmops_llmops_proto_depIdxs,
		EnumInfos:         file_llmops_llmops_proto_enumTypes,
		MessageInfos:      file_llmops_llmops_proto_msgTypes,
	}.Build()
	File_llmops_llmops_proto = out.File
	file_llmops_llmops_proto_rawDesc = nil
	file_llmops_llmops_proto_goTypes = nil
	file_llmops_llmops_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LLMOpsServiceClient is the client API for LLMOpsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LLMOpsServiceClient interface {
	Call(ctx context.Context, in *CallRequest, opts ...grpc.CallOption) (*CallResponse, error)
}

type lLMOpsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLLMOpsServiceClient(cc grpc.ClientConnInterface) LLMOpsServiceClient {
	return &lLMOpsServiceClient{cc}
}

func (c *lLMOpsServiceClient) Call(ctx context.Context, in *CallRequest, opts ...grpc.CallOption) (*CallResponse, error) {
	out := new(CallResponse)
	err := c.cc.Invoke(ctx, "/llmops.LLMOpsService/Call", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LLMOpsServiceServer is the server API for LLMOpsService service.
type LLMOpsServiceServer interface {
	Call(context.Context, *CallRequest) (*CallResponse, error)
}

// UnimplementedLLMOpsServiceServer can be embedded to have forward compatible implementations.
type UnimplementedLLMOpsServiceServer struct {
}

func (*UnimplementedLLMOpsServiceServer) Call(context.Context, *CallRequest) (*CallResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Call not implemented")
}

func RegisterLLMOpsServiceServer(s *grpc.Server, srv LLMOpsServiceServer) {
	s.RegisterService(&_LLMOpsService_serviceDesc, srv)
}

func _LLMOpsService_Call_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LLMOpsServiceServer).Call(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/llmops.LLMOpsService/Call",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LLMOpsServiceServer).Call(ctx, req.(*CallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _LLMOpsService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "llmops.LLMOpsService",
	HandlerType: (*LLMOpsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Call",
			Handler:    _LLMOpsService_Call_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "llmops/llmops.proto",
}
