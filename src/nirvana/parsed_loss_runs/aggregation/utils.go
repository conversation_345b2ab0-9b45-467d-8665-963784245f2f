package aggregation

import (
	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

var ErrNoAppEnumCoverageMapping = errors.New("no app enum coverage found for given coverageInferred")

func getAppEnumCoverageFromCoverageInferred(coverageInferred pibit.CoverageInferred) (*app_enums.Coverage, error) {
	switch coverageInferred {
	case pibit.CoverageInferredAutoLiability:
		return pointer_utils.ToPointer(app_enums.CoverageAutoLiability), nil
	case pibit.CoverageInferredAutoPhysicalDamage:
		return pointer_utils.ToPointer(app_enums.CoverageAutoPhysicalDamage), nil
	case pibit.CoverageInferredGeneralLiability:
		return pointer_utils.ToPointer(app_enums.CoverageGeneralLiability), nil
	case pibit.CoverageInferredMotorTruckCargo:
		return pointer_utils.ToPointer(app_enums.CoverageMotorTruckCargo), nil
	default:
		return nil, ErrNoAppEnumCoverageMapping
	}
}
