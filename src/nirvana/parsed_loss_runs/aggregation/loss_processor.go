package aggregation

import (
	"time"

	"nirvanatech.com/nirvana/common-go/slice_utils"

	"github.com/google/uuid"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func BuildProcessedLosses(
	aggregationID uuid.UUID,
	lines []LossLineWithPolicyAndClaim,
	periods []CoverageAggregationPeriods,
) []pibit.ProcessedLoss {
	var results []pibit.ProcessedLoss
	coveragePeriodMap := buildCoveragePeriodMap(periods)

	for _, line := range lines {
		coverage, unmappedReasons := inferCoverage(line.Loss)
		periodStart, periodEnd, dateReason := matchPeriod(line.Claim.DateOfLoss, coverage, coveragePeriodMap)

		if dateReason != nil {
			unmappedReasons = append(unmappedReasons, *dateReason)
		}

		lowConfidenceReasons := getLowConfidenceReasons(line)

		unmapped := len(unmappedReasons) > 0
		pl := buildProcessedLoss(aggregationID, line, coverage, periodStart, periodEnd, unmapped, unmappedReasons, lowConfidenceReasons)
		results = append(results, pl)
	}

	return results
}

func buildCoveragePeriodMap(periods []CoverageAggregationPeriods) map[app_enums.Coverage][]pibit.Period {
	m := make(map[app_enums.Coverage][]pibit.Period)
	for _, cp := range periods {
		m[cp.Coverage] = slice_utils.Map(cp.Periods, func(periodWithPUCount pibit.PeriodWithPUCount) pibit.Period {
			return periodWithPUCount.Period
		})
	}
	return m
}

func inferCoverage(loss pibit.LossData) (*app_enums.Coverage, []pibit.UnmappedLossReason) {
	if loss.CoverageInferred == nil {
		return nil, []pibit.UnmappedLossReason{pibit.UnmappedLossReasonMissingCoverage}
	}
	coverage, err := getAppEnumCoverageFromCoverageInferred(*loss.CoverageInferred)
	if err != nil {
		return nil, []pibit.UnmappedLossReason{pibit.UnmappedLossReasonUnsupportedCoverage}
	}
	return coverage, nil
}

func matchPeriod(
	dateOfLoss *time.Time,
	coverage *app_enums.Coverage,
	coveragePeriodMap map[app_enums.Coverage][]pibit.Period,
) (*time.Time, *time.Time, *pibit.UnmappedLossReason) {
	if coverage == nil {
		return nil, nil, pointer_utils.ToPointer(pibit.UnmappedLossReasonMissingCoverage)
	}
	if dateOfLoss == nil {
		return nil, nil, pointer_utils.ToPointer(pibit.UnmappedLossReasonMissingDateOfLoss)
	}

	for _, p := range coveragePeriodMap[*coverage] {
		if !dateOfLoss.Before(p.FromDate) && !dateOfLoss.After(p.ToDate) {
			return &p.FromDate, &p.ToDate, nil
		}
	}

	r := pibit.UnmappedLossReasonOutsideAggregationPeriods
	return nil, nil, &r
}

func getLowConfidenceReasons(lossLine LossLineWithPolicyAndClaim) []pibit.LowConfidenceLossReason {
	var reasons []pibit.LowConfidenceLossReason
	if lossLine.Claim.DateOfLoss == nil {
		reasons = append(reasons, pibit.LowConfidenceLossReasonMissingDateOfLoss)
	}
	if lossLine.Claim.ClaimID == nil {
		reasons = append(reasons, pibit.LowConfidenceLossReasonMissingClaimId)
	}
	if lossLine.Policy.PolicyNo == nil {
		reasons = append(reasons, pibit.LowConfidenceLossReasonMissingPolicyNo)
	}
	if lossLine.Loss.ClaimLossType == nil {
		reasons = append(reasons, pibit.LowConfidenceLossReasonMissingClaimLossType)
	}
	return reasons
}

func buildProcessedLoss(
	aggregationID uuid.UUID,
	line LossLineWithPolicyAndClaim,
	coverage *app_enums.Coverage,
	periodStart, periodEnd *time.Time,
	unmapped bool,
	unmappedReasons []pibit.UnmappedLossReason,
	lowConfidenceReasons []pibit.LowConfidenceLossReason,
) pibit.ProcessedLoss {
	loss := line.Loss
	claim := line.Claim
	policy := line.Policy

	confidenceLevel := pibit.ConfidenceLevelHigh
	if len(lowConfidenceReasons) > 0 {
		confidenceLevel = pibit.ConfidenceLevelLow
	}

	var age *int
	if claim.Age != nil {
		age = pointer_utils.ToPointer(int(*claim.Age))
	}

	return pibit.ProcessedLoss{
		ID:                    uuid.NewString(),
		AggregationID:         aggregationID.String(),
		PeriodStartDate:       periodStart,
		PeriodEndDate:         periodEnd,
		Coverage:              coverage,
		PolicyNo:              policy.PolicyNo,
		Lob:                   policy.Lob,
		Insurer:               policy.Insurer,
		Insured:               policy.Insured,
		Agent:                 policy.Agent,
		EffDate:               policy.EffDate,
		ExpDate:               policy.ExpDate,
		CancelDate:            policy.CancelDate,
		ReportGenerationDate:  policy.ReportGenerationDate,
		NoLoss:                int(pointer_utils.Int32ValOr(policy.NoLoss, 0)),
		PolicyTotalPaid:       policy.TotalPaid,
		PolicyTotalReserve:    policy.TotalReserve,
		PolicyTotalRecovered:  policy.TotalRecovered,
		PolicyTotalIncurred:   pointer_utils.Float64ValOr(policy.TotalIncurred, 0.0),
		ClaimID:               claim.ClaimID,
		OccurrenceID:          claim.OccurrenceID,
		DateOfLoss:            claim.DateOfLoss,
		DateReported:          claim.DateReported,
		TimeOfLoss:            claim.TimeOfLoss,
		ClosedDate:            claim.ClosedDate,
		CauseOfLossSummary:    claim.CauseOfLossSummary,
		CauseOfLossDesc:       claim.CauseOfLossDescription,
		VIN:                   claim.VIN,
		Type:                  claim.Type,
		DriverID:              claim.DriverID,
		Name:                  claim.Name,
		Age:                   age,
		Gender:                claim.Gender,
		DOB:                   claim.DOB,
		HiringDate:            claim.HiringDate,
		Street:                claim.Street,
		CityCounty:            claim.CityCounty,
		State:                 claim.State,
		Zip:                   claim.Zip,
		LossLocation:          claim.LossLocation,
		ClaimStatus:           claim.ClaimStatus,
		Claimant:              loss.Claimant,
		CoverageInferred:      loss.CoverageInferred,
		ClaimLossType:         loss.ClaimLossType,
		ClaimLossTypeInferred: loss.ClaimLossTypeInferred,
		Examiner:              loss.Examiner,
		LossReserved:          loss.LossReserved,
		LossPaid:              loss.LossPaid,
		ExpenseReserve:        loss.ExpenseReserve,
		ExpensePaid:           loss.ExpensePaid,
		LossTotalReserve:      loss.TotalReserve,
		LossTotalPaid:         loss.TotalPaid,
		LossTotalRecovered:    loss.TotalRecovered,
		LossTotalIncurred:     pointer_utils.Float64ValOr(loss.TotalIncurred, 0.0),
		DeductibleAmount:      loss.DeductibleAmount,
		SubrogationAmount:     loss.SubrogationAmount,
		SalvageAmount:         loss.SalvageAmount,
		OtherRecovery:         loss.OtherRecovery,
		LegalReserve:          loss.LegalReserve,
		LegalPaid:             loss.LegalPaid,
		LegalIncurred:         loss.LegalIncurred,
		AlaeExpenseReserve:    loss.AlaeExpenseReserve,
		AlaePaid:              loss.AlaePaid,
		AlaeIncurred:          loss.AlaeIncurred,
		OtherExpenseReserve:   loss.OtherExpenseReserve,
		OtherExpensePaid:      loss.OtherExpensePaid,
		OtherExpenseIncurred:  loss.OtherExpenseIncurred,
		Unmapped:              &unmapped,
		UnmappedReasons:       unmappedReasons,
		CreatedAt:             time.Now(),
		ConfidenceInfo: pibit.LossConfidenceInfo{
			Level:                confidenceLevel,
			LowConfidenceReasons: lowConfidenceReasons,
		},
	}
}
