{"name": "monorepo", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"lint": "eslint --no-error-on-unmatched-pattern --fix {apps,packages}/**/*.{ts,tsx,js,jsx}", "format": "prettier \"**/*.{ts,tsx,js,jsox,json,css}\" --write", "api": "yarn workspace @nirvana/api api", "prepare": "cd ../../../ && husky src/nirvana/client/.husky", "ci:graphql": "yarn turbo ci:graphql", "build:api-types": "yarn ci:graphql && yarn api", "api:safety": "yarn workspace @nirvana/safety api:safety", "api:support": "yarn workspace @nirvana/support api", "api:quoting": "yarn workspace @nirvana/quoting api:graphql", "cy:safety:open": "yarn workspace @nirvana/safety cypress open --browser chrome", "cy:support:open": "yarn workspace @nirvana/support cypress open --browser chrome", "cy:quoting:open": "yarn workspace @nirvana/quoting cypress open --browser chrome", "cy:core:open": "yarn workspace @nirvana/core cypress open --browser chrome", "cy:underwriter:open": "yarn workspace @nirvana/underwriter cypress open --browser chrome", "cy:safety:run": "yarn workspace @nirvana/safety cypress run --browser chrome", "cy:quoting:run": "yarn workspace @nirvana/quoting cypress run --browser chrome", "cy:core:run": "yarn workspace @nirvana/core cypress run --browser chrome", "cy:support:run": "yarn workspace @nirvana/support cypress run --browser chrome", "cy:underwriter:run": "yarn workspace @nirvana/underwriter cypress run --browser chrome", "start:safety": "concurrently \"yarn turbo run @nirvana/safety#dev\" \"yarn api:safety\"", "start:support": "concurrently \"yarn turbo run @nirvana/support#dev\" \"yarn api:support\"", "start:quoting": "yarn turbo run @nirvana/quoting#dev", "start:storybook": "yarn turbo run @nirvana/storybook#dev", "start:underwriter": "yarn turbo run @nirvana/underwriter#dev", "start:graphiql": "yarn turbo run @nirvana/graphiql#dev", "build:production": "yarn turbo run build:production", "build:safety": "yarn turbo run @nirvana/safety#build:production", "build:support": "yarn turbo run @nirvana/support#build:production", "build:quoting": "yarn turbo run @nirvana/quoting#build:production", "build:underwriter": "yarn turbo run @nirvana/underwriter#build:production", "build:storybook": "yarn turbo run @nirvana/storybook#build:production", "build:graphiql": "yarn turbo run @nirvana/graphiql#build:production", "serve:safety": "yarn workspace @nirvana/safety serve", "serve:support": "yarn workspace @nirvana/support serve", "serve:quoting": "yarn workspace @nirvana/quoting serve", "serve:underwriter": "yarn workspace @nirvana/underwriter serve", "postinstall": "patch-package"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn format"}}, "lint-staged": {"{apps,packages}/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write"], "{apps,packages}/**/*.{tsx, ts, js, jsx}": ["eslint --fix"]}, "devDependencies": {"@babel/core": "^7.28.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/cypress": "^10.0.3", "@types/react": "^18.3.1", "@types/react-date-range": "^1.4.10", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cypress": "^14.5.4", "cypress-localstorage-commands": "^2.2.8", "cypress-map": "^1.48.1", "cypress-recurse": "^1.37.1", "cypress-vite": "^1.6.0", "cypress-xpath": "^2.0.1", "eslint": "^7.32.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-cypress": "^3.6.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-mocha": "^10.5.0", "eslint-plugin-no-inline-styles": "^1.0.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.2.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-storybook": "^0.8.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "patch-package": "^8.0.0", "postcss": "^8.5.6", "postinstall-postinstall": "^2.1.0", "prettier": "^3.6.2", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss-animate": "^1.0.7", "turbo": "^2.5.5"}, "dependencies": {"@parcel/watcher": "^2.5.1", "@sentry/react": "^10.4.0", "@sentry/vite-plugin": "^3.5.0", "@tanstack/react-table": "^8.21.3", "@testing-library/jest-dom": "6.6.4", "clsx": "^2.1.1", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.1", "graphql": "^16.11.0", "jotai": "^2.12.5", "match-sorter": "^8.0.3", "react": "^18.3.1", "react-date-range": "^2.0.1", "react-dom": "^18.3.1", "react-router-dom": "^6.29.0", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "usehooks-ts": "^3.1.1", "vite-plugin-svgr": "^4.3.0"}, "packageManager": "yarn@1.22.22"}