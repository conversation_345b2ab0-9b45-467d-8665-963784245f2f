{"name": "@nirvana/storybook", "version": "0.0.0", "private": true, "scripts": {"dev": "storybook dev -p 6006", "build:staging": "yarn build", "build:test": "yarn build", "build:production": "yarn build", "build": "storybook build -o dist"}, "dependencies": {"@hookform/resolvers": "^5.1.0", "@nirvana/core": "*", "@nirvana/ui": "*", "react-hook-form": "^7.57.0", "react-icons": "5.4.0", "react-number-format": "^4.5.3", "zod": "3.25.64"}, "devDependencies": {"@storybook/addon-docs": "^9.1.1", "@storybook/addon-onboarding": "^9.0.10", "@storybook/react": "^9.0.18", "@storybook/react-vite": "^9.0.12", "@types/node": "^24.1.0", "storybook": "^9.1.1"}}