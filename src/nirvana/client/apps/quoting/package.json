{"name": "@nirvana/quoting", "version": "0.1.0", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "private": true, "dependencies": {"@apollo/client": "^3.13.9", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@impler/react": "^0.29.0", "@material-ui/core": "5.0.0-alpha.29", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "5.0.0-alpha.29", "@nirvana/api": "*", "@nirvana/ui-kit": "*", "@reduxjs/toolkit": "^2.8.2", "@sentry/vite-plugin": "^3.5.0", "@tanstack/react-query": "^4.36.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.6.1", "@vis.gl/react-google-maps": "^1.5.3", "axios": "^0.21.1", "constate": "^3.3.3", "launchdarkly-react-client-sdk": "^3.8.1", "logrocket": "^10.0.0", "lottie-react": "^2.4.1", "notistack": "2.0.1-alpha.5", "posthog-js": "^1.257.0", "react-dropzone": "^14.3.8", "react-helmet-async": "^2.0.5", "react-hook-form": "react-hook-form@7.43.5", "react-hook-form-v6": "npm:react-hook-form@^6.15.4", "react-multi-email": "^1.0.25", "react-number-format": "^4.5.3", "react-phone-number-input": "^3.4.12", "react-redux": "^7.2.2", "react-slick": "^0.30.3", "react-table": "^7.6.3", "react-transition-group": "^4.4.5", "react-use-intercom": "^5.5.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.7", "react-window-infinite-loader": "^1.0.10", "redux-logger": "^3.0.6", "slick-carousel": "^1.8.1", "tiny-emitter": "^2.1.0", "typescript": "5.4.5", "use-clipboard-copy": "^0.2.0", "zod": "^3.25.64"}, "scripts": {"start": "vite", "tsc": "tsc --noEmit", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "build:staging": "vite build --mode staging", "build:test": "vite build --mode testing", "build:production": "vite build", "serve": "vite preview", "dev": "vite", "ci:graphql": "graphql-codegen --config codegen.yml"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@graphql-codegen/cli": "5.0.7", "@graphql-codegen/typescript": "2.4.8", "@graphql-codegen/typescript-operations": "2.3.5", "@graphql-codegen/typescript-react-apollo": "3.3.7", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/react-redux": "^7.1.16", "@types/react-table": "^7.0.28", "@types/react-virtualized-auto-sizer": "^1.0.8", "@types/react-window": "^1.8.5", "@types/react-window-infinite-loader": "^1.0.9", "@types/redux-logger": "^3.0.13", "@vitejs/plugin-react": "^4.7.0", "vite": "^6.3.5", "vite-plugin-checker": "^0.8.0", "vite-plugin-node-polyfills": "^0.24.0"}}