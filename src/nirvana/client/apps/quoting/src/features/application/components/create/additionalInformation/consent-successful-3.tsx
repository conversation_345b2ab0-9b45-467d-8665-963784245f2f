import {
  Box,
  Button,
  IconButton,
  InputAdornment,
  Link,
  OutlinedInput,
  Typography,
} from '@material-ui/core';
import { Dialog, Show } from '@nirvana/ui-kit';
import { useSnackbar } from 'notistack';
import { useEffect, useState } from 'react';
import {
  HiCheck,
  HiOutlineDocumentDuplicate,
  HiX,
  HiXCircle,
} from 'react-icons/hi';
import { ReactMultiEmail } from 'react-multi-email';
import 'react-multi-email/dist/style.css';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import IconAITeal from 'src/assets/icons/ai-teal-600.svg';
import { sendTelematicsConsentRequestEmail } from 'src/features/application/actions';
import { applicationSelector } from 'src/features/application/slices';
import {
  COPY_TELEMATICS_LINK_SUCCESS_NEW_UX,
  TELEMATICS_LINK_SENT_NEW_UX,
} from 'src/features/telematics/events';
import { useAnalytics } from 'src/helpers/analytics';
import { useDispatch } from 'src/redux';
import { useClipboard } from 'use-clipboard-copy';
import EmailPreview from './email-preview';

type ConsentSuccessfulProps = {
  open: boolean;
  onClose: () => void;
  connectionURL: string | undefined;
};

const EMAIL_SENT_RESET_TIMEOUT = 3000;

export default function ConsentSuccessfulV3({
  open,
  onClose,
  connectionURL,
}: ConsentSuccessfulProps) {
  const dispatch = useDispatch();
  const { capture } = useAnalytics();
  const { enqueueSnackbar } = useSnackbar();
  const { applicationId = '' } = useParams();
  const { activeApplication } = useSelector(applicationSelector);
  const [isEmailPreviewOpen, setIsEmailPreviewOpen] = useState(false);
  const [emailSendingStatus, setEmailSendingStatus] = useState<
    'pending' | 'progress' | 'completed'
  >('pending');

  const [emails, setEmails] = useState<string[]>([]);
  const clipboard = useClipboard({ copiedTimeout: EMAIL_SENT_RESET_TIMEOUT });

  useEffect(() => {
    const defaultEmails = [];
    if (activeApplication?.telematicsInfo?.email) {
      defaultEmails.push(activeApplication.telematicsInfo.email);
    }

    if (
      activeApplication?.indicationForm?.operationsForm?.retailerInfo?.email
    ) {
      defaultEmails.push(
        activeApplication.indicationForm.operationsForm.retailerInfo.email,
      );
    }
    setEmails(defaultEmails);
  }, [
    activeApplication?.telematicsInfo?.email,
    activeApplication?.indicationForm?.operationsForm?.retailerInfo?.email,
  ]);

  function handleSendEmail() {
    capture(TELEMATICS_LINK_SENT_NEW_UX, {
      applicationId,
      emails,
    });

    setEmailSendingStatus('progress');

    dispatch(
      sendTelematicsConsentRequestEmail({
        applicationId,
        payload: {
          completed: true,
          email: { to: emails.map((email) => ({ email })) },
        },
      }),
    ).then((response) => {
      if (sendTelematicsConsentRequestEmail.fulfilled.match(response)) {
        setEmailSendingStatus('completed');
        enqueueSnackbar('Email sent successfully', {
          variant: 'success',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
          TransitionProps: {
            timeout: 500,
            easing: 'ease-in',
          },
        });
        setTimeout(() => {
          setEmailSendingStatus('pending');
        }, EMAIL_SENT_RESET_TIMEOUT);
      }
    });
  }

  return (
    <>
      <Dialog open={open} onClose={onClose}>
        <div className="max-w-lg">
          <div className="flex items-start justify-between px-6 pb-2 mb-6 -mx-6 -my-2 border-b">
            <p className="font-bold">Send Telematics Link</p>
            <IconButton size="small" onClick={onClose}>
              <HiX />
            </IconButton>
          </div>
          <p className="mb-4">
            The insured should use this secure link to authorize a connection to
            their telematics provider. This is required to obtain a quote.
          </p>
          <p className="mb-6">
            <a
              target="_blank"
              rel="noreferrer"
              className="underline text-primary-main hover:text-primary-dark"
              href="https://www.nirvanatech.com/privacy-promise"
            >
              Read more
            </a>{' '}
            to understand how data usage and privacy are handled.
          </p>

          <p className="mb-2 font-bold">
            Send email through Nirvana{' '}
            <span
              onClick={() => setIsEmailPreviewOpen(true)}
              className="ml-1 text-xs font-normal cursor-pointer text-primary-main"
            >
              See Preview
            </span>
          </p>

          <Box
            display="flex"
            px={2}
            py={1.5}
            alignItems="flex-start"
            gap={1}
            borderRadius={1.6}
            sx={{ bgcolor: 'teal.100', border: 0, mb: 3 }}
          >
            <img src={IconAITeal} />
            <Typography variant="body2">
              Sending the telematics email through Nirvana increases your
              connection success rate by 14% on average.
            </Typography>
          </Box>

          <div className="flex flex-wrap items-center gap-2 mb-0.5">
            <ReactMultiEmail
              emails={emails}
              onChange={setEmails}
              className="border-text-disabled focus:border-primary-main hover:border-text-primary"
              placeholder="Add emails separated by comma"
              getLabel={(
                email: string,
                index: number,
                removeEmail: (index: number) => void,
              ) => (
                <div
                  className="flex items-center px-2 py-1 mx-1 border rounded border-text-disabled"
                  key={index}
                >
                  <span className="mr-1 text-text-primary">{email}</span>
                  <HiXCircle
                    className="cursor-pointer text-text-hint"
                    onClick={() => removeEmail(index)}
                  />
                </div>
              )}
            />
            <Button
              variant="contained"
              onClick={handleSendEmail}
              disabled={
                emails.length === 0 || emailSendingStatus === 'progress'
              }
            >
              {emailSendingStatus === 'completed' ? 'Sent!' : 'Send'}
            </Button>
          </div>
          <p className="mb-6 text-xxs text-text-hint">
            Press enter to add multiple emails
          </p>

          <p className="mb-2 font-bold">Send link in your own message</p>

          <OutlinedInput
            readOnly
            fullWidth
            value={connectionURL}
            className="mb-6 bg-primary-extraLight text-text-hint"
            endAdornment={
              <InputAdornment>
                <Link
                  underline="none"
                  className="flex items-center cursor-pointer"
                  onClick={() => {
                    clipboard.copy(connectionURL);
                    capture(COPY_TELEMATICS_LINK_SUCCESS_NEW_UX, {
                      applicationId,
                    });
                  }}
                >
                  <Show
                    when={clipboard.copied}
                    fallback={
                      <>
                        <Typography variant="caption" color="primary" mr={0.5}>
                          Copy Link
                        </Typography>
                        <HiOutlineDocumentDuplicate />
                      </>
                    }
                  >
                    <Typography variant="caption" color="primary" mr={0.5}>
                      Link Copied
                    </Typography>
                    <HiCheck />
                  </Show>
                </Link>
              </InputAdornment>
            }
          />
        </div>
      </Dialog>

      <EmailPreview
        open={isEmailPreviewOpen}
        onClose={() => setIsEmailPreviewOpen(false)}
      />
    </>
  );
}
