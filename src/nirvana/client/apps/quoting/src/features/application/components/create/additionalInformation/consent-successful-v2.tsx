import {
  Button,
  FormControl,
  FormControlLabel,
  IconButton,
  InputAdornment,
  Link,
  OutlinedInput,
  Radio,
  RadioGroup,
  Typography,
} from '@material-ui/core';
import { ArrowForwardIosRounded } from '@material-ui/icons';
import { Dialog, Show, Switch } from '@nirvana/ui-kit';
import { useMutation } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';
import { useEffect, useState } from 'react';
import {
  HiCheck,
  HiOutlineDocumentDuplicate,
  HiX,
  HiXCircle,
} from 'react-icons/hi';
import { ReactMultiEmail } from 'react-multi-email';
import 'react-multi-email/dist/style.css';
import PhoneInput from 'react-phone-number-input/input';
import 'react-phone-number-input/style.css';
import { useSelector } from 'react-redux';
import { useLocation, useParams } from 'react-router-dom';
import { sendTelematicsConsentRequestEmail } from 'src/features/application/actions';
import { applicationSelector } from 'src/features/application/slices';
import { useApplicationDetailsContext } from 'src/features/admitted/hooks/useApplicationDetails';
import {
  COPY_TELEMATICS_LINK_SUCCESS,
  TELEMATICS_LINK_EMAIL_SENT,
  TELEMATICS_LINK_SMS_SENT,
} from 'src/features/telematics/events';
import { useAnalytics } from 'src/helpers/analytics';
import { useDispatch } from 'src/redux';
import { useClipboard } from 'use-clipboard-copy';
import EmailPreview from './email-preview';
import { sendTelematicsConsentSms } from './queries';

type ConsentSuccessfulV2Props = {
  open: boolean;
  onClose: () => void;
  connectionURL: string | undefined;
};

type SendMethod = 'email' | 'sms';

// this component is also used in NF consent
export default function ConsentSuccessfulV2({
  open,
  onClose,
  connectionURL,
}: ConsentSuccessfulV2Props) {
  const dispatch = useDispatch();
  const { capture } = useAnalytics();
  const { enqueueSnackbar } = useSnackbar();
  const location = useLocation();

  // non fleet specific code
  const isNonFleetFlow = location?.pathname?.includes('/non-fleet/');
  const { applicationDetails } = useApplicationDetailsContext();

  const { applicationId = '' } = useParams();
  const { activeApplication } = useSelector(applicationSelector);
  const [isEmailPreviewOpen, setIsEmailPreviewOpen] = useState(false);
  const [sendMethod, setSendMethod] = useState<SendMethod>('email');

  const [emailSendingStatus, setEmailSendingStatus] = useState<
    'pending' | 'progress'
  >('pending');

  const [recipientEmails, setRecipientEmails] = useState<string[]>([]);
  const [ccEmails, setCcEmails] = useState<string[]>([]);

  const [phoneNumber, setPhoneNumber] = useState('');

  const clipboard = useClipboard({ copiedTimeout: 3000 });

  useEffect(() => {
    const defaultEmails: string[] = [];
    if (isNonFleetFlow) {
      if (applicationDetails?.admitted?.indicationForm?.telematicsInfo?.email) {
        defaultEmails.push(
          applicationDetails.admitted.indicationForm.telematicsInfo.email,
        );
      }
    } else if (activeApplication?.telematicsInfo?.email) {
      defaultEmails.push(activeApplication.telematicsInfo.email);
    }

    if (
      activeApplication?.indicationForm?.operationsForm?.retailerInfo?.email
    ) {
      defaultEmails.push(
        activeApplication.indicationForm.operationsForm.retailerInfo.email,
      );
    }

    setRecipientEmails(defaultEmails);
  }, [
    isNonFleetFlow,
    applicationDetails?.admitted?.indicationForm?.telematicsInfo?.email,
    activeApplication?.telematicsInfo?.email,
    activeApplication?.indicationForm?.operationsForm?.retailerInfo?.email,
  ]);

  const smsMutation = useMutation({
    mutationFn: (phone: string) =>
      sendTelematicsConsentSms(applicationId, {
        completed: true,
        sms: {
          to: [{ phoneNumber: `+1${phone}` }],
        },
      }),
    onSuccess: () => {
      enqueueSnackbar('SMS sent successfully', {
        variant: 'success',
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'center',
        },
        TransitionProps: {
          timeout: 500,
          easing: 'ease-in',
        },
      });
      onClose();
    },
  });

  function handleSendEmail() {
    if (!recipientEmails.length) {
      return;
    }

    capture(TELEMATICS_LINK_EMAIL_SENT, {
      applicationId,
      recipient: recipientEmails,
      cc: ccEmails,
    });

    setEmailSendingStatus('progress');

    dispatch(
      sendTelematicsConsentRequestEmail({
        applicationId,
        payload: {
          completed: true,
          email: {
            to: recipientEmails.map((email) => ({ email })),
            cc:
              ccEmails.length > 0
                ? ccEmails.map((email) => ({ email }))
                : undefined,
          },
        },
      }),
    ).then((response) => {
      if (sendTelematicsConsentRequestEmail.fulfilled.match(response)) {
        enqueueSnackbar('Email sent successfully', {
          variant: 'success',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
          TransitionProps: {
            timeout: 500,
            easing: 'ease-in',
          },
        });
        onClose();
      } else {
        setEmailSendingStatus('pending');
      }
    });
  }

  function handleSendSMS() {
    if (!phoneNumber) {
      return;
    }

    // Remove any non-numeric characters for API consumption
    const cleanedPhoneNumber = phoneNumber.replace(/\D/g, '');

    capture(TELEMATICS_LINK_SMS_SENT, {
      applicationId,
      phoneNumber: `+1${cleanedPhoneNumber}`,
    });

    smsMutation.mutate(cleanedPhoneNumber);
  }

  function handleSend() {
    if (sendMethod === 'email') {
      handleSendEmail();
    } else {
      handleSendSMS();
    }
  }

  const handlePhoneChange = (value: string | undefined) => {
    setPhoneNumber(value || '');
  };

  const MIN_PHONE_NUMBER_LENGTH = 10;

  const isPhoneValid = phoneNumber
    ? phoneNumber.replace(/\D/g, '').length >= MIN_PHONE_NUMBER_LENGTH
    : false;

  const handleRecipientEmailChange = (emails: string[]) => {
    // Check if any of the new recipient emails exist in CC
    const emailsToMoveFromCC: string[] = [];
    emails.forEach((email) => {
      if (email && ccEmails.includes(email)) {
        emailsToMoveFromCC.push(email);
      }
    });

    // Remove any emails that are being added as recipients from CC
    if (emailsToMoveFromCC.length > 0) {
      const updatedCcEmails = ccEmails.filter(
        (email) => !emailsToMoveFromCC.includes(email),
      );
      setCcEmails(updatedCcEmails);

      const message =
        emailsToMoveFromCC.length === 1
          ? 'Email moved from CC to Recipient'
          : 'Emails moved from CC to Recipient';

      enqueueSnackbar(message, {
        variant: 'info',
      });
    }

    setRecipientEmails(emails);
  };

  const handleCcEmailsChange = (emails: string[]) => {
    // Filter out any email that matches any of the recipient emails
    const filteredEmails = emails.filter(
      (email) => !recipientEmails.includes(email),
    );

    // If the filtered list is different from the input, it means we removed recipient emails
    if (filteredEmails.length !== emails.length) {
      enqueueSnackbar('Duplicate email not allowed in CC', {
        variant: 'warning',
      });
    }

    setCcEmails(filteredEmails);
  };

  return (
    <>
      <Dialog open={open} onClose={onClose}>
        <div className="max-w-lg pb-2">
          <div className="flex items-start justify-between px-6 pb-2 mb-6 -mx-6 -my-2 border-b">
            <p className="font-bold">Request telematics consent</p>
            <IconButton size="small" onClick={onClose}>
              <HiX />
            </IconButton>
          </div>

          <div className="flex items-start justify-between mb-4">
            <p className="text-xl font-bold">Send this link to the Insured</p>
          </div>
          <p className="mb-4">
            The insured should use this secure link to authorize a connection to
            their telematics provider. This is required to obtain a quote.
          </p>
          <p className="mb-6">
            <a
              target="_blank"
              rel="noreferrer"
              className="underline text-primary-main hover:text-primary-dark"
              href="https://www.nirvanatech.com/why-telematics"
            >
              Read more
            </a>{' '}
            to understand how data usage and privacy are handled.
          </p>

          <p className="mb-3 font-bold">Send link in your own message</p>

          <OutlinedInput
            readOnly
            fullWidth
            value={connectionURL}
            className="mb-6 bg-primary-extraLight text-text-hint"
            endAdornment={
              <InputAdornment position="end">
                <Link
                  underline="none"
                  className="flex items-center cursor-pointer"
                  onClick={() => {
                    clipboard.copy(connectionURL);
                    capture(COPY_TELEMATICS_LINK_SUCCESS, {
                      applicationId,
                    });
                  }}
                >
                  <Show
                    when={clipboard.copied}
                    fallback={
                      <>
                        <Typography
                          variant="caption"
                          color="primary"
                          className="mr-4"
                        >
                          Copy Link
                        </Typography>
                        <HiOutlineDocumentDuplicate />
                      </>
                    }
                  >
                    <Typography
                      variant="caption"
                      color="primary"
                      className="mr-4"
                    >
                      Link Copied
                    </Typography>
                    <HiCheck />
                  </Show>
                </Link>
              </InputAdornment>
            }
          />

          <div className="flex items-center mb-6 space-x-3">
            <hr className="flex-1" />
            <p className="text-text-hint">or</p>
            <hr className="flex-1" />
          </div>

          <p className="mb-3 font-medium">
            Send telematics request through Nirvana
          </p>

          <FormControl component="fieldset" className="mb-4">
            <RadioGroup
              name="sendMethod"
              value={sendMethod}
              onChange={(e) => setSendMethod(e.target.value as SendMethod)}
              row
            >
              <FormControlLabel
                value="email"
                control={<Radio color="primary" size="small" />}
                label="Branded email by Nirvana Tech"
              />
              <FormControlLabel
                value="sms"
                control={<Radio color="primary" size="small" />}
                label="SMS"
              />
            </RadioGroup>
          </FormControl>

          <Switch>
            <Switch.Match when={sendMethod === 'email'}>
              <div className="flex flex-col mb-6">
                <div className="mb-4">
                  <p className="mb-2 text-sm font-medium">Recipient:</p>
                  <ReactMultiEmail
                    emails={recipientEmails.length ? recipientEmails : []}
                    onChange={handleRecipientEmailChange}
                    className="border-text-disabled focus:border-primary-main hover:border-text-primary"
                    placeholder="Insured email"
                    getLabel={(
                      email: string,
                      index: number,
                      removeEmail: (index: number) => void,
                    ) => (
                      <div
                        className="flex items-center px-2 py-1 mx-1 border rounded border-text-disabled"
                        key={index}
                      >
                        <span className="mr-1 text-text-primary">{email}</span>
                        <HiXCircle
                          className="cursor-pointer text-text-hint"
                          onClick={() => removeEmail(index)}
                        />
                      </div>
                    )}
                  />
                </div>

                <div className="mb-4">
                  <p className="mb-2 text-sm font-medium">
                    CC:{' '}
                    <span className="font-normal text-text-hint">
                      Producer email
                    </span>
                  </p>
                  <ReactMultiEmail
                    emails={ccEmails}
                    onChange={handleCcEmailsChange}
                    className="border-text-disabled focus:border-primary-main hover:border-text-primary"
                    placeholder="Producer email"
                    getLabel={(
                      email: string,
                      index: number,
                      removeEmail: (index: number) => void,
                    ) => (
                      <div
                        className="flex items-center px-2 py-1 mx-1 border rounded border-text-disabled"
                        key={index}
                      >
                        <span className="mr-1 text-text-primary">{email}</span>
                        <HiXCircle
                          className="cursor-pointer text-text-hint"
                          onClick={() => removeEmail(index)}
                        />
                      </div>
                    )}
                  />
                  <p className="mt-1 text-xxs text-text-hint">
                    Separate by comma to add multiple
                  </p>
                </div>
              </div>
            </Switch.Match>
            <Switch.Match when={sendMethod === 'sms'}>
              <div className="mb-6">
                <p className="mb-2 text-sm font-medium">Recipient mobile no.</p>
                <div className="flex items-center border rounded border-text-disabled">
                  <div className="flex items-center px-3 py-2 border-r border-text-disabled bg-primary-extraLight">
                    <span className="mr-2">🇺🇸</span>
                    <span className="text-text-hint">+1</span>
                  </div>
                  <PhoneInput
                    country="US"
                    value={phoneNumber}
                    onChange={handlePhoneChange}
                    placeholder="Enter mobile number"
                    className="w-full p-2 outline-none text-text-primary"
                  />
                </div>
              </div>
            </Switch.Match>
          </Switch>

          <div className="flex justify-end gap-2 mt-6">
            <Button
              variant="outlined"
              size="small"
              onClick={() => setIsEmailPreviewOpen(true)}
            >
              View Preview
            </Button>
            <Button
              variant="contained"
              type="button"
              onClick={handleSend}
              size="small"
              disabled={
                (sendMethod === 'email' && !recipientEmails.length) ||
                (sendMethod === 'sms' && (!phoneNumber || !isPhoneValid)) ||
                emailSendingStatus === 'progress' ||
                smsMutation.isLoading
              }
              endIcon={<ArrowForwardIosRounded />}
            >
              Send
            </Button>
          </div>
        </div>
      </Dialog>

      <EmailPreview
        open={isEmailPreviewOpen}
        onClose={() => setIsEmailPreviewOpen(false)}
      />
    </>
  );
}
