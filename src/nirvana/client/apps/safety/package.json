{"name": "@nirvana/safety", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "tsc": "tsc --noEmit", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "build:production": "vite build", "build:staging": "vite build --mode staging", "build:test": "vite build --mode testing", "serve": "vite preview", "preapi:safety": "task graphql:schema", "api:safety": "graphql-codegen --config codegen.yml --watch", "ci:graphql": "graphql-codegen --config codegen.yml"}, "dependencies": {"@apollo/client": "^3.13.9", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "4.1.3", "@material-ui/core": "5.0.0-alpha.29", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "5.0.0-alpha.29", "@nirvana/api": "*", "@nirvana/ui-kit": "*", "@turf/bbox": "^7.2.0", "date-fns": "^2.23.0", "launchdarkly-react-client-sdk": "^3.8.1", "lodash-es": "^4.17.21", "logrocket": "^10.0.0", "mapbox-gl": "^2.8.2", "notistack": "^3.0.0", "percent-round": "^2.3.1", "posthog-js": "^1.257.0", "query-string": "^9.2.2", "react-dropzone": "^14.3.8", "react-highlight-words": "^0.21.0", "react-hook-form": "^7.51.5", "react-icons": "^5.4.0", "react-map-gl": "^7.0.25", "react-markdown": "^7.1.2", "react-query": "^3.39.3", "react-router-hash-link": "^2.4.3", "react-slick": "^0.30.3", "react-use-intercom": "^5.5.0", "react-use-measure": "^2.1.7", "react-youtube": "^10.1.0", "recharts": "^2.1.9", "slick-carousel": "^1.8.1", "use-clipboard-copy": "^0.2.0", "zod": "3.25.64"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typescript": "^2.4.7", "@graphql-codegen/typescript-operations": "^2.3.4", "@graphql-codegen/typescript-react-apollo": "^3.3.7", "@types/react-highlight-words": "^0.20.0", "@types/react-router-hash-link": "^2.4.9", "@types/react-table": "^7.7.2", "@vitejs/plugin-react": "^4.7.0", "fishery": "^2.2.3", "typescript": "^5.7.3", "vite": "^6.3.5", "vite-plugin-checker": "^0.8.0"}}