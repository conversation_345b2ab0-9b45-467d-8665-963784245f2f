import { defineConfig } from 'cypress';
import vitePreprocessor from 'cypress-vite';
import { clerkSetup } from '@clerk/testing/cypress';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.testing' });

export default defineConfig({
  viewportWidth: 1366,
  viewportHeight: 800,
  env: {
    CLERK_TEST_EMAIL: process.env.CYPRESS_CLERK_TEST_EMAIL,
  },
  e2e: {
    setupNodeEvents(on, config) {
      on(
        'file:preprocessor',
        vitePreprocessor({
          configFile: 'vite.cypress.config.ts',
          mode: 'testing',
        }),
      );
      return clerkSetup({
        config,
        options: {
          publishableKey: process.env.VITE_CLERK_PUBLISHABLE_KEY,
        },
      });
    },
  },
  component: {
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
  },
});
