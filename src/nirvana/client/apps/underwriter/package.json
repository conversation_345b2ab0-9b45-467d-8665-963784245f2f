{"name": "@nirvana/underwriter", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "tsc": "tsc --noEmit", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "build:staging": "vite build --mode staging", "build:test": "vite build --mode testing", "build:production": "vite build", "serve": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.30.4", "@clerk/testing": "^1.8.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^1.7.3", "@impler/react": "^1.3.0", "@material-ui/core": "5.0.0-alpha.29", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "5.0.0-alpha.29", "@nirvana/api": "*", "@nirvana/core": "*", "@nirvana/ui": "*", "@nirvana/ui-kit": "*", "@react-oauth/google": "^0.12.2", "@tanstack/react-virtual": "^3.13.12", "@tiptap/extension-placeholder": "^3.0.9", "@tiptap/extension-task-item": "^3.0.9", "@tiptap/extension-task-list": "^3.1.0", "@tiptap/extension-text-align": "^3.0.9", "@tiptap/extension-underline": "^3.0.9", "@tiptap/react": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "axios": "^0.21.1", "clsx": "^2.1.1", "constate": "^3.3.3", "date-fns": "^2.23.0", "dotenv": "^16.5.0", "file-saver": "^2.0.5", "formik": "^2.4.6", "idb-keyval": "^6.2.2", "jszip": "^3.10.1", "launchdarkly-react-client-sdk": "^3.8.1", "lodash-es": "^4.17.21", "marked": "^15.0.12", "match-sorter": "^8.0.3", "notistack": "2.0.1-alpha.5", "posthog-js": "^1.257.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "react-hotkeys-hook": "^5.1.0", "react-icons": "^5.4.0", "react-query": "^3.39.3", "react-resizable-panels": "^3.0.3", "react-table": "^7.7.0", "recharts": "^2.12.7", "turndown": "^7.2.0", "uuid": "^11.1.0", "yup": "^1.4.0"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.12", "@types/node": "^24.1.0", "@types/react-resizable": "^3.0.8", "@types/react-table": "^7.7.2", "@types/turndown": "^5.0.5", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.7.0", "react-resizable": "^3.0.5", "vite": "^6.3.5", "vite-plugin-checker": "^0.8.0", "vite-plugin-html": "^3.2.2", "vite-plugin-pwa": "^1.0.0"}}