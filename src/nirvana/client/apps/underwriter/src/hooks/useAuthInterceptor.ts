import { useAuth } from '@clerk/clerk-react';
import { useEffect } from 'react';
import { configureAuthInterceptor } from 'src/utils/auth';
import { configureApolloAuthInterceptor } from 'src/utils/apollo';

export const useAuthInterceptor = () => {
  const { getToken } = useAuth();

  useEffect(() => {
    const tokenGetter = async () => {
      try {
        return await getToken({
          template: import.meta.env.VITE_CLERK_TOKEN_TEMPLATE,
        });
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Token retrieval failed:', error);
        return null;
      }
    };

    // Configure both Axios and Apollo interceptors
    const authCleanup = configureAuthInterceptor(tokenGetter);
    const apolloCleanup = configureApolloAuthInterceptor(tokenGetter);

    return () => {
      authCleanup();
      apolloCleanup();
    };
  }, [getToken]);
};
