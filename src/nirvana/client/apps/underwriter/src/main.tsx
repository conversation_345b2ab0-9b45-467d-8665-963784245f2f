import * as React from 'react';
import ReactDOM from 'react-dom';
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/clerk-react';
import { init, browserTracingIntegration, addIntegration } from '@sentry/react';

import App from './App';
import './styles/index.css';
import '@nirvana/ui/global.css';
import 'mapbox-gl/dist/mapbox-gl.css';

init({
  // Enable only for production
  dsn: import.meta.env.VITE_SENTRY_DSN, // DSN from sentry project settings
  release: import.meta.env.VITE_RELEASE, // Souremaps are tethered to release
  integrations: [browserTracingIntegration()],
  // Performance Monitoring
  tracesSampleRate: 1.0, //  Capture 100% of the transactions
  // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
  tracePropagationTargets: ['localhost', import.meta.env.VITE_API_URL],
  // Session Replay
  replaysSessionSampleRate: 0,
  replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
});

import('@sentry/react').then((lazyLoadedSentry) => {
  addIntegration(
    lazyLoadedSentry.replayIntegration({
      maskAllText: false,
      blockAllMedia: false,
    }),
  );
});

const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!PUBLISHABLE_KEY) {
  throw new Error('Auth Error: Missing Publishable Key');
}

ReactDOM.render(
  <React.StrictMode>
    <ClerkProvider publishableKey={PUBLISHABLE_KEY} afterSignOutUrl="/">
      <App />
    </ClerkProvider>
  </React.StrictMode>,
  document.getElementById('root'),
);
