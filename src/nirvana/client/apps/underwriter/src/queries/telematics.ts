import { TelematicsReminderEmailInfo } from '@nirvana/api/uw';
import apiService from 'src/utils/api-service';

export const fetchTelematicsStatus = async (applicationId: string) => {
  const { data } =
    await apiService.getApplicationTSPConnectionInfo(applicationId);
  return data;
};

export const updateEmailReminderStatus = async (
  applicationId: string,
  payload: Partial<TelematicsReminderEmailInfo>,
) => {
  const { data } = await apiService.updateTelematicsReminderEmailInfo(
    applicationId,
    payload,
  );

  return data;
};
