import { AuthApi, Configuration, LoginRequest } from '@nirvana/api/auth';
import { cookieStorage } from '@nirvana/core/utils';
import { OAuthConnectionData } from '@nirvana/api/quoting';
import { authApiService } from 'src/utils/api-service';

export const fetchUserProfile = async () => {
  const apiKey = cookieStorage.get({ key: import.meta.env.VITE_AUTH_JWT });
  if (apiKey) {
    const configOptions = new Configuration({ apiKey });
    const authenticatedApiService = new AuthApi(configOptions);
    const { data } = await authenticatedApiService.meGet();
    return data;
  }
  return null;
};

export const fetchUserProfileByEmail = async (credentials: LoginRequest) => {
  const configOptions = new Configuration();
  const apiService = new AuthApi(configOptions);
  const { data } = await apiService.authLoginPost(credentials);

  return data;
};

export const postGmailAuthToken = async (token: OAuthConnectionData) => {
  const { data } = await authApiService.googleAuthPost(token);
  return data;
};

export const fetchGmailProfile = async () => {
  const { data } = await authApiService.googleAuthGet();
  return data;
};

export const fetchCallbackURL = async () => {
  const { data } = await authApiService.googleAuthStartPost();
  return data;
};
