import {
  AncillaryCoverage,
  ApplicationReviewAccountGradeProperty,
  ApplicationReviewCloseReasonsForm,
  ApplicationReviewConfirmBindableQuoteForm,
  ApplicationReviewCoverages,
  ApplicationReviewDeclineReasonsForm,
  ApplicationReviewPackageTypeValue,
  ApplicationReviewSummaryForm,
  ProgramType,
  UpdateApplicationReviewRequest,
} from '@nirvana/api/uw';
import {
  apiService,
  clearanceService,
  nfAdmittedService,
} from 'src/utils/api-service';

export const fetchApplicationReviewById = async (appReviewId: string) => {
  const { data } = await apiService.getApplicationReviewById(appReviewId);
  return data;
};

export const updateApplicationReviewById = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: UpdateApplicationReviewRequest;
}) => {
  const { data } = await apiService.updateApplicationReview(appReviewId, body);
  return data;
};

export const fetchApplicationReviewSummary = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewWidgetSummary(appReviewId);
  return data;
};

export const updateApplicationReviewSummary = async ({
  appReviewId,
  form,
}: {
  appReviewId: string;
  form: ApplicationReviewSummaryForm;
}) => {
  const { data } = await apiService.updateApplicationReviewSummary(
    appReviewId,
    form,
  );
  return data;
};

export const fetchApplicationReviewAccountGrade = async (
  appReviewId: string,
) => {
  const { data } =
    await apiService.getApplicationReviewAccountGrade(appReviewId);
  return data;
};

export const updateApplicationAccountGrade = async ({
  appReviewId,
  accountGrade,
}: {
  appReviewId: string;
  accountGrade: ApplicationReviewAccountGradeProperty;
}) => {
  const { data } = await apiService.updateApplicationReviewAccountGrade(
    appReviewId,
    { grade: accountGrade },
  );
  return data;
};

export const fetchApplicationReviewNotes = async (appReviewId: string) => {
  const { data } = await apiService.getApplicationReviewNotes(appReviewId);
  return data;
};

export const updateApplicationReviewNotes = async ({
  appReviewId,
  notes,
}: {
  appReviewId: string;
  notes: string;
}) => {
  const { data } = await apiService.updateApplicationReviewNotes(appReviewId, {
    notes,
  });
  return data;
};

export const updateApplicationReviewPanelComments = async ({
  appReviewId,
  panel,
  comments,
}: {
  appReviewId: string;
  panel: string;
  comments: string;
}) => {
  const { data } = await apiService.updateApplicationReview(appReviewId, {
    [panel]: {
      comments: comments,
    },
  });
  return data;
};

export const fetchApplicationReviewDocuments = async (appReviewId: string) => {
  const { data } = await apiService.getApplicationReviewDocuments(appReviewId);
  return data;
};

export const uploadApplicationReviewDocuments = async ({
  appReviewId,
  file,
  fileType,
  fileDestinationGroup,
}: {
  appReviewId: string;
  file: any;
  fileType: string;
  fileDestinationGroup: string;
}) => {
  const { data } = await apiService.postApplicationReviewDocuments(
    appReviewId,
    file,
    fileType,
    fileDestinationGroup,
  );
  return data;
};

export const fetchApplicationReviewDocumentLink = async ({
  appReviewId,
  fileHandle,
}: {
  appReviewId: string;
  fileHandle: string;
}) => {
  const { data } = await apiService.getApplicationReviewDocumentLink(
    appReviewId,
    fileHandle,
  );
  return data;
};

export const fetchApplicationReviewQuote = async (appReviewId: string) => {
  const { data } = await apiService.getApplicationReviewQuote(appReviewId);
  return data;
};

export const updateApplicationReviewPackageType = async ({
  appReviewId,
  packageType,
}: {
  appReviewId: string;
  packageType: ApplicationReviewPackageTypeValue;
}) => {
  const { data } = await apiService.updateApplicationReviewPackageType(
    appReviewId,
    {
      packageType: packageType,
    },
  );
  return data;
};

export const updateApplicationReviewQuote = async ({
  appReviewId,
}: {
  appReviewId: string;
}) => {
  const { data } = await apiService.updateApplicationReviewQuote(appReviewId);
  return data;
};

export const declineApplication = async ({
  appReviewId,
}: {
  appReviewId: string;
}) => {
  const { data } = await apiService.declineApplicationReview(
    appReviewId,
    {} as any,
  );
  return data;
};

export const createApplicationBindableQuote = async ({
  appReviewId,
}: {
  appReviewId: string;
}) => {
  const { data } =
    await apiService.createApplicationReviewBindableQuote(appReviewId);
  return data;
};

export const fetchApplicationBindableQuote = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewBindableQuote(appReviewId);
  return data;
};

export const confirmApplicationBindableQuote = async ({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: ApplicationReviewConfirmBindableQuoteForm;
}) => {
  const { data } = await apiService.confirmApplicationReviewBindableQuote(
    appReviewId,
    payload,
  );
  return data;
};

export const fetchApplicationCoverages = async (appReviewId: string) => {
  const { data } = await apiService.getApplicationReviewCoverages(appReviewId);
  return data;
};

export const updateApplicationCoverages = async ({
  appReviewId,
  applicationCoverages,
}: {
  appReviewId: string;
  applicationCoverages: ApplicationReviewCoverages;
}) => {
  const { data } = await apiService.updateApplicationReviewCoverages(
    appReviewId,
    applicationCoverages,
  );
  return data;
};

export const reopenApplication = async ({
  appReviewId,
  comment = '',
}: {
  appReviewId: string;
  comment?: string;
}) => {
  const { data } = await apiService.rollbackApplicaitonReview(appReviewId, {
    comment,
  });
  return data;
};

export const reassignUnderwriter = async ({
  appReviewId,
  underwriterId,
}: {
  appReviewId: string;
  underwriterId: string;
}) => {
  const { data } = await apiService.updateApplicationReviewAssignees(
    appReviewId,
    { underwriterID: underwriterId },
  );

  return data;
};

export const fetchAncilliaryCoverages = async (appReviewId: string) => {
  const { data } = await apiService.getAncillaryCoverages(appReviewId);
  return data;
};

export const updateAncillaryCoverge = async ({
  appReviewId,
  ancillaryCoverage,
}: {
  appReviewId: string;
  ancillaryCoverage: AncillaryCoverage[];
}) => {
  const { data } = await apiService.putAncillaryCoverages(
    appReviewId,
    ancillaryCoverage,
  );
  return data;
};

export const fetchApplicationCloseReasons = async (appReviewId: string) => {
  const { data } = await apiService.getApplicationCloseReasons(appReviewId);
  return data;
};

export const postApplicationCloseReasons = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewCloseReasonsForm;
}) => {
  const { data } = await apiService.closeApplicationReview(appReviewId, body);
  return data;
};

export const fetchApplicationDeclineReasons = async (appReviewId: string) => {
  const { data } = await apiService.getApplicationDeclineReasons(appReviewId);
  return data;
};

export const updateApplicationDeclineReasons = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewDeclineReasonsForm;
}) => {
  const { data } = await apiService.declineApplicationReview(appReviewId, body);
  return data;
};

export const fetchApprovalPermissions = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewPermissions(appReviewId);
  return data;
};

export const fetchConstants = async () => {
  const { data } = await nfAdmittedService.getAdmittedConstants();
  return data;
};

export const fetchRecommendationsData = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOverviewRecommendedAction(appReviewId);
  return data;
};

export const fetchRecommendedActionTrail = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewRecommendedActionTrail(appReviewId);
  return data;
};

export const fetchRecommendedActionNotification = async (
  appReviewId: string,
) => {
  const { data } =
    await apiService.getRecommendedActionNotification(appReviewId);

  return data;
};

export const setRecommendedActionNotification = async ({
  appReviewId,
  notificationId,
}: {
  appReviewId: string;
  notificationId: string;
}) => {
  const { data } =
    await apiService.setRecommendedActionNotificationAcknowledged(
      appReviewId,
      notificationId,
    );

  return data;
};

export const getClearedApplication = async (
  dotNumber: string,
  effectiveDate?: string,
) => {
  const { data } = await clearanceService.applicationClearedApplicationGet(
    dotNumber,
    effectiveDate,
  );
  return data;
};

export const clearApplication = async (applicationID: string) => {
  const { data } =
    await clearanceService.applicationApplicationIDClearPost(applicationID);
  return data;
};

export const fetchFactorRankings = async (appReviewId: string) => {
  const { data } = await apiService.getFactorRankings(appReviewId);
  return data;
};

export const fetchApplicationDeclineReasonsNF = async (appReviewId: string) => {
  const VERSION = 2;
  const { data } = await apiService.getApplicationDeclineReasons(
    appReviewId,
    ProgramType.ProgramTypeNonFleetAdmitted,
    VERSION,
  );
  return data;
};
