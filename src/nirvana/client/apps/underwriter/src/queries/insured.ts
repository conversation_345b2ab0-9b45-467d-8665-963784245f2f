import {
  GenerateProvisionalLossRunsReportsForDotRequest,
  LossRunsReportLinkForPolicy,
} from '@nirvana/api/insured';
import { insuredApiService } from 'src/utils/api-service';

export const generateProvisionalLossRunsReportsForDOT = async (
  payload: GenerateProvisionalLossRunsReportsForDotRequest,
): Promise<LossRunsReportLinkForPolicy[]> => {
  const { data } =
    await insuredApiService.generateProvisionalLossRunsReportsForDOT(payload);

  return data;
};
