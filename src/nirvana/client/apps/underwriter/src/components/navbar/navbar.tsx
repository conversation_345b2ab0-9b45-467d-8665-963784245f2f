import { useMemo } from 'react';
import { useQueries } from 'react-query';
import { IconButton } from '@material-ui/core';
import { HiChevronDown } from 'react-icons/hi';
import KeyboardBackspaceIcon from '@material-ui/icons/KeyboardBackspace';
import { Link, useLocation, useNavigate, useParams } from 'react-router-dom';

import {
  Branding,
  BrandingTypes,
  Menu,
  Show,
  storage,
  Switch,
} from '@nirvana/ui-kit';

import useAuthContext from 'src/hooks/use-auth-context';
import { RecommendationCount } from 'src/components/recommendations';
import { fetchApplicationReviewById } from 'src/queries/applications';
import { fetchEndorsementReviewById } from 'src/pages/endorsement/queries';
import { getNFApplicationSummary } from 'src/pages/admitted-application/queries';
import { fetchEndorsementReviewById as fetchEndorsementReviewv2ById } from 'src/pages/endorsement-v2/queries';

export default function Navbar() {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { user, logout } = useAuthContext();
  const { appReviewId = '', endorsementReviewId = '' } = useParams();

  function handleLogout() {
    logout();
  }

  const [
    { data: appReview },
    { data: nfAppSummary },
    { data: endorsementReview },
    { data: endorsementReviewV2 },
  ] = useQueries([
    {
      queryKey: ['applications', appReviewId],
      queryFn: () => fetchApplicationReviewById(appReviewId),
      enabled: !!appReviewId && !pathname.includes('non-fleet'),
    },
    {
      queryKey: ['app-summary', appReviewId],
      queryFn: () => getNFApplicationSummary(appReviewId),
      enabled: !!appReviewId && pathname.includes('non-fleet'),
    },
    {
      queryKey: ['endorsement', endorsementReviewId],
      queryFn: () => fetchEndorsementReviewById(endorsementReviewId),
      enabled: !!endorsementReviewId && !pathname.includes('non-fleet-v2'),
    },
    {
      queryKey: ['endorsement-v2', endorsementReviewId],
      queryFn: () => fetchEndorsementReviewv2ById(endorsementReviewId),
      enabled: !!endorsementReviewId && pathname.includes('non-fleet-v2'),
    },
  ]);

  const trigger = useMemo(
    () => (
      <div className="flex items-center space-x-3">
        <div className="flex items-center justify-center w-8 h-8 font-mono font-extrabold text-white bg-blue-500 rounded-full">
          {user?.name.charAt(0).toUpperCase()}
        </div>
        <span className="text-sm font-medium">{user?.name}</span>
        <HiChevronDown />
      </div>
    ),
    [user],
  );

  return (
    <nav className="flex items-center justify-between px-8 py-2 space-x-4 bg-white border-b border-primary-extraLight">
      <Show when={appReviewId || endorsementReviewId}>
        <div className="flex space-x-1">
          <IconButton
            onClick={() =>
              navigate(storage.get('selectedPath') ?? '/fleet/applications')
            }
          >
            <KeyboardBackspaceIcon className="text-secondary-main" />
          </IconButton>
          <div className="flex flex-col items-start py-2">
            <Switch>
              <Switch.Match when={appReview?.summary}>
                {({ companyName, applicationShortID }) => (
                  <div className="flex">
                    <div>
                      <p className="mr-2 text-xs font-medium text-secondary-main">
                        {companyName}
                      </p>
                      <p className="mr-2 text-xs font-medium text-info-main">
                        #{applicationShortID}
                      </p>
                    </div>

                    <RecommendationCount />
                  </div>
                )}
              </Switch.Match>

              <Switch.Match when={nfAppSummary}>
                {({ companyName, applicationShortID }) => (
                  <>
                    <p className="mr-2 text-xs font-medium text-secondary-main">
                      {companyName}
                    </p>
                    <p className="mr-2 text-xs font-medium text-info-main">
                      #{applicationShortID}
                    </p>
                  </>
                )}
              </Switch.Match>
              <Switch.Match when={endorsementReview}>
                {({ companyName, shortID }) => (
                  <>
                    <p className="mr-2 text-sm font-medium text-secondary-main">
                      {companyName}
                    </p>
                    <p className="mr-2 text-sm font-medium text-info-main">
                      #{shortID}
                    </p>
                  </>
                )}
              </Switch.Match>
              <Switch.Match when={endorsementReviewV2}>
                {({ companyName, id }) => (
                  <>
                    <p className="mr-2 text-sm font-medium text-secondary-main">
                      {companyName}
                    </p>
                    <p className="mr-2 text-sm font-medium text-info-main">
                      {/* eslint-disable-next-line no-magic-numbers */}#
                      {id.substring(0, 8)}
                    </p>
                  </>
                )}
              </Switch.Match>
            </Switch>
          </div>
        </div>
      </Show>

      <Link className="flex items-center" to="/fleet/applications">
        <Branding brandingType={BrandingTypes.Icon} />
      </Link>

      <Menu trigger={trigger}>
        <Menu.Item onClick={handleLogout}>Logout</Menu.Item>
      </Menu>
    </nav>
  );
}
