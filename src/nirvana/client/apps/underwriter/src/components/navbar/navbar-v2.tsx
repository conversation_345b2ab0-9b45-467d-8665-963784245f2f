import { IconButton } from '@material-ui/core';
import KeyboardBackspaceIcon from '@material-ui/icons/KeyboardBackspace';
import { Show } from '@nirvana/ui';
import { Branding, BrandingTypes, Menu, storage } from '@nirvana/ui-kit';
import { useMemo } from 'react';
import { HiChevronDown } from 'react-icons/hi';
import { Link, useNavigate } from 'react-router-dom';
import useAuthContext from 'src/hooks/use-auth-context';

interface NavbarV2Props {
  companyName?: string;
  shortID?: string;
}

export default function NavbarV2({ companyName, shortID }: NavbarV2Props) {
  const navigate = useNavigate();
  const { user, logout } = useAuthContext();

  function handleLogout() {
    logout();
  }

  const trigger = useMemo(
    () => (
      <div className="flex items-center space-x-3">
        <div className="flex items-center justify-center w-8 h-8 font-mono font-extrabold text-white bg-blue-500 rounded-full">
          {user?.name.charAt(0).toUpperCase()}
        </div>
        <span className="text-sm font-medium">{user?.name}</span>
        <HiChevronDown />
      </div>
    ),
    [user],
  );

  return (
    <nav className="flex items-center justify-between px-8 py-2 space-x-4 bg-white border-b border-primary-extraLight">
      <div className="flex space-x-1">
        <IconButton
          onClick={() =>
            navigate(storage.get('selectedPath') ?? '/fleet/applications')
          }
        >
          <KeyboardBackspaceIcon className="text-secondary-main" />
        </IconButton>
        <div className="flex flex-col items-start py-2">
          <Show when={companyName}>
            <p className="mr-2 text-sm font-medium text-secondary-main">
              {companyName}
            </p>
          </Show>
          <Show when={shortID}>
            <p className="mr-2 text-sm font-medium text-info-main">
              #{shortID}
            </p>
          </Show>
        </div>
      </div>

      <Link className="flex items-center" to="/fleet/applications">
        <Branding brandingType={BrandingTypes.Icon} />
      </Link>

      <Menu trigger={trigger}>
        <Menu.Item onClick={handleLogout}>Logout</Menu.Item>
      </Menu>
    </nav>
  );
}
