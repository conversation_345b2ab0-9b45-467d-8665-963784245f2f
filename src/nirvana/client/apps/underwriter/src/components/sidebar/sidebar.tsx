import clsx from 'clsx';
import { useEffect, useMemo } from 'react';
import { HiChevronDown } from 'react-icons/hi2';
import { NavLink, useLocation, useNavigate } from 'react-router-dom';

import { Branding, BrandingTypes, Menu, Show, storage } from '@nirvana/ui-kit';

import useAuthContext from 'src/hooks/use-auth-context';
import UserGroupActive from 'src/assets/icons/user-group-active.svg?react';
import UserGroupInactive from 'src/assets/icons/user-group-inactive.svg?react';
import UserActive from 'src/assets/icons/user-active.svg?react';
import UserInactive from 'src/assets/icons/user-inactive.svg?react';
import ApplicationsActive from 'src/assets/icons/applications-active.svg?react';
import ApplicationsInactive from 'src/assets/icons/applications-inactive.svg?react';
import EndorsementsActive from 'src/assets/icons/endorsements-active.svg?react';
import EndorsementsInactive from 'src/assets/icons/endorsements-inactive.svg?react';
import { Feature, useFeatureEnabled } from 'src/utils/feature-flags';

// To improve page load performance,
// load Operations module when user enter's mouse on any table row
const loadApplications = () => import('src/pages/applications');
const loadEndorsements = () => import('src/pages/endorsement-list');

type Category = {
  title: string;
  key: string;
  icon: JSX.Element;
  iconActive: JSX.Element;
  subcategories: Array<{
    route: string;
    title: string;
    icon: JSX.Element;
    iconActive: JSX.Element;
    onMouseEnter?: () => Promise<unknown>;
  }>;
};

const getTabs = (
  isReviewReadinessEnabled: boolean,
  isPoliciesListEnabled: boolean,
  isBizAutoEnabled: boolean,
): Category[] => [
  {
    title: 'Fleet',
    key: 'fleet',
    icon: <UserGroupInactive />,
    iconActive: <UserGroupActive className="text-primary-main" />,
    subcategories: [
      {
        route: '/fleet/applications',
        title: 'Applications',
        icon: <ApplicationsInactive />,
        iconActive: <ApplicationsActive className="text-primary-main" />,
        onMouseEnter: loadApplications,
      },
      {
        route: '/fleet/endorsements',
        title: 'Endorsements',
        icon: <EndorsementsInactive />,
        iconActive: <EndorsementsActive className="text-primary-main" />,
        onMouseEnter: loadEndorsements,
      },
      ...(isReviewReadinessEnabled
        ? [
            {
              route: '/fleet/pending-applications',
              title: 'Pending Applications',
              icon: <ApplicationsInactive />,
              iconActive: <ApplicationsActive className="text-primary-main" />,
              onMouseEnter: loadEndorsements,
            },
          ]
        : []),
    ],
  },
  {
    title: 'Non Fleet',
    key: 'non-fleet',
    icon: <UserInactive />,
    iconActive: <UserActive className="text-primary-main" />,
    subcategories: [
      {
        route: '/non-fleet/applications',
        title: 'Applications',
        icon: <ApplicationsInactive />,
        iconActive: <ApplicationsActive className="text-primary-main" />,
        onMouseEnter: loadApplications,
      },
      {
        route: '/non-fleet/endorsements',
        title: 'Endorsements',
        icon: <EndorsementsInactive />,
        iconActive: <EndorsementsActive className=" text-primary-main" />,
        onMouseEnter: loadEndorsements,
      },
      ...(isPoliciesListEnabled
        ? [
            {
              route: '/non-fleet-v2/endorsements',
              title: 'Endorsements-v2',
              icon: <EndorsementsInactive />,
              iconActive: <EndorsementsActive className=" text-primary-main" />,
              onMouseEnter: loadEndorsements,
            },
          ]
        : []),
    ],
  },
  ...(isBizAutoEnabled
    ? [
        {
          title: 'Biz Auto',
          key: 'biz-auto',
          icon: <UserInactive />,
          iconActive: <UserActive className="text-primary-main" />,
          subcategories: [
            {
              route: '/biz-auto/applications',
              title: 'Applications',
              icon: <ApplicationsInactive />,
              iconActive: <ApplicationsActive className="text-primary-main" />,
              onMouseEnter: loadApplications,
            },
          ],
        },
      ]
    : []),
];

export default function Sidebar() {
  const location = useLocation();
  const navigate = useNavigate();
  const isReviewReadinessEnabled = useFeatureEnabled(
    Feature.REVIEW_READINESS,
    false,
  );
  const isPoliciesListEnabled = useFeatureEnabled(
    Feature.FEATURE_POLICIES_LIST,
    false,
  );
  const isBizAutoEnabled = useFeatureEnabled(Feature.BIZ_AUTO, false);

  const { user, logout } = useAuthContext();

  useEffect(() => {
    if (location?.pathname) {
      // Save last location in localStorage so that the user
      // does not need to navigate to the same page again
      storage.set('selectedPath', location.pathname);
    }
  }, [location?.pathname]);

  const activeTabRoutes = getTabs(
    isReviewReadinessEnabled,
    isPoliciesListEnabled,
    isBizAutoEnabled,
  ).find((tab) => location.pathname.startsWith(`/${tab.key}`))?.subcategories;

  const handleCategoryClick = (category: Category) => {
    if (category.subcategories?.length) {
      navigate(category.subcategories[0].route);
    }
  };

  function handleLogout() {
    logout();
  }

  const trigger = useMemo(
    () => (
      <div className="flex items-center space-x-3">
        <div className="flex items-center justify-center w-8 h-8 font-mono font-extrabold text-white bg-blue-500 rounded-full">
          {user?.name.charAt(0).toUpperCase()}
        </div>
        <span className="text-sm font-medium">{user?.name}</span>
        <HiChevronDown />
      </div>
    ),
    [user?.name],
  );

  return (
    <aside className="flex flex-col border-r w-52 2xl:w-72 bg-gray-50">
      <div className="p-4 space-y-4">
        <Branding brandingType={BrandingTypes.Icon} />

        <div
          className={clsx('grid w-full p-1 bg-white rounded-xl', {
            'grid-cols-3': isBizAutoEnabled,
            'grid-cols-2': !isBizAutoEnabled,
          })}
        >
          {getTabs(
            isReviewReadinessEnabled,
            isPoliciesListEnabled,
            isBizAutoEnabled,
          ).map((tab) => {
            const isActive = location.pathname.startsWith(`/${tab.key}`);

            return (
              <div
                key={tab.key}
                className={clsx(
                  'flex flex-col space-y-1 items-center rounded-lg cursor-pointer justify-center h-16',
                  {
                    'bg-[#E9EDFF] font-bold text-primary-main': isActive,
                  },
                )}
                onClick={() => handleCategoryClick(tab)}
              >
                <Show when={isActive} fallback={tab.icon}>
                  {tab.iconActive}
                </Show>
                <span className="text-center">{tab.title}</span>
              </div>
            );
          })}
        </div>
      </div>

      {activeTabRoutes?.map(
        ({ title, icon, iconActive, route, onMouseEnter }) => (
          <NavLink
            to={route}
            key={title}
            onMouseEnter={onMouseEnter}
            className={({ isActive }) =>
              clsx(
                'flex items-center border-l-4 py-3 pl-3 space-x-3 text-sm',
                isActive
                  ? 'border-primary-dark font-semibold text-primary-main bg-primary-extraLight'
                  : 'border-transparent text-text-secondary hover:bg-primary-extraLight',
              )
            }
          >
            {({ isActive }) => (
              <>
                {isActive ? iconActive : icon}
                <span>{title}</span>
              </>
            )}
          </NavLink>
        ),
      )}
      <div className="flex-1" />

      <div className="p-4">
        <Menu trigger={trigger}>
          <Menu.Item onClick={handleLogout}>Logout</Menu.Item>
        </Menu>
      </div>
    </aside>
  );
}
