import { useMemo } from 'react';
import { useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';

import { Button, Dialog } from '@nirvana/ui';
import { RecommendationInfo } from '@nirvana/api/uw';

import Recommendations from './recommendations';
import { fetchRecommendations } from './queries';
import { getSortedRecommendations } from './utils';

type RecommendationModalProps = {
  open: boolean;
  onClose: () => void;
};

export default function RecommendationModal({
  open,
  onClose,
}: RecommendationModalProps) {
  const { appReviewId = '' } = useParams();
  const navigate = useNavigate();
  const { data = [] } = useQuery(['recommendations', { appReviewId }], () =>
    fetchRecommendations(appReviewId),
  );

  const filteredList = useMemo(() => {
    return getSortedRecommendations(data);
  }, [data]);

  const handleExpand = (record: RecommendationInfo) => {
    navigate(
      `/applications/${appReviewId}/${record.panelTypes[0].toLowerCase()}`,
    );
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <Dialog.Content className="w-[720px] max-w-4xl">
        <Dialog.Header>
          <Dialog.Title>Recommendations</Dialog.Title>
        </Dialog.Header>

        <div className="flex flex-col mt-3">
          <Recommendations
            appReviewId={appReviewId}
            list={filteredList}
            onExpand={handleExpand}
          />
        </div>

        <Dialog.Footer>
          <Dialog.Close asChild>
            <Button>Dismiss</Button>
          </Dialog.Close>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
}
