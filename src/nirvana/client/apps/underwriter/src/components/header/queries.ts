import { ApplicationReviewCurrentStatusForm } from '@nirvana/api/uw';
import { apiService, clearanceService } from 'src/utils/api-service';

export async function fetchActions(appReviewId: string) {
  const { data } = await apiService.getApplicationReviewActions(appReviewId);

  return data;
}

export const getClearedApplication = async (
  dotNumber: string,
  effectiveDate?: string,
) => {
  const { data } = await clearanceService.applicationClearedApplicationGet(
    dotNumber,
    effectiveDate,
  );
  return data;
};

export const clearApplication = async (applicationID: string) => {
  const { data } =
    await clearanceService.applicationApplicationIDClearPost(applicationID);
  return data;
};

export const getMSTReferralAppReviewStatus = async (appReviewId: string) => {
  const { data } = await apiService.getCurrentStatus(appReviewId);
  return data;
};

export const updateMSTReferralAppReviewStatus = async (
  appReviewId: string,
  status: ApplicationReviewCurrentStatusForm,
) => {
  const { data } = await apiService.updateCurrentStatus(appReviewId, status);
  return data;
};
