import { useParams } from 'react-router-dom';
import { useCallback, useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';

import { Button, Show, Tooltip } from '@nirvana/ui';
import { ApplicationReviewAction } from '@nirvana/api/uw';
import { createApplicationBindableQuote } from 'src/queries/applications';

import ActionTooltip from '../action-tooltip';
import ApproveModal from '../modals/approve-modal';

type ApproveButtonProps = {
  data?: ApplicationReviewAction;
  UWData?: ApplicationReviewAction;
};

export default function ApproveButton({ data, UWData }: ApproveButtonProps) {
  const { appReviewId = '' } = useParams();
  const [approveModalVisibility, setApproveModalVisibility] = useState(false);

  const queryClient = useQueryClient();

  const { mutate } = useMutation(createApplicationBindableQuote, {
    onSuccess: () => {
      queryClient.invalidateQueries(['bindable-quote', appReviewId]);
    },
  });

  const handleApprove = useCallback(() => {
    mutate({ appReviewId });
    setApproveModalVisibility(true);
  }, [mutate, appReviewId]);

  if (!data && !UWData) {
    return null;
  }

  const isEnabled = data?.isEnabled ?? false;
  const disableReasons = data?.disableReasons;
  const actionType = data?.actionType;
  const tooltipProps = { actionType, content: disableReasons };
  const isUWDisableReasonsPresent = UWData?.disableReasons;

  const ApproveCTA = () => {
    return (
      <Button disabled={!isEnabled} onClick={handleApprove}>
        Approve
      </Button>
    );
  };

  return (
    <>
      <Show
        when={disableReasons || isUWDisableReasonsPresent}
        fallback={<ApproveCTA />}
      >
        <Tooltip>
          <Tooltip.Trigger>
            <ApproveCTA />
          </Tooltip.Trigger>
          <Tooltip.Content>
            <div className="max-h-[80vh] overflow-scroll">
              <ActionTooltip {...tooltipProps} UWData={UWData} />
            </div>
          </Tooltip.Content>
        </Tooltip>
      </Show>

      <ApproveModal
        open={approveModalVisibility}
        onClose={() => setApproveModalVisibility(false)}
      />
    </>
  );
}
