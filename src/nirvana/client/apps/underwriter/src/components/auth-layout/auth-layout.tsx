import { useAuth } from '@clerk/clerk-react';
import * as React from 'react';
import { Navigate, useLocation } from 'react-router-dom';

type AuthLayoutProps = {
  component: React.ComponentType<unknown>;
};

export default function AuthLayout({ component: Component }: AuthLayoutProps) {
  const { isLoaded, isSignedIn } = useAuth();
  const location = useLocation();

  if (!isLoaded) {
    return null;
  }

  if (!isSignedIn) {
    return <Navigate to="/" state={{ from: location }} />;
  }

  return <Component />;
}
