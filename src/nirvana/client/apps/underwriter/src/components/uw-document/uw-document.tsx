import './prosemirror.css';
import StarterKit from '@tiptap/starter-kit';
import { useCallback, useEffect } from 'react';
import TaskItem from '@tiptap/extension-task-item';
import TaskList from '@tiptap/extension-task-list';
import TextAlign from '@tiptap/extension-text-align';
import { CircularProgress } from '@material-ui/core';
import { useMutation, useQueries } from 'react-query';
import Placeholder from '@tiptap/extension-placeholder';
import { EditorContent, useEditor } from '@tiptap/react';
import { useLocation, useParams } from 'react-router-dom';

import {
  ApplicationReviewDetail,
  ApplicationReviewPanelNotes,
} from '@nirvana/api/uw';
import { Show, Switch } from '@nirvana/ui-kit';
import { fetchApplicationReviewById } from 'src/queries/applications';
import Notes from '../application-sidebar/notes';
import PanelComments from '../application-sidebar/panel-comments';

import BubbleMenu from './bubble-menu';
import {
  fetchPanelNotesForApplicationReview,
  updatePanelNotesForApplicationReview,
} from './queries';
import { htmlToMarkdown, markdownToHtml } from './utils';

const extensions = [
  StarterKit.configure({
    heading: { levels: [1] },
  }),
  TextAlign.configure({ types: ['heading', 'paragraph'] }),
  Placeholder.configure({
    placeholder: ({ node }) => {
      if (node.type.name === 'heading') {
        return `Heading ${node.attrs.level}`;
      }

      return 'Add a key finding or action item';
    },
    includeChildren: true,
  }),
  TaskList.configure({
    HTMLAttributes: { class: 'not-prose pl-2' },
  }),
  TaskItem.configure({
    HTMLAttributes: { class: 'flex items-start gap-2 my-2' },
  }),
];

const HTTP_NOT_FOUND = 404;

export default function UwDocument() {
  const { appReviewId = '' } = useParams();
  const { pathname } = useLocation();
  const activeTab = pathname.split(
    '/',
  )[3] as keyof ApplicationReviewDetail['panelWiseReviewInfo'];

  const { mutate } = useMutation(updatePanelNotesForApplicationReview);

  const editor = useEditor({
    extensions,
    content: '',
    autofocus: 'end',
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      mutate({
        appReviewId,
        payload: { notes: { drivers: htmlToMarkdown(html) } },
      });
    },
    editorProps: {
      attributes: {
        class:
          'prose prose-sm prose-h1:text-xl prose-h1:font-semibold prose-p:my-1 max-w-full h-full focus:outline-none focus:ring-0 focus:border-0',
      },
    },
  });

  const setEditorContent = useCallback(
    (content: string) => {
      if (editor) {
        const html = markdownToHtml(content);
        editor.commands.setContent(html);
      }
    },
    [editor],
  );

  const [
    { data: aiNotes, isLoading: aiNotesLoading, error: aiNotesError },
    { data: panelComments },
  ] = useQueries([
    {
      queryKey: ['ai-notes', appReviewId],
      queryFn: () => fetchPanelNotesForApplicationReview(appReviewId),
      retry: false,
      select: (data: ApplicationReviewPanelNotes) => data.notes,
    },
    {
      queryKey: ['panel-comments', appReviewId],
      queryFn: () => fetchApplicationReviewById(appReviewId),
      select: (data: ApplicationReviewDetail) =>
        data.panelWiseReviewInfo[activeTab]?.comments ?? '',
    },
  ]);

  useEffect(() => {
    if (aiNotes) {
      setEditorContent(aiNotes.drivers);
    }
  }, [aiNotes, setEditorContent]);

  return (
    <div className="relative flex flex-col h-full overflow-y-auto bg-gray-50">
      <Switch>
        <Switch.Match when={aiNotesLoading}>
          <div className="flex flex-col items-center justify-center h-full gap-3 px-3 py-2">
            <CircularProgress size={18} />
            <span className="font-medium">Generating key insights...</span>
          </div>
        </Switch.Match>

        <Switch.Match
          when={
            panelComments ||
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (aiNotesError as any)?.response?.status === HTTP_NOT_FOUND
          }
        >
          <Notes />
          <PanelComments />
        </Switch.Match>

        <Switch.Match when={!aiNotesLoading && aiNotes}>
          <Notes className="h-56" />
          <div className="flex-1 bg-white">
            <Show when={editor}>
              {(editor) => <BubbleMenu editor={editor} />}
            </Show>
            <EditorContent
              autoFocus
              editor={editor}
              className="flex-1 px-4 py-2 overflow-y-auto"
            />
          </div>
        </Switch.Match>
      </Switch>
    </div>
  );
}
