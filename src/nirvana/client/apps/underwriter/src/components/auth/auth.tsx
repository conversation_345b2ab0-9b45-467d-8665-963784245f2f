import * as React from 'react';
import { useAuth } from '@clerk/clerk-react';
import { useQuery, useQueryClient } from 'react-query';
import { CircularProgress } from '@material-ui/core';
import { cookieStorage } from '@nirvana/core/utils';

import { fetchUserProfile } from 'src/queries/auth';
import AuthContext from 'src/contexts/auth-context';

const LOGOUT_RETRY_COUNT = 3;
const COOKIE_EXPIRY_DAYS = 365; // 1 year

type AuthProps = {
  children: React.ReactNode;
};

export default function Auth({ children }: AuthProps) {
  const { isSignedIn, isLoaded, signOut, getToken } = useAuth();
  const [isTokenReady, setIsTokenReady] = React.useState(false);

  // Handle setting the JWT cookie when user signs in
  React.useEffect(() => {
    const handleToken = async () => {
      try {
        // Check if cookie already exists (e.g., on page refresh)
        const existingToken = cookieStorage.get({
          key: import.meta.env.VITE_AUTH_JWT,
        });
        if (existingToken) {
          setIsTokenReady(true);
          return;
        }

        // Get new token from Clerk and set cookie
        const token = await getToken({
          template: import.meta.env.VITE_CLERK_TOKEN_TEMPLATE,
        });

        if (token) {
          cookieStorage.set({
            key: import.meta.env.VITE_AUTH_JWT,
            domain: import.meta.env.VITE_AUTH_COOKIE_DOMAIN,
            value: token,
            days: COOKIE_EXPIRY_DAYS,
          });
          setIsTokenReady(true);
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to set JWT cookie:', error);
        setIsTokenReady(false);
      }
    };

    if (isSignedIn && isLoaded) {
      handleToken();
    } else {
      setIsTokenReady(false);
    }
  }, [isSignedIn, isLoaded, getToken]);

  const { data: user } = useQuery('auth', fetchUserProfile, {
    enabled: !!isSignedIn && isTokenReady,
    onError: (error) => {
      // eslint-disable-next-line no-console
      console.error('Failed to fetch user profile:', error);
    },
    // Don't retry on error during sign-out
    retry: isSignedIn ? LOGOUT_RETRY_COUNT : 0,
  });

  const queryClient = useQueryClient();

  async function logout() {
    try {
      // Reset token ready state
      setIsTokenReady(false);

      // First remove the JWT cookie
      cookieStorage.remove({
        key: import.meta.env.VITE_AUTH_JWT,
        domain: import.meta.env.VITE_AUTH_COOKIE_DOMAIN,
      });

      // Clear all queries from the cache
      queryClient.clear();

      // Sign out from Clerk - this will trigger a redirect to "/"
      await signOut();
    } catch (error) {
      // If Clerk sign out fails, force redirect to "/"
      window.location.href = '/';
    }
  }

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center w-screen h-screen">
        <CircularProgress className="text-secondary-light" />
      </div>
    );
  }

  return (
    <AuthContext.Provider value={{ user, logout }}>
      {children}
    </AuthContext.Provider>
  );
}
