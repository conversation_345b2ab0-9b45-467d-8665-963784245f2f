import { cookieStorage } from '@nirvana/core/utils';
import axios from 'axios';

let authInterceptorId: number | undefined;

export const configureAuthInterceptor = (
  getToken: () => Promise<string | null>,
) => {
  // Remove existing interceptor if any
  if (authInterceptorId !== undefined) {
    axios.interceptors.request.eject(authInterceptorId);
  }

  authInterceptorId = axios.interceptors.request.use(async (config) => {
    const token = await getToken();

    if (token) {
      config.headers['CLERK-AUTHORIZATION'] = `Bearer ${token}`;
    } else {
      config.headers.JSESSIONID = cookieStorage.get({
        key: import.meta.env.VITE_AUTH_JWT,
      });
    }

    return config;
  });

  // Return a cleanup function to remove the interceptor
  return () => {
    if (authInterceptorId !== undefined) {
      axios.interceptors.request.eject(authInterceptorId);
      authInterceptorId = undefined;
    }
  };
};
