import { AuthApi } from '@nirvana/api/auth';
import { BusinessAutoApi } from '@nirvana/api/bizAuto';
import { EmailsApi } from '@nirvana/api/emails';
import { EndorsementApi } from '@nirvana/api/endorsements';
import { EndorsementuwApi } from '@nirvana/api/endorsementuw';
import { InsuredApi } from '@nirvana/api/insured';
import { UnderwritingApi as NfUnderwritingApi } from '@nirvana/api/nfuw';
import { ApplicationApi as NonFleetApplicationApi } from '@nirvana/api/non-fleet';
import { ApplicationApi, Configuration } from '@nirvana/api/quoting';
import { UnderwritingApi, ClearanceApi } from '@nirvana/api/uw';

const configOptions = new Configuration();

// Create all API client instances
const apiService = new UnderwritingApi(configOptions);
const applicationClient = new ApplicationApi(configOptions);
const authApiService = new AuthApi(configOptions);
const bizAutoService = new BusinessAutoApi(configOptions);
const clearanceService = new ClearanceApi(configOptions);
const emailApiService = new EmailsApi(configOptions);
const endorsementsService = new EndorsementApi(configOptions);
const endorsementuwService = new EndorsementuwApi(configOptions);
const fleetService = new ApplicationApi(configOptions);
const insuredApiService = new InsuredApi(configOptions);
const nfAdmittedService = new NonFleetApplicationApi(configOptions);
const nfService = new NfUnderwritingApi(configOptions);

// Export all clients
export {
  apiService,
  applicationClient,
  authApiService,
  bizAutoService,
  clearanceService,
  emailApiService,
  endorsementsService,
  endorsementuwService,
  fleetService,
  insuredApiService,
  nfAdmittedService,
  nfService,
};

// Export default as main underwriting API service for backward compatibility
export default apiService;
