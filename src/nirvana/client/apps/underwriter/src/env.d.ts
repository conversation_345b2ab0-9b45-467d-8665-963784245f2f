/**
 * Add Type definition for env variables to enable intellisense
 * For more details, refer -> https://vitejs.dev/guide/env-and-mode.html#intellisense
 */
interface ImportMetaEnv {
  VITE_API_URL: string;
  VITE_MAPBOX_TOKEN: string;
  VITE_AUTH_TOKEN: string;
  VITE_AUTH_COOKIE_DOMAIN?: string;
  VITE_AGENTS_APP_URL: string;
  VITE_SUPPORT_APP_URL: string;
  VITE_SAFETY_APP_URL: string;
  VITE_LOGROCKET_KEY: string;
  VITE_LAUNCHDARKLY_KEY: string;
  VITE_ANALYTICS_API_KEY: string;
  VITE_SENTRY_AUTH_TOKEN: string;
  VITE_SENTRY_DSN?: string;
  VITE_RELEASE?: string;
  VITE_PUBLIC_POSTHOG_KEY?: string;
  VITE_PUBLIC_POSTHOG_HOST?: string;
  VITE_BOARDS_APP_URL: string;
  VITE_UW_AI_API_URL: string;
  VITE_AUTH_JWT: string;
}
