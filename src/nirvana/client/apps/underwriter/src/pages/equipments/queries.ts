import {
  ApplicationReviewEquipmentsUnitsForm,
  AppReviewEquipmentAddRequest,
} from '@nirvana/api/uw';

import { apiService } from 'src/utils/api-service';

export const fetchUnitList = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewEquipmentsUnits(appReviewId);
  return data;
};

export const fetchOwnerOperators = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewEquipmentsOwnerOperators(appReviewId);
  return data;
};

export const fetchAdditionalUnits = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewEquipmentsAdditionalInfoUnits(
      appReviewId,
    );
  return data;
};

export const updateUnitList = async ({
  appReviewId,
  unitList,
}: {
  appReviewId: string;
  unitList: ApplicationReviewEquipmentsUnitsForm;
}) => {
  const { data } = await apiService.updateApplicationReviewEquipmentsUnits(
    appReviewId,
    unitList,
  );
  return data;
};

export const addEquipmentUnit = async ({
  appReviewId,
  unit,
}: {
  appReviewId: string;
  unit: AppReviewEquipmentAddRequest;
}) => {
  const data = apiService.addEquipmentUnitToAppReview(appReviewId, unit);
  return data;
};

export const updateEquipmentList = async ({
  appReviewId,
  equipmentList,
}: {
  appReviewId: string;
  equipmentList: ApplicationReviewEquipmentsUnitsForm;
}) => {
  const { data } = await apiService.updateApplicationReviewEquipmentsUnits(
    appReviewId,
    equipmentList,
  );
  return data;
};
