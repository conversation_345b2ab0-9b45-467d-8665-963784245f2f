import {
  AppState,
  GetCoverageOptionsRequestBody,
  PatchUnderwritingOverridesRequest,
} from '@nirvana/api/bizAuto';
import { bizAutoService } from 'src/utils/api-service';

export const updateOverrides = async ({
  applicationId,
  payload,
}: {
  applicationId: string;
  payload: PatchUnderwritingOverridesRequest;
}) => {
  const { data } =
    await bizAutoService.updateBusinessAutoApplicationUnderwritingOverrides(
      applicationId,
      payload,
    );
  return data;
};

export const approveApplication = async (applicationId: string) => {
  const { data } = await bizAutoService.stateTransitionBusinessAutoApplication(
    applicationId,
    {
      transitionToState: AppState.Approved,
    },
  );
  return data;
};

export const declineApplication = async (applicationId: string) => {
  const { data } = await bizAutoService.stateTransitionBusinessAutoApplication(
    applicationId,
    {
      transitionToState: AppState.Declined,
    },
  );
  return data;
};

export const closeApplication = async (applicationId: string) => {
  const { data } = await bizAutoService.stateTransitionBusinessAutoApplication(
    applicationId,
    {
      transitionToState: AppState.Closed,
    },
  );
  return data;
};

export const fetchPricingInfo = async (applicationId: string) => {
  const { data } =
    await bizAutoService.getBusinessAutoPricingInfo(applicationId);
  return data;
};

export const triggerPricing = async (applicationId: string) => {
  const { data } = await bizAutoService.postTriggerPricingJob(applicationId);
  return data;
};

export const fetchPricingJobStatus = async (applicationId: string) => {
  const { data } =
    await bizAutoService.getBusinessAutoPricingStatus(applicationId);
  return data;
};

export const fetchCoverageOptions = async ({
  applicationId,
  coverages,
}: {
  applicationId: string;
  coverages: GetCoverageOptionsRequestBody;
}) => {
  const { data } = await bizAutoService.getBusinessAutoCoverageOptions(
    applicationId,
    coverages,
  );
  return data;
};
