import {
  AppState,
  PostBusinessAutoAppRequest,
  PutBusinessAutoAppRequest,
} from '@nirvana/api/bizAuto';
import {
  ProgramType,
  TelematicsApplicationConsentLinkRequestInsuredInformation,
} from '@nirvana/api/quoting';
import { bizAutoService, fleetService } from 'src/utils/api-service';

export const createApplication = async (
  payload: PostBusinessAutoAppRequest,
) => {
  const { data } = await bizAutoService.createBusinessAutoApplication(payload);

  return data;
};

export const updateApplication = async (
  applicationId: string,
  payload: PutBusinessAutoAppRequest,
) => {
  const { data } = await bizAutoService.putBusinessAutoApplication(
    applicationId,
    payload,
  );
  return data;
};

export const fetchApplicationDetails = async (applicationId: string) => {
  const { data } =
    await bizAutoService.getBusinessAutoApplication(applicationId);
  return data;
};

export const fetchProducers = async () => {
  const { data } = await bizAutoService.getAvailableProducers();
  return data;
};

export const submitForReview = async (applicationId: string) => {
  const { data } = await bizAutoService.stateTransitionBusinessAutoApplication(
    applicationId,
    {
      transitionToState: AppState.UnderReview,
    },
  );
  return data;
};

export const unsubmitFromReview = async (applicationId: string) => {
  const { data } = await bizAutoService.stateTransitionBusinessAutoApplication(
    applicationId,
    {
      transitionToState: AppState.Created,
    },
  );
  return data;
};

export const createTelematicsConsentLink = async ({
  applicationId,
  payload,
}: {
  applicationId: string;
  payload: TelematicsApplicationConsentLinkRequestInsuredInformation;
}) => {
  const { data } = await fleetService.postTelematicsApplicationConsentLink({
    applicationId: applicationId,
    programType: ProgramType.ProgramTypeBusinessAuto,
    insuredInformation: payload,
  });
  return data.link;
};
