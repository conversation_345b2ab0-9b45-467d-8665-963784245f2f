import {
  ApplicationReviewAssigneesForm,
  CloseApplicationReviewForm,
  DeclineApplicationReviewForm,
  UpdateApplicationReviewDriverRecordForm,
  UpdateApplicationReviewDriversForm,
  UpdateApplicationReviewEquipmentForm,
  UpdateApplicationReviewLossesForm,
  UpdateApplicationReviewOperationForm,
  UpdateApplicationReviewPackagesForm,
  UpdateApplicationReviewSafetyForm,
  UpdateSafetyScoreRequest,
} from '@nirvana/api/nfuw';
import {
  fleetService,
  nfAdmittedService,
  nfService,
} from 'src/utils/api-service';

export async function getNFApplicationFlags(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewFlags(appReviewId);
  return data;
}

export async function getNFApplicationSummary(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewsSummary(appReviewId);
  return data;
}

export async function getNFApplicationTimeline(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewTimeline(appReviewId);
  return data;
}

export async function getNFApplicationPackages(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewsPackages(appReviewId);
  return data;
}

export async function updateNFApplicationPackages({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: UpdateApplicationReviewPackagesForm;
}) {
  const { data } = await nfService.updateApplicationReviewPackages(
    appReviewId,
    payload,
  );
  return data;
}

export async function getNFApplicationOperations(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewOperations(appReviewId);
  return data;
}

export async function updateNFApplicationOperations({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: UpdateApplicationReviewOperationForm;
}) {
  const { data } = await nfService.updateApplicationReviewOperations(
    appReviewId,
    payload,
  );
  return data;
}

export async function getNFApplicationEquipments(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewEquipments(appReviewId);
  return data;
}

export async function updateNFApplicationEquipments({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: UpdateApplicationReviewEquipmentForm;
}) {
  const { data } = await nfService.updateApplicationReviewEquipments(
    appReviewId,
    payload,
  );
  return data;
}

export async function getNFApplicationDrivers(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewDrivers(appReviewId);
  return data;
}

export async function getNFApplicationDriversV2(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewDriversV2(appReviewId);
  return data;
}

export async function updateNFApplicationDrivers({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: UpdateApplicationReviewDriversForm;
}) {
  const { data } = await nfService.updateApplicationReviewDrivers(
    appReviewId,
    payload,
  );
  return data;
}

export async function updateNFApplicationDriver({
  appReviewId,
  dlNumber,
  isExcluded,
  isOutOfState,
}: {
  appReviewId: string;
  dlNumber: string;
  isExcluded: boolean;
  isOutOfState: boolean;
}) {
  const { data } = await nfService.updateApplicationReviewDriver(
    appReviewId,
    dlNumber,
    { isExcluded, isOutOfState },
  );
  return data;
}

export async function getNFApplicationSafetyScore(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewSafetyScore(appReviewId);
  return data;
}

export async function updateNFApplicationSafetyScore({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: UpdateSafetyScoreRequest;
}) {
  const { data } = await nfService.updateApplicationReviewSafetyScoreV2(
    appReviewId,
    { form: payload },
  );
  return data;
}

export async function getNFApplicationSafety(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewSafety(appReviewId);
  return {
    ...data,
    basicScores: data.basicScores
      .filter(({ score }) => score)
      .map((t) => ({
        ...t,
        color: (t.score ?? 0) > t.threshold ? '#FF823C' : '#25B255',
        score: t.score || t.score === 0 ? t.score : 'Inconclusive',
      })),
  };
}

export async function updateNFApplicationSafety({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: UpdateApplicationReviewSafetyForm;
}) {
  const { data } = await nfService.updateApplicationReviewSafety(
    appReviewId,
    payload,
  );
  return data;
}

export async function getNFApplicationLosses(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewLosses(appReviewId);
  return data;
}

export async function updateNFApplicationLosses({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: UpdateApplicationReviewLossesForm;
}) {
  const { data } = await nfService.updateApplicationReviewLosses(
    appReviewId,
    payload,
  );
  return data;
}

export async function pullNFApplicationMVRs(appReviewId: string) {
  const { data } = await nfService.setApplicationReviewMVRPull(appReviewId);
  return data;
}

export async function closeNFApplication({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: CloseApplicationReviewForm;
}) {
  const { data } = await nfService.closeApplicationReview(appReviewId, payload);
  return data;
}

export async function declineNFApplication({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: DeclineApplicationReviewForm;
}) {
  const { data } = await nfService.declineApplicationReview(
    appReviewId,
    payload,
  );
  return data;
}

export async function referNFApplication({
  appReviewId,
  note,
}: {
  appReviewId: string;
  note: string;
}) {
  const { data } = await nfService.referApplicationReview(appReviewId, {
    note,
  });
  return data;
}

export async function approveNFApplication({
  appReviewId,
  note,
}: {
  appReviewId: string;
  note: string;
}) {
  const { data } = await nfService.approveApplicationReview(appReviewId, {
    note,
  });
  return data;
}

export async function getNFApplicationNotes(appReviewId: string) {
  const { data } = await nfService.getApplicationReviewNotes(appReviewId);
  return data;
}

export async function updateNFApplicationNotes({
  appReviewId,
  notes,
}: {
  appReviewId: string;
  notes: string;
}) {
  const { data } = await nfService.updateApplicationReviewNotes(appReviewId, {
    notes,
  });
  return data;
}

export async function getFileDownloadLink(handleId: string) {
  const { data } = await fleetService.applicationFileLinkGet(handleId);

  return data;
}

export async function submitApplicationReviewQuote({
  appReviewId,
  bindable,
}: {
  appReviewId: string;
  bindable?: boolean;
}) {
  const { data } = await nfService.postApplicationReviewQuoteSubmit(
    appReviewId,
    bindable,
  );
  return data;
}

export async function getNFApplicationDocuments(applicationReviewID: string) {
  const { data } =
    await nfService.getApplicationReviewDocuments(applicationReviewID);
  return { ...data, files: data.files ?? [] };
}

export const updateNFApplicationDocuments = async ({
  appReviewId,
  file,
  fileType,
  fileDestinationGroup,
}: {
  appReviewId: string;
  file: any;
  fileType: string;
  fileDestinationGroup: string;
}) => {
  const { data } = await nfService.uploadApplicationReviewDocuments(
    appReviewId,
    file,
    fileType,
    fileDestinationGroup,
  );
  return data;
};

export const reopenApplicationReview = async ({
  appReviewId,
}: {
  appReviewId: string;
}) => {
  const { data } = await nfService.rollbackApplicationReview(appReviewId);
  return data;
};

export async function getAdmittedConstants() {
  const { data } = await nfAdmittedService.getAdmittedConstants();
  return data;
}

export const reassignUnderwriter = async ({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: ApplicationReviewAssigneesForm;
}) => {
  const { data } = await nfService.updateApplicationReviewAssignee(
    appReviewId,
    payload,
  );
  return data;
};

export const fetchViolationsList = async () => {
  const { data } = await nfService.getDriverViolations();
  return data;
};

export const updateDriverViolations = async ({
  appReviewId,
  dlNumber,
  payload,
}: {
  appReviewId: string;
  dlNumber: string;
  payload: UpdateApplicationReviewDriverRecordForm;
}) => {
  const { data } = await nfService.updateApplicationReviewDriverV2(
    appReviewId,
    dlNumber,
    payload,
  );
  return data;
};

export const fetchNonFleetActions = async (appReviewId: string) => {
  const { data } =
    await nfService.getNonFleetApplicationReviewActions(appReviewId);
  return data;
};
