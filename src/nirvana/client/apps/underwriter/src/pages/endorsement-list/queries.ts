import { EndorsementReviewTab, ProgramType } from '@nirvana/api/endorsements';
import { endorsementsService } from 'src/utils/api-service';

export type FetchEndorsementsFilters = {
  cursor?: string;
  pageSize?: number;
  q?: string;
  effectiveDateRange?: {
    start?: string;
    end?: string;
  };
  underwriterId?: string;
  tab?: EndorsementReviewTab;
  programTypes: Array<ProgramType>;
};

export async function fetchEndorsementReviews(
  filters: FetchEndorsementsFilters,
) {
  const { data } = await endorsementsService.listEndorsementReviews(
    filters?.pageSize,
    filters?.cursor,
    filters?.q,
    filters?.effectiveDateRange?.end,
    filters?.effectiveDateRange?.start,
    filters?.underwriterId,
    filters?.tab,
    filters?.programTypes,
  );

  return data;
}

export async function fetchEndorsementsCount(
  countFilters: FetchEndorsementsFilters,
) {
  const { data } = await endorsementsService.endorsementReviewsCountGet(
    countFilters.programTypes,
    countFilters.q,
    countFilters.effectiveDateRange?.end,
    countFilters.effectiveDateRange?.start,
    countFilters.underwriterId,
  );

  return data.counts;
}
