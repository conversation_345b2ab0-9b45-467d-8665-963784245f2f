import { v4 as uuidv4 } from 'uuid';
import { format, parseISO } from 'date-fns';
import {
  ApplicationReviewOperationsGaragingLocationForm,
  ApplicationReviewOperationsProjectedInformationForm,
  ApplicationReviewOperationsRadiusOfOperationForm,
  ApplicationReviewOperationsHazardZonesForm,
  ApplicationReviewOperationsOperatingClassesForm,
  ApplicationReviewOperationsFleetHistoryForm,
  ApplicationReviewWidgetMeta,
  ApplicationReviewOperationsCommoditiesForm,
  ApplicationReviewOperationsVehicleZonesForm,
  SelectApplicationReviewOperationsTerminalLocationForm,
  UpdateApplicationReviewOperationsTerminalLocations,
} from '@nirvana/api/uw';
import { apiService } from 'src/utils/api-service';

export const fetchYearsInBusiness = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsYearsInBusiness(appReviewId);
  return data;
};

export const updateYearsInBusiness = async ({
  appReviewId,
  badgeValues,
}: {
  appReviewId: string;
  badgeValues: Partial<ApplicationReviewWidgetMeta>;
}) => {
  const { data } =
    await apiService.updateApplicationReviewOperationsYearsInBusiness(
      appReviewId,
      { meta: badgeValues },
    );
  return data;
};

export const fetchGaragingLocation = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsGaragingLocation(
      appReviewId,
    );
  return data;
};

export const updateGaragingLocation = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewOperationsGaragingLocationForm;
}) => {
  const { data } =
    await apiService.updateApplicationReviewOperationsGaragingLocation(
      appReviewId,
      body,
    );
  return data;
};

export const fetchTerminalLocations = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsTerminalLocations(
      appReviewId,
    );
  return data;
};

export const fetchProjectedInfo = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsProjectedInformation(
      appReviewId,
    );
  return data;
};

export const fetchProjectedInfoReasons = async (appReviewId: string) => {
  const { data } = await apiService.getMileageEstimateReasons(appReviewId);
  return data;
};

export const updateProjectedInfo = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewOperationsProjectedInformationForm;
}) => {
  const { data } =
    await apiService.updateApplicationReviewOperationsProjectedInformation(
      appReviewId,
      body,
    );
  return data;
};

export const fetchRadiusOfOperation = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsRadiusOfOperation(
      appReviewId,
    );

  // Transforming data recieved from the API to the format we want for the bar chart
  return {
    ...data,
    bucketedData: [
      { name: '0-50', ...data.bucketedData.MileageRadiusBucketZeroToFifty },
      {
        name: '50-200',
        ...data.bucketedData.MileageRadiusBucketFiftyToTwoHundred,
      },
      {
        name: '200-500',
        ...data.bucketedData.MileageRadiusBucketTwoHundredToFiveHundred,
      },
      { name: '500+', ...data.bucketedData.MileageRadiusBucketFiveHundredPlus },
      { name: 'Unknown', ...data.bucketedData.MileageRadiusBucketUnknown },
    ],
  };
};

export const updateRadiusOfOperation = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewOperationsRadiusOfOperationForm;
}) => {
  const { data } =
    await apiService.updateApplicationReviewOperationsRadiusOfOperation(
      appReviewId,
      body,
    );
  return data;
};

export const fetchCustomers = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsCustomers(appReviewId);
  return data;
};

export const fetchVehicleZones = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsVehicleZones(appReviewId);
  return data;
};

export const updateVehicleZones = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewOperationsVehicleZonesForm;
}) => {
  const { data } =
    await apiService.updateApplicationReviewOperationsVehicleZones(
      appReviewId,
      body,
    );
  return data;
};

export const fetchHazardZones = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsHazardZones(appReviewId);
  return {
    ...data,
    hazardZoneStates: data.hazardZoneStates.sort(
      (a, b) => (b.miles ?? 0) - (a.miles ?? 0),
    ),
  };
};

export const updateHazardZones = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewOperationsHazardZonesForm;
}) => {
  const { data } =
    await apiService.updateApplicationReviewOperationsHazardZones(
      appReviewId,
      body,
    );
  return data;
};

export const fetchOperatingClasses = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsOperatingClasses(
      appReviewId,
    );
  return data;
};

export const updateOperatingClasses = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewOperationsOperatingClassesForm;
}) => {
  const { data } =
    await apiService.updateApplicationReviewOperationsOperatingClasses(
      appReviewId,
      body,
    );
  return data;
};

export const fetchCommodities = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsCommodities(appReviewId);
  return {
    ...data,
    data: {
      ...data.data,
      primary: data.data.primary.map((record) => ({ ...record, id: uuidv4() })),
    },
  };
};

export const updateCommodities = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewOperationsCommoditiesForm;
}) => {
  const { data } =
    await apiService.updateApplicationReviewOperationsCommodities(
      appReviewId,
      body,
    );
  return data;
};

export const fetchSupportedCommodities = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsCommoditiesSupportedOperations(
      appReviewId,
    );

  return data;
};

export const fetchFleetHistory = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewOperationsFleetHistory(appReviewId);
  return {
    ...data,
    powerUnitsTrend: data.powerUnitsTrend.map((val) => ({
      ...val,
      date: format(parseISO(val.date), 'yyyy'),
    })),
  };
};

export const updateFleetHistory = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewOperationsFleetHistoryForm;
}) => {
  const { data } =
    await apiService.updateApplicationReviewOperationsFleetHistory(
      appReviewId,
      body,
    );
  return data;
};

export const updateMailingAddress = async ({
  appReviewId,
  form,
}: {
  appReviewId: string;
  form: SelectApplicationReviewOperationsTerminalLocationForm;
}) => {
  const { data } =
    await apiService.postApplicationReviewOperationsTerminalLocationSelect(
      appReviewId,
      form,
    );
  return data;
};

export const updateTerminalLocations = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: UpdateApplicationReviewOperationsTerminalLocations;
}) => {
  const { data } =
    await apiService.patchApplicationReviewOperationsTerminalLocations(
      appReviewId,
      body,
    );
  return data;
};
