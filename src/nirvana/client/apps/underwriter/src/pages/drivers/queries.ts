import {
  ApplicationReviewDriverCreateRequest,
  ApplicationReviewDriverUpdateRequest,
  ApplicationReviewDriversListForm,
} from '@nirvana/api/uw';
import { apiService } from 'src/utils/api-service';

export const fetchDriversList = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewDriversList(appReviewId);

  return {
    ...data,
    drivers: data.drivers.sort((a, b) => {
      // First sort by isDeletedByUW
      if (a.isDeletedByUW && !b.isDeletedByUW) {
        return 1;
      } else if (!a.isDeletedByUW && b.isDeletedByUW) {
        return -1;
      } else {
        // Then sort by name if both are the same in terms of isDeletedByUW
        return (a?.name ?? '').localeCompare(b?.name ?? '');
      }
    }),
  };
};

export const updateDriversList = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewDriversListForm;
}) => {
  const { data } = await apiService.updateApplicationReviewDriversList(
    appReviewId,
    body,
  );
  return data;
};

export const updateDriver = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewDriverUpdateRequest;
}) => {
  const { data } = await apiService.updateApplicationReviewDriver(
    appReviewId,
    body,
  );
  return data;
};

export const pullMVR = async ({ appReviewId }: { appReviewId: string }) => {
  const { data } = await apiService.setMVRPull(appReviewId);
  return data;
};

export const fetchAttractScore = async ({
  appReviewId,
}: {
  appReviewId: string;
}) => {
  const { data } = await apiService.setAttractScore(appReviewId);
  return data;
};

export const createDriver = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewDriverCreateRequest;
}) => {
  const { data } = await apiService.createApplicationReviewDriver(
    appReviewId,
    body,
  );

  return data;
};

export const refetchMVR = async ({ appReviewId }: { appReviewId: string }) => {
  const { data } = await apiService.refetchMVR(appReviewId);

  return data;
};
