import { apiService } from 'src/utils/api-service';
import { ApplicationReviewWidgetMeta } from '@nirvana/api/uw';

export const fetchFinancialsData = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewFinancialsData(appReviewId);
  return data;
};

export const updateFinancialsData = async ({
  appReviewId,
  badgeValues,
}: {
  appReviewId: string;
  badgeValues: Partial<ApplicationReviewWidgetMeta>;
}) => {
  const { data } = await apiService.updateApplicationReviewFinancialsData(
    appReviewId,
    { meta: badgeValues },
  );
  return data;
};
