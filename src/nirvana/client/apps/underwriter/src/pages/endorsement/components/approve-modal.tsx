import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  FormControl,
  InputLabel,
  TextField,
} from '@material-ui/core';
import { EndorsementReviewState, ProgramType } from '@nirvana/api/endorsements';
import { FileDestinationGroup } from '@nirvana/api/quoting';
import { Dialog, FileUploadProgress, Switch } from '@nirvana/ui-kit';
import { AxiosError } from 'axios';
import { useSnackbar } from 'notistack';
import pMap from 'p-map';
import * as React from 'react';
import { useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import ApproveApplicationIcon from 'src/assets/icons/approve-application.svg?react';
import {
  approveEndorsementReview,
  fetchEndorsementReviewById,
  replacePolicyChangeForm,
} from 'src/pages/endorsement/queries';
import { applicationClient } from 'src/utils/api-service';
import formatNumber from 'src/utils/format-number';
import { queryClient } from 'src/utils/query-client';
import ApproveSuccessModal from './approve-success-modal';
import { FilesByName, FileUploadInput } from './file-upload-input';

const ENDORSEMENT_REFRESH_INTERVAL = 5000;

type ApproveModalProps = {
  open: boolean;
  onClose: () => void;
};

export default function ApproveModal({ open, onClose }: ApproveModalProps) {
  const [note, setNote] = useState('');
  const { endorsementReviewId = '' } = useParams();
  const [approveSuccessModalVisibility, setApproveSuccessModalVisibility] =
    useState(false);
  const [quoteStatus, setQuoteStatus] = useState<EndorsementReviewState>(
    EndorsementReviewState.EndorsementReviewStateRefreshingPrice,
  );
  const [policyChangeForm, setPolicyChangeForm] = useState<FilesByName>({});
  const [uploadedSupportingFiles, setUploadedSupportingFiles] =
    useState<FilesByName>({});
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  const isTaskRunning =
    quoteStatus ===
    EndorsementReviewState.EndorsementReviewStateRefreshingPrice;

  const { data, isFetching } = useQuery(
    ['endorsement', endorsementReviewId],
    () => fetchEndorsementReviewById(endorsementReviewId),
    {
      onSuccess: ({ state, policyChangeForm }) => {
        setQuoteStatus(
          state || EndorsementReviewState.EndorsementReviewStateReadyToReview,
        );
        if (policyChangeForm) {
          setPolicyChangeForm({
            [policyChangeForm.handleID]: {
              name: policyChangeForm.fileName,
              handle: policyChangeForm.handleID,
              status: 'succeeded',
            },
          });
        }
      },
      refetchInterval: (data) =>
        data?.state ===
          EndorsementReviewState.EndorsementReviewStateRefreshingPrice ||
        (data?.programType === ProgramType.ProgramTypeNonFleetAdmitted &&
          !data.policyChangeForm)
          ? ENDORSEMENT_REFRESH_INTERVAL
          : false,
    },
  );

  let redirectPath = '';

  switch (data?.programType) {
    case ProgramType.ProgramTypeFleet:
      redirectPath = '/fleet/endorsements';
      break;
    case ProgramType.ProgramTypeNonFleetCanopiusNrb:
    case ProgramType.ProgramTypeNonFleetAdmitted:
      redirectPath = '/non-fleet/endorsements';
      break;
  }

  const { mutate, isLoading } = useMutation(approveEndorsementReview, {
    onSuccess: () => {
      onClose();
      setApproveSuccessModalVisibility(true);
      queryClient.invalidateQueries(['endorsement', endorsementReviewId]);
    },
  });

  const { mutate: handleReplacePolicyForm } = useMutation(
    replacePolicyChangeForm,
    {
      onSuccess: () =>
        queryClient.invalidateQueries(['endorsement', endorsementReviewId]),
    },
  );

  const handleReplacePolicyChangeForm = (files: File[]) => {
    if (files.length > 1) {
      enqueueSnackbar('Please upload a single policy change form', {
        variant: 'error',
        TransitionProps: {
          timeout: 500,
        },
      });
      return;
    }

    const file = files[0];
    handleReplacePolicyForm({
      endorsementReviewId,
      file: file,
      fileType: 'FileTypePDFForm',
    });
  };

  const premiumByCoverage = [];
  if (data?.alApdRateMLOutput?.al !== undefined) {
    premiumByCoverage.push({
      title: 'AL Premium',
      premium: data.alApdRateMLOutput.al,
    });
  }
  if (data?.alApdRateMLOutput?.apd !== undefined) {
    premiumByCoverage.push({
      title: 'APD Premium',
      premium: data.alApdRateMLOutput.apd,
    });
  }

  // handleFileUpload manages the upload of files for either the endorsement forms or its supporting docs
  const handleFileUpload = (
    uploadedFiles: FilesByName,
    setUploadedFiles: (value: React.SetStateAction<FilesByName>) => void,
  ): ((files: File[]) => void) => {
    return async (files: File[]) => {
      const snackbarKey = enqueueSnackbar('Uploading files...', {
        variant: 'info',
        TransitionProps: {
          easing: 'ease-in',
        },
      });

      const responses = await pMap(
        files,
        async (file: File): Promise<FileUploadProgress> => {
          try {
            const response = await applicationClient.applicationFilePost(
              file,
              'FileTypePDFForm',
              FileDestinationGroup.FileDestinationGroupForms,
            );

            return {
              handle: response.data.handle,
              name: file.name,
              status: 'succeeded',
            };
          } catch (err: unknown) {
            const error = err as AxiosError;
            let snackBarMessage = 'Unexpected error while uploading file';
            if (error.response?.data?.message.includes('file size')) {
              snackBarMessage = 'File too heavy';
            }

            enqueueSnackbar(snackBarMessage, {
              variant: 'error',
              TransitionProps: {
                timeout: 500,
                easing: 'ease-in',
              },
            });

            return {
              name: file.name,
              status: 'failed',
            };
          }
        },
        { concurrency: 5 },
      );

      closeSnackbar(snackbarKey);

      const filesAndHandles = responses
        .filter((r): r is FileUploadProgress => !!r)
        .reduce(
          (prev: FilesByName, cur: FileUploadProgress) =>
            cur.handle ? { ...prev, [cur.handle]: cur } : prev,
          { ...uploadedFiles },
        );
      setUploadedFiles(filesAndHandles);
    };
  };

  const handleSupportingFileUpload = (files: File[]) => {
    const fileUploadFunction = handleFileUpload(
      uploadedSupportingFiles,
      setUploadedSupportingFiles,
    );
    return fileUploadFunction(files);
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        primaryAction={
          <Button
            variant="contained"
            onClick={() =>
              mutate({
                endorsementReviewId,
                reason: note,
                formsHandles: Object.keys(policyChangeForm),
                supportingDocsHandles: Object.keys(uploadedSupportingFiles),
              })
            }
            disabled={isFetching || isTaskRunning || isLoading}
          >
            Approve
          </Button>
        }
        secondaryAction={
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
        }
      >
        <div className="flex flex-col items-center max-w-xl text-center">
          <ApproveApplicationIcon />
          <p className="mt-5 text-xl font-semibold text-text-primary">
            Approve Endorsement?
          </p>
          <p className="mt-2 mb-6 text-xs leading-5 text-text-hint">
            The endorsement will be recorded and sent to billing,
            <br /> if applicable. Please review the calculated premium
            <br /> before moving to next step.
          </p>
          <ul className="w-full px-3 mb-2 space-y-2">
            {premiumByCoverage.map(({ title, premium }) => (
              <li key={title} className="flex items-center justify-between">
                <p className="font-normal text-text-hint">{title}</p>
                <Switch fallback={<p className="text-secondary-dark">-</p>}>
                  <Switch.Match when={isFetching || isTaskRunning}>
                    <div className="w-16 h-6 bg-gray-100 rounded animate-pulse" />
                  </Switch.Match>
                  <Switch.Match when={premium}>
                    {(premium) => (
                      <p className="text-text-primary">
                        ${formatNumber(premium)}
                      </p>
                    )}
                  </Switch.Match>
                </Switch>
              </li>
            ))}
          </ul>
          <div className="flex items-center justify-between w-full px-3 py-2 mb-4 rounded bg-gold-tint">
            <p className="text-sm font-normal text-text-primary leading-[20px] tracking-[0.15px]">
              Total Premium
            </p>
            <Switch
              fallback={
                <p className="text-base font-bold text-text-primary">$0</p>
              }
            >
              <Switch.Match when={isFetching || isTaskRunning}>
                <div className="w-20 h-6 rounded bg-text-disabled animate-pulse" />
              </Switch.Match>
              <Switch.Match when={data?.flatCharge}>
                {(premium) => (
                  <p className="text-base font-bold text-text-primary">
                    ${formatNumber(premium)}
                  </p>
                )}
              </Switch.Match>
            </Switch>
          </div>

          <FormControl fullWidth>
            <InputLabel shrink className="text-primary-main">
              Reason (optional)
            </InputLabel>
            <TextField
              rows={5}
              multiline
              value={note}
              className="mt-4"
              placeholder="Write here"
              onChange={(e) => setNote(e.target.value)}
            />
          </FormControl>

          <Divider
            sx={{
              height: 16,
            }}
          />

          <FileUploadInput
            title="Endorsement Policy Change Form (optional)"
            files={policyChangeForm}
            onChange={handleReplacePolicyChangeForm}
            onRemove={(file) => {
              setPolicyChangeForm((prevFiles) =>
                Object.fromEntries(
                  Object.entries(prevFiles)?.filter(
                    ([, uploadedFile]) => uploadedFile?.name !== file?.name,
                  ),
                ),
              );
            }}
          />

          <FileUploadInput
            title="Supporting Files (optional)"
            files={uploadedSupportingFiles}
            onChange={handleSupportingFileUpload}
            onRemove={(file) => {
              for (const uploadedFileHandle of Object.keys(
                uploadedSupportingFiles,
              )) {
                const uploadedFile =
                  uploadedSupportingFiles[uploadedFileHandle];
                if (file?.name === uploadedFile.name) {
                  const newUploadedSupportingFiles = {
                    ...uploadedSupportingFiles,
                  };
                  delete newUploadedSupportingFiles[uploadedFileHandle];
                  setUploadedSupportingFiles(newUploadedSupportingFiles);
                  break;
                }
              }
            }}
          />
        </div>
      </Dialog>

      <ApproveSuccessModal
        open={approveSuccessModalVisibility}
        onClose={() => setApproveSuccessModalVisibility(false)}
        url={redirectPath}
      />
    </>
  );
}
