import { PatchEndorsementReviewForm } from '@nirvana/api/endorsements';
import { endorsementsService, fleetService } from 'src/utils/api-service';

export async function fetchEndorsementReviewById(endorsementReviewId: string) {
  const { data } =
    await endorsementsService.getEndorsementReview(endorsementReviewId);

  return data;
}

export async function updateEndorsementReview({
  endorsementReviewId,
  payload,
}: {
  endorsementReviewId: string;
  payload: PatchEndorsementReviewForm;
}) {
  const { data } = await endorsementsService.updateEndorsementReview(
    endorsementReviewId,
    payload,
  );

  return data;
}

export async function approveEndorsementReview({
  endorsementReviewId,
  reason,
  formsHandles,
  supportingDocsHandles,
}: {
  endorsementReviewId: string;
  reason: string;
  formsHandles?: string[];
  supportingDocsHandles?: string[];
}) {
  const { data } = await endorsementsService.approveEndorsementReview(
    endorsementReviewId,
    {
      reason,
      formsHandles,
      supportingDocsHandles,
    },
  );

  return data;
}

export async function declineEndorsementReview({
  endorsementReviewId,
  reason,
}: {
  endorsementReviewId: string;
  reason: string;
}) {
  const { data } = await endorsementsService.declineEndorsementReview(
    endorsementReviewId,
    { reason },
  );

  return data;
}

export async function pullEndorsementMVRs(endorsementReviewId: string) {
  const { data } =
    await endorsementsService.setEndorsementReviewMVRPull(endorsementReviewId);
  return data;
}

export async function refreshPrice({
  endorsementReviewId,
}: {
  endorsementReviewId: string;
}) {
  const { data } =
    await endorsementsService.refreshEndorsementReviewPrice(
      endorsementReviewId,
    );
  return data;
}

export async function fillPolicyChangeForm({
  endorsementReviewId,
}: {
  endorsementReviewId: string;
}) {
  const { data } =
    await endorsementsService.fillPolicyChangeForm(endorsementReviewId);
  return data;
}

export async function replacePolicyChangeForm({
  endorsementReviewId,
  file,
  fileType,
}: {
  endorsementReviewId: string;
  file: any;
  fileType: string;
}) {
  const { data } = await endorsementsService.replacePolicyChangeForm(
    endorsementReviewId,
    file,
    fileType,
  );
  return data;
}

export async function fetchFileDownloadLink(handleId: string) {
  const { data } = await fleetService.applicationFileLinkGet(handleId);
  return data;
}
