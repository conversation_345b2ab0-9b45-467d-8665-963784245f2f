import { ApplicationReviewDataCompletionTab } from '@nirvana/api/uw';
import { apiService } from 'src/utils/api-service';

type FetchDataCompletionFilters = {
  pageSize?: number;
  cursor?: string;
  q?: string;
  tab?: ApplicationReviewDataCompletionTab;
};

export async function fetchDataCompletionList(
  filters: FetchDataCompletionFilters,
) {
  const { data } =
    await apiService.underwritingApplicationReviewDataCompletionListGet(
      filters?.pageSize,
      filters?.cursor,
      filters?.q,
      filters?.tab,
    );

  return data;
}
