import { ApplicationReviewTab, RecommendedAction } from '@nirvana/api/uw';
import { apiService } from 'src/utils/api-service';

type FetchReviewsFilters = {
  cursor?: string;
  pageSize?: number;
  q?: string;
  effectiveDateRange?: {
    start?: string;
    end?: string;
  };
  underwriterId?: string;
  tab?: ApplicationReviewTab;
  recommendedActions?: RecommendedAction[];
};

export const fetchPaginatedReviews = async (filters: FetchReviewsFilters) => {
  const { data } = await apiService.v2UnderwritingApplicationReviewListGet(
    filters?.pageSize,
    filters?.cursor,
    filters?.q,
    filters?.effectiveDateRange?.end,
    filters?.effectiveDateRange?.start,
    filters?.underwriterId,
    filters?.tab,
    filters?.recommendedActions,
  );

  return data;
};

export const fetchApplicationCount = async (filters: FetchReviewsFilters) => {
  const { data } = await apiService.underwritingApplicationReviewCountGet(
    filters?.q,
    filters?.effectiveDateRange?.end,
    filters?.effectiveDateRange?.start,
    filters?.underwriterId,
    filters?.recommendedActions,
  );

  return data;
};
