import {
  ApplicationReviewLargeLossesForm,
  ApplicationReviewLossAveragesForm,
  ApplicationReviewLossSummaryForm,
  ApplicationReviewLossSummaryV2Form,
  ApplicationReviewLossSummaryV2Request,
} from '@nirvana/api/uw';
import { format, parseISO } from 'date-fns';
import { apiService } from 'src/utils/api-service';

export const fetchLossAverages = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewLossAverages(appReviewId);
  return data;
};

export const updateLossAverages = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewLossAveragesForm;
}) => {
  const { data } = await apiService.updateApplicationReviewLossAverages(
    appReviewId,
    body,
  );
  return data;
};

export const fetchLargeLosses = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewLargeLosses(appReviewId);
  return {
    ...data,
    value: data.value.map((loss, index) => ({ ...loss, id: `${index}` })),
  };
};

export const updateLargeLosses = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewLargeLossesForm;
}) => {
  const { data } = await apiService.updateApplicationReviewLargeLosses(
    appReviewId,
    body,
  );
  return data;
};

export const fetchLossSummary = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewLossSummary(appReviewId);

  return {
    ...data,
    value: data.value.map((t, i) => ({
      id: `row-${i}`,
      coverageType: t.coverageType,
      ...t.summary[0],
      policyPeriod: `${format(
        parseISO(t.summary[0].policyPeriodStartDate),
        'MM/dd/yy',
      )} - ${format(parseISO(t.summary[0].policyPeriodEndDate), 'MM/dd/yy')}`,
      subRows: t.summary
        .filter((_, i) => i > 0)
        .map((s, j) => ({
          ...s,
          policyPeriod: `${format(
            parseISO(s.policyPeriodStartDate),
            'MM/dd/yy',
          )} - ${format(parseISO(s.policyPeriodEndDate), 'MM/dd/yy')}`,
          id: `${i}-subrow-${j}`,
        })),
    })),
  };
};

export const updateLossSummary = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewLossSummaryForm;
}) => {
  const { data } = await apiService.updateApplicationReviewLossSummary(
    appReviewId,
    body,
  );
  return data;
};

export const fetchClaimsHistory = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewClaimHistory(appReviewId);
  return data;
};

export const fetchClaimHistoryData = async ({
  appReviewId,
  documentId,
  policySn,
  claimSn,
}: {
  appReviewId: string;
  documentId: string;
  policySn: number;
  claimSn: number;
}) => {
  const { data } = await apiService.getApplicationReviewClaimHistoryDescription(
    appReviewId,
    documentId,
    policySn,
    claimSn,
  );

  return data;
};

export async function fetchLossSummaryV2(
  appReviewId: string,
  payload: ApplicationReviewLossSummaryV2Request,
) {
  const { data } = await apiService.getApplicationReviewLossSummaryV2(
    appReviewId,
    payload,
  );

  return data;
}

export async function updateLossSummaryV2({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewLossSummaryV2Form;
}) {
  const { data } = await apiService.updateApplicationReviewLossSummaryV2(
    appReviewId,
    body,
  );
  return data;
}
