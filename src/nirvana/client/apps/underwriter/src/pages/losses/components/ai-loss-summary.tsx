import { words } from 'lodash-es';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import { HiOutlineClock } from 'react-icons/hi';
import { type Dispatch, Fragment, type SetStateAction, useState } from 'react';

import {
  ApplicationReviewLossSummaryItemRecordV2,
  CoverageType,
  LossRunAverages,
  LossSummaryCoverageParams,
} from '@nirvana/api/uw';
import { formatNumber, getFormattedDate } from '@nirvana/core/utils';
import { InputNumber, Label, Select, Show, Tag, Tooltip } from '@nirvana/ui';

import { Widget, WidgetTitle } from 'src/components/widget';
import { fetchApplicationReviewQuote } from 'src/queries/applications';

import { fetchLossSummaryV2 } from '../queries';
import EditLossSummaryDialog from './edit-loss-summary-dialog';

const LOSS_RATIO_RANGE = {
  DANGER: 40,
  WARNING: 60,
};

type LossRatioAggregate = {
  periodLabel: string;
  valuePercent: number;
};

export default function AiLossSummary() {
  const { appReviewId = '' } = useParams();
  const [coverageParams, setCoverageParams] =
    useState<LossSummaryCoverageParams[]>();

  useQuery(
    ['quote', appReviewId],
    () => fetchApplicationReviewQuote(appReviewId),
    {
      onSuccess: ({
        autoLiabilityPremium,
        autoPhysicalDamagePremium,
        motorTruckCargoPremium,
      }) => {
        setCoverageParams([
          {
            coverageType: CoverageType.CoverageAutoLiability,
            preClaimDeductible: autoLiabilityPremium?.deductible ?? 0,
            requestedPremiumPerPU: autoLiabilityPremium?.premiumPerUnit ?? 0,
          },
          {
            coverageType: CoverageType.CoverageAutoPhysicalDamage,
            preClaimDeductible: autoPhysicalDamagePremium?.deductible ?? 0,
            requestedPremiumPerPU:
              autoPhysicalDamagePremium?.premiumPerUnit ?? 0,
          },
          {
            coverageType: CoverageType.CoverageMotorTruckCargo,
            preClaimDeductible: motorTruckCargoPremium?.deductible ?? 0,
            requestedPremiumPerPU: motorTruckCargoPremium?.premiumPerUnit ?? 0,
          },
        ]);
      },
    },
  );

  const { data } = useQuery(
    ['loss-summary-v2', appReviewId, coverageParams],
    () =>
      fetchLossSummaryV2(appReviewId, { coverageParams: coverageParams ?? [] }),
    { enabled: !!coverageParams },
  );

  return (
    <Widget title={null}>
      {data?.latestCoverageSummaries.map((covSummary, idx) => (
        <Fragment key={covSummary.coverageType}>
          <div className="flex items-center justify-between mb-6">
            <WidgetTitle>
              {words(covSummary.coverageType.replace('Coverage', '')).join(' ')}
            </WidgetTitle>
            <EditLossSummaryDialog
              data={covSummary.summary}
              coverageType={covSummary.coverageType}
            />
          </div>

          <LossCalculator
            coverageParams={coverageParams}
            setCoverageParams={setCoverageParams}
            coverageType={covSummary.coverageType}
          />
          <LossSummaryTable
            data={covSummary.summary}
            averages={covSummary.averages}
            aggregates={
              covSummary.lossRatioAggregates as unknown as LossRatioAggregate[]
            }
          />
          {idx < data.latestCoverageSummaries.length - 1 && (
            <hr className="mb-4 border-black" />
          )}
        </Fragment>
      ))}
    </Widget>
  );
}

type LossCalculatorProps = {
  coverageType: CoverageType;
  coverageParams?: LossSummaryCoverageParams[];
  setCoverageParams: Dispatch<
    SetStateAction<LossSummaryCoverageParams[] | undefined>
  >;
};

function LossCalculator({
  coverageType,
  coverageParams,
  setCoverageParams,
}: LossCalculatorProps) {
  function handleCoverageParamChange(
    key: keyof LossSummaryCoverageParams,
    newValue: number,
  ) {
    setCoverageParams((prevParams) =>
      prevParams?.map((param) =>
        param.coverageType === coverageType
          ? { ...param, [key]: newValue }
          : param,
      ),
    );
  }

  return (
    <section
      id="loss-calculator"
      className="grid grid-cols-3 gap-6 px-5 py-6 rounded-lg 2xl:gap-10 shadow-tw-sm bg-tw-navy-50"
    >
      <div className="flex flex-col gap-1">
        <p className="font-semibold">Loss Calculator</p>
        <p className="text-tw-gray-700">
          Modify these values to test different loss ratios
        </p>
      </div>

      <div className="flex flex-col gap-1">
        <Label>Pre Claim Deductible</Label>
        <Select
          className="w-full"
          value={coverageParams
            ?.find((param) => param.coverageType === coverageType)
            ?.preClaimDeductible.toString()}
          onValueChange={(value) =>
            handleCoverageParamChange(
              'preClaimDeductible',
              parseInt(value ?? '0'),
            )
          }
        >
          <Select.Trigger>
            <Select.Value placeholder="Select Deductible" />
          </Select.Trigger>
          <Select.Content>
            <Select.Item value="0">$0</Select.Item>
            <Select.Item value="1000">$1,000</Select.Item>
            <Select.Item value="2500">$2,500</Select.Item>
            <Select.Item value="5000">$5,000</Select.Item>
            <Select.Item value="10000">$10,000</Select.Item>
          </Select.Content>
        </Select>
      </div>

      <div className="flex flex-col gap-1">
        <Label>Requested Premium Per Power Unit</Label>
        <InputNumber
          prefix="$"
          thousandSeparator
          value={
            coverageParams?.find((param) => param.coverageType === coverageType)
              ?.requestedPremiumPerPU
          }
          onValueChange={(value: number) => {
            handleCoverageParamChange('requestedPremiumPerPU', value);
          }}
        />
      </div>
    </section>
  );
}

type LossSummaryTableProps = {
  averages: LossRunAverages;
  aggregates: LossRatioAggregate[];
  data: ApplicationReviewLossSummaryItemRecordV2[];
};

function LossSummaryTable({
  data,
  averages,
  aggregates,
}: LossSummaryTableProps) {
  return (
    <section id="loss-table">
      {/* Table Header */}
      <div className="grid grid-cols-6 gap-4 py-3 text-xs font-semibold border-b bg-tw-gray-50 border-tw-gray-200">
        <div className="col-span-2 px-2">Policy Period</div>
        <div className="text-right"># of PUs</div>
        <div className="text-right"># of Claims</div>
        <div className="text-right">Gross Loss</div>
        <div className="px-2 text-right">Loss Ratio</div>
      </div>

      {/* Table Body */}
      <div className="border-b divide-y divide-tw-gray-200">
        {data.map((row, index) => (
          <div
            key={index}
            className="grid grid-cols-6 gap-4 py-4 hover:bg-tw-gray-50"
          >
            <div className="flex flex-col col-span-2 gap-1 px-2">
              <p className="text-sm font-medium text-tw-gray-900">
                {getFormattedDate(row.periodStartDate)} -{' '}
                {getFormattedDate(row.periodEndDate)}
              </p>

              <Show when={row.tags && row.tags.length > 0}>
                <div className="flex gap-1">
                  {row.tags?.map((tag) => (
                    <Tag key={tag} color="red">
                      {tag}
                    </Tag>
                  ))}
                </div>
              </Show>
            </div>
            <div className="flex items-center justify-end gap-1 text-sm text-tw-gray-900">
              <Show when={row.numberOfPowerUnits.override}>
                <Tooltip>
                  <Tooltip.Trigger>
                    <HiOutlineClock className="text-tw-gray-400" />
                  </Tooltip.Trigger>
                  <Tooltip.Content>
                    Original Value: {row.numberOfPowerUnits.value}
                  </Tooltip.Content>
                </Tooltip>
              </Show>

              {row.numberOfPowerUnits.override ?? row.numberOfPowerUnits.value}
            </div>

            <div className="flex items-center justify-end gap-1 text-sm text-tw-gray-900">
              <Show when={row.numberOfClaims.override}>
                <Tooltip>
                  <Tooltip.Trigger>
                    <HiOutlineClock className="text-tw-gray-400" />
                  </Tooltip.Trigger>
                  <Tooltip.Content>
                    Original Value: {row.numberOfClaims.value}
                  </Tooltip.Content>
                </Tooltip>
              </Show>

              {row.numberOfClaims.override ?? row.numberOfClaims.value}
            </div>

            <div className="flex items-center justify-end gap-1 text-sm text-tw-gray-900">
              <Show when={row.grossLoss.override}>
                <Tooltip>
                  <Tooltip.Trigger>
                    <HiOutlineClock className="text-tw-gray-400" />
                  </Tooltip.Trigger>
                  <Tooltip.Content>
                    Original Value: ${formatNumber(row.grossLoss.value)}
                  </Tooltip.Content>
                </Tooltip>
              </Show>
              ${formatNumber(row.grossLoss.override ?? row.grossLoss.value)}
            </div>

            <div className="px-2 text-right">
              <Tag
                color={
                  row.lossRatio < LOSS_RATIO_RANGE.DANGER
                    ? 'red'
                    : row.lossRatio < LOSS_RATIO_RANGE.WARNING
                      ? 'orange'
                      : 'green'
                }
                className="text-xs font-semibold rounded-full"
              >
                {row.lossRatio}%
              </Tag>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-6 gap-4 border-b divide-x">
        <div className="col-span-2 py-4 pl-3 text-xs font-semibold">
          Loss Ratio Aggregates
        </div>
        <div className="flex flex-col col-span-4 gap-2 px-3 py-4">
          {aggregates.map((aggregate) => (
            <div className="flex justify-between" key={aggregate.periodLabel}>
              <p className="font-medium text-tw-gray-600">
                {aggregate.periodLabel}
              </p>
              <p className="text-xl font-medium text-right text-tw-green-700">
                {aggregate.valuePercent}%
              </p>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-6 gap-4 divide-x">
        <div className="col-span-2 py-4 pl-3 text-xs font-semibold">
          Averages
        </div>
        <div className="flex flex-col col-span-2 gap-2 px-3 py-4">
          <div className="flex justify-between">
            <p className="font-medium text-tw-gray-600">Average claim size</p>
            <p className="font-medium text-right">
              ${formatNumber(averages.averageClaimSize)}
            </p>
          </div>

          <div className="flex justify-between">
            <p className="font-medium text-tw-gray-600">Average burn rate</p>
            <p className="font-medium text-right">
              ${formatNumber(averages.averageBurnRate)}
            </p>
          </div>
        </div>

        <div className="flex flex-col col-span-2 gap-2 px-3 py-4">
          <div className="flex justify-between">
            <p className="font-medium text-tw-gray-600">Loss frequency</p>
            <p className="font-medium text-right">
              {averages.lossFrequency.perUnit}/unit
            </p>
          </div>

          <div className="flex justify-between">
            <p className="font-medium text-tw-gray-600" />
            <p className="font-medium text-right">
              {averages.lossFrequency.perMillionMiles}/mil mi
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
