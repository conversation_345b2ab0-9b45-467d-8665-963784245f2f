import { z } from 'zod';
import { useState } from 'react';
import { HiPencil } from 'react-icons/hi';
import { useParams } from 'react-router-dom';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import { useMutation, useQueryClient } from 'react-query';

import {
  ApplicationReviewLossSummaryItemRecordV2,
  CoverageType,
} from '@nirvana/api/uw';
import { getFormattedDate } from '@nirvana/core/utils';
import { Button, Dialog, Form, InputNumber } from '@nirvana/ui';

import { updateLossSummaryV2 } from '../queries';

const lossSummarySchema = z.object({
  summary: z.array(
    z.object({
      periodStartDate: z.string().min(1, 'Period Start Date is required'),
      periodEndDate: z.string().min(1, 'Policy End Period is required'),
      numberOfPowerUnitsOverride: z.coerce
        .number()
        .min(0, 'Number of PUs must be at least 0'),
      numberOfClaimsOverride: z.coerce
        .number()
        .min(0, 'Number of Claims must be at least 0'),
      grossLossOverride: z.coerce
        .number()
        .min(0, 'Gross Loss must be at least 0'),
    }),
  ),
});

type EditLossSummaryDialogProps = {
  coverageType: CoverageType;
  data: ApplicationReviewLossSummaryItemRecordV2[];
};

export default function EditLossSummaryDialog({
  data,
  coverageType,
}: EditLossSummaryDialogProps) {
  const [open, setOpen] = useState(false);
  const { appReviewId = '' } = useParams();

  const form = useForm<z.infer<typeof lossSummarySchema>>({
    defaultValues: {
      summary: data.map((i) => ({
        periodStartDate: i.periodStartDate,
        periodEndDate: i.periodEndDate,
        numberOfPowerUnitsOverride:
          i.numberOfPowerUnits.override ?? i.numberOfPowerUnits.value,
        numberOfClaimsOverride:
          i.numberOfClaims.override ?? i.numberOfClaims.value,
        grossLossOverride: i.grossLoss.override ?? i.grossLoss.value,
      })),
    },
    resolver: zodResolver(lossSummarySchema),
  });

  const { fields } = useFieldArray({ name: 'summary', control: form.control });

  const queryClient = useQueryClient();
  const { mutate, isLoading } = useMutation(updateLossSummaryV2, {
    onSuccess: () => {
      queryClient.invalidateQueries(['loss-summary-v2', appReviewId]);
      setOpen(false);
      form.reset();
    },
  });

  function onSubmit({ summary }: z.infer<typeof lossSummarySchema>) {
    mutate({ appReviewId, body: { data: { coverageType, summary } } });
  }

  function handleOpenChange(open: boolean) {
    setOpen(open);
    if (!open) {
      form.reset();
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <Dialog.Trigger asChild>
        <Button variant="secondary" startIcon={<HiPencil />}>
          Edit Rows
        </Button>
      </Dialog.Trigger>

      <Dialog.Content className="max-w-2xl">
        <Dialog.Header>
          <Dialog.Title>
            Edit {getCoverageAcronym(coverageType)} Loss Data
          </Dialog.Title>
        </Dialog.Header>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-5 gap-4 py-3 text-xs font-semibold border-b bg-tw-gray-50 border-tw-gray-200">
              <div className="col-span-2 px-2">Policy Period</div>
              <div className="text-right"># of PUs</div>
              <div className="text-right"># of Claims</div>
              <div className="text-right">Gross Loss</div>
            </div>

            <div className="grid grid-cols-5 gap-4 py-4">
              {fields.map((field, index) => (
                <>
                  <p className="col-span-2 px-2">
                    {getFormattedDate(field.periodStartDate)} -{' '}
                    {getFormattedDate(field.periodEndDate)}
                  </p>
                  <Form.Field
                    control={form.control}
                    name={`summary.${index}.numberOfPowerUnitsOverride`}
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Control>
                          <InputNumber
                            value={field.value}
                            className="text-right"
                            onValueChange={field.onChange}
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                  <Form.Field
                    control={form.control}
                    name={`summary.${index}.numberOfClaimsOverride`}
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Control>
                          <InputNumber
                            value={field.value}
                            className="text-right"
                            onValueChange={field.onChange}
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                  <Form.Field
                    control={form.control}
                    name={`summary.${index}.grossLossOverride`}
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Control>
                          <InputNumber
                            prefix="$"
                            thousandSeparator
                            value={field.value}
                            className="text-right"
                            onValueChange={field.onChange}
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                </>
              ))}
            </div>

            <Dialog.Footer className="mt-4">
              <Dialog.Close asChild>
                <Button variant="text">Cancel</Button>
              </Dialog.Close>
              <Button type="submit" loading={isLoading}>
                Save
              </Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
}

function getCoverageAcronym(coverageType: CoverageType) {
  switch (coverageType) {
    case CoverageType.CoverageAutoLiability:
      return 'AL';
    case CoverageType.CoverageAutoPhysicalDamage:
      return 'APD';
    case CoverageType.CoverageMotorTruckCargo:
      return 'MTC';
    default:
      return '';
  }
}
