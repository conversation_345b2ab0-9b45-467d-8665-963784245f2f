import { BulkUpdateReviewReadinessTaskRequest } from '@nirvana/api/uw';
import { apiService } from 'src/utils/api-service';

export async function fetchTasksForAppReview(appReviewId: string) {
  const { data } = await apiService.getReviewReadinessTaskList(appReviewId);

  return data;
}

export async function updateTask({
  appReviewId,
  payload,
}: {
  appReviewId: string;
  payload: BulkUpdateReviewReadinessTaskRequest;
}) {
  const { data } = await apiService.bulkUpdateReviewReadinessTasks(
    appReviewId,
    payload,
  );
  return data;
}

export async function markAppReadyForReview({
  appReviewId,
}: {
  appReviewId: string;
}) {
  const { data } = await apiService.readyApplicationReview(appReviewId);
  return data;
}
