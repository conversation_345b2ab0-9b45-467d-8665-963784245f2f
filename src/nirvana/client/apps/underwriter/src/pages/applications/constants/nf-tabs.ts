import { ApplicationReviewTab } from '@nirvana/api/nfuw';

export const nfTabs = [
  { label: 'All', value: ApplicationReviewTab.ApplicationReviewTabAll },
  { label: 'Pending', value: ApplicationReviewTab.ApplicationReviewTabPending },
  {
    label: 'Flex Quote',
    value: ApplicationReviewTab.ApplicationReviewTabPreTelematicsExperiment,
  },
  {
    label: 'Express Lane',
    value: ApplicationReviewTab.ApplicationReviewTabExpressLane,
  },
  {
    label: 'Approved',
    value: ApplicationReviewTab.ApplicationReviewTabApproved,
  },
  {
    label: 'Declined',
    value: ApplicationReviewTab.ApplicationReviewTabDeclined,
  },
  { label: 'Closed', value: ApplicationReviewTab.ApplicationReviewTabClosed },
  {
    label: 'Internal',
    value: ApplicationReviewTab.ApplicationReviewTabInternal,
  },
];
