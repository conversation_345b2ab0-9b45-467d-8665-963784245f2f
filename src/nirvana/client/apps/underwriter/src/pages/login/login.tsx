import { SignIn, useAuth } from '@clerk/clerk-react';
import { AuthLayout, BrandingTypes } from '@nirvana/ui-kit';
import { Navigate, useLocation } from 'react-router-dom';

export default function Login() {
  const { isSignedIn } = useAuth();
  const { state } = useLocation();
  const from =
    state?.from?.pathname ?? // Use location from state
    localStorage.getItem('selectedPath') ?? // Use last location from localStorage
    '/fleet/applications'; // Default location

  if (isSignedIn) {
    return <Navigate to={from} replace />;
  }

  return (
    <AuthLayout
      title="Modern fleet insurance that rewards safety"
      brandingType={BrandingTypes.Inverted}
    >
      <SignIn
        routing="hash"
        appearance={{
          elements: {
            formButtonPrimary: 'bg-primary-main hover:bg-primary-dark',
          },
        }}
      />
    </AuthLayout>
  );
}
