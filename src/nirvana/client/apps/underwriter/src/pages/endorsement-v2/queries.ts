import {
  PatchEndorsementReviewRequestBody,
  PatchEndorsementReviewRequestBodyActionEnum,
  PostEndorsementReviewRefreshPriceRequestBody,
} from '@nirvana/api/endorsementuw';
import { endorsementuwService, fleetService } from 'src/utils/api-service';

export async function fetchEndorsementReviewById(endorsementReviewId: string) {
  const { data } =
    await endorsementuwService.getEndorsementReview(endorsementReviewId);

  return data;
}

export async function updateEndorsementReview({
  endorsementReviewId,
  payload,
}: {
  endorsementReviewId: string;
  payload: PatchEndorsementReviewRequestBody;
}) {
  const { data } = await endorsementuwService.updateEndorsementReview(
    endorsementReviewId,
    payload,
  );

  return data;
}

export async function approveEndorsementReview({
  endorsementReviewId,
  reason,
}: {
  endorsementReviewId: string;
  reason: string;
  formsHandles?: string[];
  supportingDocsHandles?: string[];
}) {
  const { data } = await endorsementuwService.updateEndorsementReview(
    endorsementReviewId,
    {
      action: PatchEndorsementReviewRequestBodyActionEnum.Approve,
      reason,
    },
  );

  return data;
}

export async function declineEndorsementReview({
  endorsementReviewId,
  reason,
}: {
  endorsementReviewId: string;
  reason: string;
}) {
  const { data } = await endorsementuwService.updateEndorsementReview(
    endorsementReviewId,
    { action: PatchEndorsementReviewRequestBodyActionEnum.Decline, reason },
  );

  return data;
}

export async function closeEndorsementReview({
  endorsementReviewId,
  reason,
}: {
  endorsementReviewId: string;
  reason: string;
}) {
  const { data } = await endorsementuwService.updateEndorsementReview(
    endorsementReviewId,
    { action: PatchEndorsementReviewRequestBodyActionEnum.Close, reason },
  );

  return data;
}

export async function pullEndorsementMVRs(endorsementReviewId: string) {
  const { data } = await endorsementuwService.pullMVR(endorsementReviewId);
  return data;
}

export async function refreshPrice({
  endorsementReviewId,
  payload,
}: {
  endorsementReviewId: string;
  payload: PostEndorsementReviewRefreshPriceRequestBody;
}) {
  const { data } = await endorsementuwService.refreshEndorsementReviewPrice(
    endorsementReviewId,
    payload,
  );
  return data;
}

export async function fetchFileDownloadLink(handleId: string) {
  const { data } = await fleetService.applicationFileLinkGet(handleId);
  return data;
}

export async function bindEndorsement({
  endorsementReviewId,
  reason,
  supportingDocsHandles,
}: {
  endorsementReviewId: string;
  reason?: string;
  supportingDocsHandles?: string[];
}) {
  const { data } = await endorsementuwService.bindEndorsement(
    endorsementReviewId,
    { reason, supportingDocsHandles },
  );
  return data;
}

export async function replacePolicyChangeForm({
  endorsementReviewId,
  policyNumber,
  file,
  fileType,
}: {
  endorsementReviewId: string;
  policyNumber: string;
  file: any;
  fileType: string;
}) {
  const { data } = await endorsementuwService.replacePolicyChangeForm(
    endorsementReviewId,
    file,
    fileType,
    policyNumber,
  );
  return data;
}
