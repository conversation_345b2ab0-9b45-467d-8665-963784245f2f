import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>,
  FormControl,
  InputLabel,
  TextField,
} from '@material-ui/core';
import {
  DownloadFileLinkResponse,
  FileDestinationGroup,
} from '@nirvana/api/quoting';
import { Dialog, downloadFile, FileUploadProgress } from '@nirvana/ui-kit';
import { AxiosError } from 'axios';
import { useSnackbar } from 'notistack';
import pMap from 'p-map';
import * as React from 'react';
import { useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import ApproveApplicationIcon from 'src/assets/icons/approve-application.svg?react';
import { applicationClient } from 'src/utils/api-service';
import { queryClient } from 'src/utils/query-client';
import {
  bindEndorsement,
  fetchEndorsementReviewById,
  fetchFileDownloadLink,
  replacePolicyChangeForm,
} from '../queries';
import BindSuccessModal from './bind-success-modal';
import FileInput from './file-input';
import { FilesByName, FileUploadInput } from './file-upload-input';

const ENDORSEMENT_REFRESH_INTERVAL = 5000;

type BindModalProps = {
  open: boolean;
  onClose: () => void;
};

export default function BindModal({ open, onClose }: BindModalProps) {
  const [note, setNote] = useState('');
  const { closeSnackbar, enqueueSnackbar } = useSnackbar();
  const { endorsementReviewId = '' } = useParams();
  const [bindSuccessModalVisibility, setBindSuccessModalVisibility] =
    useState(false);
  const [uploadedSupportingFiles, setUploadedSupportingFiles] =
    useState<FilesByName>({});

  const redirectPath = '/non-fleet-v2/endorsements';

  const { data } = useQuery(
    ['endorsement-v2', endorsementReviewId],
    () => fetchEndorsementReviewById(endorsementReviewId),
    {
      refetchInterval: (data) =>
        !data?.policyChangeForms ? ENDORSEMENT_REFRESH_INTERVAL : false,
    },
  );

  const { mutate, isLoading } = useMutation(bindEndorsement, {
    onSuccess: () => {
      setBindSuccessModalVisibility(true);
      queryClient.invalidateQueries(['endorsement-v2', endorsementReviewId]);
    },
    onError: () => {
      enqueueSnackbar('Failed to bind endorsement.', { variant: 'error' });
    },
  });

  const { mutate: handleReplacePolicyForm } = useMutation(
    replacePolicyChangeForm,
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['endorsement-v2', endorsementReviewId]);
        enqueueSnackbar('Success', { variant: 'success' });
      },
      onError: () => {
        enqueueSnackbar('Failed to replace form.', { variant: 'error' });
      },
    },
  );

  const handleFileUpload = (
    uploadedFiles: FilesByName,
    setUploadedFiles: (value: React.SetStateAction<FilesByName>) => void,
  ): ((files: File[]) => void) => {
    return async (files: File[]) => {
      const snackbarKey = enqueueSnackbar('Uploading files...', {
        variant: 'info',
        TransitionProps: {
          easing: 'ease-in',
        },
      });

      const responses = await pMap(
        files,
        async (file: File): Promise<FileUploadProgress> => {
          try {
            const response = await applicationClient.applicationFilePost(
              file,
              'FileTypePDFForm',
              FileDestinationGroup.FileDestinationGroupForms,
            );

            return {
              handle: response.data.handle,
              name: file.name,
              status: 'succeeded',
            };
          } catch (err: unknown) {
            const error = err as AxiosError;
            let snackBarMessage = 'Unexpected error while uploading file';
            if (error.response?.data?.message.includes('file size')) {
              snackBarMessage = 'File too heavy';
            }

            enqueueSnackbar(snackBarMessage, {
              variant: 'error',
              TransitionProps: {
                timeout: 500,
                easing: 'ease-in',
              },
            });

            return {
              name: file.name,
              status: 'failed',
            };
          }
        },
        { concurrency: 5 },
      );

      closeSnackbar(snackbarKey);

      const filesAndHandles = responses
        .filter((r): r is FileUploadProgress => !!r)
        .reduce(
          (prev: FilesByName, cur: FileUploadProgress) =>
            cur.handle ? { ...prev, [cur.handle]: cur } : prev,
          { ...uploadedFiles },
        );
      setUploadedFiles(filesAndHandles);
    };
  };

  const handleReplacePolicyChangeForm = (policyNumber: string, file: File) => {
    handleReplacePolicyForm({
      endorsementReviewId,
      file: file,
      fileType: 'FileTypePDFForm',
      policyNumber: policyNumber,
    });
  };

  const handleSupportingFileUpload = (files: File[]) => {
    const fileUploadFunction = handleFileUpload(
      uploadedSupportingFiles,
      setUploadedSupportingFiles,
    );
    return fileUploadFunction(files);
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        primaryAction={
          <Button
            variant="contained"
            onClick={() =>
              mutate({
                endorsementReviewId,
                reason: note,
                supportingDocsHandles: Object.keys(uploadedSupportingFiles),
              })
            }
            disabled={isLoading}
          >
            Bind
          </Button>
        }
        secondaryAction={
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
        }
      >
        <div className="flex flex-col max-w-xl text-center">
          <div className="flex flex-col items-center">
            <ApproveApplicationIcon />
          </div>
          <p className="mt-5 text-xl font-semibold text-text-primary">
            Process Endorsement
          </p>
          <p className="mt-2 mb-6 text-xs leading-5 text-text-hint">
            Please process the endorsement only after the agent
            <br />
            has confirmed the premium impact.
          </p>

          <div className="text-left text-xxs text-primary-main">
            <p>Endorsement Policy Change Forms (optional)</p>
            <Divider
              sx={{
                height: 5,
                color: 'transparent',
              }}
            />
          </div>

          {data?.policyChangeForms &&
            data.policyChangeForms.map((changeForm) => (
              <FileInput
                key={changeForm.handleID}
                title={changeForm.policyNumber}
                fileName={changeForm.fileName}
                handleId={changeForm.handleID}
                onUpload={handleReplacePolicyChangeForm}
                onDownload={async (handleId: string) => {
                  if (!handleId) {
                    enqueueSnackbar(
                      'No file handle ID available for download.',
                      {
                        variant: 'warning',
                      },
                    );
                    return;
                  }

                  try {
                    const response: DownloadFileLinkResponse =
                      await fetchFileDownloadLink(handleId);
                    if (response?.link) {
                      downloadFile(response.link);
                      enqueueSnackbar('File download started.', {
                        variant: 'success',
                      });
                    } else {
                      enqueueSnackbar('Download link not found.', {
                        variant: 'error',
                      });
                    }
                  } catch (err: unknown) {
                    const error = err as AxiosError;
                    const errorMessage =
                      error.response?.data?.message ||
                      'API error during file download.';
                    enqueueSnackbar(errorMessage, { variant: 'error' });
                  }
                }}
              />
            ))}

          <FileUploadInput
            title="Supporting Files (optional)"
            files={uploadedSupportingFiles}
            onChange={handleSupportingFileUpload}
            onRemove={(file) => {
              for (const uploadedFileHandle of Object.keys(
                uploadedSupportingFiles,
              )) {
                const uploadedFile =
                  uploadedSupportingFiles[uploadedFileHandle];
                if (file?.name === uploadedFile.name) {
                  const newUploadedSupportingFiles = {
                    ...uploadedSupportingFiles,
                  };
                  delete newUploadedSupportingFiles[uploadedFileHandle];
                  setUploadedSupportingFiles(newUploadedSupportingFiles);
                  break;
                }
              }
            }}
          />

          <FormControl fullWidth className="mt-2">
            <InputLabel shrink className="text-primary-main">
              Reason (optional)
            </InputLabel>
            <TextField
              rows={5}
              multiline
              value={note}
              className="mt-4"
              placeholder="Write here"
              onChange={(e) => setNote(e.target.value)}
            />
          </FormControl>
        </div>
      </Dialog>

      <BindSuccessModal
        open={bindSuccessModalVisibility}
        onClose={() => {
          setBindSuccessModalVisibility(false);
          onClose();
        }}
        url={redirectPath}
      />
    </>
  );
}
