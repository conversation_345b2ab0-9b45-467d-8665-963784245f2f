import { apiService } from 'src/utils/api-service';
import { format, parseISO } from 'date-fns';
import {
  ApplicationReviewSafetyScoreForm,
  ApplicationReviewSafetyBasicScoreThresholdForm,
  ApplicationReviewSafetyBasicScoreTrendForm,
  ApplicationReviewSafetySevereViolationsForm,
  ApplicationReviewSafetyDotRatingForm,
  ApplicationReviewSafetyOOSViolationsForm,
  ApplicationReviewSafetyCrashRecordForm,
} from '@nirvana/api/uw';

export const fetchSafetyScore = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewSafetyScore(appReviewId);

  return {
    ...data,
    riskScore: {
      ...data.riskScore,
      riskScoreTrend: data.riskScore?.riskScoreTrend.map((rec) =>
        rec.isShortHaul ? { ...rec, score: null } : rec,
      ),
    },
    trend: data.trend.map((t) => ({
      ...t,
      timestamp: format(parseISO(t.timestamp), 'MMM yyyy'),
    })),
  };
};

export const updateSafetyScore = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewSafetyScoreForm;
}) => {
  const { data } = await apiService.updateApplicationReviewSafetyScore(
    appReviewId,
    body,
  );
  return data;
};

export const fetchBasicThreshold = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewSafetyBasicScoreThreshold(appReviewId);
  return {
    ...data,
    data: data.data.map((t) => ({
      ...t,
      color: (t.score ?? 0) > t.threshold ? '#FF823C' : '#25B255',
      score: t.score || t.score === 0 ? t.score : 'Inconclusive',
    })),
  };
};

export const updateBasicThreshold = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewSafetyBasicScoreThresholdForm;
}) => {
  const { data } =
    await apiService.updateApplicationReviewSafetyBasicScoreThreshold(
      appReviewId,
      body,
    );
  return data;
};

export const fetchBasicTrend = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewSafetyBasicScoreTrend(appReviewId);
  return {
    ...data,
    data: data.data.map((t) => ({
      ...t,
      month: format(parseISO(t.date), 'MMM yyyy'),
    })),
  };
};

export const updateBasicTrend = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewSafetyBasicScoreTrendForm;
}) => {
  const { data } =
    await apiService.updateApplicationReviewSafetyBasicScoreTrend(
      appReviewId,
      body,
    );
  return data;
};

export const fetchOosSummary = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewSafetyOOSViolations(appReviewId);
  return data;
};

export const updateOosSummary = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewSafetyOOSViolationsForm;
}) => {
  const { data } = await apiService.updateApplicationReviewSafetyOOSViolations(
    appReviewId,
    body,
  );
  return data;
};

export const fetchSevereViolations = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewSafetySevereViolations(appReviewId);
  return data;
};

export const updateSevereViolations = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewSafetySevereViolationsForm;
}) => {
  const { data } =
    await apiService.updateApplicationReviewSafetySevereViolations(
      appReviewId,
      body,
    );
  return data;
};

export const fetchDotRating = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewSafetyDotRating(appReviewId);
  return data;
};

export const updateDotRating = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewSafetyDotRatingForm;
}) => {
  const { data } = await apiService.updateApplicationReviewSafetyDotRating(
    appReviewId,
    body,
  );
  return data;
};

export const fetchISSScore = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewSafetyISSScoreTrend(appReviewId);
  return {
    ...data,
    trend: data.trend.map((t) => ({
      ...t,
      month: format(parseISO(t.timestamp), 'MMM yyyy'),
    })),
  };
};

export const updateISSScore = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewSafetyBasicScoreTrendForm;
}) => {
  const { data } = await apiService.updateApplicationReviewSafetyISSScoreTrend(
    appReviewId,
    body,
  );
  return data;
};

export const fetchSafetyCrashRecords = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewSafetyCrashRecord(appReviewId);
  return data;
};

export const updateSafetyCrashRecords = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewSafetyCrashRecordForm;
}) => {
  const { data } = await apiService.updateApplicationReviewSafetyCrashRecord(
    appReviewId,
    body,
  );
  return data;
};
