import { ApplicationReviewNegotiatedRates } from '@nirvana/api/uw';
import { apiService } from 'src/utils/api-service';

export const fetchNegotiatedRates = async (appReviewId: string) => {
  const { data } =
    await apiService.getApplicationReviewNegotiatedRates(appReviewId);
  return data;
};

export const updateNegotiatedRate = async ({
  appReviewId,
  body,
}: {
  appReviewId: string;
  body: ApplicationReviewNegotiatedRates;
}) => {
  const { data } = await apiService.updateApplicationReviewNegotiatedRates(
    appReviewId,
    body,
  );

  return data;
};
