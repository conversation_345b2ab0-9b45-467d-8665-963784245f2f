import {
  ApplicationReviewMstReferralReviewFormData,
  ApplicationReviewReportForm,
  ApplicationReviewVinVisibilityChecklistPut,
} from '@nirvana/api/uw';
import { apiService } from 'src/utils/api-service';

export async function fetchVinVisibility(appReviewId: string) {
  const { data } = await apiService.getVinVisibility(appReviewId);

  return data;
}

export async function fetchVinVisibilityChecklist(appReviewId: string) {
  const { data } = await apiService.getVinVisibilityCheckList(appReviewId);

  return data;
}

export async function updateVinVisibilityChecklist({
  appReviewId,
  checklist,
}: {
  appReviewId: string;
  checklist: ApplicationReviewVinVisibilityChecklistPut;
}) {
  const { data } = await apiService.updateVinVisibilityCheckList(
    appReviewId,
    checklist,
  );

  return data;
}

export async function fetchMstReferral(appReviewId: string) {
  const { data } = await apiService.getMstReferralReview(appReviewId);
  return data;
}

export type UpdateMstReferralDto = {
  appReviewId: string;
  applicationReviewMstReferralReviewForm: ApplicationReviewMstReferralReviewFormData;
};

export async function updateMstReferral({
  appReviewId,
  applicationReviewMstReferralReviewForm,
}: UpdateMstReferralDto) {
  const { data } = await apiService.updateMstReferralReview(appReviewId, {
    data: applicationReviewMstReferralReviewForm,
  });

  return data;
}

export type DownloadMstReferralPacketDto = {
  appReviewId: string;
  applicationReviewReportForm: ApplicationReviewReportForm;
};

export async function downloadMstReferralPacket({
  appReviewId,
  applicationReviewReportForm,
}: DownloadMstReferralPacketDto) {
  const { data } = await apiService.postReport(
    appReviewId,
    applicationReviewReportForm,
  );
  return data;
}
