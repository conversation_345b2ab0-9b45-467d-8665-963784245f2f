/// <reference types="cypress" />
import { mockMe, RoutesHelper } from '@nirvana/core/testUtils';
import 'cypress-recurse/commands';
import { addClerkCommands } from '@clerk/testing/cypress';

addClerkCommands({ Cypress, cy });

const routesHelper = new RoutesHelper();
const baseUrl = routesHelper.getBaseUrl();

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Assert the current URL
       * @param route
       * @example cy.assertRoute('/page-2')
       */
      assertRoute(route: string): Chainable<any>;

      /**
       * Get the HTML Element by data-cy attribute
       * @param dataCyAttribute
       * @example cy.getByData('email-input')
       */
      getByData(dataCyAttribute: string): Chainable<any>;

      /**
       * Logs in the UW app using Clerk authentication
       */
      login(): Chainable<void>;
    }
  }
}

Cypress.Commands.add('assertRoute', (route) => {
  cy.url().should('equal', `${window.location.origin}${route}`);
});

Cypress.Commands.add('getByData', (selector) => {
  return cy.get(`[data-cy=${selector}]`);
});

Cypress.Commands.add('login', () => {
  // Set up API mocks for every test (not cached). Ideally this should not be done here, but we're
  // keeping it for both convenience and backwards compatibility (earlier non-cached version was
  // built that way).
  mockMe();

  cy.session(
    'user-cached-session',
    () => {
      cy.visit(`${baseUrl}/`);
      cy.clerkSignIn({
        strategy: 'email_code',
        identifier: Cypress.env('CLERK_TEST_EMAIL'),
      });
    },
    {
      validate() {
        // Check that Clerk's short‑lived session cookie is present
        cy.getCookie('__session').should('have.property', 'value');
      },
      cacheAcrossSpecs: true, // Cache the session across all test files
    },
  );
});
