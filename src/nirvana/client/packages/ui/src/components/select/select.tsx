import * as React from 'react';
import { Select as SelectPrimitive } from 'radix-ui';
import { Hi<PERSON>he<PERSON>ronDown, Hi<PERSON><PERSON><PERSON><PERSON>U<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Hi<PERSON> } from 'react-icons/hi';
import { cn } from '@nirvana/core/utils';
import Show from '../show';

const Select = ({
  children,
  className,
  onValueChange,
  allowClear = false,
  ...props
}: Omit<
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Root>,
  'onValueChange'
> & {
  className?: string;
  allowClear?: boolean;
  onValueChange?: (value: string | undefined) => void;
}) => {
  // Workaround: Radix UI Select has no uncontrolled clear API,
  // so we bump the key to force a remount and reset internal state.
  const [key, setKey] = React.useState(0);

  function handleClear(e: React.MouseEvent) {
    e.stopPropagation();
    onValueChange?.(undefined);
    setKey((prev) => prev + 1); // Reset the select component
  }

  return (
    <div
      className={cn('relative group w-fit', className)}
      data-allow-clear={allowClear}
    >
      <SelectPrimitive.Root key={key} onValueChange={onValueChange} {...props}>
        {children}
        <Show when={allowClear}>
          <button
            tabIndex={-1}
            onClick={handleClear}
            aria-label="Clear selection"
            className="absolute hidden transform -translate-y-1/2 group-hover:block right-4 top-1/2"
          >
            <HiX />
          </button>
        </Show>
      </SelectPrimitive.Root>
    </div>
  );
};
Select.displayName = SelectPrimitive.Root.displayName;

const SelectGroup = SelectPrimitive.Group;

const SelectValue = SelectPrimitive.Value;

const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>
>(({ className, children, ...props }, ref) => {
  return (
    <SelectPrimitive.Trigger
      className={cn(
        'flex w-full h-9 items-center justify-between px-4 py-2 bg-white shadow-tw-sm rounded-lg data-[placeholder]:text-tw-gray-600 focus:outline-none disabled:cursor-not-allowed [&>span]:truncate aria-invalid:border aria-invalid:border-tw-red-600',
        className,
      )}
      {...props}
      ref={ref}
    >
      {children}
      <SelectPrimitive.Icon asChild>
        <HiChevronDown className="w-4 h-4 text-tw-gray-1000 group-hover:group-data-[allow-clear=true]:hidden" />
      </SelectPrimitive.Icon>
    </SelectPrimitive.Trigger>
  );
});
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName;

const SelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn(
      'flex cursor-default items-center justify-center py-1',
      className,
    )}
    {...props}
  >
    <HiChevronUp className="w-4 h-4" />
  </SelectPrimitive.ScrollUpButton>
));
SelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;

const SelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn(
      'flex cursor-default items-center justify-center py-1',
      className,
    )}
    {...props}
  >
    <HiChevronDown className="w-4 h-4" />
  </SelectPrimitive.ScrollDownButton>
));
SelectScrollDownButton.displayName =
  SelectPrimitive.ScrollDownButton.displayName;

const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = 'popper', ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        'relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-lg bg-white shadow-tw-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]',
        {
          'my-2 data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1':
            position === 'popper',
        },
        className,
      )}
      position={position}
      {...props}
    >
      <SelectScrollUpButton />

      <SelectPrimitive.Viewport
        className={cn('p-2', {
          'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]':
            position === 'popper',
        })}
      >
        {children}
      </SelectPrimitive.Viewport>
      <SelectScrollDownButton />
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
));
SelectContent.displayName = SelectPrimitive.Content.displayName;

const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn('py-1.5 pl-2 pr-8 text-sm font-semibold', className)}
    {...props}
  >
    {children}
  </SelectPrimitive.Label>
));
SelectLabel.displayName = SelectPrimitive.Label.displayName;

const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      'relative flex items-center py-1.5 pl-2 pr-8 w-full text-sm outline-none cursor-default select-none rounded focus:bg-tw-gray-100 data-[disabled]:pointer-events-none',
      className,
    )}
    {...props}
  >
    <span className="absolute right-2 flex h-3.5 w-3.5 items-center justify-center">
      <SelectPrimitive.ItemIndicator>
        <HiCheck className="w-4 h-4" />
      </SelectPrimitive.ItemIndicator>
    </span>

    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
));
SelectItem.displayName = SelectPrimitive.Item.displayName;

const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn('-mx-1 my-1 h-px bg-tw-gray-100', className)}
    {...props}
  />
));
SelectSeparator.displayName = SelectPrimitive.Separator.displayName;

export default Object.assign(Select, {
  Content: SelectContent,
  Group: SelectGroup,
  Item: SelectItem,
  Label: SelectLabel,
  Separator: SelectSeparator,
  Value: SelectValue,
  Trigger: SelectTrigger,
});
