// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: jobber/jobber.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessorStatus int32

const (
	ProcessorStatus_NOT_STARTED ProcessorStatus = 0
	ProcessorStatus_RUNNING     ProcessorStatus = 1
	ProcessorStatus_DRAINING    ProcessorStatus = 2
	ProcessorStatus_STOPPED     ProcessorStatus = 4
)

// Enum value maps for ProcessorStatus.
var (
	ProcessorStatus_name = map[int32]string{
		0: "NOT_STARTED",
		1: "RUNNING",
		2: "DRAINING",
		4: "STOPPED",
	}
	ProcessorStatus_value = map[string]int32{
		"NOT_STARTED": 0,
		"RUNNING":     1,
		"DRAINING":    2,
		"STOPPED":     4,
	}
)

func (x ProcessorStatus) Enum() *ProcessorStatus {
	p := new(ProcessorStatus)
	*p = x
	return p
}

func (x ProcessorStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessorStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_jobber_jobber_proto_enumTypes[0].Descriptor()
}

func (ProcessorStatus) Type() protoreflect.EnumType {
	return &file_jobber_jobber_proto_enumTypes[0]
}

func (x ProcessorStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessorStatus.Descriptor instead.
func (ProcessorStatus) EnumDescriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{0}
}

type ClusterIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ClusterIdResponse) Reset() {
	*x = ClusterIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClusterIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterIdResponse) ProtoMessage() {}

func (x *ClusterIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterIdResponse.ProtoReflect.Descriptor instead.
func (*ClusterIdResponse) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{0}
}

func (x *ClusterIdResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type JobRunId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId string `protobuf:"bytes,1,opt,name=jobId,proto3" json:"jobId,omitempty"`
	RunId int64  `protobuf:"varint,2,opt,name=runId,proto3" json:"runId,omitempty"`
}

func (x *JobRunId) Reset() {
	*x = JobRunId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobRunId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobRunId) ProtoMessage() {}

func (x *JobRunId) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobRunId.ProtoReflect.Descriptor instead.
func (*JobRunId) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{1}
}

func (x *JobRunId) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *JobRunId) GetRunId() int64 {
	if x != nil {
		return x.RunId
	}
	return 0
}

type JobRun struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          *JobRunId `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Metadata    []byte    `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
	State       []byte    `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	CreatorUUID string    `protobuf:"bytes,4,opt,name=creatorUUID,proto3" json:"creatorUUID,omitempty"`
}

func (x *JobRun) Reset() {
	*x = JobRun{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobRun) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobRun) ProtoMessage() {}

func (x *JobRun) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobRun.ProtoReflect.Descriptor instead.
func (*JobRun) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{2}
}

func (x *JobRun) GetId() *JobRunId {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *JobRun) GetMetadata() []byte {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *JobRun) GetState() []byte {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *JobRun) GetCreatorUUID() string {
	if x != nil {
		return x.CreatorUUID
	}
	return ""
}

type ManyJobRuns struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobRuns []*JobRun `protobuf:"bytes,1,rep,name=jobRuns,proto3" json:"jobRuns,omitempty"`
}

func (x *ManyJobRuns) Reset() {
	*x = ManyJobRuns{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManyJobRuns) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManyJobRuns) ProtoMessage() {}

func (x *ManyJobRuns) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManyJobRuns.ProtoReflect.Descriptor instead.
func (*ManyJobRuns) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{3}
}

func (x *ManyJobRuns) GetJobRuns() []*JobRun {
	if x != nil {
		return x.JobRuns
	}
	return nil
}

type AddJobRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegistryKey string  `protobuf:"bytes,1,opt,name=registryKey,proto3" json:"registryKey,omitempty"`
	JobIdSuffix string  `protobuf:"bytes,2,opt,name=jobIdSuffix,proto3" json:"jobIdSuffix,omitempty"`
	SerMsg      []byte  `protobuf:"bytes,3,opt,name=serMsg,proto3" json:"serMsg,omitempty"`
	Metadata    []byte  `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Schedule    []byte  `protobuf:"bytes,5,opt,name=schedule,proto3,oneof" json:"schedule,omitempty"`
	CreatorUuid *string `protobuf:"bytes,6,opt,name=CreatorUuid,proto3,oneof" json:"CreatorUuid,omitempty"`
}

func (x *AddJobRunRequest) Reset() {
	*x = AddJobRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddJobRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddJobRunRequest) ProtoMessage() {}

func (x *AddJobRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddJobRunRequest.ProtoReflect.Descriptor instead.
func (*AddJobRunRequest) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{4}
}

func (x *AddJobRunRequest) GetRegistryKey() string {
	if x != nil {
		return x.RegistryKey
	}
	return ""
}

func (x *AddJobRunRequest) GetJobIdSuffix() string {
	if x != nil {
		return x.JobIdSuffix
	}
	return ""
}

func (x *AddJobRunRequest) GetSerMsg() []byte {
	if x != nil {
		return x.SerMsg
	}
	return nil
}

func (x *AddJobRunRequest) GetMetadata() []byte {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *AddJobRunRequest) GetSchedule() []byte {
	if x != nil {
		return x.Schedule
	}
	return nil
}

func (x *AddJobRunRequest) GetCreatorUuid() string {
	if x != nil && x.CreatorUuid != nil {
		return *x.CreatorUuid
	}
	return ""
}

type GetJobRunByCreatorUUIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreatorUUID string `protobuf:"bytes,1,opt,name=creatorUUID,proto3" json:"creatorUUID,omitempty"`
}

func (x *GetJobRunByCreatorUUIDRequest) Reset() {
	*x = GetJobRunByCreatorUUIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobRunByCreatorUUIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobRunByCreatorUUIDRequest) ProtoMessage() {}

func (x *GetJobRunByCreatorUUIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobRunByCreatorUUIDRequest.ProtoReflect.Descriptor instead.
func (*GetJobRunByCreatorUUIDRequest) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{5}
}

func (x *GetJobRunByCreatorUUIDRequest) GetCreatorUUID() string {
	if x != nil {
		return x.CreatorUUID
	}
	return ""
}

type GetJobRunsByOwnerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Owner string `protobuf:"bytes,1,opt,name=owner,proto3" json:"owner,omitempty"`
}

func (x *GetJobRunsByOwnerRequest) Reset() {
	*x = GetJobRunsByOwnerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobRunsByOwnerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobRunsByOwnerRequest) ProtoMessage() {}

func (x *GetJobRunsByOwnerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobRunsByOwnerRequest.ProtoReflect.Descriptor instead.
func (*GetJobRunsByOwnerRequest) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{6}
}

func (x *GetJobRunsByOwnerRequest) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

type AddProcessorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessorInfo *ProcessorInfo `protobuf:"bytes,1,opt,name=processorInfo,proto3" json:"processorInfo,omitempty"`
}

func (x *AddProcessorRequest) Reset() {
	*x = AddProcessorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddProcessorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddProcessorRequest) ProtoMessage() {}

func (x *AddProcessorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddProcessorRequest.ProtoReflect.Descriptor instead.
func (*AddProcessorRequest) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{7}
}

func (x *AddProcessorRequest) GetProcessorInfo() *ProcessorInfo {
	if x != nil {
		return x.ProcessorInfo
	}
	return nil
}

type ResourceStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                  string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Total                 int64  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	FreeApprox            int64  `protobuf:"varint,3,opt,name=freeApprox,proto3" json:"freeApprox,omitempty"`
	CanDivergeInSystem    bool   `protobuf:"varint,4,opt,name=canDivergeInSystem,proto3" json:"canDivergeInSystem,omitempty"`
	FreeInSystemApprox    int64  `protobuf:"varint,5,opt,name=freeInSystemApprox,proto3" json:"freeInSystemApprox,omitempty"`
	AverageJobConsumption int64  `protobuf:"varint,6,opt,name=averageJobConsumption,proto3" json:"averageJobConsumption,omitempty"`
}

func (x *ResourceStatus) Reset() {
	*x = ResourceStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceStatus) ProtoMessage() {}

func (x *ResourceStatus) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceStatus.ProtoReflect.Descriptor instead.
func (*ResourceStatus) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{8}
}

func (x *ResourceStatus) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResourceStatus) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ResourceStatus) GetFreeApprox() int64 {
	if x != nil {
		return x.FreeApprox
	}
	return 0
}

func (x *ResourceStatus) GetCanDivergeInSystem() bool {
	if x != nil {
		return x.CanDivergeInSystem
	}
	return false
}

func (x *ResourceStatus) GetFreeInSystemApprox() int64 {
	if x != nil {
		return x.FreeInSystemApprox
	}
	return 0
}

func (x *ResourceStatus) GetAverageJobConsumption() int64 {
	if x != nil {
		return x.AverageJobConsumption
	}
	return 0
}

type ResourcesStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resources []*ResourceStatus `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
}

func (x *ResourcesStatus) Reset() {
	*x = ResourcesStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourcesStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourcesStatus) ProtoMessage() {}

func (x *ResourcesStatus) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourcesStatus.ProtoReflect.Descriptor instead.
func (*ResourcesStatus) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{9}
}

func (x *ResourcesStatus) GetResources() []*ResourceStatus {
	if x != nil {
		return x.Resources
	}
	return nil
}

type AddProcessorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeartbeatResponse *ProcessorHeartbeatResponse `protobuf:"bytes,1,opt,name=heartbeatResponse,proto3" json:"heartbeatResponse,omitempty"`
}

func (x *AddProcessorResponse) Reset() {
	*x = AddProcessorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddProcessorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddProcessorResponse) ProtoMessage() {}

func (x *AddProcessorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddProcessorResponse.ProtoReflect.Descriptor instead.
func (*AddProcessorResponse) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{10}
}

func (x *AddProcessorResponse) GetHeartbeatResponse() *ProcessorHeartbeatResponse {
	if x != nil {
		return x.HeartbeatResponse
	}
	return nil
}

type ProcessorHeartbeatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                    string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SeqNo                 int64                  `protobuf:"varint,2,opt,name=seqNo,proto3" json:"seqNo,omitempty"`
	SendTime              *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=sendTime,proto3" json:"sendTime,omitempty"`
	CurrentResourceStatus *ResourcesStatus       `protobuf:"bytes,4,opt,name=currentResourceStatus,proto3" json:"currentResourceStatus,omitempty"`
	ClusterId             string                 `protobuf:"bytes,6,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
}

func (x *ProcessorHeartbeatRequest) Reset() {
	*x = ProcessorHeartbeatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessorHeartbeatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessorHeartbeatRequest) ProtoMessage() {}

func (x *ProcessorHeartbeatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessorHeartbeatRequest.ProtoReflect.Descriptor instead.
func (*ProcessorHeartbeatRequest) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{11}
}

func (x *ProcessorHeartbeatRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ProcessorHeartbeatRequest) GetSeqNo() int64 {
	if x != nil {
		return x.SeqNo
	}
	return 0
}

func (x *ProcessorHeartbeatRequest) GetSendTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SendTime
	}
	return nil
}

func (x *ProcessorHeartbeatRequest) GetCurrentResourceStatus() *ResourcesStatus {
	if x != nil {
		return x.CurrentResourceStatus
	}
	return nil
}

func (x *ProcessorHeartbeatRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

type ProcessorHeartbeatResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ProcessorHeartbeatResponse) Reset() {
	*x = ProcessorHeartbeatResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessorHeartbeatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessorHeartbeatResponse) ProtoMessage() {}

func (x *ProcessorHeartbeatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessorHeartbeatResponse.ProtoReflect.Descriptor instead.
func (*ProcessorHeartbeatResponse) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{12}
}

type AliveProcessorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterId string `protobuf:"bytes,1,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
}

func (x *AliveProcessorsRequest) Reset() {
	*x = AliveProcessorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliveProcessorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliveProcessorsRequest) ProtoMessage() {}

func (x *AliveProcessorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliveProcessorsRequest.ProtoReflect.Descriptor instead.
func (*AliveProcessorsRequest) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{13}
}

func (x *AliveProcessorsRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

type ProcessorInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                        string                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ClusterId                 string                     `protobuf:"bytes,2,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
	DeadOnNoHeartbeatInterval *durationpb.Duration       `protobuf:"bytes,3,opt,name=deadOnNoHeartbeatInterval,proto3" json:"deadOnNoHeartbeatInterval,omitempty"`
	CurrentResourceStatus     *ResourcesStatus           `protobuf:"bytes,4,opt,name=currentResourceStatus,proto3" json:"currentResourceStatus,omitempty"`
	ProcessorStatus           ProcessorStatus            `protobuf:"varint,5,opt,name=processorStatus,proto3,enum=jobber.ProcessorStatus" json:"processorStatus,omitempty"`
	Address                   string                     `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	Heartbeat                 *ProcessorHeartbeatRequest `protobuf:"bytes,7,opt,name=heartbeat,proto3" json:"heartbeat,omitempty"`
}

func (x *ProcessorInfo) Reset() {
	*x = ProcessorInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessorInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessorInfo) ProtoMessage() {}

func (x *ProcessorInfo) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessorInfo.ProtoReflect.Descriptor instead.
func (*ProcessorInfo) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{14}
}

func (x *ProcessorInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ProcessorInfo) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *ProcessorInfo) GetDeadOnNoHeartbeatInterval() *durationpb.Duration {
	if x != nil {
		return x.DeadOnNoHeartbeatInterval
	}
	return nil
}

func (x *ProcessorInfo) GetCurrentResourceStatus() *ResourcesStatus {
	if x != nil {
		return x.CurrentResourceStatus
	}
	return nil
}

func (x *ProcessorInfo) GetProcessorStatus() ProcessorStatus {
	if x != nil {
		return x.ProcessorStatus
	}
	return ProcessorStatus_NOT_STARTED
}

func (x *ProcessorInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ProcessorInfo) GetHeartbeat() *ProcessorHeartbeatRequest {
	if x != nil {
		return x.Heartbeat
	}
	return nil
}

type AliveProcessorsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Infos []*ProcessorInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
}

func (x *AliveProcessorsResponse) Reset() {
	*x = AliveProcessorsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliveProcessorsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliveProcessorsResponse) ProtoMessage() {}

func (x *AliveProcessorsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliveProcessorsResponse.ProtoReflect.Descriptor instead.
func (*AliveProcessorsResponse) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{15}
}

func (x *AliveProcessorsResponse) GetInfos() []*ProcessorInfo {
	if x != nil {
		return x.Infos
	}
	return nil
}

type UpdateProcessorStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterId string          `protobuf:"bytes,1,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
	Id        string          `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	NewStatus ProcessorStatus `protobuf:"varint,3,opt,name=newStatus,proto3,enum=jobber.ProcessorStatus" json:"newStatus,omitempty"`
}

func (x *UpdateProcessorStatusRequest) Reset() {
	*x = UpdateProcessorStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateProcessorStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProcessorStatusRequest) ProtoMessage() {}

func (x *UpdateProcessorStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProcessorStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateProcessorStatusRequest) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateProcessorStatusRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *UpdateProcessorStatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateProcessorStatusRequest) GetNewStatus() ProcessorStatus {
	if x != nil {
		return x.NewStatus
	}
	return ProcessorStatus_NOT_STARTED
}

type UpdateProcessorStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateProcessorStatusResponse) Reset() {
	*x = UpdateProcessorStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateProcessorStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProcessorStatusResponse) ProtoMessage() {}

func (x *UpdateProcessorStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProcessorStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateProcessorStatusResponse) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{17}
}

type CurrentStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CurrentStatusRequest) Reset() {
	*x = CurrentStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentStatusRequest) ProtoMessage() {}

func (x *CurrentStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentStatusRequest.ProtoReflect.Descriptor instead.
func (*CurrentStatusRequest) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{18}
}

type DrainAndStopProcessorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NonBlocking bool `protobuf:"varint,1,opt,name=NonBlocking,proto3" json:"NonBlocking,omitempty"`
}

func (x *DrainAndStopProcessorRequest) Reset() {
	*x = DrainAndStopProcessorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DrainAndStopProcessorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DrainAndStopProcessorRequest) ProtoMessage() {}

func (x *DrainAndStopProcessorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DrainAndStopProcessorRequest.ProtoReflect.Descriptor instead.
func (*DrainAndStopProcessorRequest) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{19}
}

func (x *DrainAndStopProcessorRequest) GetNonBlocking() bool {
	if x != nil {
		return x.NonBlocking
	}
	return false
}

type DrainAndStopProcessorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DrainAndStopProcessorResponse) Reset() {
	*x = DrainAndStopProcessorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DrainAndStopProcessorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DrainAndStopProcessorResponse) ProtoMessage() {}

func (x *DrainAndStopProcessorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DrainAndStopProcessorResponse.ProtoReflect.Descriptor instead.
func (*DrainAndStopProcessorResponse) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{20}
}

type UpdateJobRunPickupTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobRunId      *JobRunId              `protobuf:"bytes,1,opt,name=jobRunId,proto3" json:"jobRunId,omitempty"`
	NewPickupTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=newPickupTime,proto3" json:"newPickupTime,omitempty"`
}

func (x *UpdateJobRunPickupTimeRequest) Reset() {
	*x = UpdateJobRunPickupTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateJobRunPickupTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobRunPickupTimeRequest) ProtoMessage() {}

func (x *UpdateJobRunPickupTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobRunPickupTimeRequest.ProtoReflect.Descriptor instead.
func (*UpdateJobRunPickupTimeRequest) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateJobRunPickupTimeRequest) GetJobRunId() *JobRunId {
	if x != nil {
		return x.JobRunId
	}
	return nil
}

func (x *UpdateJobRunPickupTimeRequest) GetNewPickupTime() *timestamppb.Timestamp {
	if x != nil {
		return x.NewPickupTime
	}
	return nil
}

type UpdateJobRunPickupTimeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateJobRunPickupTimeResponse) Reset() {
	*x = UpdateJobRunPickupTimeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_jobber_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateJobRunPickupTimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobRunPickupTimeResponse) ProtoMessage() {}

func (x *UpdateJobRunPickupTimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_jobber_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobRunPickupTimeResponse.ProtoReflect.Descriptor instead.
func (*UpdateJobRunPickupTimeResponse) Descriptor() ([]byte, []int) {
	return file_jobber_jobber_proto_rawDescGZIP(), []int{22}
}

var File_jobber_jobber_proto protoreflect.FileDescriptor

var file_jobber_jobber_proto_rawDesc = []byte{
	0x0a, 0x13, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2f, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x1a, 0x1e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x23, 0x0a, 0x11, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x36, 0x0a, 0x08, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x6a, 0x6f, 0x62, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x22, 0x7e, 0x0a, 0x06, 0x4a, 0x6f, 0x62, 0x52,
	0x75, 0x6e, 0x12, 0x20, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49, 0x64,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x55, 0x55, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x55, 0x55, 0x49, 0x44, 0x22, 0x37, 0x0a, 0x0b, 0x4d, 0x61, 0x6e, 0x79,
	0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x73, 0x12, 0x28, 0x0a, 0x07, 0x6a, 0x6f, 0x62, 0x52, 0x75,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65,
	0x72, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x52, 0x07, 0x6a, 0x6f, 0x62, 0x52, 0x75, 0x6e,
	0x73, 0x22, 0xef, 0x01, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x79, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x6a, 0x6f, 0x62, 0x49,
	0x64, 0x53, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6a,
	0x6f, 0x62, 0x49, 0x64, 0x53, 0x75, 0x66, 0x66, 0x69, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65,
	0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x65, 0x72, 0x4d,
	0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1f,
	0x0a, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c,
	0x48, 0x00, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x25, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x75, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55,
	0x75, 0x69, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55,
	0x75, 0x69, 0x64, 0x22, 0x41, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e,
	0x42, 0x79, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x55, 0x49, 0x44, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x55,
	0x55, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x55, 0x55, 0x49, 0x44, 0x22, 0x30, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62,
	0x52, 0x75, 0x6e, 0x73, 0x42, 0x79, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x22, 0x52, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3b, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xf0, 0x01, 0x0a,
	0x0e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x72, 0x65,
	0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x66,
	0x72, 0x65, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x78, 0x12, 0x2e, 0x0a, 0x12, 0x63, 0x61, 0x6e,
	0x44, 0x69, 0x76, 0x65, 0x72, 0x67, 0x65, 0x49, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x63, 0x61, 0x6e, 0x44, 0x69, 0x76, 0x65, 0x72, 0x67,
	0x65, 0x49, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x2e, 0x0a, 0x12, 0x66, 0x72, 0x65,
	0x65, 0x49, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x78, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x66, 0x72, 0x65, 0x65, 0x49, 0x6e, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x78, 0x12, 0x34, 0x0a, 0x15, 0x61, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x47, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x22, 0x68, 0x0a, 0x14, 0x41, 0x64, 0x64, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x50, 0x0a, 0x11, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x48, 0x65,
	0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x11, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0xe6, 0x01, 0x0a, 0x19, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72,
	0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x65, 0x71, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x73, 0x65, 0x71, 0x4e, 0x6f, 0x12, 0x36, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4d,
	0x0a, 0x15, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x15, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x22, 0x1c, 0x0a, 0x1a, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x36, 0x0a, 0x16, 0x41, 0x6c, 0x69,
	0x76, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x83, 0x03, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x57, 0x0a, 0x19, 0x64, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x4e, 0x6f, 0x48, 0x65, 0x61,
	0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x19, 0x64, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x4e, 0x6f, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65,
	0x61, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x4d, 0x0a, 0x15, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6a, 0x6f, 0x62, 0x62,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x15, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x0f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3f, 0x0a, 0x09, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62,
	0x65, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6a, 0x6f, 0x62, 0x62,
	0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x48, 0x65, 0x61, 0x72,
	0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x09, 0x68, 0x65,
	0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x22, 0x46, 0x0a, 0x17, 0x41, 0x6c, 0x69, 0x76, 0x65,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x22,
	0x83, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35,
	0x0a, 0x09, 0x6e, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x1f, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x40,
	0x0a, 0x1c, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x6e, 0x64, 0x53, 0x74, 0x6f, 0x70, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20,
	0x0a, 0x0b, 0x4e, 0x6f, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x4e, 0x6f, 0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x22, 0x1f, 0x0a, 0x1d, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x6e, 0x64, 0x53, 0x74, 0x6f, 0x70,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x8f, 0x01, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52,
	0x75, 0x6e, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x08, 0x6a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x4a,
	0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x52, 0x08, 0x6a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49,
	0x64, 0x12, 0x40, 0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6e, 0x65, 0x77, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x20, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62,
	0x52, 0x75, 0x6e, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2a, 0x4a, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x4f, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x52, 0x54, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x55, 0x4e,
	0x4e, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x52, 0x41, 0x49, 0x4e, 0x49,
	0x4e, 0x47, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10,
	0x04, 0x32, 0x99, 0x04, 0x0a, 0x06, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x09,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x19, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x39,
	0x0a, 0x09, 0x41, 0x64, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x12, 0x18, 0x2e, 0x6a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x10, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x4a,
	0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x22, 0x00, 0x12, 0x2f, 0x0a, 0x09, 0x47, 0x65, 0x74,
	0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x12, 0x10, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e,
	0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x1a, 0x0e, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65,
	0x72, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x42, 0x79, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x55, 0x55, 0x49, 0x44, 0x12, 0x25, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x42, 0x79, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x55, 0x55, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x6a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x22, 0x00, 0x12, 0x4c, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x73, 0x42, 0x79, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x12, 0x20, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4a,
	0x6f, 0x62, 0x52, 0x75, 0x6e, 0x73, 0x42, 0x79, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x4d, 0x61,
	0x6e, 0x79, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x73, 0x22, 0x00, 0x12, 0x3a, 0x0a, 0x0c, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x12, 0x10, 0x2e, 0x6a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x3e, 0x0a, 0x10, 0x4b, 0x69, 0x6c, 0x6c, 0x4a,
	0x6f, 0x62, 0x52, 0x75, 0x6e, 0x55, 0x6e, 0x73, 0x61, 0x66, 0x65, 0x12, 0x10, 0x2e, 0x6a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x16, 0x50, 0x69, 0x63, 0x6b, 0x75,
	0x70, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49, 0x66, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x12, 0x10, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x75,
	0x6e, 0x49, 0x64, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x32, 0xf3, 0x02,
	0x0a, 0x07, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x4b, 0x0a, 0x0c, 0x41, 0x64, 0x64,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x12, 0x1b, 0x2e, 0x6a, 0x6f, 0x62, 0x62,
	0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e,
	0x41, 0x64, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x12, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62,
	0x65, 0x61, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x12, 0x21, 0x2e, 0x6a,
	0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x48,
	0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x22, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x6f, 0x72, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x0f, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x73, 0x12, 0x1e, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65,
	0x72, 0x2e, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65,
	0x72, 0x2e, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x15, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x6a, 0x6f, 0x62,
	0x62, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x32, 0xb1, 0x02, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f,
	0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x12, 0x49, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x2e,
	0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x6a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x15, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x6e, 0x64,
	0x53, 0x74, 0x6f, 0x70, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x12, 0x24, 0x2e,
	0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x6e, 0x64, 0x53,
	0x74, 0x6f, 0x70, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x44, 0x72, 0x61,
	0x69, 0x6e, 0x41, 0x6e, 0x64, 0x53, 0x74, 0x6f, 0x70, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x16,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x50, 0x69, 0x63, 0x6b,
	0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x50, 0x69, 0x63, 0x6b,
	0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e,
	0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62,
	0x52, 0x75, 0x6e, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_jobber_jobber_proto_rawDescOnce sync.Once
	file_jobber_jobber_proto_rawDescData = file_jobber_jobber_proto_rawDesc
)

func file_jobber_jobber_proto_rawDescGZIP() []byte {
	file_jobber_jobber_proto_rawDescOnce.Do(func() {
		file_jobber_jobber_proto_rawDescData = protoimpl.X.CompressGZIP(file_jobber_jobber_proto_rawDescData)
	})
	return file_jobber_jobber_proto_rawDescData
}

var file_jobber_jobber_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_jobber_jobber_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_jobber_jobber_proto_goTypes = []interface{}{
	(ProcessorStatus)(0),                   // 0: jobber.ProcessorStatus
	(*ClusterIdResponse)(nil),              // 1: jobber.ClusterIdResponse
	(*JobRunId)(nil),                       // 2: jobber.JobRunId
	(*JobRun)(nil),                         // 3: jobber.JobRun
	(*ManyJobRuns)(nil),                    // 4: jobber.ManyJobRuns
	(*AddJobRunRequest)(nil),               // 5: jobber.AddJobRunRequest
	(*GetJobRunByCreatorUUIDRequest)(nil),  // 6: jobber.GetJobRunByCreatorUUIDRequest
	(*GetJobRunsByOwnerRequest)(nil),       // 7: jobber.GetJobRunsByOwnerRequest
	(*AddProcessorRequest)(nil),            // 8: jobber.AddProcessorRequest
	(*ResourceStatus)(nil),                 // 9: jobber.ResourceStatus
	(*ResourcesStatus)(nil),                // 10: jobber.ResourcesStatus
	(*AddProcessorResponse)(nil),           // 11: jobber.AddProcessorResponse
	(*ProcessorHeartbeatRequest)(nil),      // 12: jobber.ProcessorHeartbeatRequest
	(*ProcessorHeartbeatResponse)(nil),     // 13: jobber.ProcessorHeartbeatResponse
	(*AliveProcessorsRequest)(nil),         // 14: jobber.AliveProcessorsRequest
	(*ProcessorInfo)(nil),                  // 15: jobber.ProcessorInfo
	(*AliveProcessorsResponse)(nil),        // 16: jobber.AliveProcessorsResponse
	(*UpdateProcessorStatusRequest)(nil),   // 17: jobber.UpdateProcessorStatusRequest
	(*UpdateProcessorStatusResponse)(nil),  // 18: jobber.UpdateProcessorStatusResponse
	(*CurrentStatusRequest)(nil),           // 19: jobber.CurrentStatusRequest
	(*DrainAndStopProcessorRequest)(nil),   // 20: jobber.DrainAndStopProcessorRequest
	(*DrainAndStopProcessorResponse)(nil),  // 21: jobber.DrainAndStopProcessorResponse
	(*UpdateJobRunPickupTimeRequest)(nil),  // 22: jobber.UpdateJobRunPickupTimeRequest
	(*UpdateJobRunPickupTimeResponse)(nil), // 23: jobber.UpdateJobRunPickupTimeResponse
	(*timestamppb.Timestamp)(nil),          // 24: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),            // 25: google.protobuf.Duration
	(*emptypb.Empty)(nil),                  // 26: google.protobuf.Empty
}
var file_jobber_jobber_proto_depIdxs = []int32{
	2,  // 0: jobber.JobRun.id:type_name -> jobber.JobRunId
	3,  // 1: jobber.ManyJobRuns.jobRuns:type_name -> jobber.JobRun
	15, // 2: jobber.AddProcessorRequest.processorInfo:type_name -> jobber.ProcessorInfo
	9,  // 3: jobber.ResourcesStatus.resources:type_name -> jobber.ResourceStatus
	13, // 4: jobber.AddProcessorResponse.heartbeatResponse:type_name -> jobber.ProcessorHeartbeatResponse
	24, // 5: jobber.ProcessorHeartbeatRequest.sendTime:type_name -> google.protobuf.Timestamp
	10, // 6: jobber.ProcessorHeartbeatRequest.currentResourceStatus:type_name -> jobber.ResourcesStatus
	25, // 7: jobber.ProcessorInfo.deadOnNoHeartbeatInterval:type_name -> google.protobuf.Duration
	10, // 8: jobber.ProcessorInfo.currentResourceStatus:type_name -> jobber.ResourcesStatus
	0,  // 9: jobber.ProcessorInfo.processorStatus:type_name -> jobber.ProcessorStatus
	12, // 10: jobber.ProcessorInfo.heartbeat:type_name -> jobber.ProcessorHeartbeatRequest
	15, // 11: jobber.AliveProcessorsResponse.infos:type_name -> jobber.ProcessorInfo
	0,  // 12: jobber.UpdateProcessorStatusRequest.newStatus:type_name -> jobber.ProcessorStatus
	2,  // 13: jobber.UpdateJobRunPickupTimeRequest.jobRunId:type_name -> jobber.JobRunId
	24, // 14: jobber.UpdateJobRunPickupTimeRequest.newPickupTime:type_name -> google.protobuf.Timestamp
	26, // 15: jobber.Jobber.ClusterId:input_type -> google.protobuf.Empty
	5,  // 16: jobber.Jobber.AddJobRun:input_type -> jobber.AddJobRunRequest
	2,  // 17: jobber.Jobber.GetJobRun:input_type -> jobber.JobRunId
	6,  // 18: jobber.Jobber.GetJobRunByCreatorUUID:input_type -> jobber.GetJobRunByCreatorUUIDRequest
	7,  // 19: jobber.Jobber.GetJobRunsByOwner:input_type -> jobber.GetJobRunsByOwnerRequest
	2,  // 20: jobber.Jobber.CancelJobRun:input_type -> jobber.JobRunId
	2,  // 21: jobber.Jobber.KillJobRunUnsafe:input_type -> jobber.JobRunId
	2,  // 22: jobber.Jobber.PickupJobRunIfPossible:input_type -> jobber.JobRunId
	8,  // 23: jobber.Monitor.AddProcessor:input_type -> jobber.AddProcessorRequest
	12, // 24: jobber.Monitor.HeartbeatProcessor:input_type -> jobber.ProcessorHeartbeatRequest
	14, // 25: jobber.Monitor.AliveProcessors:input_type -> jobber.AliveProcessorsRequest
	17, // 26: jobber.Monitor.UpdateProcessorStatus:input_type -> jobber.UpdateProcessorStatusRequest
	19, // 27: jobber.ProcessorInternal.GetCurrentStatus:input_type -> jobber.CurrentStatusRequest
	20, // 28: jobber.ProcessorInternal.DrainAndStopProcessor:input_type -> jobber.DrainAndStopProcessorRequest
	22, // 29: jobber.ProcessorInternal.UpdateJobRunPickupTime:input_type -> jobber.UpdateJobRunPickupTimeRequest
	1,  // 30: jobber.Jobber.ClusterId:output_type -> jobber.ClusterIdResponse
	2,  // 31: jobber.Jobber.AddJobRun:output_type -> jobber.JobRunId
	3,  // 32: jobber.Jobber.GetJobRun:output_type -> jobber.JobRun
	3,  // 33: jobber.Jobber.GetJobRunByCreatorUUID:output_type -> jobber.JobRun
	4,  // 34: jobber.Jobber.GetJobRunsByOwner:output_type -> jobber.ManyJobRuns
	26, // 35: jobber.Jobber.CancelJobRun:output_type -> google.protobuf.Empty
	26, // 36: jobber.Jobber.KillJobRunUnsafe:output_type -> google.protobuf.Empty
	26, // 37: jobber.Jobber.PickupJobRunIfPossible:output_type -> google.protobuf.Empty
	11, // 38: jobber.Monitor.AddProcessor:output_type -> jobber.AddProcessorResponse
	13, // 39: jobber.Monitor.HeartbeatProcessor:output_type -> jobber.ProcessorHeartbeatResponse
	16, // 40: jobber.Monitor.AliveProcessors:output_type -> jobber.AliveProcessorsResponse
	18, // 41: jobber.Monitor.UpdateProcessorStatus:output_type -> jobber.UpdateProcessorStatusResponse
	15, // 42: jobber.ProcessorInternal.GetCurrentStatus:output_type -> jobber.ProcessorInfo
	21, // 43: jobber.ProcessorInternal.DrainAndStopProcessor:output_type -> jobber.DrainAndStopProcessorResponse
	23, // 44: jobber.ProcessorInternal.UpdateJobRunPickupTime:output_type -> jobber.UpdateJobRunPickupTimeResponse
	30, // [30:45] is the sub-list for method output_type
	15, // [15:30] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_jobber_jobber_proto_init() }
func file_jobber_jobber_proto_init() {
	if File_jobber_jobber_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_jobber_jobber_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClusterIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobRunId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobRun); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManyJobRuns); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddJobRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobRunByCreatorUUIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobRunsByOwnerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddProcessorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourcesStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddProcessorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessorHeartbeatRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessorHeartbeatResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliveProcessorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessorInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliveProcessorsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateProcessorStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateProcessorStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DrainAndStopProcessorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DrainAndStopProcessorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateJobRunPickupTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_jobber_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateJobRunPickupTimeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_jobber_jobber_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_jobber_jobber_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   3,
		},
		GoTypes:           file_jobber_jobber_proto_goTypes,
		DependencyIndexes: file_jobber_jobber_proto_depIdxs,
		EnumInfos:         file_jobber_jobber_proto_enumTypes,
		MessageInfos:      file_jobber_jobber_proto_msgTypes,
	}.Build()
	File_jobber_jobber_proto = out.File
	file_jobber_jobber_proto_rawDesc = nil
	file_jobber_jobber_proto_goTypes = nil
	file_jobber_jobber_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// JobberClient is the client API for Jobber service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type JobberClient interface {
	ClusterId(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ClusterIdResponse, error)
	AddJobRun(ctx context.Context, in *AddJobRunRequest, opts ...grpc.CallOption) (*JobRunId, error)
	GetJobRun(ctx context.Context, in *JobRunId, opts ...grpc.CallOption) (*JobRun, error)
	GetJobRunByCreatorUUID(ctx context.Context, in *GetJobRunByCreatorUUIDRequest, opts ...grpc.CallOption) (*JobRun, error)
	GetJobRunsByOwner(ctx context.Context, in *GetJobRunsByOwnerRequest, opts ...grpc.CallOption) (*ManyJobRuns, error)
	CancelJobRun(ctx context.Context, in *JobRunId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	KillJobRunUnsafe(ctx context.Context, in *JobRunId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	PickupJobRunIfPossible(ctx context.Context, in *JobRunId, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type jobberClient struct {
	cc grpc.ClientConnInterface
}

func NewJobberClient(cc grpc.ClientConnInterface) JobberClient {
	return &jobberClient{cc}
}

func (c *jobberClient) ClusterId(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ClusterIdResponse, error) {
	out := new(ClusterIdResponse)
	err := c.cc.Invoke(ctx, "/jobber.Jobber/ClusterId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobberClient) AddJobRun(ctx context.Context, in *AddJobRunRequest, opts ...grpc.CallOption) (*JobRunId, error) {
	out := new(JobRunId)
	err := c.cc.Invoke(ctx, "/jobber.Jobber/AddJobRun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobberClient) GetJobRun(ctx context.Context, in *JobRunId, opts ...grpc.CallOption) (*JobRun, error) {
	out := new(JobRun)
	err := c.cc.Invoke(ctx, "/jobber.Jobber/GetJobRun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobberClient) GetJobRunByCreatorUUID(ctx context.Context, in *GetJobRunByCreatorUUIDRequest, opts ...grpc.CallOption) (*JobRun, error) {
	out := new(JobRun)
	err := c.cc.Invoke(ctx, "/jobber.Jobber/GetJobRunByCreatorUUID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobberClient) GetJobRunsByOwner(ctx context.Context, in *GetJobRunsByOwnerRequest, opts ...grpc.CallOption) (*ManyJobRuns, error) {
	out := new(ManyJobRuns)
	err := c.cc.Invoke(ctx, "/jobber.Jobber/GetJobRunsByOwner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobberClient) CancelJobRun(ctx context.Context, in *JobRunId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/jobber.Jobber/CancelJobRun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobberClient) KillJobRunUnsafe(ctx context.Context, in *JobRunId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/jobber.Jobber/KillJobRunUnsafe", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobberClient) PickupJobRunIfPossible(ctx context.Context, in *JobRunId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/jobber.Jobber/PickupJobRunIfPossible", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JobberServer is the server API for Jobber service.
type JobberServer interface {
	ClusterId(context.Context, *emptypb.Empty) (*ClusterIdResponse, error)
	AddJobRun(context.Context, *AddJobRunRequest) (*JobRunId, error)
	GetJobRun(context.Context, *JobRunId) (*JobRun, error)
	GetJobRunByCreatorUUID(context.Context, *GetJobRunByCreatorUUIDRequest) (*JobRun, error)
	GetJobRunsByOwner(context.Context, *GetJobRunsByOwnerRequest) (*ManyJobRuns, error)
	CancelJobRun(context.Context, *JobRunId) (*emptypb.Empty, error)
	KillJobRunUnsafe(context.Context, *JobRunId) (*emptypb.Empty, error)
	PickupJobRunIfPossible(context.Context, *JobRunId) (*emptypb.Empty, error)
}

// UnimplementedJobberServer can be embedded to have forward compatible implementations.
type UnimplementedJobberServer struct {
}

func (*UnimplementedJobberServer) ClusterId(context.Context, *emptypb.Empty) (*ClusterIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClusterId not implemented")
}
func (*UnimplementedJobberServer) AddJobRun(context.Context, *AddJobRunRequest) (*JobRunId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddJobRun not implemented")
}
func (*UnimplementedJobberServer) GetJobRun(context.Context, *JobRunId) (*JobRun, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobRun not implemented")
}
func (*UnimplementedJobberServer) GetJobRunByCreatorUUID(context.Context, *GetJobRunByCreatorUUIDRequest) (*JobRun, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobRunByCreatorUUID not implemented")
}
func (*UnimplementedJobberServer) GetJobRunsByOwner(context.Context, *GetJobRunsByOwnerRequest) (*ManyJobRuns, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobRunsByOwner not implemented")
}
func (*UnimplementedJobberServer) CancelJobRun(context.Context, *JobRunId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelJobRun not implemented")
}
func (*UnimplementedJobberServer) KillJobRunUnsafe(context.Context, *JobRunId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KillJobRunUnsafe not implemented")
}
func (*UnimplementedJobberServer) PickupJobRunIfPossible(context.Context, *JobRunId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PickupJobRunIfPossible not implemented")
}

func RegisterJobberServer(s *grpc.Server, srv JobberServer) {
	s.RegisterService(&_Jobber_serviceDesc, srv)
}

func _Jobber_ClusterId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobberServer).ClusterId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Jobber/ClusterId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobberServer).ClusterId(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobber_AddJobRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddJobRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobberServer).AddJobRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Jobber/AddJobRun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobberServer).AddJobRun(ctx, req.(*AddJobRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobber_GetJobRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobRunId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobberServer).GetJobRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Jobber/GetJobRun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobberServer).GetJobRun(ctx, req.(*JobRunId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobber_GetJobRunByCreatorUUID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobRunByCreatorUUIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobberServer).GetJobRunByCreatorUUID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Jobber/GetJobRunByCreatorUUID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobberServer).GetJobRunByCreatorUUID(ctx, req.(*GetJobRunByCreatorUUIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobber_GetJobRunsByOwner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobRunsByOwnerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobberServer).GetJobRunsByOwner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Jobber/GetJobRunsByOwner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobberServer).GetJobRunsByOwner(ctx, req.(*GetJobRunsByOwnerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobber_CancelJobRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobRunId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobberServer).CancelJobRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Jobber/CancelJobRun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobberServer).CancelJobRun(ctx, req.(*JobRunId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobber_KillJobRunUnsafe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobRunId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobberServer).KillJobRunUnsafe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Jobber/KillJobRunUnsafe",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobberServer).KillJobRunUnsafe(ctx, req.(*JobRunId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jobber_PickupJobRunIfPossible_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobRunId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobberServer).PickupJobRunIfPossible(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Jobber/PickupJobRunIfPossible",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobberServer).PickupJobRunIfPossible(ctx, req.(*JobRunId))
	}
	return interceptor(ctx, in, info, handler)
}

var _Jobber_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jobber.Jobber",
	HandlerType: (*JobberServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ClusterId",
			Handler:    _Jobber_ClusterId_Handler,
		},
		{
			MethodName: "AddJobRun",
			Handler:    _Jobber_AddJobRun_Handler,
		},
		{
			MethodName: "GetJobRun",
			Handler:    _Jobber_GetJobRun_Handler,
		},
		{
			MethodName: "GetJobRunByCreatorUUID",
			Handler:    _Jobber_GetJobRunByCreatorUUID_Handler,
		},
		{
			MethodName: "GetJobRunsByOwner",
			Handler:    _Jobber_GetJobRunsByOwner_Handler,
		},
		{
			MethodName: "CancelJobRun",
			Handler:    _Jobber_CancelJobRun_Handler,
		},
		{
			MethodName: "KillJobRunUnsafe",
			Handler:    _Jobber_KillJobRunUnsafe_Handler,
		},
		{
			MethodName: "PickupJobRunIfPossible",
			Handler:    _Jobber_PickupJobRunIfPossible_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "jobber/jobber.proto",
}

// MonitorClient is the client API for Monitor service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MonitorClient interface {
	AddProcessor(ctx context.Context, in *AddProcessorRequest, opts ...grpc.CallOption) (*AddProcessorResponse, error)
	HeartbeatProcessor(ctx context.Context, in *ProcessorHeartbeatRequest, opts ...grpc.CallOption) (*ProcessorHeartbeatResponse, error)
	AliveProcessors(ctx context.Context, in *AliveProcessorsRequest, opts ...grpc.CallOption) (*AliveProcessorsResponse, error)
	UpdateProcessorStatus(ctx context.Context, in *UpdateProcessorStatusRequest, opts ...grpc.CallOption) (*UpdateProcessorStatusResponse, error)
}

type monitorClient struct {
	cc grpc.ClientConnInterface
}

func NewMonitorClient(cc grpc.ClientConnInterface) MonitorClient {
	return &monitorClient{cc}
}

func (c *monitorClient) AddProcessor(ctx context.Context, in *AddProcessorRequest, opts ...grpc.CallOption) (*AddProcessorResponse, error) {
	out := new(AddProcessorResponse)
	err := c.cc.Invoke(ctx, "/jobber.Monitor/AddProcessor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) HeartbeatProcessor(ctx context.Context, in *ProcessorHeartbeatRequest, opts ...grpc.CallOption) (*ProcessorHeartbeatResponse, error) {
	out := new(ProcessorHeartbeatResponse)
	err := c.cc.Invoke(ctx, "/jobber.Monitor/HeartbeatProcessor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) AliveProcessors(ctx context.Context, in *AliveProcessorsRequest, opts ...grpc.CallOption) (*AliveProcessorsResponse, error) {
	out := new(AliveProcessorsResponse)
	err := c.cc.Invoke(ctx, "/jobber.Monitor/AliveProcessors", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) UpdateProcessorStatus(ctx context.Context, in *UpdateProcessorStatusRequest, opts ...grpc.CallOption) (*UpdateProcessorStatusResponse, error) {
	out := new(UpdateProcessorStatusResponse)
	err := c.cc.Invoke(ctx, "/jobber.Monitor/UpdateProcessorStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MonitorServer is the server API for Monitor service.
type MonitorServer interface {
	AddProcessor(context.Context, *AddProcessorRequest) (*AddProcessorResponse, error)
	HeartbeatProcessor(context.Context, *ProcessorHeartbeatRequest) (*ProcessorHeartbeatResponse, error)
	AliveProcessors(context.Context, *AliveProcessorsRequest) (*AliveProcessorsResponse, error)
	UpdateProcessorStatus(context.Context, *UpdateProcessorStatusRequest) (*UpdateProcessorStatusResponse, error)
}

// UnimplementedMonitorServer can be embedded to have forward compatible implementations.
type UnimplementedMonitorServer struct {
}

func (*UnimplementedMonitorServer) AddProcessor(context.Context, *AddProcessorRequest) (*AddProcessorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddProcessor not implemented")
}
func (*UnimplementedMonitorServer) HeartbeatProcessor(context.Context, *ProcessorHeartbeatRequest) (*ProcessorHeartbeatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HeartbeatProcessor not implemented")
}
func (*UnimplementedMonitorServer) AliveProcessors(context.Context, *AliveProcessorsRequest) (*AliveProcessorsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliveProcessors not implemented")
}
func (*UnimplementedMonitorServer) UpdateProcessorStatus(context.Context, *UpdateProcessorStatusRequest) (*UpdateProcessorStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProcessorStatus not implemented")
}

func RegisterMonitorServer(s *grpc.Server, srv MonitorServer) {
	s.RegisterService(&_Monitor_serviceDesc, srv)
}

func _Monitor_AddProcessor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddProcessorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).AddProcessor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Monitor/AddProcessor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).AddProcessor(ctx, req.(*AddProcessorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_HeartbeatProcessor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessorHeartbeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).HeartbeatProcessor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Monitor/HeartbeatProcessor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).HeartbeatProcessor(ctx, req.(*ProcessorHeartbeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_AliveProcessors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliveProcessorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).AliveProcessors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Monitor/AliveProcessors",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).AliveProcessors(ctx, req.(*AliveProcessorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_UpdateProcessorStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateProcessorStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).UpdateProcessorStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.Monitor/UpdateProcessorStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).UpdateProcessorStatus(ctx, req.(*UpdateProcessorStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Monitor_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jobber.Monitor",
	HandlerType: (*MonitorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddProcessor",
			Handler:    _Monitor_AddProcessor_Handler,
		},
		{
			MethodName: "HeartbeatProcessor",
			Handler:    _Monitor_HeartbeatProcessor_Handler,
		},
		{
			MethodName: "AliveProcessors",
			Handler:    _Monitor_AliveProcessors_Handler,
		},
		{
			MethodName: "UpdateProcessorStatus",
			Handler:    _Monitor_UpdateProcessorStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "jobber/jobber.proto",
}

// ProcessorInternalClient is the client API for ProcessorInternal service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ProcessorInternalClient interface {
	GetCurrentStatus(ctx context.Context, in *CurrentStatusRequest, opts ...grpc.CallOption) (*ProcessorInfo, error)
	DrainAndStopProcessor(ctx context.Context, in *DrainAndStopProcessorRequest, opts ...grpc.CallOption) (*DrainAndStopProcessorResponse, error)
	UpdateJobRunPickupTime(ctx context.Context, in *UpdateJobRunPickupTimeRequest, opts ...grpc.CallOption) (*UpdateJobRunPickupTimeResponse, error)
}

type processorInternalClient struct {
	cc grpc.ClientConnInterface
}

func NewProcessorInternalClient(cc grpc.ClientConnInterface) ProcessorInternalClient {
	return &processorInternalClient{cc}
}

func (c *processorInternalClient) GetCurrentStatus(ctx context.Context, in *CurrentStatusRequest, opts ...grpc.CallOption) (*ProcessorInfo, error) {
	out := new(ProcessorInfo)
	err := c.cc.Invoke(ctx, "/jobber.ProcessorInternal/GetCurrentStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorInternalClient) DrainAndStopProcessor(ctx context.Context, in *DrainAndStopProcessorRequest, opts ...grpc.CallOption) (*DrainAndStopProcessorResponse, error) {
	out := new(DrainAndStopProcessorResponse)
	err := c.cc.Invoke(ctx, "/jobber.ProcessorInternal/DrainAndStopProcessor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *processorInternalClient) UpdateJobRunPickupTime(ctx context.Context, in *UpdateJobRunPickupTimeRequest, opts ...grpc.CallOption) (*UpdateJobRunPickupTimeResponse, error) {
	out := new(UpdateJobRunPickupTimeResponse)
	err := c.cc.Invoke(ctx, "/jobber.ProcessorInternal/UpdateJobRunPickupTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProcessorInternalServer is the server API for ProcessorInternal service.
type ProcessorInternalServer interface {
	GetCurrentStatus(context.Context, *CurrentStatusRequest) (*ProcessorInfo, error)
	DrainAndStopProcessor(context.Context, *DrainAndStopProcessorRequest) (*DrainAndStopProcessorResponse, error)
	UpdateJobRunPickupTime(context.Context, *UpdateJobRunPickupTimeRequest) (*UpdateJobRunPickupTimeResponse, error)
}

// UnimplementedProcessorInternalServer can be embedded to have forward compatible implementations.
type UnimplementedProcessorInternalServer struct {
}

func (*UnimplementedProcessorInternalServer) GetCurrentStatus(context.Context, *CurrentStatusRequest) (*ProcessorInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCurrentStatus not implemented")
}
func (*UnimplementedProcessorInternalServer) DrainAndStopProcessor(context.Context, *DrainAndStopProcessorRequest) (*DrainAndStopProcessorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DrainAndStopProcessor not implemented")
}
func (*UnimplementedProcessorInternalServer) UpdateJobRunPickupTime(context.Context, *UpdateJobRunPickupTimeRequest) (*UpdateJobRunPickupTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateJobRunPickupTime not implemented")
}

func RegisterProcessorInternalServer(s *grpc.Server, srv ProcessorInternalServer) {
	s.RegisterService(&_ProcessorInternal_serviceDesc, srv)
}

func _ProcessorInternal_GetCurrentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CurrentStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorInternalServer).GetCurrentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.ProcessorInternal/GetCurrentStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorInternalServer).GetCurrentStatus(ctx, req.(*CurrentStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProcessorInternal_DrainAndStopProcessor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DrainAndStopProcessorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorInternalServer).DrainAndStopProcessor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.ProcessorInternal/DrainAndStopProcessor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorInternalServer).DrainAndStopProcessor(ctx, req.(*DrainAndStopProcessorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProcessorInternal_UpdateJobRunPickupTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateJobRunPickupTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProcessorInternalServer).UpdateJobRunPickupTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobber.ProcessorInternal/UpdateJobRunPickupTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProcessorInternalServer).UpdateJobRunPickupTime(ctx, req.(*UpdateJobRunPickupTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ProcessorInternal_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jobber.ProcessorInternal",
	HandlerType: (*ProcessorInternalServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCurrentStatus",
			Handler:    _ProcessorInternal_GetCurrentStatus_Handler,
		},
		{
			MethodName: "DrainAndStopProcessor",
			Handler:    _ProcessorInternal_DrainAndStopProcessor_Handler,
		},
		{
			MethodName: "UpdateJobRunPickupTime",
			Handler:    _ProcessorInternal_UpdateJobRunPickupTime_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "jobber/jobber.proto",
}
