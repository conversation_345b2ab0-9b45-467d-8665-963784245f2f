// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: jobber/ops/ops.proto

package opsproto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OpsState int32

const (
	OpsState_Blank                     OpsState = 0
	OpsState_ReadyToDrainAfterRedeploy OpsState = 1
)

// Enum value maps for OpsState.
var (
	OpsState_name = map[int32]string{
		0: "Blank",
		1: "ReadyToDrainAfterRedeploy",
	}
	OpsState_value = map[string]int32{
		"Blank":                     0,
		"ReadyToDrainAfterRedeploy": 1,
	}
)

func (x OpsState) Enum() *OpsState {
	p := new(OpsState)
	*p = x
	return p
}

func (x OpsState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OpsState) Descriptor() protoreflect.EnumDescriptor {
	return file_jobber_ops_ops_proto_enumTypes[0].Descriptor()
}

func (OpsState) Type() protoreflect.EnumType {
	return &file_jobber_ops_ops_proto_enumTypes[0]
}

func (x OpsState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OpsState.Descriptor instead.
func (OpsState) EnumDescriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{0}
}

type CurrentStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CurrentStatusRequest) Reset() {
	*x = CurrentStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentStatusRequest) ProtoMessage() {}

func (x *CurrentStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentStatusRequest.ProtoReflect.Descriptor instead.
func (*CurrentStatusRequest) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{0}
}

type CurrentStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpsState OpsState `protobuf:"varint,1,opt,name=opsState,proto3,enum=jobberops.OpsState" json:"opsState,omitempty"`
}

func (x *CurrentStatusResponse) Reset() {
	*x = CurrentStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentStatusResponse) ProtoMessage() {}

func (x *CurrentStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentStatusResponse.ProtoReflect.Descriptor instead.
func (*CurrentStatusResponse) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{1}
}

func (x *CurrentStatusResponse) GetOpsState() OpsState {
	if x != nil {
		return x.OpsState
	}
	return OpsState_Blank
}

type SetTaskProtectedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetTaskProtectedRequest) Reset() {
	*x = SetTaskProtectedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTaskProtectedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTaskProtectedRequest) ProtoMessage() {}

func (x *SetTaskProtectedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTaskProtectedRequest.ProtoReflect.Descriptor instead.
func (*SetTaskProtectedRequest) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{2}
}

type SetTaskProtectedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetTaskProtectedResponse) Reset() {
	*x = SetTaskProtectedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTaskProtectedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTaskProtectedResponse) ProtoMessage() {}

func (x *SetTaskProtectedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTaskProtectedResponse.ProtoReflect.Descriptor instead.
func (*SetTaskProtectedResponse) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{3}
}

type SetTaskUnprotectedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetTaskUnprotectedRequest) Reset() {
	*x = SetTaskUnprotectedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTaskUnprotectedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTaskUnprotectedRequest) ProtoMessage() {}

func (x *SetTaskUnprotectedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTaskUnprotectedRequest.ProtoReflect.Descriptor instead.
func (*SetTaskUnprotectedRequest) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{4}
}

type SetTaskUnprotectedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetTaskUnprotectedResponse) Reset() {
	*x = SetTaskUnprotectedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTaskUnprotectedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTaskUnprotectedResponse) ProtoMessage() {}

func (x *SetTaskUnprotectedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTaskUnprotectedResponse.ProtoReflect.Descriptor instead.
func (*SetTaskUnprotectedResponse) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{5}
}

type PrepForDrainingRedeployRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PrepForDrainingRedeployRequest) Reset() {
	*x = PrepForDrainingRedeployRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrepForDrainingRedeployRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrepForDrainingRedeployRequest) ProtoMessage() {}

func (x *PrepForDrainingRedeployRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrepForDrainingRedeployRequest.ProtoReflect.Descriptor instead.
func (*PrepForDrainingRedeployRequest) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{6}
}

type PrepForDrainingRedeployResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PrepForDrainingRedeployResponse) Reset() {
	*x = PrepForDrainingRedeployResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrepForDrainingRedeployResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrepForDrainingRedeployResponse) ProtoMessage() {}

func (x *PrepForDrainingRedeployResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrepForDrainingRedeployResponse.ProtoReflect.Descriptor instead.
func (*PrepForDrainingRedeployResponse) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{7}
}

type StartDrainingAfterRedeployRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartDrainingAfterRedeployRequest) Reset() {
	*x = StartDrainingAfterRedeployRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDrainingAfterRedeployRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDrainingAfterRedeployRequest) ProtoMessage() {}

func (x *StartDrainingAfterRedeployRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDrainingAfterRedeployRequest.ProtoReflect.Descriptor instead.
func (*StartDrainingAfterRedeployRequest) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{8}
}

type StartDrainingAfterRedeployResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartDrainingAfterRedeployResponse) Reset() {
	*x = StartDrainingAfterRedeployResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDrainingAfterRedeployResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDrainingAfterRedeployResponse) ProtoMessage() {}

func (x *StartDrainingAfterRedeployResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDrainingAfterRedeployResponse.ProtoReflect.Descriptor instead.
func (*StartDrainingAfterRedeployResponse) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{9}
}

type ResetOpsStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResetOpsStateRequest) Reset() {
	*x = ResetOpsStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetOpsStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetOpsStateRequest) ProtoMessage() {}

func (x *ResetOpsStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetOpsStateRequest.ProtoReflect.Descriptor instead.
func (*ResetOpsStateRequest) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{10}
}

type ResetOpsStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResetOpsStateResponse) Reset() {
	*x = ResetOpsStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_jobber_ops_ops_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetOpsStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetOpsStateResponse) ProtoMessage() {}

func (x *ResetOpsStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jobber_ops_ops_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetOpsStateResponse.ProtoReflect.Descriptor instead.
func (*ResetOpsStateResponse) Descriptor() ([]byte, []int) {
	return file_jobber_ops_ops_proto_rawDescGZIP(), []int{11}
}

var File_jobber_ops_ops_proto protoreflect.FileDescriptor

var file_jobber_ops_ops_proto_rawDesc = []byte{
	0x0a, 0x14, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x2f, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x6f, 0x70,
	0x73, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x48, 0x0a, 0x15, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2f, 0x0a, 0x08, 0x6f, 0x70, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x6f, 0x70, 0x73,
	0x2e, 0x4f, 0x70, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x08, 0x6f, 0x70, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x22, 0x19, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72,
	0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x1a,
	0x0a, 0x18, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1b, 0x0a, 0x19, 0x53, 0x65,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x55, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x1c, 0x0a, 0x1a, 0x53, 0x65, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x55, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x20, 0x0a, 0x1e, 0x50, 0x72, 0x65, 0x70, 0x46, 0x6f, 0x72,
	0x44, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x21, 0x0a, 0x1f, 0x50, 0x72, 0x65, 0x70, 0x46,
	0x6f, 0x72, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0x0a, 0x21, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x41, 0x66, 0x74, 0x65, 0x72,
	0x52, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22,
	0x24, 0x0a, 0x22, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x41, 0x66, 0x74, 0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4f, 0x70,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x17, 0x0a,
	0x15, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4f, 0x70, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2a, 0x34, 0x0a, 0x08, 0x4f, 0x70, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x10, 0x00, 0x12, 0x1d, 0x0a,
	0x19, 0x52, 0x65, 0x61, 0x64, 0x79, 0x54, 0x6f, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x66, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x10, 0x01, 0x32, 0xfd, 0x04, 0x0a,
	0x0d, 0x41, 0x77, 0x73, 0x45, 0x63, 0x73, 0x54, 0x61, 0x73, 0x6b, 0x4f, 0x70, 0x73, 0x12, 0x5d,
	0x0a, 0x10, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x12, 0x22, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x6f, 0x70, 0x73, 0x2e, 0x53,
	0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x6f,
	0x70, 0x73, 0x2e, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a,
	0x12, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x55, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x12, 0x24, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x6f, 0x70, 0x73, 0x2e,
	0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x55, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x6a, 0x6f, 0x62, 0x62,
	0x65, 0x72, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x55, 0x6e, 0x70,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x54, 0x0a, 0x0d, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1f, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x6f, 0x70, 0x73, 0x2e,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x6f, 0x70, 0x73,
	0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x72, 0x0a, 0x17, 0x50, 0x72, 0x65, 0x70,
	0x46, 0x6f, 0x72, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x12, 0x29, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x6f, 0x70, 0x73, 0x2e,
	0x50, 0x72, 0x65, 0x70, 0x46, 0x6f, 0x72, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a,
	0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x70, 0x46,
	0x6f, 0x72, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x1a,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x41, 0x66, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x12, 0x2c, 0x2e, 0x6a, 0x6f, 0x62,
	0x62, 0x65, 0x72, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x72, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x41, 0x66, 0x74, 0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6a, 0x6f, 0x62, 0x62, 0x65,
	0x72, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x72, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x41, 0x66, 0x74, 0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x61, 0x0a, 0x0d, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x4f, 0x70, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x6a, 0x6f, 0x62,
	0x62, 0x65, 0x72, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4f, 0x70, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x72, 0x61,
	0x69, 0x6e, 0x69, 0x6e, 0x67, 0x41, 0x66, 0x74, 0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_jobber_ops_ops_proto_rawDescOnce sync.Once
	file_jobber_ops_ops_proto_rawDescData = file_jobber_ops_ops_proto_rawDesc
)

func file_jobber_ops_ops_proto_rawDescGZIP() []byte {
	file_jobber_ops_ops_proto_rawDescOnce.Do(func() {
		file_jobber_ops_ops_proto_rawDescData = protoimpl.X.CompressGZIP(file_jobber_ops_ops_proto_rawDescData)
	})
	return file_jobber_ops_ops_proto_rawDescData
}

var file_jobber_ops_ops_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_jobber_ops_ops_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_jobber_ops_ops_proto_goTypes = []interface{}{
	(OpsState)(0),                              // 0: jobberops.OpsState
	(*CurrentStatusRequest)(nil),               // 1: jobberops.CurrentStatusRequest
	(*CurrentStatusResponse)(nil),              // 2: jobberops.CurrentStatusResponse
	(*SetTaskProtectedRequest)(nil),            // 3: jobberops.SetTaskProtectedRequest
	(*SetTaskProtectedResponse)(nil),           // 4: jobberops.SetTaskProtectedResponse
	(*SetTaskUnprotectedRequest)(nil),          // 5: jobberops.SetTaskUnprotectedRequest
	(*SetTaskUnprotectedResponse)(nil),         // 6: jobberops.SetTaskUnprotectedResponse
	(*PrepForDrainingRedeployRequest)(nil),     // 7: jobberops.PrepForDrainingRedeployRequest
	(*PrepForDrainingRedeployResponse)(nil),    // 8: jobberops.PrepForDrainingRedeployResponse
	(*StartDrainingAfterRedeployRequest)(nil),  // 9: jobberops.StartDrainingAfterRedeployRequest
	(*StartDrainingAfterRedeployResponse)(nil), // 10: jobberops.StartDrainingAfterRedeployResponse
	(*ResetOpsStateRequest)(nil),               // 11: jobberops.ResetOpsStateRequest
	(*ResetOpsStateResponse)(nil),              // 12: jobberops.ResetOpsStateResponse
}
var file_jobber_ops_ops_proto_depIdxs = []int32{
	0,  // 0: jobberops.CurrentStatusResponse.opsState:type_name -> jobberops.OpsState
	3,  // 1: jobberops.AwsEcsTaskOps.SetTaskProtected:input_type -> jobberops.SetTaskProtectedRequest
	5,  // 2: jobberops.AwsEcsTaskOps.SetTaskUnprotected:input_type -> jobberops.SetTaskUnprotectedRequest
	1,  // 3: jobberops.AwsEcsTaskOps.CurrentStatus:input_type -> jobberops.CurrentStatusRequest
	7,  // 4: jobberops.AwsEcsTaskOps.PrepForDrainingRedeploy:input_type -> jobberops.PrepForDrainingRedeployRequest
	9,  // 5: jobberops.AwsEcsTaskOps.StartDrainingAfterRedeploy:input_type -> jobberops.StartDrainingAfterRedeployRequest
	11, // 6: jobberops.AwsEcsTaskOps.ResetOpsState:input_type -> jobberops.ResetOpsStateRequest
	4,  // 7: jobberops.AwsEcsTaskOps.SetTaskProtected:output_type -> jobberops.SetTaskProtectedResponse
	6,  // 8: jobberops.AwsEcsTaskOps.SetTaskUnprotected:output_type -> jobberops.SetTaskUnprotectedResponse
	2,  // 9: jobberops.AwsEcsTaskOps.CurrentStatus:output_type -> jobberops.CurrentStatusResponse
	8,  // 10: jobberops.AwsEcsTaskOps.PrepForDrainingRedeploy:output_type -> jobberops.PrepForDrainingRedeployResponse
	10, // 11: jobberops.AwsEcsTaskOps.StartDrainingAfterRedeploy:output_type -> jobberops.StartDrainingAfterRedeployResponse
	10, // 12: jobberops.AwsEcsTaskOps.ResetOpsState:output_type -> jobberops.StartDrainingAfterRedeployResponse
	7,  // [7:13] is the sub-list for method output_type
	1,  // [1:7] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_jobber_ops_ops_proto_init() }
func file_jobber_ops_ops_proto_init() {
	if File_jobber_ops_ops_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_jobber_ops_ops_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_ops_ops_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_ops_ops_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTaskProtectedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_ops_ops_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTaskProtectedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_ops_ops_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTaskUnprotectedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_ops_ops_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTaskUnprotectedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_ops_ops_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrepForDrainingRedeployRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_ops_ops_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrepForDrainingRedeployResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_ops_ops_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDrainingAfterRedeployRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_ops_ops_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDrainingAfterRedeployResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_ops_ops_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetOpsStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_jobber_ops_ops_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetOpsStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_jobber_ops_ops_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_jobber_ops_ops_proto_goTypes,
		DependencyIndexes: file_jobber_ops_ops_proto_depIdxs,
		EnumInfos:         file_jobber_ops_ops_proto_enumTypes,
		MessageInfos:      file_jobber_ops_ops_proto_msgTypes,
	}.Build()
	File_jobber_ops_ops_proto = out.File
	file_jobber_ops_ops_proto_rawDesc = nil
	file_jobber_ops_ops_proto_goTypes = nil
	file_jobber_ops_ops_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// AwsEcsTaskOpsClient is the client API for AwsEcsTaskOps service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AwsEcsTaskOpsClient interface {
	SetTaskProtected(ctx context.Context, in *SetTaskProtectedRequest, opts ...grpc.CallOption) (*SetTaskProtectedResponse, error)
	SetTaskUnprotected(ctx context.Context, in *SetTaskUnprotectedRequest, opts ...grpc.CallOption) (*SetTaskUnprotectedResponse, error)
	CurrentStatus(ctx context.Context, in *CurrentStatusRequest, opts ...grpc.CallOption) (*CurrentStatusResponse, error)
	PrepForDrainingRedeploy(ctx context.Context, in *PrepForDrainingRedeployRequest, opts ...grpc.CallOption) (*PrepForDrainingRedeployResponse, error)
	StartDrainingAfterRedeploy(ctx context.Context, in *StartDrainingAfterRedeployRequest, opts ...grpc.CallOption) (*StartDrainingAfterRedeployResponse, error)
	ResetOpsState(ctx context.Context, in *ResetOpsStateRequest, opts ...grpc.CallOption) (*StartDrainingAfterRedeployResponse, error)
}

type awsEcsTaskOpsClient struct {
	cc grpc.ClientConnInterface
}

func NewAwsEcsTaskOpsClient(cc grpc.ClientConnInterface) AwsEcsTaskOpsClient {
	return &awsEcsTaskOpsClient{cc}
}

func (c *awsEcsTaskOpsClient) SetTaskProtected(ctx context.Context, in *SetTaskProtectedRequest, opts ...grpc.CallOption) (*SetTaskProtectedResponse, error) {
	out := new(SetTaskProtectedResponse)
	err := c.cc.Invoke(ctx, "/jobberops.AwsEcsTaskOps/SetTaskProtected", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsEcsTaskOpsClient) SetTaskUnprotected(ctx context.Context, in *SetTaskUnprotectedRequest, opts ...grpc.CallOption) (*SetTaskUnprotectedResponse, error) {
	out := new(SetTaskUnprotectedResponse)
	err := c.cc.Invoke(ctx, "/jobberops.AwsEcsTaskOps/SetTaskUnprotected", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsEcsTaskOpsClient) CurrentStatus(ctx context.Context, in *CurrentStatusRequest, opts ...grpc.CallOption) (*CurrentStatusResponse, error) {
	out := new(CurrentStatusResponse)
	err := c.cc.Invoke(ctx, "/jobberops.AwsEcsTaskOps/CurrentStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsEcsTaskOpsClient) PrepForDrainingRedeploy(ctx context.Context, in *PrepForDrainingRedeployRequest, opts ...grpc.CallOption) (*PrepForDrainingRedeployResponse, error) {
	out := new(PrepForDrainingRedeployResponse)
	err := c.cc.Invoke(ctx, "/jobberops.AwsEcsTaskOps/PrepForDrainingRedeploy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsEcsTaskOpsClient) StartDrainingAfterRedeploy(ctx context.Context, in *StartDrainingAfterRedeployRequest, opts ...grpc.CallOption) (*StartDrainingAfterRedeployResponse, error) {
	out := new(StartDrainingAfterRedeployResponse)
	err := c.cc.Invoke(ctx, "/jobberops.AwsEcsTaskOps/StartDrainingAfterRedeploy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsEcsTaskOpsClient) ResetOpsState(ctx context.Context, in *ResetOpsStateRequest, opts ...grpc.CallOption) (*StartDrainingAfterRedeployResponse, error) {
	out := new(StartDrainingAfterRedeployResponse)
	err := c.cc.Invoke(ctx, "/jobberops.AwsEcsTaskOps/ResetOpsState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AwsEcsTaskOpsServer is the server API for AwsEcsTaskOps service.
type AwsEcsTaskOpsServer interface {
	SetTaskProtected(context.Context, *SetTaskProtectedRequest) (*SetTaskProtectedResponse, error)
	SetTaskUnprotected(context.Context, *SetTaskUnprotectedRequest) (*SetTaskUnprotectedResponse, error)
	CurrentStatus(context.Context, *CurrentStatusRequest) (*CurrentStatusResponse, error)
	PrepForDrainingRedeploy(context.Context, *PrepForDrainingRedeployRequest) (*PrepForDrainingRedeployResponse, error)
	StartDrainingAfterRedeploy(context.Context, *StartDrainingAfterRedeployRequest) (*StartDrainingAfterRedeployResponse, error)
	ResetOpsState(context.Context, *ResetOpsStateRequest) (*StartDrainingAfterRedeployResponse, error)
}

// UnimplementedAwsEcsTaskOpsServer can be embedded to have forward compatible implementations.
type UnimplementedAwsEcsTaskOpsServer struct {
}

func (*UnimplementedAwsEcsTaskOpsServer) SetTaskProtected(context.Context, *SetTaskProtectedRequest) (*SetTaskProtectedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTaskProtected not implemented")
}
func (*UnimplementedAwsEcsTaskOpsServer) SetTaskUnprotected(context.Context, *SetTaskUnprotectedRequest) (*SetTaskUnprotectedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTaskUnprotected not implemented")
}
func (*UnimplementedAwsEcsTaskOpsServer) CurrentStatus(context.Context, *CurrentStatusRequest) (*CurrentStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CurrentStatus not implemented")
}
func (*UnimplementedAwsEcsTaskOpsServer) PrepForDrainingRedeploy(context.Context, *PrepForDrainingRedeployRequest) (*PrepForDrainingRedeployResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PrepForDrainingRedeploy not implemented")
}
func (*UnimplementedAwsEcsTaskOpsServer) StartDrainingAfterRedeploy(context.Context, *StartDrainingAfterRedeployRequest) (*StartDrainingAfterRedeployResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDrainingAfterRedeploy not implemented")
}
func (*UnimplementedAwsEcsTaskOpsServer) ResetOpsState(context.Context, *ResetOpsStateRequest) (*StartDrainingAfterRedeployResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetOpsState not implemented")
}

func RegisterAwsEcsTaskOpsServer(s *grpc.Server, srv AwsEcsTaskOpsServer) {
	s.RegisterService(&_AwsEcsTaskOps_serviceDesc, srv)
}

func _AwsEcsTaskOps_SetTaskProtected_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTaskProtectedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsEcsTaskOpsServer).SetTaskProtected(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobberops.AwsEcsTaskOps/SetTaskProtected",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsEcsTaskOpsServer).SetTaskProtected(ctx, req.(*SetTaskProtectedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsEcsTaskOps_SetTaskUnprotected_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTaskUnprotectedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsEcsTaskOpsServer).SetTaskUnprotected(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobberops.AwsEcsTaskOps/SetTaskUnprotected",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsEcsTaskOpsServer).SetTaskUnprotected(ctx, req.(*SetTaskUnprotectedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsEcsTaskOps_CurrentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CurrentStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsEcsTaskOpsServer).CurrentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobberops.AwsEcsTaskOps/CurrentStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsEcsTaskOpsServer).CurrentStatus(ctx, req.(*CurrentStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsEcsTaskOps_PrepForDrainingRedeploy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PrepForDrainingRedeployRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsEcsTaskOpsServer).PrepForDrainingRedeploy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobberops.AwsEcsTaskOps/PrepForDrainingRedeploy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsEcsTaskOpsServer).PrepForDrainingRedeploy(ctx, req.(*PrepForDrainingRedeployRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsEcsTaskOps_StartDrainingAfterRedeploy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartDrainingAfterRedeployRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsEcsTaskOpsServer).StartDrainingAfterRedeploy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobberops.AwsEcsTaskOps/StartDrainingAfterRedeploy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsEcsTaskOpsServer).StartDrainingAfterRedeploy(ctx, req.(*StartDrainingAfterRedeployRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsEcsTaskOps_ResetOpsState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetOpsStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsEcsTaskOpsServer).ResetOpsState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jobberops.AwsEcsTaskOps/ResetOpsState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsEcsTaskOpsServer).ResetOpsState(ctx, req.(*ResetOpsStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AwsEcsTaskOps_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jobberops.AwsEcsTaskOps",
	HandlerType: (*AwsEcsTaskOpsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetTaskProtected",
			Handler:    _AwsEcsTaskOps_SetTaskProtected_Handler,
		},
		{
			MethodName: "SetTaskUnprotected",
			Handler:    _AwsEcsTaskOps_SetTaskUnprotected_Handler,
		},
		{
			MethodName: "CurrentStatus",
			Handler:    _AwsEcsTaskOps_CurrentStatus_Handler,
		},
		{
			MethodName: "PrepForDrainingRedeploy",
			Handler:    _AwsEcsTaskOps_PrepForDrainingRedeploy_Handler,
		},
		{
			MethodName: "StartDrainingAfterRedeploy",
			Handler:    _AwsEcsTaskOps_StartDrainingAfterRedeploy_Handler,
		},
		{
			MethodName: "ResetOpsState",
			Handler:    _AwsEcsTaskOps_ResetOpsState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "jobber/ops/ops.proto",
}
