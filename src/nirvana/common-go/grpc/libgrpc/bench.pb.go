// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: libgrpc/bench.proto

package libgrpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BenchmarkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TextField string `protobuf:"bytes,1,opt,name=textField,proto3" json:"textField,omitempty"`
}

func (x *BenchmarkRequest) Reset() {
	*x = BenchmarkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_libgrpc_bench_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenchmarkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenchmarkRequest) ProtoMessage() {}

func (x *BenchmarkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_libgrpc_bench_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenchmarkRequest.ProtoReflect.Descriptor instead.
func (*BenchmarkRequest) Descriptor() ([]byte, []int) {
	return file_libgrpc_bench_proto_rawDescGZIP(), []int{0}
}

func (x *BenchmarkRequest) GetTextField() string {
	if x != nil {
		return x.TextField
	}
	return ""
}

type BenchmarkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TextField string `protobuf:"bytes,1,opt,name=textField,proto3" json:"textField,omitempty"`
}

func (x *BenchmarkResponse) Reset() {
	*x = BenchmarkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_libgrpc_bench_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenchmarkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenchmarkResponse) ProtoMessage() {}

func (x *BenchmarkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_libgrpc_bench_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenchmarkResponse.ProtoReflect.Descriptor instead.
func (*BenchmarkResponse) Descriptor() ([]byte, []int) {
	return file_libgrpc_bench_proto_rawDescGZIP(), []int{1}
}

func (x *BenchmarkResponse) GetTextField() string {
	if x != nil {
		return x.TextField
	}
	return ""
}

var File_libgrpc_bench_proto protoreflect.FileDescriptor

var file_libgrpc_bench_proto_rawDesc = []byte{
	0x0a, 0x13, 0x6c, 0x69, 0x62, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6c, 0x69, 0x62, 0x67, 0x72, 0x70, 0x63, 0x22, 0x30,
	0x0a, 0x10, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x65, 0x78, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x65, 0x78, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x22, 0x31, 0x0a, 0x11, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x65, 0x78, 0x74, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x65, 0x78, 0x74, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x32, 0x56, 0x0a, 0x10, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x12, 0x19, 0x2e, 0x6c, 0x69, 0x62, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x65, 0x6e,
	0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e,
	0x6c, 0x69, 0x62, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_libgrpc_bench_proto_rawDescOnce sync.Once
	file_libgrpc_bench_proto_rawDescData = file_libgrpc_bench_proto_rawDesc
)

func file_libgrpc_bench_proto_rawDescGZIP() []byte {
	file_libgrpc_bench_proto_rawDescOnce.Do(func() {
		file_libgrpc_bench_proto_rawDescData = protoimpl.X.CompressGZIP(file_libgrpc_bench_proto_rawDescData)
	})
	return file_libgrpc_bench_proto_rawDescData
}

var file_libgrpc_bench_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_libgrpc_bench_proto_goTypes = []interface{}{
	(*BenchmarkRequest)(nil),  // 0: libgrpc.BenchmarkRequest
	(*BenchmarkResponse)(nil), // 1: libgrpc.BenchmarkResponse
}
var file_libgrpc_bench_proto_depIdxs = []int32{
	0, // 0: libgrpc.BenchmarkService.Process:input_type -> libgrpc.BenchmarkRequest
	1, // 1: libgrpc.BenchmarkService.Process:output_type -> libgrpc.BenchmarkResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_libgrpc_bench_proto_init() }
func file_libgrpc_bench_proto_init() {
	if File_libgrpc_bench_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_libgrpc_bench_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenchmarkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_libgrpc_bench_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenchmarkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_libgrpc_bench_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_libgrpc_bench_proto_goTypes,
		DependencyIndexes: file_libgrpc_bench_proto_depIdxs,
		MessageInfos:      file_libgrpc_bench_proto_msgTypes,
	}.Build()
	File_libgrpc_bench_proto = out.File
	file_libgrpc_bench_proto_rawDesc = nil
	file_libgrpc_bench_proto_goTypes = nil
	file_libgrpc_bench_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// BenchmarkServiceClient is the client API for BenchmarkService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BenchmarkServiceClient interface {
	Process(ctx context.Context, in *BenchmarkRequest, opts ...grpc.CallOption) (*BenchmarkResponse, error)
}

type benchmarkServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBenchmarkServiceClient(cc grpc.ClientConnInterface) BenchmarkServiceClient {
	return &benchmarkServiceClient{cc}
}

func (c *benchmarkServiceClient) Process(ctx context.Context, in *BenchmarkRequest, opts ...grpc.CallOption) (*BenchmarkResponse, error) {
	out := new(BenchmarkResponse)
	err := c.cc.Invoke(ctx, "/libgrpc.BenchmarkService/Process", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BenchmarkServiceServer is the server API for BenchmarkService service.
type BenchmarkServiceServer interface {
	Process(context.Context, *BenchmarkRequest) (*BenchmarkResponse, error)
}

// UnimplementedBenchmarkServiceServer can be embedded to have forward compatible implementations.
type UnimplementedBenchmarkServiceServer struct {
}

func (*UnimplementedBenchmarkServiceServer) Process(context.Context, *BenchmarkRequest) (*BenchmarkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Process not implemented")
}

func RegisterBenchmarkServiceServer(s *grpc.Server, srv BenchmarkServiceServer) {
	s.RegisterService(&_BenchmarkService_serviceDesc, srv)
}

func _BenchmarkService_Process_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BenchmarkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BenchmarkServiceServer).Process(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/libgrpc.BenchmarkService/Process",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BenchmarkServiceServer).Process(ctx, req.(*BenchmarkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BenchmarkService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "libgrpc.BenchmarkService",
	HandlerType: (*BenchmarkServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Process",
			Handler:    _BenchmarkService_Process_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "libgrpc/bench.proto",
}
