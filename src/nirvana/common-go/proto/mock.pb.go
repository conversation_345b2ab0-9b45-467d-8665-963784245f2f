// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: common/mock.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MockEnum int32

const (
	MockEnum_MOCK_ENUM_UNSPECIFIED MockEnum = 0
	MockEnum_MOCK_ENUM_ONE         MockEnum = 1
	MockEnum_MOCK_ENUM_TWO         MockEnum = 2
	MockEnum_MOCK_ENUM_THREE       MockEnum = 3
)

// Enum value maps for MockEnum.
var (
	MockEnum_name = map[int32]string{
		0: "MOCK_ENUM_UNSPECIFIED",
		1: "MOCK_ENUM_ONE",
		2: "MOCK_ENUM_TWO",
		3: "MOCK_ENUM_THREE",
	}
	MockEnum_value = map[string]int32{
		"MOCK_ENUM_UNSPECIFIED": 0,
		"MOCK_ENUM_ONE":         1,
		"MOCK_ENUM_TWO":         2,
		"MOCK_ENUM_THREE":       3,
	}
)

func (x MockEnum) Enum() *MockEnum {
	p := new(MockEnum)
	*p = x
	return p
}

func (x MockEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MockEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_common_mock_proto_enumTypes[0].Descriptor()
}

func (MockEnum) Type() protoreflect.EnumType {
	return &file_common_mock_proto_enumTypes[0]
}

func (x MockEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MockEnum.Descriptor instead.
func (MockEnum) EnumDescriptor() ([]byte, []int) {
	return file_common_mock_proto_rawDescGZIP(), []int{0}
}

type OneOfMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Choice:
	//
	//	*OneOfMessage_Choice1
	//	*OneOfMessage_Choice2
	Choice isOneOfMessage_Choice `protobuf_oneof:"choice"`
}

func (x *OneOfMessage) Reset() {
	*x = OneOfMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_mock_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OneOfMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OneOfMessage) ProtoMessage() {}

func (x *OneOfMessage) ProtoReflect() protoreflect.Message {
	mi := &file_common_mock_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OneOfMessage.ProtoReflect.Descriptor instead.
func (*OneOfMessage) Descriptor() ([]byte, []int) {
	return file_common_mock_proto_rawDescGZIP(), []int{0}
}

func (m *OneOfMessage) GetChoice() isOneOfMessage_Choice {
	if m != nil {
		return m.Choice
	}
	return nil
}

func (x *OneOfMessage) GetChoice1() *OneOfChoice1 {
	if x, ok := x.GetChoice().(*OneOfMessage_Choice1); ok {
		return x.Choice1
	}
	return nil
}

func (x *OneOfMessage) GetChoice2() *OnceOfChoice2 {
	if x, ok := x.GetChoice().(*OneOfMessage_Choice2); ok {
		return x.Choice2
	}
	return nil
}

type isOneOfMessage_Choice interface {
	isOneOfMessage_Choice()
}

type OneOfMessage_Choice1 struct {
	Choice1 *OneOfChoice1 `protobuf:"bytes,1,opt,name=choice1,proto3,oneof"`
}

type OneOfMessage_Choice2 struct {
	Choice2 *OnceOfChoice2 `protobuf:"bytes,2,opt,name=choice2,proto3,oneof"`
}

func (*OneOfMessage_Choice1) isOneOfMessage_Choice() {}

func (*OneOfMessage_Choice2) isOneOfMessage_Choice() {}

type OneOfChoice1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OneOfChoice1) Reset() {
	*x = OneOfChoice1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_mock_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OneOfChoice1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OneOfChoice1) ProtoMessage() {}

func (x *OneOfChoice1) ProtoReflect() protoreflect.Message {
	mi := &file_common_mock_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OneOfChoice1.ProtoReflect.Descriptor instead.
func (*OneOfChoice1) Descriptor() ([]byte, []int) {
	return file_common_mock_proto_rawDescGZIP(), []int{1}
}

type OnceOfChoice2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OnceOfChoice2) Reset() {
	*x = OnceOfChoice2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_mock_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnceOfChoice2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnceOfChoice2) ProtoMessage() {}

func (x *OnceOfChoice2) ProtoReflect() protoreflect.Message {
	mi := &file_common_mock_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnceOfChoice2.ProtoReflect.Descriptor instead.
func (*OnceOfChoice2) Descriptor() ([]byte, []int) {
	return file_common_mock_proto_rawDescGZIP(), []int{2}
}

type Mock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name         string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Created      *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created,proto3" json:"created,omitempty"`
	Updated      *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=updated,proto3" json:"updated,omitempty"`
	Description  string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Age          int32                  `protobuf:"varint,6,opt,name=age,proto3" json:"age,omitempty"`
	Active       bool                   `protobuf:"varint,7,opt,name=active,proto3" json:"active,omitempty"`
	Score        float64                `protobuf:"fixed64,8,opt,name=score,proto3" json:"score,omitempty"`
	Rating       float32                `protobuf:"fixed32,9,opt,name=rating,proto3" json:"rating,omitempty"`
	Data         []byte                 `protobuf:"bytes,10,opt,name=data,proto3" json:"data,omitempty"`
	Tags         []string               `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`
	Properties   map[string]string      `protobuf:"bytes,12,rep,name=properties,proto3" json:"properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Nested       *Mock                  `protobuf:"bytes,13,opt,name=nested,proto3" json:"nested,omitempty"`
	NestedList   []*Mock                `protobuf:"bytes,14,rep,name=nestedList,proto3" json:"nestedList,omitempty"`
	NestedMap    map[string]*Mock       `protobuf:"bytes,15,rep,name=nestedMap,proto3" json:"nestedMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Enum         MockEnum               `protobuf:"varint,16,opt,name=enum,proto3,enum=common.MockEnum" json:"enum,omitempty"`
	OneOfMessage *OneOfMessage          `protobuf:"bytes,17,opt,name=oneOfMessage,proto3" json:"oneOfMessage,omitempty"`
}

func (x *Mock) Reset() {
	*x = Mock{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_mock_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Mock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mock) ProtoMessage() {}

func (x *Mock) ProtoReflect() protoreflect.Message {
	mi := &file_common_mock_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mock.ProtoReflect.Descriptor instead.
func (*Mock) Descriptor() ([]byte, []int) {
	return file_common_mock_proto_rawDescGZIP(), []int{3}
}

func (x *Mock) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Mock) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Mock) GetCreated() *timestamppb.Timestamp {
	if x != nil {
		return x.Created
	}
	return nil
}

func (x *Mock) GetUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.Updated
	}
	return nil
}

func (x *Mock) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Mock) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *Mock) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

func (x *Mock) GetScore() float64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *Mock) GetRating() float32 {
	if x != nil {
		return x.Rating
	}
	return 0
}

func (x *Mock) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Mock) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Mock) GetProperties() map[string]string {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *Mock) GetNested() *Mock {
	if x != nil {
		return x.Nested
	}
	return nil
}

func (x *Mock) GetNestedList() []*Mock {
	if x != nil {
		return x.NestedList
	}
	return nil
}

func (x *Mock) GetNestedMap() map[string]*Mock {
	if x != nil {
		return x.NestedMap
	}
	return nil
}

func (x *Mock) GetEnum() MockEnum {
	if x != nil {
		return x.Enum
	}
	return MockEnum_MOCK_ENUM_UNSPECIFIED
}

func (x *Mock) GetOneOfMessage() *OneOfMessage {
	if x != nil {
		return x.OneOfMessage
	}
	return nil
}

var File_common_mock_proto protoreflect.FileDescriptor

var file_common_mock_proto_rawDesc = []byte{
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6d, 0x6f, 0x63, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7d, 0x0a, 0x0c,
	0x4f, 0x6e, 0x65, 0x4f, 0x66, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x30, 0x0a, 0x07,
	0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x6e, 0x65, 0x4f, 0x66, 0x43, 0x68, 0x6f, 0x69,
	0x63, 0x65, 0x31, 0x48, 0x00, 0x52, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x31, 0x12, 0x31,
	0x0a, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x6e, 0x63, 0x65, 0x4f, 0x66, 0x43,
	0x68, 0x6f, 0x69, 0x63, 0x65, 0x32, 0x48, 0x00, 0x52, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65,
	0x32, 0x42, 0x08, 0x0a, 0x06, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x22, 0x0e, 0x0a, 0x0c, 0x4f,
	0x6e, 0x65, 0x4f, 0x66, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x31, 0x22, 0x0f, 0x0a, 0x0d, 0x4f,
	0x6e, 0x63, 0x65, 0x4f, 0x66, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x32, 0x22, 0xf0, 0x05, 0x0a,
	0x04, 0x4d, 0x6f, 0x63, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12,
	0x34, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e,
	0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x06, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d,
	0x6f, 0x63, 0x6b, 0x52, 0x06, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x2c, 0x0a, 0x0a, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x52, 0x0a, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x09, 0x6e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x4d, 0x61, 0x70, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x6e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x4d, 0x61, 0x70, 0x12, 0x24, 0x0a, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f, 0x63, 0x6b,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x38, 0x0a, 0x0c, 0x6f, 0x6e,
	0x65, 0x4f, 0x66, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x6e, 0x65, 0x4f, 0x66, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x6f, 0x6e, 0x65, 0x4f, 0x66, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x1a, 0x3d, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x4a, 0x0a, 0x0e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x22, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x4d, 0x6f, 0x63, 0x6b, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a,
	0x60, 0x0a, 0x08, 0x4d, 0x6f, 0x63, 0x6b, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x19, 0x0a, 0x15, 0x4d,
	0x4f, 0x43, 0x4b, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4f, 0x43, 0x4b, 0x5f, 0x45,
	0x4e, 0x55, 0x4d, 0x5f, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4f, 0x43,
	0x4b, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x54, 0x57, 0x4f, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f,
	0x4d, 0x4f, 0x43, 0x4b, 0x5f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x45, 0x10,
	0x03, 0x42, 0x29, 0x5a, 0x27, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2d, 0x67, 0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_mock_proto_rawDescOnce sync.Once
	file_common_mock_proto_rawDescData = file_common_mock_proto_rawDesc
)

func file_common_mock_proto_rawDescGZIP() []byte {
	file_common_mock_proto_rawDescOnce.Do(func() {
		file_common_mock_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_mock_proto_rawDescData)
	})
	return file_common_mock_proto_rawDescData
}

var file_common_mock_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_common_mock_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_common_mock_proto_goTypes = []interface{}{
	(MockEnum)(0),                 // 0: common.MockEnum
	(*OneOfMessage)(nil),          // 1: common.OneOfMessage
	(*OneOfChoice1)(nil),          // 2: common.OneOfChoice1
	(*OnceOfChoice2)(nil),         // 3: common.OnceOfChoice2
	(*Mock)(nil),                  // 4: common.Mock
	nil,                           // 5: common.Mock.PropertiesEntry
	nil,                           // 6: common.Mock.NestedMapEntry
	(*timestamppb.Timestamp)(nil), // 7: google.protobuf.Timestamp
}
var file_common_mock_proto_depIdxs = []int32{
	2,  // 0: common.OneOfMessage.choice1:type_name -> common.OneOfChoice1
	3,  // 1: common.OneOfMessage.choice2:type_name -> common.OnceOfChoice2
	7,  // 2: common.Mock.created:type_name -> google.protobuf.Timestamp
	7,  // 3: common.Mock.updated:type_name -> google.protobuf.Timestamp
	5,  // 4: common.Mock.properties:type_name -> common.Mock.PropertiesEntry
	4,  // 5: common.Mock.nested:type_name -> common.Mock
	4,  // 6: common.Mock.nestedList:type_name -> common.Mock
	6,  // 7: common.Mock.nestedMap:type_name -> common.Mock.NestedMapEntry
	0,  // 8: common.Mock.enum:type_name -> common.MockEnum
	1,  // 9: common.Mock.oneOfMessage:type_name -> common.OneOfMessage
	4,  // 10: common.Mock.NestedMapEntry.value:type_name -> common.Mock
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_common_mock_proto_init() }
func file_common_mock_proto_init() {
	if File_common_mock_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_common_mock_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OneOfMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_mock_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OneOfChoice1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_mock_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnceOfChoice2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_mock_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Mock); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_common_mock_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*OneOfMessage_Choice1)(nil),
		(*OneOfMessage_Choice2)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_mock_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_mock_proto_goTypes,
		DependencyIndexes: file_common_mock_proto_depIdxs,
		EnumInfos:         file_common_mock_proto_enumTypes,
		MessageInfos:      file_common_mock_proto_msgTypes,
	}.Build()
	File_common_mock_proto = out.File
	file_common_mock_proto_rawDesc = nil
	file_common_mock_proto_goTypes = nil
	file_common_mock_proto_depIdxs = nil
}
