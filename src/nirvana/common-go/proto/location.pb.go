// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: common/location.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Address struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nation     *string `protobuf:"bytes,1,opt,name=nation,proto3,oneof" json:"nation,omitempty"`
	State      *string `protobuf:"bytes,2,opt,name=state,proto3,oneof" json:"state,omitempty"`
	City       *string `protobuf:"bytes,3,opt,name=city,proto3,oneof" json:"city,omitempty"`
	CountyCode *string `protobuf:"bytes,4,opt,name=countyCode,proto3,oneof" json:"countyCode,omitempty"`
	Street     *string `protobuf:"bytes,5,opt,name=street,proto3,oneof" json:"street,omitempty"`
	ZipCode    *string `protobuf:"bytes,6,opt,name=zipCode,proto3,oneof" json:"zipCode,omitempty"`
	Street2    *string `protobuf:"bytes,7,opt,name=street2,proto3,oneof" json:"street2,omitempty"`
}

func (x *Address) Reset() {
	*x = Address{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_location_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_common_location_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_common_location_proto_rawDescGZIP(), []int{0}
}

func (x *Address) GetNation() string {
	if x != nil && x.Nation != nil {
		return *x.Nation
	}
	return ""
}

func (x *Address) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *Address) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *Address) GetCountyCode() string {
	if x != nil && x.CountyCode != nil {
		return *x.CountyCode
	}
	return ""
}

func (x *Address) GetStreet() string {
	if x != nil && x.Street != nil {
		return *x.Street
	}
	return ""
}

func (x *Address) GetZipCode() string {
	if x != nil && x.ZipCode != nil {
		return *x.ZipCode
	}
	return ""
}

func (x *Address) GetStreet2() string {
	if x != nil && x.Street2 != nil {
		return *x.Street2
	}
	return ""
}

var File_common_location_proto protoreflect.FileDescriptor

var file_common_location_proto_rawDesc = []byte{
	0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22,
	0xaa, 0x02, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1b, 0x0a, 0x06, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x02, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x03, 0x52, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x1b, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x04, 0x52, 0x06, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1d,
	0x0a, 0x07, 0x7a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x05, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a,
	0x07, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x32, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06,
	0x52, 0x07, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x32, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74,
	0x72, 0x65, 0x65, 0x74, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x7a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65,
	0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74, 0x32, 0x42, 0x29, 0x5a, 0x27,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2d, 0x67,
	0x6f, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_location_proto_rawDescOnce sync.Once
	file_common_location_proto_rawDescData = file_common_location_proto_rawDesc
)

func file_common_location_proto_rawDescGZIP() []byte {
	file_common_location_proto_rawDescOnce.Do(func() {
		file_common_location_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_location_proto_rawDescData)
	})
	return file_common_location_proto_rawDescData
}

var file_common_location_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_common_location_proto_goTypes = []interface{}{
	(*Address)(nil), // 0: common.Address
}
var file_common_location_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_common_location_proto_init() }
func file_common_location_proto_init() {
	if File_common_location_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_common_location_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Address); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_common_location_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_location_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_location_proto_goTypes,
		DependencyIndexes: file_common_location_proto_depIdxs,
		MessageInfos:      file_common_location_proto_msgTypes,
	}.Build()
	File_common_location_proto = out.File
	file_common_location_proto_rawDesc = nil
	file_common_location_proto_goTypes = nil
	file_common_location_proto_depIdxs = nil
}
