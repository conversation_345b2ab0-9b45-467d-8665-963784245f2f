// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: account/v1/model/account.proto

package accountv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	proto "nirvanatech.com/nirvana/common-go/proto"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AccountType int32

const (
	AccountType_ACCOUNT_TYPE_UNSPECIFIED AccountType = 0
	AccountType_ACCOUNT_TYPE_BUSINESS    AccountType = 1
	AccountType_ACCOUNT_TYPE_INDIVIDUAL  AccountType = 2
)

// Enum value maps for AccountType.
var (
	AccountType_name = map[int32]string{
		0: "ACCOUNT_TYPE_UNSPECIFIED",
		1: "ACCOUNT_TYPE_BUSINESS",
		2: "ACCOUNT_TYPE_INDIVIDUAL",
	}
	AccountType_value = map[string]int32{
		"ACCOUNT_TYPE_UNSPECIFIED": 0,
		"ACCOUNT_TYPE_BUSINESS":    1,
		"ACCOUNT_TYPE_INDIVIDUAL":  2,
	}
)

func (x AccountType) Enum() *AccountType {
	p := new(AccountType)
	*p = x
	return p
}

func (x AccountType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountType) Descriptor() protoreflect.EnumDescriptor {
	return file_account_v1_model_account_proto_enumTypes[0].Descriptor()
}

func (AccountType) Type() protoreflect.EnumType {
	return &file_account_v1_model_account_proto_enumTypes[0]
}

func (x AccountType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountType.Descriptor instead.
func (AccountType) EnumDescriptor() ([]byte, []int) {
	return file_account_v1_model_account_proto_rawDescGZIP(), []int{0}
}

type IdentifierType int32

const (
	IdentifierType_IDENTIFIER_TYPE_UNSPECIFIED IdentifierType = 0
	IdentifierType_IDENTIFIER_TYPE_DOT         IdentifierType = 1
	IdentifierType_IDENTIFIER_TYPE_FEIN        IdentifierType = 2
)

// Enum value maps for IdentifierType.
var (
	IdentifierType_name = map[int32]string{
		0: "IDENTIFIER_TYPE_UNSPECIFIED",
		1: "IDENTIFIER_TYPE_DOT",
		2: "IDENTIFIER_TYPE_FEIN",
	}
	IdentifierType_value = map[string]int32{
		"IDENTIFIER_TYPE_UNSPECIFIED": 0,
		"IDENTIFIER_TYPE_DOT":         1,
		"IDENTIFIER_TYPE_FEIN":        2,
	}
)

func (x IdentifierType) Enum() *IdentifierType {
	p := new(IdentifierType)
	*p = x
	return p
}

func (x IdentifierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentifierType) Descriptor() protoreflect.EnumDescriptor {
	return file_account_v1_model_account_proto_enumTypes[1].Descriptor()
}

func (IdentifierType) Type() protoreflect.EnumType {
	return &file_account_v1_model_account_proto_enumTypes[1]
}

func (x IdentifierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdentifierType.Descriptor instead.
func (IdentifierType) EnumDescriptor() ([]byte, []int) {
	return file_account_v1_model_account_proto_rawDescGZIP(), []int{1}
}

type ProgramType int32

const (
	ProgramType_PROGRAM_TYPE_UNSPECIFIED   ProgramType = 0
	ProgramType_PROGRAM_TYPE_FLEET         ProgramType = 1
	ProgramType_PROGRAM_TYPE_NON_FLEET     ProgramType = 2
	ProgramType_PROGRAM_TYPE_BUSINESS_AUTO ProgramType = 3
)

// Enum value maps for ProgramType.
var (
	ProgramType_name = map[int32]string{
		0: "PROGRAM_TYPE_UNSPECIFIED",
		1: "PROGRAM_TYPE_FLEET",
		2: "PROGRAM_TYPE_NON_FLEET",
		3: "PROGRAM_TYPE_BUSINESS_AUTO",
	}
	ProgramType_value = map[string]int32{
		"PROGRAM_TYPE_UNSPECIFIED":   0,
		"PROGRAM_TYPE_FLEET":         1,
		"PROGRAM_TYPE_NON_FLEET":     2,
		"PROGRAM_TYPE_BUSINESS_AUTO": 3,
	}
)

func (x ProgramType) Enum() *ProgramType {
	p := new(ProgramType)
	*p = x
	return p
}

func (x ProgramType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProgramType) Descriptor() protoreflect.EnumDescriptor {
	return file_account_v1_model_account_proto_enumTypes[2].Descriptor()
}

func (ProgramType) Type() protoreflect.EnumType {
	return &file_account_v1_model_account_proto_enumTypes[2]
}

func (x ProgramType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProgramType.Descriptor instead.
func (ProgramType) EnumDescriptor() ([]byte, []int) {
	return file_account_v1_model_account_proto_rawDescGZIP(), []int{2}
}

type ContactRole int32

const (
	ContactRole_CONTACT_ROLE_UNSPECIFIED ContactRole = 0
	ContactRole_CONTACT_ROLE_PRIMARY     ContactRole = 1
)

// Enum value maps for ContactRole.
var (
	ContactRole_name = map[int32]string{
		0: "CONTACT_ROLE_UNSPECIFIED",
		1: "CONTACT_ROLE_PRIMARY",
	}
	ContactRole_value = map[string]int32{
		"CONTACT_ROLE_UNSPECIFIED": 0,
		"CONTACT_ROLE_PRIMARY":     1,
	}
)

func (x ContactRole) Enum() *ContactRole {
	p := new(ContactRole)
	*p = x
	return p
}

func (x ContactRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContactRole) Descriptor() protoreflect.EnumDescriptor {
	return file_account_v1_model_account_proto_enumTypes[3].Descriptor()
}

func (ContactRole) Type() protoreflect.EnumType {
	return &file_account_v1_model_account_proto_enumTypes[3]
}

func (x ContactRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContactRole.Descriptor instead.
func (ContactRole) EnumDescriptor() ([]byte, []int) {
	return file_account_v1_model_account_proto_rawDescGZIP(), []int{3}
}

type AccountStatus int32

const (
	AccountStatus_ACCOUNT_STATUS_UNSPECIFIED AccountStatus = 0
	AccountStatus_ACCOUNT_STATUS_ACTIVE      AccountStatus = 1
	AccountStatus_ACCOUNT_STATUS_INACTIVE    AccountStatus = 2
	AccountStatus_ACCOUNT_STATUS_SUSPENDED   AccountStatus = 3
	AccountStatus_ACCOUNT_STATUS_ARCHIVED    AccountStatus = 4
)

// Enum value maps for AccountStatus.
var (
	AccountStatus_name = map[int32]string{
		0: "ACCOUNT_STATUS_UNSPECIFIED",
		1: "ACCOUNT_STATUS_ACTIVE",
		2: "ACCOUNT_STATUS_INACTIVE",
		3: "ACCOUNT_STATUS_SUSPENDED",
		4: "ACCOUNT_STATUS_ARCHIVED",
	}
	AccountStatus_value = map[string]int32{
		"ACCOUNT_STATUS_UNSPECIFIED": 0,
		"ACCOUNT_STATUS_ACTIVE":      1,
		"ACCOUNT_STATUS_INACTIVE":    2,
		"ACCOUNT_STATUS_SUSPENDED":   3,
		"ACCOUNT_STATUS_ARCHIVED":    4,
	}
)

func (x AccountStatus) Enum() *AccountStatus {
	p := new(AccountStatus)
	*p = x
	return p
}

func (x AccountStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_account_v1_model_account_proto_enumTypes[4].Descriptor()
}

func (AccountStatus) Type() protoreflect.EnumType {
	return &file_account_v1_model_account_proto_enumTypes[4]
}

func (x AccountStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountStatus.Descriptor instead.
func (AccountStatus) EnumDescriptor() ([]byte, []int) {
	return file_account_v1_model_account_proto_rawDescGZIP(), []int{4}
}

type PossibleMatchStatus int32

const (
	PossibleMatchStatus_POSSIBLE_MATCH_STATUS_UNSPECIFIED PossibleMatchStatus = 0
	PossibleMatchStatus_POSSIBLE_MATCH_STATUS_PENDING     PossibleMatchStatus = 1
	PossibleMatchStatus_POSSIBLE_MATCH_STATUS_REVIEWED    PossibleMatchStatus = 2
	PossibleMatchStatus_POSSIBLE_MATCH_STATUS_DISMISSED   PossibleMatchStatus = 3
	PossibleMatchStatus_POSSIBLE_MATCH_STATUS_MERGED      PossibleMatchStatus = 4
)

// Enum value maps for PossibleMatchStatus.
var (
	PossibleMatchStatus_name = map[int32]string{
		0: "POSSIBLE_MATCH_STATUS_UNSPECIFIED",
		1: "POSSIBLE_MATCH_STATUS_PENDING",
		2: "POSSIBLE_MATCH_STATUS_REVIEWED",
		3: "POSSIBLE_MATCH_STATUS_DISMISSED",
		4: "POSSIBLE_MATCH_STATUS_MERGED",
	}
	PossibleMatchStatus_value = map[string]int32{
		"POSSIBLE_MATCH_STATUS_UNSPECIFIED": 0,
		"POSSIBLE_MATCH_STATUS_PENDING":     1,
		"POSSIBLE_MATCH_STATUS_REVIEWED":    2,
		"POSSIBLE_MATCH_STATUS_DISMISSED":   3,
		"POSSIBLE_MATCH_STATUS_MERGED":      4,
	}
)

func (x PossibleMatchStatus) Enum() *PossibleMatchStatus {
	p := new(PossibleMatchStatus)
	*p = x
	return p
}

func (x PossibleMatchStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PossibleMatchStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_account_v1_model_account_proto_enumTypes[5].Descriptor()
}

func (PossibleMatchStatus) Type() protoreflect.EnumType {
	return &file_account_v1_model_account_proto_enumTypes[5]
}

func (x PossibleMatchStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PossibleMatchStatus.Descriptor instead.
func (PossibleMatchStatus) EnumDescriptor() ([]byte, []int) {
	return file_account_v1_model_account_proto_rawDescGZIP(), []int{5}
}

type MatchMethod int32

const (
	MatchMethod_MATCH_METHOD_UNSPECIFIED   MatchMethod = 0
	MatchMethod_MATCH_METHOD_EXACT_ID      MatchMethod = 1
	MatchMethod_MATCH_METHOD_FUZZY_PG_TRGM MatchMethod = 2
	MatchMethod_MATCH_METHOD_MANUAL        MatchMethod = 3
)

// Enum value maps for MatchMethod.
var (
	MatchMethod_name = map[int32]string{
		0: "MATCH_METHOD_UNSPECIFIED",
		1: "MATCH_METHOD_EXACT_ID",
		2: "MATCH_METHOD_FUZZY_PG_TRGM",
		3: "MATCH_METHOD_MANUAL",
	}
	MatchMethod_value = map[string]int32{
		"MATCH_METHOD_UNSPECIFIED":   0,
		"MATCH_METHOD_EXACT_ID":      1,
		"MATCH_METHOD_FUZZY_PG_TRGM": 2,
		"MATCH_METHOD_MANUAL":        3,
	}
)

func (x MatchMethod) Enum() *MatchMethod {
	p := new(MatchMethod)
	*p = x
	return p
}

func (x MatchMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MatchMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_account_v1_model_account_proto_enumTypes[6].Descriptor()
}

func (MatchMethod) Type() protoreflect.EnumType {
	return &file_account_v1_model_account_proto_enumTypes[6]
}

func (x MatchMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MatchMethod.Descriptor instead.
func (MatchMethod) EnumDescriptor() ([]byte, []int) {
	return file_account_v1_model_account_proto_rawDescGZIP(), []int{6}
}

type Identifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  IdentifierType `protobuf:"varint,1,opt,name=type,proto3,enum=account.v1.IdentifierType" json:"type,omitempty"`
	Value string         `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Identifier) Reset() {
	*x = Identifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_model_account_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Identifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Identifier) ProtoMessage() {}

func (x *Identifier) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_model_account_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Identifier.ProtoReflect.Descriptor instead.
func (*Identifier) Descriptor() ([]byte, []int) {
	return file_account_v1_model_account_proto_rawDescGZIP(), []int{0}
}

func (x *Identifier) GetType() IdentifierType {
	if x != nil {
		return x.Type
	}
	return IdentifierType_IDENTIFIER_TYPE_UNSPECIFIED
}

func (x *Identifier) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type AccountContact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstName string      `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName  string      `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Title     string      `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Phone     string      `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	Email     string      `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	Role      ContactRole `protobuf:"varint,6,opt,name=role,proto3,enum=account.v1.ContactRole" json:"role,omitempty"`
}

func (x *AccountContact) Reset() {
	*x = AccountContact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_model_account_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountContact) ProtoMessage() {}

func (x *AccountContact) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_model_account_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountContact.ProtoReflect.Descriptor instead.
func (*AccountContact) Descriptor() ([]byte, []int) {
	return file_account_v1_model_account_proto_rawDescGZIP(), []int{1}
}

func (x *AccountContact) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *AccountContact) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *AccountContact) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AccountContact) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *AccountContact) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AccountContact) GetRole() ContactRole {
	if x != nil {
		return x.Role
	}
	return ContactRole_CONTACT_ROLE_UNSPECIFIED
}

type Account struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	AccountType  AccountType            `protobuf:"varint,2,opt,name=account_type,json=accountType,proto3,enum=account.v1.AccountType" json:"account_type,omitempty"`
	Name         string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Identifiers  []*Identifier          `protobuf:"bytes,4,rep,name=identifiers,proto3" json:"identifiers,omitempty"`
	Contacts     []*AccountContact      `protobuf:"bytes,5,rep,name=contacts,proto3" json:"contacts,omitempty"`
	Address      *proto.Address         `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	ProgramTypes []ProgramType          `protobuf:"varint,7,rep,packed,name=program_types,json=programTypes,proto3,enum=account.v1.ProgramType" json:"program_types,omitempty"`
	CreatedAt    *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt    *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *Account) Reset() {
	*x = Account{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_model_account_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_model_account_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_account_v1_model_account_proto_rawDescGZIP(), []int{2}
}

func (x *Account) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Account) GetAccountType() AccountType {
	if x != nil {
		return x.AccountType
	}
	return AccountType_ACCOUNT_TYPE_UNSPECIFIED
}

func (x *Account) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Account) GetIdentifiers() []*Identifier {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

func (x *Account) GetContacts() []*AccountContact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *Account) GetAddress() *proto.Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *Account) GetProgramTypes() []ProgramType {
	if x != nil {
		return x.ProgramTypes
	}
	return nil
}

func (x *Account) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Account) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AccountSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *AccountSummary) Reset() {
	*x = AccountSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_model_account_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSummary) ProtoMessage() {}

func (x *AccountSummary) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_model_account_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSummary.ProtoReflect.Descriptor instead.
func (*AccountSummary) Descriptor() ([]byte, []int) {
	return file_account_v1_model_account_proto_rawDescGZIP(), []int{3}
}

func (x *AccountSummary) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AccountSummary) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_account_v1_model_account_proto protoreflect.FileDescriptor

var file_account_v1_model_account_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x52, 0x0a, 0x0a, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xbb, 0x01, 0x0a, 0x0e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c,
	0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2b, 0x0a, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x6f, 0x6c, 0x65,
	0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0xba, 0x03, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x3a, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52,
	0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x08,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x73, 0x12, 0x29, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x3c, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0c, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0x34, 0x0a, 0x0e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x2a, 0x63, 0x0a, 0x0b, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x10,
	0x01, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x56, 0x49, 0x44, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x2a, 0x64,
	0x0a, 0x0e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4f, 0x54, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x45,
	0x49, 0x4e, 0x10, 0x02, 0x2a, 0x7f, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x46, 0x4c, 0x45, 0x45, 0x54, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x41, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x46, 0x4c,
	0x45, 0x45, 0x54, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41,
	0x55, 0x54, 0x4f, 0x10, 0x03, 0x2a, 0x45, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f,
	0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x5f, 0x52, 0x4f,
	0x4c, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x01, 0x2a, 0xa2, 0x01, 0x0a,
	0x0d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e,
	0x0a, 0x1a, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19,
	0x0a, 0x15, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x45, 0x4e, 0x44,
	0x45, 0x44, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x52, 0x43, 0x48, 0x49, 0x56, 0x45, 0x44, 0x10,
	0x04, 0x2a, 0xca, 0x01, 0x0a, 0x13, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x4f, 0x53,
	0x53, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x21, 0x0a, 0x1d, 0x50, 0x4f, 0x53, 0x53, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x4f, 0x53, 0x53, 0x49, 0x42, 0x4c, 0x45, 0x5f,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x56,
	0x49, 0x45, 0x57, 0x45, 0x44, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x4f, 0x53, 0x53, 0x49,
	0x42, 0x4c, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x44, 0x49, 0x53, 0x4d, 0x49, 0x53, 0x53, 0x45, 0x44, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c,
	0x50, 0x4f, 0x53, 0x53, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x45, 0x52, 0x47, 0x45, 0x44, 0x10, 0x04, 0x2a, 0x7f,
	0x0a, 0x0b, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1c, 0x0a,
	0x18, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x4d,
	0x41, 0x54, 0x43, 0x48, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x45, 0x58, 0x41, 0x43,
	0x54, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x46, 0x55, 0x5a, 0x5a, 0x59, 0x5f, 0x50, 0x47, 0x5f,
	0x54, 0x52, 0x47, 0x4d, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x03, 0x42,
	0x2e, 0x5a, 0x2c, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x76, 0x31, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_account_v1_model_account_proto_rawDescOnce sync.Once
	file_account_v1_model_account_proto_rawDescData = file_account_v1_model_account_proto_rawDesc
)

func file_account_v1_model_account_proto_rawDescGZIP() []byte {
	file_account_v1_model_account_proto_rawDescOnce.Do(func() {
		file_account_v1_model_account_proto_rawDescData = protoimpl.X.CompressGZIP(file_account_v1_model_account_proto_rawDescData)
	})
	return file_account_v1_model_account_proto_rawDescData
}

var file_account_v1_model_account_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_account_v1_model_account_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_account_v1_model_account_proto_goTypes = []interface{}{
	(AccountType)(0),              // 0: account.v1.AccountType
	(IdentifierType)(0),           // 1: account.v1.IdentifierType
	(ProgramType)(0),              // 2: account.v1.ProgramType
	(ContactRole)(0),              // 3: account.v1.ContactRole
	(AccountStatus)(0),            // 4: account.v1.AccountStatus
	(PossibleMatchStatus)(0),      // 5: account.v1.PossibleMatchStatus
	(MatchMethod)(0),              // 6: account.v1.MatchMethod
	(*Identifier)(nil),            // 7: account.v1.Identifier
	(*AccountContact)(nil),        // 8: account.v1.AccountContact
	(*Account)(nil),               // 9: account.v1.Account
	(*AccountSummary)(nil),        // 10: account.v1.AccountSummary
	(*proto.Address)(nil),         // 11: common.Address
	(*timestamppb.Timestamp)(nil), // 12: google.protobuf.Timestamp
}
var file_account_v1_model_account_proto_depIdxs = []int32{
	1,  // 0: account.v1.Identifier.type:type_name -> account.v1.IdentifierType
	3,  // 1: account.v1.AccountContact.role:type_name -> account.v1.ContactRole
	0,  // 2: account.v1.Account.account_type:type_name -> account.v1.AccountType
	7,  // 3: account.v1.Account.identifiers:type_name -> account.v1.Identifier
	8,  // 4: account.v1.Account.contacts:type_name -> account.v1.AccountContact
	11, // 5: account.v1.Account.address:type_name -> common.Address
	2,  // 6: account.v1.Account.program_types:type_name -> account.v1.ProgramType
	12, // 7: account.v1.Account.created_at:type_name -> google.protobuf.Timestamp
	12, // 8: account.v1.Account.updated_at:type_name -> google.protobuf.Timestamp
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_account_v1_model_account_proto_init() }
func file_account_v1_model_account_proto_init() {
	if File_account_v1_model_account_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_account_v1_model_account_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Identifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_model_account_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountContact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_model_account_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Account); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_model_account_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_account_v1_model_account_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_account_v1_model_account_proto_goTypes,
		DependencyIndexes: file_account_v1_model_account_proto_depIdxs,
		EnumInfos:         file_account_v1_model_account_proto_enumTypes,
		MessageInfos:      file_account_v1_model_account_proto_msgTypes,
	}.Build()
	File_account_v1_model_account_proto = out.File
	file_account_v1_model_account_proto_rawDesc = nil
	file_account_v1_model_account_proto_goTypes = nil
	file_account_v1_model_account_proto_depIdxs = nil
}
