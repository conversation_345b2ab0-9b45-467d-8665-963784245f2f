// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: account/v1/account_service.proto

package accountv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	model "nirvanatech.com/nirvana/account/v1/model"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MatchOrCreateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Candidate           *model.Account `protobuf:"bytes,1,opt,name=candidate,proto3" json:"candidate,omitempty"`
	AutoPickStrongMatch bool           `protobuf:"varint,2,opt,name=auto_pick_strong_match,json=autoPickStrongMatch,proto3" json:"auto_pick_strong_match,omitempty"`
}

func (x *MatchOrCreateRequest) Reset() {
	*x = MatchOrCreateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_account_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchOrCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchOrCreateRequest) ProtoMessage() {}

func (x *MatchOrCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_account_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchOrCreateRequest.ProtoReflect.Descriptor instead.
func (*MatchOrCreateRequest) Descriptor() ([]byte, []int) {
	return file_account_v1_account_service_proto_rawDescGZIP(), []int{0}
}

func (x *MatchOrCreateRequest) GetCandidate() *model.Account {
	if x != nil {
		return x.Candidate
	}
	return nil
}

func (x *MatchOrCreateRequest) GetAutoPickStrongMatch() bool {
	if x != nil {
		return x.AutoPickStrongMatch
	}
	return false
}

type NearMatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account *model.AccountSummary `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Score   float64               `protobuf:"fixed64,2,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *NearMatch) Reset() {
	*x = NearMatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_account_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NearMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NearMatch) ProtoMessage() {}

func (x *NearMatch) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_account_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NearMatch.ProtoReflect.Descriptor instead.
func (*NearMatch) Descriptor() ([]byte, []int) {
	return file_account_v1_account_service_proto_rawDescGZIP(), []int{1}
}

func (x *NearMatch) GetAccount() *model.AccountSummary {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *NearMatch) GetScore() float64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type MatchOrCreateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId   string       `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	NearMatches []*NearMatch `protobuf:"bytes,2,rep,name=near_matches,json=nearMatches,proto3" json:"near_matches,omitempty"`
	Created     bool         `protobuf:"varint,3,opt,name=created,proto3" json:"created,omitempty"`
}

func (x *MatchOrCreateResponse) Reset() {
	*x = MatchOrCreateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_account_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchOrCreateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchOrCreateResponse) ProtoMessage() {}

func (x *MatchOrCreateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_account_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchOrCreateResponse.ProtoReflect.Descriptor instead.
func (*MatchOrCreateResponse) Descriptor() ([]byte, []int) {
	return file_account_v1_account_service_proto_rawDescGZIP(), []int{2}
}

func (x *MatchOrCreateResponse) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *MatchOrCreateResponse) GetNearMatches() []*NearMatch {
	if x != nil {
		return x.NearMatches
	}
	return nil
}

func (x *MatchOrCreateResponse) GetCreated() bool {
	if x != nil {
		return x.Created
	}
	return false
}

type MergeAccountsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetAccountId string `protobuf:"bytes,1,opt,name=target_account_id,json=targetAccountId,proto3" json:"target_account_id,omitempty"`
	SourceAccountId string `protobuf:"bytes,2,opt,name=source_account_id,json=sourceAccountId,proto3" json:"source_account_id,omitempty"`
	MergedBy        string `protobuf:"bytes,3,opt,name=merged_by,json=mergedBy,proto3" json:"merged_by,omitempty"`
}

func (x *MergeAccountsRequest) Reset() {
	*x = MergeAccountsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_account_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeAccountsRequest) ProtoMessage() {}

func (x *MergeAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_account_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeAccountsRequest.ProtoReflect.Descriptor instead.
func (*MergeAccountsRequest) Descriptor() ([]byte, []int) {
	return file_account_v1_account_service_proto_rawDescGZIP(), []int{3}
}

func (x *MergeAccountsRequest) GetTargetAccountId() string {
	if x != nil {
		return x.TargetAccountId
	}
	return ""
}

func (x *MergeAccountsRequest) GetSourceAccountId() string {
	if x != nil {
		return x.SourceAccountId
	}
	return ""
}

func (x *MergeAccountsRequest) GetMergedBy() string {
	if x != nil {
		return x.MergedBy
	}
	return ""
}

type MergeAccountsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MergeAccountsResponse) Reset() {
	*x = MergeAccountsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_account_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeAccountsResponse) ProtoMessage() {}

func (x *MergeAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_account_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeAccountsResponse.ProtoReflect.Descriptor instead.
func (*MergeAccountsResponse) Descriptor() ([]byte, []int) {
	return file_account_v1_account_service_proto_rawDescGZIP(), []int{4}
}

type PaginationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize int32  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Cursor   string `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
}

func (x *PaginationParams) Reset() {
	*x = PaginationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_account_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaginationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationParams) ProtoMessage() {}

func (x *PaginationParams) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_account_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationParams.ProtoReflect.Descriptor instead.
func (*PaginationParams) Descriptor() ([]byte, []int) {
	return file_account_v1_account_service_proto_rawDescGZIP(), []int{5}
}

func (x *PaginationParams) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PaginationParams) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

type ListPossibleMatchesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId        string            `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	PaginationParams *PaginationParams `protobuf:"bytes,2,opt,name=pagination_params,json=paginationParams,proto3" json:"pagination_params,omitempty"`
}

func (x *ListPossibleMatchesRequest) Reset() {
	*x = ListPossibleMatchesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_account_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPossibleMatchesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPossibleMatchesRequest) ProtoMessage() {}

func (x *ListPossibleMatchesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_account_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPossibleMatchesRequest.ProtoReflect.Descriptor instead.
func (*ListPossibleMatchesRequest) Descriptor() ([]byte, []int) {
	return file_account_v1_account_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListPossibleMatchesRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *ListPossibleMatchesRequest) GetPaginationParams() *PaginationParams {
	if x != nil {
		return x.PaginationParams
	}
	return nil
}

type PossibleMatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Candidate          *model.AccountSummary     `protobuf:"bytes,1,opt,name=candidate,proto3" json:"candidate,omitempty"`
	Matched            *model.AccountSummary     `protobuf:"bytes,2,opt,name=matched,proto3" json:"matched,omitempty"`
	MatchConfidence    float64                   `protobuf:"fixed64,3,opt,name=match_confidence,json=matchConfidence,proto3" json:"match_confidence,omitempty"`
	MatchMethod        string                    `protobuf:"bytes,4,opt,name=match_method,json=matchMethod,proto3" json:"match_method,omitempty"`
	IdentifiersMatched []string                  `protobuf:"bytes,5,rep,name=identifiers_matched,json=identifiersMatched,proto3" json:"identifiers_matched,omitempty"`
	Status             model.PossibleMatchStatus `protobuf:"varint,6,opt,name=status,proto3,enum=account.v1.PossibleMatchStatus" json:"status,omitempty"`
	ReviewedAt         *timestamppb.Timestamp    `protobuf:"bytes,7,opt,name=reviewed_at,json=reviewedAt,proto3" json:"reviewed_at,omitempty"`
	ReviewedBy         string                    `protobuf:"bytes,8,opt,name=reviewed_by,json=reviewedBy,proto3" json:"reviewed_by,omitempty"`
}

func (x *PossibleMatch) Reset() {
	*x = PossibleMatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_account_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PossibleMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PossibleMatch) ProtoMessage() {}

func (x *PossibleMatch) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_account_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PossibleMatch.ProtoReflect.Descriptor instead.
func (*PossibleMatch) Descriptor() ([]byte, []int) {
	return file_account_v1_account_service_proto_rawDescGZIP(), []int{7}
}

func (x *PossibleMatch) GetCandidate() *model.AccountSummary {
	if x != nil {
		return x.Candidate
	}
	return nil
}

func (x *PossibleMatch) GetMatched() *model.AccountSummary {
	if x != nil {
		return x.Matched
	}
	return nil
}

func (x *PossibleMatch) GetMatchConfidence() float64 {
	if x != nil {
		return x.MatchConfidence
	}
	return 0
}

func (x *PossibleMatch) GetMatchMethod() string {
	if x != nil {
		return x.MatchMethod
	}
	return ""
}

func (x *PossibleMatch) GetIdentifiersMatched() []string {
	if x != nil {
		return x.IdentifiersMatched
	}
	return nil
}

func (x *PossibleMatch) GetStatus() model.PossibleMatchStatus {
	if x != nil {
		return x.Status
	}
	return model.PossibleMatchStatus(0)
}

func (x *PossibleMatch) GetReviewedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ReviewedAt
	}
	return nil
}

func (x *PossibleMatch) GetReviewedBy() string {
	if x != nil {
		return x.ReviewedBy
	}
	return ""
}

type ListPossibleMatchesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items      []*PossibleMatch `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	NextCursor string           `protobuf:"bytes,2,opt,name=next_cursor,json=nextCursor,proto3" json:"next_cursor,omitempty"`
}

func (x *ListPossibleMatchesResponse) Reset() {
	*x = ListPossibleMatchesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_account_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPossibleMatchesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPossibleMatchesResponse) ProtoMessage() {}

func (x *ListPossibleMatchesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_account_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPossibleMatchesResponse.ProtoReflect.Descriptor instead.
func (*ListPossibleMatchesResponse) Descriptor() ([]byte, []int) {
	return file_account_v1_account_service_proto_rawDescGZIP(), []int{8}
}

func (x *ListPossibleMatchesResponse) GetItems() []*PossibleMatch {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListPossibleMatchesResponse) GetNextCursor() string {
	if x != nil {
		return x.NextCursor
	}
	return ""
}

type DeenrollProgramRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId   string            `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	ProgramType model.ProgramType `protobuf:"varint,2,opt,name=program_type,json=programType,proto3,enum=account.v1.ProgramType" json:"program_type,omitempty"`
}

func (x *DeenrollProgramRequest) Reset() {
	*x = DeenrollProgramRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_account_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeenrollProgramRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeenrollProgramRequest) ProtoMessage() {}

func (x *DeenrollProgramRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_account_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeenrollProgramRequest.ProtoReflect.Descriptor instead.
func (*DeenrollProgramRequest) Descriptor() ([]byte, []int) {
	return file_account_v1_account_service_proto_rawDescGZIP(), []int{9}
}

func (x *DeenrollProgramRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DeenrollProgramRequest) GetProgramType() model.ProgramType {
	if x != nil {
		return x.ProgramType
	}
	return model.ProgramType(0)
}

type DeenrollProgramResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeenrollProgramResponse) Reset() {
	*x = DeenrollProgramResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_v1_account_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeenrollProgramResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeenrollProgramResponse) ProtoMessage() {}

func (x *DeenrollProgramResponse) ProtoReflect() protoreflect.Message {
	mi := &file_account_v1_account_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeenrollProgramResponse.ProtoReflect.Descriptor instead.
func (*DeenrollProgramResponse) Descriptor() ([]byte, []int) {
	return file_account_v1_account_service_proto_rawDescGZIP(), []int{10}
}

var File_account_v1_account_service_proto protoreflect.FileDescriptor

var file_account_v1_account_service_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x7e, 0x0a, 0x14, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x09, 0x63, 0x61, 0x6e, 0x64, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x09, 0x63, 0x61, 0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x16, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x72, 0x6f, 0x6e, 0x67, 0x5f, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x61, 0x75, 0x74, 0x6f,
	0x50, 0x69, 0x63, 0x6b, 0x53, 0x74, 0x72, 0x6f, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x22,
	0x57, 0x0a, 0x09, 0x4e, 0x65, 0x61, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x34, 0x0a, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x15, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x38, 0x0a, 0x0c, 0x6e, 0x65, 0x61, 0x72, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x61, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x0b,
	0x6e, 0x65, 0x61, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x22, 0x8b, 0x01, 0x0a, 0x14, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a,
	0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x64, 0x42, 0x79, 0x22, 0x17, 0x0a, 0x15, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x47, 0x0a, 0x10,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0x86, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f,
	0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x11, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x10, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x95,
	0x03, 0x0a, 0x0d, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x12, 0x38, 0x0a, 0x09, 0x63, 0x61, 0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x09, 0x63, 0x61, 0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64,
	0x12, 0x29, 0x0a, 0x10, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x2f,
	0x0a, 0x13, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x5f, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x12,
	0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x73,
	0x73, 0x69, 0x62, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x65, 0x64, 0x42, 0x79, 0x22, 0x6f, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f,
	0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x63,
	0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x65, 0x78,
	0x74, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0x73, 0x0a, 0x16, 0x44, 0x65, 0x65, 0x6e, 0x72,
	0x6f, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x19, 0x0a, 0x17,
	0x44, 0x65, 0x65, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x80, 0x03, 0x0a, 0x0e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x54, 0x0a, 0x0d, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x20, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x72,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x54, 0x0a, 0x0d, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x12, 0x20, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f,
	0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x12, 0x26, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a,
	0x0a, 0x0f, 0x44, 0x65, 0x65, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x12, 0x22, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x65, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x65, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x2e, 0x5a, 0x2c, 0x6e, 0x69,
	0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69,
	0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_account_v1_account_service_proto_rawDescOnce sync.Once
	file_account_v1_account_service_proto_rawDescData = file_account_v1_account_service_proto_rawDesc
)

func file_account_v1_account_service_proto_rawDescGZIP() []byte {
	file_account_v1_account_service_proto_rawDescOnce.Do(func() {
		file_account_v1_account_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_account_v1_account_service_proto_rawDescData)
	})
	return file_account_v1_account_service_proto_rawDescData
}

var file_account_v1_account_service_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_account_v1_account_service_proto_goTypes = []interface{}{
	(*MatchOrCreateRequest)(nil),        // 0: account.v1.MatchOrCreateRequest
	(*NearMatch)(nil),                   // 1: account.v1.NearMatch
	(*MatchOrCreateResponse)(nil),       // 2: account.v1.MatchOrCreateResponse
	(*MergeAccountsRequest)(nil),        // 3: account.v1.MergeAccountsRequest
	(*MergeAccountsResponse)(nil),       // 4: account.v1.MergeAccountsResponse
	(*PaginationParams)(nil),            // 5: account.v1.PaginationParams
	(*ListPossibleMatchesRequest)(nil),  // 6: account.v1.ListPossibleMatchesRequest
	(*PossibleMatch)(nil),               // 7: account.v1.PossibleMatch
	(*ListPossibleMatchesResponse)(nil), // 8: account.v1.ListPossibleMatchesResponse
	(*DeenrollProgramRequest)(nil),      // 9: account.v1.DeenrollProgramRequest
	(*DeenrollProgramResponse)(nil),     // 10: account.v1.DeenrollProgramResponse
	(*model.Account)(nil),               // 11: account.v1.Account
	(*model.AccountSummary)(nil),        // 12: account.v1.AccountSummary
	(model.PossibleMatchStatus)(0),      // 13: account.v1.PossibleMatchStatus
	(*timestamppb.Timestamp)(nil),       // 14: google.protobuf.Timestamp
	(model.ProgramType)(0),              // 15: account.v1.ProgramType
}
var file_account_v1_account_service_proto_depIdxs = []int32{
	11, // 0: account.v1.MatchOrCreateRequest.candidate:type_name -> account.v1.Account
	12, // 1: account.v1.NearMatch.account:type_name -> account.v1.AccountSummary
	1,  // 2: account.v1.MatchOrCreateResponse.near_matches:type_name -> account.v1.NearMatch
	5,  // 3: account.v1.ListPossibleMatchesRequest.pagination_params:type_name -> account.v1.PaginationParams
	12, // 4: account.v1.PossibleMatch.candidate:type_name -> account.v1.AccountSummary
	12, // 5: account.v1.PossibleMatch.matched:type_name -> account.v1.AccountSummary
	13, // 6: account.v1.PossibleMatch.status:type_name -> account.v1.PossibleMatchStatus
	14, // 7: account.v1.PossibleMatch.reviewed_at:type_name -> google.protobuf.Timestamp
	7,  // 8: account.v1.ListPossibleMatchesResponse.items:type_name -> account.v1.PossibleMatch
	15, // 9: account.v1.DeenrollProgramRequest.program_type:type_name -> account.v1.ProgramType
	0,  // 10: account.v1.AccountService.MatchOrCreate:input_type -> account.v1.MatchOrCreateRequest
	3,  // 11: account.v1.AccountService.MergeAccounts:input_type -> account.v1.MergeAccountsRequest
	6,  // 12: account.v1.AccountService.ListPossibleMatches:input_type -> account.v1.ListPossibleMatchesRequest
	9,  // 13: account.v1.AccountService.DeenrollProgram:input_type -> account.v1.DeenrollProgramRequest
	2,  // 14: account.v1.AccountService.MatchOrCreate:output_type -> account.v1.MatchOrCreateResponse
	4,  // 15: account.v1.AccountService.MergeAccounts:output_type -> account.v1.MergeAccountsResponse
	8,  // 16: account.v1.AccountService.ListPossibleMatches:output_type -> account.v1.ListPossibleMatchesResponse
	10, // 17: account.v1.AccountService.DeenrollProgram:output_type -> account.v1.DeenrollProgramResponse
	14, // [14:18] is the sub-list for method output_type
	10, // [10:14] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_account_v1_account_service_proto_init() }
func file_account_v1_account_service_proto_init() {
	if File_account_v1_account_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_account_v1_account_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchOrCreateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_account_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NearMatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_account_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchOrCreateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_account_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeAccountsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_account_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeAccountsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_account_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaginationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_account_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPossibleMatchesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_account_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PossibleMatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_account_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPossibleMatchesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_account_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeenrollProgramRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_v1_account_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeenrollProgramResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_account_v1_account_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_account_v1_account_service_proto_goTypes,
		DependencyIndexes: file_account_v1_account_service_proto_depIdxs,
		MessageInfos:      file_account_v1_account_service_proto_msgTypes,
	}.Build()
	File_account_v1_account_service_proto = out.File
	file_account_v1_account_service_proto_rawDesc = nil
	file_account_v1_account_service_proto_goTypes = nil
	file_account_v1_account_service_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// AccountServiceClient is the client API for AccountService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AccountServiceClient interface {
	MatchOrCreate(ctx context.Context, in *MatchOrCreateRequest, opts ...grpc.CallOption) (*MatchOrCreateResponse, error)
	MergeAccounts(ctx context.Context, in *MergeAccountsRequest, opts ...grpc.CallOption) (*MergeAccountsResponse, error)
	ListPossibleMatches(ctx context.Context, in *ListPossibleMatchesRequest, opts ...grpc.CallOption) (*ListPossibleMatchesResponse, error)
	DeenrollProgram(ctx context.Context, in *DeenrollProgramRequest, opts ...grpc.CallOption) (*DeenrollProgramResponse, error)
}

type accountServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountServiceClient(cc grpc.ClientConnInterface) AccountServiceClient {
	return &accountServiceClient{cc}
}

func (c *accountServiceClient) MatchOrCreate(ctx context.Context, in *MatchOrCreateRequest, opts ...grpc.CallOption) (*MatchOrCreateResponse, error) {
	out := new(MatchOrCreateResponse)
	err := c.cc.Invoke(ctx, "/account.v1.AccountService/MatchOrCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) MergeAccounts(ctx context.Context, in *MergeAccountsRequest, opts ...grpc.CallOption) (*MergeAccountsResponse, error) {
	out := new(MergeAccountsResponse)
	err := c.cc.Invoke(ctx, "/account.v1.AccountService/MergeAccounts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) ListPossibleMatches(ctx context.Context, in *ListPossibleMatchesRequest, opts ...grpc.CallOption) (*ListPossibleMatchesResponse, error) {
	out := new(ListPossibleMatchesResponse)
	err := c.cc.Invoke(ctx, "/account.v1.AccountService/ListPossibleMatches", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) DeenrollProgram(ctx context.Context, in *DeenrollProgramRequest, opts ...grpc.CallOption) (*DeenrollProgramResponse, error) {
	out := new(DeenrollProgramResponse)
	err := c.cc.Invoke(ctx, "/account.v1.AccountService/DeenrollProgram", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountServiceServer is the server API for AccountService service.
type AccountServiceServer interface {
	MatchOrCreate(context.Context, *MatchOrCreateRequest) (*MatchOrCreateResponse, error)
	MergeAccounts(context.Context, *MergeAccountsRequest) (*MergeAccountsResponse, error)
	ListPossibleMatches(context.Context, *ListPossibleMatchesRequest) (*ListPossibleMatchesResponse, error)
	DeenrollProgram(context.Context, *DeenrollProgramRequest) (*DeenrollProgramResponse, error)
}

// UnimplementedAccountServiceServer can be embedded to have forward compatible implementations.
type UnimplementedAccountServiceServer struct {
}

func (*UnimplementedAccountServiceServer) MatchOrCreate(context.Context, *MatchOrCreateRequest) (*MatchOrCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MatchOrCreate not implemented")
}
func (*UnimplementedAccountServiceServer) MergeAccounts(context.Context, *MergeAccountsRequest) (*MergeAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeAccounts not implemented")
}
func (*UnimplementedAccountServiceServer) ListPossibleMatches(context.Context, *ListPossibleMatchesRequest) (*ListPossibleMatchesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPossibleMatches not implemented")
}
func (*UnimplementedAccountServiceServer) DeenrollProgram(context.Context, *DeenrollProgramRequest) (*DeenrollProgramResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeenrollProgram not implemented")
}

func RegisterAccountServiceServer(s *grpc.Server, srv AccountServiceServer) {
	s.RegisterService(&_AccountService_serviceDesc, srv)
}

func _AccountService_MatchOrCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MatchOrCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).MatchOrCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account.v1.AccountService/MatchOrCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).MatchOrCreate(ctx, req.(*MatchOrCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_MergeAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).MergeAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account.v1.AccountService/MergeAccounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).MergeAccounts(ctx, req.(*MergeAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_ListPossibleMatches_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPossibleMatchesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).ListPossibleMatches(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account.v1.AccountService/ListPossibleMatches",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).ListPossibleMatches(ctx, req.(*ListPossibleMatchesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_DeenrollProgram_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeenrollProgramRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).DeenrollProgram(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/account.v1.AccountService/DeenrollProgram",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).DeenrollProgram(ctx, req.(*DeenrollProgramRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AccountService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "account.v1.AccountService",
	HandlerType: (*AccountServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MatchOrCreate",
			Handler:    _AccountService_MatchOrCreate_Handler,
		},
		{
			MethodName: "MergeAccounts",
			Handler:    _AccountService_MergeAccounts_Handler,
		},
		{
			MethodName: "ListPossibleMatches",
			Handler:    _AccountService_ListPossibleMatches_Handler,
		},
		{
			MethodName: "DeenrollProgram",
			Handler:    _AccountService_DeenrollProgram_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "account/v1/account_service.proto",
}
