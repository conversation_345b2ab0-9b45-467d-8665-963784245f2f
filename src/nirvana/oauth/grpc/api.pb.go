// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: oauth_manager/api.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GenerateRedirectURIRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId string `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	State    string `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *GenerateRedirectURIRequest) Reset() {
	*x = GenerateRedirectURIRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oauth_manager_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateRedirectURIRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateRedirectURIRequest) ProtoMessage() {}

func (x *GenerateRedirectURIRequest) ProtoReflect() protoreflect.Message {
	mi := &file_oauth_manager_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateRedirectURIRequest.ProtoReflect.Descriptor instead.
func (*GenerateRedirectURIRequest) Descriptor() ([]byte, []int) {
	return file_oauth_manager_api_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateRedirectURIRequest) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *GenerateRedirectURIRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type HandleId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *HandleId) Reset() {
	*x = HandleId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oauth_manager_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleId) ProtoMessage() {}

func (x *HandleId) ProtoReflect() protoreflect.Message {
	mi := &file_oauth_manager_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleId.ProtoReflect.Descriptor instead.
func (*HandleId) Descriptor() ([]byte, []int) {
	return file_oauth_manager_api_proto_rawDescGZIP(), []int{1}
}

func (x *HandleId) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type ActivateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId string `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	AuthCode string `protobuf:"bytes,2,opt,name=auth_code,json=authCode,proto3" json:"auth_code,omitempty"`
}

func (x *ActivateRequest) Reset() {
	*x = ActivateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oauth_manager_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateRequest) ProtoMessage() {}

func (x *ActivateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_oauth_manager_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateRequest.ProtoReflect.Descriptor instead.
func (*ActivateRequest) Descriptor() ([]byte, []int) {
	return file_oauth_manager_api_proto_rawDescGZIP(), []int{2}
}

func (x *ActivateRequest) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *ActivateRequest) GetAuthCode() string {
	if x != nil {
		return x.AuthCode
	}
	return ""
}

type OAuthConnectionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId    string                 `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	Provider    string                 `protobuf:"bytes,2,opt,name=provider,proto3" json:"provider,omitempty"`
	State       string                 `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	ActivatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=activated_at,json=activatedAt,proto3,oneof" json:"activated_at,omitempty"`
	DiscardedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=discarded_at,json=discardedAt,proto3,oneof" json:"discarded_at,omitempty"`
}

func (x *OAuthConnectionInfo) Reset() {
	*x = OAuthConnectionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oauth_manager_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OAuthConnectionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OAuthConnectionInfo) ProtoMessage() {}

func (x *OAuthConnectionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_oauth_manager_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OAuthConnectionInfo.ProtoReflect.Descriptor instead.
func (*OAuthConnectionInfo) Descriptor() ([]byte, []int) {
	return file_oauth_manager_api_proto_rawDescGZIP(), []int{3}
}

func (x *OAuthConnectionInfo) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *OAuthConnectionInfo) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *OAuthConnectionInfo) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *OAuthConnectionInfo) GetActivatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ActivatedAt
	}
	return nil
}

func (x *OAuthConnectionInfo) GetDiscardedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscardedAt
	}
	return nil
}

type GetBulkConnectionInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []*HandleId `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (x *GetBulkConnectionInfoRequest) Reset() {
	*x = GetBulkConnectionInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oauth_manager_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBulkConnectionInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBulkConnectionInfoRequest) ProtoMessage() {}

func (x *GetBulkConnectionInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_oauth_manager_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBulkConnectionInfoRequest.ProtoReflect.Descriptor instead.
func (*GetBulkConnectionInfoRequest) Descriptor() ([]byte, []int) {
	return file_oauth_manager_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetBulkConnectionInfoRequest) GetIds() []*HandleId {
	if x != nil {
		return x.Ids
	}
	return nil
}

type GetBulkConnectionInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Infos []*OAuthConnectionInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
}

func (x *GetBulkConnectionInfoResponse) Reset() {
	*x = GetBulkConnectionInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oauth_manager_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBulkConnectionInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBulkConnectionInfoResponse) ProtoMessage() {}

func (x *GetBulkConnectionInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_oauth_manager_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBulkConnectionInfoResponse.ProtoReflect.Descriptor instead.
func (*GetBulkConnectionInfoResponse) Descriptor() ([]byte, []int) {
	return file_oauth_manager_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetBulkConnectionInfoResponse) GetInfos() []*OAuthConnectionInfo {
	if x != nil {
		return x.Infos
	}
	return nil
}

type BeginOauthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId string `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	State    string `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *BeginOauthRequest) Reset() {
	*x = BeginOauthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oauth_manager_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BeginOauthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BeginOauthRequest) ProtoMessage() {}

func (x *BeginOauthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_oauth_manager_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BeginOauthRequest.ProtoReflect.Descriptor instead.
func (*BeginOauthRequest) Descriptor() ([]byte, []int) {
	return file_oauth_manager_api_proto_rawDescGZIP(), []int{6}
}

func (x *BeginOauthRequest) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *BeginOauthRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type FinalizeOauthConnectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthCode *string `protobuf:"bytes,1,opt,name=auth_code,json=authCode,proto3,oneof" json:"auth_code,omitempty"`
	State    *string `protobuf:"bytes,2,opt,name=state,proto3,oneof" json:"state,omitempty"`
	Scope    *string `protobuf:"bytes,3,opt,name=scope,proto3,oneof" json:"scope,omitempty"`
	Error    *string `protobuf:"bytes,4,opt,name=error,proto3,oneof" json:"error,omitempty"`
}

func (x *FinalizeOauthConnectionRequest) Reset() {
	*x = FinalizeOauthConnectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oauth_manager_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinalizeOauthConnectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinalizeOauthConnectionRequest) ProtoMessage() {}

func (x *FinalizeOauthConnectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_oauth_manager_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinalizeOauthConnectionRequest.ProtoReflect.Descriptor instead.
func (*FinalizeOauthConnectionRequest) Descriptor() ([]byte, []int) {
	return file_oauth_manager_api_proto_rawDescGZIP(), []int{7}
}

func (x *FinalizeOauthConnectionRequest) GetAuthCode() string {
	if x != nil && x.AuthCode != nil {
		return *x.AuthCode
	}
	return ""
}

func (x *FinalizeOauthConnectionRequest) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *FinalizeOauthConnectionRequest) GetScope() string {
	if x != nil && x.Scope != nil {
		return *x.Scope
	}
	return ""
}

func (x *FinalizeOauthConnectionRequest) GetError() string {
	if x != nil && x.Error != nil {
		return *x.Error
	}
	return ""
}

var File_oauth_manager_api_proto protoreflect.FileDescriptor

var file_oauth_manager_api_proto_rawDesc = []byte{
	0x0a, 0x17, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4f, 0x0a, 0x1a, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x52, 0x49, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x20, 0x0a, 0x08, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x4b, 0x0a, 0x0f, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61,
	0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x94, 0x02, 0x0a, 0x13, 0x4f, 0x41, 0x75, 0x74,
	0x68, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x42,
	0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x48, 0x00, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x42, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x61, 0x72, 0x64, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x63, 0x61, 0x72, 0x64, 0x65,
	0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x69, 0x73, 0x63,
	0x61, 0x72, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x4a, 0x04, 0x08, 0x06, 0x10, 0x07, 0x22, 0x3b,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x4b, 0x0a, 0x1d, 0x47,
	0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x05,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x4f, 0x41,
	0x75, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x46, 0x0a, 0x11, 0x42, 0x65, 0x67, 0x69,
	0x6e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x22, 0xbf, 0x01, 0x0a, 0x1e, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x4f, 0x61, 0x75,
	0x74, 0x68, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f,
	0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x19, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x32, 0xe6, 0x04, 0x0a, 0x0c, 0x4f, 0x41, 0x75, 0x74, 0x68, 0x4d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x13, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x52, 0x49, 0x12, 0x1b, 0x2e, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x52, 0x49,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x34, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x09, 0x2e, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x49, 0x64, 0x1a, 0x14, 0x2e, 0x4f, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x56, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x6c, 0x6b, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x12,
	0x10, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x39, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x09, 0x2e, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x1a, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x31, 0x0a, 0x0c, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x12, 0x09, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x38, 0x0a, 0x0d, 0x52, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x09, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x64, 0x1a, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x3e, 0x0a, 0x0a, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x12,
	0x12, 0x2e, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x58, 0x0a, 0x17, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x4f, 0x61, 0x75,
	0x74, 0x68, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x46,
	0x69, 0x6e, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_oauth_manager_api_proto_rawDescOnce sync.Once
	file_oauth_manager_api_proto_rawDescData = file_oauth_manager_api_proto_rawDesc
)

func file_oauth_manager_api_proto_rawDescGZIP() []byte {
	file_oauth_manager_api_proto_rawDescOnce.Do(func() {
		file_oauth_manager_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_oauth_manager_api_proto_rawDescData)
	})
	return file_oauth_manager_api_proto_rawDescData
}

var file_oauth_manager_api_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_oauth_manager_api_proto_goTypes = []interface{}{
	(*GenerateRedirectURIRequest)(nil),     // 0: GenerateRedirectURIRequest
	(*HandleId)(nil),                       // 1: HandleId
	(*ActivateRequest)(nil),                // 2: ActivateRequest
	(*OAuthConnectionInfo)(nil),            // 3: OAuthConnectionInfo
	(*GetBulkConnectionInfoRequest)(nil),   // 4: GetBulkConnectionInfoRequest
	(*GetBulkConnectionInfoResponse)(nil),  // 5: GetBulkConnectionInfoResponse
	(*BeginOauthRequest)(nil),              // 6: BeginOauthRequest
	(*FinalizeOauthConnectionRequest)(nil), // 7: FinalizeOauthConnectionRequest
	(*timestamppb.Timestamp)(nil),          // 8: google.protobuf.Timestamp
	(*wrapperspb.StringValue)(nil),         // 9: google.protobuf.StringValue
	(*emptypb.Empty)(nil),                  // 10: google.protobuf.Empty
}
var file_oauth_manager_api_proto_depIdxs = []int32{
	8,  // 0: OAuthConnectionInfo.activated_at:type_name -> google.protobuf.Timestamp
	8,  // 1: OAuthConnectionInfo.discarded_at:type_name -> google.protobuf.Timestamp
	1,  // 2: GetBulkConnectionInfoRequest.ids:type_name -> HandleId
	3,  // 3: GetBulkConnectionInfoResponse.infos:type_name -> OAuthConnectionInfo
	0,  // 4: OAuthManager.GenerateRedirectURI:input_type -> GenerateRedirectURIRequest
	1,  // 5: OAuthManager.GetConnectionInfo:input_type -> HandleId
	4,  // 6: OAuthManager.GetBulkConnectionInfo:input_type -> GetBulkConnectionInfoRequest
	2,  // 7: OAuthManager.Activate:input_type -> ActivateRequest
	1,  // 8: OAuthManager.GetAccessToken:input_type -> HandleId
	1,  // 9: OAuthManager.ForceRefresh:input_type -> HandleId
	1,  // 10: OAuthManager.RefreshStatus:input_type -> HandleId
	6,  // 11: OAuthManager.BeginOauth:input_type -> BeginOauthRequest
	7,  // 12: OAuthManager.FinalizeOauthConnection:input_type -> FinalizeOauthConnectionRequest
	9,  // 13: OAuthManager.GenerateRedirectURI:output_type -> google.protobuf.StringValue
	3,  // 14: OAuthManager.GetConnectionInfo:output_type -> OAuthConnectionInfo
	5,  // 15: OAuthManager.GetBulkConnectionInfo:output_type -> GetBulkConnectionInfoResponse
	10, // 16: OAuthManager.Activate:output_type -> google.protobuf.Empty
	9,  // 17: OAuthManager.GetAccessToken:output_type -> google.protobuf.StringValue
	10, // 18: OAuthManager.ForceRefresh:output_type -> google.protobuf.Empty
	9,  // 19: OAuthManager.RefreshStatus:output_type -> google.protobuf.StringValue
	9,  // 20: OAuthManager.BeginOauth:output_type -> google.protobuf.StringValue
	9,  // 21: OAuthManager.FinalizeOauthConnection:output_type -> google.protobuf.StringValue
	13, // [13:22] is the sub-list for method output_type
	4,  // [4:13] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_oauth_manager_api_proto_init() }
func file_oauth_manager_api_proto_init() {
	if File_oauth_manager_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_oauth_manager_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateRedirectURIRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oauth_manager_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandleId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oauth_manager_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oauth_manager_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OAuthConnectionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oauth_manager_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBulkConnectionInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oauth_manager_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBulkConnectionInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oauth_manager_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BeginOauthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oauth_manager_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinalizeOauthConnectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_oauth_manager_api_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_oauth_manager_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_oauth_manager_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_oauth_manager_api_proto_goTypes,
		DependencyIndexes: file_oauth_manager_api_proto_depIdxs,
		MessageInfos:      file_oauth_manager_api_proto_msgTypes,
	}.Build()
	File_oauth_manager_api_proto = out.File
	file_oauth_manager_api_proto_rawDesc = nil
	file_oauth_manager_api_proto_goTypes = nil
	file_oauth_manager_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// OAuthManagerClient is the client API for OAuthManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OAuthManagerClient interface {
	GenerateRedirectURI(ctx context.Context, in *GenerateRedirectURIRequest, opts ...grpc.CallOption) (*wrapperspb.StringValue, error)
	GetConnectionInfo(ctx context.Context, in *HandleId, opts ...grpc.CallOption) (*OAuthConnectionInfo, error)
	GetBulkConnectionInfo(ctx context.Context, in *GetBulkConnectionInfoRequest, opts ...grpc.CallOption) (*GetBulkConnectionInfoResponse, error)
	Activate(ctx context.Context, in *ActivateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetAccessToken(ctx context.Context, in *HandleId, opts ...grpc.CallOption) (*wrapperspb.StringValue, error)
	ForceRefresh(ctx context.Context, in *HandleId, opts ...grpc.CallOption) (*emptypb.Empty, error)
	RefreshStatus(ctx context.Context, in *HandleId, opts ...grpc.CallOption) (*wrapperspb.StringValue, error)
	BeginOauth(ctx context.Context, in *BeginOauthRequest, opts ...grpc.CallOption) (*wrapperspb.StringValue, error)
	FinalizeOauthConnection(ctx context.Context, in *FinalizeOauthConnectionRequest, opts ...grpc.CallOption) (*wrapperspb.StringValue, error)
}

type oAuthManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewOAuthManagerClient(cc grpc.ClientConnInterface) OAuthManagerClient {
	return &oAuthManagerClient{cc}
}

func (c *oAuthManagerClient) GenerateRedirectURI(ctx context.Context, in *GenerateRedirectURIRequest, opts ...grpc.CallOption) (*wrapperspb.StringValue, error) {
	out := new(wrapperspb.StringValue)
	err := c.cc.Invoke(ctx, "/OAuthManager/GenerateRedirectURI", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oAuthManagerClient) GetConnectionInfo(ctx context.Context, in *HandleId, opts ...grpc.CallOption) (*OAuthConnectionInfo, error) {
	out := new(OAuthConnectionInfo)
	err := c.cc.Invoke(ctx, "/OAuthManager/GetConnectionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oAuthManagerClient) GetBulkConnectionInfo(ctx context.Context, in *GetBulkConnectionInfoRequest, opts ...grpc.CallOption) (*GetBulkConnectionInfoResponse, error) {
	out := new(GetBulkConnectionInfoResponse)
	err := c.cc.Invoke(ctx, "/OAuthManager/GetBulkConnectionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oAuthManagerClient) Activate(ctx context.Context, in *ActivateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/OAuthManager/Activate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oAuthManagerClient) GetAccessToken(ctx context.Context, in *HandleId, opts ...grpc.CallOption) (*wrapperspb.StringValue, error) {
	out := new(wrapperspb.StringValue)
	err := c.cc.Invoke(ctx, "/OAuthManager/GetAccessToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oAuthManagerClient) ForceRefresh(ctx context.Context, in *HandleId, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/OAuthManager/ForceRefresh", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oAuthManagerClient) RefreshStatus(ctx context.Context, in *HandleId, opts ...grpc.CallOption) (*wrapperspb.StringValue, error) {
	out := new(wrapperspb.StringValue)
	err := c.cc.Invoke(ctx, "/OAuthManager/RefreshStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oAuthManagerClient) BeginOauth(ctx context.Context, in *BeginOauthRequest, opts ...grpc.CallOption) (*wrapperspb.StringValue, error) {
	out := new(wrapperspb.StringValue)
	err := c.cc.Invoke(ctx, "/OAuthManager/BeginOauth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oAuthManagerClient) FinalizeOauthConnection(ctx context.Context, in *FinalizeOauthConnectionRequest, opts ...grpc.CallOption) (*wrapperspb.StringValue, error) {
	out := new(wrapperspb.StringValue)
	err := c.cc.Invoke(ctx, "/OAuthManager/FinalizeOauthConnection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OAuthManagerServer is the server API for OAuthManager service.
type OAuthManagerServer interface {
	GenerateRedirectURI(context.Context, *GenerateRedirectURIRequest) (*wrapperspb.StringValue, error)
	GetConnectionInfo(context.Context, *HandleId) (*OAuthConnectionInfo, error)
	GetBulkConnectionInfo(context.Context, *GetBulkConnectionInfoRequest) (*GetBulkConnectionInfoResponse, error)
	Activate(context.Context, *ActivateRequest) (*emptypb.Empty, error)
	GetAccessToken(context.Context, *HandleId) (*wrapperspb.StringValue, error)
	ForceRefresh(context.Context, *HandleId) (*emptypb.Empty, error)
	RefreshStatus(context.Context, *HandleId) (*wrapperspb.StringValue, error)
	BeginOauth(context.Context, *BeginOauthRequest) (*wrapperspb.StringValue, error)
	FinalizeOauthConnection(context.Context, *FinalizeOauthConnectionRequest) (*wrapperspb.StringValue, error)
}

// UnimplementedOAuthManagerServer can be embedded to have forward compatible implementations.
type UnimplementedOAuthManagerServer struct {
}

func (*UnimplementedOAuthManagerServer) GenerateRedirectURI(context.Context, *GenerateRedirectURIRequest) (*wrapperspb.StringValue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateRedirectURI not implemented")
}
func (*UnimplementedOAuthManagerServer) GetConnectionInfo(context.Context, *HandleId) (*OAuthConnectionInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConnectionInfo not implemented")
}
func (*UnimplementedOAuthManagerServer) GetBulkConnectionInfo(context.Context, *GetBulkConnectionInfoRequest) (*GetBulkConnectionInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBulkConnectionInfo not implemented")
}
func (*UnimplementedOAuthManagerServer) Activate(context.Context, *ActivateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Activate not implemented")
}
func (*UnimplementedOAuthManagerServer) GetAccessToken(context.Context, *HandleId) (*wrapperspb.StringValue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccessToken not implemented")
}
func (*UnimplementedOAuthManagerServer) ForceRefresh(context.Context, *HandleId) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForceRefresh not implemented")
}
func (*UnimplementedOAuthManagerServer) RefreshStatus(context.Context, *HandleId) (*wrapperspb.StringValue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshStatus not implemented")
}
func (*UnimplementedOAuthManagerServer) BeginOauth(context.Context, *BeginOauthRequest) (*wrapperspb.StringValue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BeginOauth not implemented")
}
func (*UnimplementedOAuthManagerServer) FinalizeOauthConnection(context.Context, *FinalizeOauthConnectionRequest) (*wrapperspb.StringValue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinalizeOauthConnection not implemented")
}

func RegisterOAuthManagerServer(s *grpc.Server, srv OAuthManagerServer) {
	s.RegisterService(&_OAuthManager_serviceDesc, srv)
}

func _OAuthManager_GenerateRedirectURI_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateRedirectURIRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OAuthManagerServer).GenerateRedirectURI(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/OAuthManager/GenerateRedirectURI",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OAuthManagerServer).GenerateRedirectURI(ctx, req.(*GenerateRedirectURIRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OAuthManager_GetConnectionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OAuthManagerServer).GetConnectionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/OAuthManager/GetConnectionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OAuthManagerServer).GetConnectionInfo(ctx, req.(*HandleId))
	}
	return interceptor(ctx, in, info, handler)
}

func _OAuthManager_GetBulkConnectionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBulkConnectionInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OAuthManagerServer).GetBulkConnectionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/OAuthManager/GetBulkConnectionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OAuthManagerServer).GetBulkConnectionInfo(ctx, req.(*GetBulkConnectionInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OAuthManager_Activate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OAuthManagerServer).Activate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/OAuthManager/Activate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OAuthManagerServer).Activate(ctx, req.(*ActivateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OAuthManager_GetAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OAuthManagerServer).GetAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/OAuthManager/GetAccessToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OAuthManagerServer).GetAccessToken(ctx, req.(*HandleId))
	}
	return interceptor(ctx, in, info, handler)
}

func _OAuthManager_ForceRefresh_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OAuthManagerServer).ForceRefresh(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/OAuthManager/ForceRefresh",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OAuthManagerServer).ForceRefresh(ctx, req.(*HandleId))
	}
	return interceptor(ctx, in, info, handler)
}

func _OAuthManager_RefreshStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OAuthManagerServer).RefreshStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/OAuthManager/RefreshStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OAuthManagerServer).RefreshStatus(ctx, req.(*HandleId))
	}
	return interceptor(ctx, in, info, handler)
}

func _OAuthManager_BeginOauth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BeginOauthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OAuthManagerServer).BeginOauth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/OAuthManager/BeginOauth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OAuthManagerServer).BeginOauth(ctx, req.(*BeginOauthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OAuthManager_FinalizeOauthConnection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FinalizeOauthConnectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OAuthManagerServer).FinalizeOauthConnection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/OAuthManager/FinalizeOauthConnection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OAuthManagerServer).FinalizeOauthConnection(ctx, req.(*FinalizeOauthConnectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _OAuthManager_serviceDesc = grpc.ServiceDesc{
	ServiceName: "OAuthManager",
	HandlerType: (*OAuthManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateRedirectURI",
			Handler:    _OAuthManager_GenerateRedirectURI_Handler,
		},
		{
			MethodName: "GetConnectionInfo",
			Handler:    _OAuthManager_GetConnectionInfo_Handler,
		},
		{
			MethodName: "GetBulkConnectionInfo",
			Handler:    _OAuthManager_GetBulkConnectionInfo_Handler,
		},
		{
			MethodName: "Activate",
			Handler:    _OAuthManager_Activate_Handler,
		},
		{
			MethodName: "GetAccessToken",
			Handler:    _OAuthManager_GetAccessToken_Handler,
		},
		{
			MethodName: "ForceRefresh",
			Handler:    _OAuthManager_ForceRefresh_Handler,
		},
		{
			MethodName: "RefreshStatus",
			Handler:    _OAuthManager_RefreshStatus_Handler,
		},
		{
			MethodName: "BeginOauth",
			Handler:    _OAuthManager_BeginOauth_Handler,
		},
		{
			MethodName: "FinalizeOauthConnection",
			Handler:    _OAuthManager_FinalizeOauthConnection_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "oauth_manager/api.proto",
}
