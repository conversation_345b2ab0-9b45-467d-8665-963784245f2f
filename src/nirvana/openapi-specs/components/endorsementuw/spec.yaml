components:
  parameters:
    EndorsementReviewID:
      in: path
      name: endorsementReviewID
      required: true
      schema:
        type: string
        example: a81bc81b-dead-4e5d-abff-90865d1e13b1
  schemas:
    GetEndorsementReviewsResponse:
      type: object
      required:
        - reviews
      properties:
        reviews:
          type: array
          items:
            $ref: '#/components/schemas/EndorsementReviewItem'
    EndorsementReviewItem:
        type: object
        required:
          - id
          - companyName
          - effectiveDate
          - underwriterInfo
          - state
          - endorsementNumber
        properties:
          id:
            type: string
            format: uuid
          companyName:
            type: string
            example: 'ABC Company'
          effectiveDate:
            type: string
            format: date
            example: 2005-11-01
          state:
            $ref: '#/components/schemas/EndorsementReviewState'
          underwriterAssistantInfo:
            $ref: '../nirvana/spec.yaml#/components/schemas/UserInfo'
          endorsementNumber:
            type: string
    GetEndorsementReviewResponse:
      type: object
      required:
        - id
        - companyName
        - effectiveDate
        - underwriterInfo
        - state
        - changes
        - originalApplicationReviewID
        - endorsementNumber
      properties:
        id:
          type: string
          format: uuid
        companyName:
          type: string
          example: 'ABC Company'
        effectiveDate:
          type: string
          format: date
          example: 2005-11-01
        state:
          $ref: '#/components/schemas/EndorsementReviewState'
        underwriterInfo:
          $ref: '../nirvana/spec.yaml#/components/schemas/UserInfo'
        changes:
          type: array
          items:
            $ref: '#/components/schemas/EndorsementChange'
        originalApplicationReviewID:
          type: string
          format: uuid
        price:
          $ref: '../endorsementapp/intake/spec.yaml#/components/schemas/EndorsementPrice'
        mvrDetails:
          type: array
          items:
            $ref: '#/components/schemas/DriverMVRDetails'
        endorsementNumber:
          type: string
        policyChangeForms:
          type: array
          items:
            $ref: '#/components/schemas/PolicyChangeForm'
    PatchEndorsementReviewRequestBody:
      type: object
      required:
        - action
        - reason
      properties:
        action:
          type: string
          enum: [ Approve, Decline, Close ]
        reason:
          type: string
          description: Reason for updating the endorsement review

    EndorsementReviewState:
      type: string
      enum: [ Invalid, Approved, Declined, Closed, Pending, Stale, RefreshingPrice, Bound, Panic ]

    EndorsementChange:
      type: object
      required:
        - changeType
        - data
      properties:
        changeType:
          $ref: '../endorsementapp/intake/spec.yaml#/components/schemas/EndorsementChangeType'
        data:
          anyOf:
            - $ref: '../endorsementapp/intake/spec.yaml#/components/schemas/DriverChange'
            - $ref: '../endorsementapp/intake/spec.yaml#/components/schemas/VehicleChange'
            - $ref: '../endorsementapp/intake/spec.yaml#/components/schemas/AddressChange'
            - $ref: '../endorsementapp/intake/spec.yaml#/components/schemas/AdditionalInsuredChange'

    DriverMVRDetails:
      type: object
      required:
        - cdlNumber
      properties:
        yearIssued:
          type: integer
          example: 2010
        cdlNumber:
          type: string
          example: 'I-165-520-62-245-0'
        mvrPullStatus:
          $ref: '../common/spec.yaml#/components/schemas/MVRPullStatus'
        mvrPullError:
          type: string
          example: 'MVR Pull Error'
        issueDate:
          type: string
          format: date
          example: 2005-11-01
        expirationDate:
          type: string
          format: date
          example: 2005-11-01
        violations:
          type: array
          items:
            $ref: '../common/spec.yaml#/components/schemas/DriverViolation'
        cdlClass:
          type: string
          example: 'CDL-AM'
        cdlStatus:
          type: string
          example: 'VALID'
        violationPoints:
          type: integer
          example: 2
        violationCount:
          type: integer
          example: 1

    PostEndorsementReviewRefreshPriceRequestBody:
      type: object
      required:
        - runType
      properties:
        runType:
          $ref: '../endorsementapp/intake/spec.yaml#/components/schemas/RunType'

    PostBindEndorsementRequestBody:
      type: object
      properties:
        reason:
          type: string
          description: Reason for binding the endorsement
        supportingDocsHandles:
          type: array
          items:
            type: string
            format: uuid

    ReplacePolicyChangeFormRequestBody:
      type: object
      required:
        - file
        - fileType
        - policyNumber
      properties:
        file:
          type: string
          format: binary
        fileType:
          type: string
        policyNumber:
          type: string

    PolicyChangeForm:
      type: object
      required:
        - policyNumber
        - fileName
        - handleID
      properties:
        policyNumber:
          type: string
        fileName:
          type: string
        handleID:
          type: string
          format: uuid
