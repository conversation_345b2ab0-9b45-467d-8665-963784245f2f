// Package endorsementuw provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/deepmap/oapi-codegen/v2 version v2.0.0 DO NOT EDIT.
package endorsementuw

import (
	"encoding/json"

	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
	externalRef0 "nirvanatech.com/nirvana/openapi-specs/components/common"
	externalRef1 "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
	externalRef2 "nirvanatech.com/nirvana/openapi-specs/components/nirvana"
)

// Defines values for EndorsementReviewState.
const (
	Approved        EndorsementReviewState = "Approved"
	Bound           EndorsementReviewState = "Bound"
	Closed          EndorsementReviewState = "Closed"
	Declined        EndorsementReviewState = "Declined"
	Invalid         EndorsementReviewState = "Invalid"
	Panic           EndorsementReviewState = "Panic"
	Pending         EndorsementReviewState = "Pending"
	RefreshingPrice EndorsementReviewState = "RefreshingPrice"
	Stale           EndorsementReviewState = "Stale"
)

// Defines values for PatchEndorsementReviewRequestBodyAction.
const (
	Approve PatchEndorsementReviewRequestBodyAction = "Approve"
	Close   PatchEndorsementReviewRequestBodyAction = "Close"
	Decline PatchEndorsementReviewRequestBodyAction = "Decline"
)

// DriverMVRDetails defines model for DriverMVRDetails.
type DriverMVRDetails struct {
	CdlClass        *string                         `json:"cdlClass,omitempty"`
	CdlNumber       string                          `json:"cdlNumber"`
	CdlStatus       *string                         `json:"cdlStatus,omitempty"`
	ExpirationDate  *openapi_types.Date             `json:"expirationDate,omitempty"`
	IssueDate       *openapi_types.Date             `json:"issueDate,omitempty"`
	MvrPullError    *string                         `json:"mvrPullError,omitempty"`
	MvrPullStatus   *externalRef0.MVRPullStatus     `json:"mvrPullStatus,omitempty"`
	ViolationCount  *int                            `json:"violationCount,omitempty"`
	ViolationPoints *int                            `json:"violationPoints,omitempty"`
	Violations      *[]externalRef0.DriverViolation `json:"violations,omitempty"`
	YearIssued      *int                            `json:"yearIssued,omitempty"`
}

// EndorsementChange defines model for EndorsementChange.
type EndorsementChange struct {
	ChangeType externalRef1.EndorsementChangeType `json:"changeType"`
	Data       EndorsementChange_Data             `json:"data"`
}

// EndorsementChange_Data defines model for EndorsementChange.Data.
type EndorsementChange_Data struct {
	union json.RawMessage
}

// EndorsementReviewItem defines model for EndorsementReviewItem.
type EndorsementReviewItem struct {
	CompanyName              string                 `json:"companyName"`
	EffectiveDate            openapi_types.Date     `json:"effectiveDate"`
	EndorsementNumber        string                 `json:"endorsementNumber"`
	Id                       openapi_types.UUID     `json:"id"`
	State                    EndorsementReviewState `json:"state"`
	UnderwriterAssistantInfo *externalRef2.UserInfo `json:"underwriterAssistantInfo,omitempty"`
}

// EndorsementReviewState defines model for EndorsementReviewState.
type EndorsementReviewState string

// GetEndorsementReviewResponse defines model for GetEndorsementReviewResponse.
type GetEndorsementReviewResponse struct {
	Changes                     []EndorsementChange            `json:"changes"`
	CompanyName                 string                         `json:"companyName"`
	EffectiveDate               openapi_types.Date             `json:"effectiveDate"`
	EndorsementNumber           string                         `json:"endorsementNumber"`
	Id                          openapi_types.UUID             `json:"id"`
	MvrDetails                  *[]DriverMVRDetails            `json:"mvrDetails,omitempty"`
	OriginalApplicationReviewID openapi_types.UUID             `json:"originalApplicationReviewID"`
	PolicyChangeForms           *[]PolicyChangeForm            `json:"policyChangeForms,omitempty"`
	Price                       *externalRef1.EndorsementPrice `json:"price,omitempty"`
	State                       EndorsementReviewState         `json:"state"`
	UnderwriterInfo             externalRef2.UserInfo          `json:"underwriterInfo"`
}

// GetEndorsementReviewsResponse defines model for GetEndorsementReviewsResponse.
type GetEndorsementReviewsResponse struct {
	Reviews []EndorsementReviewItem `json:"reviews"`
}

// PatchEndorsementReviewRequestBody defines model for PatchEndorsementReviewRequestBody.
type PatchEndorsementReviewRequestBody struct {
	Action PatchEndorsementReviewRequestBodyAction `json:"action"`

	// Reason Reason for updating the endorsement review
	Reason string `json:"reason"`
}

// PatchEndorsementReviewRequestBodyAction defines model for PatchEndorsementReviewRequestBody.Action.
type PatchEndorsementReviewRequestBodyAction string

// PolicyChangeForm defines model for PolicyChangeForm.
type PolicyChangeForm struct {
	FileName     string             `json:"fileName"`
	HandleID     openapi_types.UUID `json:"handleID"`
	PolicyNumber string             `json:"policyNumber"`
}

// PostBindEndorsementRequestBody defines model for PostBindEndorsementRequestBody.
type PostBindEndorsementRequestBody struct {
	// Reason Reason for binding the endorsement
	Reason                *string               `json:"reason,omitempty"`
	SupportingDocsHandles *[]openapi_types.UUID `json:"supportingDocsHandles,omitempty"`
}

// PostEndorsementReviewRefreshPriceRequestBody defines model for PostEndorsementReviewRefreshPriceRequestBody.
type PostEndorsementReviewRefreshPriceRequestBody struct {
	RunType externalRef1.RunType `json:"runType"`
}

// ReplacePolicyChangeFormRequestBody defines model for ReplacePolicyChangeFormRequestBody.
type ReplacePolicyChangeFormRequestBody struct {
	File         openapi_types.File `json:"file"`
	FileType     string             `json:"fileType"`
	PolicyNumber string             `json:"policyNumber"`
}

// EndorsementReviewID defines model for EndorsementReviewID.
type EndorsementReviewID = string

// AsExternalRef1DriverChange returns the union data inside the EndorsementChange_Data as a externalRef1.DriverChange
func (t EndorsementChange_Data) AsExternalRef1DriverChange() (externalRef1.DriverChange, error) {
	var body externalRef1.DriverChange
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromExternalRef1DriverChange overwrites any union data inside the EndorsementChange_Data as the provided externalRef1.DriverChange
func (t *EndorsementChange_Data) FromExternalRef1DriverChange(v externalRef1.DriverChange) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeExternalRef1DriverChange performs a merge with any union data inside the EndorsementChange_Data, using the provided externalRef1.DriverChange
func (t *EndorsementChange_Data) MergeExternalRef1DriverChange(v externalRef1.DriverChange) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JsonMerge(t.union, b)
	t.union = merged
	return err
}

// AsExternalRef1VehicleChange returns the union data inside the EndorsementChange_Data as a externalRef1.VehicleChange
func (t EndorsementChange_Data) AsExternalRef1VehicleChange() (externalRef1.VehicleChange, error) {
	var body externalRef1.VehicleChange
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromExternalRef1VehicleChange overwrites any union data inside the EndorsementChange_Data as the provided externalRef1.VehicleChange
func (t *EndorsementChange_Data) FromExternalRef1VehicleChange(v externalRef1.VehicleChange) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeExternalRef1VehicleChange performs a merge with any union data inside the EndorsementChange_Data, using the provided externalRef1.VehicleChange
func (t *EndorsementChange_Data) MergeExternalRef1VehicleChange(v externalRef1.VehicleChange) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JsonMerge(t.union, b)
	t.union = merged
	return err
}

// AsExternalRef1AddressChange returns the union data inside the EndorsementChange_Data as a externalRef1.AddressChange
func (t EndorsementChange_Data) AsExternalRef1AddressChange() (externalRef1.AddressChange, error) {
	var body externalRef1.AddressChange
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromExternalRef1AddressChange overwrites any union data inside the EndorsementChange_Data as the provided externalRef1.AddressChange
func (t *EndorsementChange_Data) FromExternalRef1AddressChange(v externalRef1.AddressChange) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeExternalRef1AddressChange performs a merge with any union data inside the EndorsementChange_Data, using the provided externalRef1.AddressChange
func (t *EndorsementChange_Data) MergeExternalRef1AddressChange(v externalRef1.AddressChange) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JsonMerge(t.union, b)
	t.union = merged
	return err
}

// AsExternalRef1AdditionalInsuredChange returns the union data inside the EndorsementChange_Data as a externalRef1.AdditionalInsuredChange
func (t EndorsementChange_Data) AsExternalRef1AdditionalInsuredChange() (externalRef1.AdditionalInsuredChange, error) {
	var body externalRef1.AdditionalInsuredChange
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromExternalRef1AdditionalInsuredChange overwrites any union data inside the EndorsementChange_Data as the provided externalRef1.AdditionalInsuredChange
func (t *EndorsementChange_Data) FromExternalRef1AdditionalInsuredChange(v externalRef1.AdditionalInsuredChange) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeExternalRef1AdditionalInsuredChange performs a merge with any union data inside the EndorsementChange_Data, using the provided externalRef1.AdditionalInsuredChange
func (t *EndorsementChange_Data) MergeExternalRef1AdditionalInsuredChange(v externalRef1.AdditionalInsuredChange) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JsonMerge(t.union, b)
	t.union = merged
	return err
}

func (t EndorsementChange_Data) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *EndorsementChange_Data) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}
