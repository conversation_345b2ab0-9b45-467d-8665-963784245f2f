load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "underwriting",
    srcs = [
        "gen.go",
        "spec.go",
    ],
    importpath = "nirvanatech.com/nirvana/openapi-specs/components/underwriting",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/openapi-specs/components/common",
        "@com_github_oapi_codegen_runtime//:runtime",
        "@com_github_oapi_codegen_runtime//types",
    ],
)
