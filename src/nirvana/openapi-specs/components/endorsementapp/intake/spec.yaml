components:
  parameters:
    EndorsementRequestID:
      in: path
      name: endorsementRequestID
      description: The ID of the endorsement request
      required: true
      schema:
        type: string
        example: ae69f0c4-6ffb-4440-b0ab-056dd900ea11

  schemas:
    #    The following schemas are for the request/response bodies

    CreateEndorsementRequestBody:
      type: object
      required:
        - programType
      properties:
        programType:
          $ref: '../../common/spec.yaml#/components/schemas/ProgramType'
    CreateEndorsementResponse:
      type: object
      required:
        - endorsementRequestID
      properties:
        endorsementRequestID:
          type: string
          example: ae69f0c4-6ffb-4440-b0ab-056dd900ea11

    GetEndorsementRequestResponse:
      type: object
      required:
        - effectiveDates
        - state
        - provisionalEndorsementNumber
        - insured
        - underwriterInfo
        - bundleDuration
      properties:
        state:
          $ref: '#/components/schemas/EndorsementRequestState'
        effectiveDates:
          type: array
          items:
            $ref: '#/components/schemas/EffectiveDateEntry'
        defaultEffectiveDate:
          type: string
          format: date-time
        defaultExpirationDate:
          type: string
          format: date-time
        changes:
          type: array
          items:
            $ref: '#/components/schemas/EndorsementChange'
        provisionalEndorsementNumber:
          type: string
        insured:
          $ref: '../../insurance-bundle/spec.yaml#/components/schemas/Insured'
        underwriterInfo:
          $ref: '../../nirvana/spec.yaml#/components/schemas/UserInfo'
        bundleDuration:
          $ref: '../../insurance-bundle/spec.yaml#/components/schemas/BundleDuration'

    UpdateEndorsementRequestRequestBody:
      type: object
      required:
          - defaultEffectiveDate
      properties:
        defaultEffectiveDate:
          type: string
          format: date-time

    GetEndorsementRequestCoveragesResponse:
      type: object
      properties:
        coverages:
          type: array
          items:
            $ref: '#/components/schemas/SubCoverageChangeGroup'
    PatchEndorsementRequestCoveragesRequestBody:
      type: object
      required:
        - coverages
      properties:
        effectiveDate:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        coverages:
          type: array
          items:
            $ref: '#/components/schemas/CoverageGroup'

    GetEndorsementRequestEquipmentsResponse:
      type: object
      properties:
        equipments:
          type: array
          items:
            $ref: '#/components/schemas/VehicleChange'
    PatchEndorsementRequestEquipmentsRequestBody:
      type: object
      required:
        - vehicles
      properties:
        effectiveDate:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        vehicles:
          type: array
          items:
            $ref: '../../insurance-bundle/spec.yaml#/components/schemas/Vehicle'

    GetEndorsementRequestDriversResponse:
      type: object
      properties:
        drivers:
          type: array
          items:
            $ref: '#/components/schemas/DriverChange'
    PatchEndorsementRequestDriversRequestBody:
      type: object
      required:
        - drivers
      properties:
        effectiveDate:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        drivers:
          type: array
          items:
            $ref: '../../insurance-bundle/spec.yaml#/components/schemas/Driver'

    GetEndorsementRequestMiscellaneousResponse:
      type: object
      required:
        - mailingAddress
        - terminalAddress
        - additionalInsureds
        - hasBlanketWaiverOfSubrogation
        - hasBlanketAdditionalInsured
      properties:
        mailingAddress:
          $ref: '#/components/schemas/AddressChange'
        terminalAddress:
          $ref: '#/components/schemas/AddressChange'
        additionalInsureds:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalInsuredChange'
        hasBlanketWaiverOfSubrogation:
          type: boolean
        hasBlanketAdditionalInsured:
          type: boolean

    PatchEndorsementRequestMiscellaneousRequestBody:
      type: object
      properties:
        effectiveDate:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        mailingAddress:
          $ref: '../../nirvana/spec.yaml#/components/schemas/Address'
        terminalAddress:
          $ref: '../../nirvana/spec.yaml#/components/schemas/Address'
        additionalInsureds:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalInsuredWithWoS'

    GetAllEndorsementRequestsResponse:
      type: object
      required:
        - endorsementRequests
      properties:
        endorsementRequests:
          type: array
          items:
              $ref: '#/components/schemas/EndorsementRequestDetails'

    SubmitEndorsementRequestForReviewRequestBody:
      type: object
      properties:
        isQuoteRequested:
          type: boolean
          description: Is quote requested by agent for the endorsement request

    #    The following schemas are used above as part of the request/response schemas
    CoverageGroup:
      type: object
      required:
        - parentCoverage
        - subCoverages
      properties:
        coverage:
          $ref: '../../common/spec.yaml#/components/schemas/Coverage'
        subCoverages:
          type: array
          items:
            $ref: '../../common/spec.yaml#/components/schemas/SubCoverage'
    SubCoverageChangeGroup:
      type: object
      required:
        - coverage
        - subCoverages
      properties:
        coverage:
          $ref: '../../common/spec.yaml#/components/schemas/Coverage'
        subCoverages:
          type: array
          items:
            $ref: '#/components/schemas/SubCoverageChange'
    SubCoverageChange:
      type: object
      required:
        - changeType
        - subCoverage
      properties:
        changeType:
          type: string
          enum: [ added, deleted, updated-limit, updated-deductible, unchanged ]
        subCoverage:
          $ref: '../../common/spec.yaml#/components/schemas/SubCoverage'
        oldValue:
          $ref: '../../common/spec.yaml#/components/schemas/SubCoverage'
    VehicleChange:
      type: object
      required:
        - changeType
        - vehicle
      properties:
        changeType:
          type: string
          enum: [ added, deleted, updated, unchanged ]
        vehicle:
          $ref: '../../insurance-bundle/spec.yaml#/components/schemas/Vehicle'
        oldValue:
          $ref: '../../insurance-bundle/spec.yaml#/components/schemas/Vehicle'
    DriverChange:
      type: object
      required:
        - changeType
        - driver
      properties:
        changeType:
          type: string
          enum: [ added, deleted, updated, unchanged ]
        driver:
          $ref: '../../insurance-bundle/spec.yaml#/components/schemas/Driver'
        oldValue:
          $ref: '../../insurance-bundle/spec.yaml#/components/schemas/Driver'
    AddressChange:
      type: object
      required:
        - changeType
        - address
      properties:
        changeType:
          type: string
          enum: [ added, deleted, updated, unchanged ]
        address:
          $ref: '../../nirvana/spec.yaml#/components/schemas/Address'
        oldValue:
          $ref: '../../nirvana/spec.yaml#/components/schemas/Address'
    AdditionalInsuredChange:
      type: object
      required:
        - changeType
        - additionalInsured
        - hasWoS
      properties:
        changeType:
          type: string
          enum: [ added, deleted, updated, unchanged ]
        additionalInsured:
          $ref: '../../nirvana/spec.yaml#/components/schemas/AdditionalInsured'
        oldValue:
          $ref: '../../nirvana/spec.yaml#/components/schemas/AdditionalInsured'
        hasWoS:
          type: boolean
    EffectiveDateEntry:
      type: object
      required:
        - changeID
        - effectiveDate
        - expirationDate
      properties:
        changeID:
          type: string
          description: Identifier for the specific change
        effectiveDate:
          type: string
          format: date
        expirationDate:
          type: string
          format: date
    EndorsementRequestDetails:
      type: object
      required:
        - endorsementRequestID
        - state
        - endorsementNumber
        - endorsementDescription
      properties:
        endorsementRequestID:
          type: string
        state:
          $ref: '#/components/schemas/EndorsementRequestState'
        effectiveDate:
          type: string
          format: date-time
        endorsementNumber:
          type: string
        endorsementFee:
          type: number
          format: float
        endorsementDescription:
          type: array
          items:
            $ref: '#/components/schemas/EndorsementChange'
    EndorsementChangeType:
      type: string
      enum:
        - Drivers
        - Equipments
        - Terminal Location
        - Mailing Address
        - Additional Insureds
        - Coverages
        - Sub Coverages
        - Invalid
    EndorsementChange:
      type: object
      required:
        - changeType
        - descriptions
      properties:
        changeType:
          $ref: '#/components/schemas/EndorsementChangeType'
        descriptions:
          type: array
          items:
            type: string
    EndorsementRequestState:
      type: string
      enum:
        - EndorsementRequestStateInvalid
        - EndorsementRequestStateCreated
        - EndorsementRequestStateUnderUWReview
        - EndorsementRequestStateApproved
        - EndorsementRequestStateBound
        - EndorsementRequestStateDeclined
        - EndorsementRequestStateClosed
        - EndorsementRequestStateOutOfSync

    GetEndorsementRequestChargesResponseTemp:
      type: object
      required:
        - endorsementFees
        - perCoverageCharge
      properties:
        endorsementFees:
          type: number
          format: float
        perCoverageCharge:
          type: array
          items:
            $ref: '#/components/schemas/PerCoverageCharge'

    PerCoverageCharge:
      type: object
      required:
        - coverage
        - charge
      properties:
        coverage:
          type: string
        charge:
          type: number
          format: float

    AdditionalInsuredWithWoS:
      type: object
      required:
        - additionalInsured
        - hasWoS
      properties:
        additionalInsured:
          $ref: '../../nirvana/spec.yaml#/components/schemas/AdditionalInsured'
        hasWoS:
          type: boolean

    EndorsementPrice:
      type: object
      required:
        - writtenPremium
        - writtenPremiumByCoverage
      properties:
        writtenPremium:
          type: number
          format: double
        writtenPremiumByCoverage:
          type: array
          items:
            $ref: '#/components/schemas/WrittenPremiumByCoverage'
        stateSurcharge:
          type: number
          format: double
        flatCharge:
          type: number
          format: double
    WrittenPremiumByCoverage:
      type: object
      required:
        - coverage
        - writtenPremium
      properties:
        coverage:
          type: string
          $ref: '../../common/spec.yaml#/components/schemas/CoverageType'
        writtenPremium:
          type: number
          format: double

    GenerateQuoteRequestBody:
      type: object
      required:
        - runType
      properties:
        runType:
          $ref: '#/components/schemas/RunType'
    GetEndorsementQuoteResponse:
      type: object
      required:
        - state
      properties:
        price:
          $ref: '#/components/schemas/EndorsementPrice'
        state:
          $ref: '#/components/schemas/EndorsementQuoteState'
    EndorsementQuoteState:
      type: string
      description: Status of the endorsement quote
      enum:
        - EndorsementQuoteNotAvailable
        - EndorsementQuoteRefreshing
        - EndorsementQuoteGenerated
        - EndorsementQuotePanic
    RunType:
      type: string
      enum: [ UWBindableSubmission, UWSubmission, Indication ]
