// Package endorsementapp_intake provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/deepmap/oapi-codegen/v2 version v2.0.0 DO NOT EDIT.
package endorsementapp_intake

import (
	"time"

	openapi_types "github.com/oapi-codegen/runtime/types"
	externalRef0 "nirvanatech.com/nirvana/openapi-specs/components/common"
	externalRef1 "nirvanatech.com/nirvana/openapi-specs/components/insurance-bundle"
	externalRef2 "nirvanatech.com/nirvana/openapi-specs/components/nirvana"
)

// Defines values for AdditionalInsuredChangeChangeType.
const (
	AdditionalInsuredChangeChangeTypeAdded     AdditionalInsuredChangeChangeType = "added"
	AdditionalInsuredChangeChangeTypeDeleted   AdditionalInsuredChangeChangeType = "deleted"
	AdditionalInsuredChangeChangeTypeUnchanged AdditionalInsuredChangeChangeType = "unchanged"
	AdditionalInsuredChangeChangeTypeUpdated   AdditionalInsuredChangeChangeType = "updated"
)

// Defines values for AddressChangeChangeType.
const (
	AddressChangeChangeTypeAdded     AddressChangeChangeType = "added"
	AddressChangeChangeTypeDeleted   AddressChangeChangeType = "deleted"
	AddressChangeChangeTypeUnchanged AddressChangeChangeType = "unchanged"
	AddressChangeChangeTypeUpdated   AddressChangeChangeType = "updated"
)

// Defines values for DriverChangeChangeType.
const (
	DriverChangeChangeTypeAdded     DriverChangeChangeType = "added"
	DriverChangeChangeTypeDeleted   DriverChangeChangeType = "deleted"
	DriverChangeChangeTypeUnchanged DriverChangeChangeType = "unchanged"
	DriverChangeChangeTypeUpdated   DriverChangeChangeType = "updated"
)

// Defines values for EndorsementChangeType.
const (
	AdditionalInsureds EndorsementChangeType = "Additional Insureds"
	Coverages          EndorsementChangeType = "Coverages"
	Drivers            EndorsementChangeType = "Drivers"
	Equipments         EndorsementChangeType = "Equipments"
	Invalid            EndorsementChangeType = "Invalid"
	MailingAddress     EndorsementChangeType = "Mailing Address"
	SubCoverages       EndorsementChangeType = "Sub Coverages"
	TerminalLocation   EndorsementChangeType = "Terminal Location"
)

// Defines values for EndorsementQuoteState.
const (
	EndorsementQuoteGenerated    EndorsementQuoteState = "EndorsementQuoteGenerated"
	EndorsementQuoteNotAvailable EndorsementQuoteState = "EndorsementQuoteNotAvailable"
	EndorsementQuotePanic        EndorsementQuoteState = "EndorsementQuotePanic"
	EndorsementQuoteRefreshing   EndorsementQuoteState = "EndorsementQuoteRefreshing"
)

// Defines values for EndorsementRequestState.
const (
	EndorsementRequestStateApproved      EndorsementRequestState = "EndorsementRequestStateApproved"
	EndorsementRequestStateBound         EndorsementRequestState = "EndorsementRequestStateBound"
	EndorsementRequestStateClosed        EndorsementRequestState = "EndorsementRequestStateClosed"
	EndorsementRequestStateCreated       EndorsementRequestState = "EndorsementRequestStateCreated"
	EndorsementRequestStateDeclined      EndorsementRequestState = "EndorsementRequestStateDeclined"
	EndorsementRequestStateInvalid       EndorsementRequestState = "EndorsementRequestStateInvalid"
	EndorsementRequestStateOutOfSync     EndorsementRequestState = "EndorsementRequestStateOutOfSync"
	EndorsementRequestStateUnderUWReview EndorsementRequestState = "EndorsementRequestStateUnderUWReview"
)

// Defines values for RunType.
const (
	Indication           RunType = "Indication"
	UWBindableSubmission RunType = "UWBindableSubmission"
	UWSubmission         RunType = "UWSubmission"
)

// Defines values for SubCoverageChangeChangeType.
const (
	SubCoverageChangeChangeTypeAdded             SubCoverageChangeChangeType = "added"
	SubCoverageChangeChangeTypeDeleted           SubCoverageChangeChangeType = "deleted"
	SubCoverageChangeChangeTypeUnchanged         SubCoverageChangeChangeType = "unchanged"
	SubCoverageChangeChangeTypeUpdatedDeductible SubCoverageChangeChangeType = "updated-deductible"
	SubCoverageChangeChangeTypeUpdatedLimit      SubCoverageChangeChangeType = "updated-limit"
)

// Defines values for VehicleChangeChangeType.
const (
	Added     VehicleChangeChangeType = "added"
	Deleted   VehicleChangeChangeType = "deleted"
	Unchanged VehicleChangeChangeType = "unchanged"
	Updated   VehicleChangeChangeType = "updated"
)

// AdditionalInsuredChange defines model for AdditionalInsuredChange.
type AdditionalInsuredChange struct {
	AdditionalInsured externalRef2.AdditionalInsured    `json:"additionalInsured"`
	ChangeType        AdditionalInsuredChangeChangeType `json:"changeType"`
	HasWoS            bool                              `json:"hasWoS"`
	OldValue          *externalRef2.AdditionalInsured   `json:"oldValue,omitempty"`
}

// AdditionalInsuredChangeChangeType defines model for AdditionalInsuredChange.ChangeType.
type AdditionalInsuredChangeChangeType string

// AdditionalInsuredWithWoS defines model for AdditionalInsuredWithWoS.
type AdditionalInsuredWithWoS struct {
	AdditionalInsured externalRef2.AdditionalInsured `json:"additionalInsured"`
	HasWoS            bool                           `json:"hasWoS"`
}

// AddressChange defines model for AddressChange.
type AddressChange struct {
	Address    externalRef2.Address    `json:"address"`
	ChangeType AddressChangeChangeType `json:"changeType"`
	OldValue   *externalRef2.Address   `json:"oldValue,omitempty"`
}

// AddressChangeChangeType defines model for AddressChange.ChangeType.
type AddressChangeChangeType string

// CoverageGroup defines model for CoverageGroup.
type CoverageGroup struct {
	Coverage     *externalRef0.Coverage     `json:"coverage,omitempty"`
	SubCoverages []externalRef0.SubCoverage `json:"subCoverages"`
}

// CreateEndorsementRequestBody defines model for CreateEndorsementRequestBody.
type CreateEndorsementRequestBody struct {
	ProgramType externalRef0.ProgramType `json:"programType"`
}

// CreateEndorsementResponse defines model for CreateEndorsementResponse.
type CreateEndorsementResponse struct {
	EndorsementRequestID string `json:"endorsementRequestID"`
}

// DriverChange defines model for DriverChange.
type DriverChange struct {
	ChangeType DriverChangeChangeType `json:"changeType"`
	Driver     externalRef1.Driver    `json:"driver"`
	OldValue   *externalRef1.Driver   `json:"oldValue,omitempty"`
}

// DriverChangeChangeType defines model for DriverChange.ChangeType.
type DriverChangeChangeType string

// EffectiveDateEntry defines model for EffectiveDateEntry.
type EffectiveDateEntry struct {
	// ChangeID Identifier for the specific change
	ChangeID       string             `json:"changeID"`
	EffectiveDate  openapi_types.Date `json:"effectiveDate"`
	ExpirationDate openapi_types.Date `json:"expirationDate"`
}

// EndorsementChange defines model for EndorsementChange.
type EndorsementChange struct {
	ChangeType   EndorsementChangeType `json:"changeType"`
	Descriptions []string              `json:"descriptions"`
}

// EndorsementChangeType defines model for EndorsementChangeType.
type EndorsementChangeType string

// EndorsementPrice defines model for EndorsementPrice.
type EndorsementPrice struct {
	FlatCharge               *float64                   `json:"flatCharge,omitempty"`
	StateSurcharge           *float64                   `json:"stateSurcharge,omitempty"`
	WrittenPremium           float64                    `json:"writtenPremium"`
	WrittenPremiumByCoverage []WrittenPremiumByCoverage `json:"writtenPremiumByCoverage"`
}

// EndorsementQuoteState Status of the endorsement quote
type EndorsementQuoteState string

// EndorsementRequestDetails defines model for EndorsementRequestDetails.
type EndorsementRequestDetails struct {
	EffectiveDate          *time.Time              `json:"effectiveDate,omitempty"`
	EndorsementDescription []EndorsementChange     `json:"endorsementDescription"`
	EndorsementFee         *float32                `json:"endorsementFee,omitempty"`
	EndorsementNumber      string                  `json:"endorsementNumber"`
	EndorsementRequestID   string                  `json:"endorsementRequestID"`
	State                  EndorsementRequestState `json:"state"`
}

// EndorsementRequestState defines model for EndorsementRequestState.
type EndorsementRequestState string

// GenerateQuoteRequestBody defines model for GenerateQuoteRequestBody.
type GenerateQuoteRequestBody struct {
	RunType RunType `json:"runType"`
}

// GetAllEndorsementRequestsResponse defines model for GetAllEndorsementRequestsResponse.
type GetAllEndorsementRequestsResponse struct {
	EndorsementRequests []EndorsementRequestDetails `json:"endorsementRequests"`
}

// GetEndorsementQuoteResponse defines model for GetEndorsementQuoteResponse.
type GetEndorsementQuoteResponse struct {
	Price *EndorsementPrice `json:"price,omitempty"`

	// State Status of the endorsement quote
	State EndorsementQuoteState `json:"state"`
}

// GetEndorsementRequestChargesResponseTemp defines model for GetEndorsementRequestChargesResponseTemp.
type GetEndorsementRequestChargesResponseTemp struct {
	EndorsementFees   float32             `json:"endorsementFees"`
	PerCoverageCharge []PerCoverageCharge `json:"perCoverageCharge"`
}

// GetEndorsementRequestCoveragesResponse defines model for GetEndorsementRequestCoveragesResponse.
type GetEndorsementRequestCoveragesResponse struct {
	Coverages *[]SubCoverageChangeGroup `json:"coverages,omitempty"`
}

// GetEndorsementRequestDriversResponse defines model for GetEndorsementRequestDriversResponse.
type GetEndorsementRequestDriversResponse struct {
	Drivers *[]DriverChange `json:"drivers,omitempty"`
}

// GetEndorsementRequestEquipmentsResponse defines model for GetEndorsementRequestEquipmentsResponse.
type GetEndorsementRequestEquipmentsResponse struct {
	Equipments *[]VehicleChange `json:"equipments,omitempty"`
}

// GetEndorsementRequestMiscellaneousResponse defines model for GetEndorsementRequestMiscellaneousResponse.
type GetEndorsementRequestMiscellaneousResponse struct {
	AdditionalInsureds            []AdditionalInsuredChange `json:"additionalInsureds"`
	HasBlanketAdditionalInsured   bool                      `json:"hasBlanketAdditionalInsured"`
	HasBlanketWaiverOfSubrogation bool                      `json:"hasBlanketWaiverOfSubrogation"`
	MailingAddress                AddressChange             `json:"mailingAddress"`
	TerminalAddress               AddressChange             `json:"terminalAddress"`
}

// GetEndorsementRequestResponse defines model for GetEndorsementRequestResponse.
type GetEndorsementRequestResponse struct {
	BundleDuration               externalRef1.BundleDuration `json:"bundleDuration"`
	Changes                      *[]EndorsementChange        `json:"changes,omitempty"`
	DefaultEffectiveDate         *time.Time                  `json:"defaultEffectiveDate,omitempty"`
	DefaultExpirationDate        *time.Time                  `json:"defaultExpirationDate,omitempty"`
	EffectiveDates               []EffectiveDateEntry        `json:"effectiveDates"`
	Insured                      externalRef1.Insured        `json:"insured"`
	ProvisionalEndorsementNumber string                      `json:"provisionalEndorsementNumber"`
	State                        EndorsementRequestState     `json:"state"`
	UnderwriterInfo              externalRef2.UserInfo       `json:"underwriterInfo"`
}

// PatchEndorsementRequestCoveragesRequestBody defines model for PatchEndorsementRequestCoveragesRequestBody.
type PatchEndorsementRequestCoveragesRequestBody struct {
	Coverages     []CoverageGroup `json:"coverages"`
	EffectiveDate *time.Time      `json:"effectiveDate,omitempty"`
}

// PatchEndorsementRequestDriversRequestBody defines model for PatchEndorsementRequestDriversRequestBody.
type PatchEndorsementRequestDriversRequestBody struct {
	Drivers       []externalRef1.Driver `json:"drivers"`
	EffectiveDate *time.Time            `json:"effectiveDate,omitempty"`
}

// PatchEndorsementRequestEquipmentsRequestBody defines model for PatchEndorsementRequestEquipmentsRequestBody.
type PatchEndorsementRequestEquipmentsRequestBody struct {
	EffectiveDate *time.Time             `json:"effectiveDate,omitempty"`
	Vehicles      []externalRef1.Vehicle `json:"vehicles"`
}

// PatchEndorsementRequestMiscellaneousRequestBody defines model for PatchEndorsementRequestMiscellaneousRequestBody.
type PatchEndorsementRequestMiscellaneousRequestBody struct {
	AdditionalInsureds *[]AdditionalInsuredWithWoS `json:"additionalInsureds,omitempty"`
	EffectiveDate      *time.Time                  `json:"effectiveDate,omitempty"`
	MailingAddress     *externalRef2.Address       `json:"mailingAddress,omitempty"`
	TerminalAddress    *externalRef2.Address       `json:"terminalAddress,omitempty"`
}

// PerCoverageCharge defines model for PerCoverageCharge.
type PerCoverageCharge struct {
	Charge   float32 `json:"charge"`
	Coverage string  `json:"coverage"`
}

// RunType defines model for RunType.
type RunType string

// SubCoverageChange defines model for SubCoverageChange.
type SubCoverageChange struct {
	ChangeType  SubCoverageChangeChangeType `json:"changeType"`
	OldValue    *externalRef0.SubCoverage   `json:"oldValue,omitempty"`
	SubCoverage externalRef0.SubCoverage    `json:"subCoverage"`
}

// SubCoverageChangeChangeType defines model for SubCoverageChange.ChangeType.
type SubCoverageChangeChangeType string

// SubCoverageChangeGroup defines model for SubCoverageChangeGroup.
type SubCoverageChangeGroup struct {
	Coverage     externalRef0.Coverage `json:"coverage"`
	SubCoverages []SubCoverageChange   `json:"subCoverages"`
}

// SubmitEndorsementRequestForReviewRequestBody defines model for SubmitEndorsementRequestForReviewRequestBody.
type SubmitEndorsementRequestForReviewRequestBody struct {
	// IsQuoteRequested Is quote requested by agent for the endorsement request
	IsQuoteRequested *bool `json:"isQuoteRequested,omitempty"`
}

// UpdateEndorsementRequestRequestBody defines model for UpdateEndorsementRequestRequestBody.
type UpdateEndorsementRequestRequestBody struct {
	DefaultEffectiveDate time.Time `json:"defaultEffectiveDate"`
}

// VehicleChange defines model for VehicleChange.
type VehicleChange struct {
	ChangeType VehicleChangeChangeType `json:"changeType"`
	OldValue   *externalRef1.Vehicle   `json:"oldValue,omitempty"`
	Vehicle    externalRef1.Vehicle    `json:"vehicle"`
}

// VehicleChangeChangeType defines model for VehicleChange.ChangeType.
type VehicleChangeChangeType string

// WrittenPremiumByCoverage defines model for WrittenPremiumByCoverage.
type WrittenPremiumByCoverage struct {
	Coverage       externalRef0.CoverageType `json:"coverage"`
	WrittenPremium float64                   `json:"writtenPremium"`
}

// EndorsementRequestID defines model for EndorsementRequestID.
type EndorsementRequestID = string
