// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_core/insurance_carrier.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InsuranceCarrier int32

const (
	InsuranceCarrier_InsuranceCarrierFallsLake              InsuranceCarrier = 0
	InsuranceCarrier_InsuranceCarrier_Empty                 InsuranceCarrier = 1
	InsuranceCarrier_InsuranceCarrier_SiriusPoint           InsuranceCarrier = 2
	InsuranceCarrier_InsuranceCarrier_FronterX              InsuranceCarrier = 3
	InsuranceCarrier_InsuranceCarrier_MSTransverse          InsuranceCarrier = 4
	InsuranceCarrier_InsuranceCarrier_SiriusPointSpeciality InsuranceCarrier = 5
)

// Enum value maps for InsuranceCarrier.
var (
	InsuranceCarrier_name = map[int32]string{
		0: "InsuranceCarrierFallsLake",
		1: "InsuranceCarrier_Empty",
		2: "InsuranceCarrier_SiriusPoint",
		3: "InsuranceCarrier_FronterX",
		4: "InsuranceCarrier_MSTransverse",
		5: "InsuranceCarrier_SiriusPointSpeciality",
	}
	InsuranceCarrier_value = map[string]int32{
		"InsuranceCarrierFallsLake":              0,
		"InsuranceCarrier_Empty":                 1,
		"InsuranceCarrier_SiriusPoint":           2,
		"InsuranceCarrier_FronterX":              3,
		"InsuranceCarrier_MSTransverse":          4,
		"InsuranceCarrier_SiriusPointSpeciality": 5,
	}
)

func (x InsuranceCarrier) Enum() *InsuranceCarrier {
	p := new(InsuranceCarrier)
	*p = x
	return p
}

func (x InsuranceCarrier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InsuranceCarrier) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_core_insurance_carrier_proto_enumTypes[0].Descriptor()
}

func (InsuranceCarrier) Type() protoreflect.EnumType {
	return &file_insurance_core_insurance_carrier_proto_enumTypes[0]
}

func (x InsuranceCarrier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InsuranceCarrier.Descriptor instead.
func (InsuranceCarrier) EnumDescriptor() ([]byte, []int) {
	return file_insurance_core_insurance_carrier_proto_rawDescGZIP(), []int{0}
}

type CarrierAdmittedType int32

const (
	CarrierAdmittedType_CarrierAdmittedType_Unspecified CarrierAdmittedType = 0
	CarrierAdmittedType_CarrierAdmittedType_Admitted    CarrierAdmittedType = 1
	CarrierAdmittedType_CarrierAdmittedType_NonAdmitted CarrierAdmittedType = 2
)

// Enum value maps for CarrierAdmittedType.
var (
	CarrierAdmittedType_name = map[int32]string{
		0: "CarrierAdmittedType_Unspecified",
		1: "CarrierAdmittedType_Admitted",
		2: "CarrierAdmittedType_NonAdmitted",
	}
	CarrierAdmittedType_value = map[string]int32{
		"CarrierAdmittedType_Unspecified": 0,
		"CarrierAdmittedType_Admitted":    1,
		"CarrierAdmittedType_NonAdmitted": 2,
	}
)

func (x CarrierAdmittedType) Enum() *CarrierAdmittedType {
	p := new(CarrierAdmittedType)
	*p = x
	return p
}

func (x CarrierAdmittedType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CarrierAdmittedType) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_core_insurance_carrier_proto_enumTypes[1].Descriptor()
}

func (CarrierAdmittedType) Type() protoreflect.EnumType {
	return &file_insurance_core_insurance_carrier_proto_enumTypes[1]
}

func (x CarrierAdmittedType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CarrierAdmittedType.Descriptor instead.
func (CarrierAdmittedType) EnumDescriptor() ([]byte, []int) {
	return file_insurance_core_insurance_carrier_proto_rawDescGZIP(), []int{1}
}

var File_insurance_core_insurance_carrier_proto protoreflect.FileDescriptor

var file_insurance_core_insurance_carrier_proto_rawDesc = []byte{
	0x0a, 0x26, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2a, 0xdd, 0x01, 0x0a, 0x10, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x1d, 0x0a,
	0x19, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x46, 0x61, 0x6c, 0x6c, 0x73, 0x4c, 0x61, 0x6b, 0x65, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x5f, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x53, 0x69, 0x72,
	0x69, 0x75, 0x73, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x46,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x72, 0x58, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x4d, 0x53,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x76, 0x65, 0x72, 0x73, 0x65, 0x10, 0x04, 0x12, 0x2a, 0x0a, 0x26,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x5f, 0x53, 0x69, 0x72, 0x69, 0x75, 0x73, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x10, 0x05, 0x2a, 0x81, 0x01, 0x0a, 0x13, 0x43, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x23, 0x0a, 0x1f, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x41, 0x64, 0x6d, 0x69, 0x74,
	0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x64, 0x6d,
	0x69, 0x74, 0x74, 0x65, 0x64, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e,
	0x6f, 0x6e, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x10, 0x02, 0x42, 0x2e, 0x5a, 0x2c,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x2d, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insurance_core_insurance_carrier_proto_rawDescOnce sync.Once
	file_insurance_core_insurance_carrier_proto_rawDescData = file_insurance_core_insurance_carrier_proto_rawDesc
)

func file_insurance_core_insurance_carrier_proto_rawDescGZIP() []byte {
	file_insurance_core_insurance_carrier_proto_rawDescOnce.Do(func() {
		file_insurance_core_insurance_carrier_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_core_insurance_carrier_proto_rawDescData)
	})
	return file_insurance_core_insurance_carrier_proto_rawDescData
}

var file_insurance_core_insurance_carrier_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_insurance_core_insurance_carrier_proto_goTypes = []interface{}{
	(InsuranceCarrier)(0),    // 0: insurance_core.InsuranceCarrier
	(CarrierAdmittedType)(0), // 1: insurance_core.CarrierAdmittedType
}
var file_insurance_core_insurance_carrier_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_insurance_core_insurance_carrier_proto_init() }
func file_insurance_core_insurance_carrier_proto_init() {
	if File_insurance_core_insurance_carrier_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_core_insurance_carrier_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_core_insurance_carrier_proto_goTypes,
		DependencyIndexes: file_insurance_core_insurance_carrier_proto_depIdxs,
		EnumInfos:         file_insurance_core_insurance_carrier_proto_enumTypes,
	}.Build()
	File_insurance_core_insurance_carrier_proto = out.File
	file_insurance_core_insurance_carrier_proto_rawDesc = nil
	file_insurance_core_insurance_carrier_proto_goTypes = nil
	file_insurance_core_insurance_carrier_proto_depIdxs = nil
}
