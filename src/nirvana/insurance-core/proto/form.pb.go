// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_core/form.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FormCompilationType int32

const (
	FormCompilationType_FormCompilationTypeUnknown         FormCompilationType = 0
	FormCompilationType_FormCompilationTypePolicy          FormCompilationType = 1
	FormCompilationType_FormCompilationTypeSignaturePacket FormCompilationType = 2
	FormCompilationType_FormCompilationTypeEndorsement     FormCompilationType = 3
)

// Enum value maps for FormCompilationType.
var (
	FormCompilationType_name = map[int32]string{
		0: "FormCompilationTypeUnknown",
		1: "FormCompilationTypePolicy",
		2: "FormCompilationTypeSignaturePacket",
		3: "FormCompilationTypeEndorsement",
	}
	FormCompilationType_value = map[string]int32{
		"FormCompilationTypeUnknown":         0,
		"FormCompilationTypePolicy":          1,
		"FormCompilationTypeSignaturePacket": 2,
		"FormCompilationTypeEndorsement":     3,
	}
)

func (x FormCompilationType) Enum() *FormCompilationType {
	p := new(FormCompilationType)
	*p = x
	return p
}

func (x FormCompilationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FormCompilationType) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_core_form_proto_enumTypes[0].Descriptor()
}

func (FormCompilationType) Type() protoreflect.EnumType {
	return &file_insurance_core_form_proto_enumTypes[0]
}

func (x FormCompilationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FormCompilationType.Descriptor instead.
func (FormCompilationType) EnumDescriptor() ([]byte, []int) {
	return file_insurance_core_form_proto_rawDescGZIP(), []int{0}
}

type FormCore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FormCompilationId   string              `protobuf:"bytes,1,opt,name=formCompilationId,proto3" json:"formCompilationId,omitempty"`
	FormCompilationType FormCompilationType `protobuf:"varint,2,opt,name=formCompilationType,proto3,enum=insurance_core.FormCompilationType" json:"formCompilationType,omitempty"`
	DocumentHandleId    string              `protobuf:"bytes,3,opt,name=documentHandleId,proto3" json:"documentHandleId,omitempty"`
	FormCodes           []string            `protobuf:"bytes,4,rep,name=formCodes,proto3" json:"formCodes,omitempty"`
}

func (x *FormCore) Reset() {
	*x = FormCore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_core_form_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormCore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormCore) ProtoMessage() {}

func (x *FormCore) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_core_form_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormCore.ProtoReflect.Descriptor instead.
func (*FormCore) Descriptor() ([]byte, []int) {
	return file_insurance_core_form_proto_rawDescGZIP(), []int{0}
}

func (x *FormCore) GetFormCompilationId() string {
	if x != nil {
		return x.FormCompilationId
	}
	return ""
}

func (x *FormCore) GetFormCompilationType() FormCompilationType {
	if x != nil {
		return x.FormCompilationType
	}
	return FormCompilationType_FormCompilationTypeUnknown
}

func (x *FormCore) GetDocumentHandleId() string {
	if x != nil {
		return x.DocumentHandleId
	}
	return ""
}

func (x *FormCore) GetFormCodes() []string {
	if x != nil {
		return x.FormCodes
	}
	return nil
}

type FormInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoreForm        *FormCore   `protobuf:"bytes,1,opt,name=coreForm,proto3" json:"coreForm,omitempty"`
	AdditionalForms []*FormCore `protobuf:"bytes,2,rep,name=additionalForms,proto3" json:"additionalForms,omitempty"`
}

func (x *FormInfo) Reset() {
	*x = FormInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_core_form_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormInfo) ProtoMessage() {}

func (x *FormInfo) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_core_form_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormInfo.ProtoReflect.Descriptor instead.
func (*FormInfo) Descriptor() ([]byte, []int) {
	return file_insurance_core_form_proto_rawDescGZIP(), []int{1}
}

func (x *FormInfo) GetCoreForm() *FormCore {
	if x != nil {
		return x.CoreForm
	}
	return nil
}

func (x *FormInfo) GetAdditionalForms() []*FormCore {
	if x != nil {
		return x.AdditionalForms
	}
	return nil
}

var File_insurance_core_form_proto protoreflect.FileDescriptor

var file_insurance_core_form_proto_rawDesc = []byte{
	0x0a, 0x19, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xd9, 0x01, 0x0a, 0x08,
	0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x72, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x66, 0x6f, 0x72, 0x6d,
	0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x66, 0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x13, 0x66, 0x6f, 0x72, 0x6d, 0x43, 0x6f,
	0x6d, 0x70, 0x69, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x66, 0x6f, 0x72, 0x6d, 0x43, 0x6f,
	0x6d, 0x70, 0x69, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a,
	0x10, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x6f, 0x72,
	0x6d, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x66, 0x6f,
	0x72, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x84, 0x01, 0x0a, 0x08, 0x46, 0x6f, 0x72, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a, 0x08, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x6f, 0x72, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x72, 0x65,
	0x52, 0x08, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x42, 0x0a, 0x0f, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x72, 0x65, 0x52, 0x0f, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x2a, 0xa0,
	0x01, 0x0a, 0x13, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f,
	0x6d, 0x70, 0x69, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f,
	0x6d, 0x70, 0x69, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x6d,
	0x70, 0x69, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x53, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x10, 0x02, 0x12, 0x22, 0x0a,
	0x1e, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x10,
	0x03, 0x42, 0x2e, 0x5a, 0x2c, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x2d, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insurance_core_form_proto_rawDescOnce sync.Once
	file_insurance_core_form_proto_rawDescData = file_insurance_core_form_proto_rawDesc
)

func file_insurance_core_form_proto_rawDescGZIP() []byte {
	file_insurance_core_form_proto_rawDescOnce.Do(func() {
		file_insurance_core_form_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_core_form_proto_rawDescData)
	})
	return file_insurance_core_form_proto_rawDescData
}

var file_insurance_core_form_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_insurance_core_form_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_insurance_core_form_proto_goTypes = []interface{}{
	(FormCompilationType)(0), // 0: insurance_core.FormCompilationType
	(*FormCore)(nil),         // 1: insurance_core.FormCore
	(*FormInfo)(nil),         // 2: insurance_core.FormInfo
}
var file_insurance_core_form_proto_depIdxs = []int32{
	0, // 0: insurance_core.FormCore.formCompilationType:type_name -> insurance_core.FormCompilationType
	1, // 1: insurance_core.FormInfo.coreForm:type_name -> insurance_core.FormCore
	1, // 2: insurance_core.FormInfo.additionalForms:type_name -> insurance_core.FormCore
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_insurance_core_form_proto_init() }
func file_insurance_core_form_proto_init() {
	if File_insurance_core_form_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_insurance_core_form_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormCore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_core_form_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_core_form_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_core_form_proto_goTypes,
		DependencyIndexes: file_insurance_core_form_proto_depIdxs,
		EnumInfos:         file_insurance_core_form_proto_enumTypes,
		MessageInfos:      file_insurance_core_form_proto_msgTypes,
	}.Build()
	File_insurance_core_form_proto = out.File
	file_insurance_core_form_proto_rawDesc = nil
	file_insurance_core_form_proto_goTypes = nil
	file_insurance_core_form_proto_depIdxs = nil
}
