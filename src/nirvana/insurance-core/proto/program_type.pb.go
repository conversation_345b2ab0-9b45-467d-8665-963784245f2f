// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_core/program_type.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProgramType int32

const (
	ProgramType_ProgramType_Invalid          ProgramType = 0
	ProgramType_ProgramType_Fleet            ProgramType = 1
	ProgramType_ProgramType_CanopiusNRB      ProgramType = 2
	ProgramType_ProgramType_NonFleetAdmitted ProgramType = 3
	ProgramType_ProgramType_BusinessAuto     ProgramType = 4
)

// Enum value maps for ProgramType.
var (
	ProgramType_name = map[int32]string{
		0: "ProgramType_Invalid",
		1: "ProgramType_Fleet",
		2: "ProgramType_CanopiusNRB",
		3: "ProgramType_NonFleetAdmitted",
		4: "ProgramType_BusinessAuto",
	}
	ProgramType_value = map[string]int32{
		"ProgramType_Invalid":          0,
		"ProgramType_Fleet":            1,
		"ProgramType_CanopiusNRB":      2,
		"ProgramType_NonFleetAdmitted": 3,
		"ProgramType_BusinessAuto":     4,
	}
)

func (x ProgramType) Enum() *ProgramType {
	p := new(ProgramType)
	*p = x
	return p
}

func (x ProgramType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProgramType) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_core_program_type_proto_enumTypes[0].Descriptor()
}

func (ProgramType) Type() protoreflect.EnumType {
	return &file_insurance_core_program_type_proto_enumTypes[0]
}

func (x ProgramType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProgramType.Descriptor instead.
func (ProgramType) EnumDescriptor() ([]byte, []int) {
	return file_insurance_core_program_type_proto_rawDescGZIP(), []int{0}
}

var File_insurance_core_program_type_proto protoreflect.FileDescriptor

var file_insurance_core_program_type_proto_rawDesc = []byte{
	0x0a, 0x21, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2a, 0x9a, 0x01, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x6c, 0x65, 0x65,
	0x74, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x43, 0x61, 0x6e, 0x6f, 0x70, 0x69, 0x75, 0x73, 0x4e, 0x52, 0x42, 0x10, 0x02,
	0x12, 0x20, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64,
	0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x10, 0x04,
	0x42, 0x2e, 0x5a, 0x2c, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x69, 0x6e, 0x73, 0x75,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x2d, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insurance_core_program_type_proto_rawDescOnce sync.Once
	file_insurance_core_program_type_proto_rawDescData = file_insurance_core_program_type_proto_rawDesc
)

func file_insurance_core_program_type_proto_rawDescGZIP() []byte {
	file_insurance_core_program_type_proto_rawDescOnce.Do(func() {
		file_insurance_core_program_type_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_core_program_type_proto_rawDescData)
	})
	return file_insurance_core_program_type_proto_rawDescData
}

var file_insurance_core_program_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_insurance_core_program_type_proto_goTypes = []interface{}{
	(ProgramType)(0), // 0: insurance_core.ProgramType
}
var file_insurance_core_program_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_insurance_core_program_type_proto_init() }
func file_insurance_core_program_type_proto_init() {
	if File_insurance_core_program_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_core_program_type_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_core_program_type_proto_goTypes,
		DependencyIndexes: file_insurance_core_program_type_proto_depIdxs,
		EnumInfos:         file_insurance_core_program_type_proto_enumTypes,
	}.Build()
	File_insurance_core_program_type_proto = out.File
	file_insurance_core_program_type_proto_rawDesc = nil
	file_insurance_core_program_type_proto_goTypes = nil
	file_insurance_core_program_type_proto_depIdxs = nil
}
