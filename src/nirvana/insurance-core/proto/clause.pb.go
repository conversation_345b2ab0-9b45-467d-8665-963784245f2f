// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_core/clause.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ClauseType int32

const (
	ClauseType_ClauseTypeUnknown                                          ClauseType = 0
	ClauseType_ClauseTypeAdditionalInsured                                ClauseType = 1
	ClauseType_ClauseTypeWaiverOfSubrogation                              ClauseType = 2
	ClauseType_ClauseTypeUIIA                                             ClauseType = 3
	ClauseType_ClauseTypeSpecifiedAdditionalInsured                       ClauseType = 4
	ClauseType_ClauseTypeSpecifiedWaiverOfSubrogation                     ClauseType = 5
	ClauseType_ClauseTypeSpecifiedAdditionalInsuredPrimaryNonContributory ClauseType = 6
)

// Enum value maps for ClauseType.
var (
	ClauseType_name = map[int32]string{
		0: "ClauseTypeUnknown",
		1: "ClauseTypeAdditionalInsured",
		2: "ClauseTypeWaiverOfSubrogation",
		3: "ClauseTypeUIIA",
		4: "ClauseTypeSpecifiedAdditionalInsured",
		5: "ClauseTypeSpecifiedWaiverOfSubrogation",
		6: "ClauseTypeSpecifiedAdditionalInsuredPrimaryNonContributory",
	}
	ClauseType_value = map[string]int32{
		"ClauseTypeUnknown":                                          0,
		"ClauseTypeAdditionalInsured":                                1,
		"ClauseTypeWaiverOfSubrogation":                              2,
		"ClauseTypeUIIA":                                             3,
		"ClauseTypeSpecifiedAdditionalInsured":                       4,
		"ClauseTypeSpecifiedWaiverOfSubrogation":                     5,
		"ClauseTypeSpecifiedAdditionalInsuredPrimaryNonContributory": 6,
	}
)

func (x ClauseType) Enum() *ClauseType {
	p := new(ClauseType)
	*p = x
	return p
}

func (x ClauseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClauseType) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_core_clause_proto_enumTypes[0].Descriptor()
}

func (ClauseType) Type() protoreflect.EnumType {
	return &file_insurance_core_clause_proto_enumTypes[0]
}

func (x ClauseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClauseType.Descriptor instead.
func (ClauseType) EnumDescriptor() ([]byte, []int) {
	return file_insurance_core_clause_proto_rawDescGZIP(), []int{0}
}

type ParticipantScopeApplicabilityType int32

const (
	ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeUnknown  ParticipantScopeApplicabilityType = 0
	ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeBlanket  ParticipantScopeApplicabilityType = 1
	ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeSpecific ParticipantScopeApplicabilityType = 2
)

// Enum value maps for ParticipantScopeApplicabilityType.
var (
	ParticipantScopeApplicabilityType_name = map[int32]string{
		0: "ParticipantScopeApplicabilityTypeUnknown",
		1: "ParticipantScopeApplicabilityTypeBlanket",
		2: "ParticipantScopeApplicabilityTypeSpecific",
	}
	ParticipantScopeApplicabilityType_value = map[string]int32{
		"ParticipantScopeApplicabilityTypeUnknown":  0,
		"ParticipantScopeApplicabilityTypeBlanket":  1,
		"ParticipantScopeApplicabilityTypeSpecific": 2,
	}
)

func (x ParticipantScopeApplicabilityType) Enum() *ParticipantScopeApplicabilityType {
	p := new(ParticipantScopeApplicabilityType)
	*p = x
	return p
}

func (x ParticipantScopeApplicabilityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParticipantScopeApplicabilityType) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_core_clause_proto_enumTypes[1].Descriptor()
}

func (ParticipantScopeApplicabilityType) Type() protoreflect.EnumType {
	return &file_insurance_core_clause_proto_enumTypes[1]
}

func (x ParticipantScopeApplicabilityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParticipantScopeApplicabilityType.Descriptor instead.
func (ParticipantScopeApplicabilityType) EnumDescriptor() ([]byte, []int) {
	return file_insurance_core_clause_proto_rawDescGZIP(), []int{1}
}

type ParticipantType int32

const (
	ParticipantType_ParticipantTypeUnknown ParticipantType = 0
	ParticipantType_ParticipantTypeInsured ParticipantType = 1
)

// Enum value maps for ParticipantType.
var (
	ParticipantType_name = map[int32]string{
		0: "ParticipantTypeUnknown",
		1: "ParticipantTypeInsured",
	}
	ParticipantType_value = map[string]int32{
		"ParticipantTypeUnknown": 0,
		"ParticipantTypeInsured": 1,
	}
)

func (x ParticipantType) Enum() *ParticipantType {
	p := new(ParticipantType)
	*p = x
	return p
}

func (x ParticipantType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParticipantType) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_core_clause_proto_enumTypes[2].Descriptor()
}

func (ParticipantType) Type() protoreflect.EnumType {
	return &file_insurance_core_clause_proto_enumTypes[2]
}

func (x ParticipantType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParticipantType.Descriptor instead.
func (ParticipantType) EnumDescriptor() ([]byte, []int) {
	return file_insurance_core_clause_proto_rawDescGZIP(), []int{2}
}

type ClauseList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clauses []*Clause `protobuf:"bytes,1,rep,name=clauses,proto3" json:"clauses,omitempty"`
}

func (x *ClauseList) Reset() {
	*x = ClauseList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_core_clause_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClauseList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClauseList) ProtoMessage() {}

func (x *ClauseList) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_core_clause_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClauseList.ProtoReflect.Descriptor instead.
func (*ClauseList) Descriptor() ([]byte, []int) {
	return file_insurance_core_clause_proto_rawDescGZIP(), []int{0}
}

func (x *ClauseList) GetClauses() []*Clause {
	if x != nil {
		return x.Clauses
	}
	return nil
}

type Clause struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               *ClauseId         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type             ClauseType        `protobuf:"varint,2,opt,name=type,proto3,enum=insurance_core.ClauseType" json:"type,omitempty"`
	ParticipantScope *ParticipantScope `protobuf:"bytes,3,opt,name=participantScope,proto3" json:"participantScope,omitempty"`
}

func (x *Clause) Reset() {
	*x = Clause{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_core_clause_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Clause) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Clause) ProtoMessage() {}

func (x *Clause) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_core_clause_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Clause.ProtoReflect.Descriptor instead.
func (*Clause) Descriptor() ([]byte, []int) {
	return file_insurance_core_clause_proto_rawDescGZIP(), []int{1}
}

func (x *Clause) GetId() *ClauseId {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *Clause) GetType() ClauseType {
	if x != nil {
		return x.Type
	}
	return ClauseType_ClauseTypeUnknown
}

func (x *Clause) GetParticipantScope() *ParticipantScope {
	if x != nil {
		return x.ParticipantScope
	}
	return nil
}

type ClauseId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ClauseId) Reset() {
	*x = ClauseId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_core_clause_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClauseId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClauseId) ProtoMessage() {}

func (x *ClauseId) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_core_clause_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClauseId.ProtoReflect.Descriptor instead.
func (*ClauseId) Descriptor() ([]byte, []int) {
	return file_insurance_core_clause_proto_rawDescGZIP(), []int{2}
}

func (x *ClauseId) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ParticipantScope struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                   ParticipantScopeApplicabilityType `protobuf:"varint,1,opt,name=type,proto3,enum=insurance_core.ParticipantScopeApplicabilityType" json:"type,omitempty"`
	ApplicableParticipants []*Participant                    `protobuf:"bytes,2,rep,name=applicableParticipants,proto3" json:"applicableParticipants,omitempty"`
}

func (x *ParticipantScope) Reset() {
	*x = ParticipantScope{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_core_clause_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParticipantScope) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParticipantScope) ProtoMessage() {}

func (x *ParticipantScope) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_core_clause_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParticipantScope.ProtoReflect.Descriptor instead.
func (*ParticipantScope) Descriptor() ([]byte, []int) {
	return file_insurance_core_clause_proto_rawDescGZIP(), []int{3}
}

func (x *ParticipantScope) GetType() ParticipantScopeApplicabilityType {
	if x != nil {
		return x.Type
	}
	return ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeUnknown
}

func (x *ParticipantScope) GetApplicableParticipants() []*Participant {
	if x != nil {
		return x.ApplicableParticipants
	}
	return nil
}

type Participant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type ParticipantType `protobuf:"varint,1,opt,name=type,proto3,enum=insurance_core.ParticipantType" json:"type,omitempty"`
	Id   string          `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *Participant) Reset() {
	*x = Participant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_core_clause_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Participant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Participant) ProtoMessage() {}

func (x *Participant) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_core_clause_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Participant.ProtoReflect.Descriptor instead.
func (*Participant) Descriptor() ([]byte, []int) {
	return file_insurance_core_clause_proto_rawDescGZIP(), []int{4}
}

func (x *Participant) GetType() ParticipantType {
	if x != nil {
		return x.Type
	}
	return ParticipantType_ParticipantTypeUnknown
}

func (x *Participant) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_insurance_core_clause_proto protoreflect.FileDescriptor

var file_insurance_core_clause_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x63, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x3e, 0x0a,
	0x0a, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x07, 0x63,
	0x6c, 0x61, 0x75, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x6c,
	0x61, 0x75, 0x73, 0x65, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x73, 0x22, 0xb0, 0x01,
	0x0a, 0x06, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x49, 0x64, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x4c, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e,
	0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x50, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x10,
	0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65,
	0x22, 0x1a, 0x0a, 0x08, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xae, 0x01, 0x0a,
	0x10, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x70,
	0x65, 0x12, 0x45, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x31, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x70,
	0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x69, 0x70, 0x61, 0x6e, 0x74, 0x52, 0x16, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c,
	0x65, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x73, 0x22, 0x52, 0x0a,
	0x0b, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74,
	0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x2a, 0x91, 0x02, 0x0a, 0x0a, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x15, 0x0a, 0x11, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x6c, 0x61, 0x75, 0x73,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x6c, 0x61, 0x75,
	0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75,
	0x62, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x43,
	0x6c, 0x61, 0x75, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x55, 0x49, 0x49, 0x41, 0x10, 0x03, 0x12,
	0x28, 0x0a, 0x24, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x10, 0x04, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x6c, 0x61,
	0x75, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f, 0x67, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x10, 0x05, 0x12, 0x3e, 0x0a, 0x3a, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x50, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x6f, 0x72, 0x79, 0x10, 0x06, 0x2a, 0xae, 0x01, 0x0a, 0x21, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x28, 0x50,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x2c, 0x0a, 0x28, 0x50, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x6c,
	0x61, 0x6e, 0x6b, 0x65, 0x74, 0x10, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x10, 0x02, 0x2a, 0x49, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x69, 0x70, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69,
	0x70, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x10,
	0x01, 0x42, 0x2e, 0x5a, 0x2c, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x2d, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insurance_core_clause_proto_rawDescOnce sync.Once
	file_insurance_core_clause_proto_rawDescData = file_insurance_core_clause_proto_rawDesc
)

func file_insurance_core_clause_proto_rawDescGZIP() []byte {
	file_insurance_core_clause_proto_rawDescOnce.Do(func() {
		file_insurance_core_clause_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_core_clause_proto_rawDescData)
	})
	return file_insurance_core_clause_proto_rawDescData
}

var file_insurance_core_clause_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_insurance_core_clause_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_insurance_core_clause_proto_goTypes = []interface{}{
	(ClauseType)(0),                        // 0: insurance_core.ClauseType
	(ParticipantScopeApplicabilityType)(0), // 1: insurance_core.ParticipantScopeApplicabilityType
	(ParticipantType)(0),                   // 2: insurance_core.ParticipantType
	(*ClauseList)(nil),                     // 3: insurance_core.ClauseList
	(*Clause)(nil),                         // 4: insurance_core.Clause
	(*ClauseId)(nil),                       // 5: insurance_core.ClauseId
	(*ParticipantScope)(nil),               // 6: insurance_core.ParticipantScope
	(*Participant)(nil),                    // 7: insurance_core.Participant
}
var file_insurance_core_clause_proto_depIdxs = []int32{
	4, // 0: insurance_core.ClauseList.clauses:type_name -> insurance_core.Clause
	5, // 1: insurance_core.Clause.id:type_name -> insurance_core.ClauseId
	0, // 2: insurance_core.Clause.type:type_name -> insurance_core.ClauseType
	6, // 3: insurance_core.Clause.participantScope:type_name -> insurance_core.ParticipantScope
	1, // 4: insurance_core.ParticipantScope.type:type_name -> insurance_core.ParticipantScopeApplicabilityType
	7, // 5: insurance_core.ParticipantScope.applicableParticipants:type_name -> insurance_core.Participant
	2, // 6: insurance_core.Participant.type:type_name -> insurance_core.ParticipantType
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_insurance_core_clause_proto_init() }
func file_insurance_core_clause_proto_init() {
	if File_insurance_core_clause_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_insurance_core_clause_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClauseList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_core_clause_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Clause); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_core_clause_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClauseId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_core_clause_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParticipantScope); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_core_clause_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Participant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_core_clause_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_core_clause_proto_goTypes,
		DependencyIndexes: file_insurance_core_clause_proto_depIdxs,
		EnumInfos:         file_insurance_core_clause_proto_enumTypes,
		MessageInfos:      file_insurance_core_clause_proto_msgTypes,
	}.Build()
	File_insurance_core_clause_proto = out.File
	file_insurance_core_clause_proto_rawDesc = nil
	file_insurance_core_clause_proto_goTypes = nil
	file_insurance_core_clause_proto_depIdxs = nil
}
