// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_core/seller.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SellerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgencyID string `protobuf:"bytes,1,opt,name=agencyID,proto3" json:"agencyID,omitempty"`
}

func (x *SellerInfo) Reset() {
	*x = SellerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_core_seller_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellerInfo) ProtoMessage() {}

func (x *SellerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_core_seller_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellerInfo.ProtoReflect.Descriptor instead.
func (*SellerInfo) Descriptor() ([]byte, []int) {
	return file_insurance_core_seller_proto_rawDescGZIP(), []int{0}
}

func (x *SellerInfo) GetAgencyID() string {
	if x != nil {
		return x.AgencyID
	}
	return ""
}

var File_insurance_core_seller_proto protoreflect.FileDescriptor

var file_insurance_core_seller_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x28, 0x0a,
	0x0a, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x61,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x44, 0x42, 0x2e, 0x5a, 0x2c, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x2d, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insurance_core_seller_proto_rawDescOnce sync.Once
	file_insurance_core_seller_proto_rawDescData = file_insurance_core_seller_proto_rawDesc
)

func file_insurance_core_seller_proto_rawDescGZIP() []byte {
	file_insurance_core_seller_proto_rawDescOnce.Do(func() {
		file_insurance_core_seller_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_core_seller_proto_rawDescData)
	})
	return file_insurance_core_seller_proto_rawDescData
}

var file_insurance_core_seller_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_insurance_core_seller_proto_goTypes = []interface{}{
	(*SellerInfo)(nil), // 0: insurance_core.SellerInfo
}
var file_insurance_core_seller_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_insurance_core_seller_proto_init() }
func file_insurance_core_seller_proto_init() {
	if File_insurance_core_seller_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_insurance_core_seller_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_core_seller_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_core_seller_proto_goTypes,
		DependencyIndexes: file_insurance_core_seller_proto_depIdxs,
		MessageInfos:      file_insurance_core_seller_proto_msgTypes,
	}.Build()
	File_insurance_core_seller_proto = out.File
	file_insurance_core_seller_proto_rawDesc = nil
	file_insurance_core_seller_proto_goTypes = nil
	file_insurance_core_seller_proto_depIdxs = nil
}
