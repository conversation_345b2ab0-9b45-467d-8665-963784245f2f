// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_core/vehicle.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VehicleType int32

const (
	VehicleType_VEHICLE_TYPE_UNSPECIFIED VehicleType = 0
	VehicleType_VEHICLE_TYPE_TRUCK       VehicleType = 1
	VehicleType_VEHICLE_TYPE_TRACTOR     VehicleType = 2
	VehicleType_VEHICLE_TYPE_TRAILER     VehicleType = 3
	VehicleType_VEHICLE_TYPE_PICKUP      VehicleType = 4
)

// Enum value maps for VehicleType.
var (
	VehicleType_name = map[int32]string{
		0: "VEHICLE_TYPE_UNSPECIFIED",
		1: "VEHICLE_TYPE_TRUCK",
		2: "VEHICLE_TYPE_TRACTOR",
		3: "VEHICLE_TYPE_TRAILER",
		4: "VEHICLE_TYPE_PICKUP",
	}
	VehicleType_value = map[string]int32{
		"VEHICLE_TYPE_UNSPECIFIED": 0,
		"VEHICLE_TYPE_TRUCK":       1,
		"VEHICLE_TYPE_TRACTOR":     2,
		"VEHICLE_TYPE_TRAILER":     3,
		"VEHICLE_TYPE_PICKUP":      4,
	}
)

func (x VehicleType) Enum() *VehicleType {
	p := new(VehicleType)
	*p = x
	return p
}

func (x VehicleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleType) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_core_vehicle_proto_enumTypes[0].Descriptor()
}

func (VehicleType) Type() protoreflect.EnumType {
	return &file_insurance_core_vehicle_proto_enumTypes[0]
}

func (x VehicleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleType.Descriptor instead.
func (VehicleType) EnumDescriptor() ([]byte, []int) {
	return file_insurance_core_vehicle_proto_rawDescGZIP(), []int{0}
}

var File_insurance_core_vehicle_proto protoreflect.FileDescriptor

var file_insurance_core_vehicle_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2a, 0x90,
	0x01, 0x0a, 0x0b, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c,
	0x0a, 0x18, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12,
	0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x55,
	0x43, 0x4b, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x18,
	0x0a, 0x14, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54,
	0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x10,
	0x04, 0x42, 0x2e, 0x5a, 0x2c, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x2d, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insurance_core_vehicle_proto_rawDescOnce sync.Once
	file_insurance_core_vehicle_proto_rawDescData = file_insurance_core_vehicle_proto_rawDesc
)

func file_insurance_core_vehicle_proto_rawDescGZIP() []byte {
	file_insurance_core_vehicle_proto_rawDescOnce.Do(func() {
		file_insurance_core_vehicle_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_core_vehicle_proto_rawDescData)
	})
	return file_insurance_core_vehicle_proto_rawDescData
}

var file_insurance_core_vehicle_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_insurance_core_vehicle_proto_goTypes = []interface{}{
	(VehicleType)(0), // 0: insurance_core.VehicleType
}
var file_insurance_core_vehicle_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_insurance_core_vehicle_proto_init() }
func file_insurance_core_vehicle_proto_init() {
	if File_insurance_core_vehicle_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_core_vehicle_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_core_vehicle_proto_goTypes,
		DependencyIndexes: file_insurance_core_vehicle_proto_depIdxs,
		EnumInfos:         file_insurance_core_vehicle_proto_enumTypes,
	}.Build()
	File_insurance_core_vehicle_proto = out.File
	file_insurance_core_vehicle_proto_rawDesc = nil
	file_insurance_core_vehicle_proto_goTypes = nil
	file_insurance_core_vehicle_proto_depIdxs = nil
}
