// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_core/insured.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	proto "nirvanatech.com/nirvana/common-go/proto"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InsuredType int32

const (
	InsuredType_InsuredType_Invalid                       InsuredType = 0
	InsuredType_InsuredType_PrimaryInsured                InsuredType = 1
	InsuredType_InsuredType_AdditionalNamedInsured        InsuredType = 2
	InsuredType_InsuredType_DesignatedInsured             InsuredType = 3
	InsuredType_InsuredType_AdditionalInsured             InsuredType = 4
	InsuredType_InsuredType_AdditionalInsuredAndLossPayee InsuredType = 5
	InsuredType_InsuredType_SpecifiedAdditionalInsured    InsuredType = 6
	InsuredType_InsuredType_LossPayee                     InsuredType = 7
)

// Enum value maps for InsuredType.
var (
	InsuredType_name = map[int32]string{
		0: "InsuredType_Invalid",
		1: "InsuredType_PrimaryInsured",
		2: "InsuredType_AdditionalNamedInsured",
		3: "InsuredType_DesignatedInsured",
		4: "InsuredType_AdditionalInsured",
		5: "InsuredType_AdditionalInsuredAndLossPayee",
		6: "InsuredType_SpecifiedAdditionalInsured",
		7: "InsuredType_LossPayee",
	}
	InsuredType_value = map[string]int32{
		"InsuredType_Invalid":                       0,
		"InsuredType_PrimaryInsured":                1,
		"InsuredType_AdditionalNamedInsured":        2,
		"InsuredType_DesignatedInsured":             3,
		"InsuredType_AdditionalInsured":             4,
		"InsuredType_AdditionalInsuredAndLossPayee": 5,
		"InsuredType_SpecifiedAdditionalInsured":    6,
		"InsuredType_LossPayee":                     7,
	}
)

func (x InsuredType) Enum() *InsuredType {
	p := new(InsuredType)
	*p = x
	return p
}

func (x InsuredType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InsuredType) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_core_insured_proto_enumTypes[0].Descriptor()
}

func (InsuredType) Type() protoreflect.EnumType {
	return &file_insurance_core_insured_proto_enumTypes[0]
}

func (x InsuredType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InsuredType.Descriptor instead.
func (InsuredType) EnumDescriptor() ([]byte, []int) {
	return file_insurance_core_insured_proto_rawDescGZIP(), []int{0}
}

type InsuredIdentifierType int32

const (
	InsuredIdentifierType_InsuredIdentifierType_Invalid   InsuredIdentifierType = 0
	InsuredIdentifierType_InsuredIdentifierType_DOTNumber InsuredIdentifierType = 1
	InsuredIdentifierType_InsuredIdentifierType_FEIN      InsuredIdentifierType = 2
)

// Enum value maps for InsuredIdentifierType.
var (
	InsuredIdentifierType_name = map[int32]string{
		0: "InsuredIdentifierType_Invalid",
		1: "InsuredIdentifierType_DOTNumber",
		2: "InsuredIdentifierType_FEIN",
	}
	InsuredIdentifierType_value = map[string]int32{
		"InsuredIdentifierType_Invalid":   0,
		"InsuredIdentifierType_DOTNumber": 1,
		"InsuredIdentifierType_FEIN":      2,
	}
)

func (x InsuredIdentifierType) Enum() *InsuredIdentifierType {
	p := new(InsuredIdentifierType)
	*p = x
	return p
}

func (x InsuredIdentifierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InsuredIdentifierType) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_core_insured_proto_enumTypes[1].Descriptor()
}

func (InsuredIdentifierType) Type() protoreflect.EnumType {
	return &file_insurance_core_insured_proto_enumTypes[1]
}

func (x InsuredIdentifierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InsuredIdentifierType.Descriptor instead.
func (InsuredIdentifierType) EnumDescriptor() ([]byte, []int) {
	return file_insurance_core_insured_proto_rawDescGZIP(), []int{1}
}

type InsuredName struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessName string `protobuf:"bytes,1,opt,name=BusinessName,proto3" json:"BusinessName,omitempty"`
}

func (x *InsuredName) Reset() {
	*x = InsuredName{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_core_insured_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsuredName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsuredName) ProtoMessage() {}

func (x *InsuredName) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_core_insured_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsuredName.ProtoReflect.Descriptor instead.
func (*InsuredName) Descriptor() ([]byte, []int) {
	return file_insurance_core_insured_proto_rawDescGZIP(), []int{0}
}

func (x *InsuredName) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

type InsuredIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  InsuredIdentifierType `protobuf:"varint,1,opt,name=type,proto3,enum=insurance_core.InsuredIdentifierType" json:"type,omitempty"`
	Value []string              `protobuf:"bytes,2,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *InsuredIdentifier) Reset() {
	*x = InsuredIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_core_insured_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsuredIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsuredIdentifier) ProtoMessage() {}

func (x *InsuredIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_core_insured_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsuredIdentifier.ProtoReflect.Descriptor instead.
func (*InsuredIdentifier) Descriptor() ([]byte, []int) {
	return file_insurance_core_insured_proto_rawDescGZIP(), []int{1}
}

func (x *InsuredIdentifier) GetType() InsuredIdentifierType {
	if x != nil {
		return x.Type
	}
	return InsuredIdentifierType_InsuredIdentifierType_Invalid
}

func (x *InsuredIdentifier) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

type Insured struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type               InsuredType        `protobuf:"varint,2,opt,name=type,proto3,enum=insurance_core.InsuredType" json:"type,omitempty"`
	Name               *InsuredName       `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Address            *proto.Address     `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	ExternalIdentifier *InsuredIdentifier `protobuf:"bytes,5,opt,name=externalIdentifier,proto3,oneof" json:"externalIdentifier,omitempty"`
}

func (x *Insured) Reset() {
	*x = Insured{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_core_insured_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Insured) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Insured) ProtoMessage() {}

func (x *Insured) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_core_insured_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Insured.ProtoReflect.Descriptor instead.
func (*Insured) Descriptor() ([]byte, []int) {
	return file_insurance_core_insured_proto_rawDescGZIP(), []int{2}
}

func (x *Insured) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Insured) GetType() InsuredType {
	if x != nil {
		return x.Type
	}
	return InsuredType_InsuredType_Invalid
}

func (x *Insured) GetName() *InsuredName {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Insured) GetAddress() *proto.Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *Insured) GetExternalIdentifier() *InsuredIdentifier {
	if x != nil {
		return x.ExternalIdentifier
	}
	return nil
}

var File_insurance_core_insured_proto protoreflect.FileDescriptor

var file_insurance_core_insured_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x1a, 0x15,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x31, 0x0a, 0x0b, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x64, 0x0a, 0x11, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x39, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x95,
	0x02, 0x0a, 0x07, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x69, 0x6e, 0x73, 0x75,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x56, 0x0a, 0x12, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x12, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42,
	0x15, 0x0a, 0x13, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x2a, 0xaa, 0x02, 0x0a, 0x0b, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12,
	0x1e, 0x0a, 0x1a, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x10, 0x01, 0x12,
	0x26, 0x0a, 0x22, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x65, 0x64, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x65,
	0x64, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x10, 0x04, 0x12, 0x2d, 0x0a,
	0x29, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x41, 0x6e,
	0x64, 0x4c, 0x6f, 0x73, 0x73, 0x50, 0x61, 0x79, 0x65, 0x65, 0x10, 0x05, 0x12, 0x2a, 0x0a, 0x26,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x10, 0x06, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4c, 0x6f, 0x73, 0x73, 0x50, 0x61, 0x79, 0x65,
	0x65, 0x10, 0x07, 0x2a, 0x7f, 0x0a, 0x15, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12,
	0x23, 0x0a, 0x1f, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x4f, 0x54, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x45,
	0x49, 0x4e, 0x10, 0x02, 0x42, 0x2e, 0x5a, 0x2c, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74,
	0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x2d, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insurance_core_insured_proto_rawDescOnce sync.Once
	file_insurance_core_insured_proto_rawDescData = file_insurance_core_insured_proto_rawDesc
)

func file_insurance_core_insured_proto_rawDescGZIP() []byte {
	file_insurance_core_insured_proto_rawDescOnce.Do(func() {
		file_insurance_core_insured_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_core_insured_proto_rawDescData)
	})
	return file_insurance_core_insured_proto_rawDescData
}

var file_insurance_core_insured_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_insurance_core_insured_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_insurance_core_insured_proto_goTypes = []interface{}{
	(InsuredType)(0),           // 0: insurance_core.InsuredType
	(InsuredIdentifierType)(0), // 1: insurance_core.InsuredIdentifierType
	(*InsuredName)(nil),        // 2: insurance_core.InsuredName
	(*InsuredIdentifier)(nil),  // 3: insurance_core.InsuredIdentifier
	(*Insured)(nil),            // 4: insurance_core.Insured
	(*proto.Address)(nil),      // 5: common.Address
}
var file_insurance_core_insured_proto_depIdxs = []int32{
	1, // 0: insurance_core.InsuredIdentifier.type:type_name -> insurance_core.InsuredIdentifierType
	0, // 1: insurance_core.Insured.type:type_name -> insurance_core.InsuredType
	2, // 2: insurance_core.Insured.name:type_name -> insurance_core.InsuredName
	5, // 3: insurance_core.Insured.address:type_name -> common.Address
	3, // 4: insurance_core.Insured.externalIdentifier:type_name -> insurance_core.InsuredIdentifier
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_insurance_core_insured_proto_init() }
func file_insurance_core_insured_proto_init() {
	if File_insurance_core_insured_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_insurance_core_insured_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsuredName); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_core_insured_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsuredIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_core_insured_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Insured); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_insurance_core_insured_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_core_insured_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_core_insured_proto_goTypes,
		DependencyIndexes: file_insurance_core_insured_proto_depIdxs,
		EnumInfos:         file_insurance_core_insured_proto_enumTypes,
		MessageInfos:      file_insurance_core_insured_proto_msgTypes,
	}.Build()
	File_insurance_core_insured_proto = out.File
	file_insurance_core_insured_proto_rawDesc = nil
	file_insurance_core_insured_proto_goTypes = nil
	file_insurance_core_insured_proto_depIdxs = nil
}
