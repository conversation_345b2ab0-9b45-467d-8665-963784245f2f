package non_fleet_admitted

import (
	"context"

	"nirvanatech.com/nirvana/forms/fill_inputs"

	"nirvanatech.com/nirvana/forms/fill_inputs/models"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/forms/fill_inputs/core"
	"nirvanatech.com/nirvana/policy_common/forms_generator"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
)

func inputsGenerateFunc(
	ctx context.Context,
	deps *fill_inputs.Deps,
	formComp compilation.FormsCompilation,
	handler *core.FillInputsEntityHandler[
		*application.Application[*admitted_app.AdmittedApp],
		*application.Submission[*admitted_app.AdmittedApp],
		*application.IndicationOption,
	],
) (*forms_generator.FillInputsNew, error) {
	if formComp == nil || deps == nil {
		return nil, errors.New("nil form compilation or dependencies")
	}

	data, err := handler.GenerateCommonData(ctx, deps, formComp)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to generate common data")
	}

	data, err = handler.GenerateComputedFields(ctx, deps, *data)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to generate computed fields")
	}

	return data.FillInputs, nil
}

var computedFieldConfigs = models.FieldConfigs[
	*application.Application[*admitted_app.AdmittedApp],
	*application.Submission[*admitted_app.AdmittedApp],
	*application.IndicationOption,
]{
	"PolicyInfo.PolicyNumbers.CoverageAutoLiability":    policyNumberAL,
	"PolicyInfo.PolicyNumbers.CoverageGeneralLiability": policyNumberGL,
	"PolicyInfo.PolicyNumbers.CoverageMotorTruckCargo":  policyNumberMTC,
	"CompanyInfo.Name":             companyName,
	"InsuranceCarrierOnFormFooter": insuranceCarrierOnFormFooter,
	"AgentName":                    agentName,
	"AgentNumber":                  agentNumber,
}
