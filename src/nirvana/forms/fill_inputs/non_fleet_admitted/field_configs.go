package non_fleet_admitted

import (
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/forms/fill_inputs/models/non_fleet_admitted"
)

var policyNumberAL = createPolicyNumberConfig(
	app_enums.CoverageAutoLiability,
	true,
)

var policyNumberGL = createPolicyNumberConfig(
	app_enums.CoverageGeneralLiability,
	false,
)

var policyNumberMTC = createPolicyNumberConfig(
	app_enums.CoverageMotorTruckCargo,
	false,
)

var companyName = non_fleet_admitted.NewFieldConfig(companyNameGen, companyNameValidator, true)

var insuranceCarrierOnFormFooter = non_fleet_admitted.NewFieldConfig(insuranceCarrierOnFormFooterGen, nil, false)

var agentName = non_fleet_admitted.NewFieldConfig(agentNameGen, nil, false)

var agentNumber = non_fleet_admitted.NewFieldConfig(agentNumberGen, nil, false)
