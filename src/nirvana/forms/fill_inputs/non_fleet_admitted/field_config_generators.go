package non_fleet_admitted

import (
	"context"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/forms/fill_inputs"
	"nirvanatech.com/nirvana/forms/fill_inputs/models"
	"nirvanatech.com/nirvana/policy_common/constants"
)

var companyNameGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[
		*application.Application[*admitted_app.AdmittedApp],
		*application.Submission[*admitted_app.AdmittedApp],
		*application.IndicationOption,
	],
) (models.CommonData[
	*application.Application[*admitted_app.AdmittedApp],
	*application.Submission[*admitted_app.AdmittedApp],
	*application.IndicationOption,
], error,
) {
	insuranceCarrier := data.App.InsuranceCarrier
	data.FillInputs.ComputedFields.InsuranceCarrier = insuranceCarrier.String()
	return *data, nil
}

var insuranceCarrierOnFormFooterGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[
		*application.Application[*admitted_app.AdmittedApp],
		*application.Submission[*admitted_app.AdmittedApp],
		*application.IndicationOption,
	],
) (models.CommonData[
	*application.Application[*admitted_app.AdmittedApp],
	*application.Submission[*admitted_app.AdmittedApp],
	*application.IndicationOption,
], error,
) {
	_, ok := fill_inputs.StateRequiresCarrierOnForms[data.App.Info.GetState()]
	if !ok {
		data.FillInputs.ComputedFields.InsuranceCarrierOnFormFooter = constants.InsuranceCarrierEmpty.String()
		return *data, nil
	}
	data.FillInputs.ComputedFields.InsuranceCarrierOnFormFooter = data.App.InsuranceCarrier.String()
	return *data, nil
}

var agentNameGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[
		*application.Application[*admitted_app.AdmittedApp],
		*application.Submission[*admitted_app.AdmittedApp],
		*application.IndicationOption,
	],
) (models.CommonData[
	*application.Application[*admitted_app.AdmittedApp],
	*application.Submission[*admitted_app.AdmittedApp],
	*application.IndicationOption,
], error,
) {
	data.FillInputs.ComputedFields.AgentName = constants.InsuranceProducerNirvana.String()
	return *data, nil
}

var agentNumberGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[
		*application.Application[*admitted_app.AdmittedApp],
		*application.Submission[*admitted_app.AdmittedApp],
		*application.IndicationOption,
	],
) (models.CommonData[
	*application.Application[*admitted_app.AdmittedApp],
	*application.Submission[*admitted_app.AdmittedApp],
	*application.IndicationOption,
], error,
) {
	agentNumber, err := constants.InsuranceProducerNirvana.AgentPhoneNumber(policyenums.ProgramTypeNonFleetAdmitted)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get agent phone number for non-fleet admitted")
	}
	data.FillInputs.ComputedFields.AgentNumber = agentNumber
	return *data, nil
}
