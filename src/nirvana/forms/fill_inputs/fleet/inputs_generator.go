package fleet

import (
	"context"

	"nirvanatech.com/nirvana/forms/fill_inputs"

	"nirvanatech.com/nirvana/forms/fill_inputs/models"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/forms/fill_inputs/core"
	"nirvanatech.com/nirvana/policy_common/forms_generator"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
)

func inputsGenerateFunc(
	ctx context.Context,
	deps *fill_inputs.Deps,
	formComp compilation.FormsCompilation,
	handler *core.FillInputsEntityHandler[
		*application.Application,
		*application.SubmissionObject,
		*application.IndicationOption,
	],
) (*forms_generator.FillInputsNew, error) {
	if formComp == nil || deps == nil {
		return nil, errors.New("nil form compilation or dependencies")
	}

	data, err := handler.GenerateCommonData(ctx, deps, formComp)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to generate common data")
	}

	data, err = handler.GenerateComputedFields(ctx, deps, *data)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to generate computed fields")
	}

	return data.FillInputs, nil
}

var computedFieldConfigs = models.FieldConfigs[
	*application.Application,
	*application.SubmissionObject,
	*application.IndicationOption,
]{
	"PolicyInfo.PolicyNumbers.CoverageAutoLiability": policyNumberAL,
	"Broker":                                                       brokerName,
	"BrokerMailingAddress":                                         brokerMailingAddress,
	"CompanyInfo.Address.PhysicalAddress":                          physicalAddress,
	"CompanyInfo.CompanyNamePlusDBA":                               insuredNamePlusDBA,
	"CompanyInfo.Name":                                             insuredName,
	"CoverageReeferWithHumanErrorPresent":                          coverageReeferWithHumanErrorPresent,
	"CoverageReeferWithoutHumanErrorPresent":                       coverageReeferWithoutHumanErrorPresent,
	"CoveragesInfo.CoverageCargoTrailerInterchange.Deductible":     ctiDeductible,
	"CoveragesInfo.CoverageCargoTrailerInterchange.Limit":          ctiLimit,
	"CoveragesInfo.CoverageDebrisRemoval.Limit":                    debrisRemovalLimit,
	"CoveragesInfo.CoverageEarnedFreight.Limit":                    earnedFreightLimit,
	"CoveragesInfo.CoverageMiscellaneousEquipment.Limit":           miscellaneousEquipmentLimit,
	"CoveragesInfo.CoverageMotorTruckCargo.Deductible":             mtcDeductible,
	"CoveragesInfo.CoverageMotorTruckCargo.Limit":                  mtcLimit,
	"CoveragesInfo.CoverageMotorTruckCargo.Premium":                mtcPremium,
	"CoveragesInfo.CoverageMotorTruckCargo.PremiumPerHundredMiles": mtcPremiumPerHundredMiles,
	"CoveragesInfo.CoverageMotorTruckCargo.StampingFee":            mtcStampingFee,
	"CoveragesInfo.CoverageMotorTruckCargo.SurplusLinesTax":        mtcSurplusLinesTax,
	"CoveragesInfo.CoveragePollutantCleanupAndRemoval.Limit":       pollutantCleanUpAndRemovalLimit,
	"CoveragesInfo.CoverageLossMitigationExpenses.Limit":           lossMitigationExpensesLimit,
	"Date":                         dateNow,
	"InsuranceCarrier":             carrierName,
	"InsuranceCarrierOnFormFooter": insuranceCarrierOnFormFooter,
	"AgentName":                    agentName,
	"AgentNumber":                  agentNumber,
	"LimitOfInsuranceUM":           limitOfInsuranceUM,
	"PolicyInfo.CameraSubsidyDetails.CameraSubsidyAmount": cameraSubsidyAmt,
	"PolicyInfo.CameraSubsidyDetails.NumberOfCameras":     numberOfCameras,
	"PolicyInfo.NumberOfDaysNoticeCancellation":           numberOfDaysNoticeCancellation,
	"PolicyInfo.NumberOfDaysNoticeNonpayment":             numberOfDaysNoticeNonpayment,
	"PolicyInfo.PolicyNumbers.CoverageGeneralLiability":   policyNumberGL,
	"PolicyInfo.PolicyNumbers.CoverageMotorTruckCargo":    policyNumberMTC,
	"PolicyInfo.PolicyPeriod.From.Date":                   policyEffectiveDate,
	"PolicyInfo.PolicyPeriod.To.Date":                     policyExpirationDate,
	"ReeferBreakdownDeductible":                           reeferBreakdownDeductible,
	"ReeferLimitPerVehicle":                               reeferLimitPerVehicle,
	"ReferToTerminalSchedule":                             referToTerminalSchedule,
	"UMUIMCheckbox":                                       umuimCheckbox,
}
