package fleet

import (
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/forms/fill_inputs/models/fleet"
)

var policyNumberAL = createPolicyNumberConfig(app_enums.CoverageAutoLiability, true)

var policyNumberGL = createPolicyNumberConfig(app_enums.CoverageGeneralLiability, false)

var policyNumberMTC = createPolicyNumberConfig(app_enums.CoverageMotorTruckCargo, false)

var carrierName = fleet.NewFieldConfig(carrierNameGen, carrierNameValidator, true)

var insuranceCarrierOnFormFooter = fleet.NewFieldConfig(insuranceCarrierOnFormFooterGen, nil, false)

var insuredNamePlusDBA = fleet.NewFieldConfig(insuredNamePlusDBAGen, nil, true)

var insuredName = fleet.NewFieldConfig(insuredNameGen, nil, true)

var policyEffectiveDate = fleet.NewFieldConfig(policyEffectiveDateGen, nil, true)

var policyExpirationDate = fleet.NewFieldConfig(policyExpirationDateGen, nil, true)

var physicalAddress = fleet.NewFieldConfig(physicalAddressGen, nil, true)

var mtcLimit = fleet.NewFieldConfig(mtcLimitGen, nil, false)

var mtcDeductible = fleet.NewFieldConfig(mtcDeductibleGen, nil, false)

var mtcPremiumPerHundredMiles = fleet.NewFieldConfig(mtcPremiumPerHundredMilesGen, nil, false)

var mtcPremium = fleet.NewFieldConfig(mtcTotalPremiumGen, nil, false)

var mtcSurplusLinesTax = fleet.NewFieldConfig(mtcSurplusLinesTaxGen, nil, false)

var mtcStampingFee = fleet.NewFieldConfig(mtcStampingFeeGen, nil, false)

var numberOfDaysNoticeNonpayment = fleet.NewFieldConfig(numberOfDaysNoticeNonpaymentGen, nil, true)

var numberOfDaysNoticeCancellation = fleet.NewFieldConfig(numberOfDaysNoticeCancellationGen, nil, true)

var numberOfCameras = fleet.NewFieldConfig(numberOfCamerasGen, nil, false)

var cameraSubsidyAmt = fleet.NewFieldConfig(cameraSubsidyAmtGen, nil, false)

var ctiLimit = fleet.NewFieldConfig(ctiLimitGen, nil, false)

var ctiDeductible = fleet.NewFieldConfig(ctiDeductibleGen, nil, false)

var referToTerminalSchedule = fleet.NewFieldConfig(referToTerminalScheduleGen, nil, false)

var debrisRemovalLimit = fleet.NewFieldConfig(debrisRemovalLimitGen, nil, false)

var earnedFreightLimit = fleet.NewFieldConfig(earnedFreightLimitGen, nil, false)

var pollutantCleanUpAndRemovalLimit = fleet.NewFieldConfig(pollutantCleanUpAndRemovalLimitGen, nil, false)

var miscellaneousEquipmentLimit = fleet.NewFieldConfig(miscellaneousEquipmentLimitGen, nil, false)

var coverageReeferWithoutHumanErrorPresent = fleet.NewFieldConfig(coverageReeferWithoutPresentGen, nil, false)

var coverageReeferWithHumanErrorPresent = fleet.NewFieldConfig(coverageReeferWithHumanErrorPresentGen, nil, false)

var reeferLimitPerVehicle = fleet.NewFieldConfig(reeferLimitPerVehicleGen, nil, false)

var reeferBreakdownDeductible = fleet.NewFieldConfig(reeferBreakdownDeductibleGen, nil, false)

var umuimCheckbox = fleet.NewFieldConfig(umuimCheckboxGen, nil, false)

var limitOfInsuranceUM = fleet.NewFieldConfig(limitOfInsuranceUMGen, nil, false)

var dateNow = fleet.NewFieldConfig(dateNowGen, nil, true)

var brokerName = fleet.NewFieldConfig(brokerNameGen, nil, true)

var brokerMailingAddress = fleet.NewFieldConfig(brokerMailingAddressGen, nil, true)

var lossMitigationExpensesLimit = fleet.NewFieldConfig(lossMitigationExpensesLimitGen, nil, false)

var agentName = fleet.NewFieldConfig(agentNameGen, nil, false)

var agentNumber = fleet.NewFieldConfig(agentNumberGen, nil, false)
