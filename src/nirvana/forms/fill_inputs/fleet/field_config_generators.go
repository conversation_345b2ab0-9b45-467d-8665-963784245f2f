package fleet

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/forms/fill_inputs"
	"nirvanatech.com/nirvana/forms/fill_inputs/models"
	"nirvanatech.com/nirvana/pdffill/requests"
	"nirvanatech.com/nirvana/policy_common/constants"
	"nirvanatech.com/nirvana/underwriting/app_review"
	"nirvanatech.com/nirvana/underwriting/app_review/widgets/global"
)

var carrierNameGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	insuranceCarrier := data.App.ModelPinConfig.Application.InsuranceCarrier
	data.FillInputs.ComputedFields.InsuranceCarrier = insuranceCarrier.String()
	return *data, nil
}

var insuranceCarrierOnFormFooterGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	_, ok := fill_inputs.StateRequiresCarrierOnForms[data.App.CompanyInfo.USState]
	if !ok {
		data.FillInputs.ComputedFields.InsuranceCarrierOnFormFooter = constants.InsuranceCarrierEmpty.String()
		return *data, nil
	}

	data.FillInputs.ComputedFields.InsuranceCarrierOnFormFooter = data.App.ModelPinConfig.Application.InsuranceCarrier.String()
	return *data, nil
}

var insuredNamePlusDBAGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	dotDetails, err := getDotDetails(ctx, deps.FMCSAWrapper, data.App.CompanyInfo.DOTNumber)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get DOT details for DOT number %s", data.App.CompanyInfo.DOTNumber)
	}
	var dba string
	if dotDetails.Census.Dba != nil {
		dba = *dotDetails.Census.Dba
	}
	if dba != "" {
		dba = strings.Join([]string{"DBA", dba}, " ")
	}
	data.FillInputs.ComputedFields.CompanyInfo.CompanyNamePlusDBA = strings.Join([]string{dotDetails.Name, dba}, " ")
	return *data, nil
}

var insuredNameGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	dotDetails, err := getDotDetails(ctx, deps.FMCSAWrapper, data.App.CompanyInfo.DOTNumber)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get insured name for DOT number %s", data.App.CompanyInfo.DOTNumber)
	}
	data.FillInputs.ComputedFields.CompanyInfo.Name = dotDetails.Name
	return *data, nil
}

var policyEffectiveDateGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	date := data.Sub.CoverageInfo.EffectiveDate.Format(time_utils.USLayout)
	data.FillInputs.ComputedFields.PolicyInfo.PolicyPeriod.From.Date = date
	return *data, nil
}

var policyExpirationDateGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	date := data.Sub.CoverageInfo.EffectiveDate.AddDate(1, 0, 0).Format(time_utils.USLayout)
	data.FillInputs.ComputedFields.PolicyInfo.PolicyPeriod.To.Date = date
	return *data, nil
}

var physicalAddressGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	appReview, err := app_review.GetApprovedAppReview(ctx, deps.FleetAppReviewWrapper, data.App.ID)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get approved app review for app %s", data.App.ID)
	}
	address, err := global.GenerateMailingAddress(ctx, deps.FMCSAWrapper, *appReview)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to generate mailing address for app %s", data.App.ID)
	}
	data.FillInputs.ComputedFields.CompanyInfo.Address.PhysicalAddress = *address
	return *data, nil
}

var mtcLimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	limit := getCoverageLimit(data, enums.CoverageMotorTruckCargo)
	data.FillInputs.ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.Limit = limit
	return *data, nil
}

var mtcDeductibleGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	deductible := getCoverageDeductible(data, enums.CoverageMotorTruckCargo)
	data.FillInputs.ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.Deductible = deductible
	return *data, nil
}

var mtcPremiumPerHundredMilesGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	premiumPerHundredMiles := getCoveragePremiumPerHundredMiles(data, enums.CoverageMotorTruckCargo)
	data.FillInputs.ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.PremiumPerHundredMiles = premiumPerHundredMiles
	return *data, nil
}

var mtcTotalPremiumGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	premium := getCoveragePremium(data, enums.CoverageMotorTruckCargo)
	data.FillInputs.ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.Premium = premium
	return *data, nil
}

var numberOfDaysNoticeNonpaymentGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	data.FillInputs.ComputedFields.PolicyInfo.NumberOfDaysNoticeNonpayment = fill_inputs.DaysNoticeForNonPayment[data.App.CompanyInfo.USState]
	return *data, nil
}

var numberOfDaysNoticeCancellationGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	data.FillInputs.ComputedFields.PolicyInfo.NumberOfDaysNoticeCancellation = fill_inputs.DaysNoticeForCancellation[data.App.CompanyInfo.USState]
	return *data, nil
}

var mtcSurplusLinesTaxGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	surchargeInfo := data.IndOpt.SurchargeInfo

	if surchargeInfo != nil && surchargeInfo.MTCPolicySurplusLinesTax != nil {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.SurplusLinesTax = str_utils.NumberToLocaleString(*surchargeInfo.MTCPolicySurplusLinesTax, 0)
	}
	return *data, nil
}

var mtcStampingFeeGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	surchargeInfo := data.IndOpt.SurchargeInfo

	if surchargeInfo != nil && surchargeInfo.MTCPolicyStampingFee != nil {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.StampingFee = str_utils.NumberToLocaleString(*surchargeInfo.MTCPolicyStampingFee, 0)
	}
	return *data, nil
}

var numberOfCamerasGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	appReview, err := app_review.GetApprovedAppReview(ctx, deps.FleetAppReviewWrapper, data.App.ID)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get approved app review for app %s", data.App.ID)
	}

	if appReview.CameraSubsidyDetails != nil {
		data.FillInputs.ComputedFields.PolicyInfo.CameraSubsidyDetails.NumberOfCameras = strconv.Itoa(appReview.CameraSubsidyDetails.NumberOfCameras)
	}
	return *data, nil
}

var cameraSubsidyAmtGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	appReview, err := app_review.GetApprovedAppReview(ctx, deps.FleetAppReviewWrapper, data.App.ID)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get approved app review for app %s", data.App.ID)
	}

	if appReview.CameraSubsidyDetails != nil && appReview.CameraSubsidyDetails.SubsidyAmount != nil {
		data.FillInputs.ComputedFields.PolicyInfo.CameraSubsidyDetails.CameraSubsidyAmount = str_utils.NumberToLocaleString(*appReview.CameraSubsidyDetails.SubsidyAmount, 0)
	}
	return *data, nil
}

var ctiLimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	limit := getCoverageLimit(data, enums.CoverageCargoTrailerInterchange)
	data.FillInputs.ComputedFields.CoveragesInfo.CoverageCargoTrailerInterchange.Limit = limit
	return *data, nil
}

var ctiDeductibleGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	deductible := getCoverageDeductible(data, enums.CoverageCargoTrailerInterchange)
	data.FillInputs.ComputedFields.CoveragesInfo.CoverageCargoTrailerInterchange.Deductible = deductible
	return *data, nil
}

var referToTerminalScheduleGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	if isCoveragePresent(data, enums.CoverageCargoAtScheduledTerminals) {
		data.FillInputs.ComputedFields.ReferToTerminalSchedule = constants.ReferToTerminalSchedule
	}
	return *data, nil
}

var debrisRemovalLimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	limit := getCoverageLimit(data, enums.CoverageDebrisRemoval)
	data.FillInputs.ComputedFields.CoveragesInfo.CoverageDebrisRemoval.Limit = limit
	return *data, nil
}

var earnedFreightLimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	limit := getCoverageLimit(data, enums.CoverageEarnedFreight)
	data.FillInputs.ComputedFields.CoveragesInfo.CoverageEarnedFreight.Limit = limit
	return *data, nil
}

var lossMitigationExpensesLimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	limit := getCoverageLimit(data, enums.CoverageLossMitigationExpenses)
	data.FillInputs.ComputedFields.CoveragesInfo.CoverageLossMitigationExpenses.Limit = limit
	return *data, nil
}

var pollutantCleanUpAndRemovalLimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	limit := getCoverageLimit(data, enums.CoveragePollutantCleanupAndRemoval)
	data.FillInputs.ComputedFields.CoveragesInfo.CoveragePollutantCleanupAndRemoval.Limit = limit
	return *data, nil
}

var miscellaneousEquipmentLimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	limit := getCoverageLimit(data, enums.CoverageMiscellaneousEquipment)
	data.FillInputs.ComputedFields.CoveragesInfo.CoverageMiscellaneousEquipment.Limit = limit
	return *data, nil
}

var coverageReeferWithoutPresentGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	if isCoveragePresent(data, enums.CoverageReeferWithoutHumanError) {
		data.FillInputs.ComputedFields.CoverageReeferWithoutHumanErrorPresent = requests.ReturnExportedValue(true)
	} else {
		data.FillInputs.ComputedFields.CoverageReeferWithoutHumanErrorPresent = requests.ReturnExportedValue(false)
	}

	return *data, nil
}

var coverageReeferWithHumanErrorPresentGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	if isCoveragePresent(data, enums.CoverageReeferWithHumanError) {
		data.FillInputs.ComputedFields.CoverageReeferWithHumanErrorPresent = requests.ReturnExportedValue(true)
	} else {
		data.FillInputs.ComputedFields.CoverageReeferWithHumanErrorPresent = requests.ReturnExportedValue(false)
	}

	return *data, nil
}

var reeferLimitPerVehicleGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	mtcLimit := getCoverageLimit(data, enums.CoverageMotorTruckCargo)
	if isCoveragePresent(data, enums.CoverageReeferWithoutHumanError) {
		data.FillInputs.ComputedFields.ReeferLimitPerVehicle = mtcLimit
	} else if isCoveragePresent(data, enums.CoverageReeferWithHumanError) {
		data.FillInputs.ComputedFields.ReeferLimitPerVehicle = mtcLimit
	} else {
		data.FillInputs.ComputedFields.ReeferLimitPerVehicle = constants.NoCoverage
	}
	return *data, nil
}

var reeferBreakdownDeductibleGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	mtcDeductible := getCoverageDeductible(data, enums.CoverageMotorTruckCargo)
	if isCoveragePresent(data, enums.CoverageReeferWithoutHumanError) {
		data.FillInputs.ComputedFields.ReeferBreakdownDeductible = mtcDeductible
	} else if isCoveragePresent(data, enums.CoverageReeferWithHumanError) {
		data.FillInputs.ComputedFields.ReeferBreakdownDeductible = mtcDeductible
	} else {
		data.FillInputs.ComputedFields.ReeferBreakdownDeductible = constants.NoCoverage
	}
	return *data, nil
}

// This is a list of UM coverages with order of precedence.
var umCovs = []enums.Coverage{
	enums.CoverageUMUIM,
	enums.CoverageUM,
	enums.CoverageUninsuredMotoristBodilyInjury,
	enums.CoverageUninsuredMotoristPropertyDamage,
}

var umuimCheckboxGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	for _, c := range umCovs {
		if isCoveragePresent(data, c) {
			data.FillInputs.ComputedFields.UMUIMCheckbox = requests.ReturnExportedValue(true)
			return *data, nil
		}
	}
	data.FillInputs.ComputedFields.UMUIMCheckbox = requests.ReturnExportedValue(false)
	return *data, nil
}

var limitOfInsuranceUMGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	for _, c := range umCovs {
		if isCoveragePresent(data, c) {
			data.FillInputs.ComputedFields.LimitOfInsuranceUM = getCoverageLimit(data, c)
			break
		}
	}
	return *data, nil
}

var dateNowGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	data.FillInputs.ComputedFields.Date = time.Now().Format(time_utils.USLayout)
	return *data, nil
}

var brokerNameGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	entityLicense, err := deps.EntityLicence.GetByEntityIDAndUSState(
		ctx,
		data.App.AgencyID,
		data.App.CompanyInfo.USState,
	)
	if err != nil {
		log.Info(
			ctx,
			"failed to get entity license for agency ID %s and US state %s: %v",
			log.Any("agencyID", data.App.AgencyID),
			log.Any("USState", data.App.CompanyInfo.USState),
			log.Err(err),
		)
		return *data, nil
	}
	if entityLicense != nil && entityLicense.EntityName != nil {
		data.FillInputs.ComputedFields.Broker = *entityLicense.EntityName
	}
	return *data, nil
}

var brokerMailingAddressGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	entityLicense, err := deps.EntityLicence.GetByEntityIDAndUSState(
		ctx,
		data.App.AgencyID,
		data.App.CompanyInfo.USState,
	)
	if err != nil {
		log.Info(
			ctx,
			"failed to get entity license for agency ID %s and US state %s: %v",
			log.Any("agencyID", data.App.AgencyID),
			log.Any("USState", data.App.CompanyInfo.USState),
			log.Err(err),
		)
		return *data, nil
	}

	if entityLicense != nil && entityLicense.BusinessAddress != nil {
		data.FillInputs.ComputedFields.BrokerMailingAddress = *entityLicense.BusinessAddress.AsString()
	}
	return *data, nil
}

var agentNameGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	data.FillInputs.ComputedFields.AgentName = constants.InsuranceProducerNirvana.String()
	return *data, nil
}

var agentNumberGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption],
) (models.CommonData[*application.Application, *application.SubmissionObject, *application.IndicationOption], error) {
	agentNumber, err := constants.InsuranceProducerNirvana.AgentPhoneNumber(policyenums.ProgramTypeFleet)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get agent phone number for fleet")
	}
	data.FillInputs.ComputedFields.AgentNumber = agentNumber
	return *data, nil
}
