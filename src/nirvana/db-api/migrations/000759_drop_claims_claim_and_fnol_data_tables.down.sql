CREATE TABLE IF NOT EXISTS claims.fnol_data (
    id uuid PRIMARY KEY NOT NULL,
    policy_number varchar NOT NULL,
    client_data jsonb,
    location_data jsonb,
    reported_person_data jsonb,
    loss_data jsonb,
    police_data jsonb,
    misc_data jsonb,
    property_data jsonb,
    party_data jsonb,
    updated_at timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fnol_files varchar[],
    total_attachments_size integer
);

CREATE TYPE claims.claim_state AS ENUM (
    'ClaimStateFNOLErrored',
    'ClaimStateFNOLProcessed'
);

CREATE TABLE IF NOT EXISTS claims.claim (
    client_claim_number varchar PRIMARY KEY NOT NULL,
    policy_number varchar,
    fnol_data_id uuid NOT NULL,
    state claims.claim_state NOT NULL,
    updated_at timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by uuid NOT NULL,
    agency_id uuid NOT NULL
);

ALTER TABLE IF EXISTS claims.claim ADD CONSTRAINT claim_fnol_data_id_fkey FOREIGN KEY (fnol_data_id) REFERENCES claims.fnol_data(id);
