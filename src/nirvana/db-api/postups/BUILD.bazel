load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "postups",
    srcs = [
        "add_agency_id_to_policy.go",
        "add_application_type_to_forms_metadata.go",
        "add_bd_to_agency_bd_table.go",
        "add_bd_to_application_table.go",
        "add_camera_subsidy_details_to_app_review.go",
        "add_carrier_column_to_policy_table_and_backfill_app_sub.go",
        "add_company_info_to_nf_app.go",
        "add_eff_date_in_nf_app_review.go",
        "add_effective_date_coverage_info_nf.go",
        "add_effective_date_to_app_review.go",
        "add_fields_billing_info_policy.go",
        "add_followup_questions.go",
        "add_insured_name_and_company_info_to_policy.go",
        "add_interval_claim_summaries.go",
        "add_model_pin_config.go",
        "add_name_to_keeptruckin_vehicle.go",
        "add_panel_type_list_to_application_review_recommendation.go",
        "add_personal_details_to_auth_users.go",
        "add_premium_adjustments.go",
        "add_primary_coverages_to_fleet_pd.go",
        "add_program_package_nf_indication.go",
        "add_program_type_to_nf_app_review.go",
        "add_program_type_to_nf_application.go",
        "backfill_ancillary_coverages.go",
        "backfill_api_key.go",
        "backfill_company_info_to_admitted_pd.go",
        "backfill_companyid_keeptruckin_vehicle.go",
        "backfill_connections_with_truckercloud.go",
        "backfill_data_context_id_on_app_table.go",
        "backfill_data_context_id_on_sub_table.go",
        "backfill_effective_from_tracked_vehicles.go",
        "backfill_form_comp_ids.go",
        "backfill_forms_list_in_policy_form_info.go",
        "backfill_last_seen_in_tracked_vehicles.go",
        "backfill_latest_submission_id_on_app_review.go",
        "backfill_marketer_id_application.go",
        "backfill_marketer_id_nf_application.go",
        "backfill_owner_pipelines.go",
        "configure_ds_user_password.go",
        "configure_role_password.go",
        "create_telematics_pipeline_info.go",
        "db_password_utils.go",
        "endorsement_migrate_to_generic_rml_breakdown.go",
        "endorsement_review_migrate_to_generic_rml_breakdown.go",
        "expand_policy_table.go",
        "fix_policy_states_to_new_states.go",
        "ib_add_endorsed_ib_persisted.go",
        "oauth_credential_legal_limit.go",
        "populate_tsp_activation_status.go",
        "populate_unified_connections_table.go",
        "postup_interface.go",
        "update_jobber_schedule_format.go",
        "update_policy_set_constraint.go",
        "update_short_id_in_fleet_application.go",
        "update_short_id_in_nf_application.go",
        "uw_add_telematics_connection_state.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/postups",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/billing/enums",
        "//nirvana/billing/legacy/billinginfo",
        "//nirvana/common-go/auth-util",
        "//nirvana/common-go/aws_utils",
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/problem",
        "//nirvana/common-go/retry",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/common-go/uuid_utils",
        "//nirvana/common-go/zip_code_utils",
        "//nirvana/db-api/db_models",
        "//nirvana/db-api/db_models/auth",
        "//nirvana/db-api/db_models/non_fleet",
        "//nirvana/db-api/db_models/telematics",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/auth/enums",
        "//nirvana/db-api/db_wrappers/db",
        "//nirvana/db-api/db_wrappers/endorsement/endorsement",
        "//nirvana/db-api/db_wrappers/endorsement/endorsement-review",
        "//nirvana/db-api/db_wrappers/forms",
        "//nirvana/db-api/db_wrappers/forms/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy/program_data",
        "//nirvana/db-api/db_wrappers/policy/program_data/fleet",
        "//nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/external_data_management/mvr",
        "//nirvana/fmcsa/models",
        "//nirvana/infra/config",
        "//nirvana/infra/constants",
        "//nirvana/jobber/jtypes",
        "//nirvana/policy/constants",
        "//nirvana/policy/enums",
        "//nirvana/policy_common/constants",
        "//nirvana/policy_common/forms_generator/compilation",
        "//nirvana/policy_common/forms_generator/forms",
        "//nirvana/policy_common/policy/shared",
        "//nirvana/quoting/app_state_machine/enums",
        "//nirvana/quoting/appetite_checker",
        "//nirvana/rating/data_processing/vin_processing",
        "//nirvana/rating/rtypes",
        "//nirvana/telematics",
        "//nirvana/telematics/data_platform",
        "//nirvana/telematics/data_platform/workflows/jobs",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_jinzhu_now//:now",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@com_github_volatiletech_sqlboiler_v4//queries/qmhelper",
        "@com_github_volatiletech_sqlboiler_v4//types",
    ],
)

go_test(
    name = "postups_test",
    srcs = ["postups_test.go"],
    deps = [
        ":postups",
        "@com_github_stretchr_testify//require",
    ],
)
