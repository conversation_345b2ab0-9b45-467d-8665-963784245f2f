package postups

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/log"
)

func init() {
	RegisterNirvanaDbPostup(760, "backfill_marketer_id_application", backfillMarketerIdApplication)
}

func backfillMarketerIdApplication(ctx context.Context, tx *sql.Tx) error {
	query := "UPDATE application SET marketer_id = created_by WHERE marketer_id IS NULL"
	result, err := tx.ExecContext(ctx, query)
	if err != nil {
		return errors.Wrap(err, "unable to update application marketer_id")
	}

	count, err := result.RowsAffected()
	if err != nil {
		return errors.Wrap(err, "unable to get rows affected count")
	}

	log.Info(ctx, "Updated application marketer_id", log.Int64("rows_affected", count))
	return nil
}
