load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "application",
    srcs = [
        "authz_resources.go",
        "export.go",
        "filters.go",
        "fx.go",
        "interfaces.go",
        "lossrunvaluesource_enumer.go",
        "object_defs.go",
        "serde_utils.go",
        "utils.go",
        "wrapper.go",
        "wrapper_mock.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/application",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/metrics",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/problem",
        "//nirvana/common-go/retry",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/common-go/vin_utils",
        "//nirvana/common-go/zip_code_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/application/enums/old_enums",
        "//nirvana/db-api/db_wrappers/common",
        "//nirvana/fleet/model",
        "//nirvana/infra/authz",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/jobber/jtypes",
        "//nirvana/nirvanaapp/models/application",
        "//nirvana/openapi-specs/components/application",
        "//nirvana/policy_common/constants",
        "//nirvana/quoting/app_state_machine/enums",
        "//nirvana/quoting/clearance/enums",
        "//nirvana/quoting/quoting_metrics",
        "//nirvana/rating/data_processing/vin_processing/iso_utils",
        "//nirvana/rating/rtypes",
        "//nirvana/telematics",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_lib_pq//:pq",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "application_test",
    srcs = [
        "export_test.go",
        "object_defs_test.go",
        "serde_test.go",
        "wrapper_test.go",
    ],
    embed = [":application"],
    deps = [
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/problem",
        "//nirvana/common-go/test_utils/builders/application",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_models",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/application/enums/old_enums",
        "//nirvana/jobber/jtypes",
        "//nirvana/policy_common/constants",
        "//nirvana/quoting/app_state_machine/enums",
        "//nirvana/rating/data_processing/vin_processing",
        "//nirvana/rating/data_processing/vin_processing/enums",
        "//nirvana/rating/rtypes",
        "//nirvana/rating/utils",
        "//nirvana/telematics",
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_volatiletech_null_v8//:null",
    ],
)
