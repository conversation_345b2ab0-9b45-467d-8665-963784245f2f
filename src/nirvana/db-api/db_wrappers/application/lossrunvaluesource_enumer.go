// Code generated by "enumer -type=LossRunValueSource -json"; DO NOT EDIT.

package application

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _LossRunValueSourceName = "LossRunValueSourceAgentLossRunValueSourceParsedLossRunValueSourceUnderwriter"

var _LossRunValueSourceIndex = [...]uint8{0, 23, 47, 76}

const _LossRunValueSourceLowerName = "lossrunvaluesourceagentlossrunvaluesourceparsedlossrunvaluesourceunderwriter"

func (i LossRunValueSource) String() string {
	if i < 0 || i >= LossRunValueSource(len(_LossRunValueSourceIndex)-1) {
		return fmt.Sprintf("LossRunValueSource(%d)", i)
	}
	return _LossRunValueSourceName[_LossRunValueSourceIndex[i]:_LossRunValueSourceIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _LossRunValueSourceNoOp() {
	var x [1]struct{}
	_ = x[LossRunValueSourceAgent-(0)]
	_ = x[LossRunValueSourceParsed-(1)]
	_ = x[LossRunValueSourceUnderwriter-(2)]
}

var _LossRunValueSourceValues = []LossRunValueSource{LossRunValueSourceAgent, LossRunValueSourceParsed, LossRunValueSourceUnderwriter}

var _LossRunValueSourceNameToValueMap = map[string]LossRunValueSource{
	_LossRunValueSourceName[0:23]:       LossRunValueSourceAgent,
	_LossRunValueSourceLowerName[0:23]:  LossRunValueSourceAgent,
	_LossRunValueSourceName[23:47]:      LossRunValueSourceParsed,
	_LossRunValueSourceLowerName[23:47]: LossRunValueSourceParsed,
	_LossRunValueSourceName[47:76]:      LossRunValueSourceUnderwriter,
	_LossRunValueSourceLowerName[47:76]: LossRunValueSourceUnderwriter,
}

var _LossRunValueSourceNames = []string{
	_LossRunValueSourceName[0:23],
	_LossRunValueSourceName[23:47],
	_LossRunValueSourceName[47:76],
}

// LossRunValueSourceString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func LossRunValueSourceString(s string) (LossRunValueSource, error) {
	if val, ok := _LossRunValueSourceNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _LossRunValueSourceNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to LossRunValueSource values", s)
}

// LossRunValueSourceValues returns all values of the enum
func LossRunValueSourceValues() []LossRunValueSource {
	return _LossRunValueSourceValues
}

// LossRunValueSourceStrings returns a slice of all String values of the enum
func LossRunValueSourceStrings() []string {
	strs := make([]string, len(_LossRunValueSourceNames))
	copy(strs, _LossRunValueSourceNames)
	return strs
}

// IsALossRunValueSource returns "true" if the value is listed in the enum definition. "false" otherwise
func (i LossRunValueSource) IsALossRunValueSource() bool {
	for _, v := range _LossRunValueSourceValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for LossRunValueSource
func (i LossRunValueSource) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for LossRunValueSource
func (i *LossRunValueSource) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("LossRunValueSource should be a string, got %s", data)
	}

	var err error
	*i, err = LossRunValueSourceString(s)
	return err
}
