// Code generated by "enumer -type=Coverage -json"; DO NOT EDIT.

package enums

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _CoverageName = "CoverageAutoLiabilityCoverageAutoPhysicalDamageCoverageGeneralLiabilityCoverageTrailerInterchangeCoverageUninsuredMotoristBodilyInjuryCoverageUninsuredMotoristBodilyInjuryEachPersonCoverageUninsuredMotoristPropertyDamageCoverageUnderinsuredMotoristBodilyInjuryCoverageUnderinsuredMotoristBodilyInjuryEachPersonCoveragePersonalInjuryProtectionCoverageMedicalPaymentsCoverageBroadenedPollutionCoverageBlanketAdditionalCoverageGLBlanketAdditionalCoverageMTCBlanketAdditionalCoverageUIIACoverageBlanketWaiverOfSubrogationCoverageGLBlanketWaiverOfSubrogationCoverageMTCBlanketWaiverOfSubrogationCoverageMotorTruckCargoCoverageUnderinsuredMotoristPropertyDamageCoverageUMUIMBodilyInjuryCoverageUMUIMPhysicalDamageCoveragePropertyProtectionInsuranceCoverageUMUIMCoverageUMBIUIMBICoverageUMCoverageUIMCoverageTerrorismCoverageTowingLaborAndStorageCoverageNonOwnedTrailerCoverageAPDNonOwnedTrailerCoverageMTCNonOwnedTrailerCoverageAPDUIIACoverageMTCUIIACoverageAPDTrailerInterchangeCoverageMTCTrailerInterchangeCoverageReeferCoverageDebrisRemovalCoverageUnattendedTruckCoverageEarnedFreightCoverageRentalReimbursementCoverageReeferWithHumanErrorCoveragePIPExcessAttendantCareCoveragePersonalInjuryProtectionBasicCoveragePersonalInjuryProtectionIncreasedCoverageEnhancedPackageTowingLimitCoverageGuestPersonalInjuryProtectionCoverageReeferWithoutHumanErrorCoverageStopGapCoverageCollisionCoverageComprehensiveCoverageBodilyInjuryCoveragePropertyDamageCoveragePIPWorkLossAndRPLServiceCoverageHiredAutoCoverageBlanketAdditionalPNCCoverageCargoAtScheduledTerminalsCoverageCargoTrailerInterchangeCoveragePollutantCleanupAndRemovalCoverageLossMitigationExpensesCoverageMiscellaneousEquipmentCoverageNonOwnedAutoCoverageWorkLossBenefitsCoverageFuneralExpenseBenefitsCoverageAccidentalDeathBenefitsCoverageExtraordinaryMedicalBenefitsCoverageMedicalExpenseBenefitsCoverageHiredAutoLiabCoverageHiredAutoPDCoverageEssentialServiceExpenses"

var _CoverageIndex = [...]uint16{0, 21, 47, 71, 97, 134, 181, 220, 260, 310, 342, 365, 391, 416, 443, 471, 483, 517, 553, 590, 613, 655, 680, 707, 742, 755, 772, 782, 793, 810, 839, 862, 888, 914, 929, 944, 973, 1002, 1016, 1037, 1060, 1081, 1108, 1136, 1166, 1203, 1244, 1278, 1315, 1346, 1361, 1378, 1399, 1419, 1441, 1473, 1490, 1518, 1551, 1582, 1616, 1646, 1676, 1696, 1720, 1750, 1781, 1817, 1847, 1868, 1887, 1919}

const _CoverageLowerName = "coverageautoliabilitycoverageautophysicaldamagecoveragegeneralliabilitycoveragetrailerinterchangecoverageuninsuredmotoristbodilyinjurycoverageuninsuredmotoristbodilyinjuryeachpersoncoverageuninsuredmotoristpropertydamagecoverageunderinsuredmotoristbodilyinjurycoverageunderinsuredmotoristbodilyinjuryeachpersoncoveragepersonalinjuryprotectioncoveragemedicalpaymentscoveragebroadenedpollutioncoverageblanketadditionalcoverageglblanketadditionalcoveragemtcblanketadditionalcoverageuiiacoverageblanketwaiverofsubrogationcoverageglblanketwaiverofsubrogationcoveragemtcblanketwaiverofsubrogationcoveragemotortruckcargocoverageunderinsuredmotoristpropertydamagecoverageumuimbodilyinjurycoverageumuimphysicaldamagecoveragepropertyprotectioninsurancecoverageumuimcoverageumbiuimbicoverageumcoverageuimcoverageterrorismcoveragetowinglaborandstoragecoveragenonownedtrailercoverageapdnonownedtrailercoveragemtcnonownedtrailercoverageapduiiacoveragemtcuiiacoverageapdtrailerinterchangecoveragemtctrailerinterchangecoveragereefercoveragedebrisremovalcoverageunattendedtruckcoverageearnedfreightcoveragerentalreimbursementcoveragereeferwithhumanerrorcoveragepipexcessattendantcarecoveragepersonalinjuryprotectionbasiccoveragepersonalinjuryprotectionincreasedcoverageenhancedpackagetowinglimitcoverageguestpersonalinjuryprotectioncoveragereeferwithouthumanerrorcoveragestopgapcoveragecollisioncoveragecomprehensivecoveragebodilyinjurycoveragepropertydamagecoveragepipworklossandrplservicecoveragehiredautocoverageblanketadditionalpnccoveragecargoatscheduledterminalscoveragecargotrailerinterchangecoveragepollutantcleanupandremovalcoveragelossmitigationexpensescoveragemiscellaneousequipmentcoveragenonownedautocoverageworklossbenefitscoveragefuneralexpensebenefitscoverageaccidentaldeathbenefitscoverageextraordinarymedicalbenefitscoveragemedicalexpensebenefitscoveragehiredautoliabcoveragehiredautopdcoverageessentialserviceexpenses"

func (i Coverage) String() string {
	if i < 0 || i >= Coverage(len(_CoverageIndex)-1) {
		return fmt.Sprintf("Coverage(%d)", i)
	}
	return _CoverageName[_CoverageIndex[i]:_CoverageIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _CoverageNoOp() {
	var x [1]struct{}
	_ = x[CoverageAutoLiability-(0)]
	_ = x[CoverageAutoPhysicalDamage-(1)]
	_ = x[CoverageGeneralLiability-(2)]
	_ = x[CoverageTrailerInterchange-(3)]
	_ = x[CoverageUninsuredMotoristBodilyInjury-(4)]
	_ = x[CoverageUninsuredMotoristBodilyInjuryEachPerson-(5)]
	_ = x[CoverageUninsuredMotoristPropertyDamage-(6)]
	_ = x[CoverageUnderinsuredMotoristBodilyInjury-(7)]
	_ = x[CoverageUnderinsuredMotoristBodilyInjuryEachPerson-(8)]
	_ = x[CoveragePersonalInjuryProtection-(9)]
	_ = x[CoverageMedicalPayments-(10)]
	_ = x[CoverageBroadenedPollution-(11)]
	_ = x[CoverageBlanketAdditional-(12)]
	_ = x[CoverageGLBlanketAdditional-(13)]
	_ = x[CoverageMTCBlanketAdditional-(14)]
	_ = x[CoverageUIIA-(15)]
	_ = x[CoverageBlanketWaiverOfSubrogation-(16)]
	_ = x[CoverageGLBlanketWaiverOfSubrogation-(17)]
	_ = x[CoverageMTCBlanketWaiverOfSubrogation-(18)]
	_ = x[CoverageMotorTruckCargo-(19)]
	_ = x[CoverageUnderinsuredMotoristPropertyDamage-(20)]
	_ = x[CoverageUMUIMBodilyInjury-(21)]
	_ = x[CoverageUMUIMPhysicalDamage-(22)]
	_ = x[CoveragePropertyProtectionInsurance-(23)]
	_ = x[CoverageUMUIM-(24)]
	_ = x[CoverageUMBIUIMBI-(25)]
	_ = x[CoverageUM-(26)]
	_ = x[CoverageUIM-(27)]
	_ = x[CoverageTerrorism-(28)]
	_ = x[CoverageTowingLaborAndStorage-(29)]
	_ = x[CoverageNonOwnedTrailer-(30)]
	_ = x[CoverageAPDNonOwnedTrailer-(31)]
	_ = x[CoverageMTCNonOwnedTrailer-(32)]
	_ = x[CoverageAPDUIIA-(33)]
	_ = x[CoverageMTCUIIA-(34)]
	_ = x[CoverageAPDTrailerInterchange-(35)]
	_ = x[CoverageMTCTrailerInterchange-(36)]
	_ = x[CoverageReefer-(37)]
	_ = x[CoverageDebrisRemoval-(38)]
	_ = x[CoverageUnattendedTruck-(39)]
	_ = x[CoverageEarnedFreight-(40)]
	_ = x[CoverageRentalReimbursement-(41)]
	_ = x[CoverageReeferWithHumanError-(42)]
	_ = x[CoveragePIPExcessAttendantCare-(43)]
	_ = x[CoveragePersonalInjuryProtectionBasic-(44)]
	_ = x[CoveragePersonalInjuryProtectionIncreased-(45)]
	_ = x[CoverageEnhancedPackageTowingLimit-(46)]
	_ = x[CoverageGuestPersonalInjuryProtection-(47)]
	_ = x[CoverageReeferWithoutHumanError-(48)]
	_ = x[CoverageStopGap-(49)]
	_ = x[CoverageCollision-(50)]
	_ = x[CoverageComprehensive-(51)]
	_ = x[CoverageBodilyInjury-(52)]
	_ = x[CoveragePropertyDamage-(53)]
	_ = x[CoveragePIPWorkLossAndRPLService-(54)]
	_ = x[CoverageHiredAuto-(55)]
	_ = x[CoverageBlanketAdditionalPNC-(56)]
	_ = x[CoverageCargoAtScheduledTerminals-(57)]
	_ = x[CoverageCargoTrailerInterchange-(58)]
	_ = x[CoveragePollutantCleanupAndRemoval-(59)]
	_ = x[CoverageLossMitigationExpenses-(60)]
	_ = x[CoverageMiscellaneousEquipment-(61)]
	_ = x[CoverageNonOwnedAuto-(62)]
	_ = x[CoverageWorkLossBenefits-(63)]
	_ = x[CoverageFuneralExpenseBenefits-(64)]
	_ = x[CoverageAccidentalDeathBenefits-(65)]
	_ = x[CoverageExtraordinaryMedicalBenefits-(66)]
	_ = x[CoverageMedicalExpenseBenefits-(67)]
	_ = x[CoverageHiredAutoLiab-(68)]
	_ = x[CoverageHiredAutoPD-(69)]
	_ = x[CoverageEssentialServiceExpenses-(70)]
}

var _CoverageValues = []Coverage{CoverageAutoLiability, CoverageAutoPhysicalDamage, CoverageGeneralLiability, CoverageTrailerInterchange, CoverageUninsuredMotoristBodilyInjury, CoverageUninsuredMotoristBodilyInjuryEachPerson, CoverageUninsuredMotoristPropertyDamage, CoverageUnderinsuredMotoristBodilyInjury, CoverageUnderinsuredMotoristBodilyInjuryEachPerson, CoveragePersonalInjuryProtection, CoverageMedicalPayments, CoverageBroadenedPollution, CoverageBlanketAdditional, CoverageGLBlanketAdditional, CoverageMTCBlanketAdditional, CoverageUIIA, CoverageBlanketWaiverOfSubrogation, CoverageGLBlanketWaiverOfSubrogation, CoverageMTCBlanketWaiverOfSubrogation, CoverageMotorTruckCargo, CoverageUnderinsuredMotoristPropertyDamage, CoverageUMUIMBodilyInjury, CoverageUMUIMPhysicalDamage, CoveragePropertyProtectionInsurance, CoverageUMUIM, CoverageUMBIUIMBI, CoverageUM, CoverageUIM, CoverageTerrorism, CoverageTowingLaborAndStorage, CoverageNonOwnedTrailer, CoverageAPDNonOwnedTrailer, CoverageMTCNonOwnedTrailer, CoverageAPDUIIA, CoverageMTCUIIA, CoverageAPDTrailerInterchange, CoverageMTCTrailerInterchange, CoverageReefer, CoverageDebrisRemoval, CoverageUnattendedTruck, CoverageEarnedFreight, CoverageRentalReimbursement, CoverageReeferWithHumanError, CoveragePIPExcessAttendantCare, CoveragePersonalInjuryProtectionBasic, CoveragePersonalInjuryProtectionIncreased, CoverageEnhancedPackageTowingLimit, CoverageGuestPersonalInjuryProtection, CoverageReeferWithoutHumanError, CoverageStopGap, CoverageCollision, CoverageComprehensive, CoverageBodilyInjury, CoveragePropertyDamage, CoveragePIPWorkLossAndRPLService, CoverageHiredAuto, CoverageBlanketAdditionalPNC, CoverageCargoAtScheduledTerminals, CoverageCargoTrailerInterchange, CoveragePollutantCleanupAndRemoval, CoverageLossMitigationExpenses, CoverageMiscellaneousEquipment, CoverageNonOwnedAuto, CoverageWorkLossBenefits, CoverageFuneralExpenseBenefits, CoverageAccidentalDeathBenefits, CoverageExtraordinaryMedicalBenefits, CoverageMedicalExpenseBenefits, CoverageHiredAutoLiab, CoverageHiredAutoPD, CoverageEssentialServiceExpenses}

var _CoverageNameToValueMap = map[string]Coverage{
	_CoverageName[0:21]:           CoverageAutoLiability,
	_CoverageLowerName[0:21]:      CoverageAutoLiability,
	_CoverageName[21:47]:          CoverageAutoPhysicalDamage,
	_CoverageLowerName[21:47]:     CoverageAutoPhysicalDamage,
	_CoverageName[47:71]:          CoverageGeneralLiability,
	_CoverageLowerName[47:71]:     CoverageGeneralLiability,
	_CoverageName[71:97]:          CoverageTrailerInterchange,
	_CoverageLowerName[71:97]:     CoverageTrailerInterchange,
	_CoverageName[97:134]:         CoverageUninsuredMotoristBodilyInjury,
	_CoverageLowerName[97:134]:    CoverageUninsuredMotoristBodilyInjury,
	_CoverageName[134:181]:        CoverageUninsuredMotoristBodilyInjuryEachPerson,
	_CoverageLowerName[134:181]:   CoverageUninsuredMotoristBodilyInjuryEachPerson,
	_CoverageName[181:220]:        CoverageUninsuredMotoristPropertyDamage,
	_CoverageLowerName[181:220]:   CoverageUninsuredMotoristPropertyDamage,
	_CoverageName[220:260]:        CoverageUnderinsuredMotoristBodilyInjury,
	_CoverageLowerName[220:260]:   CoverageUnderinsuredMotoristBodilyInjury,
	_CoverageName[260:310]:        CoverageUnderinsuredMotoristBodilyInjuryEachPerson,
	_CoverageLowerName[260:310]:   CoverageUnderinsuredMotoristBodilyInjuryEachPerson,
	_CoverageName[310:342]:        CoveragePersonalInjuryProtection,
	_CoverageLowerName[310:342]:   CoveragePersonalInjuryProtection,
	_CoverageName[342:365]:        CoverageMedicalPayments,
	_CoverageLowerName[342:365]:   CoverageMedicalPayments,
	_CoverageName[365:391]:        CoverageBroadenedPollution,
	_CoverageLowerName[365:391]:   CoverageBroadenedPollution,
	_CoverageName[391:416]:        CoverageBlanketAdditional,
	_CoverageLowerName[391:416]:   CoverageBlanketAdditional,
	_CoverageName[416:443]:        CoverageGLBlanketAdditional,
	_CoverageLowerName[416:443]:   CoverageGLBlanketAdditional,
	_CoverageName[443:471]:        CoverageMTCBlanketAdditional,
	_CoverageLowerName[443:471]:   CoverageMTCBlanketAdditional,
	_CoverageName[471:483]:        CoverageUIIA,
	_CoverageLowerName[471:483]:   CoverageUIIA,
	_CoverageName[483:517]:        CoverageBlanketWaiverOfSubrogation,
	_CoverageLowerName[483:517]:   CoverageBlanketWaiverOfSubrogation,
	_CoverageName[517:553]:        CoverageGLBlanketWaiverOfSubrogation,
	_CoverageLowerName[517:553]:   CoverageGLBlanketWaiverOfSubrogation,
	_CoverageName[553:590]:        CoverageMTCBlanketWaiverOfSubrogation,
	_CoverageLowerName[553:590]:   CoverageMTCBlanketWaiverOfSubrogation,
	_CoverageName[590:613]:        CoverageMotorTruckCargo,
	_CoverageLowerName[590:613]:   CoverageMotorTruckCargo,
	_CoverageName[613:655]:        CoverageUnderinsuredMotoristPropertyDamage,
	_CoverageLowerName[613:655]:   CoverageUnderinsuredMotoristPropertyDamage,
	_CoverageName[655:680]:        CoverageUMUIMBodilyInjury,
	_CoverageLowerName[655:680]:   CoverageUMUIMBodilyInjury,
	_CoverageName[680:707]:        CoverageUMUIMPhysicalDamage,
	_CoverageLowerName[680:707]:   CoverageUMUIMPhysicalDamage,
	_CoverageName[707:742]:        CoveragePropertyProtectionInsurance,
	_CoverageLowerName[707:742]:   CoveragePropertyProtectionInsurance,
	_CoverageName[742:755]:        CoverageUMUIM,
	_CoverageLowerName[742:755]:   CoverageUMUIM,
	_CoverageName[755:772]:        CoverageUMBIUIMBI,
	_CoverageLowerName[755:772]:   CoverageUMBIUIMBI,
	_CoverageName[772:782]:        CoverageUM,
	_CoverageLowerName[772:782]:   CoverageUM,
	_CoverageName[782:793]:        CoverageUIM,
	_CoverageLowerName[782:793]:   CoverageUIM,
	_CoverageName[793:810]:        CoverageTerrorism,
	_CoverageLowerName[793:810]:   CoverageTerrorism,
	_CoverageName[810:839]:        CoverageTowingLaborAndStorage,
	_CoverageLowerName[810:839]:   CoverageTowingLaborAndStorage,
	_CoverageName[839:862]:        CoverageNonOwnedTrailer,
	_CoverageLowerName[839:862]:   CoverageNonOwnedTrailer,
	_CoverageName[862:888]:        CoverageAPDNonOwnedTrailer,
	_CoverageLowerName[862:888]:   CoverageAPDNonOwnedTrailer,
	_CoverageName[888:914]:        CoverageMTCNonOwnedTrailer,
	_CoverageLowerName[888:914]:   CoverageMTCNonOwnedTrailer,
	_CoverageName[914:929]:        CoverageAPDUIIA,
	_CoverageLowerName[914:929]:   CoverageAPDUIIA,
	_CoverageName[929:944]:        CoverageMTCUIIA,
	_CoverageLowerName[929:944]:   CoverageMTCUIIA,
	_CoverageName[944:973]:        CoverageAPDTrailerInterchange,
	_CoverageLowerName[944:973]:   CoverageAPDTrailerInterchange,
	_CoverageName[973:1002]:       CoverageMTCTrailerInterchange,
	_CoverageLowerName[973:1002]:  CoverageMTCTrailerInterchange,
	_CoverageName[1002:1016]:      CoverageReefer,
	_CoverageLowerName[1002:1016]: CoverageReefer,
	_CoverageName[1016:1037]:      CoverageDebrisRemoval,
	_CoverageLowerName[1016:1037]: CoverageDebrisRemoval,
	_CoverageName[1037:1060]:      CoverageUnattendedTruck,
	_CoverageLowerName[1037:1060]: CoverageUnattendedTruck,
	_CoverageName[1060:1081]:      CoverageEarnedFreight,
	_CoverageLowerName[1060:1081]: CoverageEarnedFreight,
	_CoverageName[1081:1108]:      CoverageRentalReimbursement,
	_CoverageLowerName[1081:1108]: CoverageRentalReimbursement,
	_CoverageName[1108:1136]:      CoverageReeferWithHumanError,
	_CoverageLowerName[1108:1136]: CoverageReeferWithHumanError,
	_CoverageName[1136:1166]:      CoveragePIPExcessAttendantCare,
	_CoverageLowerName[1136:1166]: CoveragePIPExcessAttendantCare,
	_CoverageName[1166:1203]:      CoveragePersonalInjuryProtectionBasic,
	_CoverageLowerName[1166:1203]: CoveragePersonalInjuryProtectionBasic,
	_CoverageName[1203:1244]:      CoveragePersonalInjuryProtectionIncreased,
	_CoverageLowerName[1203:1244]: CoveragePersonalInjuryProtectionIncreased,
	_CoverageName[1244:1278]:      CoverageEnhancedPackageTowingLimit,
	_CoverageLowerName[1244:1278]: CoverageEnhancedPackageTowingLimit,
	_CoverageName[1278:1315]:      CoverageGuestPersonalInjuryProtection,
	_CoverageLowerName[1278:1315]: CoverageGuestPersonalInjuryProtection,
	_CoverageName[1315:1346]:      CoverageReeferWithoutHumanError,
	_CoverageLowerName[1315:1346]: CoverageReeferWithoutHumanError,
	_CoverageName[1346:1361]:      CoverageStopGap,
	_CoverageLowerName[1346:1361]: CoverageStopGap,
	_CoverageName[1361:1378]:      CoverageCollision,
	_CoverageLowerName[1361:1378]: CoverageCollision,
	_CoverageName[1378:1399]:      CoverageComprehensive,
	_CoverageLowerName[1378:1399]: CoverageComprehensive,
	_CoverageName[1399:1419]:      CoverageBodilyInjury,
	_CoverageLowerName[1399:1419]: CoverageBodilyInjury,
	_CoverageName[1419:1441]:      CoveragePropertyDamage,
	_CoverageLowerName[1419:1441]: CoveragePropertyDamage,
	_CoverageName[1441:1473]:      CoveragePIPWorkLossAndRPLService,
	_CoverageLowerName[1441:1473]: CoveragePIPWorkLossAndRPLService,
	_CoverageName[1473:1490]:      CoverageHiredAuto,
	_CoverageLowerName[1473:1490]: CoverageHiredAuto,
	_CoverageName[1490:1518]:      CoverageBlanketAdditionalPNC,
	_CoverageLowerName[1490:1518]: CoverageBlanketAdditionalPNC,
	_CoverageName[1518:1551]:      CoverageCargoAtScheduledTerminals,
	_CoverageLowerName[1518:1551]: CoverageCargoAtScheduledTerminals,
	_CoverageName[1551:1582]:      CoverageCargoTrailerInterchange,
	_CoverageLowerName[1551:1582]: CoverageCargoTrailerInterchange,
	_CoverageName[1582:1616]:      CoveragePollutantCleanupAndRemoval,
	_CoverageLowerName[1582:1616]: CoveragePollutantCleanupAndRemoval,
	_CoverageName[1616:1646]:      CoverageLossMitigationExpenses,
	_CoverageLowerName[1616:1646]: CoverageLossMitigationExpenses,
	_CoverageName[1646:1676]:      CoverageMiscellaneousEquipment,
	_CoverageLowerName[1646:1676]: CoverageMiscellaneousEquipment,
	_CoverageName[1676:1696]:      CoverageNonOwnedAuto,
	_CoverageLowerName[1676:1696]: CoverageNonOwnedAuto,
	_CoverageName[1696:1720]:      CoverageWorkLossBenefits,
	_CoverageLowerName[1696:1720]: CoverageWorkLossBenefits,
	_CoverageName[1720:1750]:      CoverageFuneralExpenseBenefits,
	_CoverageLowerName[1720:1750]: CoverageFuneralExpenseBenefits,
	_CoverageName[1750:1781]:      CoverageAccidentalDeathBenefits,
	_CoverageLowerName[1750:1781]: CoverageAccidentalDeathBenefits,
	_CoverageName[1781:1817]:      CoverageExtraordinaryMedicalBenefits,
	_CoverageLowerName[1781:1817]: CoverageExtraordinaryMedicalBenefits,
	_CoverageName[1817:1847]:      CoverageMedicalExpenseBenefits,
	_CoverageLowerName[1817:1847]: CoverageMedicalExpenseBenefits,
	_CoverageName[1847:1868]:      CoverageHiredAutoLiab,
	_CoverageLowerName[1847:1868]: CoverageHiredAutoLiab,
	_CoverageName[1868:1887]:      CoverageHiredAutoPD,
	_CoverageLowerName[1868:1887]: CoverageHiredAutoPD,
	_CoverageName[1887:1919]:      CoverageEssentialServiceExpenses,
	_CoverageLowerName[1887:1919]: CoverageEssentialServiceExpenses,
}

var _CoverageNames = []string{
	_CoverageName[0:21],
	_CoverageName[21:47],
	_CoverageName[47:71],
	_CoverageName[71:97],
	_CoverageName[97:134],
	_CoverageName[134:181],
	_CoverageName[181:220],
	_CoverageName[220:260],
	_CoverageName[260:310],
	_CoverageName[310:342],
	_CoverageName[342:365],
	_CoverageName[365:391],
	_CoverageName[391:416],
	_CoverageName[416:443],
	_CoverageName[443:471],
	_CoverageName[471:483],
	_CoverageName[483:517],
	_CoverageName[517:553],
	_CoverageName[553:590],
	_CoverageName[590:613],
	_CoverageName[613:655],
	_CoverageName[655:680],
	_CoverageName[680:707],
	_CoverageName[707:742],
	_CoverageName[742:755],
	_CoverageName[755:772],
	_CoverageName[772:782],
	_CoverageName[782:793],
	_CoverageName[793:810],
	_CoverageName[810:839],
	_CoverageName[839:862],
	_CoverageName[862:888],
	_CoverageName[888:914],
	_CoverageName[914:929],
	_CoverageName[929:944],
	_CoverageName[944:973],
	_CoverageName[973:1002],
	_CoverageName[1002:1016],
	_CoverageName[1016:1037],
	_CoverageName[1037:1060],
	_CoverageName[1060:1081],
	_CoverageName[1081:1108],
	_CoverageName[1108:1136],
	_CoverageName[1136:1166],
	_CoverageName[1166:1203],
	_CoverageName[1203:1244],
	_CoverageName[1244:1278],
	_CoverageName[1278:1315],
	_CoverageName[1315:1346],
	_CoverageName[1346:1361],
	_CoverageName[1361:1378],
	_CoverageName[1378:1399],
	_CoverageName[1399:1419],
	_CoverageName[1419:1441],
	_CoverageName[1441:1473],
	_CoverageName[1473:1490],
	_CoverageName[1490:1518],
	_CoverageName[1518:1551],
	_CoverageName[1551:1582],
	_CoverageName[1582:1616],
	_CoverageName[1616:1646],
	_CoverageName[1646:1676],
	_CoverageName[1676:1696],
	_CoverageName[1696:1720],
	_CoverageName[1720:1750],
	_CoverageName[1750:1781],
	_CoverageName[1781:1817],
	_CoverageName[1817:1847],
	_CoverageName[1847:1868],
	_CoverageName[1868:1887],
	_CoverageName[1887:1919],
}

// CoverageString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func CoverageString(s string) (Coverage, error) {
	if val, ok := _CoverageNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _CoverageNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to Coverage values", s)
}

// CoverageValues returns all values of the enum
func CoverageValues() []Coverage {
	return _CoverageValues
}

// CoverageStrings returns a slice of all String values of the enum
func CoverageStrings() []string {
	strs := make([]string, len(_CoverageNames))
	copy(strs, _CoverageNames)
	return strs
}

// IsACoverage returns "true" if the value is listed in the enum definition. "false" otherwise
func (i Coverage) IsACoverage() bool {
	for _, v := range _CoverageValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for Coverage
func (i Coverage) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for Coverage
func (i *Coverage) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("Coverage should be a string, got %s", data)
	}

	var err error
	*i, err = CoverageString(s)
	return err
}
