package enums

import (
	"strings"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/str_utils"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
)

const (
	coverageAutoLiabilityPrefix    = "AL"
	coverageGeneralLiabilityPrefix = "GL"
	coverageMotorTruckCargoPrefix  = "MTC"
)

// Do not change the enum names because these are persisted. If you decide to
// change them, remember to update openapi and db persisted enums.

//go:generate go run github.com/dmarkham/enumer -type=MileageRadiusBucket -json
type MileageRadiusBucket int

const (
	MileageRadiusBucketZeroToFifty MileageRadiusBucket = iota
	MileageRadiusBucketFiftyToTwoHundred
	MileageRadiusBucketTwoHundredToFiveHundred
	MileageRadiusBucketFiveHundredPlus
)

func (i MileageRadiusBucket) UserVisibleStr() string {
	switch i {
	case MileageRadiusBucketZeroToFifty:
		return "0-50 miles"
	case MileageRadiusBucketFiftyToTwoHundred:
		return "50-200 miles"
	case MileageRadiusBucketTwoHundredToFiveHundred:
		return "200-500 miles"
	case MileageRadiusBucketFiveHundredPlus:
		return "500+ miles"
	default:
		return ""
	}
}

//go:generate go run github.com/dmarkham/enumer -type=TypeOfTerminal -trimprefix=TypeOfTerminal -json
type TypeOfTerminal int

const (
	TypeOfTerminalDock TypeOfTerminal = iota
	TypeOfTerminalDropLot
	TypeOfTerminalOffice
	TypeOfTerminalTerminal
)

//go:generate go run github.com/dmarkham/enumer -type=Coverage -json
type Coverage int

// NOTE: While adding new coverages, append them at the end
// instead of anywhere in the middle. If not done it changes
// the coverage to integer mapping (`type Coverage int`)
const (
	CoverageAutoLiability Coverage = iota
	CoverageAutoPhysicalDamage
	CoverageGeneralLiability
	CoverageTrailerInterchange
	CoverageUninsuredMotoristBodilyInjury
	CoverageUninsuredMotoristBodilyInjuryEachPerson
	CoverageUninsuredMotoristPropertyDamage
	CoverageUnderinsuredMotoristBodilyInjury
	CoverageUnderinsuredMotoristBodilyInjuryEachPerson
	CoveragePersonalInjuryProtection
	// TODO: create PIP specific coverages for KS
	CoverageMedicalPayments
	// TODO: refactor to endorsements
	CoverageBroadenedPollution
	// TODO: Refactor to ALBlanketAdditional
	CoverageBlanketAdditional
	CoverageGLBlanketAdditional
	CoverageMTCBlanketAdditional
	CoverageUIIA
	// TODO: Refactor to CoverageALBlanketWaiverOfSubrogation
	CoverageBlanketWaiverOfSubrogation
	CoverageGLBlanketWaiverOfSubrogation
	CoverageMTCBlanketWaiverOfSubrogation
	// TODO: add UMUIM coverage and limit for TN
	CoverageMotorTruckCargo
	CoverageUnderinsuredMotoristPropertyDamage
	CoverageUMUIMBodilyInjury
	CoverageUMUIMPhysicalDamage
	CoveragePropertyProtectionInsurance
	CoverageUMUIM
	CoverageUMBIUIMBI
	CoverageUM
	CoverageUIM
	CoverageTerrorism
	/* Non-Fleet */
	CoverageTowingLaborAndStorage
	CoverageNonOwnedTrailer
	CoverageAPDNonOwnedTrailer
	CoverageMTCNonOwnedTrailer
	CoverageAPDUIIA
	CoverageMTCUIIA
	CoverageAPDTrailerInterchange
	CoverageMTCTrailerInterchange
	CoverageReefer
	CoverageDebrisRemoval
	CoverageUnattendedTruck // DEPRECATED: Was earlier used forProgramTypeNonFleetCanopiusNRB
	CoverageEarnedFreight
	CoverageRentalReimbursement
	// Not exclusive to NF
	CoverageReeferWithHumanError
	CoveragePIPExcessAttendantCare
	CoveragePersonalInjuryProtectionBasic
	CoveragePersonalInjuryProtectionIncreased
	CoverageEnhancedPackageTowingLimit
	CoverageGuestPersonalInjuryProtection
	CoverageReeferWithoutHumanError
	CoverageStopGap
	CoverageCollision
	CoverageComprehensive
	CoverageBodilyInjury
	CoveragePropertyDamage
	CoveragePIPWorkLossAndRPLService // Does not exist in ancillary sheet today, update when added to the sheet
	CoverageHiredAuto
	CoverageBlanketAdditionalPNC
	CoverageCargoAtScheduledTerminals
	CoverageCargoTrailerInterchange
	CoveragePollutantCleanupAndRemoval
	CoverageLossMitigationExpenses
	CoverageMiscellaneousEquipment
	CoverageNonOwnedAuto
	CoverageWorkLossBenefits
	CoverageFuneralExpenseBenefits
	CoverageAccidentalDeathBenefits
	CoverageExtraordinaryMedicalBenefits
	CoverageMedicalExpenseBenefits
	CoverageHiredAutoLiab
	CoverageHiredAutoPD
	CoverageEssentialServiceExpenses
)

// AncCoverageToPrimaryCoverage is a mapping from ancillary coverages to their
// corresponding primary coverage. This is used to determine the primary
// coverage for a given ancillary coverage.
// nolint:exhaustive
var AncCoverageToPrimaryCoverage = map[Coverage]Coverage{
	CoverageTrailerInterchange:                         CoverageAutoPhysicalDamage,
	CoverageUninsuredMotoristBodilyInjury:              CoverageAutoLiability,
	CoverageUninsuredMotoristBodilyInjuryEachPerson:    CoverageAutoLiability,
	CoverageUninsuredMotoristPropertyDamage:            CoverageAutoLiability,
	CoverageUnderinsuredMotoristBodilyInjury:           CoverageAutoLiability,
	CoverageUnderinsuredMotoristBodilyInjuryEachPerson: CoverageAutoLiability,
	CoveragePersonalInjuryProtection:                   CoverageAutoLiability,
	CoverageMedicalPayments:                            CoverageAutoLiability,
	CoverageBroadenedPollution:                         CoverageAutoLiability,
	CoverageBlanketAdditional:                          CoverageAutoLiability,
	CoverageGLBlanketAdditional:                        CoverageGeneralLiability,
	CoverageMTCBlanketAdditional:                       CoverageMotorTruckCargo,
	CoverageUIIA:                                       CoverageAutoLiability,
	CoverageBlanketWaiverOfSubrogation:                 CoverageAutoLiability,
	CoverageGLBlanketWaiverOfSubrogation:               CoverageGeneralLiability,
	CoverageMTCBlanketWaiverOfSubrogation:              CoverageMotorTruckCargo,
	CoverageUnderinsuredMotoristPropertyDamage:         CoverageAutoLiability,
	CoverageUMUIMBodilyInjury:                          CoverageAutoLiability,
	CoverageUMUIMPhysicalDamage:                        CoverageAutoLiability,
	CoveragePropertyProtectionInsurance:                CoverageAutoLiability,
	CoverageUMUIM:                                      CoverageAutoLiability,
	CoverageUMBIUIMBI:                                  CoverageAutoLiability,
	CoverageUM:                                         CoverageAutoLiability,
	CoverageUIM:                                        CoverageAutoLiability,
	CoverageTerrorism:                                  CoverageAutoLiability,
	/* Non-Fleet */
	CoverageTowingLaborAndStorage: CoverageAutoPhysicalDamage,
	// How do we reuse the same sub cov for different Coverages?
	CoverageNonOwnedTrailer:                   CoverageAutoPhysicalDamage,
	CoverageAPDNonOwnedTrailer:                CoverageAutoPhysicalDamage,
	CoverageMTCNonOwnedTrailer:                CoverageMotorTruckCargo,
	CoverageAPDUIIA:                           CoverageAutoPhysicalDamage,
	CoverageMTCUIIA:                           CoverageMotorTruckCargo,
	CoverageAPDTrailerInterchange:             CoverageAutoPhysicalDamage,
	CoverageMTCTrailerInterchange:             CoverageMotorTruckCargo,
	CoverageReefer:                            CoverageMotorTruckCargo,
	CoverageDebrisRemoval:                     CoverageMotorTruckCargo,
	CoverageEarnedFreight:                     CoverageMotorTruckCargo,
	CoverageRentalReimbursement:               CoverageAutoPhysicalDamage,
	CoverageReeferWithHumanError:              CoverageMotorTruckCargo,
	CoveragePIPExcessAttendantCare:            CoverageAutoLiability,
	CoveragePersonalInjuryProtectionBasic:     CoverageAutoLiability,
	CoveragePersonalInjuryProtectionIncreased: CoverageAutoLiability,
	CoverageEnhancedPackageTowingLimit:        CoverageAutoPhysicalDamage,
	CoverageGuestPersonalInjuryProtection:     CoverageAutoLiability,
	CoverageReeferWithoutHumanError:           CoverageMotorTruckCargo,
	CoverageStopGap:                           CoverageGeneralLiability,
	CoverageCollision:                         CoverageAutoPhysicalDamage,
	CoverageComprehensive:                     CoverageAutoPhysicalDamage,
	CoverageBodilyInjury:                      CoverageAutoLiability,
	CoveragePropertyDamage:                    CoverageAutoLiability,
	CoveragePIPWorkLossAndRPLService:          CoverageAutoLiability,
	CoverageHiredAuto:                         CoverageAutoLiability,
	CoverageCargoAtScheduledTerminals:         CoverageMotorTruckCargo,
	CoverageCargoTrailerInterchange:           CoverageMotorTruckCargo,
	CoveragePollutantCleanupAndRemoval:        CoverageMotorTruckCargo,
	CoverageLossMitigationExpenses:            CoverageMotorTruckCargo,
	CoverageMiscellaneousEquipment:            CoverageMotorTruckCargo,
	CoverageBlanketAdditionalPNC:              CoverageAutoLiability,
	CoverageNonOwnedAuto:                      CoverageAutoLiability,
	CoverageWorkLossBenefits:                  CoverageAutoLiability,
	CoverageFuneralExpenseBenefits:            CoverageAutoLiability,
	CoverageAccidentalDeathBenefits:           CoverageAutoLiability,
	CoverageExtraordinaryMedicalBenefits:      CoverageAutoLiability,
	CoverageMedicalExpenseBenefits:            CoverageAutoLiability,
	CoverageHiredAutoLiab:                     CoverageAutoLiability,
	CoverageHiredAutoPD:                       CoverageAutoPhysicalDamage,
	CoverageEssentialServiceExpenses:          CoverageAutoLiability,
}

func GetPrimaryCoverageFromCoverage(coverage Coverage) (*Coverage, error) {
	if coverage.IsPrimaryCoverage() {
		return pointer_utils.ToPointer(coverage), nil
	}
	if primaryCoverage, exists := AncCoverageToPrimaryCoverage[coverage]; exists {
		return pointer_utils.ToPointer(primaryCoverage), nil
	} else {
		return nil, errors.Newf("coverage %s does not have a primary coverage", coverage.String())
	}
}

// StateSurcharge is a non-coverage line item that is used to represent
// state surcharge in the system.
const StateSurcharge = "StateSurcharge"

func GetCoverageFromShortCode(shortCode string) (Coverage, error) {
	switch shortCode {
	case "PIP":
		return CoveragePersonalInjuryProtection, nil
	case "MedPay":
		return CoverageMedicalPayments, nil
	case "PPI":
		return CoveragePropertyProtectionInsurance, nil
	case "UMPD":
		return CoverageUninsuredMotoristPropertyDamage, nil
	case "UMBI":
		return CoverageUninsuredMotoristBodilyInjury, nil
	case "UIMBI":
		return CoverageUnderinsuredMotoristBodilyInjury, nil
	case "UIMPD":
		return CoverageUnderinsuredMotoristPropertyDamage, nil
	case "TI":
		return CoverageTrailerInterchange, nil
	case "TL&S":
		return CoverageTowingLaborAndStorage, nil
	case "NOT":
		return CoverageNonOwnedTrailer, nil
	case "BlanketAdditionalInsured":
		return CoverageBlanketAdditional, nil
	case "PIPBasic":
		return CoveragePersonalInjuryProtectionBasic, nil
	case "PIPIncreased":
		return CoveragePersonalInjuryProtectionIncreased, nil
	case "GuestPIP":
		return CoverageGuestPersonalInjuryProtection, nil
	case "HiredAuto":
		return CoverageHiredAuto, nil
	case "CargoAtScheduledTerminals":
		return CoverageCargoAtScheduledTerminals, nil
	case "CargoTrailerInterchange":
		return CoverageCargoTrailerInterchange, nil
	case "DebrisRemoval":
		return CoverageDebrisRemoval, nil
	case "EarnedFreightCharges":
		return CoverageEarnedFreight, nil
	case "PollutantCleanupAndRemoval":
		return CoveragePollutantCleanupAndRemoval, nil
	case "LossMitigationExpenses":
		return CoverageLossMitigationExpenses, nil
	case "MiscellaneousEquipment":
		return CoverageMiscellaneousEquipment, nil
	case "BlanketPNC":
		return CoverageBlanketAdditionalPNC, nil
	case "NonOwnedAuto":
		return CoverageNonOwnedAuto, nil
	case "First Party Benefits - Work Loss Add-On":
		return CoverageWorkLossBenefits, nil
	case "First Party Benefits - Funeral Expense":
		return CoverageFuneralExpenseBenefits, nil
	case "First Party Benefits - Accidental Death":
		return CoverageAccidentalDeathBenefits, nil
	case "First Party Benefits - Extraordinary Medical Expenses":
		return CoverageExtraordinaryMedicalBenefits, nil
	case "PIP (First Party Benefits - Medical Expense)":
		return CoverageMedicalExpenseBenefits, nil
	case "HiredAutoLiab":
		return CoverageHiredAutoLiab, nil
	case "HiredAutoPD":
		return CoverageHiredAutoPD, nil
	case "First Party Benefits - Essential Service Expenses":
		return CoverageEssentialServiceExpenses, nil
	}
	if coverage, err := CoverageString("Coverage" + shortCode); err == nil {
		return coverage, nil
	}
	return Coverage(-1), errors.Newf("Invalid coverage short code: %s", shortCode)
}

func (i *Coverage) GetShortName() (string, error) {
	switch *i {
	case CoverageAutoLiability:
		return "AL", nil
	case CoverageAutoPhysicalDamage:
		return "APD", nil
	case CoverageGeneralLiability:
		return "GL", nil
	case CoverageMotorTruckCargo:
		return "MTC", nil

	default:
		return "", errors.Newf("Cov: %s doesn't have a short name", i.String())
	}
}

func (i *Coverage) IsPrimaryCoverage() bool {
	return *i == CoverageAutoLiability ||
		*i == CoverageAutoPhysicalDamage ||
		*i == CoverageMotorTruckCargo ||
		*i == CoverageGeneralLiability
}

func (i *Coverage) IsMTCCoverage() bool {
	return *i == CoverageMotorTruckCargo
}

func (i Coverage) IsPartOfCoverage(primaryCoverage Coverage) bool {
	parentCoverage, exists := AncCoverageToPrimaryCoverage[i]
	if !exists {
		return false
	}

	return parentCoverage == primaryCoverage
}

// RenewalCarryForwardAncillaryCoverages defines which ancillary coverage types
// should be carried forward from the previous application during renewal
var RenewalCarryForwardAncillaryCoverages = map[Coverage]bool{
	CoverageReeferWithoutHumanError:   true,
	CoverageReeferWithHumanError:      true,
	CoverageCargoAtScheduledTerminals: false,
	CoverageCargoTrailerInterchange:   false,
}

func (i *Coverage) IsEligibleAncCovForRenewal() bool {
	return RenewalCarryForwardAncillaryCoverages[*i]
}

//go:generate go run github.com/dmarkham/enumer -type=IndicationOptionTag -json
type IndicationOptionTag int

const (
	IndicationOptionTagBasic IndicationOptionTag = iota
	IndicationOptionTagStandard
	IndicationOptionTagComplete
	IndicationOptionTagCustom
	IndicationOptionTagInvalid
)

func (i IndicationOptionTag) GetTrimmedPackageNameForFileName() string {
	return strings.TrimPrefix(i.String(), "IndicationOptionTag")
}

//go:generate go run github.com/dmarkham/enumer -type=AdditionalInformationCommodity -json
type AdditionalInformationCommodity int

const (
	AddlInfoHazardousMaterialsInclClass9 AdditionalInformationCommodity = iota
	AddlInfoLiftGateOrWhiteGloveService
	AddlInfoResidentialDelivery
	AddlInfoDoubleOrTripleTrailers
	AddlInfoMeatOnHook
)

// PremiumPerPowerUnit contains buckets to define premiums per power unit. Each
// enum makes reference to a dollar amount range.
//
//go:generate go run github.com/dmarkham/enumer -type=PremiumPerPowerUnit -json
type PremiumPerPowerUnit int

const (
	PremiumPerPowerUnitZeroToFiveThousand PremiumPerPowerUnit = iota
	PremiumPerPowerUnitFiveThousandToEightThousand
	PremiumPerPowerUnitEightThousandToTenThousand
	PremiumPerPowerUnitTenThousandToFifteenThousand
	PremiumPerPowerUnitFifteenThousandPlus
)

//go:generate go run github.com/dmarkham/enumer -type=NegotiatedRatesExemption -json
type NegotiatedRatesExemption int

const (
	NegotiatedRatesExemptionRuleFifteen NegotiatedRatesExemption = iota
	NegotiatedRatesExemptionLargeFleet
	NegotiatedRatesExemptionConsentToRate
)

// EmailPreference represents the various preferences for an email.
//
//go:generate go run github.com/dmarkham/enumer -type=EmailPreference -json
type EmailPreference int

const (
	EmailPreferenceInvalid EmailPreference = iota
	EmailPreferenceActive
	EmailPreferenceStopped
)

// EmailJobStatus represents the various status that an email job can be represented by.
//
//go:generate go run github.com/dmarkham/enumer -type=EmailJobStatus -json
type EmailJobStatus int

const (
	EmailJobStatusInvalid EmailJobStatus = iota
	EmailJobStatusCompleted
	EmailJobStatusCancelled
	EmailJobStatusSkipped
)

//go:generate go run github.com/dmarkham/enumer -type=TelematicsDataStatus -json -trimprefix=TelematicsDataStatus
type TelematicsDataStatus int

const (
	TelemtaticsDataStatusWaitingForTSPConsent TelematicsDataStatus = iota
	TelematicsDataStatusProcessingData
	TelematicsDataStatusReconnectionRequired
	TelematicsDataStatusDataAvailable
	TelematicsDataStatusNotEnoughData
	TelematicsDataStatusPanic
	TelematicsDataNoHistoricalData
	TelematicsDataStatusConnectionFailed
)

//go:generate go run github.com/dmarkham/enumer -type=ApplicationTag -json
type ApplicationTag int

const (
	ApplicationTagFirstAgentApplication ApplicationTag = iota
	// ApplicationTagHasValidLicenseForNonAdmitted is used to tag the application if producer doesn't have
	// valid license for non admitted submission
	ApplicationTagHasValidLicenseForNonAdmitted
	// ApplicationTagClearanceProtected is used to tag the application if it is protected for clearance
	ApplicationTagClearanceProtected
)

func (t *TelematicsDataStatus) GenerateOapiEnum() (*oapi_common.TelematicsDataStatus, error) {
	switch *t {
	case TelemtaticsDataStatusWaitingForTSPConsent:
		status := oapi_common.WaitingForTSPConsent
		return &status, nil
	case TelematicsDataStatusProcessingData:
		status := oapi_common.ProcessingData
		return &status, nil
	case TelematicsDataStatusReconnectionRequired:
		status := oapi_common.ReConnectionRequired
		return &status, nil
	case TelematicsDataStatusDataAvailable:
		status := oapi_common.DataAvailable
		return &status, nil
	case TelematicsDataStatusNotEnoughData:
		status := oapi_common.NotEnoughData
		return &status, nil
	case TelematicsDataStatusPanic:
		status := oapi_common.Panic
		return &status, nil
	case TelematicsDataNoHistoricalData:
		status := oapi_common.NoHistoricalDataException
		return &status, nil
	case TelematicsDataStatusConnectionFailed:
		status := oapi_common.ConnectionFailed
		return &status, nil
	}
	return nil, errors.Newf("unable to generate oapi enum for telematics data status %s", t)
}

func GetCoverageLabel(cov Coverage) string {
	label := cov.String()
	// TODO: Remove this once we have a better way to handle this
	// This is a hack to handle the fact that the correct name is blanket additional insured
	if cov == CoverageBlanketAdditional {
		label += " Insured"
	}
	// This is a hack to handle the fact that the correct name is hired auto liability
	if cov == CoverageHiredAuto {
		label += "Liability"
	}
	return str_utils.PrettyEnumString(label, "Coverage")
}

func GetCoveragePrefixForPrimaryCoverage(cov Coverage) string {
	//nolint:exhaustive
	switch cov {
	case CoverageAutoLiability:
		return coverageAutoLiabilityPrefix
	case CoverageGeneralLiability:
		return coverageGeneralLiabilityPrefix
	case CoverageMotorTruckCargo:
		return coverageMotorTruckCargoPrefix
	default:
		return ""
	}
}

// GetCoverageEnumFromSubCoverage finds the primary Coverage enum for a given sub-coverage ID
func GetCoverageEnumFromSubCoverage(subCoverageID string) (Coverage, error) {
	for k, v := range AncCoverageToPrimaryCoverage {
		if k.String() == subCoverageID {
			return v, nil
		}
	}

	// We are consciously not adding this to AncCoverageToPrimaryCoverage
	// given that it is a primary coverage itself
	if subCoverageID == CoverageMotorTruckCargo.String() {
		return CoverageMotorTruckCargo, nil
	}

	// We are consciously not adding this to AncCoverageToPrimaryCoverage
	// given that it is a primary coverage itself
	if subCoverageID == CoverageGeneralLiability.String() {
		return CoverageGeneralLiability, nil
	}
	return Coverage(-1), errors.Newf("Unable to find coverage for sub-coverage: %s", subCoverageID)
}

// GetCoverageStringFromSubCoverage finds the primary coverage string for a given sub-coverage ID
func GetCoverageStringFromSubCoverage(subCoverageID string) (string, error) {
	for k, v := range AncCoverageToPrimaryCoverage {
		if k.String() == subCoverageID {
			return v.String(), nil
		}
	}

	// We are consciously not adding this to AncCoverageToPrimaryCoverage
	// given that it is a primary coverage itself
	if subCoverageID == CoverageMotorTruckCargo.String() {
		return CoverageMotorTruckCargo.String(), nil
	}

	// We are consciously not adding this to AncCoverageToPrimaryCoverage
	// given that it is a primary coverage itself
	if subCoverageID == CoverageGeneralLiability.String() {
		return CoverageGeneralLiability.String(), nil
	}

	return "", errors.Newf("Unable to find coverage for sub-coverage: %s", subCoverageID)
}

//go:generate go run github.com/dmarkham/enumer -type=LossRunSummaryPeriod -json -trimprefix=LossRunSummaryPeriod
type LossRunSummaryPeriod int

const (
	LossRunSummaryPeriodInvalid LossRunSummaryPeriod = iota
	LossRunSummaryPeriodCurrent
	LossRunSummaryPeriodFirstPriorYear
	LossRunSummaryPeriodSecondPriorYear
	LossRunSummaryPeriodThirdPriorYear
	LossRunSummaryPeriodFourthPriorYear
	LossRunSummaryPeriodFifthPriorYear
)

//go:generate go run github.com/dmarkham/enumer -type=ConstructionClass -json -trimprefix=ConstructionClass
type ConstructionClass int

const (
	ConstructionClassFrame ConstructionClass = iota + 1
	ConstructionClassJoistedMasonry
	ConstructionClassNonCombustible
	ConstructionClassMasonryNonCombustible
	ConstructionClassModifiedFireResistive
	ConstructionClassFireResistive
	ConstructionClassNoneOfAboveOrUnknown
)

//go:generate go run github.com/dmarkham/enumer -type=PrivateTheftProtection -json -trimprefix=PrivateTheftProtection
type PrivateTheftProtection int

const (
	PrivateTheftProtectionNoAlarmSystem PrivateTheftProtection = iota + 1
	PrivateTheftProtectionLocalAlarm
	PrivateTheftProtectionCentralStationAlarm
	PrivateTheftProtectionCentralStationAlarmAndAdditionalTheftProtections
)

//go:generate go run github.com/dmarkham/enumer -type=PrivateFireProtection -json -trimprefix=PrivateFireProtection
type PrivateFireProtection int

const (
	PrivateFireProtectionNoAutomaticFireSuppression PrivateFireProtection = iota + 1
	PrivateFireProtectionIncompleteSuppression
	PrivateFireProtectionCompleteSuppression
	PrivateFireProtectionAutomaticSuppressionAndAdditionalProtections
)

//go:generate go run github.com/dmarkham/enumer -type=PublicProtectionClass -json -trimprefix=PublicProtectionClass
type PublicProtectionClass int

const (
	PublicProtectionClass1 PublicProtectionClass = iota + 1
	PublicProtectionClass2
	PublicProtectionClass3
	PublicProtectionClass4
	PublicProtectionClass5
	PublicProtectionClass6
	PublicProtectionClass7
	PublicProtectionClass8
	PublicProtectionClass9
	PublicProtectionClass10
)
