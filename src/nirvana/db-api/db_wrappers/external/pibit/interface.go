package pibit

import (
	"context"
)

type DataWrapper interface {
	GetParsedLossRunDocument(ctx context.Context, documentId string) (*Document, error)
	InsertParsedLossRunDocument(ctx context.Context, document *Document) error
	UpdateParsedLossRunDocument(ctx context.Context, document *Document) error
	GetAllParsedLossRunPolicyByDocumentId(ctx context.Context, documentId string) ([]PolicyData, error)
	GetParsedLossRunPolicyByUniqueColumns(ctx context.Context, policy *PolicyData) (*PolicyData, error)
	InsertParsedLossRunPolicy(crx context.Context, policy *PolicyData) error
	GetAllParsedLossRunClaimByDocumentId(ctx context.Context, documentId string) ([]ClaimData, error)
	GetParsedLossRunClaimByUniqueColumns(ctx context.Context, claim *ClaimData) (*ClaimData, error)
	InsertParsedLossRunClaim(ctx context.Context, claim *ClaimData) error
	GetAllParsedLossRunLossByDocumentId(ctx context.Context, documentId string) ([]LossData, error)
	InsertParsedLossRunLoss(ctx context.Context, loss *LossData) error
	InsertDocumentStateTransition(ctx context.Context, transition DocumentStateTransition) error
	GetDocumentsBySubmissionIds(ctx context.Context, submissionIds []string) ([]Document, error)
	InsertAggregation(ctx context.Context, aggregation Aggregation) error
	InsertProcessedLoss(ctx context.Context, processedLoss ProcessedLoss) error
	GetAggregationsByApplicationAndStatus(ctx context.Context, applicationId string, status AggregationStatus) ([]Aggregation, error)
	UpdateAggregation(ctx context.Context, aggregation Aggregation) error
	GetLatestAggregationForAppReview(ctx context.Context, appReviewId string) (*Aggregation, error)
}
