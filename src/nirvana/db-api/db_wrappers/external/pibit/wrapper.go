package pibit

import (
	"context"
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	db_api "nirvanatech.com/nirvana/db-api"
	"nirvanatech.com/nirvana/db-api/db_models/parsed_loss_runs"
)

type DataWrapperImpl struct {
	db db_api.NirvanaRW
}

func NewDataWrapperImpl(db db_api.NirvanaRW) *DataWrapperImpl {
	return &DataWrapperImpl{db: db}
}

func (f *DataWrapperImpl) GetParsedLossRunDocument(ctx context.Context, documentId string) (*Document, error) {
	documentDb, err := parsed_loss_runs.Documents(parsed_loss_runs.DocumentWhere.ID.EQ(documentId)).One(ctx, f.db)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get parsed loss run document for document id %s", documentId)
	}
	document, err := dbToDocument(documentDb)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to transform db object to document for document id %s", documentId)
	}
	return document, nil
}

func (f *DataWrapperImpl) InsertParsedLossRunDocument(ctx context.Context, document *Document) error {
	documentDb, err := documentToDb(document)
	if err != nil {
		return errors.Wrapf(err, "unable to transform document to db object for documentId: %s", document.ID)
	}
	err = documentDb.Insert(ctx, f.db, boil.Infer())
	if err != nil {
		return errors.Wrapf(err, "unable to insert parsed loss run document for documentId: %s", document.ID)
	}
	return nil
}

func (f *DataWrapperImpl) UpdateParsedLossRunDocument(ctx context.Context, document *Document) error {
	documentDb, err := documentToDb(document)
	if err != nil {
		return errors.Wrapf(err, "unable to transform document to db object for documentId: %s", document.ID)
	}
	_, err = documentDb.Update(ctx, f.db, boil.Infer())
	if err != nil {
		return errors.Wrapf(err, "unable to update parsed loss run document for documentId: %s", document.ID)
	}
	return nil
}

func (f *DataWrapperImpl) GetAllParsedLossRunPolicyByDocumentId(ctx context.Context,
	documentId string,
) ([]PolicyData, error) {
	policyDb, err := parsed_loss_runs.Policies(parsed_loss_runs.PolicyWhere.DocumentID.EQ(documentId)).All(ctx, f.db)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get parsed loss run policy for document Id: %s", documentId)
	}
	policies := make([]PolicyData, 0)
	for _, policy := range policyDb {
		policies = append(policies, *dbToPolicy(policy))
	}
	return policies, nil
}

func (f *DataWrapperImpl) GetParsedLossRunPolicyByUniqueColumns(ctx context.Context,
	policy *PolicyData,
) (*PolicyData, error) {
	policyDb, err := parsed_loss_runs.Policies(
		parsed_loss_runs.PolicyWhere.DocumentID.EQ(*policy.DocumentId),
		parsed_loss_runs.PolicyWhere.PolicySN.EQ(int(*policy.PolicySn)),
	).One(ctx, f.db)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get parsed loss run policy for document Id: %s", *policy.DocumentId)
	}
	return dbToPolicy(policyDb), nil
}

func (f *DataWrapperImpl) InsertParsedLossRunPolicy(ctx context.Context, policy *PolicyData) error {
	policyDb := policyToDb(policy)
	err := policyDb.Insert(ctx, f.db, boil.Infer())
	if err != nil {
		return errors.Wrapf(err, "unable to insert parsed loss run policy for documentId: %s", policy.DocumentId)
	}
	return nil
}

func (f *DataWrapperImpl) GetParsedLossRunClaimByUniqueColumns(ctx context.Context,
	claim *ClaimData,
) (*ClaimData, error) {
	claimDb, err := parsed_loss_runs.Claims(
		parsed_loss_runs.ClaimWhere.DocumentID.EQ(*claim.DocumentId),
		parsed_loss_runs.ClaimWhere.PolicySN.EQ(int(*claim.PolicySn)),
		parsed_loss_runs.ClaimWhere.ClaimSN.EQ(int(*claim.ClaimSn)),
	).One(ctx, f.db)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get parsed loss run policy for document Id: %s", *claim.DocumentId)
	}
	return dbToClaim(claimDb), nil
}

func (f *DataWrapperImpl) GetAllParsedLossRunClaimByDocumentId(ctx context.Context,
	documentId string,
) ([]ClaimData, error) {
	claimDb, err := parsed_loss_runs.Claims(parsed_loss_runs.ClaimWhere.DocumentID.EQ(documentId)).All(ctx, f.db)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get parsed loss run claim for document Id: %s", documentId)
	}
	claims := make([]ClaimData, 0)
	for _, claim := range claimDb {
		claims = append(claims, *dbToClaim(claim))
	}
	return claims, nil
}

func (f *DataWrapperImpl) InsertParsedLossRunClaim(ctx context.Context, claim *ClaimData) error {
	claimDb := claimToDb(claim)
	err := claimDb.Insert(ctx, f.db, boil.Infer())
	if err != nil {
		return errors.Wrapf(err, "unable to insert parsed loss run claim for documentId: %s", claim.DocumentId)
	}
	return nil
}

func (f *DataWrapperImpl) GetAllParsedLossRunLossByDocumentId(ctx context.Context,
	documentId string,
) ([]LossData, error) {
	lossDb, err := parsed_loss_runs.Losses(parsed_loss_runs.LossWhere.DocumentID.EQ(documentId)).All(ctx, f.db)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get parsed loss run loss for document Id: %s", documentId)
	}
	losses := make([]LossData, 0)
	for _, loss := range lossDb {
		lossData, err := dbToLoss(loss)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to transform db loss to lossdata for lossId %s", loss.ID)
		}
		losses = append(losses, *lossData)
	}
	return losses, nil
}

func (f *DataWrapperImpl) InsertParsedLossRunLoss(ctx context.Context, loss *LossData) error {
	lossDb := lossToDb(loss)
	err := lossDb.Insert(ctx, f.db, boil.Infer())
	if err != nil {
		return errors.Wrapf(err, "unable to insert parsed loss run loss for documentId: %s", loss.DocumentId)
	}
	return nil
}

func (f *DataWrapperImpl) InsertDocumentStateTransition(ctx context.Context, transition DocumentStateTransition) error {
	transitionDb := docStateTransitionToDb(transition)
	err := transitionDb.Insert(ctx, f.db, boil.Infer())
	if err != nil {
		return errors.Wrapf(err, "unable to insert parsed loss run document state transition for documentId: %s", transition.DocumentID)
	}
	return nil
}

func (f *DataWrapperImpl) GetDocumentsBySubmissionIds(ctx context.Context, submissionIds []string) ([]Document, error) {
	// Convert []string to []interface{}
	// This is crucial for qm.WhereIn to correctly interpret the arguments.
	args := make([]interface{}, len(submissionIds))
	for i, id := range submissionIds {
		args[i] = id
	}
	qms := []qm.QueryMod{
		qm.WhereIn(fmt.Sprintf("%s in ?", parsed_loss_runs.DocumentTableColumns.SubmissionID), args...),
	}
	documentsDb, err := parsed_loss_runs.Documents(qms...).All(ctx, f.db)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get parsed loss run document for submission ids %v", submissionIds)
	}
	var documents []Document
	for _, documentDb := range documentsDb {
		document, err := dbToDocument(documentDb)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to transform db object to document for submission id %s", documentDb.SubmissionID)
		}
		documents = append(documents, *document)
	}
	return documents, nil
}

func (f *DataWrapperImpl) InsertAggregation(ctx context.Context, aggregation Aggregation) error {
	aggregationDb, err := aggregationToDb(aggregation)
	if err != nil {
		return errors.Wrapf(err, "failed to map aggregation to aggregationDb")
	}
	err = aggregationDb.Insert(ctx, f.db, boil.Infer())
	if err != nil {
		return errors.Wrapf(err, "failed to insert aggregation")
	}
	return nil
}

func (f *DataWrapperImpl) InsertProcessedLoss(ctx context.Context, processedLoss ProcessedLoss) error {
	processedLossDb, err := processedLossToDb(processedLoss)
	if err != nil {
		return errors.Wrapf(err, "failed to map processedLoss to processedLossDb")
	}
	err = processedLossDb.Insert(ctx, f.db, boil.Infer())
	if err != nil {
		return errors.Wrapf(err, "failed to insert processedLoss")
	}
	return nil
}

func (f *DataWrapperImpl) GetAggregationsByApplicationAndStatus(ctx context.Context, applicationId string, status AggregationStatus) ([]Aggregation, error) {
	aggregationsDb, err := parsed_loss_runs.Aggregations(parsed_loss_runs.AggregationWhere.ApplicationID.EQ(applicationId), parsed_loss_runs.AggregationWhere.Status.EQ(status.String())).All(ctx, f.db)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get aggregations for applicationId %s and status %s", applicationId, status.String())
	}
	aggregations := make([]Aggregation, 0)
	for _, dbAggregation := range aggregationsDb {
		aggregation, err := dbToAggregation(*dbAggregation)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to transform db aggregation to aggregation object for id %s", dbAggregation.ID)
		}
		aggregations = append(aggregations, *aggregation)
	}
	return aggregations, nil
}

func (f *DataWrapperImpl) UpdateAggregation(ctx context.Context, aggregation Aggregation) error {
	aggregationDb, err := aggregationToDb(aggregation)
	if err != nil {
		return errors.Wrapf(err, "failed to map aggregation to aggregationDb for id %s", aggregation.ID)
	}
	_, err = aggregationDb.Update(ctx, f.db, boil.Infer())
	if err != nil {
		return errors.Wrapf(err, "failed to update aggregation for id %s", aggregation.ID)
	}
	return nil
}

func (f *DataWrapperImpl) GetLatestAggregationForAppReview(ctx context.Context, appReviewId string) (*Aggregation, error) {
	aggregationDb, err := parsed_loss_runs.Aggregations(
		parsed_loss_runs.AggregationWhere.ApplicationReviewID.EQ(appReviewId),
		qm.OrderBy(parsed_loss_runs.AggregationTableColumns.CreatedAt+" DESC"),
	).One(ctx, f.db)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get latest aggregation for app review id %s", appReviewId)
	}
	aggregation, err := dbToAggregation(*aggregationDb)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to transform db aggregation to aggregation object for id %s", aggregationDb.ID)
	}
	return aggregation, nil
}

var _ DataWrapper = &DataWrapperImpl{}
