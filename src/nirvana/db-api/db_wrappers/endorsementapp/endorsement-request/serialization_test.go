package endorsement_request

import (
	"google.golang.org/protobuf/types/known/timestamppb"
	common_proto "nirvanatech.com/nirvana/common-go/proto"
	dbendorsementapp "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	"nirvanatech.com/nirvana/insurance-bundle/model/endorsement"
	"nirvanatech.com/nirvana/insurance-core/proto"
	nf_types "nirvanatech.com/nirvana/nonfleet/model/endorsement"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/openapi-specs/components/common"
)

func TestEndorsementRequestSerialization_WithQuoteGenerationInfo(t *testing.T) {
	requestID := uuid.New()
	baseID := uuid.New()
	producerID := uuid.New()
	marketerID := uuid.New()
	createdBy := uuid.New()
	agencyID := uuid.New()

	now := time.Now()
	mvrPullTime := time.Date(2024, 1, 15, 12, 0, 0, 0, time.UTC)

	request := &Request{
		ID:                   requestID,
		State:                enums.EndorsementRequestStateCreated,
		ProgramType:          proto.ProgramType_ProgramType_NonFleetAdmitted,
		ProducerID:           producerID,
		MarketerID:           marketerID,
		CreatedBy:            createdBy,
		AgencyID:             agencyID,
		CreatedAt:            now,
		UpdatedAt:            now,
		DefaultEffectiveDate: &now,
		Base: RequestBase{
			ID:   baseID,
			Type: enums.BasedOutOfBundle,
		},
		QuoteGenerationInfo: &QuoteGenerationInfo{
			PricingJobInfo: &PricingJobInfo{
				JobRunID: getJobRunID(),
				Status:   enums.PricingJobStatusRunning,
				Error:    nil,
			},
			MVRPullDetails: &MVRPullDetails{
				Status:         common.MVRPullStatusSuccess,
				LatestPullTime: mvrPullTime,
			},
		},
		Changes: []dbendorsementapp.Change{{
			Id:       "change-1",
			IsActive: true,
			Data: &endorsement.ChangeData{
				Data: &endorsement.ChangeData_NonFleetChange{
					NonFleetChange: &nf_types.NonFleetChange{
						ChangeType: nf_types.NonFleetChangeType_NonFleetChangeType_Driver,
						Data: &nf_types.NonFleetChange_DriverChange{DriverChange: &nf_types.DriverChange{
							Remove: []string{"F32040056059"},
						}},
					},
				},
			},
			EffectiveInterval: &common_proto.Interval{
				Start: timestamppb.New(now),
				End:   timestamppb.New(now),
			},
		}},
	}

	// Test serialization to DB
	dbObj, changesDbObj, err := endorsementRequestToDb(request)
	require.NoError(t, err)
	assert.NotNil(t, dbObj)
	assert.NotNil(t, changesDbObj)
	assert.True(t, dbObj.QuoteGenerationInfo.Valid)
}

func TestEndorsementRequestSerialization_WithoutQuoteGenerationInfo(t *testing.T) {
	// Test serialization when QuoteGenerationInfo is nil

	requestID := uuid.New()
	baseID := uuid.New()
	producerID := uuid.New()
	marketerID := uuid.New()
	createdBy := uuid.New()
	agencyID := uuid.New()

	now := time.Now()

	request := &Request{
		ID:          requestID,
		State:       enums.EndorsementRequestStateCreated,
		ProgramType: 1, // Assuming valid program type
		ProducerID:  producerID,
		MarketerID:  marketerID,
		CreatedBy:   createdBy,
		AgencyID:    agencyID,
		CreatedAt:   now,
		UpdatedAt:   now,
		Base: RequestBase{
			ID:   baseID,
			Type: enums.BasedOutOfBundle,
		},
	}

	// Test serialization to DB
	dbObj, changesDbObj, err := endorsementRequestToDb(request)
	require.NoError(t, err)
	assert.NotNil(t, dbObj)
	assert.NotNil(t, changesDbObj)

	assert.True(t, dbObj.QuoteGenerationInfo.Valid)
}

// Helper function to create JobRunId from string for tests
func getJobRunID() jtypes.JobRunId {
	return jtypes.NewJobRunId(jtypes.CreateJobId("test", "job"), jtypes.RunId(1))
}
