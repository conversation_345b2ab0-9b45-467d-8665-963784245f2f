// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: endorsementapp/change.proto

package endorsementapp

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	proto "nirvanatech.com/nirvana/common-go/proto"
	endorsement "nirvanatech.com/nirvana/insurance-bundle/model/endorsement"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Change struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	PolicyNumbers     []string                `protobuf:"bytes,2,rep,name=policyNumbers,proto3" json:"policyNumbers,omitempty"`
	Data              *endorsement.ChangeData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	EffectiveInterval *proto.Interval         `protobuf:"bytes,4,opt,name=effectiveInterval,proto3" json:"effectiveInterval,omitempty"`
	IsActive          bool                    `protobuf:"varint,5,opt,name=isActive,proto3" json:"isActive,omitempty"`
	Description       string                  `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *Change) Reset() {
	*x = Change{}
	if protoimpl.UnsafeEnabled {
		mi := &file_endorsementapp_change_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Change) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Change) ProtoMessage() {}

func (x *Change) ProtoReflect() protoreflect.Message {
	mi := &file_endorsementapp_change_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Change.ProtoReflect.Descriptor instead.
func (*Change) Descriptor() ([]byte, []int) {
	return file_endorsementapp_change_proto_rawDescGZIP(), []int{0}
}

func (x *Change) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Change) GetPolicyNumbers() []string {
	if x != nil {
		return x.PolicyNumbers
	}
	return nil
}

func (x *Change) GetData() *endorsement.ChangeData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Change) GetEffectiveInterval() *proto.Interval {
	if x != nil {
		return x.EffectiveInterval
	}
	return nil
}

func (x *Change) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Change) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

var File_endorsementapp_change_proto protoreflect.FileDescriptor

var file_endorsementapp_change_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x70,
	0x2f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x70, 0x1a, 0x11, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x34, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe9, 0x01, 0x0a, 0x06, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x3e, 0x0a, 0x11, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x52, 0x11, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x3f, 0x5a, 0x3d, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63,
	0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x2d, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x70, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_endorsementapp_change_proto_rawDescOnce sync.Once
	file_endorsementapp_change_proto_rawDescData = file_endorsementapp_change_proto_rawDesc
)

func file_endorsementapp_change_proto_rawDescGZIP() []byte {
	file_endorsementapp_change_proto_rawDescOnce.Do(func() {
		file_endorsementapp_change_proto_rawDescData = protoimpl.X.CompressGZIP(file_endorsementapp_change_proto_rawDescData)
	})
	return file_endorsementapp_change_proto_rawDescData
}

var file_endorsementapp_change_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_endorsementapp_change_proto_goTypes = []interface{}{
	(*Change)(nil),                 // 0: endorsementapp.Change
	(*endorsement.ChangeData)(nil), // 1: endorsement.ChangeData
	(*proto.Interval)(nil),         // 2: common.Interval
}
var file_endorsementapp_change_proto_depIdxs = []int32{
	1, // 0: endorsementapp.Change.data:type_name -> endorsement.ChangeData
	2, // 1: endorsementapp.Change.effectiveInterval:type_name -> common.Interval
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_endorsementapp_change_proto_init() }
func file_endorsementapp_change_proto_init() {
	if File_endorsementapp_change_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_endorsementapp_change_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Change); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_endorsementapp_change_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_endorsementapp_change_proto_goTypes,
		DependencyIndexes: file_endorsementapp_change_proto_depIdxs,
		MessageInfos:      file_endorsementapp_change_proto_msgTypes,
	}.Build()
	File_endorsementapp_change_proto = out.File
	file_endorsementapp_change_proto_rawDesc = nil
	file_endorsementapp_change_proto_goTypes = nil
	file_endorsementapp_change_proto_depIdxs = nil
}
