// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: feature_store/definitions.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FeatureName int32

const (
	FeatureName_Unknown                     FeatureName = 0
	FeatureName_MockDriverScores            FeatureName = 1
	FeatureName_HazardDistance              FeatureName = 2
	FeatureName_HazardDuration              FeatureName = 3
	FeatureName_HazardStates                FeatureName = 4
	FeatureName_CarrierLoyaltyTable         FeatureName = 5
	FeatureName_CarrierLoyaltyInsight       FeatureName = 6
	FeatureName_BucketRadiusOfOperations    FeatureName = 7
	FeatureName_DailyVinMileage             FeatureName = 8
	FeatureName_FleetRunRate                FeatureName = 9
	FeatureName_VinTelematicsSummary        FeatureName = 10
	FeatureName_MileageWeightedCrashFreqTRS FeatureName = 11
	FeatureName_StateZoneMileage            FeatureName = 12
	FeatureName_NirvanaRiskScore            FeatureName = 13
	FeatureName_NirvanaVinRiskScore         FeatureName = 14
	FeatureName_VinHaulClusterTag           FeatureName = 15
	FeatureName_NirvanaVinRiskScoreCluster  FeatureName = 16
)

// Enum value maps for FeatureName.
var (
	FeatureName_name = map[int32]string{
		0:  "Unknown",
		1:  "MockDriverScores",
		2:  "HazardDistance",
		3:  "HazardDuration",
		4:  "HazardStates",
		5:  "CarrierLoyaltyTable",
		6:  "CarrierLoyaltyInsight",
		7:  "BucketRadiusOfOperations",
		8:  "DailyVinMileage",
		9:  "FleetRunRate",
		10: "VinTelematicsSummary",
		11: "MileageWeightedCrashFreqTRS",
		12: "StateZoneMileage",
		13: "NirvanaRiskScore",
		14: "NirvanaVinRiskScore",
		15: "VinHaulClusterTag",
		16: "NirvanaVinRiskScoreCluster",
	}
	FeatureName_value = map[string]int32{
		"Unknown":                     0,
		"MockDriverScores":            1,
		"HazardDistance":              2,
		"HazardDuration":              3,
		"HazardStates":                4,
		"CarrierLoyaltyTable":         5,
		"CarrierLoyaltyInsight":       6,
		"BucketRadiusOfOperations":    7,
		"DailyVinMileage":             8,
		"FleetRunRate":                9,
		"VinTelematicsSummary":        10,
		"MileageWeightedCrashFreqTRS": 11,
		"StateZoneMileage":            12,
		"NirvanaRiskScore":            13,
		"NirvanaVinRiskScore":         14,
		"VinHaulClusterTag":           15,
		"NirvanaVinRiskScoreCluster":  16,
	}
)

func (x FeatureName) Enum() *FeatureName {
	p := new(FeatureName)
	*p = x
	return p
}

func (x FeatureName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeatureName) Descriptor() protoreflect.EnumDescriptor {
	return file_feature_store_definitions_proto_enumTypes[0].Descriptor()
}

func (FeatureName) Type() protoreflect.EnumType {
	return &file_feature_store_definitions_proto_enumTypes[0]
}

func (x FeatureName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeatureName.Descriptor instead.
func (FeatureName) EnumDescriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0}
}

type CarrierLoyaltyTableV1_DurationType int32

const (
	CarrierLoyaltyTableV1_Recent  CarrierLoyaltyTableV1_DurationType = 0
	CarrierLoyaltyTableV1_Old     CarrierLoyaltyTableV1_DurationType = 1
	CarrierLoyaltyTableV1_Overall CarrierLoyaltyTableV1_DurationType = 2
)

// Enum value maps for CarrierLoyaltyTableV1_DurationType.
var (
	CarrierLoyaltyTableV1_DurationType_name = map[int32]string{
		0: "Recent",
		1: "Old",
		2: "Overall",
	}
	CarrierLoyaltyTableV1_DurationType_value = map[string]int32{
		"Recent":  0,
		"Old":     1,
		"Overall": 2,
	}
)

func (x CarrierLoyaltyTableV1_DurationType) Enum() *CarrierLoyaltyTableV1_DurationType {
	p := new(CarrierLoyaltyTableV1_DurationType)
	*p = x
	return p
}

func (x CarrierLoyaltyTableV1_DurationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CarrierLoyaltyTableV1_DurationType) Descriptor() protoreflect.EnumDescriptor {
	return file_feature_store_definitions_proto_enumTypes[1].Descriptor()
}

func (CarrierLoyaltyTableV1_DurationType) Type() protoreflect.EnumType {
	return &file_feature_store_definitions_proto_enumTypes[1]
}

func (x CarrierLoyaltyTableV1_DurationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CarrierLoyaltyTableV1_DurationType.Descriptor instead.
func (CarrierLoyaltyTableV1_DurationType) EnumDescriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{7, 0}
}

type Catalog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MockDriverScores            *Catalog_MockDriverScoresTypes            `protobuf:"bytes,1,opt,name=MockDriverScores,proto3" json:"MockDriverScores,omitempty"`
	HazardDistance              *Catalog_HazardDistanceTypes              `protobuf:"bytes,2,opt,name=HazardDistance,proto3" json:"HazardDistance,omitempty"`
	HazardDuration              *Catalog_HazardDurationTypes              `protobuf:"bytes,3,opt,name=HazardDuration,proto3" json:"HazardDuration,omitempty"`
	HazardStates                *Catalog_HazardStatesTypes                `protobuf:"bytes,4,opt,name=HazardStates,proto3" json:"HazardStates,omitempty"`
	CarrierLoyaltyTable         *Catalog_CarrierLoyaltyTableTypes         `protobuf:"bytes,5,opt,name=CarrierLoyaltyTable,proto3" json:"CarrierLoyaltyTable,omitempty"`
	CarrierLoyaltyInsight       *Catalog_CarrierLoyaltyInsightTypes       `protobuf:"bytes,6,opt,name=CarrierLoyaltyInsight,proto3" json:"CarrierLoyaltyInsight,omitempty"`
	BucketRadiusOfOperations    *Catalog_BucketRadiusOfOperationsTypes    `protobuf:"bytes,7,opt,name=BucketRadiusOfOperations,proto3" json:"BucketRadiusOfOperations,omitempty"`
	DailyVinMileage             *Catalog_DailyVinMileageTypes             `protobuf:"bytes,8,opt,name=DailyVinMileage,proto3" json:"DailyVinMileage,omitempty"`
	FleetRunRate                *Catalog_FleetRunRateTypes                `protobuf:"bytes,9,opt,name=FleetRunRate,proto3" json:"FleetRunRate,omitempty"`
	VinTelematicsSummary        *Catalog_VinTelematicsSummaryTypes        `protobuf:"bytes,10,opt,name=VinTelematicsSummary,proto3" json:"VinTelematicsSummary,omitempty"`
	MileageWeightedCrashFreqTRS *Catalog_MileageWeightedCrashFreqTRSTypes `protobuf:"bytes,11,opt,name=MileageWeightedCrashFreqTRS,proto3" json:"MileageWeightedCrashFreqTRS,omitempty"`
	StateZoneMileage            *Catalog_StateZoneMileageTypes            `protobuf:"bytes,12,opt,name=StateZoneMileage,proto3" json:"StateZoneMileage,omitempty"`
	NirvanaRiskScore            *Catalog_NirvanaRiskScoreTypes            `protobuf:"bytes,13,opt,name=NirvanaRiskScore,proto3" json:"NirvanaRiskScore,omitempty"`
	NirvanaVinRiskScore         *Catalog_NirvanaVinRiskScoreTypes         `protobuf:"bytes,14,opt,name=NirvanaVinRiskScore,proto3" json:"NirvanaVinRiskScore,omitempty"`
	VinHaulClusterTag           *Catalog_VinHaulClusterTagTypes           `protobuf:"bytes,15,opt,name=VinHaulClusterTag,proto3" json:"VinHaulClusterTag,omitempty"`
	NirvanaVinRiskScoreCluster  *Catalog_NirvanaVinRiskScoreClusterTypes  `protobuf:"bytes,16,opt,name=NirvanaVinRiskScoreCluster,proto3" json:"NirvanaVinRiskScoreCluster,omitempty"`
}

func (x *Catalog) Reset() {
	*x = Catalog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog) ProtoMessage() {}

func (x *Catalog) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog.ProtoReflect.Descriptor instead.
func (*Catalog) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0}
}

func (x *Catalog) GetMockDriverScores() *Catalog_MockDriverScoresTypes {
	if x != nil {
		return x.MockDriverScores
	}
	return nil
}

func (x *Catalog) GetHazardDistance() *Catalog_HazardDistanceTypes {
	if x != nil {
		return x.HazardDistance
	}
	return nil
}

func (x *Catalog) GetHazardDuration() *Catalog_HazardDurationTypes {
	if x != nil {
		return x.HazardDuration
	}
	return nil
}

func (x *Catalog) GetHazardStates() *Catalog_HazardStatesTypes {
	if x != nil {
		return x.HazardStates
	}
	return nil
}

func (x *Catalog) GetCarrierLoyaltyTable() *Catalog_CarrierLoyaltyTableTypes {
	if x != nil {
		return x.CarrierLoyaltyTable
	}
	return nil
}

func (x *Catalog) GetCarrierLoyaltyInsight() *Catalog_CarrierLoyaltyInsightTypes {
	if x != nil {
		return x.CarrierLoyaltyInsight
	}
	return nil
}

func (x *Catalog) GetBucketRadiusOfOperations() *Catalog_BucketRadiusOfOperationsTypes {
	if x != nil {
		return x.BucketRadiusOfOperations
	}
	return nil
}

func (x *Catalog) GetDailyVinMileage() *Catalog_DailyVinMileageTypes {
	if x != nil {
		return x.DailyVinMileage
	}
	return nil
}

func (x *Catalog) GetFleetRunRate() *Catalog_FleetRunRateTypes {
	if x != nil {
		return x.FleetRunRate
	}
	return nil
}

func (x *Catalog) GetVinTelematicsSummary() *Catalog_VinTelematicsSummaryTypes {
	if x != nil {
		return x.VinTelematicsSummary
	}
	return nil
}

func (x *Catalog) GetMileageWeightedCrashFreqTRS() *Catalog_MileageWeightedCrashFreqTRSTypes {
	if x != nil {
		return x.MileageWeightedCrashFreqTRS
	}
	return nil
}

func (x *Catalog) GetStateZoneMileage() *Catalog_StateZoneMileageTypes {
	if x != nil {
		return x.StateZoneMileage
	}
	return nil
}

func (x *Catalog) GetNirvanaRiskScore() *Catalog_NirvanaRiskScoreTypes {
	if x != nil {
		return x.NirvanaRiskScore
	}
	return nil
}

func (x *Catalog) GetNirvanaVinRiskScore() *Catalog_NirvanaVinRiskScoreTypes {
	if x != nil {
		return x.NirvanaVinRiskScore
	}
	return nil
}

func (x *Catalog) GetVinHaulClusterTag() *Catalog_VinHaulClusterTagTypes {
	if x != nil {
		return x.VinHaulClusterTag
	}
	return nil
}

func (x *Catalog) GetNirvanaVinRiskScoreCluster() *Catalog_NirvanaVinRiskScoreClusterTypes {
	if x != nil {
		return x.NirvanaVinRiskScoreCluster
	}
	return nil
}

type MockDriverScoresV0 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dot      int64              `protobuf:"varint,1,opt,name=dot,proto3" json:"dot,omitempty"`
	Interval *Interval          `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
	Scores   []*MockDriverScore `protobuf:"bytes,3,rep,name=scores,proto3" json:"scores,omitempty"`
}

func (x *MockDriverScoresV0) Reset() {
	*x = MockDriverScoresV0{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MockDriverScoresV0) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MockDriverScoresV0) ProtoMessage() {}

func (x *MockDriverScoresV0) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MockDriverScoresV0.ProtoReflect.Descriptor instead.
func (*MockDriverScoresV0) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{1}
}

func (x *MockDriverScoresV0) GetDot() int64 {
	if x != nil {
		return x.Dot
	}
	return 0
}

func (x *MockDriverScoresV0) GetInterval() *Interval {
	if x != nil {
		return x.Interval
	}
	return nil
}

func (x *MockDriverScoresV0) GetScores() []*MockDriverScore {
	if x != nil {
		return x.Scores
	}
	return nil
}

type MockDriverScoresV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dot      int64                       `protobuf:"varint,1,opt,name=dot,proto3" json:"dot,omitempty"`
	Interval *Interval                   `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
	HandleId string                      `protobuf:"bytes,3,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	Scores   map[string]*MockDriverScore `protobuf:"bytes,4,rep,name=scores,proto3" json:"scores,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MockDriverScoresV1) Reset() {
	*x = MockDriverScoresV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MockDriverScoresV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MockDriverScoresV1) ProtoMessage() {}

func (x *MockDriverScoresV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MockDriverScoresV1.ProtoReflect.Descriptor instead.
func (*MockDriverScoresV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{2}
}

func (x *MockDriverScoresV1) GetDot() int64 {
	if x != nil {
		return x.Dot
	}
	return 0
}

func (x *MockDriverScoresV1) GetInterval() *Interval {
	if x != nil {
		return x.Interval
	}
	return nil
}

func (x *MockDriverScoresV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *MockDriverScoresV1) GetScores() map[string]*MockDriverScore {
	if x != nil {
		return x.Scores
	}
	return nil
}

type HazardDistanceV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId      string                 `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	DistanceMiles float32                `protobuf:"fixed32,2,opt,name=distance_miles,json=distanceMiles,proto3" json:"distance_miles,omitempty"`
	Percentage    float32                `protobuf:"fixed32,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *HazardDistanceV1) Reset() {
	*x = HazardDistanceV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HazardDistanceV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HazardDistanceV1) ProtoMessage() {}

func (x *HazardDistanceV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HazardDistanceV1.ProtoReflect.Descriptor instead.
func (*HazardDistanceV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{3}
}

func (x *HazardDistanceV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *HazardDistanceV1) GetDistanceMiles() float32 {
	if x != nil {
		return x.DistanceMiles
	}
	return 0
}

func (x *HazardDistanceV1) GetPercentage() float32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

func (x *HazardDistanceV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type HazardDurationV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId      string                 `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	DurationHours float32                `protobuf:"fixed32,2,opt,name=duration_hours,json=durationHours,proto3" json:"duration_hours,omitempty"`
	Percentage    float32                `protobuf:"fixed32,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *HazardDurationV1) Reset() {
	*x = HazardDurationV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HazardDurationV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HazardDurationV1) ProtoMessage() {}

func (x *HazardDurationV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HazardDurationV1.ProtoReflect.Descriptor instead.
func (*HazardDurationV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{4}
}

func (x *HazardDurationV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *HazardDurationV1) GetDurationHours() float32 {
	if x != nil {
		return x.DurationHours
	}
	return 0
}

func (x *HazardDurationV1) GetPercentage() float32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

func (x *HazardDurationV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type HazardDurationV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId        string                 `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	DurationSeconds float32                `protobuf:"fixed32,2,opt,name=duration_seconds,json=durationSeconds,proto3" json:"duration_seconds,omitempty"`
	Percentage      float32                `protobuf:"fixed32,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *HazardDurationV2) Reset() {
	*x = HazardDurationV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HazardDurationV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HazardDurationV2) ProtoMessage() {}

func (x *HazardDurationV2) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HazardDurationV2.ProtoReflect.Descriptor instead.
func (*HazardDurationV2) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{5}
}

func (x *HazardDurationV2) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *HazardDurationV2) GetDurationSeconds() float32 {
	if x != nil {
		return x.DurationSeconds
	}
	return 0
}

func (x *HazardDurationV2) GetPercentage() float32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

func (x *HazardDurationV2) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type HazardStatesV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId         string                           `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	HazardStateTable []*HazardStatesV1_HazardStateRow `protobuf:"bytes,2,rep,name=hazard_state_table,json=hazardStateTable,proto3" json:"hazard_state_table,omitempty"`
	CreatedAt        *timestamppb.Timestamp           `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *HazardStatesV1) Reset() {
	*x = HazardStatesV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HazardStatesV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HazardStatesV1) ProtoMessage() {}

func (x *HazardStatesV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HazardStatesV1.ProtoReflect.Descriptor instead.
func (*HazardStatesV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{6}
}

func (x *HazardStatesV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *HazardStatesV1) GetHazardStateTable() []*HazardStatesV1_HazardStateRow {
	if x != nil {
		return x.HazardStateTable
	}
	return nil
}

func (x *HazardStatesV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type CarrierLoyaltyTableV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId            string                                  `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	ApplicationId       string                                  `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	CarrierLoyaltyTable []*CarrierLoyaltyTableV1_CLDataframeRow `protobuf:"bytes,3,rep,name=carrier_loyalty_table,json=carrierLoyaltyTable,proto3" json:"carrier_loyalty_table,omitempty"`
	CreatedAt           *timestamppb.Timestamp                  `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *CarrierLoyaltyTableV1) Reset() {
	*x = CarrierLoyaltyTableV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarrierLoyaltyTableV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarrierLoyaltyTableV1) ProtoMessage() {}

func (x *CarrierLoyaltyTableV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarrierLoyaltyTableV1.ProtoReflect.Descriptor instead.
func (*CarrierLoyaltyTableV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{7}
}

func (x *CarrierLoyaltyTableV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *CarrierLoyaltyTableV1) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *CarrierLoyaltyTableV1) GetCarrierLoyaltyTable() []*CarrierLoyaltyTableV1_CLDataframeRow {
	if x != nil {
		return x.CarrierLoyaltyTable
	}
	return nil
}

func (x *CarrierLoyaltyTableV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type CarrierLoyaltyTableV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId              string                                  `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ApplicationReviewId        string                                  `protobuf:"bytes,2,opt,name=application_review_id,json=applicationReviewId,proto3" json:"application_review_id,omitempty"`
	CarrierLoyaltySummaryTable []*CarrierLoyaltyTableV2_CLDataframeRow `protobuf:"bytes,3,rep,name=carrier_loyalty_summary_table,json=carrierLoyaltySummaryTable,proto3" json:"carrier_loyalty_summary_table,omitempty"`
	CreatedAt                  *timestamppb.Timestamp                  `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *CarrierLoyaltyTableV2) Reset() {
	*x = CarrierLoyaltyTableV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarrierLoyaltyTableV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarrierLoyaltyTableV2) ProtoMessage() {}

func (x *CarrierLoyaltyTableV2) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarrierLoyaltyTableV2.ProtoReflect.Descriptor instead.
func (*CarrierLoyaltyTableV2) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{8}
}

func (x *CarrierLoyaltyTableV2) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *CarrierLoyaltyTableV2) GetApplicationReviewId() string {
	if x != nil {
		return x.ApplicationReviewId
	}
	return ""
}

func (x *CarrierLoyaltyTableV2) GetCarrierLoyaltySummaryTable() []*CarrierLoyaltyTableV2_CLDataframeRow {
	if x != nil {
		return x.CarrierLoyaltySummaryTable
	}
	return nil
}

func (x *CarrierLoyaltyTableV2) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type CarrierLoyaltyInsightV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId              string                 `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	ApplicationId         string                 `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	CarrierLoyaltyInsight string                 `protobuf:"bytes,3,opt,name=carrier_loyalty_insight,json=carrierLoyaltyInsight,proto3" json:"carrier_loyalty_insight,omitempty"`
	InsuranceGapInsight   string                 `protobuf:"bytes,4,opt,name=insurance_gap_insight,json=insuranceGapInsight,proto3" json:"insurance_gap_insight,omitempty"`
	CreatedAt             *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *CarrierLoyaltyInsightV1) Reset() {
	*x = CarrierLoyaltyInsightV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarrierLoyaltyInsightV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarrierLoyaltyInsightV1) ProtoMessage() {}

func (x *CarrierLoyaltyInsightV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarrierLoyaltyInsightV1.ProtoReflect.Descriptor instead.
func (*CarrierLoyaltyInsightV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{9}
}

func (x *CarrierLoyaltyInsightV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *CarrierLoyaltyInsightV1) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *CarrierLoyaltyInsightV1) GetCarrierLoyaltyInsight() string {
	if x != nil {
		return x.CarrierLoyaltyInsight
	}
	return ""
}

func (x *CarrierLoyaltyInsightV1) GetInsuranceGapInsight() string {
	if x != nil {
		return x.InsuranceGapInsight
	}
	return ""
}

func (x *CarrierLoyaltyInsightV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type CarrierLoyaltyInsightV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId          string                 `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ApplicationReviewId    string                 `protobuf:"bytes,2,opt,name=application_review_id,json=applicationReviewId,proto3" json:"application_review_id,omitempty"`
	CarrierLoyaltyInsight  string                 `protobuf:"bytes,3,opt,name=carrier_loyalty_insight,json=carrierLoyaltyInsight,proto3" json:"carrier_loyalty_insight,omitempty"`
	CarrierLoyaltyRank     int64                  `protobuf:"varint,4,opt,name=carrier_loyalty_rank,json=carrierLoyaltyRank,proto3" json:"carrier_loyalty_rank,omitempty"`
	CarrierLoyaltyCategory string                 `protobuf:"bytes,5,opt,name=carrier_loyalty_category,json=carrierLoyaltyCategory,proto3" json:"carrier_loyalty_category,omitempty"`
	InsuranceGapInsight    string                 `protobuf:"bytes,6,opt,name=insurance_gap_insight,json=insuranceGapInsight,proto3" json:"insurance_gap_insight,omitempty"`
	InsuranceGapRank       int64                  `protobuf:"varint,7,opt,name=insurance_gap_rank,json=insuranceGapRank,proto3" json:"insurance_gap_rank,omitempty"`
	InsuranceRecords       string                 `protobuf:"bytes,8,opt,name=insurance_records,json=insuranceRecords,proto3" json:"insurance_records,omitempty"`
	CreatedAt              *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *CarrierLoyaltyInsightV2) Reset() {
	*x = CarrierLoyaltyInsightV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarrierLoyaltyInsightV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarrierLoyaltyInsightV2) ProtoMessage() {}

func (x *CarrierLoyaltyInsightV2) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarrierLoyaltyInsightV2.ProtoReflect.Descriptor instead.
func (*CarrierLoyaltyInsightV2) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{10}
}

func (x *CarrierLoyaltyInsightV2) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *CarrierLoyaltyInsightV2) GetApplicationReviewId() string {
	if x != nil {
		return x.ApplicationReviewId
	}
	return ""
}

func (x *CarrierLoyaltyInsightV2) GetCarrierLoyaltyInsight() string {
	if x != nil {
		return x.CarrierLoyaltyInsight
	}
	return ""
}

func (x *CarrierLoyaltyInsightV2) GetCarrierLoyaltyRank() int64 {
	if x != nil {
		return x.CarrierLoyaltyRank
	}
	return 0
}

func (x *CarrierLoyaltyInsightV2) GetCarrierLoyaltyCategory() string {
	if x != nil {
		return x.CarrierLoyaltyCategory
	}
	return ""
}

func (x *CarrierLoyaltyInsightV2) GetInsuranceGapInsight() string {
	if x != nil {
		return x.InsuranceGapInsight
	}
	return ""
}

func (x *CarrierLoyaltyInsightV2) GetInsuranceGapRank() int64 {
	if x != nil {
		return x.InsuranceGapRank
	}
	return 0
}

func (x *CarrierLoyaltyInsightV2) GetInsuranceRecords() string {
	if x != nil {
		return x.InsuranceRecords
	}
	return ""
}

func (x *CarrierLoyaltyInsightV2) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type BucketRadiusOfOperationsV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId                                   string                 `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	CreatedAt                                  *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	MileageRadiusBucketZeroToFifty             float32                `protobuf:"fixed32,3,opt,name=MileageRadiusBucketZeroToFifty,proto3" json:"MileageRadiusBucketZeroToFifty,omitempty"`
	MileageRadiusBucketFiftyToTwoHundred       float32                `protobuf:"fixed32,4,opt,name=MileageRadiusBucketFiftyToTwoHundred,proto3" json:"MileageRadiusBucketFiftyToTwoHundred,omitempty"`
	MileageRadiusBucketTwoHundredToFiveHundred float32                `protobuf:"fixed32,5,opt,name=MileageRadiusBucketTwoHundredToFiveHundred,proto3" json:"MileageRadiusBucketTwoHundredToFiveHundred,omitempty"`
	MileageRadiusBucketFiveHundredPlus         float32                `protobuf:"fixed32,6,opt,name=MileageRadiusBucketFiveHundredPlus,proto3" json:"MileageRadiusBucketFiveHundredPlus,omitempty"`
	Unknown                                    float32                `protobuf:"fixed32,7,opt,name=Unknown,proto3" json:"Unknown,omitempty"`
}

func (x *BucketRadiusOfOperationsV1) Reset() {
	*x = BucketRadiusOfOperationsV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BucketRadiusOfOperationsV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BucketRadiusOfOperationsV1) ProtoMessage() {}

func (x *BucketRadiusOfOperationsV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BucketRadiusOfOperationsV1.ProtoReflect.Descriptor instead.
func (*BucketRadiusOfOperationsV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{11}
}

func (x *BucketRadiusOfOperationsV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *BucketRadiusOfOperationsV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BucketRadiusOfOperationsV1) GetMileageRadiusBucketZeroToFifty() float32 {
	if x != nil {
		return x.MileageRadiusBucketZeroToFifty
	}
	return 0
}

func (x *BucketRadiusOfOperationsV1) GetMileageRadiusBucketFiftyToTwoHundred() float32 {
	if x != nil {
		return x.MileageRadiusBucketFiftyToTwoHundred
	}
	return 0
}

func (x *BucketRadiusOfOperationsV1) GetMileageRadiusBucketTwoHundredToFiveHundred() float32 {
	if x != nil {
		return x.MileageRadiusBucketTwoHundredToFiveHundred
	}
	return 0
}

func (x *BucketRadiusOfOperationsV1) GetMileageRadiusBucketFiveHundredPlus() float32 {
	if x != nil {
		return x.MileageRadiusBucketFiveHundredPlus
	}
	return 0
}

func (x *BucketRadiusOfOperationsV1) GetUnknown() float32 {
	if x != nil {
		return x.Unknown
	}
	return 0
}

type DailyVinMileageV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId      string                 `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	DistanceMiles float32                `protobuf:"fixed32,2,opt,name=distance_miles,json=distanceMiles,proto3" json:"distance_miles,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *DailyVinMileageV1) Reset() {
	*x = DailyVinMileageV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyVinMileageV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyVinMileageV1) ProtoMessage() {}

func (x *DailyVinMileageV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyVinMileageV1.ProtoReflect.Descriptor instead.
func (*DailyVinMileageV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{12}
}

func (x *DailyVinMileageV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *DailyVinMileageV1) GetDistanceMiles() float32 {
	if x != nil {
		return x.DistanceMiles
	}
	return 0
}

func (x *DailyVinMileageV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type FleetRunRateV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId                string                           `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	WeeklyFleetRunRateTable []*FleetRunRateV1_WRDataframeRow `protobuf:"bytes,2,rep,name=weekly_fleet_run_rate_table,json=weeklyFleetRunRateTable,proto3" json:"weekly_fleet_run_rate_table,omitempty"`
	CreatedAt               *timestamppb.Timestamp           `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *FleetRunRateV1) Reset() {
	*x = FleetRunRateV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetRunRateV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetRunRateV1) ProtoMessage() {}

func (x *FleetRunRateV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetRunRateV1.ProtoReflect.Descriptor instead.
func (*FleetRunRateV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{13}
}

func (x *FleetRunRateV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *FleetRunRateV1) GetWeeklyFleetRunRateTable() []*FleetRunRateV1_WRDataframeRow {
	if x != nil {
		return x.WeeklyFleetRunRateTable
	}
	return nil
}

func (x *FleetRunRateV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type VinTelematicsSummaryV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId                  string                                       `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	VinTelematicsSummaryTable []*VinTelematicsSummaryV1_VinSummaryTableRow `protobuf:"bytes,2,rep,name=vin_telematics_summary_table,json=vinTelematicsSummaryTable,proto3" json:"vin_telematics_summary_table,omitempty"`
	CreatedAt                 *timestamppb.Timestamp                       `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *VinTelematicsSummaryV1) Reset() {
	*x = VinTelematicsSummaryV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VinTelematicsSummaryV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VinTelematicsSummaryV1) ProtoMessage() {}

func (x *VinTelematicsSummaryV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VinTelematicsSummaryV1.ProtoReflect.Descriptor instead.
func (*VinTelematicsSummaryV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{14}
}

func (x *VinTelematicsSummaryV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *VinTelematicsSummaryV1) GetVinTelematicsSummaryTable() []*VinTelematicsSummaryV1_VinSummaryTableRow {
	if x != nil {
		return x.VinTelematicsSummaryTable
	}
	return nil
}

func (x *VinTelematicsSummaryV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type MileageWeightedCrashFreqTRSV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId                         string                                                          `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	MileageWeightedCrashFreqTRSTable []*MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow `protobuf:"bytes,2,rep,name=MileageWeightedCrashFreqTRSTable,proto3" json:"MileageWeightedCrashFreqTRSTable,omitempty"`
	CreatedAt                        *timestamppb.Timestamp                                          `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *MileageWeightedCrashFreqTRSV1) Reset() {
	*x = MileageWeightedCrashFreqTRSV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MileageWeightedCrashFreqTRSV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MileageWeightedCrashFreqTRSV1) ProtoMessage() {}

func (x *MileageWeightedCrashFreqTRSV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MileageWeightedCrashFreqTRSV1.ProtoReflect.Descriptor instead.
func (*MileageWeightedCrashFreqTRSV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{15}
}

func (x *MileageWeightedCrashFreqTRSV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *MileageWeightedCrashFreqTRSV1) GetMileageWeightedCrashFreqTRSTable() []*MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow {
	if x != nil {
		return x.MileageWeightedCrashFreqTRSTable
	}
	return nil
}

func (x *MileageWeightedCrashFreqTRSV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type StateZoneMileageV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId              string                                    `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	StateZoneMileageTable []*StateZoneMileageV1_StateZoneMileageRow `protobuf:"bytes,2,rep,name=StateZoneMileageTable,proto3" json:"StateZoneMileageTable,omitempty"`
	CreatedAt             *timestamppb.Timestamp                    `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *StateZoneMileageV1) Reset() {
	*x = StateZoneMileageV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StateZoneMileageV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateZoneMileageV1) ProtoMessage() {}

func (x *StateZoneMileageV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateZoneMileageV1.ProtoReflect.Descriptor instead.
func (*StateZoneMileageV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{16}
}

func (x *StateZoneMileageV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *StateZoneMileageV1) GetStateZoneMileageTable() []*StateZoneMileageV1_StateZoneMileageRow {
	if x != nil {
		return x.StateZoneMileageTable
	}
	return nil
}

func (x *StateZoneMileageV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type NirvanaRiskScoreV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId        string                                  `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	ScoreType       string                                  `protobuf:"bytes,2,opt,name=scoreType,proto3" json:"scoreType,omitempty"`
	ScoreVersion    string                                  `protobuf:"bytes,3,opt,name=scoreVersion,proto3" json:"scoreVersion,omitempty"`
	UwRubricVersion string                                  `protobuf:"bytes,4,opt,name=uwRubricVersion,proto3" json:"uwRubricVersion,omitempty"`
	RiskScoreTrend  []*NirvanaRiskScoreV1_RiskScoreTrendRow `protobuf:"bytes,5,rep,name=riskScoreTrend,proto3" json:"riskScoreTrend,omitempty"`
	UWRubric        []*NirvanaRiskScoreV1_UWRubricRow       `protobuf:"bytes,6,rep,name=UWRubric,proto3" json:"UWRubric,omitempty"`
	CreatedAt       *timestamppb.Timestamp                  `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *NirvanaRiskScoreV1) Reset() {
	*x = NirvanaRiskScoreV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV1) ProtoMessage() {}

func (x *NirvanaRiskScoreV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV1.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{17}
}

func (x *NirvanaRiskScoreV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *NirvanaRiskScoreV1) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

func (x *NirvanaRiskScoreV1) GetScoreVersion() string {
	if x != nil {
		return x.ScoreVersion
	}
	return ""
}

func (x *NirvanaRiskScoreV1) GetUwRubricVersion() string {
	if x != nil {
		return x.UwRubricVersion
	}
	return ""
}

func (x *NirvanaRiskScoreV1) GetRiskScoreTrend() []*NirvanaRiskScoreV1_RiskScoreTrendRow {
	if x != nil {
		return x.RiskScoreTrend
	}
	return nil
}

func (x *NirvanaRiskScoreV1) GetUWRubric() []*NirvanaRiskScoreV1_UWRubricRow {
	if x != nil {
		return x.UWRubric
	}
	return nil
}

func (x *NirvanaRiskScoreV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type NirvanaRiskScoreV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId        string                                  `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	ScoreType       string                                  `protobuf:"bytes,2,opt,name=scoreType,proto3" json:"scoreType,omitempty"`
	ScoreVersion    string                                  `protobuf:"bytes,3,opt,name=scoreVersion,proto3" json:"scoreVersion,omitempty"`
	UwRubricVersion string                                  `protobuf:"bytes,4,opt,name=uwRubricVersion,proto3" json:"uwRubricVersion,omitempty"`
	RiskScoreTrend  []*NirvanaRiskScoreV2_RiskScoreTrendRow `protobuf:"bytes,5,rep,name=riskScoreTrend,proto3" json:"riskScoreTrend,omitempty"`
	UWRubric        []*NirvanaRiskScoreV2_UWRubricRow       `protobuf:"bytes,6,rep,name=UWRubric,proto3" json:"UWRubric,omitempty"`
	CreatedAt       *timestamppb.Timestamp                  `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *NirvanaRiskScoreV2) Reset() {
	*x = NirvanaRiskScoreV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV2) ProtoMessage() {}

func (x *NirvanaRiskScoreV2) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV2.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV2) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{18}
}

func (x *NirvanaRiskScoreV2) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *NirvanaRiskScoreV2) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

func (x *NirvanaRiskScoreV2) GetScoreVersion() string {
	if x != nil {
		return x.ScoreVersion
	}
	return ""
}

func (x *NirvanaRiskScoreV2) GetUwRubricVersion() string {
	if x != nil {
		return x.UwRubricVersion
	}
	return ""
}

func (x *NirvanaRiskScoreV2) GetRiskScoreTrend() []*NirvanaRiskScoreV2_RiskScoreTrendRow {
	if x != nil {
		return x.RiskScoreTrend
	}
	return nil
}

func (x *NirvanaRiskScoreV2) GetUWRubric() []*NirvanaRiskScoreV2_UWRubricRow {
	if x != nil {
		return x.UWRubric
	}
	return nil
}

func (x *NirvanaRiskScoreV2) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type NirvanaRiskScoreV3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId        string                                  `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	ScoreType       string                                  `protobuf:"bytes,2,opt,name=scoreType,proto3" json:"scoreType,omitempty"`
	ScoreVersion    string                                  `protobuf:"bytes,3,opt,name=scoreVersion,proto3" json:"scoreVersion,omitempty"`
	UwRubricVersion string                                  `protobuf:"bytes,4,opt,name=uwRubricVersion,proto3" json:"uwRubricVersion,omitempty"`
	RiskScoreTrend  []*NirvanaRiskScoreV3_RiskScoreTrendRow `protobuf:"bytes,5,rep,name=riskScoreTrend,proto3" json:"riskScoreTrend,omitempty"`
	UWRubric        []*NirvanaRiskScoreV3_UWRubricRow       `protobuf:"bytes,6,rep,name=UWRubric,proto3" json:"UWRubric,omitempty"`
	CreatedAt       *timestamppb.Timestamp                  `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *NirvanaRiskScoreV3) Reset() {
	*x = NirvanaRiskScoreV3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV3) ProtoMessage() {}

func (x *NirvanaRiskScoreV3) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV3.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV3) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{19}
}

func (x *NirvanaRiskScoreV3) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *NirvanaRiskScoreV3) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

func (x *NirvanaRiskScoreV3) GetScoreVersion() string {
	if x != nil {
		return x.ScoreVersion
	}
	return ""
}

func (x *NirvanaRiskScoreV3) GetUwRubricVersion() string {
	if x != nil {
		return x.UwRubricVersion
	}
	return ""
}

func (x *NirvanaRiskScoreV3) GetRiskScoreTrend() []*NirvanaRiskScoreV3_RiskScoreTrendRow {
	if x != nil {
		return x.RiskScoreTrend
	}
	return nil
}

func (x *NirvanaRiskScoreV3) GetUWRubric() []*NirvanaRiskScoreV3_UWRubricRow {
	if x != nil {
		return x.UWRubric
	}
	return nil
}

func (x *NirvanaRiskScoreV3) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type NirvanaRiskScoreV4 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId        string                                  `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	ScoreType       string                                  `protobuf:"bytes,2,opt,name=scoreType,proto3" json:"scoreType,omitempty"`
	ScoreVersion    string                                  `protobuf:"bytes,3,opt,name=scoreVersion,proto3" json:"scoreVersion,omitempty"`
	UwRubricVersion string                                  `protobuf:"bytes,4,opt,name=uwRubricVersion,proto3" json:"uwRubricVersion,omitempty"`
	RiskScoreTrend  []*NirvanaRiskScoreV4_RiskScoreTrendRow `protobuf:"bytes,5,rep,name=riskScoreTrend,proto3" json:"riskScoreTrend,omitempty"`
	UWRubric        []*NirvanaRiskScoreV4_UWRubricRow       `protobuf:"bytes,6,rep,name=UWRubric,proto3" json:"UWRubric,omitempty"`
	CreatedAt       *timestamppb.Timestamp                  `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *NirvanaRiskScoreV4) Reset() {
	*x = NirvanaRiskScoreV4{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV4) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV4) ProtoMessage() {}

func (x *NirvanaRiskScoreV4) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV4.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV4) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{20}
}

func (x *NirvanaRiskScoreV4) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *NirvanaRiskScoreV4) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

func (x *NirvanaRiskScoreV4) GetScoreVersion() string {
	if x != nil {
		return x.ScoreVersion
	}
	return ""
}

func (x *NirvanaRiskScoreV4) GetUwRubricVersion() string {
	if x != nil {
		return x.UwRubricVersion
	}
	return ""
}

func (x *NirvanaRiskScoreV4) GetRiskScoreTrend() []*NirvanaRiskScoreV4_RiskScoreTrendRow {
	if x != nil {
		return x.RiskScoreTrend
	}
	return nil
}

func (x *NirvanaRiskScoreV4) GetUWRubric() []*NirvanaRiskScoreV4_UWRubricRow {
	if x != nil {
		return x.UWRubric
	}
	return nil
}

func (x *NirvanaRiskScoreV4) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type NirvanaRiskScoreV5 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId        string                                  `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	ScoreType       string                                  `protobuf:"bytes,2,opt,name=scoreType,proto3" json:"scoreType,omitempty"`
	ScoreVersion    string                                  `protobuf:"bytes,3,opt,name=scoreVersion,proto3" json:"scoreVersion,omitempty"`
	UwRubricVersion string                                  `protobuf:"bytes,4,opt,name=uwRubricVersion,proto3" json:"uwRubricVersion,omitempty"`
	RiskScoreTrend  []*NirvanaRiskScoreV5_RiskScoreTrendRow `protobuf:"bytes,5,rep,name=riskScoreTrend,proto3" json:"riskScoreTrend,omitempty"`
	UWRubric        []*NirvanaRiskScoreV5_UWRubricRow       `protobuf:"bytes,6,rep,name=UWRubric,proto3" json:"UWRubric,omitempty"`
	CreatedAt       *timestamppb.Timestamp                  `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *NirvanaRiskScoreV5) Reset() {
	*x = NirvanaRiskScoreV5{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV5) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV5) ProtoMessage() {}

func (x *NirvanaRiskScoreV5) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV5.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV5) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{21}
}

func (x *NirvanaRiskScoreV5) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *NirvanaRiskScoreV5) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

func (x *NirvanaRiskScoreV5) GetScoreVersion() string {
	if x != nil {
		return x.ScoreVersion
	}
	return ""
}

func (x *NirvanaRiskScoreV5) GetUwRubricVersion() string {
	if x != nil {
		return x.UwRubricVersion
	}
	return ""
}

func (x *NirvanaRiskScoreV5) GetRiskScoreTrend() []*NirvanaRiskScoreV5_RiskScoreTrendRow {
	if x != nil {
		return x.RiskScoreTrend
	}
	return nil
}

func (x *NirvanaRiskScoreV5) GetUWRubric() []*NirvanaRiskScoreV5_UWRubricRow {
	if x != nil {
		return x.UWRubric
	}
	return nil
}

func (x *NirvanaRiskScoreV5) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type NirvanaVinRiskScoreV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId          string                                        `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	ScoreType         string                                        `protobuf:"bytes,2,opt,name=scoreType,proto3" json:"scoreType,omitempty"`
	ScoreVersion      string                                        `protobuf:"bytes,3,opt,name=scoreVersion,proto3" json:"scoreVersion,omitempty"`
	VinRiskScoreTrend []*NirvanaVinRiskScoreV1_VinRiskScoreTrendRow `protobuf:"bytes,4,rep,name=vinRiskScoreTrend,proto3" json:"vinRiskScoreTrend,omitempty"`
	CreatedAt         *timestamppb.Timestamp                        `protobuf:"bytes,5,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
}

func (x *NirvanaVinRiskScoreV1) Reset() {
	*x = NirvanaVinRiskScoreV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaVinRiskScoreV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaVinRiskScoreV1) ProtoMessage() {}

func (x *NirvanaVinRiskScoreV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaVinRiskScoreV1.ProtoReflect.Descriptor instead.
func (*NirvanaVinRiskScoreV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{22}
}

func (x *NirvanaVinRiskScoreV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *NirvanaVinRiskScoreV1) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

func (x *NirvanaVinRiskScoreV1) GetScoreVersion() string {
	if x != nil {
		return x.ScoreVersion
	}
	return ""
}

func (x *NirvanaVinRiskScoreV1) GetVinRiskScoreTrend() []*NirvanaVinRiskScoreV1_VinRiskScoreTrendRow {
	if x != nil {
		return x.VinRiskScoreTrend
	}
	return nil
}

func (x *NirvanaVinRiskScoreV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type VinHaulClusterTagV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId            string                                        `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	ClassifierType      string                                        `protobuf:"bytes,2,opt,name=classifierType,proto3" json:"classifierType,omitempty"`
	ClassifierVersion   string                                        `protobuf:"bytes,3,opt,name=classifierVersion,proto3" json:"classifierVersion,omitempty"`
	VinHaulClusterTrend []*VinHaulClusterTagV1_VinHaulClusterTrendRow `protobuf:"bytes,4,rep,name=vinHaulClusterTrend,proto3" json:"vinHaulClusterTrend,omitempty"`
	CreatedAt           *timestamppb.Timestamp                        `protobuf:"bytes,5,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
}

func (x *VinHaulClusterTagV1) Reset() {
	*x = VinHaulClusterTagV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VinHaulClusterTagV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VinHaulClusterTagV1) ProtoMessage() {}

func (x *VinHaulClusterTagV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VinHaulClusterTagV1.ProtoReflect.Descriptor instead.
func (*VinHaulClusterTagV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{23}
}

func (x *VinHaulClusterTagV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *VinHaulClusterTagV1) GetClassifierType() string {
	if x != nil {
		return x.ClassifierType
	}
	return ""
}

func (x *VinHaulClusterTagV1) GetClassifierVersion() string {
	if x != nil {
		return x.ClassifierVersion
	}
	return ""
}

func (x *VinHaulClusterTagV1) GetVinHaulClusterTrend() []*VinHaulClusterTagV1_VinHaulClusterTrendRow {
	if x != nil {
		return x.VinHaulClusterTrend
	}
	return nil
}

func (x *VinHaulClusterTagV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type NirvanaVinRiskScoreClusterV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId         string                                             `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	ScoreType        string                                             `protobuf:"bytes,2,opt,name=scoreType,proto3" json:"scoreType,omitempty"`
	VinClustersTrend []*NirvanaVinRiskScoreClusterV1VinClustersTrendRow `protobuf:"bytes,3,rep,name=vinClustersTrend,proto3" json:"vinClustersTrend,omitempty"`
	CreatedAt        *timestamppb.Timestamp                             `protobuf:"bytes,4,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
}

func (x *NirvanaVinRiskScoreClusterV1) Reset() {
	*x = NirvanaVinRiskScoreClusterV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaVinRiskScoreClusterV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaVinRiskScoreClusterV1) ProtoMessage() {}

func (x *NirvanaVinRiskScoreClusterV1) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaVinRiskScoreClusterV1.ProtoReflect.Descriptor instead.
func (*NirvanaVinRiskScoreClusterV1) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{24}
}

func (x *NirvanaVinRiskScoreClusterV1) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV1) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV1) GetVinClustersTrend() []*NirvanaVinRiskScoreClusterV1VinClustersTrendRow {
	if x != nil {
		return x.VinClustersTrend
	}
	return nil
}

func (x *NirvanaVinRiskScoreClusterV1) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type NirvanaVinRiskScoreClusterV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId         string                                             `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	ScoreType        string                                             `protobuf:"bytes,2,opt,name=scoreType,proto3" json:"scoreType,omitempty"`
	VinClustersTrend []*NirvanaVinRiskScoreClusterV2VinClustersTrendRow `protobuf:"bytes,3,rep,name=vinClustersTrend,proto3" json:"vinClustersTrend,omitempty"`
	CreatedAt        *timestamppb.Timestamp                             `protobuf:"bytes,4,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
}

func (x *NirvanaVinRiskScoreClusterV2) Reset() {
	*x = NirvanaVinRiskScoreClusterV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaVinRiskScoreClusterV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaVinRiskScoreClusterV2) ProtoMessage() {}

func (x *NirvanaVinRiskScoreClusterV2) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaVinRiskScoreClusterV2.ProtoReflect.Descriptor instead.
func (*NirvanaVinRiskScoreClusterV2) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{25}
}

func (x *NirvanaVinRiskScoreClusterV2) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV2) GetScoreType() string {
	if x != nil {
		return x.ScoreType
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV2) GetVinClustersTrend() []*NirvanaVinRiskScoreClusterV2VinClustersTrendRow {
	if x != nil {
		return x.VinClustersTrend
	}
	return nil
}

func (x *NirvanaVinRiskScoreClusterV2) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type Catalog_MockDriverScoresTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V0 *MockDriverScoresV0 `protobuf:"bytes,1,opt,name=v0,proto3" json:"v0,omitempty"`
	V1 *MockDriverScoresV1 `protobuf:"bytes,2,opt,name=v1,proto3" json:"v1,omitempty"`
	V2 *MockDriverScoresV0 `protobuf:"bytes,3,opt,name=v2,proto3" json:"v2,omitempty"`
}

func (x *Catalog_MockDriverScoresTypes) Reset() {
	*x = Catalog_MockDriverScoresTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_MockDriverScoresTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_MockDriverScoresTypes) ProtoMessage() {}

func (x *Catalog_MockDriverScoresTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_MockDriverScoresTypes.ProtoReflect.Descriptor instead.
func (*Catalog_MockDriverScoresTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Catalog_MockDriverScoresTypes) GetV0() *MockDriverScoresV0 {
	if x != nil {
		return x.V0
	}
	return nil
}

func (x *Catalog_MockDriverScoresTypes) GetV1() *MockDriverScoresV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

func (x *Catalog_MockDriverScoresTypes) GetV2() *MockDriverScoresV0 {
	if x != nil {
		return x.V2
	}
	return nil
}

type Catalog_HazardDistanceTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *HazardDistanceV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
}

func (x *Catalog_HazardDistanceTypes) Reset() {
	*x = Catalog_HazardDistanceTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_HazardDistanceTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_HazardDistanceTypes) ProtoMessage() {}

func (x *Catalog_HazardDistanceTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_HazardDistanceTypes.ProtoReflect.Descriptor instead.
func (*Catalog_HazardDistanceTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Catalog_HazardDistanceTypes) GetV1() *HazardDistanceV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

type Catalog_HazardDurationTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *HazardDurationV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
	V2 *HazardDurationV2 `protobuf:"bytes,2,opt,name=v2,proto3" json:"v2,omitempty"`
}

func (x *Catalog_HazardDurationTypes) Reset() {
	*x = Catalog_HazardDurationTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_HazardDurationTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_HazardDurationTypes) ProtoMessage() {}

func (x *Catalog_HazardDurationTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_HazardDurationTypes.ProtoReflect.Descriptor instead.
func (*Catalog_HazardDurationTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 2}
}

func (x *Catalog_HazardDurationTypes) GetV1() *HazardDurationV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

func (x *Catalog_HazardDurationTypes) GetV2() *HazardDurationV2 {
	if x != nil {
		return x.V2
	}
	return nil
}

type Catalog_HazardStatesTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *HazardStatesV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
}

func (x *Catalog_HazardStatesTypes) Reset() {
	*x = Catalog_HazardStatesTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_HazardStatesTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_HazardStatesTypes) ProtoMessage() {}

func (x *Catalog_HazardStatesTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_HazardStatesTypes.ProtoReflect.Descriptor instead.
func (*Catalog_HazardStatesTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 3}
}

func (x *Catalog_HazardStatesTypes) GetV1() *HazardStatesV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

type Catalog_CarrierLoyaltyTableTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *CarrierLoyaltyTableV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
	V2 *CarrierLoyaltyTableV2 `protobuf:"bytes,2,opt,name=v2,proto3" json:"v2,omitempty"`
}

func (x *Catalog_CarrierLoyaltyTableTypes) Reset() {
	*x = Catalog_CarrierLoyaltyTableTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_CarrierLoyaltyTableTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_CarrierLoyaltyTableTypes) ProtoMessage() {}

func (x *Catalog_CarrierLoyaltyTableTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_CarrierLoyaltyTableTypes.ProtoReflect.Descriptor instead.
func (*Catalog_CarrierLoyaltyTableTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 4}
}

func (x *Catalog_CarrierLoyaltyTableTypes) GetV1() *CarrierLoyaltyTableV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

func (x *Catalog_CarrierLoyaltyTableTypes) GetV2() *CarrierLoyaltyTableV2 {
	if x != nil {
		return x.V2
	}
	return nil
}

type Catalog_CarrierLoyaltyInsightTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *CarrierLoyaltyInsightV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
	V2 *CarrierLoyaltyInsightV2 `protobuf:"bytes,2,opt,name=v2,proto3" json:"v2,omitempty"`
}

func (x *Catalog_CarrierLoyaltyInsightTypes) Reset() {
	*x = Catalog_CarrierLoyaltyInsightTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_CarrierLoyaltyInsightTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_CarrierLoyaltyInsightTypes) ProtoMessage() {}

func (x *Catalog_CarrierLoyaltyInsightTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_CarrierLoyaltyInsightTypes.ProtoReflect.Descriptor instead.
func (*Catalog_CarrierLoyaltyInsightTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 5}
}

func (x *Catalog_CarrierLoyaltyInsightTypes) GetV1() *CarrierLoyaltyInsightV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

func (x *Catalog_CarrierLoyaltyInsightTypes) GetV2() *CarrierLoyaltyInsightV2 {
	if x != nil {
		return x.V2
	}
	return nil
}

type Catalog_BucketRadiusOfOperationsTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *BucketRadiusOfOperationsV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
}

func (x *Catalog_BucketRadiusOfOperationsTypes) Reset() {
	*x = Catalog_BucketRadiusOfOperationsTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_BucketRadiusOfOperationsTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_BucketRadiusOfOperationsTypes) ProtoMessage() {}

func (x *Catalog_BucketRadiusOfOperationsTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_BucketRadiusOfOperationsTypes.ProtoReflect.Descriptor instead.
func (*Catalog_BucketRadiusOfOperationsTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 6}
}

func (x *Catalog_BucketRadiusOfOperationsTypes) GetV1() *BucketRadiusOfOperationsV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

type Catalog_DailyVinMileageTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *DailyVinMileageV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
}

func (x *Catalog_DailyVinMileageTypes) Reset() {
	*x = Catalog_DailyVinMileageTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_DailyVinMileageTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_DailyVinMileageTypes) ProtoMessage() {}

func (x *Catalog_DailyVinMileageTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_DailyVinMileageTypes.ProtoReflect.Descriptor instead.
func (*Catalog_DailyVinMileageTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 7}
}

func (x *Catalog_DailyVinMileageTypes) GetV1() *DailyVinMileageV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

type Catalog_FleetRunRateTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *FleetRunRateV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
}

func (x *Catalog_FleetRunRateTypes) Reset() {
	*x = Catalog_FleetRunRateTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_FleetRunRateTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_FleetRunRateTypes) ProtoMessage() {}

func (x *Catalog_FleetRunRateTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_FleetRunRateTypes.ProtoReflect.Descriptor instead.
func (*Catalog_FleetRunRateTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 8}
}

func (x *Catalog_FleetRunRateTypes) GetV1() *FleetRunRateV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

type Catalog_VinTelematicsSummaryTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *VinTelematicsSummaryV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
}

func (x *Catalog_VinTelematicsSummaryTypes) Reset() {
	*x = Catalog_VinTelematicsSummaryTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_VinTelematicsSummaryTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_VinTelematicsSummaryTypes) ProtoMessage() {}

func (x *Catalog_VinTelematicsSummaryTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_VinTelematicsSummaryTypes.ProtoReflect.Descriptor instead.
func (*Catalog_VinTelematicsSummaryTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 9}
}

func (x *Catalog_VinTelematicsSummaryTypes) GetV1() *VinTelematicsSummaryV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

type Catalog_MileageWeightedCrashFreqTRSTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *MileageWeightedCrashFreqTRSV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
}

func (x *Catalog_MileageWeightedCrashFreqTRSTypes) Reset() {
	*x = Catalog_MileageWeightedCrashFreqTRSTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_MileageWeightedCrashFreqTRSTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_MileageWeightedCrashFreqTRSTypes) ProtoMessage() {}

func (x *Catalog_MileageWeightedCrashFreqTRSTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_MileageWeightedCrashFreqTRSTypes.ProtoReflect.Descriptor instead.
func (*Catalog_MileageWeightedCrashFreqTRSTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 10}
}

func (x *Catalog_MileageWeightedCrashFreqTRSTypes) GetV1() *MileageWeightedCrashFreqTRSV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

type Catalog_StateZoneMileageTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *StateZoneMileageV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
}

func (x *Catalog_StateZoneMileageTypes) Reset() {
	*x = Catalog_StateZoneMileageTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_StateZoneMileageTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_StateZoneMileageTypes) ProtoMessage() {}

func (x *Catalog_StateZoneMileageTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_StateZoneMileageTypes.ProtoReflect.Descriptor instead.
func (*Catalog_StateZoneMileageTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 11}
}

func (x *Catalog_StateZoneMileageTypes) GetV1() *StateZoneMileageV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

type Catalog_NirvanaRiskScoreTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *NirvanaRiskScoreV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
	V2 *NirvanaRiskScoreV2 `protobuf:"bytes,2,opt,name=v2,proto3" json:"v2,omitempty"`
	V3 *NirvanaRiskScoreV3 `protobuf:"bytes,3,opt,name=v3,proto3" json:"v3,omitempty"`
	V4 *NirvanaRiskScoreV4 `protobuf:"bytes,4,opt,name=v4,proto3" json:"v4,omitempty"`
	V5 *NirvanaRiskScoreV5 `protobuf:"bytes,5,opt,name=v5,proto3" json:"v5,omitempty"`
}

func (x *Catalog_NirvanaRiskScoreTypes) Reset() {
	*x = Catalog_NirvanaRiskScoreTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_NirvanaRiskScoreTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_NirvanaRiskScoreTypes) ProtoMessage() {}

func (x *Catalog_NirvanaRiskScoreTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_NirvanaRiskScoreTypes.ProtoReflect.Descriptor instead.
func (*Catalog_NirvanaRiskScoreTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 12}
}

func (x *Catalog_NirvanaRiskScoreTypes) GetV1() *NirvanaRiskScoreV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

func (x *Catalog_NirvanaRiskScoreTypes) GetV2() *NirvanaRiskScoreV2 {
	if x != nil {
		return x.V2
	}
	return nil
}

func (x *Catalog_NirvanaRiskScoreTypes) GetV3() *NirvanaRiskScoreV3 {
	if x != nil {
		return x.V3
	}
	return nil
}

func (x *Catalog_NirvanaRiskScoreTypes) GetV4() *NirvanaRiskScoreV4 {
	if x != nil {
		return x.V4
	}
	return nil
}

func (x *Catalog_NirvanaRiskScoreTypes) GetV5() *NirvanaRiskScoreV5 {
	if x != nil {
		return x.V5
	}
	return nil
}

type Catalog_NirvanaVinRiskScoreTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *NirvanaVinRiskScoreV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
}

func (x *Catalog_NirvanaVinRiskScoreTypes) Reset() {
	*x = Catalog_NirvanaVinRiskScoreTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_NirvanaVinRiskScoreTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_NirvanaVinRiskScoreTypes) ProtoMessage() {}

func (x *Catalog_NirvanaVinRiskScoreTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_NirvanaVinRiskScoreTypes.ProtoReflect.Descriptor instead.
func (*Catalog_NirvanaVinRiskScoreTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 13}
}

func (x *Catalog_NirvanaVinRiskScoreTypes) GetV1() *NirvanaVinRiskScoreV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

type Catalog_VinHaulClusterTagTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *VinHaulClusterTagV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
}

func (x *Catalog_VinHaulClusterTagTypes) Reset() {
	*x = Catalog_VinHaulClusterTagTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_VinHaulClusterTagTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_VinHaulClusterTagTypes) ProtoMessage() {}

func (x *Catalog_VinHaulClusterTagTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_VinHaulClusterTagTypes.ProtoReflect.Descriptor instead.
func (*Catalog_VinHaulClusterTagTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 14}
}

func (x *Catalog_VinHaulClusterTagTypes) GetV1() *VinHaulClusterTagV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

type Catalog_NirvanaVinRiskScoreClusterTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	V1 *NirvanaVinRiskScoreClusterV1 `protobuf:"bytes,1,opt,name=v1,proto3" json:"v1,omitempty"`
	V2 *NirvanaVinRiskScoreClusterV2 `protobuf:"bytes,2,opt,name=v2,proto3" json:"v2,omitempty"`
}

func (x *Catalog_NirvanaVinRiskScoreClusterTypes) Reset() {
	*x = Catalog_NirvanaVinRiskScoreClusterTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Catalog_NirvanaVinRiskScoreClusterTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Catalog_NirvanaVinRiskScoreClusterTypes) ProtoMessage() {}

func (x *Catalog_NirvanaVinRiskScoreClusterTypes) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Catalog_NirvanaVinRiskScoreClusterTypes.ProtoReflect.Descriptor instead.
func (*Catalog_NirvanaVinRiskScoreClusterTypes) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{0, 15}
}

func (x *Catalog_NirvanaVinRiskScoreClusterTypes) GetV1() *NirvanaVinRiskScoreClusterV1 {
	if x != nil {
		return x.V1
	}
	return nil
}

func (x *Catalog_NirvanaVinRiskScoreClusterTypes) GetV2() *NirvanaVinRiskScoreClusterV2 {
	if x != nil {
		return x.V2
	}
	return nil
}

type HazardStatesV1_HazardStateRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State         string  `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	DistanceMiles float32 `protobuf:"fixed32,2,opt,name=distance_miles,json=distanceMiles,proto3" json:"distance_miles,omitempty"`
	DistancePct   float32 `protobuf:"fixed32,3,opt,name=distance_pct,json=distancePct,proto3" json:"distance_pct,omitempty"`
}

func (x *HazardStatesV1_HazardStateRow) Reset() {
	*x = HazardStatesV1_HazardStateRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HazardStatesV1_HazardStateRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HazardStatesV1_HazardStateRow) ProtoMessage() {}

func (x *HazardStatesV1_HazardStateRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HazardStatesV1_HazardStateRow.ProtoReflect.Descriptor instead.
func (*HazardStatesV1_HazardStateRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{6, 0}
}

func (x *HazardStatesV1_HazardStateRow) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *HazardStatesV1_HazardStateRow) GetDistanceMiles() float32 {
	if x != nil {
		return x.DistanceMiles
	}
	return 0
}

func (x *HazardStatesV1_HazardStateRow) GetDistancePct() float32 {
	if x != nil {
		return x.DistancePct
	}
	return 0
}

type CarrierLoyaltyTableV1_CLDataframeRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DurationType          CarrierLoyaltyTableV1_DurationType `protobuf:"varint,1,opt,name=duration_type,json=durationType,proto3,enum=feature_store.CarrierLoyaltyTableV1_DurationType" json:"duration_type,omitempty"`
	CarrierCount          int64                              `protobuf:"varint,2,opt,name=carrier_count,json=carrierCount,proto3" json:"carrier_count,omitempty"`
	GapCount              int64                              `protobuf:"varint,3,opt,name=gap_count,json=gapCount,proto3" json:"gap_count,omitempty"`
	GapDays               int64                              `protobuf:"varint,4,opt,name=gap_days,json=gapDays,proto3" json:"gap_days,omitempty"`
	CarrierSwitchFreq     float32                            `protobuf:"fixed32,5,opt,name=carrier_switch_freq,json=carrierSwitchFreq,proto3" json:"carrier_switch_freq,omitempty"`
	CarrierSwitchCategory string                             `protobuf:"bytes,6,opt,name=carrier_switch_category,json=carrierSwitchCategory,proto3" json:"carrier_switch_category,omitempty"`
}

func (x *CarrierLoyaltyTableV1_CLDataframeRow) Reset() {
	*x = CarrierLoyaltyTableV1_CLDataframeRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarrierLoyaltyTableV1_CLDataframeRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarrierLoyaltyTableV1_CLDataframeRow) ProtoMessage() {}

func (x *CarrierLoyaltyTableV1_CLDataframeRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarrierLoyaltyTableV1_CLDataframeRow.ProtoReflect.Descriptor instead.
func (*CarrierLoyaltyTableV1_CLDataframeRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{7, 0}
}

func (x *CarrierLoyaltyTableV1_CLDataframeRow) GetDurationType() CarrierLoyaltyTableV1_DurationType {
	if x != nil {
		return x.DurationType
	}
	return CarrierLoyaltyTableV1_Recent
}

func (x *CarrierLoyaltyTableV1_CLDataframeRow) GetCarrierCount() int64 {
	if x != nil {
		return x.CarrierCount
	}
	return 0
}

func (x *CarrierLoyaltyTableV1_CLDataframeRow) GetGapCount() int64 {
	if x != nil {
		return x.GapCount
	}
	return 0
}

func (x *CarrierLoyaltyTableV1_CLDataframeRow) GetGapDays() int64 {
	if x != nil {
		return x.GapDays
	}
	return 0
}

func (x *CarrierLoyaltyTableV1_CLDataframeRow) GetCarrierSwitchFreq() float32 {
	if x != nil {
		return x.CarrierSwitchFreq
	}
	return 0
}

func (x *CarrierLoyaltyTableV1_CLDataframeRow) GetCarrierSwitchCategory() string {
	if x != nil {
		return x.CarrierSwitchCategory
	}
	return ""
}

type CarrierLoyaltyTableV2_CLDataframeRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DurationType           string  `protobuf:"bytes,1,opt,name=duration_type,json=durationType,proto3" json:"duration_type,omitempty"`
	CarrierCount           int64   `protobuf:"varint,2,opt,name=carrier_count,json=carrierCount,proto3" json:"carrier_count,omitempty"`
	MinEffectiveDateFrom   string  `protobuf:"bytes,3,opt,name=min_effective_date_from,json=minEffectiveDateFrom,proto3" json:"min_effective_date_from,omitempty"`
	MaxEffectiveDateTo     string  `protobuf:"bytes,4,opt,name=max_effective_date_to,json=maxEffectiveDateTo,proto3" json:"max_effective_date_to,omitempty"`
	TotalGapDays           int64   `protobuf:"varint,5,opt,name=total_gap_days,json=totalGapDays,proto3" json:"total_gap_days,omitempty"`
	EffectiveDurationYears float32 `protobuf:"fixed32,6,opt,name=effective_duration_years,json=effectiveDurationYears,proto3" json:"effective_duration_years,omitempty"`
	YearPerCarrier         float32 `protobuf:"fixed32,7,opt,name=year_per_carrier,json=yearPerCarrier,proto3" json:"year_per_carrier,omitempty"`
	CarrierLoyaltyCategory string  `protobuf:"bytes,8,opt,name=carrier_loyalty_category,json=carrierLoyaltyCategory,proto3" json:"carrier_loyalty_category,omitempty"`
	CarrierSwitches        int64   `protobuf:"varint,9,opt,name=carrier_switches,json=carrierSwitches,proto3" json:"carrier_switches,omitempty"`
}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) Reset() {
	*x = CarrierLoyaltyTableV2_CLDataframeRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarrierLoyaltyTableV2_CLDataframeRow) ProtoMessage() {}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarrierLoyaltyTableV2_CLDataframeRow.ProtoReflect.Descriptor instead.
func (*CarrierLoyaltyTableV2_CLDataframeRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{8, 0}
}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) GetDurationType() string {
	if x != nil {
		return x.DurationType
	}
	return ""
}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) GetCarrierCount() int64 {
	if x != nil {
		return x.CarrierCount
	}
	return 0
}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) GetMinEffectiveDateFrom() string {
	if x != nil {
		return x.MinEffectiveDateFrom
	}
	return ""
}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) GetMaxEffectiveDateTo() string {
	if x != nil {
		return x.MaxEffectiveDateTo
	}
	return ""
}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) GetTotalGapDays() int64 {
	if x != nil {
		return x.TotalGapDays
	}
	return 0
}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) GetEffectiveDurationYears() float32 {
	if x != nil {
		return x.EffectiveDurationYears
	}
	return 0
}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) GetYearPerCarrier() float32 {
	if x != nil {
		return x.YearPerCarrier
	}
	return 0
}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) GetCarrierLoyaltyCategory() string {
	if x != nil {
		return x.CarrierLoyaltyCategory
	}
	return ""
}

func (x *CarrierLoyaltyTableV2_CLDataframeRow) GetCarrierSwitches() int64 {
	if x != nil {
		return x.CarrierSwitches
	}
	return 0
}

type FleetRunRateV1_WRDataframeRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsoWeekStart      string   `protobuf:"bytes,1,opt,name=iso_week_start,json=isoWeekStart,proto3" json:"iso_week_start,omitempty"`
	VinCount          int64    `protobuf:"varint,2,opt,name=vin_count,json=vinCount,proto3" json:"vin_count,omitempty"`
	ActiveVinCount    *int64   `protobuf:"varint,3,opt,name=active_vin_count,json=activeVinCount,proto3,oneof" json:"active_vin_count,omitempty"`
	Mileage           *float32 `protobuf:"fixed32,4,opt,name=mileage,proto3,oneof" json:"mileage,omitempty"`
	YearlyMileage     *float32 `protobuf:"fixed32,5,opt,name=yearly_mileage,json=yearlyMileage,proto3,oneof" json:"yearly_mileage,omitempty"`
	HalfYearlyMileage *float32 `protobuf:"fixed32,6,opt,name=half_yearly_mileage,json=halfYearlyMileage,proto3,oneof" json:"half_yearly_mileage,omitempty"`
	QuarterlyMileage  *float32 `protobuf:"fixed32,7,opt,name=quarterly_mileage,json=quarterlyMileage,proto3,oneof" json:"quarterly_mileage,omitempty"`
	MonthlyMileage    *float32 `protobuf:"fixed32,8,opt,name=monthly_mileage,json=monthlyMileage,proto3,oneof" json:"monthly_mileage,omitempty"`
}

func (x *FleetRunRateV1_WRDataframeRow) Reset() {
	*x = FleetRunRateV1_WRDataframeRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetRunRateV1_WRDataframeRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetRunRateV1_WRDataframeRow) ProtoMessage() {}

func (x *FleetRunRateV1_WRDataframeRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetRunRateV1_WRDataframeRow.ProtoReflect.Descriptor instead.
func (*FleetRunRateV1_WRDataframeRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{13, 0}
}

func (x *FleetRunRateV1_WRDataframeRow) GetIsoWeekStart() string {
	if x != nil {
		return x.IsoWeekStart
	}
	return ""
}

func (x *FleetRunRateV1_WRDataframeRow) GetVinCount() int64 {
	if x != nil {
		return x.VinCount
	}
	return 0
}

func (x *FleetRunRateV1_WRDataframeRow) GetActiveVinCount() int64 {
	if x != nil && x.ActiveVinCount != nil {
		return *x.ActiveVinCount
	}
	return 0
}

func (x *FleetRunRateV1_WRDataframeRow) GetMileage() float32 {
	if x != nil && x.Mileage != nil {
		return *x.Mileage
	}
	return 0
}

func (x *FleetRunRateV1_WRDataframeRow) GetYearlyMileage() float32 {
	if x != nil && x.YearlyMileage != nil {
		return *x.YearlyMileage
	}
	return 0
}

func (x *FleetRunRateV1_WRDataframeRow) GetHalfYearlyMileage() float32 {
	if x != nil && x.HalfYearlyMileage != nil {
		return *x.HalfYearlyMileage
	}
	return 0
}

func (x *FleetRunRateV1_WRDataframeRow) GetQuarterlyMileage() float32 {
	if x != nil && x.QuarterlyMileage != nil {
		return *x.QuarterlyMileage
	}
	return 0
}

func (x *FleetRunRateV1_WRDataframeRow) GetMonthlyMileage() float32 {
	if x != nil && x.MonthlyMileage != nil {
		return *x.MonthlyMileage
	}
	return 0
}

type VinTelematicsSummaryV1_VinSummaryTableRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin                  string  `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	DataStart            string  `protobuf:"bytes,2,opt,name=data_start,json=dataStart,proto3" json:"data_start,omitempty"`
	DataEnd              string  `protobuf:"bytes,3,opt,name=data_end,json=dataEnd,proto3" json:"data_end,omitempty"`
	TotalMiles           float32 `protobuf:"fixed32,4,opt,name=total_miles,json=totalMiles,proto3" json:"total_miles,omitempty"`
	EstimatedAnnualMiles float32 `protobuf:"fixed32,5,opt,name=estimated_annual_miles,json=estimatedAnnualMiles,proto3" json:"estimated_annual_miles,omitempty"`
	RadiusOfOperation    float32 `protobuf:"fixed32,6,opt,name=radius_of_operation,json=radiusOfOperation,proto3" json:"radius_of_operation,omitempty"`
	AverageDailyMiles    float32 `protobuf:"fixed32,7,opt,name=average_daily_miles,json=averageDailyMiles,proto3" json:"average_daily_miles,omitempty"`
}

func (x *VinTelematicsSummaryV1_VinSummaryTableRow) Reset() {
	*x = VinTelematicsSummaryV1_VinSummaryTableRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VinTelematicsSummaryV1_VinSummaryTableRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VinTelematicsSummaryV1_VinSummaryTableRow) ProtoMessage() {}

func (x *VinTelematicsSummaryV1_VinSummaryTableRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VinTelematicsSummaryV1_VinSummaryTableRow.ProtoReflect.Descriptor instead.
func (*VinTelematicsSummaryV1_VinSummaryTableRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{14, 0}
}

func (x *VinTelematicsSummaryV1_VinSummaryTableRow) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *VinTelematicsSummaryV1_VinSummaryTableRow) GetDataStart() string {
	if x != nil {
		return x.DataStart
	}
	return ""
}

func (x *VinTelematicsSummaryV1_VinSummaryTableRow) GetDataEnd() string {
	if x != nil {
		return x.DataEnd
	}
	return ""
}

func (x *VinTelematicsSummaryV1_VinSummaryTableRow) GetTotalMiles() float32 {
	if x != nil {
		return x.TotalMiles
	}
	return 0
}

func (x *VinTelematicsSummaryV1_VinSummaryTableRow) GetEstimatedAnnualMiles() float32 {
	if x != nil {
		return x.EstimatedAnnualMiles
	}
	return 0
}

func (x *VinTelematicsSummaryV1_VinSummaryTableRow) GetRadiusOfOperation() float32 {
	if x != nil {
		return x.RadiusOfOperation
	}
	return 0
}

func (x *VinTelematicsSummaryV1_VinSummaryTableRow) GetAverageDailyMiles() float32 {
	if x != nil {
		return x.AverageDailyMiles
	}
	return 0
}

type MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartDate string   `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate   string   `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Frequency *float32 `protobuf:"fixed32,3,opt,name=frequency,proto3,oneof" json:"frequency,omitempty"`
	Score     *float32 `protobuf:"fixed32,4,opt,name=score,proto3,oneof" json:"score,omitempty"`
	VinCount  *int32   `protobuf:"varint,5,opt,name=vin_count,json=vinCount,proto3,oneof" json:"vin_count,omitempty"`
}

func (x *MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow) Reset() {
	*x = MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow) ProtoMessage() {}

func (x *MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow.ProtoReflect.Descriptor instead.
func (*MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{15, 0}
}

func (x *MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow) GetFrequency() float32 {
	if x != nil && x.Frequency != nil {
		return *x.Frequency
	}
	return 0
}

func (x *MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow) GetScore() float32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow) GetVinCount() int32 {
	if x != nil && x.VinCount != nil {
		return *x.VinCount
	}
	return 0
}

type StateZoneMileageV1_StateZoneMileageRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Zone               int32    `protobuf:"varint,1,opt,name=zone,proto3" json:"zone,omitempty"`
	Distance           *float32 `protobuf:"fixed32,2,opt,name=distance,proto3,oneof" json:"distance,omitempty"`
	PercentageDistance *float32 `protobuf:"fixed32,3,opt,name=percentage_distance,json=percentageDistance,proto3,oneof" json:"percentage_distance,omitempty"`
	Duration           *float32 `protobuf:"fixed32,4,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	PercentageDuration *float32 `protobuf:"fixed32,5,opt,name=percentage_duration,json=percentageDuration,proto3,oneof" json:"percentage_duration,omitempty"`
}

func (x *StateZoneMileageV1_StateZoneMileageRow) Reset() {
	*x = StateZoneMileageV1_StateZoneMileageRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StateZoneMileageV1_StateZoneMileageRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateZoneMileageV1_StateZoneMileageRow) ProtoMessage() {}

func (x *StateZoneMileageV1_StateZoneMileageRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateZoneMileageV1_StateZoneMileageRow.ProtoReflect.Descriptor instead.
func (*StateZoneMileageV1_StateZoneMileageRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{16, 0}
}

func (x *StateZoneMileageV1_StateZoneMileageRow) GetZone() int32 {
	if x != nil {
		return x.Zone
	}
	return 0
}

func (x *StateZoneMileageV1_StateZoneMileageRow) GetDistance() float32 {
	if x != nil && x.Distance != nil {
		return *x.Distance
	}
	return 0
}

func (x *StateZoneMileageV1_StateZoneMileageRow) GetPercentageDistance() float32 {
	if x != nil && x.PercentageDistance != nil {
		return *x.PercentageDistance
	}
	return 0
}

func (x *StateZoneMileageV1_StateZoneMileageRow) GetDuration() float32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *StateZoneMileageV1_StateZoneMileageRow) GetPercentageDuration() float32 {
	if x != nil && x.PercentageDuration != nil {
		return *x.PercentageDuration
	}
	return 0
}

type NirvanaRiskScoreV1_RiskScoreTrendRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WindowStart string   `protobuf:"bytes,1,opt,name=windowStart,proto3" json:"windowStart,omitempty"`
	WindowEnd   string   `protobuf:"bytes,2,opt,name=windowEnd,proto3" json:"windowEnd,omitempty"`
	WindowType  string   `protobuf:"bytes,3,opt,name=windowType,proto3" json:"windowType,omitempty"`
	Score       *float32 `protobuf:"fixed32,4,opt,name=score,proto3,oneof" json:"score,omitempty"`
	VinCount    *float32 `protobuf:"fixed32,5,opt,name=vinCount,proto3,oneof" json:"vinCount,omitempty"`
	Timestamp   string   `protobuf:"bytes,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *NirvanaRiskScoreV1_RiskScoreTrendRow) Reset() {
	*x = NirvanaRiskScoreV1_RiskScoreTrendRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV1_RiskScoreTrendRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV1_RiskScoreTrendRow) ProtoMessage() {}

func (x *NirvanaRiskScoreV1_RiskScoreTrendRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV1_RiskScoreTrendRow.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV1_RiskScoreTrendRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{17, 0}
}

func (x *NirvanaRiskScoreV1_RiskScoreTrendRow) GetWindowStart() string {
	if x != nil {
		return x.WindowStart
	}
	return ""
}

func (x *NirvanaRiskScoreV1_RiskScoreTrendRow) GetWindowEnd() string {
	if x != nil {
		return x.WindowEnd
	}
	return ""
}

func (x *NirvanaRiskScoreV1_RiskScoreTrendRow) GetWindowType() string {
	if x != nil {
		return x.WindowType
	}
	return ""
}

func (x *NirvanaRiskScoreV1_RiskScoreTrendRow) GetScore() float32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *NirvanaRiskScoreV1_RiskScoreTrendRow) GetVinCount() float32 {
	if x != nil && x.VinCount != nil {
		return *x.VinCount
	}
	return 0
}

func (x *NirvanaRiskScoreV1_RiskScoreTrendRow) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

type NirvanaRiskScoreV1_UWRubricRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreStart int64    `protobuf:"varint,1,opt,name=scoreStart,proto3" json:"scoreStart,omitempty"`
	ScoreEnd   int64    `protobuf:"varint,2,opt,name=scoreEnd,proto3" json:"scoreEnd,omitempty"`
	Decile     float32  `protobuf:"fixed32,3,opt,name=decile,proto3" json:"decile,omitempty"`
	Discount   *float32 `protobuf:"fixed32,4,opt,name=discount,proto3,oneof" json:"discount,omitempty"`
	Market     bool     `protobuf:"varint,5,opt,name=market,proto3" json:"market,omitempty"`
}

func (x *NirvanaRiskScoreV1_UWRubricRow) Reset() {
	*x = NirvanaRiskScoreV1_UWRubricRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV1_UWRubricRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV1_UWRubricRow) ProtoMessage() {}

func (x *NirvanaRiskScoreV1_UWRubricRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV1_UWRubricRow.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV1_UWRubricRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{17, 1}
}

func (x *NirvanaRiskScoreV1_UWRubricRow) GetScoreStart() int64 {
	if x != nil {
		return x.ScoreStart
	}
	return 0
}

func (x *NirvanaRiskScoreV1_UWRubricRow) GetScoreEnd() int64 {
	if x != nil {
		return x.ScoreEnd
	}
	return 0
}

func (x *NirvanaRiskScoreV1_UWRubricRow) GetDecile() float32 {
	if x != nil {
		return x.Decile
	}
	return 0
}

func (x *NirvanaRiskScoreV1_UWRubricRow) GetDiscount() float32 {
	if x != nil && x.Discount != nil {
		return *x.Discount
	}
	return 0
}

func (x *NirvanaRiskScoreV1_UWRubricRow) GetMarket() bool {
	if x != nil {
		return x.Market
	}
	return false
}

type NirvanaRiskScoreV2_RiskScoreTrendRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WindowStart string   `protobuf:"bytes,1,opt,name=windowStart,proto3" json:"windowStart,omitempty"`
	WindowEnd   string   `protobuf:"bytes,2,opt,name=windowEnd,proto3" json:"windowEnd,omitempty"`
	WindowType  string   `protobuf:"bytes,3,opt,name=windowType,proto3" json:"windowType,omitempty"`
	Score       *float32 `protobuf:"fixed32,4,opt,name=score,proto3,oneof" json:"score,omitempty"`
	VinCount    *float32 `protobuf:"fixed32,5,opt,name=vinCount,proto3,oneof" json:"vinCount,omitempty"`
	Timestamp   string   `protobuf:"bytes,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *NirvanaRiskScoreV2_RiskScoreTrendRow) Reset() {
	*x = NirvanaRiskScoreV2_RiskScoreTrendRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV2_RiskScoreTrendRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV2_RiskScoreTrendRow) ProtoMessage() {}

func (x *NirvanaRiskScoreV2_RiskScoreTrendRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV2_RiskScoreTrendRow.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV2_RiskScoreTrendRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{18, 0}
}

func (x *NirvanaRiskScoreV2_RiskScoreTrendRow) GetWindowStart() string {
	if x != nil {
		return x.WindowStart
	}
	return ""
}

func (x *NirvanaRiskScoreV2_RiskScoreTrendRow) GetWindowEnd() string {
	if x != nil {
		return x.WindowEnd
	}
	return ""
}

func (x *NirvanaRiskScoreV2_RiskScoreTrendRow) GetWindowType() string {
	if x != nil {
		return x.WindowType
	}
	return ""
}

func (x *NirvanaRiskScoreV2_RiskScoreTrendRow) GetScore() float32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *NirvanaRiskScoreV2_RiskScoreTrendRow) GetVinCount() float32 {
	if x != nil && x.VinCount != nil {
		return *x.VinCount
	}
	return 0
}

func (x *NirvanaRiskScoreV2_RiskScoreTrendRow) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

type NirvanaRiskScoreV2_UWRubricRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreStart int64    `protobuf:"varint,1,opt,name=scoreStart,proto3" json:"scoreStart,omitempty"`
	ScoreEnd   int64    `protobuf:"varint,2,opt,name=scoreEnd,proto3" json:"scoreEnd,omitempty"`
	Decile     float32  `protobuf:"fixed32,3,opt,name=decile,proto3" json:"decile,omitempty"`
	Discount   *float32 `protobuf:"fixed32,4,opt,name=discount,proto3,oneof" json:"discount,omitempty"`
	IsMarket   bool     `protobuf:"varint,5,opt,name=isMarket,proto3" json:"isMarket,omitempty"`
}

func (x *NirvanaRiskScoreV2_UWRubricRow) Reset() {
	*x = NirvanaRiskScoreV2_UWRubricRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV2_UWRubricRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV2_UWRubricRow) ProtoMessage() {}

func (x *NirvanaRiskScoreV2_UWRubricRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV2_UWRubricRow.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV2_UWRubricRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{18, 1}
}

func (x *NirvanaRiskScoreV2_UWRubricRow) GetScoreStart() int64 {
	if x != nil {
		return x.ScoreStart
	}
	return 0
}

func (x *NirvanaRiskScoreV2_UWRubricRow) GetScoreEnd() int64 {
	if x != nil {
		return x.ScoreEnd
	}
	return 0
}

func (x *NirvanaRiskScoreV2_UWRubricRow) GetDecile() float32 {
	if x != nil {
		return x.Decile
	}
	return 0
}

func (x *NirvanaRiskScoreV2_UWRubricRow) GetDiscount() float32 {
	if x != nil && x.Discount != nil {
		return *x.Discount
	}
	return 0
}

func (x *NirvanaRiskScoreV2_UWRubricRow) GetIsMarket() bool {
	if x != nil {
		return x.IsMarket
	}
	return false
}

type NirvanaRiskScoreV3_RiskScoreTrendRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WindowStart           string   `protobuf:"bytes,1,opt,name=windowStart,proto3" json:"windowStart,omitempty"`
	WindowEnd             string   `protobuf:"bytes,2,opt,name=windowEnd,proto3" json:"windowEnd,omitempty"`
	WindowType            string   `protobuf:"bytes,3,opt,name=windowType,proto3" json:"windowType,omitempty"`
	Score                 *float32 `protobuf:"fixed32,4,opt,name=score,proto3,oneof" json:"score,omitempty"`
	VinCount              *float32 `protobuf:"fixed32,5,opt,name=vinCount,proto3,oneof" json:"vinCount,omitempty"`
	Timestamp             string   `protobuf:"bytes,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	FracShortHaulDistance *float32 `protobuf:"fixed32,7,opt,name=fracShortHaulDistance,proto3,oneof" json:"fracShortHaulDistance,omitempty"`
	IsConfidentScore      *bool    `protobuf:"varint,8,opt,name=isConfidentScore,proto3,oneof" json:"isConfidentScore,omitempty"`
	LowConfidenceReason   *string  `protobuf:"bytes,9,opt,name=lowConfidenceReason,proto3,oneof" json:"lowConfidenceReason,omitempty"`
	ExposeScoreTrend      *bool    `protobuf:"varint,10,opt,name=exposeScoreTrend,proto3,oneof" json:"exposeScoreTrend,omitempty"`
	HideScoreReason       *string  `protobuf:"bytes,11,opt,name=hideScoreReason,proto3,oneof" json:"hideScoreReason,omitempty"`
	ScoreFactors          *string  `protobuf:"bytes,12,opt,name=scoreFactors,proto3,oneof" json:"scoreFactors,omitempty"`
	ScoreFactorValues     *string  `protobuf:"bytes,13,opt,name=scoreFactorValues,proto3,oneof" json:"scoreFactorValues,omitempty"`
	ScoreFactorBenchmark  *string  `protobuf:"bytes,14,opt,name=scoreFactorBenchmark,proto3,oneof" json:"scoreFactorBenchmark,omitempty"`
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) Reset() {
	*x = NirvanaRiskScoreV3_RiskScoreTrendRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV3_RiskScoreTrendRow) ProtoMessage() {}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV3_RiskScoreTrendRow.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV3_RiskScoreTrendRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{19, 0}
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetWindowStart() string {
	if x != nil {
		return x.WindowStart
	}
	return ""
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetWindowEnd() string {
	if x != nil {
		return x.WindowEnd
	}
	return ""
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetWindowType() string {
	if x != nil {
		return x.WindowType
	}
	return ""
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetScore() float32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetVinCount() float32 {
	if x != nil && x.VinCount != nil {
		return *x.VinCount
	}
	return 0
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetFracShortHaulDistance() float32 {
	if x != nil && x.FracShortHaulDistance != nil {
		return *x.FracShortHaulDistance
	}
	return 0
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetIsConfidentScore() bool {
	if x != nil && x.IsConfidentScore != nil {
		return *x.IsConfidentScore
	}
	return false
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetLowConfidenceReason() string {
	if x != nil && x.LowConfidenceReason != nil {
		return *x.LowConfidenceReason
	}
	return ""
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetExposeScoreTrend() bool {
	if x != nil && x.ExposeScoreTrend != nil {
		return *x.ExposeScoreTrend
	}
	return false
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetHideScoreReason() string {
	if x != nil && x.HideScoreReason != nil {
		return *x.HideScoreReason
	}
	return ""
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetScoreFactors() string {
	if x != nil && x.ScoreFactors != nil {
		return *x.ScoreFactors
	}
	return ""
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetScoreFactorValues() string {
	if x != nil && x.ScoreFactorValues != nil {
		return *x.ScoreFactorValues
	}
	return ""
}

func (x *NirvanaRiskScoreV3_RiskScoreTrendRow) GetScoreFactorBenchmark() string {
	if x != nil && x.ScoreFactorBenchmark != nil {
		return *x.ScoreFactorBenchmark
	}
	return ""
}

type NirvanaRiskScoreV3_UWRubricRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreStart     int64    `protobuf:"varint,1,opt,name=scoreStart,proto3" json:"scoreStart,omitempty"`
	ScoreEnd       int64    `protobuf:"varint,2,opt,name=scoreEnd,proto3" json:"scoreEnd,omitempty"`
	Decile         float32  `protobuf:"fixed32,3,opt,name=decile,proto3" json:"decile,omitempty"`
	Discount       *float32 `protobuf:"fixed32,4,opt,name=discount,proto3,oneof" json:"discount,omitempty"`
	IsMarket       bool     `protobuf:"varint,5,opt,name=isMarket,proto3" json:"isMarket,omitempty"`
	MarketCategory string   `protobuf:"bytes,6,opt,name=marketCategory,proto3" json:"marketCategory,omitempty"`
}

func (x *NirvanaRiskScoreV3_UWRubricRow) Reset() {
	*x = NirvanaRiskScoreV3_UWRubricRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV3_UWRubricRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV3_UWRubricRow) ProtoMessage() {}

func (x *NirvanaRiskScoreV3_UWRubricRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV3_UWRubricRow.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV3_UWRubricRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{19, 1}
}

func (x *NirvanaRiskScoreV3_UWRubricRow) GetScoreStart() int64 {
	if x != nil {
		return x.ScoreStart
	}
	return 0
}

func (x *NirvanaRiskScoreV3_UWRubricRow) GetScoreEnd() int64 {
	if x != nil {
		return x.ScoreEnd
	}
	return 0
}

func (x *NirvanaRiskScoreV3_UWRubricRow) GetDecile() float32 {
	if x != nil {
		return x.Decile
	}
	return 0
}

func (x *NirvanaRiskScoreV3_UWRubricRow) GetDiscount() float32 {
	if x != nil && x.Discount != nil {
		return *x.Discount
	}
	return 0
}

func (x *NirvanaRiskScoreV3_UWRubricRow) GetIsMarket() bool {
	if x != nil {
		return x.IsMarket
	}
	return false
}

func (x *NirvanaRiskScoreV3_UWRubricRow) GetMarketCategory() string {
	if x != nil {
		return x.MarketCategory
	}
	return ""
}

type NirvanaRiskScoreV4_RiskScoreTrendRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WindowStart           string   `protobuf:"bytes,1,opt,name=windowStart,proto3" json:"windowStart,omitempty"`
	WindowEnd             string   `protobuf:"bytes,2,opt,name=windowEnd,proto3" json:"windowEnd,omitempty"`
	WindowType            string   `protobuf:"bytes,3,opt,name=windowType,proto3" json:"windowType,omitempty"`
	Score                 *float32 `protobuf:"fixed32,4,opt,name=score,proto3,oneof" json:"score,omitempty"`
	VinCount              *float32 `protobuf:"fixed32,5,opt,name=vinCount,proto3,oneof" json:"vinCount,omitempty"`
	Timestamp             string   `protobuf:"bytes,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	FracShortHaulDistance *float32 `protobuf:"fixed32,7,opt,name=fracShortHaulDistance,proto3,oneof" json:"fracShortHaulDistance,omitempty"`
	IsConfidentScore      *bool    `protobuf:"varint,8,opt,name=isConfidentScore,proto3,oneof" json:"isConfidentScore,omitempty"`
	LowConfidenceReason   *string  `protobuf:"bytes,9,opt,name=lowConfidenceReason,proto3,oneof" json:"lowConfidenceReason,omitempty"`
	ExposeScoreTrend      *bool    `protobuf:"varint,10,opt,name=exposeScoreTrend,proto3,oneof" json:"exposeScoreTrend,omitempty"`
	HideScoreReason       *string  `protobuf:"bytes,11,opt,name=hideScoreReason,proto3,oneof" json:"hideScoreReason,omitempty"`
	ScoreFactors          *string  `protobuf:"bytes,12,opt,name=scoreFactors,proto3,oneof" json:"scoreFactors,omitempty"`
	ScoreFactorValues     *string  `protobuf:"bytes,13,opt,name=scoreFactorValues,proto3,oneof" json:"scoreFactorValues,omitempty"`
	ScoreFactorBenchmark  *string  `protobuf:"bytes,14,opt,name=scoreFactorBenchmark,proto3,oneof" json:"scoreFactorBenchmark,omitempty"`
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) Reset() {
	*x = NirvanaRiskScoreV4_RiskScoreTrendRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV4_RiskScoreTrendRow) ProtoMessage() {}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV4_RiskScoreTrendRow.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV4_RiskScoreTrendRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{20, 0}
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetWindowStart() string {
	if x != nil {
		return x.WindowStart
	}
	return ""
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetWindowEnd() string {
	if x != nil {
		return x.WindowEnd
	}
	return ""
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetWindowType() string {
	if x != nil {
		return x.WindowType
	}
	return ""
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetScore() float32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetVinCount() float32 {
	if x != nil && x.VinCount != nil {
		return *x.VinCount
	}
	return 0
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetFracShortHaulDistance() float32 {
	if x != nil && x.FracShortHaulDistance != nil {
		return *x.FracShortHaulDistance
	}
	return 0
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetIsConfidentScore() bool {
	if x != nil && x.IsConfidentScore != nil {
		return *x.IsConfidentScore
	}
	return false
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetLowConfidenceReason() string {
	if x != nil && x.LowConfidenceReason != nil {
		return *x.LowConfidenceReason
	}
	return ""
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetExposeScoreTrend() bool {
	if x != nil && x.ExposeScoreTrend != nil {
		return *x.ExposeScoreTrend
	}
	return false
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetHideScoreReason() string {
	if x != nil && x.HideScoreReason != nil {
		return *x.HideScoreReason
	}
	return ""
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetScoreFactors() string {
	if x != nil && x.ScoreFactors != nil {
		return *x.ScoreFactors
	}
	return ""
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetScoreFactorValues() string {
	if x != nil && x.ScoreFactorValues != nil {
		return *x.ScoreFactorValues
	}
	return ""
}

func (x *NirvanaRiskScoreV4_RiskScoreTrendRow) GetScoreFactorBenchmark() string {
	if x != nil && x.ScoreFactorBenchmark != nil {
		return *x.ScoreFactorBenchmark
	}
	return ""
}

type NirvanaRiskScoreV4_UWRubricRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreStart     int64    `protobuf:"varint,1,opt,name=scoreStart,proto3" json:"scoreStart,omitempty"`
	ScoreEnd       int64    `protobuf:"varint,2,opt,name=scoreEnd,proto3" json:"scoreEnd,omitempty"`
	Decile         float32  `protobuf:"fixed32,3,opt,name=decile,proto3" json:"decile,omitempty"`
	Discount       *float32 `protobuf:"fixed32,4,opt,name=discount,proto3,oneof" json:"discount,omitempty"`
	IsMarket       bool     `protobuf:"varint,5,opt,name=isMarket,proto3" json:"isMarket,omitempty"`
	MarketCategory string   `protobuf:"bytes,6,opt,name=marketCategory,proto3" json:"marketCategory,omitempty"`
}

func (x *NirvanaRiskScoreV4_UWRubricRow) Reset() {
	*x = NirvanaRiskScoreV4_UWRubricRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV4_UWRubricRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV4_UWRubricRow) ProtoMessage() {}

func (x *NirvanaRiskScoreV4_UWRubricRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV4_UWRubricRow.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV4_UWRubricRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{20, 1}
}

func (x *NirvanaRiskScoreV4_UWRubricRow) GetScoreStart() int64 {
	if x != nil {
		return x.ScoreStart
	}
	return 0
}

func (x *NirvanaRiskScoreV4_UWRubricRow) GetScoreEnd() int64 {
	if x != nil {
		return x.ScoreEnd
	}
	return 0
}

func (x *NirvanaRiskScoreV4_UWRubricRow) GetDecile() float32 {
	if x != nil {
		return x.Decile
	}
	return 0
}

func (x *NirvanaRiskScoreV4_UWRubricRow) GetDiscount() float32 {
	if x != nil && x.Discount != nil {
		return *x.Discount
	}
	return 0
}

func (x *NirvanaRiskScoreV4_UWRubricRow) GetIsMarket() bool {
	if x != nil {
		return x.IsMarket
	}
	return false
}

func (x *NirvanaRiskScoreV4_UWRubricRow) GetMarketCategory() string {
	if x != nil {
		return x.MarketCategory
	}
	return ""
}

type NirvanaRiskScoreV5_RiskScoreTrendRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WindowStart           string   `protobuf:"bytes,1,opt,name=windowStart,proto3" json:"windowStart,omitempty"`
	WindowEnd             string   `protobuf:"bytes,2,opt,name=windowEnd,proto3" json:"windowEnd,omitempty"`
	WindowType            string   `protobuf:"bytes,3,opt,name=windowType,proto3" json:"windowType,omitempty"`
	Score                 *float32 `protobuf:"fixed32,4,opt,name=score,proto3,oneof" json:"score,omitempty"`
	VinCount              *float32 `protobuf:"fixed32,5,opt,name=vinCount,proto3,oneof" json:"vinCount,omitempty"`
	Timestamp             string   `protobuf:"bytes,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	FracShortHaulDistance *float32 `protobuf:"fixed32,7,opt,name=fracShortHaulDistance,proto3,oneof" json:"fracShortHaulDistance,omitempty"`
	IsConfidentScore      *bool    `protobuf:"varint,8,opt,name=isConfidentScore,proto3,oneof" json:"isConfidentScore,omitempty"`
	LowConfidenceReason   *string  `protobuf:"bytes,9,opt,name=lowConfidenceReason,proto3,oneof" json:"lowConfidenceReason,omitempty"`
	ExposeScoreTrend      *bool    `protobuf:"varint,10,opt,name=exposeScoreTrend,proto3,oneof" json:"exposeScoreTrend,omitempty"`
	HideScoreReason       *string  `protobuf:"bytes,11,opt,name=hideScoreReason,proto3,oneof" json:"hideScoreReason,omitempty"`
	ScoreFactors          *string  `protobuf:"bytes,12,opt,name=scoreFactors,proto3,oneof" json:"scoreFactors,omitempty"`
	ScoreFactorValues     *string  `protobuf:"bytes,13,opt,name=scoreFactorValues,proto3,oneof" json:"scoreFactorValues,omitempty"`
	ScoreFactorBenchmark  *string  `protobuf:"bytes,14,opt,name=scoreFactorBenchmark,proto3,oneof" json:"scoreFactorBenchmark,omitempty"`
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) Reset() {
	*x = NirvanaRiskScoreV5_RiskScoreTrendRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV5_RiskScoreTrendRow) ProtoMessage() {}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV5_RiskScoreTrendRow.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV5_RiskScoreTrendRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{21, 0}
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetWindowStart() string {
	if x != nil {
		return x.WindowStart
	}
	return ""
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetWindowEnd() string {
	if x != nil {
		return x.WindowEnd
	}
	return ""
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetWindowType() string {
	if x != nil {
		return x.WindowType
	}
	return ""
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetScore() float32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetVinCount() float32 {
	if x != nil && x.VinCount != nil {
		return *x.VinCount
	}
	return 0
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetFracShortHaulDistance() float32 {
	if x != nil && x.FracShortHaulDistance != nil {
		return *x.FracShortHaulDistance
	}
	return 0
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetIsConfidentScore() bool {
	if x != nil && x.IsConfidentScore != nil {
		return *x.IsConfidentScore
	}
	return false
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetLowConfidenceReason() string {
	if x != nil && x.LowConfidenceReason != nil {
		return *x.LowConfidenceReason
	}
	return ""
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetExposeScoreTrend() bool {
	if x != nil && x.ExposeScoreTrend != nil {
		return *x.ExposeScoreTrend
	}
	return false
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetHideScoreReason() string {
	if x != nil && x.HideScoreReason != nil {
		return *x.HideScoreReason
	}
	return ""
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetScoreFactors() string {
	if x != nil && x.ScoreFactors != nil {
		return *x.ScoreFactors
	}
	return ""
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetScoreFactorValues() string {
	if x != nil && x.ScoreFactorValues != nil {
		return *x.ScoreFactorValues
	}
	return ""
}

func (x *NirvanaRiskScoreV5_RiskScoreTrendRow) GetScoreFactorBenchmark() string {
	if x != nil && x.ScoreFactorBenchmark != nil {
		return *x.ScoreFactorBenchmark
	}
	return ""
}

type NirvanaRiskScoreV5_UWRubricRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScoreStart     int64    `protobuf:"varint,1,opt,name=scoreStart,proto3" json:"scoreStart,omitempty"`
	ScoreEnd       int64    `protobuf:"varint,2,opt,name=scoreEnd,proto3" json:"scoreEnd,omitempty"`
	Decile         float32  `protobuf:"fixed32,3,opt,name=decile,proto3" json:"decile,omitempty"`
	Discount       *float32 `protobuf:"fixed32,4,opt,name=discount,proto3,oneof" json:"discount,omitempty"`
	IsMarket       bool     `protobuf:"varint,5,opt,name=isMarket,proto3" json:"isMarket,omitempty"`
	MarketCategory string   `protobuf:"bytes,6,opt,name=marketCategory,proto3" json:"marketCategory,omitempty"`
}

func (x *NirvanaRiskScoreV5_UWRubricRow) Reset() {
	*x = NirvanaRiskScoreV5_UWRubricRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaRiskScoreV5_UWRubricRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaRiskScoreV5_UWRubricRow) ProtoMessage() {}

func (x *NirvanaRiskScoreV5_UWRubricRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaRiskScoreV5_UWRubricRow.ProtoReflect.Descriptor instead.
func (*NirvanaRiskScoreV5_UWRubricRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{21, 1}
}

func (x *NirvanaRiskScoreV5_UWRubricRow) GetScoreStart() int64 {
	if x != nil {
		return x.ScoreStart
	}
	return 0
}

func (x *NirvanaRiskScoreV5_UWRubricRow) GetScoreEnd() int64 {
	if x != nil {
		return x.ScoreEnd
	}
	return 0
}

func (x *NirvanaRiskScoreV5_UWRubricRow) GetDecile() float32 {
	if x != nil {
		return x.Decile
	}
	return 0
}

func (x *NirvanaRiskScoreV5_UWRubricRow) GetDiscount() float32 {
	if x != nil && x.Discount != nil {
		return *x.Discount
	}
	return 0
}

func (x *NirvanaRiskScoreV5_UWRubricRow) GetIsMarket() bool {
	if x != nil {
		return x.IsMarket
	}
	return false
}

func (x *NirvanaRiskScoreV5_UWRubricRow) GetMarketCategory() string {
	if x != nil {
		return x.MarketCategory
	}
	return ""
}

type NirvanaVinRiskScoreV1_VinRiskScoreTrendRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VIN            string   `protobuf:"bytes,1,opt,name=VIN,proto3" json:"VIN,omitempty"`
	WindowStart    string   `protobuf:"bytes,2,opt,name=windowStart,proto3" json:"windowStart,omitempty"`
	WindowEnd      string   `protobuf:"bytes,3,opt,name=windowEnd,proto3" json:"windowEnd,omitempty"`
	WindowType     string   `protobuf:"bytes,4,opt,name=windowType,proto3" json:"windowType,omitempty"`
	Score          *float32 `protobuf:"fixed32,5,opt,name=score,proto3,oneof" json:"score,omitempty"`
	ScoreTimestamp string   `protobuf:"bytes,6,opt,name=scoreTimestamp,proto3" json:"scoreTimestamp,omitempty"`
	ErrorCode      int64    `protobuf:"varint,7,opt,name=errorCode,proto3" json:"errorCode,omitempty"`
	ErrorMessage   string   `protobuf:"bytes,8,opt,name=errorMessage,proto3" json:"errorMessage,omitempty"`
}

func (x *NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) Reset() {
	*x = NirvanaVinRiskScoreV1_VinRiskScoreTrendRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) ProtoMessage() {}

func (x *NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaVinRiskScoreV1_VinRiskScoreTrendRow.ProtoReflect.Descriptor instead.
func (*NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{22, 0}
}

func (x *NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) GetVIN() string {
	if x != nil {
		return x.VIN
	}
	return ""
}

func (x *NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) GetWindowStart() string {
	if x != nil {
		return x.WindowStart
	}
	return ""
}

func (x *NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) GetWindowEnd() string {
	if x != nil {
		return x.WindowEnd
	}
	return ""
}

func (x *NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) GetWindowType() string {
	if x != nil {
		return x.WindowType
	}
	return ""
}

func (x *NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) GetScore() float32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) GetScoreTimestamp() string {
	if x != nil {
		return x.ScoreTimestamp
	}
	return ""
}

func (x *NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) GetErrorCode() int64 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *NirvanaVinRiskScoreV1_VinRiskScoreTrendRow) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

type VinHaulClusterTagV1_VinHaulClusterTrendRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VIN          string  `protobuf:"bytes,1,opt,name=VIN,proto3" json:"VIN,omitempty"`
	WindowStart  string  `protobuf:"bytes,2,opt,name=windowStart,proto3" json:"windowStart,omitempty"`
	WindowEnd    string  `protobuf:"bytes,3,opt,name=windowEnd,proto3" json:"windowEnd,omitempty"`
	WindowType   string  `protobuf:"bytes,4,opt,name=windowType,proto3" json:"windowType,omitempty"`
	TagTimestamp string  `protobuf:"bytes,5,opt,name=tagTimestamp,proto3" json:"tagTimestamp,omitempty"`
	IsShortHaul  bool    `protobuf:"varint,6,opt,name=isShortHaul,proto3" json:"isShortHaul,omitempty"`
	ErrorMessage *string `protobuf:"bytes,7,opt,name=errorMessage,proto3,oneof" json:"errorMessage,omitempty"`
}

func (x *VinHaulClusterTagV1_VinHaulClusterTrendRow) Reset() {
	*x = VinHaulClusterTagV1_VinHaulClusterTrendRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VinHaulClusterTagV1_VinHaulClusterTrendRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VinHaulClusterTagV1_VinHaulClusterTrendRow) ProtoMessage() {}

func (x *VinHaulClusterTagV1_VinHaulClusterTrendRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VinHaulClusterTagV1_VinHaulClusterTrendRow.ProtoReflect.Descriptor instead.
func (*VinHaulClusterTagV1_VinHaulClusterTrendRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{23, 0}
}

func (x *VinHaulClusterTagV1_VinHaulClusterTrendRow) GetVIN() string {
	if x != nil {
		return x.VIN
	}
	return ""
}

func (x *VinHaulClusterTagV1_VinHaulClusterTrendRow) GetWindowStart() string {
	if x != nil {
		return x.WindowStart
	}
	return ""
}

func (x *VinHaulClusterTagV1_VinHaulClusterTrendRow) GetWindowEnd() string {
	if x != nil {
		return x.WindowEnd
	}
	return ""
}

func (x *VinHaulClusterTagV1_VinHaulClusterTrendRow) GetWindowType() string {
	if x != nil {
		return x.WindowType
	}
	return ""
}

func (x *VinHaulClusterTagV1_VinHaulClusterTrendRow) GetTagTimestamp() string {
	if x != nil {
		return x.TagTimestamp
	}
	return ""
}

func (x *VinHaulClusterTagV1_VinHaulClusterTrendRow) GetIsShortHaul() bool {
	if x != nil {
		return x.IsShortHaul
	}
	return false
}

func (x *VinHaulClusterTagV1_VinHaulClusterTrendRow) GetErrorMessage() string {
	if x != nil && x.ErrorMessage != nil {
		return *x.ErrorMessage
	}
	return ""
}

type NirvanaVinRiskScoreClusterV1VinClustersTrendRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VIN              string   `protobuf:"bytes,1,opt,name=VIN,proto3" json:"VIN,omitempty"`
	WindowStart      string   `protobuf:"bytes,2,opt,name=windowStart,proto3" json:"windowStart,omitempty"`
	WindowEnd        string   `protobuf:"bytes,3,opt,name=windowEnd,proto3" json:"windowEnd,omitempty"`
	WindowType       string   `protobuf:"bytes,4,opt,name=windowType,proto3" json:"windowType,omitempty"`
	Score            *float32 `protobuf:"fixed32,5,opt,name=score,proto3,oneof" json:"score,omitempty"`
	ClusterName      string   `protobuf:"bytes,6,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	PositiveFeatures string   `protobuf:"bytes,7,opt,name=positiveFeatures,proto3" json:"positiveFeatures,omitempty"`
	NegativeFeatures string   `protobuf:"bytes,8,opt,name=negativeFeatures,proto3" json:"negativeFeatures,omitempty"`
	ErrorCode        int64    `protobuf:"varint,9,opt,name=errorCode,proto3" json:"errorCode,omitempty"`
	ErrorMessage     string   `protobuf:"bytes,10,opt,name=errorMessage,proto3" json:"errorMessage,omitempty"`
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) Reset() {
	*x = NirvanaVinRiskScoreClusterV1VinClustersTrendRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaVinRiskScoreClusterV1VinClustersTrendRow) ProtoMessage() {}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaVinRiskScoreClusterV1VinClustersTrendRow.ProtoReflect.Descriptor instead.
func (*NirvanaVinRiskScoreClusterV1VinClustersTrendRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{24, 0}
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) GetVIN() string {
	if x != nil {
		return x.VIN
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) GetWindowStart() string {
	if x != nil {
		return x.WindowStart
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) GetWindowEnd() string {
	if x != nil {
		return x.WindowEnd
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) GetWindowType() string {
	if x != nil {
		return x.WindowType
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) GetScore() float32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) GetPositiveFeatures() string {
	if x != nil {
		return x.PositiveFeatures
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) GetNegativeFeatures() string {
	if x != nil {
		return x.NegativeFeatures
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) GetErrorCode() int64 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV1VinClustersTrendRow) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

type NirvanaVinRiskScoreClusterV2VinClustersTrendRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VIN                                    string   `protobuf:"bytes,1,opt,name=VIN,proto3" json:"VIN,omitempty"`
	WindowStart                            string   `protobuf:"bytes,2,opt,name=windowStart,proto3" json:"windowStart,omitempty"`
	WindowEnd                              string   `protobuf:"bytes,3,opt,name=windowEnd,proto3" json:"windowEnd,omitempty"`
	WindowType                             string   `protobuf:"bytes,4,opt,name=windowType,proto3" json:"windowType,omitempty"`
	Score                                  *float32 `protobuf:"fixed32,5,opt,name=score,proto3,oneof" json:"score,omitempty"`
	ClusterName                            string   `protobuf:"bytes,6,opt,name=clusterName,proto3" json:"clusterName,omitempty"`
	PositiveFeatures                       string   `protobuf:"bytes,7,opt,name=positiveFeatures,proto3" json:"positiveFeatures,omitempty"`
	NegativeFeatures                       string   `protobuf:"bytes,8,opt,name=negativeFeatures,proto3" json:"negativeFeatures,omitempty"`
	ErrorCode                              int64    `protobuf:"varint,9,opt,name=errorCode,proto3" json:"errorCode,omitempty"`
	ErrorMessage                           string   `protobuf:"bytes,10,opt,name=errorMessage,proto3" json:"errorMessage,omitempty"`
	TotalDistance                          float32  `protobuf:"fixed32,11,opt,name=totalDistance,proto3" json:"totalDistance,omitempty"`
	FracDistanceAvgSpeedLessThan_20        float32  `protobuf:"fixed32,12,opt,name=frac_distance_avg_speed_less_than_20,json=fracDistanceAvgSpeedLessThan20,proto3" json:"frac_distance_avg_speed_less_than_20,omitempty"`
	FracDistanceAvgSpeedBetween_20And_50   float32  `protobuf:"fixed32,13,opt,name=frac_distance_avg_speed_between_20_and_50,json=fracDistanceAvgSpeedBetween20And50,proto3" json:"frac_distance_avg_speed_between_20_and_50,omitempty"`
	FracDistanceAvgSpeedBetween_50And_65   float32  `protobuf:"fixed32,14,opt,name=frac_distance_avg_speed_between_50_and_65,json=fracDistanceAvgSpeedBetween50And65,proto3" json:"frac_distance_avg_speed_between_50_and_65,omitempty"`
	FracDistanceAvgSpeedGreaterThan_65     float32  `protobuf:"fixed32,15,opt,name=frac_distance_avg_speed_greater_than_65,json=fracDistanceAvgSpeedGreaterThan65,proto3" json:"frac_distance_avg_speed_greater_than_65,omitempty"`
	FracDistanceMaxSpeedLessThan_20        float32  `protobuf:"fixed32,16,opt,name=frac_distance_max_speed_less_than_20,json=fracDistanceMaxSpeedLessThan20,proto3" json:"frac_distance_max_speed_less_than_20,omitempty"`
	FracDistanceMaxSpeedBetween_20And_50   float32  `protobuf:"fixed32,17,opt,name=frac_distance_max_speed_between_20_and_50,json=fracDistanceMaxSpeedBetween20And50,proto3" json:"frac_distance_max_speed_between_20_and_50,omitempty"`
	FracDistanceMaxSpeedBetween_50And_65   float32  `protobuf:"fixed32,18,opt,name=frac_distance_max_speed_between_50_and_65,json=fracDistanceMaxSpeedBetween50And65,proto3" json:"frac_distance_max_speed_between_50_and_65,omitempty"`
	FracDistanceMaxSpeedGreaterThan_65     float32  `protobuf:"fixed32,19,opt,name=frac_distance_max_speed_greater_than_65,json=fracDistanceMaxSpeedGreaterThan65,proto3" json:"frac_distance_max_speed_greater_than_65,omitempty"`
	ContDistanceLessThan_5                 float32  `protobuf:"fixed32,20,opt,name=cont_distance_less_than_5,json=contDistanceLessThan5,proto3" json:"cont_distance_less_than_5,omitempty"`
	ContDistanceBetween_5And_15            float32  `protobuf:"fixed32,21,opt,name=cont_distance_between_5_and_15,json=contDistanceBetween5And15,proto3" json:"cont_distance_between_5_and_15,omitempty"`
	ContDistanceBetween_15And_50           float32  `protobuf:"fixed32,22,opt,name=cont_distance_between_15_and_50,json=contDistanceBetween15And50,proto3" json:"cont_distance_between_15_and_50,omitempty"`
	ContDistanceBetween_50And_100          float32  `protobuf:"fixed32,23,opt,name=cont_distance_between_50_and_100,json=contDistanceBetween50And100,proto3" json:"cont_distance_between_50_and_100,omitempty"`
	ContDistanceBetween_100And_200         float32  `protobuf:"fixed32,24,opt,name=cont_distance_between_100_and_200,json=contDistanceBetween100And200,proto3" json:"cont_distance_between_100_and_200,omitempty"`
	ContDistanceBetween_200And_300         float32  `protobuf:"fixed32,25,opt,name=cont_distance_between_200_and_300,json=contDistanceBetween200And300,proto3" json:"cont_distance_between_200_and_300,omitempty"`
	ContDistanceGreaterThan_300            float32  `protobuf:"fixed32,26,opt,name=cont_distance_greater_than_300,json=contDistanceGreaterThan300,proto3" json:"cont_distance_greater_than_300,omitempty"`
	HaltLessThan_2Mins                     float32  `protobuf:"fixed32,27,opt,name=halt_less_than_2_mins,json=haltLessThan2Mins,proto3" json:"halt_less_than_2_mins,omitempty"`
	HaltBetween_2And_5Mins                 float32  `protobuf:"fixed32,28,opt,name=halt_between_2_and_5_mins,json=haltBetween2And5Mins,proto3" json:"halt_between_2_and_5_mins,omitempty"`
	HaltBetween_5And_15Mins                float32  `protobuf:"fixed32,29,opt,name=halt_between_5_and_15_mins,json=haltBetween5And15Mins,proto3" json:"halt_between_5_and_15_mins,omitempty"`
	HaltBetween_15ToHour                   float32  `protobuf:"fixed32,30,opt,name=halt_between_15_to_hour,json=haltBetween15ToHour,proto3" json:"halt_between_15_to_hour,omitempty"`
	HaltBetween_1To_3Hours                 float32  `protobuf:"fixed32,31,opt,name=halt_between_1_to_3_hours,json=haltBetween1To3Hours,proto3" json:"halt_between_1_to_3_hours,omitempty"`
	HaltGreaterThan_3Hours                 float32  `protobuf:"fixed32,32,opt,name=halt_greater_than_3_hours,json=haltGreaterThan3Hours,proto3" json:"halt_greater_than_3_hours,omitempty"`
	GforceLessThan_0_2Per_10KMiles         float32  `protobuf:"fixed32,33,opt,name=gforce_less_than_0_2_per_10k_miles,json=gforceLessThan02Per10kMiles,proto3" json:"gforce_less_than_0_2_per_10k_miles,omitempty"`
	GforceBetween_0_2And_0_28Per_10KMiles  float32  `protobuf:"fixed32,34,opt,name=gforce_between_0_2_and_0_28_per_10k_miles,json=gforceBetween02And028Per10kMiles,proto3" json:"gforce_between_0_2_and_0_28_per_10k_miles,omitempty"`
	GforceBetween_0_28And_0_36Per_10KMiles float32  `protobuf:"fixed32,35,opt,name=gforce_between_0_28_and_0_36_per_10k_miles,json=gforceBetween028And036Per10kMiles,proto3" json:"gforce_between_0_28_and_0_36_per_10k_miles,omitempty"`
	GforceBetween_0_36And_0_44Per_10KMiles float32  `protobuf:"fixed32,36,opt,name=gforce_between_0_36_and_0_44_per_10k_miles,json=gforceBetween036And044Per10kMiles,proto3" json:"gforce_between_0_36_and_0_44_per_10k_miles,omitempty"`
	GforceBetween_0_44And_0_54Per_10KMiles float32  `protobuf:"fixed32,37,opt,name=gforce_between_0_44_and_0_54_per_10k_miles,json=gforceBetween044And054Per10kMiles,proto3" json:"gforce_between_0_44_and_0_54_per_10k_miles,omitempty"`
	GforceGreaterThan_0_54Per_10KMiles     float32  `protobuf:"fixed32,38,opt,name=gforce_greater_than_0_54_per_10k_miles,json=gforceGreaterThan054Per10kMiles,proto3" json:"gforce_greater_than_0_54_per_10k_miles,omitempty"`
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) Reset() {
	*x = NirvanaVinRiskScoreClusterV2VinClustersTrendRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_definitions_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaVinRiskScoreClusterV2VinClustersTrendRow) ProtoMessage() {}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_definitions_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaVinRiskScoreClusterV2VinClustersTrendRow.ProtoReflect.Descriptor instead.
func (*NirvanaVinRiskScoreClusterV2VinClustersTrendRow) Descriptor() ([]byte, []int) {
	return file_feature_store_definitions_proto_rawDescGZIP(), []int{25, 0}
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetVIN() string {
	if x != nil {
		return x.VIN
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetWindowStart() string {
	if x != nil {
		return x.WindowStart
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetWindowEnd() string {
	if x != nil {
		return x.WindowEnd
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetWindowType() string {
	if x != nil {
		return x.WindowType
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetScore() float32 {
	if x != nil && x.Score != nil {
		return *x.Score
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetPositiveFeatures() string {
	if x != nil {
		return x.PositiveFeatures
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetNegativeFeatures() string {
	if x != nil {
		return x.NegativeFeatures
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetErrorCode() int64 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetTotalDistance() float32 {
	if x != nil {
		return x.TotalDistance
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetFracDistanceAvgSpeedLessThan_20() float32 {
	if x != nil {
		return x.FracDistanceAvgSpeedLessThan_20
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetFracDistanceAvgSpeedBetween_20And_50() float32 {
	if x != nil {
		return x.FracDistanceAvgSpeedBetween_20And_50
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetFracDistanceAvgSpeedBetween_50And_65() float32 {
	if x != nil {
		return x.FracDistanceAvgSpeedBetween_50And_65
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetFracDistanceAvgSpeedGreaterThan_65() float32 {
	if x != nil {
		return x.FracDistanceAvgSpeedGreaterThan_65
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetFracDistanceMaxSpeedLessThan_20() float32 {
	if x != nil {
		return x.FracDistanceMaxSpeedLessThan_20
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetFracDistanceMaxSpeedBetween_20And_50() float32 {
	if x != nil {
		return x.FracDistanceMaxSpeedBetween_20And_50
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetFracDistanceMaxSpeedBetween_50And_65() float32 {
	if x != nil {
		return x.FracDistanceMaxSpeedBetween_50And_65
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetFracDistanceMaxSpeedGreaterThan_65() float32 {
	if x != nil {
		return x.FracDistanceMaxSpeedGreaterThan_65
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetContDistanceLessThan_5() float32 {
	if x != nil {
		return x.ContDistanceLessThan_5
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetContDistanceBetween_5And_15() float32 {
	if x != nil {
		return x.ContDistanceBetween_5And_15
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetContDistanceBetween_15And_50() float32 {
	if x != nil {
		return x.ContDistanceBetween_15And_50
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetContDistanceBetween_50And_100() float32 {
	if x != nil {
		return x.ContDistanceBetween_50And_100
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetContDistanceBetween_100And_200() float32 {
	if x != nil {
		return x.ContDistanceBetween_100And_200
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetContDistanceBetween_200And_300() float32 {
	if x != nil {
		return x.ContDistanceBetween_200And_300
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetContDistanceGreaterThan_300() float32 {
	if x != nil {
		return x.ContDistanceGreaterThan_300
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetHaltLessThan_2Mins() float32 {
	if x != nil {
		return x.HaltLessThan_2Mins
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetHaltBetween_2And_5Mins() float32 {
	if x != nil {
		return x.HaltBetween_2And_5Mins
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetHaltBetween_5And_15Mins() float32 {
	if x != nil {
		return x.HaltBetween_5And_15Mins
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetHaltBetween_15ToHour() float32 {
	if x != nil {
		return x.HaltBetween_15ToHour
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetHaltBetween_1To_3Hours() float32 {
	if x != nil {
		return x.HaltBetween_1To_3Hours
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetHaltGreaterThan_3Hours() float32 {
	if x != nil {
		return x.HaltGreaterThan_3Hours
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetGforceLessThan_0_2Per_10KMiles() float32 {
	if x != nil {
		return x.GforceLessThan_0_2Per_10KMiles
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetGforceBetween_0_2And_0_28Per_10KMiles() float32 {
	if x != nil {
		return x.GforceBetween_0_2And_0_28Per_10KMiles
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetGforceBetween_0_28And_0_36Per_10KMiles() float32 {
	if x != nil {
		return x.GforceBetween_0_28And_0_36Per_10KMiles
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetGforceBetween_0_36And_0_44Per_10KMiles() float32 {
	if x != nil {
		return x.GforceBetween_0_36And_0_44Per_10KMiles
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetGforceBetween_0_44And_0_54Per_10KMiles() float32 {
	if x != nil {
		return x.GforceBetween_0_44And_0_54Per_10KMiles
	}
	return 0
}

func (x *NirvanaVinRiskScoreClusterV2VinClustersTrendRow) GetGforceGreaterThan_0_54Per_10KMiles() float32 {
	if x != nil {
		return x.GforceGreaterThan_0_54Per_10KMiles
	}
	return 0
}

var File_feature_store_definitions_proto protoreflect.FileDescriptor

var file_feature_store_definitions_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x1a, 0x1a, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd6, 0x1a,
	0x0a, 0x07, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x12, 0x59, 0x0a, 0x10, 0x4d, 0x6f, 0x63,
	0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x4d, 0x6f, 0x63, 0x6b,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x52, 0x10, 0x4d, 0x6f, 0x63, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x0e, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x44, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x52, 0x0e, 0x48, 0x61, 0x7a, 0x61, 0x72,
	0x64, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x53, 0x0a, 0x0e, 0x48, 0x61, 0x7a,
	0x61, 0x72, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x52, 0x0e,
	0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d,
	0x0a, 0x0c, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x48, 0x61, 0x7a,
	0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x52,
	0x0c, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x62, 0x0a,
	0x13, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c,
	0x6f, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74,
	0x79, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x52, 0x13, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x54, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x68, 0x0a, 0x15, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61,
	0x6c, 0x74, 0x79, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x52, 0x15, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79,
	0x61, 0x6c, 0x74, 0x79, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x12, 0x71, 0x0a, 0x18, 0x42,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x61, 0x64, 0x69,
	0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x52, 0x18, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x61, 0x64, 0x69,
	0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x56,
	0x0a, 0x0f, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x56, 0x69, 0x6e, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x56, 0x69, 0x6e, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x52, 0x0f, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x56, 0x69, 0x6e, 0x4d,
	0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x52,
	0x75, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x52, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x52, 0x0c, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x52, 0x75,
	0x6e, 0x52, 0x61, 0x74, 0x65, 0x12, 0x65, 0x0a, 0x14, 0x56, 0x69, 0x6e, 0x54, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x56, 0x69, 0x6e, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x52, 0x14, 0x56, 0x69, 0x6e, 0x54, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x7a, 0x0a, 0x1b,
	0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x43,
	0x72, 0x61, 0x73, 0x68, 0x46, 0x72, 0x65, 0x71, 0x54, 0x52, 0x53, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x38, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67,
	0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x43, 0x72, 0x61, 0x73, 0x68, 0x46, 0x72,
	0x65, 0x71, 0x54, 0x52, 0x53, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x52, 0x1b, 0x4d, 0x69, 0x6c,
	0x65, 0x61, 0x67, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x43, 0x72, 0x61, 0x73,
	0x68, 0x46, 0x72, 0x65, 0x71, 0x54, 0x52, 0x53, 0x12, 0x59, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x52, 0x10, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x69, 0x6c, 0x65,
	0x61, 0x67, 0x65, 0x12, 0x59, 0x0a, 0x10, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73,
	0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x52, 0x10, 0x4e, 0x69,
	0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x62,
	0x0a, 0x13, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x61,
	0x6c, 0x6f, 0x67, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x52, 0x13, 0x4e,
	0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x5c, 0x0a, 0x11, 0x56, 0x69, 0x6e, 0x48, 0x61, 0x75, 0x6c, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x56, 0x69, 0x6e, 0x48, 0x61, 0x75, 0x6c, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x52, 0x11, 0x56,
	0x69, 0x6e, 0x48, 0x61, 0x75, 0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67,
	0x12, 0x77, 0x0a, 0x1a, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x4e, 0x69, 0x72,
	0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x52, 0x1a, 0x4e,
	0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x1a, 0xb4, 0x01, 0x0a, 0x16, 0x4d, 0x6f,
	0x63, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x02, 0x76, 0x30, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x73, 0x5f, 0x76, 0x30, 0x52, 0x02, 0x76, 0x30, 0x12, 0x32, 0x0a, 0x02, 0x76, 0x31, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x73, 0x5f, 0x76, 0x31, 0x52, 0x02, 0x76, 0x31, 0x12, 0x32, 0x0a, 0x02,
	0x76, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x5f, 0x76, 0x30, 0x52, 0x02, 0x76, 0x32,
	0x1a, 0x48, 0x0a, 0x14, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x02, 0x76, 0x31, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x76, 0x31, 0x52, 0x02, 0x76, 0x31, 0x1a, 0x7a, 0x0a, 0x14, 0x48, 0x61,
	0x7a, 0x61, 0x72, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x30, 0x0a, 0x02, 0x76, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x48,
	0x61, 0x7a, 0x61, 0x72, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x31,
	0x52, 0x02, 0x76, 0x31, 0x12, 0x30, 0x0a, 0x02, 0x76, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x76, 0x32, 0x52, 0x02, 0x76, 0x32, 0x1a, 0x44, 0x0a, 0x12, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x02,
	0x76, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x5f, 0x76, 0x31, 0x52, 0x02, 0x76, 0x31, 0x1a, 0x89, 0x01, 0x0a,
	0x19, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x02, 0x76, 0x31,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f,
	0x79, 0x61, 0x6c, 0x74, 0x79, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x76, 0x31, 0x52, 0x02, 0x76,
	0x31, 0x12, 0x35, 0x0a, 0x02, 0x76, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x54, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x76, 0x32, 0x52, 0x02, 0x76, 0x32, 0x1a, 0x8f, 0x01, 0x0a, 0x1b, 0x43, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x49, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x02, 0x76, 0x31, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61,
	0x6c, 0x74, 0x79, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x76, 0x31, 0x52, 0x02, 0x76,
	0x31, 0x12, 0x37, 0x0a, 0x02, 0x76, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x49, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x5f, 0x76, 0x32, 0x52, 0x02, 0x76, 0x32, 0x1a, 0x5c, 0x0a, 0x1e, 0x42, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x02,
	0x76, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x5f, 0x76, 0x31, 0x52, 0x02, 0x76, 0x31, 0x1a, 0x4a, 0x0a, 0x15, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x56, 0x69, 0x6e, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x31, 0x0a, 0x02, 0x76, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x56, 0x69, 0x6e, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x5f, 0x76, 0x31,
	0x52, 0x02, 0x76, 0x31, 0x1a, 0x44, 0x0a, 0x12, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x52, 0x75, 0x6e,
	0x52, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x02, 0x76, 0x31,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x52,
	0x61, 0x74, 0x65, 0x5f, 0x76, 0x31, 0x52, 0x02, 0x76, 0x31, 0x1a, 0x54, 0x0a, 0x1a, 0x56, 0x69,
	0x6e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x02, 0x76, 0x31, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x56, 0x69, 0x6e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x76, 0x31, 0x52, 0x02, 0x76, 0x31,
	0x1a, 0x62, 0x0a, 0x21, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x65, 0x64, 0x43, 0x72, 0x61, 0x73, 0x68, 0x46, 0x72, 0x65, 0x71, 0x54, 0x52, 0x53, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x02, 0x76, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65,
	0x64, 0x43, 0x72, 0x61, 0x73, 0x68, 0x46, 0x72, 0x65, 0x71, 0x54, 0x52, 0x53, 0x5f, 0x76, 0x31,
	0x52, 0x02, 0x76, 0x31, 0x1a, 0x4c, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5a, 0x6f, 0x6e,
	0x65, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x32,
	0x0a, 0x02, 0x76, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x5f, 0x76, 0x31, 0x52, 0x02,
	0x76, 0x31, 0x1a, 0x9c, 0x02, 0x0a, 0x16, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x32, 0x0a,
	0x02, 0x76, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e,
	0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x31, 0x52, 0x02, 0x76,
	0x31, 0x12, 0x32, 0x0a, 0x02, 0x76, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69,
	0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76,
	0x32, 0x52, 0x02, 0x76, 0x32, 0x12, 0x32, 0x0a, 0x02, 0x76, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x5f, 0x76, 0x33, 0x52, 0x02, 0x76, 0x33, 0x12, 0x32, 0x0a, 0x02, 0x76, 0x34, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73,
	0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x34, 0x52, 0x02, 0x76, 0x34, 0x12, 0x32, 0x0a,
	0x02, 0x76, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e,
	0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x35, 0x52, 0x02, 0x76,
	0x35, 0x1a, 0x52, 0x0a, 0x19, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x35,
	0x0a, 0x02, 0x76, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76,
	0x31, 0x52, 0x02, 0x76, 0x31, 0x1a, 0x4e, 0x0a, 0x17, 0x56, 0x69, 0x6e, 0x48, 0x61, 0x75, 0x6c,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x33, 0x0a, 0x02, 0x76, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x56, 0x69, 0x6e,
	0x48, 0x61, 0x75, 0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67, 0x5f, 0x76,
	0x31, 0x52, 0x02, 0x76, 0x31, 0x1a, 0x9e, 0x01, 0x0a, 0x20, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e,
	0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x02, 0x76, 0x31,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69,
	0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x5f, 0x76, 0x31, 0x52, 0x02, 0x76, 0x31, 0x12, 0x3c, 0x0a, 0x02, 0x76, 0x32, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x76, 0x32, 0x52, 0x02, 0x76, 0x32, 0x22, 0x94, 0x01, 0x0a, 0x13, 0x4d, 0x6f, 0x63, 0x6b, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x5f, 0x76, 0x30, 0x12, 0x10,
	0x0a, 0x03, 0x64, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x64, 0x6f, 0x74,
	0x12, 0x33, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x08, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x06, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x22, 0x9c, 0x02,
	0x0a, 0x13, 0x4d, 0x6f, 0x63, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x73, 0x5f, 0x76, 0x31, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x64, 0x6f, 0x74, 0x12, 0x33, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x1b, 0x0a, 0x09,
	0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x44, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x5f, 0x76, 0x31, 0x2e, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x73, 0x1a, 0x59, 0x0a, 0x0b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x34, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb2, 0x01, 0x0a,
	0x11, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x69, 0x6c, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0xb2, 0x01, 0x0a, 0x11, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xb6, 0x01, 0x0a, 0x11, 0x48, 0x61, 0x7a, 0x61, 0x72,
	0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x32, 0x12, 0x1b, 0x0a, 0x09,
	0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0xb8, 0x02, 0x0a, 0x0f, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73,
	0x5f, 0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x5b, 0x0a, 0x12, 0x68, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x48, 0x61, 0x7a,
	0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x5f, 0x76, 0x31, 0x2e, 0x48, 0x61, 0x7a,
	0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x10, 0x68, 0x61, 0x7a,
	0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x70, 0x0a, 0x0e, 0x48, 0x61, 0x7a, 0x61,
	0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x77, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x69, 0x6c,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x70, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x63, 0x74, 0x22, 0xe4, 0x04, 0x0a, 0x16, 0x43,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x54, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x68, 0x0a, 0x15, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x76, 0x31, 0x2e,
	0x43, 0x4c, 0x44, 0x61, 0x74, 0x61, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x13,
	0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x54, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0xae,
	0x02, 0x0a, 0x0e, 0x43, 0x4c, 0x44, 0x61, 0x74, 0x61, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x52, 0x6f,
	0x77, 0x12, 0x57, 0x0a, 0x0d, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x76, 0x31, 0x2e,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x70, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x67, 0x61, 0x70, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x67, 0x61, 0x70, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x67, 0x61, 0x70, 0x44, 0x61, 0x79, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x46, 0x72, 0x65, 0x71, 0x12, 0x36, 0x0a, 0x17, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22,
	0x30, 0x0a, 0x0c, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0a, 0x0a, 0x06, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x4f,
	0x6c, 0x64, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x10,
	0x02, 0x22, 0xdd, 0x05, 0x0a, 0x16, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79,
	0x61, 0x6c, 0x74, 0x79, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x76, 0x32, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12, 0x77, 0x0a, 0x1d, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x43,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x54, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x76, 0x32, 0x2e, 0x43, 0x4c, 0x44, 0x61, 0x74, 0x61, 0x66, 0x72, 0x61, 0x6d,
	0x65, 0x52, 0x6f, 0x77, 0x52, 0x1a, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79,
	0x61, 0x6c, 0x74, 0x79, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0xb3, 0x03, 0x0a, 0x0e,
	0x43, 0x4c, 0x44, 0x61, 0x74, 0x61, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x77, 0x12, 0x23,
	0x0a, 0x0d, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x61, 0x72, 0x72,
	0x69, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x17, 0x6d, 0x69, 0x6e, 0x5f,
	0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66,
	0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6d, 0x69, 0x6e, 0x45, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12,
	0x31, 0x0a, 0x15, 0x6d, 0x61, 0x78, 0x5f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x6d, 0x61, 0x78, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x54, 0x6f, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x67, 0x61, 0x70, 0x5f,
	0x64, 0x61, 0x79, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x47, 0x61, 0x70, 0x44, 0x61, 0x79, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x79,
	0x65, 0x61, 0x72, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x16, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x59, 0x65, 0x61,
	0x72, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x79, 0x65, 0x61, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x79, 0x65,
	0x61, 0x72, 0x50, 0x65, 0x72, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x18,
	0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16,
	0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0f, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x65,
	0x73, 0x22, 0x85, 0x02, 0x0a, 0x18, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79,
	0x61, 0x6c, 0x74, 0x79, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x76, 0x31, 0x12, 0x1b,
	0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x36, 0x0a, 0x17, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x6c, 0x6f,
	0x79, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x15, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61,
	0x6c, 0x74, 0x79, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x47, 0x61, 0x70, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xe3, 0x03, 0x0a, 0x18, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x49, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x5f, 0x76, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x32, 0x0a,
	0x15, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49,
	0x64, 0x12, 0x36, 0x0a, 0x17, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x79,
	0x61, 0x6c, 0x74, 0x79, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x15, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c,
	0x74, 0x79, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x61, 0x72,
	0x72, 0x69, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72,
	0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x38, 0x0a, 0x18, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x67, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x47,
	0x61, 0x70, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x61, 0x70, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x47, 0x61, 0x70, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x2b, 0x0a, 0x11, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0xdb, 0x03, 0x0a, 0x1b, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73,
	0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x76, 0x31, 0x12,
	0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x46, 0x0a, 0x1e, 0x4d, 0x69, 0x6c, 0x65, 0x61,
	0x67, 0x65, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5a, 0x65,
	0x72, 0x6f, 0x54, 0x6f, 0x46, 0x69, 0x66, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x1e, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x42, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x5a, 0x65, 0x72, 0x6f, 0x54, 0x6f, 0x46, 0x69, 0x66, 0x74, 0x79, 0x12,
	0x52, 0x0a, 0x24, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73,
	0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x69, 0x66, 0x74, 0x79, 0x54, 0x6f, 0x54, 0x77, 0x6f,
	0x48, 0x75, 0x6e, 0x64, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x24, 0x4d,
	0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x42, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x46, 0x69, 0x66, 0x74, 0x79, 0x54, 0x6f, 0x54, 0x77, 0x6f, 0x48, 0x75, 0x6e, 0x64,
	0x72, 0x65, 0x64, 0x12, 0x5e, 0x0a, 0x2a, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x61,
	0x64, 0x69, 0x75, 0x73, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x77, 0x6f, 0x48, 0x75, 0x6e,
	0x64, 0x72, 0x65, 0x64, 0x54, 0x6f, 0x46, 0x69, 0x76, 0x65, 0x48, 0x75, 0x6e, 0x64, 0x72, 0x65,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x2a, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65,
	0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x77, 0x6f, 0x48,
	0x75, 0x6e, 0x64, 0x72, 0x65, 0x64, 0x54, 0x6f, 0x46, 0x69, 0x76, 0x65, 0x48, 0x75, 0x6e, 0x64,
	0x72, 0x65, 0x64, 0x12, 0x4e, 0x0a, 0x22, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x61,
	0x64, 0x69, 0x75, 0x73, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x69, 0x76, 0x65, 0x48, 0x75,
	0x6e, 0x64, 0x72, 0x65, 0x64, 0x50, 0x6c, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x22, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x42, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x46, 0x69, 0x76, 0x65, 0x48, 0x75, 0x6e, 0x64, 0x72, 0x65, 0x64, 0x50,
	0x6c, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x22, 0x93, 0x01,
	0x0a, 0x12, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x56, 0x69, 0x6e, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67,
	0x65, 0x5f, 0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x69,
	0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0xb1, 0x05, 0x0a, 0x0f, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x52, 0x75, 0x6e,
	0x52, 0x61, 0x74, 0x65, 0x5f, 0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x6b, 0x0a, 0x1b, 0x77, 0x65, 0x65, 0x6b, 0x6c, 0x79, 0x5f, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x52,
	0x75, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x5f, 0x76, 0x31, 0x2e, 0x57, 0x52, 0x44, 0x61, 0x74, 0x61,
	0x66, 0x72, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x17, 0x77, 0x65, 0x65, 0x6b, 0x6c, 0x79,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x52, 0x75, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x54, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0xd8, 0x03, 0x0a,
	0x0e, 0x57, 0x52, 0x44, 0x61, 0x74, 0x61, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x77, 0x12,
	0x24, 0x0a, 0x0e, 0x69, 0x73, 0x6f, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x73, 0x6f, 0x57, 0x65, 0x65, 0x6b,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x10, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x76, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x56, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x1d, 0x0a, 0x07, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x02, 0x48, 0x01, 0x52, 0x07, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x2a, 0x0a, 0x0e, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x6d, 0x69, 0x6c, 0x65, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x02, 0x52, 0x0d, 0x79, 0x65, 0x61, 0x72,
	0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x13,
	0x68, 0x61, 0x6c, 0x66, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x6d, 0x69, 0x6c, 0x65,
	0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x48, 0x03, 0x52, 0x11, 0x68, 0x61, 0x6c,
	0x66, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x30, 0x0a, 0x11, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x5f, 0x6d,
	0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x48, 0x04, 0x52, 0x10,
	0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x6d,
	0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x48, 0x05, 0x52, 0x0e,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x76, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6d, 0x69, 0x6c, 0x65, 0x61,
	0x67, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x6d, 0x69,
	0x6c, 0x65, 0x61, 0x67, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x68, 0x61, 0x6c, 0x66, 0x5f, 0x79,
	0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x42, 0x14, 0x0a,
	0x12, 0x5f, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x5f, 0x6d, 0x69, 0x6c, 0x65,
	0x61, 0x67, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f,
	0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x22, 0x87, 0x04, 0x0a, 0x17, 0x56, 0x69, 0x6e, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x5f, 0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x7a, 0x0a, 0x1c, 0x76, 0x69, 0x6e, 0x5f, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x56, 0x69, 0x6e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x76, 0x31, 0x2e, 0x56,
	0x69, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f,
	0x77, 0x52, 0x19, 0x76, 0x69, 0x6e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x97, 0x02, 0x0a, 0x12, 0x56, 0x69, 0x6e, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x12, 0x10,
	0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x65,
	0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f,
	0x6d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x65, 0x73, 0x74,
	0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x4d, 0x69, 0x6c, 0x65,
	0x73, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11,
	0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x61, 0x69,
	0x6c, 0x79, 0x5f, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11,
	0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65,
	0x73, 0x22, 0xf6, 0x03, 0x0a, 0x1e, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x57, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x65, 0x64, 0x43, 0x72, 0x61, 0x73, 0x68, 0x46, 0x72, 0x65, 0x71, 0x54, 0x52,
	0x53, 0x5f, 0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x98, 0x01, 0x0a, 0x20, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x57, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x65, 0x64, 0x43, 0x72, 0x61, 0x73, 0x68, 0x46, 0x72, 0x65, 0x71, 0x54, 0x52,
	0x53, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4d, 0x69, 0x6c,
	0x65, 0x61, 0x67, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x43, 0x72, 0x61, 0x73,
	0x68, 0x46, 0x72, 0x65, 0x71, 0x54, 0x52, 0x53, 0x5f, 0x76, 0x31, 0x2e, 0x4d, 0x69, 0x6c, 0x65,
	0x61, 0x67, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x43, 0x72, 0x61, 0x73, 0x68,
	0x46, 0x72, 0x65, 0x71, 0x54, 0x52, 0x53, 0x52, 0x6f, 0x77, 0x52, 0x20, 0x4d, 0x69, 0x6c, 0x65,
	0x61, 0x67, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x43, 0x72, 0x61, 0x73, 0x68,
	0x46, 0x72, 0x65, 0x71, 0x54, 0x52, 0x53, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0xe0, 0x01, 0x0a, 0x1e, 0x4d, 0x69, 0x6c, 0x65,
	0x61, 0x67, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x43, 0x72, 0x61, 0x73, 0x68,
	0x46, 0x72, 0x65, 0x71, 0x54, 0x52, 0x53, 0x52, 0x6f, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x76, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x08, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x76, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xff, 0x03, 0x0a, 0x13, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x5f,
	0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12,
	0x6c, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x69, 0x6c, 0x65,
	0x61, 0x67, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x5f,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x69, 0x6c, 0x65,
	0x61, 0x67, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x15, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5a, 0x6f, 0x6e,
	0x65, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0xa1, 0x02, 0x0a, 0x13, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x6f, 0x77,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x13, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x48, 0x01, 0x52, 0x12, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x02, 0x52,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x13,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x03, 0x52, 0x12, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42,
	0x16, 0x0a, 0x14, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x64,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x94, 0x06, 0x0a,
	0x13, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x5f, 0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x77, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x77,
	0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a,
	0x0e, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73,
	0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x31, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x77, 0x52, 0x0e, 0x72, 0x69, 0x73,
	0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x12, 0x4a, 0x0a, 0x08, 0x55,
	0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69,
	0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76,
	0x31, 0x2e, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x52, 0x6f, 0x77, 0x52, 0x08, 0x55,
	0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x1a, 0xe4, 0x01, 0x0a, 0x11, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x77, 0x12, 0x20, 0x0a, 0x0b, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01, 0x52, 0x08, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xa7, 0x01, 0x0a, 0x0b, 0x55, 0x57,
	0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x52, 0x6f, 0x77, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x45, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x63, 0x69, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x64, 0x65, 0x63, 0x69, 0x6c, 0x65, 0x12, 0x1f, 0x0a,
	0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48,
	0x00, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x98, 0x06, 0x0a, 0x13, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x32, 0x12, 0x1b, 0x0a, 0x09, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x77,
	0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x77, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72,
	0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x32,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52,
	0x6f, 0x77, 0x52, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65,
	0x6e, 0x64, 0x12, 0x4a, 0x0a, 0x08, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x32, 0x2e, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69,
	0x63, 0x52, 0x6f, 0x77, 0x52, 0x08, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0xe4, 0x01, 0x0a, 0x11, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x77, 0x12,
	0x20, 0x0a, 0x0b, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x19, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x76, 0x69,
	0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01, 0x52, 0x08,
	0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x1a, 0xab, 0x01, 0x0a, 0x0b, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x52, 0x6f, 0x77,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x65, 0x63, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x64, 0x65,
	0x63, 0x69, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x4d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x88,
	0x0b, 0x0a, 0x13, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x5f, 0x76, 0x33, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x77, 0x52, 0x75, 0x62, 0x72, 0x69,
	0x63, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x75, 0x77, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x5c, 0x0a, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e,
	0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x33, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x77, 0x52, 0x0e, 0x72,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x12, 0x4a, 0x0a,
	0x08, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x5f, 0x76, 0x33, 0x2e, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x52, 0x6f, 0x77, 0x52,
	0x08, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x1a, 0xac, 0x06, 0x0a, 0x11, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x77, 0x12, 0x20, 0x0a, 0x0b, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01, 0x52, 0x08, 0x76, 0x69, 0x6e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x39, 0x0a, 0x15, 0x66, 0x72, 0x61, 0x63, 0x53, 0x68, 0x6f, 0x72,
	0x74, 0x48, 0x61, 0x75, 0x6c, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x02, 0x48, 0x02, 0x52, 0x15, 0x66, 0x72, 0x61, 0x63, 0x53, 0x68, 0x6f, 0x72, 0x74,
	0x48, 0x61, 0x75, 0x6c, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x2f, 0x0a, 0x10, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x10, 0x69, 0x73, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x35, 0x0a, 0x13, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52,
	0x13, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x6f, 0x73,
	0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x05, 0x52, 0x10, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x54, 0x72, 0x65, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0f, 0x68, 0x69, 0x64, 0x65,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x06, 0x52, 0x0f, 0x68, 0x69, 0x64, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52,
	0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x31, 0x0a, 0x11, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x08, 0x52, 0x11, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x14, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x09, 0x52, 0x14, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06,
	0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x76, 0x69, 0x6e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x66, 0x72, 0x61, 0x63, 0x53, 0x68, 0x6f, 0x72,
	0x74, 0x48, 0x61, 0x75, 0x6c, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x65,
	0x78, 0x70, 0x6f, 0x73, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x68, 0x69, 0x64, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x73, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d,
	0x61, 0x72, 0x6b, 0x1a, 0xd3, 0x01, 0x0a, 0x0b, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63,
	0x52, 0x6f, 0x77, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x6e, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x6e, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x64, 0x65, 0x63, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x06, 0x64, 0x65, 0x63, 0x69, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x4d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x88, 0x0b, 0x0a, 0x13, 0x4e, 0x69,
	0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76,
	0x34, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x28, 0x0a, 0x0f, 0x75, 0x77, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x77, 0x52, 0x75, 0x62,
	0x72, 0x69, 0x63, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x0e, 0x72, 0x69,
	0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x5f, 0x76, 0x34, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x77, 0x52, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x12, 0x4a, 0x0a, 0x08, 0x55, 0x57, 0x52, 0x75,
	0x62, 0x72, 0x69, 0x63, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x34, 0x2e, 0x55,
	0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x52, 0x6f, 0x77, 0x52, 0x08, 0x55, 0x57, 0x52, 0x75,
	0x62, 0x72, 0x69, 0x63, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a,
	0xac, 0x06, 0x0a, 0x11, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65,
	0x6e, 0x64, 0x52, 0x6f, 0x77, 0x12, 0x20, 0x0a, 0x0b, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x45, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x45, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x1f, 0x0a, 0x08, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x02, 0x48, 0x01, 0x52, 0x08, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x39, 0x0a, 0x15, 0x66, 0x72, 0x61, 0x63, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x48, 0x61, 0x75, 0x6c,
	0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x48, 0x02,
	0x52, 0x15, 0x66, 0x72, 0x61, 0x63, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x48, 0x61, 0x75, 0x6c, 0x44,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x10, 0x69, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x10, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x13, 0x6c,
	0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x13, 0x6c, 0x6f, 0x77, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x2f, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x10,
	0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0f, 0x68, 0x69, 0x64, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x0f,
	0x68, 0x69, 0x64, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x27, 0x0a, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x0c, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x11, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x08, 0x52, 0x11, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x37,
	0x0a, 0x14, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x65, 0x6e,
	0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x14,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x65, 0x6e, 0x63, 0x68,
	0x6d, 0x61, 0x72, 0x6b, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x18,
	0x0a, 0x16, 0x5f, 0x66, 0x72, 0x61, 0x63, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x48, 0x61, 0x75, 0x6c,
	0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x69, 0x73, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x42, 0x16, 0x0a,
	0x14, 0x5f, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x68,
	0x69, 0x64, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x42,
	0x14, 0x0a, 0x12, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x1a, 0xd3,
	0x01, 0x0a, 0x0b, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x52, 0x6f, 0x77, 0x12, 0x1e,
	0x0a, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65,
	0x63, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x64, 0x65, 0x63, 0x69,
	0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x12,
	0x26, 0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x88, 0x0b, 0x0a, 0x13, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61,
	0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x35, 0x12, 0x1b, 0x0a, 0x09,
	0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x75,
	0x77, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75, 0x77, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69,
	0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76,
	0x35, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64,
	0x52, 0x6f, 0x77, 0x52, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72,
	0x65, 0x6e, 0x64, 0x12, 0x4a, 0x0a, 0x08, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x52, 0x69, 0x73,
	0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x35, 0x2e, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72,
	0x69, 0x63, 0x52, 0x6f, 0x77, 0x52, 0x08, 0x55, 0x57, 0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0xac, 0x06, 0x0a, 0x11, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x77,
	0x12, 0x20, 0x0a, 0x0b, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x19, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48,
	0x00, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x76,
	0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01, 0x52,
	0x08, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x39, 0x0a, 0x15, 0x66, 0x72,
	0x61, 0x63, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x48, 0x61, 0x75, 0x6c, 0x44, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x48, 0x02, 0x52, 0x15, 0x66, 0x72, 0x61,
	0x63, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x48, 0x61, 0x75, 0x6c, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x10, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x03, 0x52, 0x10, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x13, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x13, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a,
	0x10, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x10, 0x65, 0x78, 0x70, 0x6f, 0x73,
	0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d,
	0x0a, 0x0f, 0x68, 0x69, 0x64, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x0f, 0x68, 0x69, 0x64, 0x65, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a,
	0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x73, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x11, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x08, 0x52, 0x11, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x14, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72,
	0x6b, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x14, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x88,
	0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x76, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x66, 0x72,
	0x61, 0x63, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x48, 0x61, 0x75, 0x6c, 0x44, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x6c, 0x6f, 0x77,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x42, 0x13, 0x0a, 0x11, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x54, 0x72, 0x65, 0x6e, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x68, 0x69, 0x64, 0x65, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x42, 0x17, 0x0a, 0x15, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x1a, 0xd3, 0x01, 0x0a, 0x0b, 0x55, 0x57,
	0x52, 0x75, 0x62, 0x72, 0x69, 0x63, 0x52, 0x6f, 0x77, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x45, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x63, 0x69, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x64, 0x65, 0x63, 0x69, 0x6c, 0x65, 0x12, 0x1f, 0x0a,
	0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x48,
	0x00, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x73, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0xb5, 0x04, 0x0a, 0x16, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x68, 0x0a, 0x11, 0x76, 0x69, 0x6e,
	0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x6e, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x77,
	0x52, 0x11, 0x76, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72,
	0x65, 0x6e, 0x64, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x97, 0x02,
	0x0a, 0x14, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x72,
	0x65, 0x6e, 0x64, 0x52, 0x6f, 0x77, 0x12, 0x10, 0x0a, 0x03, 0x56, 0x49, 0x4e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x56, 0x49, 0x4e, 0x12, 0x20, 0x0a, 0x0b, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x08, 0x0a,
	0x06, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xbe, 0x04, 0x0a, 0x14, 0x56, 0x69, 0x6e, 0x48,
	0x61, 0x75, 0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67, 0x5f, 0x76, 0x31,
	0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x6c, 0x0a, 0x13, 0x76, 0x69, 0x6e, 0x48, 0x61, 0x75, 0x6c, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3a, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x56, 0x69, 0x6e, 0x48, 0x61, 0x75, 0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x54,
	0x61, 0x67, 0x5f, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x6e, 0x48, 0x61, 0x75, 0x6c, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x77, 0x52, 0x13, 0x76, 0x69,
	0x6e, 0x48, 0x61, 0x75, 0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x54, 0x72, 0x65, 0x6e,
	0x64, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0x8a, 0x02, 0x0a, 0x16,
	0x56, 0x69, 0x6e, 0x48, 0x61, 0x75, 0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x54, 0x72,
	0x65, 0x6e, 0x64, 0x52, 0x6f, 0x77, 0x12, 0x10, 0x0a, 0x03, 0x56, 0x49, 0x4e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x56, 0x49, 0x4e, 0x12, 0x20, 0x0a, 0x0b, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x61, 0x67, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x74, 0x61, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x20, 0x0a, 0x0b,
	0x69, 0x73, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x48, 0x61, 0x75, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x48, 0x61, 0x75, 0x6c, 0x12, 0x27,
	0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xed, 0x04, 0x0a, 0x1d, 0x4e, 0x69, 0x72,
	0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x76, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x6c, 0x0a, 0x10, 0x76, 0x69, 0x6e, 0x43, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x73, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x40, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x76, 0x31, 0x2e, 0x76, 0x69,
	0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x6f,
	0x77, 0x52, 0x10, 0x76, 0x69, 0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x54, 0x72,
	0x65, 0x6e, 0x64, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0xe8, 0x02,
	0x0a, 0x13, 0x76, 0x69, 0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x54, 0x72, 0x65,
	0x6e, 0x64, 0x52, 0x6f, 0x77, 0x12, 0x10, 0x0a, 0x03, 0x56, 0x49, 0x4e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x56, 0x49, 0x4e, 0x12, 0x20, 0x0a, 0x0b, 0x77, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73,
	0x12, 0x2a, 0x0a, 0x10, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6e, 0x65, 0x67, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xd5, 0x14, 0x0a, 0x1d, 0x4e, 0x69, 0x72,
	0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x76, 0x32, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x6c, 0x0a, 0x10, 0x76, 0x69, 0x6e, 0x43, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x73, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x40, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x76, 0x32, 0x2e, 0x76, 0x69,
	0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x6f,
	0x77, 0x52, 0x10, 0x76, 0x69, 0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x54, 0x72,
	0x65, 0x6e, 0x64, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0xd0, 0x12,
	0x0a, 0x13, 0x76, 0x69, 0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x54, 0x72, 0x65,
	0x6e, 0x64, 0x52, 0x6f, 0x77, 0x12, 0x10, 0x0a, 0x03, 0x56, 0x49, 0x4e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x56, 0x49, 0x4e, 0x12, 0x20, 0x0a, 0x0b, 0x77, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x45, 0x6e, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73,
	0x12, 0x2a, 0x0a, 0x10, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6e, 0x65, 0x67, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x4c, 0x0a, 0x24, 0x66, 0x72, 0x61, 0x63, 0x5f, 0x64, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f,
	0x6c, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x68, 0x61, 0x6e, 0x5f, 0x32, 0x30, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x1e, 0x66, 0x72, 0x61, 0x63, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x41, 0x76, 0x67, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4c, 0x65, 0x73, 0x73, 0x54, 0x68, 0x61, 0x6e,
	0x32, 0x30, 0x12, 0x55, 0x0a, 0x29, 0x66, 0x72, 0x61, 0x63, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x65,
	0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x32, 0x30, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x35, 0x30, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x22, 0x66, 0x72, 0x61, 0x63, 0x44, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x41, 0x76, 0x67, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x65, 0x74, 0x77, 0x65,
	0x65, 0x6e, 0x32, 0x30, 0x41, 0x6e, 0x64, 0x35, 0x30, 0x12, 0x55, 0x0a, 0x29, 0x66, 0x72, 0x61,
	0x63, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x35, 0x30, 0x5f,
	0x61, 0x6e, 0x64, 0x5f, 0x36, 0x35, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x22, 0x66, 0x72,
	0x61, 0x63, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x76, 0x67, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x35, 0x30, 0x41, 0x6e, 0x64, 0x36, 0x35,
	0x12, 0x52, 0x0a, 0x27, 0x66, 0x72, 0x61, 0x63, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x61, 0x76, 0x67, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x72, 0x5f, 0x74, 0x68, 0x61, 0x6e, 0x5f, 0x36, 0x35, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x21, 0x66, 0x72, 0x61, 0x63, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x41,
	0x76, 0x67, 0x53, 0x70, 0x65, 0x65, 0x64, 0x47, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x54, 0x68,
	0x61, 0x6e, 0x36, 0x35, 0x12, 0x4c, 0x0a, 0x24, 0x66, 0x72, 0x61, 0x63, 0x5f, 0x64, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f,
	0x6c, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x68, 0x61, 0x6e, 0x5f, 0x32, 0x30, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x1e, 0x66, 0x72, 0x61, 0x63, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x4d, 0x61, 0x78, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4c, 0x65, 0x73, 0x73, 0x54, 0x68, 0x61, 0x6e,
	0x32, 0x30, 0x12, 0x55, 0x0a, 0x29, 0x66, 0x72, 0x61, 0x63, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x65,
	0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x32, 0x30, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x35, 0x30, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x02, 0x52, 0x22, 0x66, 0x72, 0x61, 0x63, 0x44, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x4d, 0x61, 0x78, 0x53, 0x70, 0x65, 0x65, 0x64, 0x42, 0x65, 0x74, 0x77, 0x65,
	0x65, 0x6e, 0x32, 0x30, 0x41, 0x6e, 0x64, 0x35, 0x30, 0x12, 0x55, 0x0a, 0x29, 0x66, 0x72, 0x61,
	0x63, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x35, 0x30, 0x5f,
	0x61, 0x6e, 0x64, 0x5f, 0x36, 0x35, 0x18, 0x12, 0x20, 0x01, 0x28, 0x02, 0x52, 0x22, 0x66, 0x72,
	0x61, 0x63, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x61, 0x78, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x35, 0x30, 0x41, 0x6e, 0x64, 0x36, 0x35,
	0x12, 0x52, 0x0a, 0x27, 0x66, 0x72, 0x61, 0x63, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x72, 0x5f, 0x74, 0x68, 0x61, 0x6e, 0x5f, 0x36, 0x35, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x21, 0x66, 0x72, 0x61, 0x63, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d,
	0x61, 0x78, 0x53, 0x70, 0x65, 0x65, 0x64, 0x47, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x54, 0x68,
	0x61, 0x6e, 0x36, 0x35, 0x12, 0x38, 0x0a, 0x19, 0x63, 0x6f, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x68, 0x61, 0x6e, 0x5f,
	0x35, 0x18, 0x14, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x44, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x4c, 0x65, 0x73, 0x73, 0x54, 0x68, 0x61, 0x6e, 0x35, 0x12, 0x41,
	0x0a, 0x1e, 0x63, 0x6f, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x35, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x31, 0x35,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x02, 0x52, 0x19, 0x63, 0x6f, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x35, 0x41, 0x6e, 0x64, 0x31,
	0x35, 0x12, 0x43, 0x0a, 0x1f, 0x63, 0x6f, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x31, 0x35, 0x5f, 0x61, 0x6e,
	0x64, 0x5f, 0x35, 0x30, 0x18, 0x16, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1a, 0x63, 0x6f, 0x6e, 0x74,
	0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x31,
	0x35, 0x41, 0x6e, 0x64, 0x35, 0x30, 0x12, 0x45, 0x0a, 0x20, 0x63, 0x6f, 0x6e, 0x74, 0x5f, 0x64,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f,
	0x35, 0x30, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x31, 0x30, 0x30, 0x18, 0x17, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x1b, 0x63, 0x6f, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x65,
	0x74, 0x77, 0x65, 0x65, 0x6e, 0x35, 0x30, 0x41, 0x6e, 0x64, 0x31, 0x30, 0x30, 0x12, 0x47, 0x0a,
	0x21, 0x63, 0x6f, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62,
	0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x31, 0x30, 0x30, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x32,
	0x30, 0x30, 0x18, 0x18, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1c, 0x63, 0x6f, 0x6e, 0x74, 0x44, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x31, 0x30, 0x30,
	0x41, 0x6e, 0x64, 0x32, 0x30, 0x30, 0x12, 0x47, 0x0a, 0x21, 0x63, 0x6f, 0x6e, 0x74, 0x5f, 0x64,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f,
	0x32, 0x30, 0x30, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x33, 0x30, 0x30, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x1c, 0x63, 0x6f, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x42,
	0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x32, 0x30, 0x30, 0x41, 0x6e, 0x64, 0x33, 0x30, 0x30, 0x12,
	0x42, 0x0a, 0x1e, 0x63, 0x6f, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x68, 0x61, 0x6e, 0x5f, 0x33, 0x30,
	0x30, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1a, 0x63, 0x6f, 0x6e, 0x74, 0x44, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x54, 0x68, 0x61, 0x6e,
	0x33, 0x30, 0x30, 0x12, 0x30, 0x0a, 0x15, 0x68, 0x61, 0x6c, 0x74, 0x5f, 0x6c, 0x65, 0x73, 0x73,
	0x5f, 0x74, 0x68, 0x61, 0x6e, 0x5f, 0x32, 0x5f, 0x6d, 0x69, 0x6e, 0x73, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x11, 0x68, 0x61, 0x6c, 0x74, 0x4c, 0x65, 0x73, 0x73, 0x54, 0x68, 0x61, 0x6e,
	0x32, 0x4d, 0x69, 0x6e, 0x73, 0x12, 0x37, 0x0a, 0x19, 0x68, 0x61, 0x6c, 0x74, 0x5f, 0x62, 0x65,
	0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x32, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x35, 0x5f, 0x6d, 0x69,
	0x6e, 0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x68, 0x61, 0x6c, 0x74, 0x42, 0x65,
	0x74, 0x77, 0x65, 0x65, 0x6e, 0x32, 0x41, 0x6e, 0x64, 0x35, 0x4d, 0x69, 0x6e, 0x73, 0x12, 0x39,
	0x0a, 0x1a, 0x68, 0x61, 0x6c, 0x74, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x35,
	0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x31, 0x35, 0x5f, 0x6d, 0x69, 0x6e, 0x73, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x15, 0x68, 0x61, 0x6c, 0x74, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x35,
	0x41, 0x6e, 0x64, 0x31, 0x35, 0x4d, 0x69, 0x6e, 0x73, 0x12, 0x34, 0x0a, 0x17, 0x68, 0x61, 0x6c,
	0x74, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x31, 0x35, 0x5f, 0x74, 0x6f, 0x5f,
	0x68, 0x6f, 0x75, 0x72, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x68, 0x61, 0x6c, 0x74,
	0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x31, 0x35, 0x54, 0x6f, 0x48, 0x6f, 0x75, 0x72, 0x12,
	0x37, 0x0a, 0x19, 0x68, 0x61, 0x6c, 0x74, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f,
	0x31, 0x5f, 0x74, 0x6f, 0x5f, 0x33, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x14, 0x68, 0x61, 0x6c, 0x74, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x31,
	0x54, 0x6f, 0x33, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x12, 0x38, 0x0a, 0x19, 0x68, 0x61, 0x6c, 0x74,
	0x5f, 0x67, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x68, 0x61, 0x6e, 0x5f, 0x33, 0x5f,
	0x68, 0x6f, 0x75, 0x72, 0x73, 0x18, 0x20, 0x20, 0x01, 0x28, 0x02, 0x52, 0x15, 0x68, 0x61, 0x6c,
	0x74, 0x47, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x54, 0x68, 0x61, 0x6e, 0x33, 0x48, 0x6f, 0x75,
	0x72, 0x73, 0x12, 0x47, 0x0a, 0x22, 0x67, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x65, 0x73,
	0x73, 0x5f, 0x74, 0x68, 0x61, 0x6e, 0x5f, 0x30, 0x5f, 0x32, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x31,
	0x30, 0x6b, 0x5f, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x21, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1b,
	0x67, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x4c, 0x65, 0x73, 0x73, 0x54, 0x68, 0x61, 0x6e, 0x30, 0x32,
	0x50, 0x65, 0x72, 0x31, 0x30, 0x6b, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x29, 0x67,
	0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x30, 0x5f,
	0x32, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x30, 0x5f, 0x32, 0x38, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x31,
	0x30, 0x6b, 0x5f, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x22, 0x20, 0x01, 0x28, 0x02, 0x52, 0x20,
	0x67, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x30, 0x32, 0x41,
	0x6e, 0x64, 0x30, 0x32, 0x38, 0x50, 0x65, 0x72, 0x31, 0x30, 0x6b, 0x4d, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x55, 0x0a, 0x2a, 0x67, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65,
	0x65, 0x6e, 0x5f, 0x30, 0x5f, 0x32, 0x38, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x30, 0x5f, 0x33, 0x36,
	0x5f, 0x70, 0x65, 0x72, 0x5f, 0x31, 0x30, 0x6b, 0x5f, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x23,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x21, 0x67, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x42, 0x65, 0x74, 0x77,
	0x65, 0x65, 0x6e, 0x30, 0x32, 0x38, 0x41, 0x6e, 0x64, 0x30, 0x33, 0x36, 0x50, 0x65, 0x72, 0x31,
	0x30, 0x6b, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x55, 0x0a, 0x2a, 0x67, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x5f, 0x30, 0x5f, 0x33, 0x36, 0x5f, 0x61,
	0x6e, 0x64, 0x5f, 0x30, 0x5f, 0x34, 0x34, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x31, 0x30, 0x6b, 0x5f,
	0x6d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x24, 0x20, 0x01, 0x28, 0x02, 0x52, 0x21, 0x67, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x30, 0x33, 0x36, 0x41, 0x6e, 0x64,
	0x30, 0x34, 0x34, 0x50, 0x65, 0x72, 0x31, 0x30, 0x6b, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x55,
	0x0a, 0x2a, 0x67, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x62, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e,
	0x5f, 0x30, 0x5f, 0x34, 0x34, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x30, 0x5f, 0x35, 0x34, 0x5f, 0x70,
	0x65, 0x72, 0x5f, 0x31, 0x30, 0x6b, 0x5f, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x21, 0x67, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x42, 0x65, 0x74, 0x77, 0x65, 0x65,
	0x6e, 0x30, 0x34, 0x34, 0x41, 0x6e, 0x64, 0x30, 0x35, 0x34, 0x50, 0x65, 0x72, 0x31, 0x30, 0x6b,
	0x4d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x26, 0x67, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x68, 0x61, 0x6e, 0x5f, 0x30, 0x5f, 0x35,
	0x34, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x31, 0x30, 0x6b, 0x5f, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x18,
	0x26, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1f, 0x67, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x47, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x72, 0x54, 0x68, 0x61, 0x6e, 0x30, 0x35, 0x34, 0x50, 0x65, 0x72, 0x31, 0x30,
	0x6b, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x2a, 0x9a, 0x03, 0x0a, 0x0b, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x4d, 0x6f, 0x63, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x73, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x48, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x48, 0x61, 0x7a, 0x61, 0x72,
	0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x48,
	0x61, 0x7a, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73, 0x10, 0x04, 0x12, 0x17, 0x0a,
	0x13, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x4c, 0x6f, 0x79, 0x61, 0x6c, 0x74, 0x79, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x10,
	0x06, 0x12, 0x1c, 0x0a, 0x18, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x61, 0x64, 0x69, 0x75,
	0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x10, 0x07, 0x12,
	0x13, 0x0a, 0x0f, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x56, 0x69, 0x6e, 0x4d, 0x69, 0x6c, 0x65, 0x61,
	0x67, 0x65, 0x10, 0x08, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x52, 0x75, 0x6e,
	0x52, 0x61, 0x74, 0x65, 0x10, 0x09, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x69, 0x6e, 0x54, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x10, 0x0a,
	0x12, 0x1f, 0x0a, 0x1b, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x65, 0x64, 0x43, 0x72, 0x61, 0x73, 0x68, 0x46, 0x72, 0x65, 0x71, 0x54, 0x52, 0x53, 0x10,
	0x0b, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x74, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x69,
	0x6c, 0x65, 0x61, 0x67, 0x65, 0x10, 0x0c, 0x12, 0x14, 0x0a, 0x10, 0x4e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x10, 0x0d, 0x12, 0x17, 0x0a,
	0x13, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x10, 0x0e, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x69, 0x6e, 0x48, 0x61, 0x75,
	0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x54, 0x61, 0x67, 0x10, 0x0f, 0x12, 0x1e, 0x0a,
	0x1a, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x56, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x10, 0x10, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_feature_store_definitions_proto_rawDescOnce sync.Once
	file_feature_store_definitions_proto_rawDescData = file_feature_store_definitions_proto_rawDesc
)

func file_feature_store_definitions_proto_rawDescGZIP() []byte {
	file_feature_store_definitions_proto_rawDescOnce.Do(func() {
		file_feature_store_definitions_proto_rawDescData = protoimpl.X.CompressGZIP(file_feature_store_definitions_proto_rawDescData)
	})
	return file_feature_store_definitions_proto_rawDescData
}

var file_feature_store_definitions_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_feature_store_definitions_proto_msgTypes = make([]protoimpl.MessageInfo, 64)
var file_feature_store_definitions_proto_goTypes = []interface{}{
	(FeatureName)(0),                                 // 0: feature_store.FeatureName
	(CarrierLoyaltyTableV1_DurationType)(0),          // 1: feature_store.CarrierLoyaltyTable_v1.DurationType
	(*Catalog)(nil),                                  // 2: feature_store.Catalog
	(*MockDriverScoresV0)(nil),                       // 3: feature_store.MockDriverScores_v0
	(*MockDriverScoresV1)(nil),                       // 4: feature_store.MockDriverScores_v1
	(*HazardDistanceV1)(nil),                         // 5: feature_store.HazardDistance_v1
	(*HazardDurationV1)(nil),                         // 6: feature_store.HazardDuration_v1
	(*HazardDurationV2)(nil),                         // 7: feature_store.HazardDuration_v2
	(*HazardStatesV1)(nil),                           // 8: feature_store.HazardStates_v1
	(*CarrierLoyaltyTableV1)(nil),                    // 9: feature_store.CarrierLoyaltyTable_v1
	(*CarrierLoyaltyTableV2)(nil),                    // 10: feature_store.CarrierLoyaltyTable_v2
	(*CarrierLoyaltyInsightV1)(nil),                  // 11: feature_store.CarrierLoyaltyInsight_v1
	(*CarrierLoyaltyInsightV2)(nil),                  // 12: feature_store.CarrierLoyaltyInsight_v2
	(*BucketRadiusOfOperationsV1)(nil),               // 13: feature_store.BucketRadiusOfOperations_v1
	(*DailyVinMileageV1)(nil),                        // 14: feature_store.DailyVinMileage_v1
	(*FleetRunRateV1)(nil),                           // 15: feature_store.FleetRunRate_v1
	(*VinTelematicsSummaryV1)(nil),                   // 16: feature_store.VinTelematicsSummary_v1
	(*MileageWeightedCrashFreqTRSV1)(nil),            // 17: feature_store.MileageWeightedCrashFreqTRS_v1
	(*StateZoneMileageV1)(nil),                       // 18: feature_store.StateZoneMileage_v1
	(*NirvanaRiskScoreV1)(nil),                       // 19: feature_store.NirvanaRiskScore_v1
	(*NirvanaRiskScoreV2)(nil),                       // 20: feature_store.NirvanaRiskScore_v2
	(*NirvanaRiskScoreV3)(nil),                       // 21: feature_store.NirvanaRiskScore_v3
	(*NirvanaRiskScoreV4)(nil),                       // 22: feature_store.NirvanaRiskScore_v4
	(*NirvanaRiskScoreV5)(nil),                       // 23: feature_store.NirvanaRiskScore_v5
	(*NirvanaVinRiskScoreV1)(nil),                    // 24: feature_store.NirvanaVinRiskScore_v1
	(*VinHaulClusterTagV1)(nil),                      // 25: feature_store.VinHaulClusterTag_v1
	(*NirvanaVinRiskScoreClusterV1)(nil),             // 26: feature_store.NirvanaVinRiskScoreCluster_v1
	(*NirvanaVinRiskScoreClusterV2)(nil),             // 27: feature_store.NirvanaVinRiskScoreCluster_v2
	(*Catalog_MockDriverScoresTypes)(nil),            // 28: feature_store.Catalog.MockDriverScores_types
	(*Catalog_HazardDistanceTypes)(nil),              // 29: feature_store.Catalog.HazardDistance_types
	(*Catalog_HazardDurationTypes)(nil),              // 30: feature_store.Catalog.HazardDuration_types
	(*Catalog_HazardStatesTypes)(nil),                // 31: feature_store.Catalog.HazardStates_types
	(*Catalog_CarrierLoyaltyTableTypes)(nil),         // 32: feature_store.Catalog.CarrierLoyaltyTable_types
	(*Catalog_CarrierLoyaltyInsightTypes)(nil),       // 33: feature_store.Catalog.CarrierLoyaltyInsight_types
	(*Catalog_BucketRadiusOfOperationsTypes)(nil),    // 34: feature_store.Catalog.BucketRadiusOfOperations_types
	(*Catalog_DailyVinMileageTypes)(nil),             // 35: feature_store.Catalog.DailyVinMileage_types
	(*Catalog_FleetRunRateTypes)(nil),                // 36: feature_store.Catalog.FleetRunRate_types
	(*Catalog_VinTelematicsSummaryTypes)(nil),        // 37: feature_store.Catalog.VinTelematicsSummary_types
	(*Catalog_MileageWeightedCrashFreqTRSTypes)(nil), // 38: feature_store.Catalog.MileageWeightedCrashFreqTRS_types
	(*Catalog_StateZoneMileageTypes)(nil),            // 39: feature_store.Catalog.StateZoneMileage_types
	(*Catalog_NirvanaRiskScoreTypes)(nil),            // 40: feature_store.Catalog.NirvanaRiskScore_types
	(*Catalog_NirvanaVinRiskScoreTypes)(nil),         // 41: feature_store.Catalog.NirvanaVinRiskScore_types
	(*Catalog_VinHaulClusterTagTypes)(nil),           // 42: feature_store.Catalog.VinHaulClusterTag_types
	(*Catalog_NirvanaVinRiskScoreClusterTypes)(nil),  // 43: feature_store.Catalog.NirvanaVinRiskScoreCluster_types
	nil,                                   // 44: feature_store.MockDriverScores_v1.ScoresEntry
	(*HazardStatesV1_HazardStateRow)(nil), // 45: feature_store.HazardStates_v1.HazardStateRow
	(*CarrierLoyaltyTableV1_CLDataframeRow)(nil),                         // 46: feature_store.CarrierLoyaltyTable_v1.CLDataframeRow
	(*CarrierLoyaltyTableV2_CLDataframeRow)(nil),                         // 47: feature_store.CarrierLoyaltyTable_v2.CLDataframeRow
	(*FleetRunRateV1_WRDataframeRow)(nil),                                // 48: feature_store.FleetRunRate_v1.WRDataframeRow
	(*VinTelematicsSummaryV1_VinSummaryTableRow)(nil),                    // 49: feature_store.VinTelematicsSummary_v1.VinSummaryTableRow
	(*MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow)(nil), // 50: feature_store.MileageWeightedCrashFreqTRS_v1.MileageWeightedCrashFreqTRSRow
	(*StateZoneMileageV1_StateZoneMileageRow)(nil),                       // 51: feature_store.StateZoneMileage_v1.StateZoneMileageRow
	(*NirvanaRiskScoreV1_RiskScoreTrendRow)(nil),                         // 52: feature_store.NirvanaRiskScore_v1.RiskScoreTrendRow
	(*NirvanaRiskScoreV1_UWRubricRow)(nil),                               // 53: feature_store.NirvanaRiskScore_v1.UWRubricRow
	(*NirvanaRiskScoreV2_RiskScoreTrendRow)(nil),                         // 54: feature_store.NirvanaRiskScore_v2.RiskScoreTrendRow
	(*NirvanaRiskScoreV2_UWRubricRow)(nil),                               // 55: feature_store.NirvanaRiskScore_v2.UWRubricRow
	(*NirvanaRiskScoreV3_RiskScoreTrendRow)(nil),                         // 56: feature_store.NirvanaRiskScore_v3.RiskScoreTrendRow
	(*NirvanaRiskScoreV3_UWRubricRow)(nil),                               // 57: feature_store.NirvanaRiskScore_v3.UWRubricRow
	(*NirvanaRiskScoreV4_RiskScoreTrendRow)(nil),                         // 58: feature_store.NirvanaRiskScore_v4.RiskScoreTrendRow
	(*NirvanaRiskScoreV4_UWRubricRow)(nil),                               // 59: feature_store.NirvanaRiskScore_v4.UWRubricRow
	(*NirvanaRiskScoreV5_RiskScoreTrendRow)(nil),                         // 60: feature_store.NirvanaRiskScore_v5.RiskScoreTrendRow
	(*NirvanaRiskScoreV5_UWRubricRow)(nil),                               // 61: feature_store.NirvanaRiskScore_v5.UWRubricRow
	(*NirvanaVinRiskScoreV1_VinRiskScoreTrendRow)(nil),                   // 62: feature_store.NirvanaVinRiskScore_v1.VinRiskScoreTrendRow
	(*VinHaulClusterTagV1_VinHaulClusterTrendRow)(nil),                   // 63: feature_store.VinHaulClusterTag_v1.VinHaulClusterTrendRow
	(*NirvanaVinRiskScoreClusterV1VinClustersTrendRow)(nil),              // 64: feature_store.NirvanaVinRiskScoreCluster_v1.vinClustersTrendRow
	(*NirvanaVinRiskScoreClusterV2VinClustersTrendRow)(nil),              // 65: feature_store.NirvanaVinRiskScoreCluster_v2.vinClustersTrendRow
	(*Interval)(nil),              // 66: feature_store.Interval
	(*MockDriverScore)(nil),       // 67: feature_store.MockDriverScore
	(*timestamppb.Timestamp)(nil), // 68: google.protobuf.Timestamp
}
var file_feature_store_definitions_proto_depIdxs = []int32{
	28, // 0: feature_store.Catalog.MockDriverScores:type_name -> feature_store.Catalog.MockDriverScores_types
	29, // 1: feature_store.Catalog.HazardDistance:type_name -> feature_store.Catalog.HazardDistance_types
	30, // 2: feature_store.Catalog.HazardDuration:type_name -> feature_store.Catalog.HazardDuration_types
	31, // 3: feature_store.Catalog.HazardStates:type_name -> feature_store.Catalog.HazardStates_types
	32, // 4: feature_store.Catalog.CarrierLoyaltyTable:type_name -> feature_store.Catalog.CarrierLoyaltyTable_types
	33, // 5: feature_store.Catalog.CarrierLoyaltyInsight:type_name -> feature_store.Catalog.CarrierLoyaltyInsight_types
	34, // 6: feature_store.Catalog.BucketRadiusOfOperations:type_name -> feature_store.Catalog.BucketRadiusOfOperations_types
	35, // 7: feature_store.Catalog.DailyVinMileage:type_name -> feature_store.Catalog.DailyVinMileage_types
	36, // 8: feature_store.Catalog.FleetRunRate:type_name -> feature_store.Catalog.FleetRunRate_types
	37, // 9: feature_store.Catalog.VinTelematicsSummary:type_name -> feature_store.Catalog.VinTelematicsSummary_types
	38, // 10: feature_store.Catalog.MileageWeightedCrashFreqTRS:type_name -> feature_store.Catalog.MileageWeightedCrashFreqTRS_types
	39, // 11: feature_store.Catalog.StateZoneMileage:type_name -> feature_store.Catalog.StateZoneMileage_types
	40, // 12: feature_store.Catalog.NirvanaRiskScore:type_name -> feature_store.Catalog.NirvanaRiskScore_types
	41, // 13: feature_store.Catalog.NirvanaVinRiskScore:type_name -> feature_store.Catalog.NirvanaVinRiskScore_types
	42, // 14: feature_store.Catalog.VinHaulClusterTag:type_name -> feature_store.Catalog.VinHaulClusterTag_types
	43, // 15: feature_store.Catalog.NirvanaVinRiskScoreCluster:type_name -> feature_store.Catalog.NirvanaVinRiskScoreCluster_types
	66, // 16: feature_store.MockDriverScores_v0.interval:type_name -> feature_store.Interval
	67, // 17: feature_store.MockDriverScores_v0.scores:type_name -> feature_store.MockDriverScore
	66, // 18: feature_store.MockDriverScores_v1.interval:type_name -> feature_store.Interval
	44, // 19: feature_store.MockDriverScores_v1.scores:type_name -> feature_store.MockDriverScores_v1.ScoresEntry
	68, // 20: feature_store.HazardDistance_v1.created_at:type_name -> google.protobuf.Timestamp
	68, // 21: feature_store.HazardDuration_v1.created_at:type_name -> google.protobuf.Timestamp
	68, // 22: feature_store.HazardDuration_v2.created_at:type_name -> google.protobuf.Timestamp
	45, // 23: feature_store.HazardStates_v1.hazard_state_table:type_name -> feature_store.HazardStates_v1.HazardStateRow
	68, // 24: feature_store.HazardStates_v1.created_at:type_name -> google.protobuf.Timestamp
	46, // 25: feature_store.CarrierLoyaltyTable_v1.carrier_loyalty_table:type_name -> feature_store.CarrierLoyaltyTable_v1.CLDataframeRow
	68, // 26: feature_store.CarrierLoyaltyTable_v1.created_at:type_name -> google.protobuf.Timestamp
	47, // 27: feature_store.CarrierLoyaltyTable_v2.carrier_loyalty_summary_table:type_name -> feature_store.CarrierLoyaltyTable_v2.CLDataframeRow
	68, // 28: feature_store.CarrierLoyaltyTable_v2.created_at:type_name -> google.protobuf.Timestamp
	68, // 29: feature_store.CarrierLoyaltyInsight_v1.created_at:type_name -> google.protobuf.Timestamp
	68, // 30: feature_store.CarrierLoyaltyInsight_v2.created_at:type_name -> google.protobuf.Timestamp
	68, // 31: feature_store.BucketRadiusOfOperations_v1.created_at:type_name -> google.protobuf.Timestamp
	68, // 32: feature_store.DailyVinMileage_v1.created_at:type_name -> google.protobuf.Timestamp
	48, // 33: feature_store.FleetRunRate_v1.weekly_fleet_run_rate_table:type_name -> feature_store.FleetRunRate_v1.WRDataframeRow
	68, // 34: feature_store.FleetRunRate_v1.created_at:type_name -> google.protobuf.Timestamp
	49, // 35: feature_store.VinTelematicsSummary_v1.vin_telematics_summary_table:type_name -> feature_store.VinTelematicsSummary_v1.VinSummaryTableRow
	68, // 36: feature_store.VinTelematicsSummary_v1.created_at:type_name -> google.protobuf.Timestamp
	50, // 37: feature_store.MileageWeightedCrashFreqTRS_v1.MileageWeightedCrashFreqTRSTable:type_name -> feature_store.MileageWeightedCrashFreqTRS_v1.MileageWeightedCrashFreqTRSRow
	68, // 38: feature_store.MileageWeightedCrashFreqTRS_v1.created_at:type_name -> google.protobuf.Timestamp
	51, // 39: feature_store.StateZoneMileage_v1.StateZoneMileageTable:type_name -> feature_store.StateZoneMileage_v1.StateZoneMileageRow
	68, // 40: feature_store.StateZoneMileage_v1.created_at:type_name -> google.protobuf.Timestamp
	52, // 41: feature_store.NirvanaRiskScore_v1.riskScoreTrend:type_name -> feature_store.NirvanaRiskScore_v1.RiskScoreTrendRow
	53, // 42: feature_store.NirvanaRiskScore_v1.UWRubric:type_name -> feature_store.NirvanaRiskScore_v1.UWRubricRow
	68, // 43: feature_store.NirvanaRiskScore_v1.created_at:type_name -> google.protobuf.Timestamp
	54, // 44: feature_store.NirvanaRiskScore_v2.riskScoreTrend:type_name -> feature_store.NirvanaRiskScore_v2.RiskScoreTrendRow
	55, // 45: feature_store.NirvanaRiskScore_v2.UWRubric:type_name -> feature_store.NirvanaRiskScore_v2.UWRubricRow
	68, // 46: feature_store.NirvanaRiskScore_v2.created_at:type_name -> google.protobuf.Timestamp
	56, // 47: feature_store.NirvanaRiskScore_v3.riskScoreTrend:type_name -> feature_store.NirvanaRiskScore_v3.RiskScoreTrendRow
	57, // 48: feature_store.NirvanaRiskScore_v3.UWRubric:type_name -> feature_store.NirvanaRiskScore_v3.UWRubricRow
	68, // 49: feature_store.NirvanaRiskScore_v3.created_at:type_name -> google.protobuf.Timestamp
	58, // 50: feature_store.NirvanaRiskScore_v4.riskScoreTrend:type_name -> feature_store.NirvanaRiskScore_v4.RiskScoreTrendRow
	59, // 51: feature_store.NirvanaRiskScore_v4.UWRubric:type_name -> feature_store.NirvanaRiskScore_v4.UWRubricRow
	68, // 52: feature_store.NirvanaRiskScore_v4.created_at:type_name -> google.protobuf.Timestamp
	60, // 53: feature_store.NirvanaRiskScore_v5.riskScoreTrend:type_name -> feature_store.NirvanaRiskScore_v5.RiskScoreTrendRow
	61, // 54: feature_store.NirvanaRiskScore_v5.UWRubric:type_name -> feature_store.NirvanaRiskScore_v5.UWRubricRow
	68, // 55: feature_store.NirvanaRiskScore_v5.created_at:type_name -> google.protobuf.Timestamp
	62, // 56: feature_store.NirvanaVinRiskScore_v1.vinRiskScoreTrend:type_name -> feature_store.NirvanaVinRiskScore_v1.VinRiskScoreTrendRow
	68, // 57: feature_store.NirvanaVinRiskScore_v1.createdAt:type_name -> google.protobuf.Timestamp
	63, // 58: feature_store.VinHaulClusterTag_v1.vinHaulClusterTrend:type_name -> feature_store.VinHaulClusterTag_v1.VinHaulClusterTrendRow
	68, // 59: feature_store.VinHaulClusterTag_v1.createdAt:type_name -> google.protobuf.Timestamp
	64, // 60: feature_store.NirvanaVinRiskScoreCluster_v1.vinClustersTrend:type_name -> feature_store.NirvanaVinRiskScoreCluster_v1.vinClustersTrendRow
	68, // 61: feature_store.NirvanaVinRiskScoreCluster_v1.createdAt:type_name -> google.protobuf.Timestamp
	65, // 62: feature_store.NirvanaVinRiskScoreCluster_v2.vinClustersTrend:type_name -> feature_store.NirvanaVinRiskScoreCluster_v2.vinClustersTrendRow
	68, // 63: feature_store.NirvanaVinRiskScoreCluster_v2.createdAt:type_name -> google.protobuf.Timestamp
	3,  // 64: feature_store.Catalog.MockDriverScores_types.v0:type_name -> feature_store.MockDriverScores_v0
	4,  // 65: feature_store.Catalog.MockDriverScores_types.v1:type_name -> feature_store.MockDriverScores_v1
	3,  // 66: feature_store.Catalog.MockDriverScores_types.v2:type_name -> feature_store.MockDriverScores_v0
	5,  // 67: feature_store.Catalog.HazardDistance_types.v1:type_name -> feature_store.HazardDistance_v1
	6,  // 68: feature_store.Catalog.HazardDuration_types.v1:type_name -> feature_store.HazardDuration_v1
	7,  // 69: feature_store.Catalog.HazardDuration_types.v2:type_name -> feature_store.HazardDuration_v2
	8,  // 70: feature_store.Catalog.HazardStates_types.v1:type_name -> feature_store.HazardStates_v1
	9,  // 71: feature_store.Catalog.CarrierLoyaltyTable_types.v1:type_name -> feature_store.CarrierLoyaltyTable_v1
	10, // 72: feature_store.Catalog.CarrierLoyaltyTable_types.v2:type_name -> feature_store.CarrierLoyaltyTable_v2
	11, // 73: feature_store.Catalog.CarrierLoyaltyInsight_types.v1:type_name -> feature_store.CarrierLoyaltyInsight_v1
	12, // 74: feature_store.Catalog.CarrierLoyaltyInsight_types.v2:type_name -> feature_store.CarrierLoyaltyInsight_v2
	13, // 75: feature_store.Catalog.BucketRadiusOfOperations_types.v1:type_name -> feature_store.BucketRadiusOfOperations_v1
	14, // 76: feature_store.Catalog.DailyVinMileage_types.v1:type_name -> feature_store.DailyVinMileage_v1
	15, // 77: feature_store.Catalog.FleetRunRate_types.v1:type_name -> feature_store.FleetRunRate_v1
	16, // 78: feature_store.Catalog.VinTelematicsSummary_types.v1:type_name -> feature_store.VinTelematicsSummary_v1
	17, // 79: feature_store.Catalog.MileageWeightedCrashFreqTRS_types.v1:type_name -> feature_store.MileageWeightedCrashFreqTRS_v1
	18, // 80: feature_store.Catalog.StateZoneMileage_types.v1:type_name -> feature_store.StateZoneMileage_v1
	19, // 81: feature_store.Catalog.NirvanaRiskScore_types.v1:type_name -> feature_store.NirvanaRiskScore_v1
	20, // 82: feature_store.Catalog.NirvanaRiskScore_types.v2:type_name -> feature_store.NirvanaRiskScore_v2
	21, // 83: feature_store.Catalog.NirvanaRiskScore_types.v3:type_name -> feature_store.NirvanaRiskScore_v3
	22, // 84: feature_store.Catalog.NirvanaRiskScore_types.v4:type_name -> feature_store.NirvanaRiskScore_v4
	23, // 85: feature_store.Catalog.NirvanaRiskScore_types.v5:type_name -> feature_store.NirvanaRiskScore_v5
	24, // 86: feature_store.Catalog.NirvanaVinRiskScore_types.v1:type_name -> feature_store.NirvanaVinRiskScore_v1
	25, // 87: feature_store.Catalog.VinHaulClusterTag_types.v1:type_name -> feature_store.VinHaulClusterTag_v1
	26, // 88: feature_store.Catalog.NirvanaVinRiskScoreCluster_types.v1:type_name -> feature_store.NirvanaVinRiskScoreCluster_v1
	27, // 89: feature_store.Catalog.NirvanaVinRiskScoreCluster_types.v2:type_name -> feature_store.NirvanaVinRiskScoreCluster_v2
	67, // 90: feature_store.MockDriverScores_v1.ScoresEntry.value:type_name -> feature_store.MockDriverScore
	1,  // 91: feature_store.CarrierLoyaltyTable_v1.CLDataframeRow.duration_type:type_name -> feature_store.CarrierLoyaltyTable_v1.DurationType
	92, // [92:92] is the sub-list for method output_type
	92, // [92:92] is the sub-list for method input_type
	92, // [92:92] is the sub-list for extension type_name
	92, // [92:92] is the sub-list for extension extendee
	0,  // [0:92] is the sub-list for field type_name
}

func init() { file_feature_store_definitions_proto_init() }
func file_feature_store_definitions_proto_init() {
	if File_feature_store_definitions_proto != nil {
		return
	}
	file_feature_store_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_feature_store_definitions_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MockDriverScoresV0); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MockDriverScoresV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HazardDistanceV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HazardDurationV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HazardDurationV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HazardStatesV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarrierLoyaltyTableV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarrierLoyaltyTableV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarrierLoyaltyInsightV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarrierLoyaltyInsightV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BucketRadiusOfOperationsV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyVinMileageV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetRunRateV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VinTelematicsSummaryV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MileageWeightedCrashFreqTRSV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StateZoneMileageV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV4); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV5); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaVinRiskScoreV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VinHaulClusterTagV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaVinRiskScoreClusterV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaVinRiskScoreClusterV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_MockDriverScoresTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_HazardDistanceTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_HazardDurationTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_HazardStatesTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_CarrierLoyaltyTableTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_CarrierLoyaltyInsightTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_BucketRadiusOfOperationsTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_DailyVinMileageTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_FleetRunRateTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_VinTelematicsSummaryTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_MileageWeightedCrashFreqTRSTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_StateZoneMileageTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_NirvanaRiskScoreTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_NirvanaVinRiskScoreTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_VinHaulClusterTagTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Catalog_NirvanaVinRiskScoreClusterTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HazardStatesV1_HazardStateRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarrierLoyaltyTableV1_CLDataframeRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarrierLoyaltyTableV2_CLDataframeRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetRunRateV1_WRDataframeRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VinTelematicsSummaryV1_VinSummaryTableRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MileageWeightedCrashFreqTRSV1_MileageWeightedCrashFreqTRSRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StateZoneMileageV1_StateZoneMileageRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV1_RiskScoreTrendRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV1_UWRubricRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV2_RiskScoreTrendRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV2_UWRubricRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV3_RiskScoreTrendRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV3_UWRubricRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV4_RiskScoreTrendRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV4_UWRubricRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV5_RiskScoreTrendRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaRiskScoreV5_UWRubricRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaVinRiskScoreV1_VinRiskScoreTrendRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VinHaulClusterTagV1_VinHaulClusterTrendRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaVinRiskScoreClusterV1VinClustersTrendRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_definitions_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaVinRiskScoreClusterV2VinClustersTrendRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_feature_store_definitions_proto_msgTypes[46].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[48].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[49].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[50].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[51].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[52].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[53].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[54].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[55].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[56].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[57].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[58].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[59].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[60].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[61].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[62].OneofWrappers = []interface{}{}
	file_feature_store_definitions_proto_msgTypes[63].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_feature_store_definitions_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   64,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_feature_store_definitions_proto_goTypes,
		DependencyIndexes: file_feature_store_definitions_proto_depIdxs,
		EnumInfos:         file_feature_store_definitions_proto_enumTypes,
		MessageInfos:      file_feature_store_definitions_proto_msgTypes,
	}.Build()
	File_feature_store_definitions_proto = out.File
	file_feature_store_definitions_proto_rawDesc = nil
	file_feature_store_definitions_proto_goTypes = nil
	file_feature_store_definitions_proto_depIdxs = nil
}
