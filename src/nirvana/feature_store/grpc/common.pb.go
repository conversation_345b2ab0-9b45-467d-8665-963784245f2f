// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: feature_store/common.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Interval struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	End   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *Interval) Reset() {
	*x = Interval{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Interval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Interval) ProtoMessage() {}

func (x *Interval) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Interval.ProtoReflect.Descriptor instead.
func (*Interval) Descriptor() ([]byte, []int) {
	return file_feature_store_common_proto_rawDescGZIP(), []int{0}
}

func (x *Interval) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *Interval) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

type MockDriver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dl        string `protobuf:"bytes,1,opt,name=dl,proto3" json:"dl,omitempty"`
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName  string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *MockDriver) Reset() {
	*x = MockDriver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MockDriver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MockDriver) ProtoMessage() {}

func (x *MockDriver) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MockDriver.ProtoReflect.Descriptor instead.
func (*MockDriver) Descriptor() ([]byte, []int) {
	return file_feature_store_common_proto_rawDescGZIP(), []int{1}
}

func (x *MockDriver) GetDl() string {
	if x != nil {
		return x.Dl
	}
	return ""
}

func (x *MockDriver) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *MockDriver) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

type MockDriverScore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Driver *MockDriver `protobuf:"bytes,1,opt,name=driver,proto3" json:"driver,omitempty"`
	Score  int32       `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *MockDriverScore) Reset() {
	*x = MockDriverScore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MockDriverScore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MockDriverScore) ProtoMessage() {}

func (x *MockDriverScore) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MockDriverScore.ProtoReflect.Descriptor instead.
func (*MockDriverScore) Descriptor() ([]byte, []int) {
	return file_feature_store_common_proto_rawDescGZIP(), []int{2}
}

func (x *MockDriverScore) GetDriver() *MockDriver {
	if x != nil {
		return x.Driver
	}
	return nil
}

func (x *MockDriverScore) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

var File_feature_store_common_proto protoreflect.FileDescriptor

var file_feature_store_common_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6a, 0x0a, 0x08,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x2c, 0x0a, 0x03, 0x65, 0x6e,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0x58, 0x0a, 0x0a, 0x4d, 0x6f, 0x63, 0x6b,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x64, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x5a, 0x0a, 0x0f, 0x4d, 0x6f, 0x63, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4d, 0x6f, 0x63, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x52, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_feature_store_common_proto_rawDescOnce sync.Once
	file_feature_store_common_proto_rawDescData = file_feature_store_common_proto_rawDesc
)

func file_feature_store_common_proto_rawDescGZIP() []byte {
	file_feature_store_common_proto_rawDescOnce.Do(func() {
		file_feature_store_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_feature_store_common_proto_rawDescData)
	})
	return file_feature_store_common_proto_rawDescData
}

var file_feature_store_common_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_feature_store_common_proto_goTypes = []interface{}{
	(*Interval)(nil),              // 0: feature_store.Interval
	(*MockDriver)(nil),            // 1: feature_store.MockDriver
	(*MockDriverScore)(nil),       // 2: feature_store.MockDriverScore
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_feature_store_common_proto_depIdxs = []int32{
	3, // 0: feature_store.Interval.start:type_name -> google.protobuf.Timestamp
	3, // 1: feature_store.Interval.end:type_name -> google.protobuf.Timestamp
	1, // 2: feature_store.MockDriverScore.driver:type_name -> feature_store.MockDriver
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_feature_store_common_proto_init() }
func file_feature_store_common_proto_init() {
	if File_feature_store_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_feature_store_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Interval); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MockDriver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MockDriverScore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_feature_store_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_feature_store_common_proto_goTypes,
		DependencyIndexes: file_feature_store_common_proto_depIdxs,
		MessageInfos:      file_feature_store_common_proto_msgTypes,
	}.Build()
	File_feature_store_common_proto = out.File
	file_feature_store_common_proto_rawDesc = nil
	file_feature_store_common_proto_goTypes = nil
	file_feature_store_common_proto_depIdxs = nil
}
