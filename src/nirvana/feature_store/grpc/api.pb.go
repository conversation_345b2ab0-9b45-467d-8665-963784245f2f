// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: feature_store/api.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IDType int32

const (
	IDType_HandleId  IDType = 0
	IDType_AppId     IDType = 1
	IDType_DotNumber IDType = 2
)

// Enum value maps for IDType.
var (
	IDType_name = map[int32]string{
		0: "HandleId",
		1: "AppId",
		2: "DotNumber",
	}
	IDType_value = map[string]int32{
		"HandleId":  0,
		"AppId":     1,
		"DotNumber": 2,
	}
)

func (x IDType) Enum() *IDType {
	p := new(IDType)
	*p = x
	return p
}

func (x IDType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IDType) Descriptor() protoreflect.EnumDescriptor {
	return file_feature_store_api_proto_enumTypes[0].Descriptor()
}

func (IDType) Type() protoreflect.EnumType {
	return &file_feature_store_api_proto_enumTypes[0]
}

func (x IDType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IDType.Descriptor instead.
func (IDType) EnumDescriptor() ([]byte, []int) {
	return file_feature_store_api_proto_rawDescGZIP(), []int{0}
}

type Query_Filter_Operator int32

const (
	Query_Filter_Equals      Query_Filter_Operator = 0
	Query_Filter_LessThan    Query_Filter_Operator = 1
	Query_Filter_GreaterThan Query_Filter_Operator = 2
)

// Enum value maps for Query_Filter_Operator.
var (
	Query_Filter_Operator_name = map[int32]string{
		0: "Equals",
		1: "LessThan",
		2: "GreaterThan",
	}
	Query_Filter_Operator_value = map[string]int32{
		"Equals":      0,
		"LessThan":    1,
		"GreaterThan": 2,
	}
)

func (x Query_Filter_Operator) Enum() *Query_Filter_Operator {
	p := new(Query_Filter_Operator)
	*p = x
	return p
}

func (x Query_Filter_Operator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Query_Filter_Operator) Descriptor() protoreflect.EnumDescriptor {
	return file_feature_store_api_proto_enumTypes[1].Descriptor()
}

func (Query_Filter_Operator) Type() protoreflect.EnumType {
	return &file_feature_store_api_proto_enumTypes[1]
}

func (x Query_Filter_Operator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Query_Filter_Operator.Descriptor instead.
func (Query_Filter_Operator) EnumDescriptor() ([]byte, []int) {
	return file_feature_store_api_proto_rawDescGZIP(), []int{2, 0, 0}
}

type Key struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FeatureName FeatureName            `protobuf:"varint,2,opt,name=feature_name,json=featureName,proto3,enum=feature_store.FeatureName" json:"feature_name,omitempty"`
	Version     string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	Timestamp   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Metadata    map[string]string      `protobuf:"bytes,5,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IdType      IDType                 `protobuf:"varint,6,opt,name=id_type,json=idType,proto3,enum=feature_store.IDType" json:"id_type,omitempty"`
}

func (x *Key) Reset() {
	*x = Key{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Key) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Key) ProtoMessage() {}

func (x *Key) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Key.ProtoReflect.Descriptor instead.
func (*Key) Descriptor() ([]byte, []int) {
	return file_feature_store_api_proto_rawDescGZIP(), []int{0}
}

func (x *Key) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Key) GetFeatureName() FeatureName {
	if x != nil {
		return x.FeatureName
	}
	return FeatureName_Unknown
}

func (x *Key) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Key) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *Key) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *Key) GetIdType() IDType {
	if x != nil {
		return x.IdType
	}
	return IDType_HandleId
}

type StoreRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   *Key       `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value *anypb.Any `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *StoreRequest) Reset() {
	*x = StoreRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreRequest) ProtoMessage() {}

func (x *StoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreRequest.ProtoReflect.Descriptor instead.
func (*StoreRequest) Descriptor() ([]byte, []int) {
	return file_feature_store_api_proto_rawDescGZIP(), []int{1}
}

func (x *StoreRequest) GetKey() *Key {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *StoreRequest) GetValue() *anypb.Any {
	if x != nil {
		return x.Value
	}
	return nil
}

type Query struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FeatureName FeatureName     `protobuf:"varint,2,opt,name=feature_name,json=featureName,proto3,enum=feature_store.FeatureName" json:"feature_name,omitempty"`
	Version     string          `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	Filters     []*Query_Filter `protobuf:"bytes,4,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *Query) Reset() {
	*x = Query{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Query) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Query) ProtoMessage() {}

func (x *Query) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Query.ProtoReflect.Descriptor instead.
func (*Query) Descriptor() ([]byte, []int) {
	return file_feature_store_api_proto_rawDescGZIP(), []int{2}
}

func (x *Query) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Query) GetFeatureName() FeatureName {
	if x != nil {
		return x.FeatureName
	}
	return FeatureName_Unknown
}

func (x *Query) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Query) GetFilters() []*Query_Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

type Query_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Field string                `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Value string                `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Op    Query_Filter_Operator `protobuf:"varint,3,opt,name=op,proto3,enum=feature_store.Query_Filter_Operator" json:"op,omitempty"`
}

func (x *Query_Filter) Reset() {
	*x = Query_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_feature_store_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Query_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Query_Filter) ProtoMessage() {}

func (x *Query_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_feature_store_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Query_Filter.ProtoReflect.Descriptor instead.
func (*Query_Filter) Descriptor() ([]byte, []int) {
	return file_feature_store_api_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Query_Filter) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *Query_Filter) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Query_Filter) GetOp() Query_Filter_Operator {
	if x != nil {
		return x.Op
	}
	return Query_Filter_Equals
}

var File_feature_store_api_proto protoreflect.FileDescriptor

var file_feature_store_api_proto_rawDesc = []byte{
	0x0a, 0x17, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2f, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd3, 0x02, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3d, 0x0a,
	0x0c, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x0b, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x3c, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x4b, 0x65, 0x79, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2e,
	0x0a, 0x07, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x49, 0x44, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x3b,
	0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x60, 0x0a, 0x0c, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xcb, 0x02,
	0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x35, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x1a, 0xa1, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x34,
	0x0a, 0x02, 0x6f, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x52, 0x02, 0x6f, 0x70, 0x22, 0x35, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x12, 0x0a, 0x0a, 0x06, 0x45, 0x71, 0x75, 0x61, 0x6c, 0x73, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x4c, 0x65, 0x73, 0x73, 0x54, 0x68, 0x61, 0x6e, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x47, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x72, 0x54, 0x68, 0x61, 0x6e, 0x10, 0x02, 0x2a, 0x30, 0x0a, 0x06, 0x49,
	0x44, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49,
	0x64, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x64, 0x10, 0x01, 0x12, 0x0d,
	0x0a, 0x09, 0x44, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x10, 0x02, 0x32, 0xb2, 0x01,
	0x0a, 0x0c, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x38,
	0x0a, 0x05, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x1b, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x4b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x03, 0x47, 0x65, 0x74, 0x12,
	0x12, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x4b, 0x65, 0x79, 0x1a, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x12, 0x37, 0x0a, 0x09, 0x47, 0x65, 0x74,
	0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x12, 0x14, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x1a, 0x14, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41,
	0x6e, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_feature_store_api_proto_rawDescOnce sync.Once
	file_feature_store_api_proto_rawDescData = file_feature_store_api_proto_rawDesc
)

func file_feature_store_api_proto_rawDescGZIP() []byte {
	file_feature_store_api_proto_rawDescOnce.Do(func() {
		file_feature_store_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_feature_store_api_proto_rawDescData)
	})
	return file_feature_store_api_proto_rawDescData
}

var file_feature_store_api_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_feature_store_api_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_feature_store_api_proto_goTypes = []interface{}{
	(IDType)(0),                   // 0: feature_store.IDType
	(Query_Filter_Operator)(0),    // 1: feature_store.Query.Filter.Operator
	(*Key)(nil),                   // 2: feature_store.Key
	(*StoreRequest)(nil),          // 3: feature_store.StoreRequest
	(*Query)(nil),                 // 4: feature_store.Query
	nil,                           // 5: feature_store.Key.MetadataEntry
	(*Query_Filter)(nil),          // 6: feature_store.Query.Filter
	(FeatureName)(0),              // 7: feature_store.FeatureName
	(*timestamppb.Timestamp)(nil), // 8: google.protobuf.Timestamp
	(*anypb.Any)(nil),             // 9: google.protobuf.Any
}
var file_feature_store_api_proto_depIdxs = []int32{
	7,  // 0: feature_store.Key.feature_name:type_name -> feature_store.FeatureName
	8,  // 1: feature_store.Key.timestamp:type_name -> google.protobuf.Timestamp
	5,  // 2: feature_store.Key.metadata:type_name -> feature_store.Key.MetadataEntry
	0,  // 3: feature_store.Key.id_type:type_name -> feature_store.IDType
	2,  // 4: feature_store.StoreRequest.key:type_name -> feature_store.Key
	9,  // 5: feature_store.StoreRequest.value:type_name -> google.protobuf.Any
	7,  // 6: feature_store.Query.feature_name:type_name -> feature_store.FeatureName
	6,  // 7: feature_store.Query.filters:type_name -> feature_store.Query.Filter
	1,  // 8: feature_store.Query.Filter.op:type_name -> feature_store.Query.Filter.Operator
	3,  // 9: feature_store.FeatureStore.Store:input_type -> feature_store.StoreRequest
	2,  // 10: feature_store.FeatureStore.Get:input_type -> feature_store.Key
	4,  // 11: feature_store.FeatureStore.GetLatest:input_type -> feature_store.Query
	2,  // 12: feature_store.FeatureStore.Store:output_type -> feature_store.Key
	9,  // 13: feature_store.FeatureStore.Get:output_type -> google.protobuf.Any
	9,  // 14: feature_store.FeatureStore.GetLatest:output_type -> google.protobuf.Any
	12, // [12:15] is the sub-list for method output_type
	9,  // [9:12] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_feature_store_api_proto_init() }
func file_feature_store_api_proto_init() {
	if File_feature_store_api_proto != nil {
		return
	}
	file_feature_store_definitions_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_feature_store_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Key); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Query); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_feature_store_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Query_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_feature_store_api_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_feature_store_api_proto_goTypes,
		DependencyIndexes: file_feature_store_api_proto_depIdxs,
		EnumInfos:         file_feature_store_api_proto_enumTypes,
		MessageInfos:      file_feature_store_api_proto_msgTypes,
	}.Build()
	File_feature_store_api_proto = out.File
	file_feature_store_api_proto_rawDesc = nil
	file_feature_store_api_proto_goTypes = nil
	file_feature_store_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// FeatureStoreClient is the client API for FeatureStore service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FeatureStoreClient interface {
	Store(ctx context.Context, in *StoreRequest, opts ...grpc.CallOption) (*Key, error)
	Get(ctx context.Context, in *Key, opts ...grpc.CallOption) (*anypb.Any, error)
	GetLatest(ctx context.Context, in *Query, opts ...grpc.CallOption) (*anypb.Any, error)
}

type featureStoreClient struct {
	cc grpc.ClientConnInterface
}

func NewFeatureStoreClient(cc grpc.ClientConnInterface) FeatureStoreClient {
	return &featureStoreClient{cc}
}

func (c *featureStoreClient) Store(ctx context.Context, in *StoreRequest, opts ...grpc.CallOption) (*Key, error) {
	out := new(Key)
	err := c.cc.Invoke(ctx, "/feature_store.FeatureStore/Store", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *featureStoreClient) Get(ctx context.Context, in *Key, opts ...grpc.CallOption) (*anypb.Any, error) {
	out := new(anypb.Any)
	err := c.cc.Invoke(ctx, "/feature_store.FeatureStore/Get", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *featureStoreClient) GetLatest(ctx context.Context, in *Query, opts ...grpc.CallOption) (*anypb.Any, error) {
	out := new(anypb.Any)
	err := c.cc.Invoke(ctx, "/feature_store.FeatureStore/GetLatest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FeatureStoreServer is the server API for FeatureStore service.
type FeatureStoreServer interface {
	Store(context.Context, *StoreRequest) (*Key, error)
	Get(context.Context, *Key) (*anypb.Any, error)
	GetLatest(context.Context, *Query) (*anypb.Any, error)
}

// UnimplementedFeatureStoreServer can be embedded to have forward compatible implementations.
type UnimplementedFeatureStoreServer struct {
}

func (*UnimplementedFeatureStoreServer) Store(context.Context, *StoreRequest) (*Key, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Store not implemented")
}
func (*UnimplementedFeatureStoreServer) Get(context.Context, *Key) (*anypb.Any, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (*UnimplementedFeatureStoreServer) GetLatest(context.Context, *Query) (*anypb.Any, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatest not implemented")
}

func RegisterFeatureStoreServer(s *grpc.Server, srv FeatureStoreServer) {
	s.RegisterService(&_FeatureStore_serviceDesc, srv)
}

func _FeatureStore_Store_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeatureStoreServer).Store(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/feature_store.FeatureStore/Store",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeatureStoreServer).Store(ctx, req.(*StoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeatureStore_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Key)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeatureStoreServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/feature_store.FeatureStore/Get",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeatureStoreServer).Get(ctx, req.(*Key))
	}
	return interceptor(ctx, in, info, handler)
}

func _FeatureStore_GetLatest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Query)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeatureStoreServer).GetLatest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/feature_store.FeatureStore/GetLatest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeatureStoreServer).GetLatest(ctx, req.(*Query))
	}
	return interceptor(ctx, in, info, handler)
}

var _FeatureStore_serviceDesc = grpc.ServiceDesc{
	ServiceName: "feature_store.FeatureStore",
	HandlerType: (*FeatureStoreServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Store",
			Handler:    _FeatureStore_Store_Handler,
		},
		{
			MethodName: "Get",
			Handler:    _FeatureStore_Get_Handler,
		},
		{
			MethodName: "GetLatest",
			Handler:    _FeatureStore_GetLatest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "feature_store/api.proto",
}
