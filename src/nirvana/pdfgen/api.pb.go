// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pdfgen/api.proto

package pdfgen

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GeneratePDFRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Template      string               `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
	Data          []byte               `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	LinkExpiry    *durationpb.Duration `protobuf:"bytes,3,opt,name=linkExpiry,proto3" json:"linkExpiry,omitempty"`
	UserPassword  string               `protobuf:"bytes,4,opt,name=userPassword,proto3" json:"userPassword,omitempty"`
	OwnerPassword string               `protobuf:"bytes,5,opt,name=ownerPassword,proto3" json:"ownerPassword,omitempty"`
	FileName      string               `protobuf:"bytes,6,opt,name=fileName,proto3" json:"fileName,omitempty"`
	UseReactPdf   bool                 `protobuf:"varint,7,opt,name=useReactPdf,proto3" json:"useReactPdf,omitempty"`
}

func (x *GeneratePDFRequest) Reset() {
	*x = GeneratePDFRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pdfgen_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeneratePDFRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratePDFRequest) ProtoMessage() {}

func (x *GeneratePDFRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pdfgen_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratePDFRequest.ProtoReflect.Descriptor instead.
func (*GeneratePDFRequest) Descriptor() ([]byte, []int) {
	return file_pdfgen_api_proto_rawDescGZIP(), []int{0}
}

func (x *GeneratePDFRequest) GetTemplate() string {
	if x != nil {
		return x.Template
	}
	return ""
}

func (x *GeneratePDFRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GeneratePDFRequest) GetLinkExpiry() *durationpb.Duration {
	if x != nil {
		return x.LinkExpiry
	}
	return nil
}

func (x *GeneratePDFRequest) GetUserPassword() string {
	if x != nil {
		return x.UserPassword
	}
	return ""
}

func (x *GeneratePDFRequest) GetOwnerPassword() string {
	if x != nil {
		return x.OwnerPassword
	}
	return ""
}

func (x *GeneratePDFRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *GeneratePDFRequest) GetUseReactPdf() bool {
	if x != nil {
		return x.UseReactPdf
	}
	return false
}

type GeneratePDFResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DownloadLink string `protobuf:"bytes,1,opt,name=downloadLink,proto3" json:"downloadLink,omitempty"`
	HandleUuid   string `protobuf:"bytes,2,opt,name=handleUuid,proto3" json:"handleUuid,omitempty"`
}

func (x *GeneratePDFResponse) Reset() {
	*x = GeneratePDFResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pdfgen_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeneratePDFResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratePDFResponse) ProtoMessage() {}

func (x *GeneratePDFResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pdfgen_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratePDFResponse.ProtoReflect.Descriptor instead.
func (*GeneratePDFResponse) Descriptor() ([]byte, []int) {
	return file_pdfgen_api_proto_rawDescGZIP(), []int{1}
}

func (x *GeneratePDFResponse) GetDownloadLink() string {
	if x != nil {
		return x.DownloadLink
	}
	return ""
}

func (x *GeneratePDFResponse) GetHandleUuid() string {
	if x != nil {
		return x.HandleUuid
	}
	return ""
}

var File_pdfgen_api_proto protoreflect.FileDescriptor

var file_pdfgen_api_proto_rawDesc = []byte{
	0x0a, 0x10, 0x70, 0x64, 0x66, 0x67, 0x65, 0x6e, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x06, 0x70, 0x64, 0x66, 0x67, 0x65, 0x6e, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x02, 0x0a, 0x12, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x44, 0x46, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x39, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x6b, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x6c, 0x69, 0x6e, 0x6b, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x12, 0x22, 0x0a, 0x0c,
	0x75, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x12, 0x24, 0x0a, 0x0d, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x52, 0x65, 0x61, 0x63, 0x74, 0x50, 0x64,
	0x66, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x52, 0x65, 0x61, 0x63,
	0x74, 0x50, 0x64, 0x66, 0x22, 0x59, 0x0a, 0x13, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x50, 0x44, 0x46, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x12,
	0x1e, 0x0a, 0x0a, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x55, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x55, 0x75, 0x69, 0x64, 0x32,
	0x4f, 0x0a, 0x06, 0x50, 0x44, 0x46, 0x47, 0x65, 0x6e, 0x12, 0x45, 0x0a, 0x08, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x2e, 0x70, 0x64, 0x66, 0x67, 0x65, 0x6e, 0x2e, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x44, 0x46, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x64, 0x66, 0x67, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x50, 0x44, 0x46, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pdfgen_api_proto_rawDescOnce sync.Once
	file_pdfgen_api_proto_rawDescData = file_pdfgen_api_proto_rawDesc
)

func file_pdfgen_api_proto_rawDescGZIP() []byte {
	file_pdfgen_api_proto_rawDescOnce.Do(func() {
		file_pdfgen_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pdfgen_api_proto_rawDescData)
	})
	return file_pdfgen_api_proto_rawDescData
}

var file_pdfgen_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pdfgen_api_proto_goTypes = []interface{}{
	(*GeneratePDFRequest)(nil),  // 0: pdfgen.GeneratePDFRequest
	(*GeneratePDFResponse)(nil), // 1: pdfgen.GeneratePDFResponse
	(*durationpb.Duration)(nil), // 2: google.protobuf.Duration
}
var file_pdfgen_api_proto_depIdxs = []int32{
	2, // 0: pdfgen.GeneratePDFRequest.linkExpiry:type_name -> google.protobuf.Duration
	0, // 1: pdfgen.PDFGen.Generate:input_type -> pdfgen.GeneratePDFRequest
	1, // 2: pdfgen.PDFGen.Generate:output_type -> pdfgen.GeneratePDFResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pdfgen_api_proto_init() }
func file_pdfgen_api_proto_init() {
	if File_pdfgen_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pdfgen_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeneratePDFRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pdfgen_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeneratePDFResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pdfgen_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pdfgen_api_proto_goTypes,
		DependencyIndexes: file_pdfgen_api_proto_depIdxs,
		MessageInfos:      file_pdfgen_api_proto_msgTypes,
	}.Build()
	File_pdfgen_api_proto = out.File
	file_pdfgen_api_proto_rawDesc = nil
	file_pdfgen_api_proto_goTypes = nil
	file_pdfgen_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// PDFGenClient is the client API for PDFGen service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PDFGenClient interface {
	Generate(ctx context.Context, in *GeneratePDFRequest, opts ...grpc.CallOption) (*GeneratePDFResponse, error)
}

type pDFGenClient struct {
	cc grpc.ClientConnInterface
}

func NewPDFGenClient(cc grpc.ClientConnInterface) PDFGenClient {
	return &pDFGenClient{cc}
}

func (c *pDFGenClient) Generate(ctx context.Context, in *GeneratePDFRequest, opts ...grpc.CallOption) (*GeneratePDFResponse, error) {
	out := new(GeneratePDFResponse)
	err := c.cc.Invoke(ctx, "/pdfgen.PDFGen/Generate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PDFGenServer is the server API for PDFGen service.
type PDFGenServer interface {
	Generate(context.Context, *GeneratePDFRequest) (*GeneratePDFResponse, error)
}

// UnimplementedPDFGenServer can be embedded to have forward compatible implementations.
type UnimplementedPDFGenServer struct {
}

func (*UnimplementedPDFGenServer) Generate(context.Context, *GeneratePDFRequest) (*GeneratePDFResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Generate not implemented")
}

func RegisterPDFGenServer(s *grpc.Server, srv PDFGenServer) {
	s.RegisterService(&_PDFGen_serviceDesc, srv)
}

func _PDFGen_Generate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GeneratePDFRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PDFGenServer).Generate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pdfgen.PDFGen/Generate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PDFGenServer).Generate(ctx, req.(*GeneratePDFRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PDFGen_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pdfgen.PDFGen",
	HandlerType: (*PDFGenServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Generate",
			Handler:    _PDFGen_Generate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pdfgen/api.proto",
}
