import { StyleSheet, Text, View } from '@react-pdf/renderer';
import { ViewProps } from '@react-pdf/types';
import { color, typography } from 'src/theme';
import Branding, { BrandingType } from './branding';

// Create styles
const styles = StyleSheet.create({
  header: {
    ...color.bgBlueGrey,
    position: 'absolute',
    height: '55mm',
    zIndex: 2,
    top: 0,
    left: 0,
    right: 0,
    paddingHorizontal: '22mm',
    paddingTop: '12mm',
    display: 'flex',
    flexDirection: 'column',
  },
  headerTextWrapper: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '2mm',
  },
  headerText: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  textDivider: {
    marginHorizontal: '2mm',
    position: 'relative',
    top: '-1mm',
  },
  logoImage: {
    width: '50mm',
    marginBottom: '10mm',
  },
});

interface CoverHeaderProps extends ViewProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
}

// Create Document Component
const CoverHeader = ({ data, ...rest }: CoverHeaderProps) => (
  <View {...rest} style={[styles.header]}>
    <Branding brandingType={BrandingType.INVERTED} style={styles.logoImage} />

    <View style={styles.headerTextWrapper}>
      <View style={styles.headerText}>
        <Text
          style={[
            typography.h4,
            typography.fontWeightSemiBold,
            color.textSecondary,
            {
              letterSpacing: 0.5,
            },
          ]}
        >
          {data.insuredCompanyName}
        </Text>
        <Text
          style={[
            typography.body2,
            typography.fontWeightRegular,
            color.textTint3,
            styles.textDivider,
          ]}
        >
          |
        </Text>
        <Text style={[typography.h4, typography.fontWeightRegular, color.textTint3]}>
          {`DOT: ${data.dotNumber}`}
        </Text>
      </View>
    </View>
    <Text
      style={[
        typography.h4,
        typography.fontWeightSemiBold,
        color.textSecondary,
        {
          letterSpacing: 0.5,
        },
      ]}
    >
      {data.reportPeriod}
    </Text>
  </View>
);

export default CoverHeader;
