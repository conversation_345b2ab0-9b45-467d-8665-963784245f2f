import { Page, StyleSheet, Text, View } from '@react-pdf/renderer';
import { PageProps } from '@react-pdf/types';
import * as React from 'react';
import { color, typography } from 'src/theme';
import Branding, { BrandingType } from './branding';

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'row',
    padding: '4mm',
    border: 0,
    ...typography.fontFamilyInter,
  },
  cover: {
    width: '100%',
    height: '100%',
    backgroundColor: '#00248A',
    position: 'relative',
  },
  corner: {
    width: 0,
    height: 0,
    borderTopWidth: '80mm',
    borderTopStyle: 'solid',
    borderTopColor: '#fff',
    borderRightWidth: '80mm',
    borderRightStyle: 'solid',
  },
  cornerTop: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  cornerBottom: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    transform: 'rotate(180deg)',
  },
  page1Container: {
    position: 'absolute',
    top: '37%',
    left: 0,
    right: 0,
    display: 'flex',
    flexDirection: 'column',
    paddingHorizontal: '40mm',
  },
  logoImage: {
    width: '96mm',
    marginBottom: '12mm',
    marginLeft: '-24mm',
  },
  textContainer: {
    width: '100%',
    borderTopWidth: 1,
    borderTopStyle: 'solid',
    borderTopColor: '#D7D8DC',
    paddingTop: '3mm',
  },
  title: {
    fontSize: 15,
    color: '#C5C7CC',
    ...typography.uppercase,
    ...typography.fontWeightRegular,
    letterSpacing: '0.3mm',
  },
  carrierContainer: {
    width: '100%',
    paddingTop: '3mm',
    marginBottom: '3mm',
  },
  carrierTitle: {
    fontSize: 25,
    color: '#C5C7CC',
    ...typography.fontWeightRegular,
    letterSpacing: '0.5mm',
  },
  companyName: {
    ...color.textPrimary,
    ...typography.fontWeightRegular,
  },
});

interface CoverProps extends PageProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
  color: string;
  title: string;
  hideBranding?: boolean;
  logoType?: BrandingType;
  component?: React.ReactNode;
  companyNameColor?: string;
}

// Create Document Component
const Cover = ({
  data,
  color,
  title,
  logoType = BrandingType.DEFAULT,
  component = null,
  hideBranding,
  companyNameColor = '#FFF',
  ...rest
}: CoverProps) => {
  const { state } = data;
  return (
    <Page {...rest} style={styles.page}>
      <View style={[styles.cover, { backgroundColor: color }]}>
        <View style={styles.page1Container}>
          {!hideBranding && <Branding brandingType={logoType} style={styles.logoImage} />}
          {state === 'NC' && (
            <View style={styles.carrierContainer}>
              <Text style={styles.carrierTitle}>{data.insuranceCarrier}</Text>
            </View>
          )}
          <View style={styles.textContainer}>
            <Text style={[typography.h3, styles.title]}>{title}</Text>
            <Text style={[typography.h1, { color: companyNameColor }]}>
              {data.insuredCompanyName}
            </Text>
          </View>
          {component}
        </View>
        <View style={[styles.corner, styles.cornerTop, { borderRightColor: color }]} />
        <View style={[styles.corner, styles.cornerBottom, { borderRightColor: color }]} />
      </View>
    </Page>
  );
};

export default Cover;
