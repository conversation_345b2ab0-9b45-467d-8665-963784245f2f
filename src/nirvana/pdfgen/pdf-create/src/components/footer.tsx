import { Link, StyleSheet, Text, View } from '@react-pdf/renderer';
import { ViewProps } from '@react-pdf/types';
import { ReactNode } from 'react';
import { color, typography } from 'src/theme';
import Branding, { BrandingType } from './branding';

// Create styles
const styles = StyleSheet.create({
  footer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopStyle: 'solid',
    borderTopColor: '#C5C7CC',
    borderTopWidth: 1,
    paddingVertical: '4mm',
    position: 'absolute',
    bottom: 0,
    left: '6mm',
    right: '6mm',
    alignItems: 'center',
  },
  pageNumber: {
    paddingRight: '4mm',
  },
  footerCenter: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerRight: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  divider: {
    marginHorizontal: '4mm',
  },
  textCenter: {
    flex: 1,
    justifyContent: 'center',
  },
  supportEmail: {
    ...color.primary,
    textDecoration: 'none',
  },
});

export const FooterContent = (
  <>
    For any questions, please contact{' '}
    <Link src="mailto:<EMAIL>" style={styles.supportEmail}>
      <EMAIL>
    </Link>
  </>
);

interface FooterProps extends ViewProps {
  brandingType?: BrandingType;
  captionCenter?: ReactNode;
  captionRight?: ReactNode;
  hideBranding?: boolean;
  state?: string;
}

// Create Document Component
const Footer = ({
  brandingType = BrandingType.INVERTED,
  captionRight,
  captionCenter,
  hideBranding,
  state,
  ...rest
}: FooterProps) => {
  const center = hideBranding ? styles.textCenter : styles.footerCenter;
  const typographyBody = state ? typography.body2 : typography.body3;
  const textAlignment = hideBranding ? typography.textCenter : typography.textLeft;

  return (
    <View {...rest} style={styles.footer}>
      {!hideBranding && <Branding brandingType={brandingType} style={{ height: '5mm' }} />}
      {!!captionCenter && (
        <View style={center}>
          <Text style={[typographyBody, textAlignment]}>{captionCenter}</Text>
        </View>
      )}

      <View style={styles.footerRight}>
        {!!captionRight && (
          <>
            <Text style={[typography.body2]}>{captionRight}</Text>
            <Text style={[typography.body2, styles.divider]}>|</Text>
          </>
        )}

        <Text
          style={[typography.body2, styles.pageNumber]}
          render={({ pageNumber }) => ` ${pageNumber}`}
          fixed
        />
      </View>
    </View>
  );
};

export default Footer;
