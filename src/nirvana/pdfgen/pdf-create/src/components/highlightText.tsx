import { StyleSheet, Text, View } from '@react-pdf/renderer';
import { ViewProps } from '@react-pdf/types';
import React from 'react';

import { color, typography } from 'src/theme';

const styles = StyleSheet.create({
  item: {
    ...typography.fontFamilyInter,
    flexDirection: 'row',
    marginBottom: '2mm',
    paddingHorizontal: '3mm',
    paddingVertical: '2mm',
    borderRadius: 4,
    backgroundColor: color.textGreen.color,
    alignItems: 'flex-end',
  },
});

interface ItemRowProps extends ViewProps {
  children: React.ReactNode;
}

export const HighlightText = (props: ItemRowProps) => (
  <View {...props} style={[styles.item]}>
    <Text style={[typography.body2, color.white, typography.fontWeightSemiBold]}>
      {props.children}
    </Text>
  </View>
);

export default HighlightText;
