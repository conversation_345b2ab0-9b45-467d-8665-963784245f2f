import { StyleSheet, Text, View } from '@react-pdf/renderer';
import { ViewProps } from '@react-pdf/types';
import { ReactNode } from 'react';
import { typography } from 'src/theme';

const LargeSize = 40;
const RegularSize = 35;
const SmallSize = 28;

// Create styles
const styles = StyleSheet.create({
  header: {
    backgroundColor: '#F1F4F5',
    position: 'absolute',
    height: `${RegularSize}mm`,
    width: '100%',
    zIndex: 2,
    top: '6mm',
    left: '6mm',
    right: '6mm',
  },
  headerLarge: {
    height: `${LargeSize}mm`,
  },
  headerSmall: {
    height: `${SmallSize}mm`,
  },
  corner: {
    width: 0,
    height: 0,
    borderBottomWidth: `${RegularSize}mm`,
    borderBottomStyle: 'solid',
    borderBottomColor: '#fff',
    borderLeftWidth: `${RegularSize}mm`,
    borderLeftStyle: 'solid',
    borderLeftColor: '#F1F4F5',
    position: 'absolute',
    bottom: 0,
    right: 0,
  },
  cornerLarge: {
    borderBottomWidth: `${LargeSize}mm`,
    borderLeftWidth: `${LargeSize}mm`,
  },
  cornerSmall: {
    borderBottomWidth: `${SmallSize}mm`,
    borderLeftWidth: `${SmallSize}mm`,
  },
  headerTextWrapper: {
    display: 'flex',
    justifyContent: 'flex-end',
    position: 'absolute',
    left: '16mm',
    bottom: 0,
  },
  headerText: {
    marginBottom: '5mm',
    color: '#00248A',
    fontWeight: 600,
  },
  headerLine: {
    height: '1mm',
    backgroundColor: '#FFB900',
    width: '31mm',
  },
});

interface HeaderProps extends ViewProps {
  title?: string;
  size?: 'large' | 'regular' | 'small';
  children?: ReactNode;
}

// Create Document Component
const Header = ({ size = 'regular', title, children, ...rest }: HeaderProps) => (
  <View
    {...rest}
    style={[
      styles.header,
      size === 'large' ? styles.headerLarge : {},
      size === 'small' ? styles.headerSmall : {},
    ]}
  >
    <View
      style={[
        styles.corner,
        size === 'large' ? styles.cornerLarge : {},
        size === 'small' ? styles.cornerSmall : {},
      ]}
    />
    <View style={styles.headerTextWrapper}>
      {title ? <Text style={[typography.h2, styles.headerText]}>{title}</Text> : children}
      <View style={styles.headerLine} />
    </View>
  </View>
);

export default Header;
