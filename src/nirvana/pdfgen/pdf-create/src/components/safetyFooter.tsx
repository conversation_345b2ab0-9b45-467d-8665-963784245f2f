import { Link, StyleSheet, Text, View } from '@react-pdf/renderer';
import { ViewProps } from '@react-pdf/types';
import { SafetyData } from 'src/templates/safety/model';
import { color, typography } from 'src/theme';
import { getFormattedDate } from '../helpers/date';

// Create styles
const styles = StyleSheet.create({
  footer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopStyle: 'solid',
    borderTopColor: '#C5C7CC',
    borderTopWidth: 1,
    paddingVertical: '4mm',
    position: 'absolute',
    bottom: 0,
    left: '6mm',
    right: '6mm',
    alignItems: 'flex-start',
    fontSize: 14,
    textAlign: 'left',
  },
  pageNumber: {
    fontSize: 14,
    paddingRight: '4mm',
    paddingLeft: '6mm',
    color: '#00248A',
    ...typography.fontWeightSemiBold,
  },
  footerCenter: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerRight: {
    fontSize: 14,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  divider: {
    marginHorizontal: '1mm',
  },
  textCenter: {
    flex: 1,
    justifyContent: 'center',
  },
  supportEmail: {
    fontSize: 12,
    ...color.primary,
  },
  reportDate: {
    ...typography.body2,
  },
  companyName: {
    color: '#8596C7',
    ...typography.body2,
  },
});

interface FooterProps extends ViewProps {
  data: SafetyData;
  _state?: string;
}

// Create Document Component
const SafetyFooter = ({ data, _state, ...rest }: FooterProps) => {
  return (
    <View {...rest} style={styles.footer}>
      <Text style={styles.companyName}>
        {data.insuredCompanyName} | {getFormattedDate(data.createdAt, 'MMMM yyyy')} Safety Report
      </Text>
      <View style={styles.footerRight}>
        <Link src="https://safety.nirvanatech.com" style={styles.supportEmail}>
          safety.nirvanatech.com
        </Link>
        <Text
          style={[typography.body2, styles.pageNumber]}
          render={({ pageNumber }) => ` ${pageNumber}`}
          fixed
        />
      </View>
    </View>
  );
};

export default SafetyFooter;
