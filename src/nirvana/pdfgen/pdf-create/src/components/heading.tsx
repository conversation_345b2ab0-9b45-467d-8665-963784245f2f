import { StyleSheet, Text, View } from '@react-pdf/renderer';
import { ViewProps } from '@react-pdf/types';
import { typography } from 'src/theme';

// Create styles
const styles = StyleSheet.create({
  heading: {
    textTransform: 'uppercase',
    fontWeight: 700,
    letterSpacing: '0.3mm',
  },
  headerLine: {
    height: 1,
    backgroundColor: '#FFB900',
    width: '13mm',
  },
});

interface HeadingProps extends ViewProps {
  text: string;
  underlineLength?: string;
}

// Create Document Component
const Heading = ({ text, underlineLength = '13mm' }: HeadingProps) => (
  <View style={{ marginBottom: '6mm' }}>
    <Text style={[typography.h4, styles.heading]}>{text}</Text>
    <View style={[styles.headerLine, { width: underlineLength }]} />
  </View>
);

export default Heading;
