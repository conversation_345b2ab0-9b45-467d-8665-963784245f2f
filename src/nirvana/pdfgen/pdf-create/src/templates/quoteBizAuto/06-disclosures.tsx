import { StyleSheet, Text, View } from '@react-pdf/renderer';
import React from 'react';
import { common, text } from './styles';
import { THEME } from './types';

const styles = StyleSheet.create({
  container: {
    padding: THEME.spacing.lg,
  },
  title: {
    ...text.title,
  },
  subtitle: {
    fontSize: THEME.fontSize.xl,
    color: THEME.colors.text,
    marginBottom: THEME.spacing.lg,
  },
  cardsContainer: {
    flexDirection: 'row',
    marginBottom: THEME.spacing.sm,
  },
  card: {
    ...common.card,
    flex: 1,
    minHeight: 90,
    marginRight: THEME.spacing.lg,
  },
  cardLast: {
    ...common.card,
    flex: 1,
    minHeight: 90,
  },
  cardText: {
    fontSize: THEME.fontSize.sm,
    color: '#374151',
    lineHeight: 1.3,
  },
  boldText: {
    fontWeight: THEME.fontWeight.bold,
  },
  finePrintLink: {
    fontSize: THEME.fontSize.base,
    color: '#1f2937',
    marginBottom: THEME.spacing.lg,
  },
  paragraph: {
    fontSize: THEME.fontSize.sm,
    color: '#374151',
    lineHeight: 1.4,
    marginBottom: THEME.spacing.sm,
    textAlign: 'justify',
  },
  signatureSection: {
    marginTop: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  signatureField: {
    width: '48%',
  },
  signatureLabel: {
    fontSize: THEME.fontSize.base,
    color: THEME.colors.textSecondary,
    marginBottom: THEME.spacing.sm,
  },
  signatureBox: {
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    backgroundColor: '#F2F3F4',
    borderBottomColor: '#9B9EA6',
    borderBottomWidth: 1,
    height: 35,
    padding: THEME.spacing.sm,
  },
});

// Card data for telematics requirements
const telematicsCards = [
  {
    text: (
      <>
        Keep telematics and/or cameras <Text style={styles.boldText}>connected</Text> in all insured
        vehicles <Text style={styles.boldText}>at all times</Text>
      </>
    ),
  },
  {
    text: (
      <>
        Set up telematics devices in new power units{' '}
        <Text style={styles.boldText}>within 30 days</Text> of acquiring the vehicle
      </>
    ),
  },
  {
    text: (
      <>
        If Nirvana loses access to your telematics data, we may have to{' '}
        <Text style={styles.boldText}>re-rate or cancel your policy</Text>
      </>
    ),
  },
];

// Disclosure paragraphs
const disclosureParagraphs = [
  'This summary is for informational purposes only and is not an indication of insurability or a guarantee of coverage.',
  'Any request to add, delete, or change coverage during the policy period must be submitted in writing to Nirvana Insurance Services, LLC (collectively with its affiliates, "Nirvana"). Please note that any quote is subject to adjustment should the exposure change prior to the effective date.',
  'Quotes are valid for 30 days or until the proposed effective date of the policy, whichever occurs first.',
  'Loss control surveys will be performed at the sole discretion of Nirvana.',
  'Customer shall provide Nirvana with access to ELD/telematics and camera data, as applicable, for all insured vehicles in operation during the policy period, including, but not limited to, all owner-operated vehicles.',
  'Customer acknowledges that access to its ELD/telematics information and devices is of critical importance to Nirvana, and Customer shall provide Nirvana with uninterrupted data connection (a) to any of its ELD/telematics devices connected at the time of bind, and (b) within 30 days of power unit acquisition for any new ELD/telematics devices added during the policy period. Customer further acknowledges that failure to provide such access to Nirvana at any time during the policy period constitutes a material change in exposure and may result in underwriting action or cancellation of the policy, subject to the applicable law.',
  'Customer must provide written notice to Nirvana at least 30 days prior to any change in ELD/telematics and camera data provider, as applicable. Nirvana has sole discretion to determine if any new ELD/telematics and camera data provider is acceptable or deemed unacceptable. Nirvana shall have authority to take underwriting action or cancel the policy, subject to the applicable law.',
  'If Nirvana elects to provide device subsidies, Customer warrants that functioning devices prescribed by Nirvana or its representatives (a) shall be installed in all insured vehicles within 45 days of policy inception and 30 days of power unit acquisition, and (b) shall provide uninterrupted access to Nirvana throughout the policy period. If Customer fails to satisfy any of these obligations, Nirvana shall have sole discretion to discontinue any device subsidies and cancel the policy, subject to the applicable law.',
];

export const Disclosures: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Disclosures and conditions</Text>

      <Text style={styles.subtitle}>Nirvana&apos;s Telematics Requirements</Text>

      {/* Telematics Requirements Cards */}
      <View style={styles.cardsContainer}>
        {telematicsCards.map((card, index) => (
          <View
            key={index}
            style={index === telematicsCards.length - 1 ? styles.cardLast : styles.card}
          >
            <View style={common.icon}>
              <Text style={common.iconText}>!</Text>
            </View>
            <Text style={styles.cardText}>{card.text}</Text>
          </View>
        ))}
      </View>

      <Text style={styles.finePrintLink}>See Fine Print for More Details →</Text>

      {/* Disclosure Paragraphs */}
      {disclosureParagraphs.map((paragraph, index) => (
        <Text key={index} style={styles.paragraph}>
          {paragraph}
        </Text>
      ))}

      {/* Signature Section */}
      <View style={styles.signatureSection}>
        <View style={styles.signatureField}>
          <Text style={styles.signatureLabel}>Signature of Authorized Insured Representative</Text>
          <View style={styles.signatureBox} />
        </View>

        <View style={styles.signatureField}>
          <Text style={styles.signatureLabel}>Date</Text>
          <View style={styles.signatureBox} />
        </View>
      </View>
    </View>
  );
};
