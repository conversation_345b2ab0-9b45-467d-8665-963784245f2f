import { Image, StyleSheet, View } from '@react-pdf/renderer';
import React from 'react';
import { page } from '../styles';

const styles = StyleSheet.create({
  pageHeader: {
    ...page.header,
  },
  logo: {
    ...page.logo,
  },
});

interface HeaderProps {
  fixed?: boolean;
}

const Header: React.FC<HeaderProps> = ({ fixed = false }) => {
  return (
    <View style={styles.pageHeader} fixed={fixed}>
      <Image style={styles.logo} src="src/assets/images/logo-black.png" />
    </View>
  );
};

export default Header;
