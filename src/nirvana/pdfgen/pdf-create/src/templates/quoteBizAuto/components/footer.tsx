import { StyleSheet, Text, View } from '@react-pdf/renderer';
import React from 'react';
import { page } from '../styles';
import { QuoteBizAutoData, THEME } from '../types';
import { createFooterData } from '../utils';

const styles = StyleSheet.create({
  footer: {
    ...page.footer,
  },
  footerText: {
    ...page.footerText,
  },
  pageNumber: {
    ...page.footerText,
    color: THEME.colors.navy,
    fontWeight: THEME.fontWeight.semibold,
  },
});

interface FooterProps {
  data: QuoteBizAutoData;
  pageNumber?: string;
  fixed?: boolean;
}

const Footer: React.FC<FooterProps> = ({ data, pageNumber, fixed = false }) => {
  const footerData = createFooterData(
    data.customerInfo.policyPeriod.startDate,
    data.customerInfo.policyNumber,
    pageNumber || '', // Will be filled by render prop if not provided
    data.timestamp
  );

  return (
    <View style={styles.footer} fixed={fixed}>
      <Text style={styles.footerText}>{footerData.left}</Text>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <Text style={[styles.footerText, { marginRight: 24 }]}>{footerData.right}</Text>
        {pageNumber ? (
          <Text style={styles.pageNumber}>{pageNumber}</Text>
        ) : (
          <Text
            style={styles.pageNumber}
            render={({ pageNumber: currentPage }) => String(currentPage).padStart(2, '0')}
            fixed
          />
        )}
      </View>
    </View>
  );
};

export default Footer;
