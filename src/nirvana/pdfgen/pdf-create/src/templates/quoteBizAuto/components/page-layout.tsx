import { Page, StyleSheet, View } from '@react-pdf/renderer';
import React from 'react';
import { Footer, Header } from '.';
import { page } from '../styles';
import { QuoteBizAutoData } from '../types';

const styles = StyleSheet.create({
  page: {
    ...page.container,
    fontFamily: 'Inter',
  },
  section: {
    ...page.section,
    marginBottom: 0,
    paddingBottom: 80,
  },
});

interface PageLayoutProps {
  children: React.ReactNode;
  data: QuoteBizAutoData;
  pageNumber?: string;
  wrap?: boolean;
}

/**
 * Reusable page layout component that provides consistent structure:
 * - Header with logo
 * - Content section
 * - Footer with dynamic or static page numbering
 */
const PageLayout: React.FC<PageLayoutProps> = ({ children, data, pageNumber, wrap = false }) => {
  return (
    <Page size="A4" style={styles.page} wrap={wrap}>
      <Header />
      <View style={styles.section}>{children}</View>
      <Footer data={data} pageNumber={pageNumber} fixed={wrap} />
    </Page>
  );
};

export default PageLayout;
