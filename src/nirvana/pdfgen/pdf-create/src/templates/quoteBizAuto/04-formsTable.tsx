import { StyleSheet, Text, View } from '@react-pdf/renderer';
import React from 'react';
import { PageLayout } from './components';
import { QuoteBizAutoData, THEME } from './types';

export interface FormInfo {
  code: string;
  name: string;
}

interface Props {
  forms: FormInfo[];
  data: QuoteBizAutoData;
}

const styles = StyleSheet.create({
  container: {
    marginTop: 24,
    marginBottom: 40,
  },
  title: {
    fontSize: 20,
    fontWeight: THEME.fontWeight.semibold,
    color: THEME.colors.text,
    marginBottom: 20,
  },
  table: {
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: 'white',
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#013565',
    paddingVertical: 12,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    alignItems: 'stretch',
    minHeight: 40,
  },
  tableHeaderText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  codeColumn: {
    width: '30%',
  },
  codeColumnHeader: {
    width: '30%',
    paddingLeft: 16,
    paddingRight: 16,
  },
  headerDivider: {
    width: 1,
    backgroundColor: 'white',
    marginVertical: -12,
    alignSelf: 'stretch',
  },
  nameColumn: {
    width: '70%',
  },
  nameColumnHeader: {
    flex: 1,
    paddingLeft: 16,
    paddingRight: 16,
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 6,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    minHeight: 32,
    alignItems: 'center',
  },
  codeText: {
    fontSize: 9,
    fontWeight: 'normal',
    color: '#000',
  },
  nameText: {
    fontSize: 9,
    fontWeight: 'normal',
    color: '#000',
    lineHeight: 1.4,
  },
});

const FormsTable: React.FC<Props> = ({ forms, data }) => {
  return (
    <PageLayout data={data} wrap>
      <View style={styles.container}>
        <Text style={styles.title}>Schedule of Forms & Endorsements</Text>

        <View style={styles.table}>
          {/* Header */}
          <View style={styles.tableHeader}>
            <View style={styles.codeColumnHeader}>
              <Text style={styles.tableHeaderText}>Form Number</Text>
            </View>
            <View style={styles.headerDivider} />
            <View style={styles.nameColumnHeader}>
              <Text style={styles.tableHeaderText}>Form Name</Text>
            </View>
          </View>

          {forms.map((form, index) => (
            <View key={index} style={styles.tableRow} wrap={false}>
              <View style={styles.codeColumn}>
                <Text style={styles.codeText}>{form.code}</Text>
              </View>
              <View style={styles.nameColumn}>
                <Text style={styles.nameText}>{form.name}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    </PageLayout>
  );
};

export default FormsTable;
