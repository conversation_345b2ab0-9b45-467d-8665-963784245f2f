import { Style } from '@react-pdf/types';

// ===========================
// LAYOUT UTILITIES
// ===========================

/**
 * Create column style with width and padding
 */
export const createColumn = (
  width: string,
  paddingHorizontal: number = 12,
  justifyContent: 'flex-start' | 'center' | 'flex-end' = 'center'
): Style => ({
  width,
  paddingHorizontal,
  justifyContent,
});

/**
 * Create consistent row style with height, padding, and optional border
 */
export const createRow = (
  height: number = 44,
  paddingHorizontal: number = 8,
  borderColor?: string
): Style => ({
  height,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingHorizontal,
  ...(borderColor && {
    borderBottomWidth: 1,
    borderBottomColor: borderColor,
  }),
});
