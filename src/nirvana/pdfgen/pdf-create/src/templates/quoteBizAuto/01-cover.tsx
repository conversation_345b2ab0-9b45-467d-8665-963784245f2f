import { Image, Page, StyleSheet, Text, View } from '@react-pdf/renderer';
import React from 'react';
import { typography } from 'src/theme';
import { getFormattedDate } from '../../helpers/date';
import { Footer } from './components';
import { QuoteBizAutoData } from './types';

const styles = StyleSheet.create({
  page: {
    ...typography.fontFamilyInter,
    padding: '12mm',
    paddingTop: '10mm',
    paddingBottom: '10mm',
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: '12mm',
  },
  nirvanaLogo: {
    width: '35mm',
    height: '7mm',
  },
  personalizedQuoteLabel: {
    ...typography.body1,
    color: '#003366',
    fontWeight: 600,
    letterSpacing: '0.3mm',
    textTransform: 'uppercase',
    marginBottom: '12mm',
    fontSize: 11,
  },
  customerName: {
    ...typography.h1,
    fontSize: 40,
    fontWeight: 400,
    color: '#040C21',
    marginVertical: '8mm',
    lineHeight: 1.0,
  },
  dividerLine: {
    height: 2,
    backgroundColor: '#040C21',
    marginBottom: '8mm',
  },
  infoGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: '6mm',
  },
  leftColumn: {
    width: '48%',
  },
  rightColumn: {
    width: '48%',
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: '2mm',
    alignItems: 'baseline',
  },
  infoLabel: {
    ...typography.body1,
    color: '#8B8B8B',
    width: '40%',
    fontSize: 11,
    fontWeight: 400,
  },
  infoValue: {
    ...typography.body1,
    color: '#040C21',
    fontWeight: 500,
    width: '60%',
    fontSize: 11,
  },
  infoValueRight: {
    ...typography.body1,
    color: '#040C21',
    fontWeight: 500,
    width: '60%',
    fontSize: 11,
    textAlign: 'right',
  },
  bestRatingSection: {
    marginBottom: '2mm',
    width: '100%',
  },
  bestRatingImage: {
    width: '100%',
    height: 'auto',
    marginBottom: '6mm',
  },
  saferSmarterFairer: {
    ...typography.h2,
    fontSize: 18,
    fontWeight: 600,
    color: '#040C21',
    textAlign: 'left',
    marginBottom: '4mm',
  },
  taglineText: {
    ...typography.body1,
    color: '#040C21',
    textAlign: 'left',
    marginBottom: '8mm',
    fontSize: 12,
    lineHeight: 1.3,
  },
  benefitsSection: {
    marginBottom: '6mm',
    color: '#040C21',
  },
  benefitItem: {
    flexDirection: 'row',
    marginBottom: '4mm',
    alignItems: 'flex-start',
  },
  benefitIcon: {
    width: '8mm',
    height: '8mm',
    marginRight: '3mm',
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    ...typography.body1,
    fontWeight: 600,
    color: '#000000',
    marginBottom: '1mm',
    fontSize: 12,
  },
  benefitDescription: {
    ...typography.body1,
    color: '#666666',
    lineHeight: 1.2,
    fontSize: 10,
  },

  rowDivider: {
    height: 1,
    backgroundColor: '#E5E5E5',
    marginVertical: '2mm',
  },
  infoRowRight: {
    flexDirection: 'row',
    marginBottom: '7mm',
    alignItems: 'baseline',
  },
});

const Cover: React.FC<{ data: QuoteBizAutoData }> = ({ data }) => {
  return (
    <Page size="A4" style={styles.page}>
      {/* Header with Nirvana Logo */}
      <View style={styles.header}>
        <View />
        <Image src="src/assets/images/logo-black.png" style={styles.nirvanaLogo} />
      </View>

      {/* Personalized Quote Section */}
      <View>
        <Text style={styles.personalizedQuoteLabel}>PERSONALIZED QUOTE FOR</Text>
        <Text style={styles.customerName}>{data.customerInfo.name}</Text>
        <View style={styles.dividerLine} />
      </View>

      {/* Info Grid */}
      <View style={styles.infoGrid}>
        <View style={styles.leftColumn}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Presented by</Text>
            <Text style={styles.infoValue}></Text>
          </View>
          <View style={styles.rowDivider} />
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Agency</Text>
            <Text style={styles.infoValue}>{data.customerInfo.agency}</Text>
          </View>
          <View style={styles.rowDivider} />
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Producer</Text>
            <Text style={styles.infoValue}>{data.customerInfo.producer}</Text>
          </View>
        </View>
        <View style={styles.rightColumn}>
          <View style={styles.infoRowRight}>
            <Text style={styles.infoLabel}>Policy Period</Text>
            <Text style={styles.infoValueRight}>
              {getFormattedDate(data.customerInfo.policyPeriod.startDate)} -{' '}
              {getFormattedDate(data.customerInfo.policyPeriod.endDate)}
            </Text>
          </View>
          <View style={styles.infoRowRight}>
            <Text style={styles.infoLabel}>DOT #</Text>
            <Text style={styles.infoValueRight}>{data.customerInfo.dotNumber}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>FEIN #</Text>
            <Text style={styles.infoValueRight}>{data.customerInfo.feinNumber}</Text>
          </View>
        </View>
      </View>

      {/* Best Rating Section */}
      <View style={styles.bestRatingSection}>
        <Image
          src="src/assets/images/quoteBizAuto/am-best-rating.png"
          style={styles.bestRatingImage}
        />
      </View>

      {/* Safer. Smarter. Fairer. */}
      <Text style={styles.saferSmarterFairer}>Safer. Smarter. Fairer.</Text>
      <Text style={styles.taglineText}>
        Nirvana&apos;s telematics-first insurance helps the safest fleets reduce their insurance
        costs.
      </Text>

      {/* Benefits Section */}
      <View style={styles.benefitsSection}>
        <View style={styles.benefitItem}>
          <Image src="src/assets/images/quoteBizAuto/safety-icon.png" style={styles.benefitIcon} />
          <View style={styles.benefitContent}>
            <Text style={styles.benefitTitle}>Up front safety discount</Text>
            <Text style={styles.benefitDescription}>
              Nirvana offers safety discounts for fleets with exceptional driving history.
            </Text>
          </View>
        </View>

        <View style={styles.benefitItem}>
          <Image src="src/assets/images/quoteBizAuto/backing-icon.png" style={styles.benefitIcon} />
          <View style={styles.benefitContent}>
            <Text style={styles.benefitTitle}>Powerful Backing</Text>
            <Text style={styles.benefitDescription}>
              Nirvana policies are underwritten by{' '}
              <Text style={{ fontWeight: 700 }}>
                MS Transverse Insurance Company, A rated (Excellent) by A.M. Best.
              </Text>
            </Text>
          </View>
        </View>

        <View style={styles.benefitItem}>
          <Image src="src/assets/images/quoteBizAuto/claims-icon.png" style={styles.benefitIcon} />
          <View style={styles.benefitContent}>
            <Text style={styles.benefitTitle}>Best-in-class claims servicing</Text>
            <Text style={styles.benefitDescription}>
              Our 24/7 claims process uses telematics and AI to resolve claims faster. Backed by
              100% US-based in-house claims experts with decades of transportation experience, we
              oversee 100% of submitted claims.
            </Text>
          </View>
        </View>
      </View>

      {/* Footer */}
      <Footer data={data} />
    </Page>
  );
};

export default Cover;
