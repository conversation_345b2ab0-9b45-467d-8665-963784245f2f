import { Document } from '@react-pdf/renderer';
import React from 'react';
import Cover from './01-cover';
import CoverageTable from './02-coverageTable';
import VehicleTable from './03-vehicleTable';
import FormsTable from './04-formsTable';
import { PolicyFees } from './05-policyFees';
import { Disclosures } from './06-disclosures';
import { PageLayout } from './components';
import { QuoteBizAutoData } from './types';

/**
 * QuoteBizAuto PDF Template
 * Generates a multi-page business auto insurance quote document
 */
const QuoteBizAuto: React.FC<{ data: QuoteBizAutoData }> = ({ data }) => {
  return (
    <Document language="en">
      {/* Page 1: Cover */}
      <Cover data={data} />

      {/* Page 2: Coverage Table */}
      <PageLayout data={data}>
        <CoverageTable coverages={data.coverages} pricingInfo={data.premiumDetails} />
      </PageLayout>

      {/* Page 3: Vehicles & Policy Fees */}
      <PageLayout data={data}>
        <VehicleTable vehicles={data.vehicles} />
        <PolicyFees data={data} />
      </PageLayout>

      {/* Page 4: Forms */}
      <FormsTable forms={data.forms} data={data} />

      {/* Page 5: Disclosures */}
      <PageLayout data={data}>
        <Disclosures />
      </PageLayout>
    </Document>
  );
};

export default QuoteBizAuto;
