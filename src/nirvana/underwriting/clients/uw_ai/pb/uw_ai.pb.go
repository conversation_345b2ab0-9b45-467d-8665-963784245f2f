// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: uw_ai/uw_ai.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Violation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code              *string `protobuf:"bytes,1,opt,name=code,proto3,oneof" json:"code,omitempty"`
	Date              *string `protobuf:"bytes,2,opt,name=date,proto3,oneof" json:"date,omitempty"`
	Description       *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	Details           *string `protobuf:"bytes,4,opt,name=details,proto3,oneof" json:"details,omitempty"`
	Points            *int32  `protobuf:"varint,5,opt,name=points,proto3,oneof" json:"points,omitempty"`
	Type              *string `protobuf:"bytes,6,opt,name=type,proto3,oneof" json:"type,omitempty"`
	IsMovingViolation *bool   `protobuf:"varint,7,opt,name=is_moving_violation,json=isMovingViolation,proto3,oneof" json:"is_moving_violation,omitempty"`
}

func (x *Violation) Reset() {
	*x = Violation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Violation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Violation) ProtoMessage() {}

func (x *Violation) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Violation.ProtoReflect.Descriptor instead.
func (*Violation) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{0}
}

func (x *Violation) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *Violation) GetDate() string {
	if x != nil && x.Date != nil {
		return *x.Date
	}
	return ""
}

func (x *Violation) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *Violation) GetDetails() string {
	if x != nil && x.Details != nil {
		return *x.Details
	}
	return ""
}

func (x *Violation) GetPoints() int32 {
	if x != nil && x.Points != nil {
		return *x.Points
	}
	return 0
}

func (x *Violation) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *Violation) GetIsMovingViolation() bool {
	if x != nil && x.IsMovingViolation != nil {
		return *x.IsMovingViolation
	}
	return false
}

type MvrDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateExpires          *string      `protobuf:"bytes,1,opt,name=date_expires,json=dateExpires,proto3,oneof" json:"date_expires,omitempty"`
	DateIssued           *string      `protobuf:"bytes,2,opt,name=date_issued,json=dateIssued,proto3,oneof" json:"date_issued,omitempty"`
	DateOfBirth          *string      `protobuf:"bytes,3,opt,name=date_of_birth,json=dateOfBirth,proto3,oneof" json:"date_of_birth,omitempty"`
	DlState              *string      `protobuf:"bytes,4,opt,name=dl_state,json=dlState,proto3,oneof" json:"dl_state,omitempty"`
	MovingViolationCount *float32     `protobuf:"fixed32,5,opt,name=moving_violation_count,json=movingViolationCount,proto3,oneof" json:"moving_violation_count,omitempty"`
	Violations           []*Violation `protobuf:"bytes,6,rep,name=violations,proto3" json:"violations,omitempty"`
}

func (x *MvrDetails) Reset() {
	*x = MvrDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MvrDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MvrDetails) ProtoMessage() {}

func (x *MvrDetails) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MvrDetails.ProtoReflect.Descriptor instead.
func (*MvrDetails) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{1}
}

func (x *MvrDetails) GetDateExpires() string {
	if x != nil && x.DateExpires != nil {
		return *x.DateExpires
	}
	return ""
}

func (x *MvrDetails) GetDateIssued() string {
	if x != nil && x.DateIssued != nil {
		return *x.DateIssued
	}
	return ""
}

func (x *MvrDetails) GetDateOfBirth() string {
	if x != nil && x.DateOfBirth != nil {
		return *x.DateOfBirth
	}
	return ""
}

func (x *MvrDetails) GetDlState() string {
	if x != nil && x.DlState != nil {
		return *x.DlState
	}
	return ""
}

func (x *MvrDetails) GetMovingViolationCount() float32 {
	if x != nil && x.MovingViolationCount != nil {
		return *x.MovingViolationCount
	}
	return 0
}

func (x *MvrDetails) GetViolations() []*Violation {
	if x != nil {
		return x.Violations
	}
	return nil
}

type Driver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                        *string     `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	DateHired                   string      `protobuf:"bytes,2,opt,name=date_hired,json=dateHired,proto3" json:"date_hired,omitempty"`
	DateOfBirth                 *string     `protobuf:"bytes,3,opt,name=date_of_birth,json=dateOfBirth,proto3,oneof" json:"date_of_birth,omitempty"`
	ExperienceStartDate         *string     `protobuf:"bytes,4,opt,name=experience_start_date,json=experienceStartDate,proto3,oneof" json:"experience_start_date,omitempty"`
	AttractScore                *int32      `protobuf:"varint,5,opt,name=attract_score,json=attractScore,proto3,oneof" json:"attract_score,omitempty"`
	DlNumber                    string      `protobuf:"bytes,6,opt,name=dl_number,json=dlNumber,proto3" json:"dl_number,omitempty"`
	UsState                     string      `protobuf:"bytes,7,opt,name=us_state,json=usState,proto3" json:"us_state,omitempty"`
	MvrDetails                  *MvrDetails `protobuf:"bytes,8,opt,name=mvr_details,json=mvrDetails,proto3" json:"mvr_details,omitempty"`
	MvrStatus                   string      `protobuf:"bytes,9,opt,name=mvr_status,json=mvrStatus,proto3" json:"mvr_status,omitempty"`
	YearsOfExperienceAgentInput *string     `protobuf:"bytes,10,opt,name=years_of_experience_agent_input,json=yearsOfExperienceAgentInput,proto3,oneof" json:"years_of_experience_agent_input,omitempty"`
	YearsOfExperienceMvr        *string     `protobuf:"bytes,11,opt,name=years_of_experience_mvr,json=yearsOfExperienceMvr,proto3,oneof" json:"years_of_experience_mvr,omitempty"`
}

func (x *Driver) Reset() {
	*x = Driver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Driver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Driver) ProtoMessage() {}

func (x *Driver) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Driver.ProtoReflect.Descriptor instead.
func (*Driver) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{2}
}

func (x *Driver) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Driver) GetDateHired() string {
	if x != nil {
		return x.DateHired
	}
	return ""
}

func (x *Driver) GetDateOfBirth() string {
	if x != nil && x.DateOfBirth != nil {
		return *x.DateOfBirth
	}
	return ""
}

func (x *Driver) GetExperienceStartDate() string {
	if x != nil && x.ExperienceStartDate != nil {
		return *x.ExperienceStartDate
	}
	return ""
}

func (x *Driver) GetAttractScore() int32 {
	if x != nil && x.AttractScore != nil {
		return *x.AttractScore
	}
	return 0
}

func (x *Driver) GetDlNumber() string {
	if x != nil {
		return x.DlNumber
	}
	return ""
}

func (x *Driver) GetUsState() string {
	if x != nil {
		return x.UsState
	}
	return ""
}

func (x *Driver) GetMvrDetails() *MvrDetails {
	if x != nil {
		return x.MvrDetails
	}
	return nil
}

func (x *Driver) GetMvrStatus() string {
	if x != nil {
		return x.MvrStatus
	}
	return ""
}

func (x *Driver) GetYearsOfExperienceAgentInput() string {
	if x != nil && x.YearsOfExperienceAgentInput != nil {
		return *x.YearsOfExperienceAgentInput
	}
	return ""
}

func (x *Driver) GetYearsOfExperienceMvr() string {
	if x != nil && x.YearsOfExperienceMvr != nil {
		return *x.YearsOfExperienceMvr
	}
	return ""
}

type DriversSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AverageTenure   int64 `protobuf:"varint,1,opt,name=average_tenure,json=averageTenure,proto3" json:"average_tenure,omitempty"`
	NumberOfDrivers int32 `protobuf:"varint,2,opt,name=number_of_drivers,json=numberOfDrivers,proto3" json:"number_of_drivers,omitempty"`
	TenureTurnover  int32 `protobuf:"varint,3,opt,name=tenure_turnover,json=tenureTurnover,proto3" json:"tenure_turnover,omitempty"`
}

func (x *DriversSummary) Reset() {
	*x = DriversSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DriversSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriversSummary) ProtoMessage() {}

func (x *DriversSummary) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriversSummary.ProtoReflect.Descriptor instead.
func (*DriversSummary) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{3}
}

func (x *DriversSummary) GetAverageTenure() int64 {
	if x != nil {
		return x.AverageTenure
	}
	return 0
}

func (x *DriversSummary) GetNumberOfDrivers() int32 {
	if x != nil {
		return x.NumberOfDrivers
	}
	return 0
}

func (x *DriversSummary) GetTenureTurnover() int32 {
	if x != nil {
		return x.TenureTurnover
	}
	return 0
}

type EquipmentSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AverageYear *int32 `protobuf:"varint,1,opt,name=average_year,json=averageYear,proto3,oneof" json:"average_year,omitempty"`
	Tiv         int32  `protobuf:"varint,2,opt,name=tiv,proto3" json:"tiv,omitempty"`
	UnitCount   int32  `protobuf:"varint,3,opt,name=unit_count,json=unitCount,proto3" json:"unit_count,omitempty"`
	UnitType    string `protobuf:"bytes,4,opt,name=unit_type,json=unitType,proto3" json:"unit_type,omitempty"`
}

func (x *EquipmentSummary) Reset() {
	*x = EquipmentSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentSummary) ProtoMessage() {}

func (x *EquipmentSummary) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentSummary.ProtoReflect.Descriptor instead.
func (*EquipmentSummary) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{4}
}

func (x *EquipmentSummary) GetAverageYear() int32 {
	if x != nil && x.AverageYear != nil {
		return *x.AverageYear
	}
	return 0
}

func (x *EquipmentSummary) GetTiv() int32 {
	if x != nil {
		return x.Tiv
	}
	return 0
}

func (x *EquipmentSummary) GetUnitCount() int32 {
	if x != nil {
		return x.UnitCount
	}
	return 0
}

func (x *EquipmentSummary) GetUnitType() string {
	if x != nil {
		return x.UnitType
	}
	return ""
}

type SevereViolation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsControlledSubstancesAndAlcohol *bool   `protobuf:"varint,1,opt,name=is_controlled_substances_and_alcohol,json=isControlledSubstancesAndAlcohol,proto3,oneof" json:"is_controlled_substances_and_alcohol,omitempty"`
	Description                      string  `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Frequency                        int32   `protobuf:"varint,3,opt,name=frequency,proto3" json:"frequency,omitempty"`
	Impact                           float32 `protobuf:"fixed32,4,opt,name=impact,proto3" json:"impact,omitempty"`
	InspectionDate                   *string `protobuf:"bytes,5,opt,name=inspection_date,json=inspectionDate,proto3,oneof" json:"inspection_date,omitempty"`
	PublishedDate                    *string `protobuf:"bytes,6,opt,name=published_date,json=publishedDate,proto3,oneof" json:"published_date,omitempty"`
	Violation                        string  `protobuf:"bytes,7,opt,name=violation,proto3" json:"violation,omitempty"`
}

func (x *SevereViolation) Reset() {
	*x = SevereViolation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SevereViolation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SevereViolation) ProtoMessage() {}

func (x *SevereViolation) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SevereViolation.ProtoReflect.Descriptor instead.
func (*SevereViolation) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{5}
}

func (x *SevereViolation) GetIsControlledSubstancesAndAlcohol() bool {
	if x != nil && x.IsControlledSubstancesAndAlcohol != nil {
		return *x.IsControlledSubstancesAndAlcohol
	}
	return false
}

func (x *SevereViolation) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SevereViolation) GetFrequency() int32 {
	if x != nil {
		return x.Frequency
	}
	return 0
}

func (x *SevereViolation) GetImpact() float32 {
	if x != nil {
		return x.Impact
	}
	return 0
}

func (x *SevereViolation) GetInspectionDate() string {
	if x != nil && x.InspectionDate != nil {
		return *x.InspectionDate
	}
	return ""
}

func (x *SevereViolation) GetPublishedDate() string {
	if x != nil && x.PublishedDate != nil {
		return *x.PublishedDate
	}
	return ""
}

func (x *SevereViolation) GetViolation() string {
	if x != nil {
		return x.Violation
	}
	return ""
}

type CrashRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessControl        *string `protobuf:"bytes,1,opt,name=access_control,json=accessControl,proto3,oneof" json:"access_control,omitempty"`
	CargoBodyType        *string `protobuf:"bytes,2,opt,name=cargo_body_type,json=cargoBodyType,proto3,oneof" json:"cargo_body_type,omitempty"`
	Date                 string  `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
	Fatalities           int32   `protobuf:"varint,4,opt,name=fatalities,proto3" json:"fatalities,omitempty"`
	Injuries             int32   `protobuf:"varint,5,opt,name=injuries,proto3" json:"injuries,omitempty"`
	LightCondition       *string `protobuf:"bytes,6,opt,name=light_condition,json=lightCondition,proto3,oneof" json:"light_condition,omitempty"`
	Location             string  `protobuf:"bytes,7,opt,name=location,proto3" json:"location,omitempty"`
	NotPreventable       *bool   `protobuf:"varint,8,opt,name=not_preventable,json=notPreventable,proto3,oneof" json:"not_preventable,omitempty"`
	RoadSurfaceCondition *string `protobuf:"bytes,9,opt,name=road_surface_condition,json=roadSurfaceCondition,proto3,oneof" json:"road_surface_condition,omitempty"`
	TowAway              bool    `protobuf:"varint,10,opt,name=tow_away,json=towAway,proto3" json:"tow_away,omitempty"`
	TrafficWay           *string `protobuf:"bytes,11,opt,name=traffic_way,json=trafficWay,proto3,oneof" json:"traffic_way,omitempty"`
	UsState              string  `protobuf:"bytes,12,opt,name=us_state,json=usState,proto3" json:"us_state,omitempty"`
	VehicleType          *string `protobuf:"bytes,13,opt,name=vehicle_type,json=vehicleType,proto3,oneof" json:"vehicle_type,omitempty"`
	WeatherCondition     *string `protobuf:"bytes,14,opt,name=weather_condition,json=weatherCondition,proto3,oneof" json:"weather_condition,omitempty"`
	City                 *string `protobuf:"bytes,15,opt,name=city,proto3,oneof" json:"city,omitempty"`
}

func (x *CrashRecord) Reset() {
	*x = CrashRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrashRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrashRecord) ProtoMessage() {}

func (x *CrashRecord) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrashRecord.ProtoReflect.Descriptor instead.
func (*CrashRecord) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{6}
}

func (x *CrashRecord) GetAccessControl() string {
	if x != nil && x.AccessControl != nil {
		return *x.AccessControl
	}
	return ""
}

func (x *CrashRecord) GetCargoBodyType() string {
	if x != nil && x.CargoBodyType != nil {
		return *x.CargoBodyType
	}
	return ""
}

func (x *CrashRecord) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *CrashRecord) GetFatalities() int32 {
	if x != nil {
		return x.Fatalities
	}
	return 0
}

func (x *CrashRecord) GetInjuries() int32 {
	if x != nil {
		return x.Injuries
	}
	return 0
}

func (x *CrashRecord) GetLightCondition() string {
	if x != nil && x.LightCondition != nil {
		return *x.LightCondition
	}
	return ""
}

func (x *CrashRecord) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *CrashRecord) GetNotPreventable() bool {
	if x != nil && x.NotPreventable != nil {
		return *x.NotPreventable
	}
	return false
}

func (x *CrashRecord) GetRoadSurfaceCondition() string {
	if x != nil && x.RoadSurfaceCondition != nil {
		return *x.RoadSurfaceCondition
	}
	return ""
}

func (x *CrashRecord) GetTowAway() bool {
	if x != nil {
		return x.TowAway
	}
	return false
}

func (x *CrashRecord) GetTrafficWay() string {
	if x != nil && x.TrafficWay != nil {
		return *x.TrafficWay
	}
	return ""
}

func (x *CrashRecord) GetUsState() string {
	if x != nil {
		return x.UsState
	}
	return ""
}

func (x *CrashRecord) GetVehicleType() string {
	if x != nil && x.VehicleType != nil {
		return *x.VehicleType
	}
	return ""
}

func (x *CrashRecord) GetWeatherCondition() string {
	if x != nil && x.WeatherCondition != nil {
		return *x.WeatherCondition
	}
	return ""
}

func (x *CrashRecord) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

type LargeLoss struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoverageType string  `protobuf:"bytes,1,opt,name=coverage_type,json=coverageType,proto3" json:"coverage_type,omitempty"`
	Date         string  `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	Description  *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	LossIncurred int32   `protobuf:"varint,4,opt,name=loss_incurred,json=lossIncurred,proto3" json:"loss_incurred,omitempty"`
}

func (x *LargeLoss) Reset() {
	*x = LargeLoss{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LargeLoss) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LargeLoss) ProtoMessage() {}

func (x *LargeLoss) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LargeLoss.ProtoReflect.Descriptor instead.
func (*LargeLoss) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{7}
}

func (x *LargeLoss) GetCoverageType() string {
	if x != nil {
		return x.CoverageType
	}
	return ""
}

func (x *LargeLoss) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *LargeLoss) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *LargeLoss) GetLossIncurred() int32 {
	if x != nil {
		return x.LossIncurred
	}
	return 0
}

type MileageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Telematics *int32 `protobuf:"varint,1,opt,name=telematics,proto3,oneof" json:"telematics,omitempty"`
	Value      int32  `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *MileageInfo) Reset() {
	*x = MileageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MileageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MileageInfo) ProtoMessage() {}

func (x *MileageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MileageInfo.ProtoReflect.Descriptor instead.
func (*MileageInfo) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{8}
}

func (x *MileageInfo) GetTelematics() int32 {
	if x != nil && x.Telematics != nil {
		return *x.Telematics
	}
	return 0
}

func (x *MileageInfo) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type PowerUnits struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value int32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *PowerUnits) Reset() {
	*x = PowerUnits{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PowerUnits) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerUnits) ProtoMessage() {}

func (x *PowerUnits) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerUnits.ProtoReflect.Descriptor instead.
func (*PowerUnits) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{9}
}

func (x *PowerUnits) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type DriverNotesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppReviewId        string              `protobuf:"bytes,1,opt,name=app_review_id,json=appReviewId,proto3" json:"app_review_id,omitempty"`
	DriversSummary     *DriversSummary     `protobuf:"bytes,2,opt,name=drivers_summary,json=driversSummary,proto3" json:"drivers_summary,omitempty"`
	Drivers            []*Driver           `protobuf:"bytes,3,rep,name=drivers,proto3" json:"drivers,omitempty"`
	CrashRecordHistory []*CrashRecord      `protobuf:"bytes,4,rep,name=crash_record_history,json=crashRecordHistory,proto3" json:"crash_record_history,omitempty"`
	SevereViolations   []*SevereViolation  `protobuf:"bytes,5,rep,name=severe_violations,json=severeViolations,proto3" json:"severe_violations,omitempty"`
	EquipmentsSummary  []*EquipmentSummary `protobuf:"bytes,6,rep,name=equipments_summary,json=equipmentsSummary,proto3" json:"equipments_summary,omitempty"`
	LargeLosses        []*LargeLoss        `protobuf:"bytes,7,rep,name=large_losses,json=largeLosses,proto3" json:"large_losses,omitempty"`
	Mileage            *MileageInfo        `protobuf:"bytes,8,opt,name=mileage,proto3" json:"mileage,omitempty"`
	PowerUnits         *PowerUnits         `protobuf:"bytes,9,opt,name=power_units,json=powerUnits,proto3" json:"power_units,omitempty"`
}

func (x *DriverNotesRequest) Reset() {
	*x = DriverNotesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DriverNotesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverNotesRequest) ProtoMessage() {}

func (x *DriverNotesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverNotesRequest.ProtoReflect.Descriptor instead.
func (*DriverNotesRequest) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{10}
}

func (x *DriverNotesRequest) GetAppReviewId() string {
	if x != nil {
		return x.AppReviewId
	}
	return ""
}

func (x *DriverNotesRequest) GetDriversSummary() *DriversSummary {
	if x != nil {
		return x.DriversSummary
	}
	return nil
}

func (x *DriverNotesRequest) GetDrivers() []*Driver {
	if x != nil {
		return x.Drivers
	}
	return nil
}

func (x *DriverNotesRequest) GetCrashRecordHistory() []*CrashRecord {
	if x != nil {
		return x.CrashRecordHistory
	}
	return nil
}

func (x *DriverNotesRequest) GetSevereViolations() []*SevereViolation {
	if x != nil {
		return x.SevereViolations
	}
	return nil
}

func (x *DriverNotesRequest) GetEquipmentsSummary() []*EquipmentSummary {
	if x != nil {
		return x.EquipmentsSummary
	}
	return nil
}

func (x *DriverNotesRequest) GetLargeLosses() []*LargeLoss {
	if x != nil {
		return x.LargeLosses
	}
	return nil
}

func (x *DriverNotesRequest) GetMileage() *MileageInfo {
	if x != nil {
		return x.Mileage
	}
	return nil
}

func (x *DriverNotesRequest) GetPowerUnits() *PowerUnits {
	if x != nil {
		return x.PowerUnits
	}
	return nil
}

type DriverNotesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Notes string `protobuf:"bytes,1,opt,name=notes,proto3" json:"notes,omitempty"`
}

func (x *DriverNotesResponse) Reset() {
	*x = DriverNotesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_uw_ai_uw_ai_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DriverNotesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverNotesResponse) ProtoMessage() {}

func (x *DriverNotesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_uw_ai_uw_ai_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverNotesResponse.ProtoReflect.Descriptor instead.
func (*DriverNotesResponse) Descriptor() ([]byte, []int) {
	return file_uw_ai_uw_ai_proto_rawDescGZIP(), []int{11}
}

func (x *DriverNotesResponse) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

var File_uw_ai_uw_ai_proto protoreflect.FileDescriptor

var file_uw_ai_uw_ai_proto_rawDesc = []byte{
	0x0a, 0x11, 0x75, 0x77, 0x5f, 0x61, 0x69, 0x2f, 0x75, 0x77, 0x5f, 0x61, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x75, 0x77, 0x5f, 0x61, 0x69, 0x22, 0xc8, 0x02, 0x0a, 0x09, 0x56,
	0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x17, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x02, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x1d, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x03, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x1b, 0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x04, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x6d, 0x6f, 0x76,
	0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x11, 0x69, 0x73, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x56,
	0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x16, 0x0a,
	0x14, 0x5f, 0x69, 0x73, 0x5f, 0x6d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xeb, 0x02, 0x0a, 0x0a, 0x4d, 0x76, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x01, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x49, 0x73, 0x73, 0x75, 0x65, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x27, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69,
	0x72, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0b, 0x64, 0x61, 0x74,
	0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x64,
	0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52,
	0x07, 0x64, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x16, 0x6d,
	0x6f, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x48, 0x04, 0x52, 0x14, 0x6d,
	0x6f, 0x76, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x0a, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x75, 0x77, 0x5f,
	0x61, 0x69, 0x2e, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x76, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x64, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x64, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x6d, 0x6f, 0x76,
	0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0xe5, 0x04, 0x0a, 0x06, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x17,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x68, 0x69, 0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74,
	0x65, 0x48, 0x69, 0x72, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f,
	0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52,
	0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12,
	0x37, 0x0a, 0x15, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02,
	0x52, 0x13, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x61, 0x74, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x48,
	0x03, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x08, 0x75, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x0b, 0x6d, 0x76,
	0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x75, 0x77, 0x5f, 0x61, 0x69, 0x2e, 0x4d, 0x76, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x0a, 0x6d, 0x76, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x76, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x76, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a,
	0x1f, 0x79, 0x65, 0x61, 0x72, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x1b, 0x79, 0x65, 0x61, 0x72, 0x73, 0x4f,
	0x66, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x17, 0x79, 0x65, 0x61, 0x72,
	0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x6d, 0x76, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x14, 0x79, 0x65, 0x61,
	0x72, 0x73, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x76,
	0x72, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x10, 0x0a,
	0x0e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x42,
	0x18, 0x0a, 0x16, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x61, 0x74,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x42, 0x22, 0x0a, 0x20, 0x5f,
	0x79, 0x65, 0x61, 0x72, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x42,
	0x1a, 0x0a, 0x18, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x65, 0x78, 0x70,
	0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x76, 0x72, 0x22, 0x8c, 0x01, 0x0a, 0x0e,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54,
	0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x6f, 0x66, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x75, 0x72, 0x6e,
	0x6f, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x65, 0x6e, 0x75,
	0x72, 0x65, 0x54, 0x75, 0x72, 0x6e, 0x6f, 0x76, 0x65, 0x72, 0x22, 0x99, 0x01, 0x0a, 0x10, 0x45,
	0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12,
	0x26, 0x0a, 0x0c, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x59, 0x65, 0x61, 0x72, 0x88, 0x01, 0x01, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x76, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x74, 0x69, 0x76, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x69,
	0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x75,
	0x6e, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x6e, 0x69, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x22, 0x86, 0x03, 0x0a, 0x0f, 0x53, 0x65, 0x76, 0x65, 0x72,
	0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x24, 0x69, 0x73,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x73, 0x75, 0x62, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x61, 0x6c, 0x63, 0x6f, 0x68,
	0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x20, 0x69, 0x73, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x64, 0x53, 0x75, 0x62, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x41, 0x6e, 0x64, 0x41, 0x6c, 0x63, 0x6f, 0x68, 0x6f, 0x6c, 0x88, 0x01, 0x01, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x16, 0x0a, 0x06, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x06, 0x69, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x12, 0x2c, 0x0a, 0x0f, 0x69, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x01, 0x52, 0x0e, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52,
	0x0d, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x27, 0x0a, 0x25, 0x5f, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65,
	0x64, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x61, 0x6e, 0x64,
	0x5f, 0x61, 0x6c, 0x63, 0x6f, 0x68, 0x6f, 0x6c, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x69, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x11, 0x0a, 0x0f,
	0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22,
	0xe2, 0x05, 0x0a, 0x0b, 0x43, 0x72, 0x61, 0x73, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x2a, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x63,
	0x61, 0x72, 0x67, 0x6f, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x67, 0x6f, 0x42, 0x6f, 0x64,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x66, 0x61, 0x74, 0x61, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x66, 0x61, 0x74, 0x61, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x6e, 0x6a, 0x75, 0x72, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x69, 0x6e, 0x6a, 0x75, 0x72, 0x69, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x0f, 0x6c, 0x69, 0x67, 0x68,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x02, 0x52, 0x0e, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x0f, 0x6e, 0x6f, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x0e, 0x6e,
	0x6f, 0x74, 0x50, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x39, 0x0a, 0x16, 0x72, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x75, 0x72, 0x66, 0x61, 0x63, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x04, 0x52, 0x14, 0x72, 0x6f, 0x61, 0x64, 0x53, 0x75, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x08, 0x74,
	0x6f, 0x77, 0x5f, 0x61, 0x77, 0x61, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x74,
	0x6f, 0x77, 0x41, 0x77, 0x61, 0x79, 0x12, 0x24, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69,
	0x63, 0x5f, 0x77, 0x61, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x0a, 0x74,
	0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x57, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x08,
	0x75, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0c, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52,
	0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x30, 0x0a, 0x11, 0x77, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x10, 0x77, 0x65,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x17, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x08, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x63, 0x61, 0x72, 0x67, 0x6f, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x70, 0x72,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x72, 0x6f,
	0x61, 0x64, 0x5f, 0x73, 0x75, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63,
	0x5f, 0x77, 0x61, 0x79, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x77, 0x65, 0x61, 0x74, 0x68, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x63, 0x69, 0x74, 0x79, 0x22, 0xa0, 0x01, 0x0a, 0x09, 0x4c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f,
	0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x6f, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6c, 0x6f, 0x73, 0x73, 0x49,
	0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x57, 0x0a, 0x0b, 0x4d, 0x69, 0x6c, 0x65, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0a, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0a, 0x74, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x88, 0x01, 0x01, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x22, 0x22, 0x0a, 0x0a, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0x8b, 0x04, 0x0a, 0x12, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4e,
	0x6f, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x61,
	0x70, 0x70, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12,
	0x3e, 0x0a, 0x0f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x75, 0x77, 0x5f, 0x61, 0x69,
	0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x0e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12,
	0x27, 0x0a, 0x07, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x75, 0x77, 0x5f, 0x61, 0x69, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52,
	0x07, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x12, 0x44, 0x0a, 0x14, 0x63, 0x72, 0x61, 0x73,
	0x68, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x75, 0x77, 0x5f, 0x61, 0x69, 0x2e, 0x43,
	0x72, 0x61, 0x73, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x12, 0x63, 0x72, 0x61, 0x73,
	0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x43,
	0x0a, 0x11, 0x73, 0x65, 0x76, 0x65, 0x72, 0x65, 0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x75, 0x77, 0x5f, 0x61,
	0x69, 0x2e, 0x53, 0x65, 0x76, 0x65, 0x72, 0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x10, 0x73, 0x65, 0x76, 0x65, 0x72, 0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x46, 0x0a, 0x12, 0x65, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x75, 0x77, 0x5f, 0x61, 0x69, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x11, 0x65, 0x71, 0x75, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x33, 0x0a, 0x0c, 0x6c,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x6c, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x75, 0x77, 0x5f, 0x61, 0x69, 0x2e, 0x4c, 0x61, 0x72, 0x67, 0x65, 0x4c,
	0x6f, 0x73, 0x73, 0x52, 0x0b, 0x6c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f, 0x73, 0x73, 0x65, 0x73,
	0x12, 0x2c, 0x0a, 0x07, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x75, 0x77, 0x5f, 0x61, 0x69, 0x2e, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x12, 0x32,
	0x0a, 0x0b, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x75, 0x77, 0x5f, 0x61, 0x69, 0x2e, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x52, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69,
	0x74, 0x73, 0x22, 0x2b, 0x0a, 0x13, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x32,
	0x55, 0x0a, 0x0b, 0x55, 0x57, 0x41, 0x49, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x46,
	0x0a, 0x0b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x19, 0x2e,
	0x75, 0x77, 0x5f, 0x61, 0x69, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x75, 0x77, 0x5f, 0x61, 0x69,
	0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_uw_ai_uw_ai_proto_rawDescOnce sync.Once
	file_uw_ai_uw_ai_proto_rawDescData = file_uw_ai_uw_ai_proto_rawDesc
)

func file_uw_ai_uw_ai_proto_rawDescGZIP() []byte {
	file_uw_ai_uw_ai_proto_rawDescOnce.Do(func() {
		file_uw_ai_uw_ai_proto_rawDescData = protoimpl.X.CompressGZIP(file_uw_ai_uw_ai_proto_rawDescData)
	})
	return file_uw_ai_uw_ai_proto_rawDescData
}

var file_uw_ai_uw_ai_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_uw_ai_uw_ai_proto_goTypes = []interface{}{
	(*Violation)(nil),           // 0: uw_ai.Violation
	(*MvrDetails)(nil),          // 1: uw_ai.MvrDetails
	(*Driver)(nil),              // 2: uw_ai.Driver
	(*DriversSummary)(nil),      // 3: uw_ai.DriversSummary
	(*EquipmentSummary)(nil),    // 4: uw_ai.EquipmentSummary
	(*SevereViolation)(nil),     // 5: uw_ai.SevereViolation
	(*CrashRecord)(nil),         // 6: uw_ai.CrashRecord
	(*LargeLoss)(nil),           // 7: uw_ai.LargeLoss
	(*MileageInfo)(nil),         // 8: uw_ai.MileageInfo
	(*PowerUnits)(nil),          // 9: uw_ai.PowerUnits
	(*DriverNotesRequest)(nil),  // 10: uw_ai.DriverNotesRequest
	(*DriverNotesResponse)(nil), // 11: uw_ai.DriverNotesResponse
}
var file_uw_ai_uw_ai_proto_depIdxs = []int32{
	0,  // 0: uw_ai.MvrDetails.violations:type_name -> uw_ai.Violation
	1,  // 1: uw_ai.Driver.mvr_details:type_name -> uw_ai.MvrDetails
	3,  // 2: uw_ai.DriverNotesRequest.drivers_summary:type_name -> uw_ai.DriversSummary
	2,  // 3: uw_ai.DriverNotesRequest.drivers:type_name -> uw_ai.Driver
	6,  // 4: uw_ai.DriverNotesRequest.crash_record_history:type_name -> uw_ai.CrashRecord
	5,  // 5: uw_ai.DriverNotesRequest.severe_violations:type_name -> uw_ai.SevereViolation
	4,  // 6: uw_ai.DriverNotesRequest.equipments_summary:type_name -> uw_ai.EquipmentSummary
	7,  // 7: uw_ai.DriverNotesRequest.large_losses:type_name -> uw_ai.LargeLoss
	8,  // 8: uw_ai.DriverNotesRequest.mileage:type_name -> uw_ai.MileageInfo
	9,  // 9: uw_ai.DriverNotesRequest.power_units:type_name -> uw_ai.PowerUnits
	10, // 10: uw_ai.UWAIService.DriverNotes:input_type -> uw_ai.DriverNotesRequest
	11, // 11: uw_ai.UWAIService.DriverNotes:output_type -> uw_ai.DriverNotesResponse
	11, // [11:12] is the sub-list for method output_type
	10, // [10:11] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_uw_ai_uw_ai_proto_init() }
func file_uw_ai_uw_ai_proto_init() {
	if File_uw_ai_uw_ai_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_uw_ai_uw_ai_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Violation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_uw_ai_uw_ai_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MvrDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_uw_ai_uw_ai_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Driver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_uw_ai_uw_ai_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DriversSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_uw_ai_uw_ai_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_uw_ai_uw_ai_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SevereViolation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_uw_ai_uw_ai_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrashRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_uw_ai_uw_ai_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LargeLoss); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_uw_ai_uw_ai_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MileageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_uw_ai_uw_ai_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PowerUnits); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_uw_ai_uw_ai_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DriverNotesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_uw_ai_uw_ai_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DriverNotesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_uw_ai_uw_ai_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_uw_ai_uw_ai_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_uw_ai_uw_ai_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_uw_ai_uw_ai_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_uw_ai_uw_ai_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_uw_ai_uw_ai_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_uw_ai_uw_ai_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_uw_ai_uw_ai_proto_msgTypes[8].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_uw_ai_uw_ai_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_uw_ai_uw_ai_proto_goTypes,
		DependencyIndexes: file_uw_ai_uw_ai_proto_depIdxs,
		MessageInfos:      file_uw_ai_uw_ai_proto_msgTypes,
	}.Build()
	File_uw_ai_uw_ai_proto = out.File
	file_uw_ai_uw_ai_proto_rawDesc = nil
	file_uw_ai_uw_ai_proto_goTypes = nil
	file_uw_ai_uw_ai_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// UWAIServiceClient is the client API for UWAIService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UWAIServiceClient interface {
	DriverNotes(ctx context.Context, in *DriverNotesRequest, opts ...grpc.CallOption) (*DriverNotesResponse, error)
}

type uWAIServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUWAIServiceClient(cc grpc.ClientConnInterface) UWAIServiceClient {
	return &uWAIServiceClient{cc}
}

func (c *uWAIServiceClient) DriverNotes(ctx context.Context, in *DriverNotesRequest, opts ...grpc.CallOption) (*DriverNotesResponse, error) {
	out := new(DriverNotesResponse)
	err := c.cc.Invoke(ctx, "/uw_ai.UWAIService/DriverNotes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UWAIServiceServer is the server API for UWAIService service.
type UWAIServiceServer interface {
	DriverNotes(context.Context, *DriverNotesRequest) (*DriverNotesResponse, error)
}

// UnimplementedUWAIServiceServer can be embedded to have forward compatible implementations.
type UnimplementedUWAIServiceServer struct {
}

func (*UnimplementedUWAIServiceServer) DriverNotes(context.Context, *DriverNotesRequest) (*DriverNotesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DriverNotes not implemented")
}

func RegisterUWAIServiceServer(s *grpc.Server, srv UWAIServiceServer) {
	s.RegisterService(&_UWAIService_serviceDesc, srv)
}

func _UWAIService_DriverNotes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DriverNotesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UWAIServiceServer).DriverNotes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/uw_ai.UWAIService/DriverNotes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UWAIServiceServer).DriverNotes(ctx, req.(*DriverNotesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _UWAIService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "uw_ai.UWAIService",
	HandlerType: (*UWAIServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DriverNotes",
			Handler:    _UWAIService_DriverNotes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "uw_ai/uw_ai.proto",
}
