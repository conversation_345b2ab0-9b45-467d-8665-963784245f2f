load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "panels",
    srcs = [
        "drivers.go",
        "equipments.go",
        "financials.go",
        "global.go",
        "losses.go",
        "operations.go",
        "overview.go",
        "package.go",
        "safety.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/app_review/panels",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/underwriting/app_review/widgets/drivers",
        "//nirvana/underwriting/app_review/widgets/equipments",
        "//nirvana/underwriting/app_review/widgets/financials",
        "//nirvana/underwriting/app_review/widgets/global",
        "//nirvana/underwriting/app_review/widgets/losses",
        "//nirvana/underwriting/app_review/widgets/lossesv2",
        "//nirvana/underwriting/app_review/widgets/operations",
        "//nirvana/underwriting/app_review/widgets/overview",
        "//nirvana/underwriting/app_review/widgets/packages",
        "//nirvana/underwriting/app_review/widgets/safety",
        "//nirvana/underwriting/app_review/widgets/safety/scorev2",
        "@org_uber_go_fx//:fx",
    ],
)
