// Code generated by "enumer -type=LossSummaryTag -json"; DO NOT EDIT.

package lossesv2

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _LossSummaryTagName = "LossSummaryTagFileMissingLossSummaryTagFileOutOfDate"

var _LossSummaryTagIndex = [...]uint8{0, 25, 52}

const _LossSummaryTagLowerName = "losssummarytagfilemissinglosssummarytagfileoutofdate"

func (i LossSummaryTag) String() string {
	if i < 0 || i >= LossSummaryTag(len(_LossSummaryTagIndex)-1) {
		return fmt.Sprintf("LossSummaryTag(%d)", i)
	}
	return _LossSummaryTagName[_LossSummaryTagIndex[i]:_LossSummaryTagIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _LossSummaryTagNoOp() {
	var x [1]struct{}
	_ = x[LossSummaryTagFileMissing-(0)]
	_ = x[LossSummaryTagFileOutOfDate-(1)]
}

var _LossSummaryTagValues = []LossSummaryTag{LossSummaryTagFileMissing, LossSummaryTagFileOutOfDate}

var _LossSummaryTagNameToValueMap = map[string]LossSummaryTag{
	_LossSummaryTagName[0:25]:       LossSummaryTagFileMissing,
	_LossSummaryTagLowerName[0:25]:  LossSummaryTagFileMissing,
	_LossSummaryTagName[25:52]:      LossSummaryTagFileOutOfDate,
	_LossSummaryTagLowerName[25:52]: LossSummaryTagFileOutOfDate,
}

var _LossSummaryTagNames = []string{
	_LossSummaryTagName[0:25],
	_LossSummaryTagName[25:52],
}

// LossSummaryTagString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func LossSummaryTagString(s string) (LossSummaryTag, error) {
	if val, ok := _LossSummaryTagNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _LossSummaryTagNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to LossSummaryTag values", s)
}

// LossSummaryTagValues returns all values of the enum
func LossSummaryTagValues() []LossSummaryTag {
	return _LossSummaryTagValues
}

// LossSummaryTagStrings returns a slice of all String values of the enum
func LossSummaryTagStrings() []string {
	strs := make([]string, len(_LossSummaryTagNames))
	copy(strs, _LossSummaryTagNames)
	return strs
}

// IsALossSummaryTag returns "true" if the value is listed in the enum definition. "false" otherwise
func (i LossSummaryTag) IsALossSummaryTag() bool {
	for _, v := range _LossSummaryTagValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for LossSummaryTag
func (i LossSummaryTag) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for LossSummaryTag
func (i *LossSummaryTag) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("LossSummaryTag should be a string, got %s", data)
	}

	var err error
	*i, err = LossSummaryTagString(s)
	return err
}
