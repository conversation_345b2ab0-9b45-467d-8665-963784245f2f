package lossesv2

import (
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

func mergeSummaries(
	agentSummary []application.LossRunSummaryPerCoverage,
	overrides *[]*application.LossRunSummaryPerCoverage,
	aggregation *pibit.Aggregation,
	hasPendingDocuments bool,
) (*LossSummaryResponse, error) {
	filteredOverrides := filterOverrides(overrides)

	var aggregationSummary []pibit.AggregationCoveragePeriodSummary
	if aggregation != nil {
		summary, err := getAggregationSummary(*aggregation)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to get aggregation summary")
		}
		aggregationSummary = summary
	}

	latestCoverageSummaries := make([]CoverageSummary, 0, len(agentSummary))
	for _, agentCoverage := range agentSummary {
		// Find the matching override and aggregation data for the current coverage.
		matchingOverridesSummary := findOverrideCoverageSummary(agentCoverage.CoverageType, filteredOverrides)
		matchingAggregationSummary := findAggregationCoverageSummary(agentCoverage.CoverageType, aggregationSummary)

		coverageSummary := processCoverageSummary(agentCoverage, matchingOverridesSummary, matchingAggregationSummary)
		latestCoverageSummaries = append(latestCoverageSummaries, coverageSummary)
	}

	return &LossSummaryResponse{
		ParsingStatus:           getParsingStatus(hasPendingDocuments),
		LatestCoverageSummaries: latestCoverageSummaries,
	}, nil
}

func processCoverageSummary(
	agentCoverageSummary application.LossRunSummaryPerCoverage,
	overrideCoverageSummary *application.LossRunSummaryPerCoverage,
	aggregationCoverageSummary *pibit.AggregationCoveragePeriodSummary,
) CoverageSummary {
	finalSummaryRecords := make([]LossSummaryRecord, 0, len(agentCoverageSummary.Summary))

	for _, agentRecord := range agentCoverageSummary.Summary {
		// For each agent period, find the corresponding override and aggregation records by looping.
		overrideRecord, overrideExists := findOverridePeriodSummary(agentRecord.PolicyPeriodStartDate, overrideCoverageSummary)
		aggRecord, aggExists := findAggregationPeriodSummary(agentRecord.PolicyPeriodStartDate, aggregationCoverageSummary)

		finalRecord := buildPeriodRecord(agentRecord, aggRecord, overrideRecord, aggExists, overrideExists)
		finalSummaryRecords = append(finalSummaryRecords, finalRecord)
	}

	return CoverageSummary{
		CoverageType: agentCoverageSummary.CoverageType,
		Summary:      finalSummaryRecords,
	}
}

func buildPeriodRecord(
	agentRecord application.LossRunSummaryRecord,
	aggRecord pibit.PeriodSummary,
	overrideRecord application.LossRunSummaryRecord,
	aggExists, overrideExists bool,
) LossSummaryRecord {
	return LossSummaryRecord{
		PeriodStartDate:    agentRecord.PolicyPeriodStartDate,
		PeriodEndDate:      agentRecord.PolicyPeriodEndDate,
		GrossLoss:          determineGrossLoss(agentRecord, aggRecord, overrideRecord, aggExists, overrideExists),
		NumberOfClaims:     determineNumClaims(agentRecord, aggRecord, overrideRecord, aggExists, overrideExists),
		NumberOfPowerUnits: determineNumPowerUnits(agentRecord, overrideRecord, overrideExists),
		// todo @Prabhnoor add tags and loss ratio calculation
	}
}

func determineGrossLoss(agentRec application.LossRunSummaryRecord, aggRec pibit.PeriodSummary, overrideRec application.LossRunSummaryRecord, aggExists, overrideExists bool) LossValue {
	baseValue := LossValue{Value: float32(agentRec.LossIncurred), ValueSource: application.LossRunValueSourceAgent}
	if aggExists {
		baseValue = LossValue{Value: float32(aggRec.GrossLoss), ValueSource: application.LossRunValueSourceParsed}
	}

	if overrideExists && (overrideRec.ValueSources == nil || overrideRec.ValueSources.LossIncurredSource == application.LossRunValueSourceUnderwriter) {
		overrideVal := float32(overrideRec.LossIncurred)
		baseValue.Override = &overrideVal
	}
	return baseValue
}

func determineNumClaims(agentRec application.LossRunSummaryRecord, aggRec pibit.PeriodSummary, overrideRec application.LossRunSummaryRecord, aggExists, overrideExists bool) LossValue {
	baseValue := LossValue{Value: float32(agentRec.NumberOfClaims), ValueSource: application.LossRunValueSourceAgent}
	if aggExists {
		baseValue = LossValue{Value: float32(aggRec.ClaimCount), ValueSource: application.LossRunValueSourceParsed}
	}

	if overrideExists && (overrideRec.ValueSources == nil || overrideRec.ValueSources.NumberOfClaimsSource == application.LossRunValueSourceUnderwriter) {
		overrideVal := float32(overrideRec.NumberOfClaims)
		baseValue.Override = &overrideVal
	}
	return baseValue
}

func determineNumPowerUnits(agentRec, overrideRec application.LossRunSummaryRecord, overrideExists bool) LossValue {
	baseValue := LossValue{Value: float32(agentRec.NumberOfPowerUnits), ValueSource: application.LossRunValueSourceAgent}

	if overrideExists && (overrideRec.ValueSources == nil || overrideRec.ValueSources.NumberOfPowerUnitsSource == application.LossRunValueSourceUnderwriter) {
		overrideVal := float32(overrideRec.NumberOfPowerUnits)
		baseValue.Override = &overrideVal
	}
	return baseValue
}

// findOverrideCoverageSummary loops through the override slice to find a matching coverage.
func findOverrideCoverageSummary(coverageType enums.Coverage, overrides []application.LossRunSummaryPerCoverage) *application.LossRunSummaryPerCoverage {
	for i, o := range overrides {
		if o.CoverageType == coverageType {
			return &overrides[i]
		}
	}
	return nil
}

// findAggregationCoverageSummary loops through the aggregation slice to find a matching coverage.
func findAggregationCoverageSummary(coverageType enums.Coverage, aggregations []pibit.AggregationCoveragePeriodSummary) *pibit.AggregationCoveragePeriodSummary {
	for i, a := range aggregations {
		if a.Coverage == coverageType {
			return &aggregations[i]
		}
	}
	return nil
}

// findOverridePeriodSummary loops through a coverage's summary to find a matching period.
func findOverridePeriodSummary(startDate time.Time, overrideCoverageSummary *application.LossRunSummaryPerCoverage) (application.LossRunSummaryRecord, bool) {
	if overrideCoverageSummary == nil {
		return application.LossRunSummaryRecord{}, false
	}
	for _, record := range overrideCoverageSummary.Summary {
		if record.PolicyPeriodStartDate.Equal(startDate) {
			return record, true
		}
	}
	return application.LossRunSummaryRecord{}, false
}

// findAggregationPeriodSummary loops through a coverage's summary to find a matching period.
func findAggregationPeriodSummary(startDate time.Time, aggregationCoverageSummary *pibit.AggregationCoveragePeriodSummary) (pibit.PeriodSummary, bool) {
	if aggregationCoverageSummary == nil {
		return pibit.PeriodSummary{}, false
	}
	for _, period := range aggregationCoverageSummary.PeriodSummary {
		if period.PeriodStartDate.Equal(startDate) {
			return period, true
		}
	}
	return pibit.PeriodSummary{}, false
}

func filterOverrides(overrides *[]*application.LossRunSummaryPerCoverage) []application.LossRunSummaryPerCoverage {
	if overrides == nil {
		return []application.LossRunSummaryPerCoverage{}
	}
	result := make([]application.LossRunSummaryPerCoverage, 0, len(*overrides))
	for _, override := range *overrides {
		if override != nil {
			result = append(result, *override)
		}
	}
	return result
}

func getParsingStatus(hasPendingDocuments bool) ParsingStatus {
	if hasPendingDocuments {
		return ParsingStatusProcessing
	}
	return ParsingStatusValidated
}

func getAggregationSummary(aggregation pibit.Aggregation) ([]pibit.AggregationCoveragePeriodSummary, error) {
	if aggregation.Summary == nil {
		return nil, errors.Newf("aggregation %s is missing summary data", aggregation.ID)
	}
	return aggregation.Summary.PeriodSummary, nil
}
