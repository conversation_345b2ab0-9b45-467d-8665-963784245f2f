load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "lossesv2",
    srcs = [
        "get_helpers.go",
        "loss_summary.go",
        "losssummarytag_enumer.go",
        "parsingstatus_enumer.go",
        "types.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/app_review/widgets/lossesv2",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/external/pibit",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/underwriting",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_fx//:fx",
    ],
)
