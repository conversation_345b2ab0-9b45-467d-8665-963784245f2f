package lossesv2

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"

	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/underwriting"
)

type LossSummary struct {
	fx.In

	Deps                  underwriting.ReviewManagerDeps
	ParsedLossRunsWrapper pibit.DataWrapper
}

func (l *LossSummary) Get(ctx context.Context, request GetLossSummaryRequestV2) (*LossSummaryResponse, error) {
	review, err := l.Deps.ApplicationReviewWrapper.GetReview(ctx, request.ApplicationReviewId)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get review %s", request.ApplicationReviewId)
	}

	hasPendingDocs, err := l.hasPendingDocuments(ctx, *review)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to check if review has pending documents")
	}

	aggregation, err := l.Deps.ParsedLossRunWrapper.GetLatestAggregationForAppReview(ctx, request.ApplicationReviewId)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, errors.Wrapf(err, "unable to get latest aggregation for app review %s", request.ApplicationReviewId)
	}

	summary, err := mergeSummaries(review.Submission.LossInfo.LossRunSummary, review.Overrides.LossSummary, aggregation, *hasPendingDocs)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to merge summaries for app review %s", request.ApplicationReviewId)
	}

	return summary, nil
}

func (l *LossSummary) hasPendingDocuments(ctx context.Context, review uw.ApplicationReview) (*bool, error) {
	submissions, err := l.Deps.ApplicationWrapper.GetSubmissionsByAppId(ctx, review.ApplicationID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get submissions for app %s", review.ApplicationID)
	}
	submissionIds := slice_utils.Map(submissions, func(submission application.SubmissionObject) string {
		return submission.ID
	})
	documents, err := l.ParsedLossRunsWrapper.GetDocumentsBySubmissionIds(ctx, submissionIds)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get documents for submissions %v", submissionIds)
	}
	isPending := slice_utils.Any(documents, func(document pibit.Document) bool {
		return document.Status == pibit.DocumentStatusSent
	})
	return &isPending, nil
}
