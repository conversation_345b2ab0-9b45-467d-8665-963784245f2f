// Code generated by "enumer -type=ParsingStatus -json"; DO NOT EDIT.

package lossesv2

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _ParsingStatusName = "ParsingStatusProcessingParsingStatusValidated"

var _ParsingStatusIndex = [...]uint8{0, 23, 45}

const _ParsingStatusLowerName = "parsingstatusprocessingparsingstatusvalidated"

func (i ParsingStatus) String() string {
	if i < 0 || i >= ParsingStatus(len(_ParsingStatusIndex)-1) {
		return fmt.Sprintf("ParsingStatus(%d)", i)
	}
	return _ParsingStatusName[_ParsingStatusIndex[i]:_ParsingStatusIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _ParsingStatusNoOp() {
	var x [1]struct{}
	_ = x[ParsingStatusProcessing-(0)]
	_ = x[ParsingStatusValidated-(1)]
}

var _ParsingStatusValues = []ParsingStatus{ParsingStatusProcessing, ParsingStatusValidated}

var _ParsingStatusNameToValueMap = map[string]ParsingStatus{
	_ParsingStatusName[0:23]:       ParsingStatusProcessing,
	_ParsingStatusLowerName[0:23]:  ParsingStatusProcessing,
	_ParsingStatusName[23:45]:      ParsingStatusValidated,
	_ParsingStatusLowerName[23:45]: ParsingStatusValidated,
}

var _ParsingStatusNames = []string{
	_ParsingStatusName[0:23],
	_ParsingStatusName[23:45],
}

// ParsingStatusString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func ParsingStatusString(s string) (ParsingStatus, error) {
	if val, ok := _ParsingStatusNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _ParsingStatusNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to ParsingStatus values", s)
}

// ParsingStatusValues returns all values of the enum
func ParsingStatusValues() []ParsingStatus {
	return _ParsingStatusValues
}

// ParsingStatusStrings returns a slice of all String values of the enum
func ParsingStatusStrings() []string {
	strs := make([]string, len(_ParsingStatusNames))
	copy(strs, _ParsingStatusNames)
	return strs
}

// IsAParsingStatus returns "true" if the value is listed in the enum definition. "false" otherwise
func (i ParsingStatus) IsAParsingStatus() bool {
	for _, v := range _ParsingStatusValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for ParsingStatus
func (i ParsingStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for ParsingStatus
func (i *ParsingStatus) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("ParsingStatus should be a string, got %s", data)
	}

	var err error
	*i, err = ParsingStatusString(s)
	return err
}
