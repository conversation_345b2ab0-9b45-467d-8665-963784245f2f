// Code generated by "enumer -type=LossRunDocParsedStatus -trimprefix=LossRunDocParsedStatus -json"; DO NOT EDIT.

package losses

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _LossRunDocParsedStatusName = "ProcessedInProgressFailedNoLossRuns"

var _LossRunDocParsedStatusIndex = [...]uint8{0, 9, 19, 25, 35}

const _LossRunDocParsedStatusLowerName = "processedinprogressfailednolossruns"

func (i LossRunDocParsedStatus) String() string {
	if i < 0 || i >= LossRunDocParsedStatus(len(_LossRunDocParsedStatusIndex)-1) {
		return fmt.Sprintf("LossRunDocParsedStatus(%d)", i)
	}
	return _LossRunDocParsedStatusName[_LossRunDocParsedStatusIndex[i]:_LossRunDocParsedStatusIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _LossRunDocParsedStatusNoOp() {
	var x [1]struct{}
	_ = x[Processed-(0)]
	_ = x[InProgress-(1)]
	_ = x[Failed-(2)]
	_ = x[NoLossRuns-(3)]
}

var _LossRunDocParsedStatusValues = []LossRunDocParsedStatus{Processed, InProgress, Failed, NoLossRuns}

var _LossRunDocParsedStatusNameToValueMap = map[string]LossRunDocParsedStatus{
	_LossRunDocParsedStatusName[0:9]:        Processed,
	_LossRunDocParsedStatusLowerName[0:9]:   Processed,
	_LossRunDocParsedStatusName[9:19]:       InProgress,
	_LossRunDocParsedStatusLowerName[9:19]:  InProgress,
	_LossRunDocParsedStatusName[19:25]:      Failed,
	_LossRunDocParsedStatusLowerName[19:25]: Failed,
	_LossRunDocParsedStatusName[25:35]:      NoLossRuns,
	_LossRunDocParsedStatusLowerName[25:35]: NoLossRuns,
}

var _LossRunDocParsedStatusNames = []string{
	_LossRunDocParsedStatusName[0:9],
	_LossRunDocParsedStatusName[9:19],
	_LossRunDocParsedStatusName[19:25],
	_LossRunDocParsedStatusName[25:35],
}

// LossRunDocParsedStatusString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func LossRunDocParsedStatusString(s string) (LossRunDocParsedStatus, error) {
	if val, ok := _LossRunDocParsedStatusNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _LossRunDocParsedStatusNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to LossRunDocParsedStatus values", s)
}

// LossRunDocParsedStatusValues returns all values of the enum
func LossRunDocParsedStatusValues() []LossRunDocParsedStatus {
	return _LossRunDocParsedStatusValues
}

// LossRunDocParsedStatusStrings returns a slice of all String values of the enum
func LossRunDocParsedStatusStrings() []string {
	strs := make([]string, len(_LossRunDocParsedStatusNames))
	copy(strs, _LossRunDocParsedStatusNames)
	return strs
}

// IsALossRunDocParsedStatus returns "true" if the value is listed in the enum definition. "false" otherwise
func (i LossRunDocParsedStatus) IsALossRunDocParsedStatus() bool {
	for _, v := range _LossRunDocParsedStatusValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for LossRunDocParsedStatus
func (i LossRunDocParsedStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for LossRunDocParsedStatus
func (i *LossRunDocParsedStatus) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("LossRunDocParsedStatus should be a string, got %s", data)
	}

	var err error
	*i, err = LossRunDocParsedStatusString(s)
	return err
}
