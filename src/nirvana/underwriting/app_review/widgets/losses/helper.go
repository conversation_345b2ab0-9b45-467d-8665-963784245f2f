package losses

import (
	"context"
	"database/sql"
	"math"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
	uw_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	openapi_underwriting "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	"nirvanatech.com/nirvana/parsed_loss_runs/files"
	"nirvanatech.com/nirvana/underwriting"
	app_review_widgets "nirvanatech.com/nirvana/underwriting/app_review/widgets"
)

const (
	LargeLossThreshold100K = 100 * 1000 // 100k (set by product)
	PrimaryInsuranceType   = "BIPD/Primary"
)

// compareLossForCoverageType returns true if following conditions are met, else returns false:
// 1. whether the given parsed loss row has a valid coverage type
// 2. the coverage type derived in item #1 matches the given coverage type
// 3. if the date of loss of the claim associated with the loss row in focus lies between given start & end date
func compareLossForCoverageType(coverageMapFileWrapper *files.CoverageMapFileWrapper,
	parsedPolicy pibit.PolicyData,
	parsedLoss pibit.LossData,
	parsedDateOfLoss *time.Time,
	details lossDetails,
) bool {
	cValue, valid := getValidClassificationValue(coverageMapFileWrapper.LevelOneCoverageMap,
		coverageMapFileWrapper.LevelTwoCoverageMap, parsedLoss.ClaimLossType, parsedPolicy.Insurer)
	if !valid {
		// no valid claim loss type found, skipping this loss
		return false
	}
	coverageType, ok := claimLossTypeToCoverageTypeMap[cValue]
	if !ok {
		return false
	}
	if parsedDateOfLoss != nil && coverageType == details.coverageType &&
		app_review_widgets.IsInInterval(*parsedDateOfLoss,
			details.policyPeriodStartDate, details.policyPeriodEndDate) {
		return true
	}
	return false
}

// compareLossForInvalidCoverageType finds out the presence of: if there is at least one parsed loss in the
// given start date & end date, where the loss has a computed total incurred > 0,
// but claim loss type is not mapped to any appropriate coverage type (i.e., AL, APD, MTC)
func compareLossForInvalidCoverageType(coverageMapFileWrapper *files.CoverageMapFileWrapper,
	parsedPolicy pibit.PolicyData,
	parsedLoss pibit.LossData,
	parsedDateOfLoss *time.Time,
	details lossDetails,
) bool {
	_, valid := getValidClassificationValue(coverageMapFileWrapper.LevelOneCoverageMap,
		coverageMapFileWrapper.LevelTwoCoverageMap, parsedLoss.ClaimLossType, parsedPolicy.Insurer)
	if parsedDateOfLoss != nil && app_review_widgets.IsInInterval(*parsedDateOfLoss, details.policyPeriodStartDate,
		details.policyPeriodEndDate) {
		if !valid && parsedLoss.TotalIncurred != nil {
			// ComputedTotalIncurred can be negative due to insurer-specific preference on showcasing loss values,
			// therefore, considering absolute value
			if math.Abs(*parsedLoss.TotalIncurred) > 0 {
				return true
			}
		}
	}
	return false
}

// compareLossForCoverageType returns true if following conditions are met, else returns false:
// 1. whether the given parsed loss row has a valid coverage type
// 2. the coverage type derived in item #1 matches the given coverage type
// 3. if the date of loss of the claim associated with the loss row's date
// 4. if computed total incurred matches the loss row's loss incurred & is greater than the large loss threshold
func compareLossOnLossDate(coverageMapFileWrapper *files.CoverageMapFileWrapper,
	parsedPolicy pibit.PolicyData,
	parsedLoss pibit.LossData,
	parsedDateOfLoss *time.Time,
	details lossDetails,
) bool {
	cValue, valid := getValidClassificationValue(coverageMapFileWrapper.LevelOneCoverageMap,
		coverageMapFileWrapper.LevelTwoCoverageMap, parsedLoss.ClaimLossType, parsedPolicy.Insurer)
	if !valid {
		// no valid claim loss type found, skipping this loss
		return false
	}
	coverageType, ok := claimLossTypeToCoverageTypeMap[cValue]
	if !ok {
		return false
	}
	if parsedDateOfLoss != nil && coverageType == details.coverageType &&
		details.lossDate.Equal(*parsedDateOfLoss) {
		totalIncurredInt32 := pointer_utils.Int32(int32(math.Abs(*parsedLoss.TotalIncurred)))
		if *totalIncurredInt32 > LargeLossThreshold100K && details.lossIncurred == *totalIncurredInt32 {
			return true
		}
	}
	return false
}

// comparePolicyAgainstLossOnCoverageType returns true if following conditions are met, else returns false:
// 1. whether the given parsed loss row has a valid coverage type
// 2. the coverage type derived in item #1 matches the given coverage type
// 3. PolicySn's & DocumentId's of parsed policy & parsed loss match
func comparePolicyAgainstLossOnCoverageType(coverageMapFileWrapper *files.CoverageMapFileWrapper,
	entity *parsedLossRunEntity,
	details *lossDetails,
) bool {
	cValue, valid := getValidClassificationValue(coverageMapFileWrapper.LevelOneCoverageMap,
		coverageMapFileWrapper.LevelTwoCoverageMap, entity.loss.ClaimLossType, entity.policy.Insurer)
	if !valid {
		// no valid claim loss type found, skipping this loss
		return false
	}
	derivedCoverageType, ok := claimLossTypeToCoverageTypeMap[cValue]
	if !ok {
		return false
	}
	switch {
	case entity.policy.DocumentId == nil || entity.loss.DocumentId == nil || entity.policy.PolicySn == nil ||
		entity.loss.PolicySn == nil:
		return false
	case derivedCoverageType != details.coverageType:
		return false
	case entity.claim.DateOfLoss == nil || !app_review_widgets.IsInInterval(*entity.claim.DateOfLoss,
		details.policyPeriodStartDate, details.policyPeriodEndDate):
		return false
	default:
		return *entity.policy.DocumentId == *entity.loss.DocumentId && *entity.policy.PolicySn == *entity.loss.PolicySn
	}
}

// getParsedLosses returns all parsed losses for a set of documentIds belonging to the review, if found
// else it returns an empty array
func getParsedLosses(ctx context.Context,
	deps *underwriting.ReviewManagerDeps,
	review *uw_wrapper.ApplicationReview,
) ([]pibit.LossData, error) {
	parsedLosses := make([]pibit.LossData, 0)
	if review.Submission.AdditionalLossInfo != nil {
		for _, f := range review.Submission.AdditionalLossInfo.Files {
			losses, err := deps.ParsedLossRunWrapper.GetAllParsedLossRunLossByDocumentId(ctx, f.Handle.String())
			if err != nil {
				return nil, errors.Wrapf(err, "unable to get parsed losses for docId %s", f.Handle.String())
			}
			parsedLosses = append(parsedLosses, losses...)
		}
	}
	return parsedLosses, nil
}

// getParsedClaimsAsMap returns all parsed claims for a set of documentIds belonging to the review, if found
// in the form of a Map with the key as {documentId, policySn, claimSn}, else it returns an empty Map
func getParsedClaimsAsMap(ctx context.Context,
	deps *underwriting.ReviewManagerDeps,
	review *uw_wrapper.ApplicationReview,
) (map[claimTableUniqueKey]pibit.ClaimData, error) {
	// func(policySn->claimSn) = claim data
	// {documentId, policySn, claimSn} is unique for any given claim row
	claimMap := make(map[claimTableUniqueKey]pibit.ClaimData)
	if review.Submission.AdditionalLossInfo != nil {
		for _, f := range review.Submission.AdditionalLossInfo.Files {
			claims, err := deps.ParsedLossRunWrapper.GetAllParsedLossRunClaimByDocumentId(ctx, f.Handle.String())
			if err != nil {
				return nil, errors.Wrapf(err, "unable to get parsed claims for review %s", review.Id)
			}
			for _, claim := range claims {
				cTableUniqueKey := claimTableUniqueKey{
					policyTableUniqueKey: policyTableUniqueKey{
						documentId: *claim.DocumentId,
						policySn:   *claim.PolicySn,
					},
					claimSn: *claim.ClaimSn,
				}
				claimMap[cTableUniqueKey] = claim
			}
		}
	}
	return claimMap, nil
}

func getParsedPoliciesAsMap(ctx context.Context,
	deps *underwriting.ReviewManagerDeps,
	review *uw_wrapper.ApplicationReview,
) (map[policyTableUniqueKey]pibit.PolicyData, error) {
	// func(documentId->policySn) = policy data
	// {documentId, policySn} is unique for any policy claim row
	policyMap := make(map[policyTableUniqueKey]pibit.PolicyData)
	if review.Submission.AdditionalLossInfo != nil {
		for _, f := range review.Submission.AdditionalLossInfo.Files {
			policies, err := deps.ParsedLossRunWrapper.GetAllParsedLossRunPolicyByDocumentId(ctx, f.Handle.String())
			if err != nil {
				return nil, errors.Wrapf(err, "unable to get parsed policy for review %s", review.Id)
			}
			for _, policy := range policies {
				policyMap[policyTableUniqueKey{documentId: *policy.DocumentId, policySn: *policy.PolicySn}] = policy
			}
		}
	}
	return policyMap, nil
}

// getLossesFromFunc returns all parsed losses as per given compare func
func getLossesFromFunc(coverageMapFileWrapper *files.CoverageMapFileWrapper,
	parsedPolicyMap map[policyTableUniqueKey]pibit.PolicyData,
	parsedClaimMap map[claimTableUniqueKey]pibit.ClaimData,
	details lossDetails,
	compare func(*files.CoverageMapFileWrapper,
		pibit.PolicyData, pibit.LossData, *time.Time, lossDetails) bool,
) ([]pibit.LossData, error) {
	var result []pibit.LossData
	for _, parsedLoss := range details.parsedLosses {
		parsedPolicy, ok := parsedPolicyMap[policyTableUniqueKey{
			documentId: *parsedLoss.DocumentId,
			policySn:   *parsedLoss.PolicySn,
		}]
		if !ok {
			return nil, errors.Newf("caught malformed parsed data for docId: %s, policySn: %d",
				*parsedLoss.DocumentId, *parsedLoss.PolicySn, *parsedLoss.ClaimSn)
		}
		cTableUniqueKey := claimTableUniqueKey{
			policyTableUniqueKey: policyTableUniqueKey{
				documentId: *parsedLoss.DocumentId,
				policySn:   *parsedLoss.PolicySn,
			},
			claimSn: *parsedLoss.ClaimSn,
		}
		parsedClaim, ok := parsedClaimMap[cTableUniqueKey]
		if !ok {
			return nil, errors.Newf("caught malformed claim data for docId: %s, policySn: %d, claimSn: %d",
				*parsedLoss.DocumentId, *parsedLoss.PolicySn, *parsedLoss.ClaimSn)
		}
		if shouldAppend := compare(coverageMapFileWrapper,
			parsedPolicy, parsedLoss, parsedClaim.DateOfLoss, details); shouldAppend {
			result = append(result, parsedLoss)
		}
	}
	return result, nil
}

// areDocumentsParsed checks whether all docs have been parsed or not, and in addition, returns a list of docs
// with their parsed status
func areDocumentsParsed(ctx context.Context,
	deps *underwriting.ReviewManagerDeps,
	review *uw_wrapper.ApplicationReview,
) (bool, []lossRunDocInfo, error) {
	var documentList []lossRunDocInfo
	if review.Submission.AdditionalLossInfo == nil || review.Submission.AdditionalLossInfo.Files == nil {
		// when no loss files are present in the submission
		return false, documentList, nil
	}
	for _, f := range review.Submission.AdditionalLossInfo.Files {
		doc, err := deps.ParsedLossRunWrapper.GetParsedLossRunDocument(ctx, (*f.Handle).String())
		if err != nil {
			if !errors.Is(err, sql.ErrNoRows) {
				return false, documentList, errors.Wrapf(err, "unable to get document for document Id: %s",
					f.Handle.String())
			}
		}
		if doc == nil {
			// this case can occur if the triggerParseLossRuns job has failed partially or in total,
			// or job has not been initiated yet
			documentList = append(documentList, lossRunDocInfo{
				name:            f.Name,
				handle:          f.Handle,
				docParsedStatus: InProgress,
			})
			continue
		}
		var lossRunDocParsedStatus LossRunDocParsedStatus
		var errorCode *pibit.DocumentErrorCode
		switch doc.Status {
		case pibit.DocumentStatusSent:
			lossRunDocParsedStatus = InProgress
		case pibit.DocumentStatusReceived:
			lossRunDocParsedStatus = Processed
		case pibit.DocumentStatusNoLossRuns:
			lossRunDocParsedStatus = NoLossRuns
		case pibit.DocumentStatusErrored:
			if endsWithPDF(f.Name) {
				lossRunDocParsedStatus = Failed
				if doc.ErrorInfo != nil {
					errorCode = doc.ErrorInfo.ErrorCode
				}
			} else {
				// non-pdf files are essentially not loss run files, hence marking them as Processed to not block
				// our underwriters. non-pdf files (usually, MS Excel ones) are agent created loss summaries
				lossRunDocParsedStatus = Processed
			}
		default:
			return false, nil, errors.Newf("%+v not handled for loss run doc parsed status", doc.Status)
		}
		documentList = append(documentList, lossRunDocInfo{
			name:            f.Name,
			handle:          f.Handle,
			docParsedStatus: lossRunDocParsedStatus,
			errorCode:       errorCode,
			document:        doc,
		})
	}
	if len(documentList) == 0 {
		return false, documentList, nil
	}
	for _, d := range documentList {
		if d.docParsedStatus != Processed {
			return false, documentList, nil
		}
	}
	return false, documentList, nil
}

// Level 1 classification check is against claim loss type only
//
// Level 2 classification check is against a set of (insurer, claim loss type), only if mapped
// output from the level 1 classification is "Need more info". "Need more info" value is set in stone by
// data science team, and we do not expect any variation/deviation to this value.
func getValidClassificationValue(
	levelOneCoverageMap files.LevelOneCoverageMap,
	levelTwoCoverageMap files.LevelTwoCoverageMap,
	claimLossType *string,
	insurer *string,
) (string, bool) {
	if claimLossType == nil {
		return "", false
	}
	levelOneClassificationValue, isLevelOneCoveragePresent := levelOneCoverageMap[cleanAndLower(*claimLossType)]
	if !isLevelOneCoveragePresent {
		// claim loss type is not present in coverage map, skip this loss run
		return "", false
	}
	_, isLevelOneCoverageValid := allowedClaimLossTypeMap[levelOneClassificationValue]
	if !isLevelOneCoverageValid {
		// claim loss type is not allowed as per level 1 classification, i.e., it is neither AL, APD nor MTC
		// let's check for level 2 classification, i.e., against (insurer, claim loss type)
		if levelOneClassificationValue == "Need more info" && insurer != nil {
			levelTwoClassificationValue, isLevelTwoCoveragePresent := levelTwoCoverageMap[files.LevelTwoCoverageMapKey{
				Insurer:       cleanAndLower(*insurer),
				ClaimLossType: cleanAndLower(*claimLossType),
			}]
			if isLevelTwoCoveragePresent {
				_, isLevelTwoCoverageValid := allowedClaimLossTypeMap[levelTwoClassificationValue]
				if isLevelTwoCoverageValid {
					// level 2 classification succeeds
					return levelTwoClassificationValue, true
				}
			}
		}
		return "", false
	}

	// level 1 classification succeeds
	return levelOneClassificationValue, true
}

func cleanAndLower(inputString string) string {
	// Remove non-alphanumeric characters using regular expression
	reg := regexp.MustCompile("[^a-zA-Z0-9]+")
	cleanedString := reg.ReplaceAllString(inputString, "")

	// Convert the string to lowercase
	lowerCleanedString := strings.ToLower(cleanedString)

	return lowerCleanedString
}

func isWithinOnePercentDifference(a, b int32) bool {
	average := float64(a+b) / 2.0
	maxDifference := 0.01 * average // 1% of the average

	difference := math.Abs(float64(a - b))

	return difference <= maxDifference
}

// getPoliciesForPeriod gets all policies which lie in the given agent's effYear & expYear, based on conditions:
//  1. Parsed effYear is not null and greater than 2008
//  2. Assume parsed expYear as effYear + 1 when null. Parsed effYear if not null, is less than current year + 1.
//  3. Parsed expYear >= Parsed effYear
//  4. Parsed effYear <= agent input row effYear (to take into account multi-year policies)
//  5. Parsed expYear >= agent input row expYear (to take into account multi-year policies)
//
// Side-Note: effYear is effective year, expYear is expiry year
func getPoliciesForPeriod(clk clock.Clock, parsedPolicyMap map[policyTableUniqueKey]pibit.PolicyData,
	effYear, expYear int,
) []pibit.PolicyData {
	var result []pibit.PolicyData
	for _, policy := range parsedPolicyMap {
		if policy.EffDate == nil || policy.EffDate.Year() <= 2008 {
			continue
		}
		parsedEffYear := policy.EffDate.Year()
		parsedExpYear := parsedEffYear + 1
		if policy.ExpDate != nil {
			if policy.ExpDate.Year() > clk.Now().Year()+1 {
				continue
			} else {
				parsedExpYear = policy.ExpDate.Year()
			}
		}
		if !(parsedExpYear >= parsedEffYear) {
			continue
		}
		if !(parsedEffYear <= effYear) {
			continue
		}
		if !(parsedExpYear >= expYear) {
			continue
		}
		result = append(result, policy)
	}
	return result
}

// computeLossIncurredFromLosses iterates throw given parsed losses and returns the total parsed loss incurred value
func computeLossIncurredFromLosses(losses []pibit.LossData) *int32 {
	var parsedLossIncurred *int32
	for _, loss := range losses {
		// TotalIncurred can be negative due to insurer-specific preference on showcasing loss values,
		// therefore, considering absolute value
		totalIncurredInt32 := pointer_utils.Int32(int32(math.Abs(*loss.TotalIncurred)))
		if parsedLossIncurred == nil {
			// ComputedTotalIncurred fraction is discarded
			parsedLossIncurred = totalIncurredInt32
		} else {
			*parsedLossIncurred = *parsedLossIncurred + *totalIncurredInt32
		}
	}
	return parsedLossIncurred
}

func getLossRunFileMetadataList(docInfoList []lossRunDocInfo) ([]openapi_underwriting.LossRunFileMetadata, error) {
	var metadataList []openapi_underwriting.LossRunFileMetadata
	for _, docInfo := range docInfoList {
		var status openapi_underwriting.LossRunFileParsedStatus
		var errorCode *openapi_underwriting.LossRunFileErrorCode
		switch docInfo.docParsedStatus {
		case Processed:
			status = openapi_underwriting.Processed
		case NoLossRuns:
			status = openapi_underwriting.Failed
		case InProgress:
			status = openapi_underwriting.InProgress
		case Failed:
			status = openapi_underwriting.Failed
			if docInfo.errorCode != nil {
				var err error
				errorCode, err = getOApiErrorCodeFromParsedDocument(*docInfo.errorCode)
				if err != nil {
					return nil, errors.Newf("cannot get open-api error code for doc: %s", docInfo.name)
				}
			}
		default:
			return nil, errors.Newf("status %+v not handled for doc: %s", docInfo.docParsedStatus, docInfo.name)
		}
		fileMetaData := openapi_underwriting.LossRunFileMetadata{
			FileHandle:   docInfo.handle.String(),
			FileName:     docInfo.name,
			ParsedStatus: status,
			ErrorCode:    errorCode,
		}
		metadataList = append(metadataList, fileMetaData)
	}
	return metadataList, nil
}

func getOApiErrorCodeFromParsedDocument(errorCode pibit.DocumentErrorCode) (*openapi_underwriting.LossRunFileErrorCode,
	error,
) {
	var oApiErrorCode *openapi_underwriting.LossRunFileErrorCode
	switch errorCode {
	case pibit.ErrorInvalidFile:
		oApiErrorCode = pointer_utils.ToPointer(openapi_underwriting.InvalidFile)
	case pibit.ErrorIncompleteFile:
		oApiErrorCode = pointer_utils.ToPointer(openapi_underwriting.IncompleteFile)
	case pibit.ErrorUnparsableFile:
		oApiErrorCode = pointer_utils.ToPointer(openapi_underwriting.UnparsableFile)
	case pibit.ErrorNoRowsPresent:
		oApiErrorCode = pointer_utils.ToPointer(openapi_underwriting.NoRowsPresent)
	default:
		return nil, errors.Newf("error code %+v not handled", errorCode)
	}
	return oApiErrorCode, nil
}

// getLossRunDocumentsParsingPeriod returns the processing start date & end date from the given doc info list
func getLossRunDocumentsParsingPeriod(docInfoList []lossRunDocInfo) (*time.Time, *time.Time) {
	var startDate, endDate *time.Time
	for _, docInfo := range docInfoList {
		if docInfo.document != nil {
			doc := docInfo.document
			if startDate == nil || doc.CreatedAt.Before(*startDate) {
				startDate = &doc.CreatedAt
			}
			if doc.FirstUpdatedAt != nil && (endDate == nil || doc.FirstUpdatedAt.After(*endDate)) {
				endDate = doc.FirstUpdatedAt
			}
		}
	}
	return startDate, endDate
}

func endsWithPDF(filename string) bool {
	lowerCaseFilename := strings.ToLower(filename)
	return strings.HasSuffix(lowerCaseFilename, ".pdf")
}

func sortApplicationReviewLossSummary(summary *openapi_underwriting.ApplicationReviewLossSummary) {
	priority := map[string]int{
		oApiCoverageTypeAutoLiability:      1,
		oApiCoverageTypeAutoPhysicalDamage: 2,
		oApiCoverageTypeMotorTruckCargo:    3,
	}

	sort.SliceStable(summary.Value, func(i, j int) bool {
		priorityI, okI := priority[summary.Value[i].CoverageType]
		priorityJ, okJ := priority[summary.Value[j].CoverageType]

		// Entries with a defined priority take precedence over those without.
		if okI && !okJ {
			return true
		}
		if !okI && okJ {
			return false
		}

		// Both have defined priorities or neither has one; sort by priority.
		if okI && okJ {
			return priorityI < priorityJ
		}

		// If both are undefined, retain original order.
		return false
	})
}
