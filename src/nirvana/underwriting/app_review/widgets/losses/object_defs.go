package losses

import (
	"time"

	"github.com/google/uuid"

	coverage_enum "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

// lossDetails is the definition of parameter obj to calculate loss incurred within dates/on date
type lossDetails struct {
	coverageType          coverage_enum.Coverage
	policyPeriodStartDate time.Time
	policyPeriodEndDate   time.Time
	lossDate              time.Time
	lossIncurred          int32
	parsedLosses          []pibit.LossData
}

// policyDetails holds parsed policy-related entities
type policyDetails struct {
	parsedPolicyMap map[policyTableUniqueKey]pibit.PolicyData
	dotNumber       int64
}

// policyTableUniqueKey - Key to map for: func(documentId, policySn) = parsed policy data
type policyTableUniqueKey struct {
	documentId string
	policySn   int32
}

// claimTableUniqueKey - Key to map for: func(documentId, policySn, claimSn) = parsed claim data
type claimTableUniqueKey struct {
	policyTableUniqueKey
	claimSn int32
}

// lossRunDocInfo holds info about loss run documents and their corresponding parsed status, along with original doc
type lossRunDocInfo struct {
	name            string
	handle          *uuid.UUID
	docParsedStatus LossRunDocParsedStatus
	errorCode       *pibit.DocumentErrorCode
	document        *pibit.Document
}

// parsedLossRunEntity holds the loss data & associated policy & claim data which makes up the loss
type parsedLossRunEntity struct {
	policy pibit.PolicyData
	claim  pibit.ClaimData
	loss   pibit.LossData
}

type LossRunDocParsedStatus int

//go:generate go run github.com/dmarkham/enumer -type=LossRunDocParsedStatus -trimprefix=LossRunDocParsedStatus -json
const (
	Processed LossRunDocParsedStatus = iota
	InProgress
	Failed
	NoLossRuns
)
