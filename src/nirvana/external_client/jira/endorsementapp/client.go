package endorsementapp

import (
	"context"
	"fmt"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/ctreminiom/go-atlassian/pkg/infra/models"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/common/application"
	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	"nirvanatech.com/nirvana/api-server/handlers/common/ib"
	"nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	"nirvanatech.com/nirvana/common-go/log"
	dbendorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-bundle/model/helper"
)

type fieldType int

const (
	FieldTypeUser fieldType = iota
	FieldTypeDateTimeUTC
	FieldTypeMultiSelect
	FieldTypeText
)

// fieldDefinition stores metadata about a custom field
type fieldDefinition struct {
	ID        string
	FieldType fieldType
}

const (
	serviceDeskID      = "7"  // NFLT-447
	requestTypeID      = "65" // Endorsement Request
	requestTypeQuoteID = "73" // Quote Request
)

func CreateNFEndorsementJiraTicket(
	ctx context.Context,
	deps deps.Deps,
	policyProcessor endorsement.PolicyProcessor,
	endReqObj *dbendorsementrequest.Request,
	bundle *model.InsuranceBundle,
	endorsementReviewID uuid.UUID,
	isQuoteRequested *bool,
) error {
	log.Info(ctx, "starting endorsement jira ticket creation",
		log.String("endorsement_request_id", endReqObj.ID.String()))

	defer recordJiraTicketManagerDuration(ctx, deps.MetricsClient, operationCreateJiraTicket)

	ibLastSegment := bundle.GetLastSegment()
	if ibLastSegment == nil {
		return errors.New(ib.ErrGetLastSegment.Error())
	}

	autoLiabilityPolicy, err := policyProcessor.ExtractAutoLiabilityPolicy(ibLastSegment)
	if err != nil {
		return errors.Wrapf(err, "failed to extract auto liability policy")
	}

	payload, err := prepareJiraPayload(ctx, deps, endReqObj, bundle, autoLiabilityPolicy, ibLastSegment, endorsementReviewID, isQuoteRequested)
	if err != nil {
		return errors.Wrap(err, "failed to prepare jira payload")
	}

	if err = createJiraIssue(ctx, deps, payload); err != nil {
		return errors.Wrap(err, "failed to create jira issue")
	}

	return nil
}

func prepareJiraPayload(
	ctx context.Context,
	deps deps.Deps,
	endorsementRequest *dbendorsementrequest.Request,
	insuranceBundle *model.InsuranceBundle,
	autoLiabilityPolicy *model.Policy,
	ibLastSegment *model.InsuranceBundleSegment,
	endorsementReviewID uuid.UUID,
	isQuoteRequested *bool,
) (*models.CreateCustomerRequestPayloadScheme, error) {
	changeTypes, err := extractChangeTypes(endorsementRequest)
	if err != nil {
		return nil, errors.Wrap(err, "failed to extract change types")
	}

	producerInfo, err := deps.AuthWrapper.FetchUserInfo(ctx, endorsementRequest.CreatedBy)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get producer info")
	}

	uwJiraUser, err := fetchUnderwriterInfo(ctx, deps, insuranceBundle)
	if err != nil {
		return nil, err
	}

	requestTypeId := requestTypeID
	//if isUWQuoteGenerationNeeded(isQuoteRequested, endorsementRequest) {
	//	log.Info(ctx, "Creating jira ticket with quote request flag")
	//	requestTypeId = requestTypeQuoteID
	//}

	payload := &models.CreateCustomerRequestPayloadScheme{
		RequestParticipants: nil,
		ServiceDeskID:       serviceDeskID,
		RequestTypeID:       requestTypeId,
	}

	if err = populateCustomFields(
		payload,
		autoLiabilityPolicy,
		endorsementRequest,
		producerInfo,
		uwJiraUser,
		ibLastSegment,
		changeTypes,
		endorsementReviewID,
	); err != nil {
		return nil, errors.Wrap(err, "failed to populate custom fields")
	}

	return payload, nil
}

//
//func isUWQuoteGenerationNeeded(isQuoteRequested *bool, endorsementRequest *dbendorsementrequest.Request) bool {
//	if isQuoteRequested != nil {
//		// If pricing jobber failed and agent requested quote
//		return *isQuoteRequested
//	}
//	if endorsementRequest.QuoteGenerationInfo == nil {
//		// If quote generation was not attempted for the endorsement request
//		return true
//	}
//	return false
//}

func extractChangeTypes(endorsementRequest *dbendorsementrequest.Request) ([]string, error) {
	var changeTypes []string
	for i := range endorsementRequest.Changes {
		describer, err := helper.GetChangeDataDescriber(endorsementRequest.Changes[i].GetData())
		if err != nil {
			return nil, errors.Wrap(err, "failed to get change data describer")
		}

		// We don't want to include ClauseChange in the change types
		if describer.GetChangeType() != "ClauseChange" {
			changeTypes = append(changeTypes, describer.GetChangeType())
		}

	}

	if len(changeTypes) == 0 {
		return nil, errors.New("no change types found")
	}
	return changeTypes, nil
}

func fetchUnderwriterInfo(
	ctx context.Context,
	deps deps.Deps,
	insuranceBundle *model.InsuranceBundle,
) (*models.UserScheme, error) {
	originalApplicationID, err := uuid.Parse(insuranceBundle.GetMetadata().RootApplicationId)
	if err != nil {
		return nil, errors.Wrap(err, application.ErrParseApplicationID.Error())
	}

	underwriterID, err := application.GetUnderwriterID(
		ctx,
		deps.ApplicationWrapper,
		deps.AdmittedAppWrapper,
		originalApplicationID,
		insuranceBundle.GetProgramType(),
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get underwriter ID")
	}

	uwInfo, err := deps.AuthWrapper.FetchUserInfo(ctx, underwriterID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get underwriter info")
	}

	uwJiraUser, err := deps.JiraPlatform.GetUserByEmail(ctx, uwInfo.Email)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get underwriter jira user")
	}

	return uwJiraUser, nil
}

func createJiraIssue(
	ctx context.Context,
	deps deps.Deps,
	payload *models.CreateCustomerRequestPayloadScheme,
) error {
	resp, err := deps.JiraSDClient.CreateIssue(ctx, payload)
	if err != nil {
		log.Error(ctx, "failed to create jira ticket for endorsement review",
			log.Err(err),
			log.Any("payload", payload))
		incrementJiraTicketManagerCounterMetric(ctx, deps.MetricsClient, operationCreateJiraTicket, common.MetricTagValueFailure)
		return errors.Wrap(err, "failed to create jira ticket")
	}

	log.Info(ctx, "successfully created jira ticket",
		log.String("issue_id", resp.IssueID),
		log.String("issue_key", resp.IssueKey))
	incrementJiraTicketManagerCounterMetric(ctx, deps.MetricsClient, operationCreateJiraTicket, common.MetricTagValueSuccess)
	return nil
}

func populateCustomFields(
	payload *models.CreateCustomerRequestPayloadScheme,
	autoLiabilityPolicy *model.Policy,
	endorsementRequest *dbendorsementrequest.Request,
	producerInfo *authz.UserInfo,
	uwJiraUser *models.UserScheme,
	ibLastSegment *model.InsuranceBundleSegment,
	changeTypes []string,
	endorsementReviewID uuid.UUID,
) error {
	customFields := getCustomFieldMap()

	summary := fmt.Sprintf("%s - %s",
		ibLastSegment.GetPrimaryInsured().GetName().GetBusinessName(),
		autoLiabilityPolicy.GetPolicyNumber(),
	)

	dotNumbers := ibLastSegment.GetPrimaryInsured().GetExternalIdentifier().GetValue()
	if len(dotNumbers) == 0 {
		return errors.New("no DOT numbers found")
	}

	link := fmt.Sprintf("https://underwriter.nirvanatech.com/non-fleet-v2/endorsements/%s/",
		endorsementReviewID.String())

	description := ""
	for i := range endorsementRequest.Changes {
		description += endorsementRequest.Changes[i].Description + "\n"
	}

	fieldMappings := map[fieldDefinition]interface{}{
		{ID: customFields["Summary"], FieldType: FieldTypeText}:                   summary,
		{ID: customFields["EffectiveDate"], FieldType: FieldTypeDateTimeUTC}:      *endorsementRequest.DefaultEffectiveDate,
		{ID: customFields["ChangeType/s"], FieldType: FieldTypeMultiSelect}:       changeTypes,
		{ID: customFields["DOT"], FieldType: FieldTypeText}:                       dotNumbers[0],
		{ID: customFields["AutoLiabilityPolicyNumber"], FieldType: FieldTypeText}: autoLiabilityPolicy.GetPolicyNumber(),
		{ID: customFields["Underwriter"], FieldType: FieldTypeUser}:               uwJiraUser.AccountID,
		{ID: customFields["Link"], FieldType: FieldTypeText}:                      link,
		{ID: customFields["AgentEmail"], FieldType: FieldTypeText}:                producerInfo.Email,
		{ID: customFields["Description"], FieldType: FieldTypeText}:               description,
		{ID: customFields["IsRequestedViaAPI"], FieldType: FieldTypeMultiSelect}:  []string{"Yes"},
	}

	for field, value := range fieldMappings {
		if err := setCustomField(payload, field, value); err != nil {
			return errors.Wrapf(err, "failed to set custom field %s", field.ID)
		}
	}

	return nil
}

func setCustomField(
	payload *models.CreateCustomerRequestPayloadScheme,
	field fieldDefinition,
	value interface{},
) error {
	switch field.FieldType {
	case FieldTypeUser:
		return payload.UserCustomField(field.ID, value.(string))
	case FieldTypeDateTimeUTC:
		return payload.DateCustomField(field.ID, value.(time.Time))
	case FieldTypeMultiSelect:
		return payload.MultiSelectOrCheckBoxCustomField(field.ID, value.([]string))
	case FieldTypeText:
		return payload.AddCustomField(field.ID, value.(string))
	default:
		return errors.New("unsupported field type")
	}
}

func getCustomFieldMap() map[string]string {
	return map[string]string{
		"ApplicableLinesOfCoverage": "customfield_10118", // customfieldtypes:multicheckboxes
		"EffectiveDate":             "customfield_10073", // customfieldtypes:datepicker
		"ChangeTypes":               "customfield_10170", // customfieldtypes:select
		"ChangeType/s":              "customfield_10180", // customfieldtypes:multiselect
		"DOT":                       "customfield_10184", // customfieldtypes:textfield
		"AutoLiabilityPolicyNumber": "customfield_10130", // customfieldtypes:textfield
		"Underwriter":               "customfield_10117", // customfieldtypes:userpicker
		"AgentEmail":                "customfield_10083", // customfieldtypes:textfield
		"PremiumBearing":            "customfield_10080", // customfieldtypes:select
		"Link":                      "customfield_10200", // customfieldtypes:textfield
		"AutoLiability":             "customfield_10132", // customfieldtypes:textfield
		"AutoPhysicalDamage":        "customfield_10133", // customfieldtypes:textfield
		"GeneralLiability":          "customfield_10134", // customfieldtypes:textfield
		"MotorTruckCargo":           "customfield_10135", // customfieldtypes:textfield
		"IsRequestedViaAPI":         "customfield_10202", // customfieldtypes:checkbox
		"Summary":                   "summary",           // summary text
		"DueDate":                   "duedate",           // due date datepicker
		"Description":               "description",       // description text
	}
}
