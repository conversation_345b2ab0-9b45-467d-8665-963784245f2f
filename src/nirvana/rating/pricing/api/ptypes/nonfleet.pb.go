// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pricing/nonfleet.proto

package ptypes

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	model "nirvanatech.com/nirvana/nonfleet/model"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NonFleet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NonFleet) Reset() {
	*x = NonFleet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet) ProtoMessage() {}

func (x *NonFleet) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet.ProtoReflect.Descriptor instead.
func (*NonFleet) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0}
}

type NonFleet_BundleChunkSpecData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CombinedDeductibleSpecs []*CombinedDeductibleSpec `protobuf:"bytes,1,rep,name=combinedDeductibleSpecs,proto3" json:"combinedDeductibleSpecs,omitempty"`
}

func (x *NonFleet_BundleChunkSpecData) Reset() {
	*x = NonFleet_BundleChunkSpecData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_BundleChunkSpecData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_BundleChunkSpecData) ProtoMessage() {}

func (x *NonFleet_BundleChunkSpecData) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_BundleChunkSpecData.ProtoReflect.Descriptor instead.
func (*NonFleet_BundleChunkSpecData) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 0}
}

func (x *NonFleet_BundleChunkSpecData) GetCombinedDeductibleSpecs() []*CombinedDeductibleSpec {
	if x != nil {
		return x.CombinedDeductibleSpecs
	}
	return nil
}

type NonFleet_PolicyChunkSpecData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommoditiesInfo  *NonFleet_CommoditiesInfo  `protobuf:"bytes,9,opt,name=commoditiesInfo,proto3" json:"commoditiesInfo,omitempty"`
	Company          *NonFleet_Company          `protobuf:"bytes,10,opt,name=company,proto3" json:"company,omitempty"`
	UnderwriterInput *NonFleet_UnderwriterInput `protobuf:"bytes,11,opt,name=underwriterInput,proto3" json:"underwriterInput,omitempty"`
	Drivers          []*NonFleet_Driver         `protobuf:"bytes,12,rep,name=drivers,proto3" json:"drivers,omitempty"`
	Vehicles         []*NonFleet_Vehicle        `protobuf:"bytes,13,rep,name=vehicles,proto3" json:"vehicles,omitempty"`
	UnclassifiedData *NonFleet_UnclassifiedData `protobuf:"bytes,14,opt,name=unclassifiedData,proto3" json:"unclassifiedData,omitempty"`
	ArtifactConfig   *ArtifactConfig            `protobuf:"bytes,15,opt,name=artifactConfig,proto3" json:"artifactConfig,omitempty"`
}

func (x *NonFleet_PolicyChunkSpecData) Reset() {
	*x = NonFleet_PolicyChunkSpecData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_PolicyChunkSpecData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_PolicyChunkSpecData) ProtoMessage() {}

func (x *NonFleet_PolicyChunkSpecData) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_PolicyChunkSpecData.ProtoReflect.Descriptor instead.
func (*NonFleet_PolicyChunkSpecData) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 1}
}

func (x *NonFleet_PolicyChunkSpecData) GetCommoditiesInfo() *NonFleet_CommoditiesInfo {
	if x != nil {
		return x.CommoditiesInfo
	}
	return nil
}

func (x *NonFleet_PolicyChunkSpecData) GetCompany() *NonFleet_Company {
	if x != nil {
		return x.Company
	}
	return nil
}

func (x *NonFleet_PolicyChunkSpecData) GetUnderwriterInput() *NonFleet_UnderwriterInput {
	if x != nil {
		return x.UnderwriterInput
	}
	return nil
}

func (x *NonFleet_PolicyChunkSpecData) GetDrivers() []*NonFleet_Driver {
	if x != nil {
		return x.Drivers
	}
	return nil
}

func (x *NonFleet_PolicyChunkSpecData) GetVehicles() []*NonFleet_Vehicle {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

func (x *NonFleet_PolicyChunkSpecData) GetUnclassifiedData() *NonFleet_UnclassifiedData {
	if x != nil {
		return x.UnclassifiedData
	}
	return nil
}

func (x *NonFleet_PolicyChunkSpecData) GetArtifactConfig() *ArtifactConfig {
	if x != nil {
		return x.ArtifactConfig
	}
	return nil
}

type NonFleet_CommoditiesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrimaryCommodityCategory model.CommodityCategory     `protobuf:"varint,1,opt,name=primaryCommodityCategory,proto3,enum=nonfleet_model.CommodityCategory" json:"primaryCommodityCategory,omitempty"`
	Records                  []*NonFleet_CommodityRecord `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *NonFleet_CommoditiesInfo) Reset() {
	*x = NonFleet_CommoditiesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_CommoditiesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_CommoditiesInfo) ProtoMessage() {}

func (x *NonFleet_CommoditiesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_CommoditiesInfo.ProtoReflect.Descriptor instead.
func (*NonFleet_CommoditiesInfo) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 2}
}

func (x *NonFleet_CommoditiesInfo) GetPrimaryCommodityCategory() model.CommodityCategory {
	if x != nil {
		return x.PrimaryCommodityCategory
	}
	return model.CommodityCategory(0)
}

func (x *NonFleet_CommoditiesInfo) GetRecords() []*NonFleet_CommodityRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

type NonFleet_CommodityRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommodityCategory model.CommodityCategory `protobuf:"varint,1,opt,name=commodityCategory,proto3,enum=nonfleet_model.CommodityCategory" json:"commodityCategory,omitempty"`
	PercentageOfHauls int64                   `protobuf:"varint,2,opt,name=percentageOfHauls,proto3" json:"percentageOfHauls,omitempty"`
}

func (x *NonFleet_CommodityRecord) Reset() {
	*x = NonFleet_CommodityRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_CommodityRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_CommodityRecord) ProtoMessage() {}

func (x *NonFleet_CommodityRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_CommodityRecord.ProtoReflect.Descriptor instead.
func (*NonFleet_CommodityRecord) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 3}
}

func (x *NonFleet_CommodityRecord) GetCommodityCategory() model.CommodityCategory {
	if x != nil {
		return x.CommodityCategory
	}
	return model.CommodityCategory(0)
}

func (x *NonFleet_CommodityRecord) GetPercentageOfHauls() int64 {
	if x != nil {
		return x.PercentageOfHauls
	}
	return 0
}

type NonFleet_Company struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Owner                                   *NonFleet_CompanyOwner                   `protobuf:"bytes,1,opt,name=owner,proto3" json:"owner,omitempty"`
	ZipCode                                 string                                   `protobuf:"bytes,2,opt,name=zipCode,proto3" json:"zipCode,omitempty"`
	DotNumber                               string                                   `protobuf:"bytes,3,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	UsState                                 string                                   `protobuf:"bytes,4,opt,name=usState,proto3" json:"usState,omitempty"`
	AnnualCostOfHire                        *int64                                   `protobuf:"varint,5,opt,name=annualCostOfHire,proto3,oneof" json:"annualCostOfHire,omitempty"`
	YearsInBusiness                         float64                                  `protobuf:"fixed64,6,opt,name=yearsInBusiness,proto3" json:"yearsInBusiness,omitempty"`
	TotalPowerUnits                         int64                                    `protobuf:"varint,7,opt,name=totalPowerUnits,proto3" json:"totalPowerUnits,omitempty"`
	AnyCarrierContinuousCoverageRecords     []*NonFleet_ContinuousCoverageRecord     `protobuf:"bytes,8,rep,name=anyCarrierContinuousCoverageRecords,proto3" json:"anyCarrierContinuousCoverageRecords,omitempty"`
	CurrentCarrierContinuousCoverageRecords []*NonFleet_ContinuousCoverageRecord     `protobuf:"bytes,9,rep,name=currentCarrierContinuousCoverageRecords,proto3" json:"currentCarrierContinuousCoverageRecords,omitempty"`
	InspectionsCount                        int64                                    `protobuf:"varint,10,opt,name=inspectionsCount,proto3" json:"inspectionsCount,omitempty"`
	AggregateOOSViolationsInfo              *NonFleet_CompanyAggregateViolationsInfo `protobuf:"bytes,11,opt,name=AggregateOOSViolationsInfo,proto3" json:"AggregateOOSViolationsInfo,omitempty"`
	AggregateNonOOSViolationsInfo           *NonFleet_CompanyAggregateViolationsInfo `protobuf:"bytes,12,opt,name=AggregateNonOOSViolationsInfo,proto3" json:"AggregateNonOOSViolationsInfo,omitempty"`
}

func (x *NonFleet_Company) Reset() {
	*x = NonFleet_Company{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_Company) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_Company) ProtoMessage() {}

func (x *NonFleet_Company) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_Company.ProtoReflect.Descriptor instead.
func (*NonFleet_Company) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 4}
}

func (x *NonFleet_Company) GetOwner() *NonFleet_CompanyOwner {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *NonFleet_Company) GetZipCode() string {
	if x != nil {
		return x.ZipCode
	}
	return ""
}

func (x *NonFleet_Company) GetDotNumber() string {
	if x != nil {
		return x.DotNumber
	}
	return ""
}

func (x *NonFleet_Company) GetUsState() string {
	if x != nil {
		return x.UsState
	}
	return ""
}

func (x *NonFleet_Company) GetAnnualCostOfHire() int64 {
	if x != nil && x.AnnualCostOfHire != nil {
		return *x.AnnualCostOfHire
	}
	return 0
}

func (x *NonFleet_Company) GetYearsInBusiness() float64 {
	if x != nil {
		return x.YearsInBusiness
	}
	return 0
}

func (x *NonFleet_Company) GetTotalPowerUnits() int64 {
	if x != nil {
		return x.TotalPowerUnits
	}
	return 0
}

func (x *NonFleet_Company) GetAnyCarrierContinuousCoverageRecords() []*NonFleet_ContinuousCoverageRecord {
	if x != nil {
		return x.AnyCarrierContinuousCoverageRecords
	}
	return nil
}

func (x *NonFleet_Company) GetCurrentCarrierContinuousCoverageRecords() []*NonFleet_ContinuousCoverageRecord {
	if x != nil {
		return x.CurrentCarrierContinuousCoverageRecords
	}
	return nil
}

func (x *NonFleet_Company) GetInspectionsCount() int64 {
	if x != nil {
		return x.InspectionsCount
	}
	return 0
}

func (x *NonFleet_Company) GetAggregateOOSViolationsInfo() *NonFleet_CompanyAggregateViolationsInfo {
	if x != nil {
		return x.AggregateOOSViolationsInfo
	}
	return nil
}

func (x *NonFleet_Company) GetAggregateNonOOSViolationsInfo() *NonFleet_CompanyAggregateViolationsInfo {
	if x != nil {
		return x.AggregateNonOOSViolationsInfo
	}
	return nil
}

type NonFleet_CompanyOwner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsDriver   bool                             `protobuf:"varint,1,opt,name=isDriver,proto3" json:"isDriver,omitempty"`
	CreditData *NonFleet_CompanyOwnerCreditData `protobuf:"bytes,2,opt,name=creditData,proto3,oneof" json:"creditData,omitempty"`
}

func (x *NonFleet_CompanyOwner) Reset() {
	*x = NonFleet_CompanyOwner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_CompanyOwner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_CompanyOwner) ProtoMessage() {}

func (x *NonFleet_CompanyOwner) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_CompanyOwner.ProtoReflect.Descriptor instead.
func (*NonFleet_CompanyOwner) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 5}
}

func (x *NonFleet_CompanyOwner) GetIsDriver() bool {
	if x != nil {
		return x.IsDriver
	}
	return false
}

func (x *NonFleet_CompanyOwner) GetCreditData() *NonFleet_CompanyOwnerCreditData {
	if x != nil {
		return x.CreditData
	}
	return nil
}

type NonFleet_CompanyOwnerCreditData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateOfCreditReportRun                                       *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=DateOfCreditReportRun,proto3,oneof" json:"DateOfCreditReportRun,omitempty"`
	DateOfOldestTradeline                                       *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=DateOfOldestTradeline,proto3,oneof" json:"DateOfOldestTradeline,omitempty"`
	DateOfEarliestTradeline                                     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=DateOfEarliestTradeline,proto3,oneof" json:"DateOfEarliestTradeline,omitempty"`
	DateOfMostRecentAutoTrade                                   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=DateOfMostRecentAutoTrade,proto3,oneof" json:"DateOfMostRecentAutoTrade,omitempty"`
	OwnerAgeAtCreditReportRunInYears                            int64                  `protobuf:"varint,5,opt,name=OwnerAgeAtCreditReportRunInYears,proto3" json:"OwnerAgeAtCreditReportRunInYears,omitempty"`
	OwnerAgeAtEarliestTradelineInYears                          int64                  `protobuf:"varint,6,opt,name=OwnerAgeAtEarliestTradelineInYears,proto3" json:"OwnerAgeAtEarliestTradelineInYears,omitempty"`
	AgeOfOldestTradelineInMonths                                int64                  `protobuf:"varint,7,opt,name=AgeOfOldestTradelineInMonths,proto3" json:"AgeOfOldestTradelineInMonths,omitempty"`
	AgeOfMostRecentAutoTradeInMonths                            int64                  `protobuf:"varint,8,opt,name=AgeOfMostRecentAutoTradeInMonths,proto3" json:"AgeOfMostRecentAutoTradeInMonths,omitempty"`
	MonthsSinceMostRecentWriteOff                               int64                  `protobuf:"varint,9,opt,name=MonthsSinceMostRecentWriteOff,proto3" json:"MonthsSinceMostRecentWriteOff,omitempty"`
	MonthsSinceMostRecentBankRevolvingTradeOpened               int64                  `protobuf:"varint,10,opt,name=MonthsSinceMostRecentBankRevolvingTradeOpened,proto3" json:"MonthsSinceMostRecentBankRevolvingTradeOpened,omitempty"`
	NumberOfCurrentSatisfactoryTrades                           int64                  `protobuf:"varint,11,opt,name=NumberOfCurrentSatisfactoryTrades,proto3" json:"NumberOfCurrentSatisfactoryTrades,omitempty"`
	NumberOfTradesWithDelinquency                               int64                  `protobuf:"varint,12,opt,name=NumberOfTradesWithDelinquency,proto3" json:"NumberOfTradesWithDelinquency,omitempty"`
	NumberOfDerogatoryInstallmentTrades                         int64                  `protobuf:"varint,13,opt,name=NumberOfDerogatoryInstallmentTrades,proto3" json:"NumberOfDerogatoryInstallmentTrades,omitempty"`
	NumberOfTradeLinesOpenedInLast12Months                      int64                  `protobuf:"varint,14,opt,name=NumberOfTradeLinesOpenedInLast12Months,proto3" json:"NumberOfTradeLinesOpenedInLast12Months,omitempty"`
	NumberOfNonInsuranceInquiries                               int64                  `protobuf:"varint,15,opt,name=NumberOfNonInsuranceInquiries,proto3" json:"NumberOfNonInsuranceInquiries,omitempty"`
	RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades float64                `protobuf:"fixed64,16,opt,name=RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades,proto3" json:"RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades,omitempty"`
	RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades  float64                `protobuf:"fixed64,17,opt,name=RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades,proto3" json:"RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades,omitempty"`
	NoHitFlag                                                   bool                   `protobuf:"varint,18,opt,name=NoHitFlag,proto3" json:"NoHitFlag,omitempty"`
	ThinFileFlag                                                bool                   `protobuf:"varint,19,opt,name=ThinFileFlag,proto3" json:"ThinFileFlag,omitempty"`
}

func (x *NonFleet_CompanyOwnerCreditData) Reset() {
	*x = NonFleet_CompanyOwnerCreditData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_CompanyOwnerCreditData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_CompanyOwnerCreditData) ProtoMessage() {}

func (x *NonFleet_CompanyOwnerCreditData) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_CompanyOwnerCreditData.ProtoReflect.Descriptor instead.
func (*NonFleet_CompanyOwnerCreditData) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 6}
}

func (x *NonFleet_CompanyOwnerCreditData) GetDateOfCreditReportRun() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfCreditReportRun
	}
	return nil
}

func (x *NonFleet_CompanyOwnerCreditData) GetDateOfOldestTradeline() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfOldestTradeline
	}
	return nil
}

func (x *NonFleet_CompanyOwnerCreditData) GetDateOfEarliestTradeline() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfEarliestTradeline
	}
	return nil
}

func (x *NonFleet_CompanyOwnerCreditData) GetDateOfMostRecentAutoTrade() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfMostRecentAutoTrade
	}
	return nil
}

func (x *NonFleet_CompanyOwnerCreditData) GetOwnerAgeAtCreditReportRunInYears() int64 {
	if x != nil {
		return x.OwnerAgeAtCreditReportRunInYears
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetOwnerAgeAtEarliestTradelineInYears() int64 {
	if x != nil {
		return x.OwnerAgeAtEarliestTradelineInYears
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetAgeOfOldestTradelineInMonths() int64 {
	if x != nil {
		return x.AgeOfOldestTradelineInMonths
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetAgeOfMostRecentAutoTradeInMonths() int64 {
	if x != nil {
		return x.AgeOfMostRecentAutoTradeInMonths
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetMonthsSinceMostRecentWriteOff() int64 {
	if x != nil {
		return x.MonthsSinceMostRecentWriteOff
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetMonthsSinceMostRecentBankRevolvingTradeOpened() int64 {
	if x != nil {
		return x.MonthsSinceMostRecentBankRevolvingTradeOpened
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetNumberOfCurrentSatisfactoryTrades() int64 {
	if x != nil {
		return x.NumberOfCurrentSatisfactoryTrades
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetNumberOfTradesWithDelinquency() int64 {
	if x != nil {
		return x.NumberOfTradesWithDelinquency
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetNumberOfDerogatoryInstallmentTrades() int64 {
	if x != nil {
		return x.NumberOfDerogatoryInstallmentTrades
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetNumberOfTradeLinesOpenedInLast12Months() int64 {
	if x != nil {
		return x.NumberOfTradeLinesOpenedInLast12Months
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetNumberOfNonInsuranceInquiries() int64 {
	if x != nil {
		return x.NumberOfNonInsuranceInquiries
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetRatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades() float64 {
	if x != nil {
		return x.RatioOfRevolvingBalancesToTotalHighCreditForRevolvingTrades
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetRatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades() float64 {
	if x != nil {
		return x.RatioOfTotalBalancesToTotalHighCreditForAllNonClosedTrades
	}
	return 0
}

func (x *NonFleet_CompanyOwnerCreditData) GetNoHitFlag() bool {
	if x != nil {
		return x.NoHitFlag
	}
	return false
}

func (x *NonFleet_CompanyOwnerCreditData) GetThinFileFlag() bool {
	if x != nil {
		return x.ThinFileFlag
	}
	return false
}

type NonFleet_ContinuousCoverageRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Years            float64           `protobuf:"fixed64,1,opt,name=years,proto3" json:"years,omitempty"`
	SubCoverageGroup *SubCoverageGroup `protobuf:"bytes,2,opt,name=subCoverageGroup,proto3" json:"subCoverageGroup,omitempty"`
}

func (x *NonFleet_ContinuousCoverageRecord) Reset() {
	*x = NonFleet_ContinuousCoverageRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_ContinuousCoverageRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_ContinuousCoverageRecord) ProtoMessage() {}

func (x *NonFleet_ContinuousCoverageRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_ContinuousCoverageRecord.ProtoReflect.Descriptor instead.
func (*NonFleet_ContinuousCoverageRecord) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 7}
}

func (x *NonFleet_ContinuousCoverageRecord) GetYears() float64 {
	if x != nil {
		return x.Years
	}
	return 0
}

func (x *NonFleet_ContinuousCoverageRecord) GetSubCoverageGroup() *SubCoverageGroup {
	if x != nil {
		return x.SubCoverageGroup
	}
	return nil
}

type NonFleet_CompanyAggregateViolationsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count    int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Severity int64 `protobuf:"varint,2,opt,name=severity,proto3" json:"severity,omitempty"`
}

func (x *NonFleet_CompanyAggregateViolationsInfo) Reset() {
	*x = NonFleet_CompanyAggregateViolationsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_CompanyAggregateViolationsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_CompanyAggregateViolationsInfo) ProtoMessage() {}

func (x *NonFleet_CompanyAggregateViolationsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_CompanyAggregateViolationsInfo.ProtoReflect.Descriptor instead.
func (*NonFleet_CompanyAggregateViolationsInfo) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 8}
}

func (x *NonFleet_CompanyAggregateViolationsInfo) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *NonFleet_CompanyAggregateViolationsInfo) GetSeverity() int64 {
	if x != nil {
		return x.Severity
	}
	return 0
}

type NonFleet_UnderwriterInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditScore           model.CreditScore       `protobuf:"varint,1,opt,name=creditScore,proto3,enum=nonfleet_model.CreditScore" json:"creditScore,omitempty"`
	UsDOTScore            model.USDOTScore        `protobuf:"varint,2,opt,name=usDOTScore,proto3,enum=nonfleet_model.USDOTScore" json:"usDOTScore,omitempty"`
	ScheduleModifications []*ScheduleModification `protobuf:"bytes,3,rep,name=scheduleModifications,proto3" json:"scheduleModifications,omitempty"`
	PaymentPlan           model.PaymentPlan       `protobuf:"varint,4,opt,name=paymentPlan,proto3,enum=nonfleet_model.PaymentPlan" json:"paymentPlan,omitempty"`
}

func (x *NonFleet_UnderwriterInput) Reset() {
	*x = NonFleet_UnderwriterInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_UnderwriterInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_UnderwriterInput) ProtoMessage() {}

func (x *NonFleet_UnderwriterInput) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_UnderwriterInput.ProtoReflect.Descriptor instead.
func (*NonFleet_UnderwriterInput) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 9}
}

func (x *NonFleet_UnderwriterInput) GetCreditScore() model.CreditScore {
	if x != nil {
		return x.CreditScore
	}
	return model.CreditScore(0)
}

func (x *NonFleet_UnderwriterInput) GetUsDOTScore() model.USDOTScore {
	if x != nil {
		return x.UsDOTScore
	}
	return model.USDOTScore(0)
}

func (x *NonFleet_UnderwriterInput) GetScheduleModifications() []*ScheduleModification {
	if x != nil {
		return x.ScheduleModifications
	}
	return nil
}

func (x *NonFleet_UnderwriterInput) GetPaymentPlan() model.PaymentPlan {
	if x != nil {
		return x.PaymentPlan
	}
	return model.PaymentPlan(0)
}

type NonFleet_Driver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                           string                         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	IsOutOfState                 bool                           `protobuf:"varint,2,opt,name=isOutOfState,proto3" json:"isOutOfState,omitempty"`
	IsIncluded                   bool                           `protobuf:"varint,3,opt,name=isIncluded,proto3" json:"isIncluded,omitempty"`
	HasViolationInLastThreeYears bool                           `protobuf:"varint,4,opt,name=hasViolationInLastThreeYears,proto3" json:"hasViolationInLastThreeYears,omitempty"`
	DateOfBirth                  *timestamppb.Timestamp         `protobuf:"bytes,5,opt,name=dateOfBirth,proto3" json:"dateOfBirth,omitempty"`
	YearsOfExperience            int32                          `protobuf:"varint,6,opt,name=yearsOfExperience,proto3" json:"yearsOfExperience,omitempty"`
	ViolationsInfo               *NonFleet_DriverViolationsInfo `protobuf:"bytes,7,opt,name=violationsInfo,proto3,oneof" json:"violationsInfo,omitempty"`
}

func (x *NonFleet_Driver) Reset() {
	*x = NonFleet_Driver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_Driver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_Driver) ProtoMessage() {}

func (x *NonFleet_Driver) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_Driver.ProtoReflect.Descriptor instead.
func (*NonFleet_Driver) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 10}
}

func (x *NonFleet_Driver) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *NonFleet_Driver) GetIsOutOfState() bool {
	if x != nil {
		return x.IsOutOfState
	}
	return false
}

func (x *NonFleet_Driver) GetIsIncluded() bool {
	if x != nil {
		return x.IsIncluded
	}
	return false
}

func (x *NonFleet_Driver) GetHasViolationInLastThreeYears() bool {
	if x != nil {
		return x.HasViolationInLastThreeYears
	}
	return false
}

func (x *NonFleet_Driver) GetDateOfBirth() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *NonFleet_Driver) GetYearsOfExperience() int32 {
	if x != nil {
		return x.YearsOfExperience
	}
	return 0
}

func (x *NonFleet_Driver) GetViolationsInfo() *NonFleet_DriverViolationsInfo {
	if x != nil {
		return x.ViolationsInfo
	}
	return nil
}

type NonFleet_DriverViolationsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DriverId             string           `protobuf:"bytes,1,opt,name=driverId,proto3" json:"driverId,omitempty"`
	ViolationPoints      int64            `protobuf:"varint,2,opt,name=violationPoints,proto3" json:"violationPoints,omitempty"`
	ViolationClassCounts map[string]int64 `protobuf:"bytes,3,rep,name=violationClassCounts,proto3" json:"violationClassCounts,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *NonFleet_DriverViolationsInfo) Reset() {
	*x = NonFleet_DriverViolationsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_DriverViolationsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_DriverViolationsInfo) ProtoMessage() {}

func (x *NonFleet_DriverViolationsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_DriverViolationsInfo.ProtoReflect.Descriptor instead.
func (*NonFleet_DriverViolationsInfo) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 11}
}

func (x *NonFleet_DriverViolationsInfo) GetDriverId() string {
	if x != nil {
		return x.DriverId
	}
	return ""
}

func (x *NonFleet_DriverViolationsInfo) GetViolationPoints() int64 {
	if x != nil {
		return x.ViolationPoints
	}
	return 0
}

func (x *NonFleet_DriverViolationsInfo) GetViolationClassCounts() map[string]int64 {
	if x != nil {
		return x.ViolationClassCounts
	}
	return nil
}

type NonFleet_Vehicle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin                  string                     `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	Type                 model.VehicleType          `protobuf:"varint,2,opt,name=type,proto3,enum=nonfleet_model.VehicleType" json:"type,omitempty"`
	WeightClass          model.VehicleWeightClass   `protobuf:"varint,3,opt,name=weightClass,proto3,enum=nonfleet_model.VehicleWeightClass" json:"weightClass,omitempty"`
	Class                model.VehicleClass         `protobuf:"varint,4,opt,name=class,proto3,enum=nonfleet_model.VehicleClass" json:"class,omitempty"`
	MaxRadiusOfOperation model.MaxRadiusOfOperation `protobuf:"varint,5,opt,name=maxRadiusOfOperation,proto3,enum=nonfleet_model.MaxRadiusOfOperation" json:"maxRadiusOfOperation,omitempty"`
	YearMade             int64                      `protobuf:"varint,6,opt,name=yearMade,proto3" json:"yearMade,omitempty"`
	StatedValue          *int32                     `protobuf:"varint,7,opt,name=statedValue,proto3,oneof" json:"statedValue,omitempty"`
}

func (x *NonFleet_Vehicle) Reset() {
	*x = NonFleet_Vehicle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_Vehicle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_Vehicle) ProtoMessage() {}

func (x *NonFleet_Vehicle) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_Vehicle.ProtoReflect.Descriptor instead.
func (*NonFleet_Vehicle) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 12}
}

func (x *NonFleet_Vehicle) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *NonFleet_Vehicle) GetType() model.VehicleType {
	if x != nil {
		return x.Type
	}
	return model.VehicleType(0)
}

func (x *NonFleet_Vehicle) GetWeightClass() model.VehicleWeightClass {
	if x != nil {
		return x.WeightClass
	}
	return model.VehicleWeightClass(0)
}

func (x *NonFleet_Vehicle) GetClass() model.VehicleClass {
	if x != nil {
		return x.Class
	}
	return model.VehicleClass(0)
}

func (x *NonFleet_Vehicle) GetMaxRadiusOfOperation() model.MaxRadiusOfOperation {
	if x != nil {
		return x.MaxRadiusOfOperation
	}
	return model.MaxRadiusOfOperation(0)
}

func (x *NonFleet_Vehicle) GetYearMade() int64 {
	if x != nil {
		return x.YearMade
	}
	return 0
}

func (x *NonFleet_Vehicle) GetStatedValue() int32 {
	if x != nil && x.StatedValue != nil {
		return *x.StatedValue
	}
	return 0
}

type NonFleet_UnclassifiedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AllSubCoveragesSafetyScheduleMod     float64 `protobuf:"fixed64,1,opt,name=allSubCoveragesSafetyScheduleMod,proto3" json:"allSubCoveragesSafetyScheduleMod,omitempty"`
	IsSpareTrailerEnabledForAllCoverages bool    `protobuf:"varint,2,opt,name=isSpareTrailerEnabledForAllCoverages,proto3" json:"isSpareTrailerEnabledForAllCoverages,omitempty"`
	HasUIIA                              bool    `protobuf:"varint,3,opt,name=hasUIIA,proto3" json:"hasUIIA,omitempty"`
}

func (x *NonFleet_UnclassifiedData) Reset() {
	*x = NonFleet_UnclassifiedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_UnclassifiedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_UnclassifiedData) ProtoMessage() {}

func (x *NonFleet_UnclassifiedData) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_UnclassifiedData.ProtoReflect.Descriptor instead.
func (*NonFleet_UnclassifiedData) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 13}
}

func (x *NonFleet_UnclassifiedData) GetAllSubCoveragesSafetyScheduleMod() float64 {
	if x != nil {
		return x.AllSubCoveragesSafetyScheduleMod
	}
	return 0
}

func (x *NonFleet_UnclassifiedData) GetIsSpareTrailerEnabledForAllCoverages() bool {
	if x != nil {
		return x.IsSpareTrailerEnabledForAllCoverages
	}
	return false
}

func (x *NonFleet_UnclassifiedData) GetHasUIIA() bool {
	if x != nil {
		return x.HasUIIA
	}
	return false
}

type NonFleet_ChunkOutputMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditScore      *model.CreditScore                              `protobuf:"varint,1,opt,name=creditScore,proto3,enum=nonfleet_model.CreditScore,oneof" json:"creditScore,omitempty"`
	UsDOTScore       model.USDOTScore                                `protobuf:"varint,2,opt,name=usDOTScore,proto3,enum=nonfleet_model.USDOTScore" json:"usDOTScore,omitempty"`
	PuCount          int32                                           `protobuf:"varint,3,opt,name=puCount,proto3" json:"puCount,omitempty"`
	VehiclesMetadata []*NonFleet_ChunkOutputMetadata_VehicleMetadata `protobuf:"bytes,4,rep,name=vehiclesMetadata,proto3" json:"vehiclesMetadata,omitempty"`
}

func (x *NonFleet_ChunkOutputMetadata) Reset() {
	*x = NonFleet_ChunkOutputMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_ChunkOutputMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_ChunkOutputMetadata) ProtoMessage() {}

func (x *NonFleet_ChunkOutputMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_ChunkOutputMetadata.ProtoReflect.Descriptor instead.
func (*NonFleet_ChunkOutputMetadata) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 14}
}

func (x *NonFleet_ChunkOutputMetadata) GetCreditScore() model.CreditScore {
	if x != nil && x.CreditScore != nil {
		return *x.CreditScore
	}
	return model.CreditScore(0)
}

func (x *NonFleet_ChunkOutputMetadata) GetUsDOTScore() model.USDOTScore {
	if x != nil {
		return x.UsDOTScore
	}
	return model.USDOTScore(0)
}

func (x *NonFleet_ChunkOutputMetadata) GetPuCount() int32 {
	if x != nil {
		return x.PuCount
	}
	return 0
}

func (x *NonFleet_ChunkOutputMetadata) GetVehiclesMetadata() []*NonFleet_ChunkOutputMetadata_VehicleMetadata {
	if x != nil {
		return x.VehiclesMetadata
	}
	return nil
}

type NonFleet_ChunkOutputMetadata_VehicleMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin         string  `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	StatedValue float64 `protobuf:"fixed64,2,opt,name=statedValue,proto3" json:"statedValue,omitempty"`
}

func (x *NonFleet_ChunkOutputMetadata_VehicleMetadata) Reset() {
	*x = NonFleet_ChunkOutputMetadata_VehicleMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_nonfleet_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NonFleet_ChunkOutputMetadata_VehicleMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NonFleet_ChunkOutputMetadata_VehicleMetadata) ProtoMessage() {}

func (x *NonFleet_ChunkOutputMetadata_VehicleMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_nonfleet_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NonFleet_ChunkOutputMetadata_VehicleMetadata.ProtoReflect.Descriptor instead.
func (*NonFleet_ChunkOutputMetadata_VehicleMetadata) Descriptor() ([]byte, []int) {
	return file_pricing_nonfleet_proto_rawDescGZIP(), []int{0, 14, 0}
}

func (x *NonFleet_ChunkOutputMetadata_VehicleMetadata) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *NonFleet_ChunkOutputMetadata_VehicleMetadata) GetStatedValue() float64 {
	if x != nil {
		return x.StatedValue
	}
	return 0
}

var File_pricing_nonfleet_proto protoreflect.FileDescriptor

var file_pricing_nonfleet_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x15, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2f, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x6e, 0x6f,
	0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xc2, 0x2c, 0x0a, 0x08, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x1a, 0x70, 0x0a, 0x13,
	0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x59, 0x0a, 0x17, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x44,
	0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x17, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x44,
	0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73, 0x1a, 0xe3,
	0x03, 0x0a, 0x13, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70,
	0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4b, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65,
	0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e,
	0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52,
	0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x4e, 0x0a, 0x10, 0x75, 0x6e, 0x64, 0x65,
	0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x10, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x32, 0x0a, 0x07, 0x64, 0x72, 0x69, 0x76,
	0x65, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x52, 0x07, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x12, 0x35, 0x0a, 0x08,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65,
	0x74, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x10, 0x75, 0x6e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74,
	0x2e, 0x55, 0x6e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x10, 0x75, 0x6e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a, 0x0e, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x1a, 0xad, 0x01, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5d, 0x0a, 0x18, 0x70, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6e, 0x6f, 0x6e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x18, 0x70,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x3b, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x64, 0x69, 0x74, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x1a, 0x90, 0x01, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x4f, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74,
	0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x2c, 0x0a, 0x11, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x4f, 0x66, 0x48, 0x61, 0x75, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x4f, 0x66, 0x48, 0x61, 0x75, 0x6c, 0x73, 0x1a, 0xc6, 0x06, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x12, 0x34, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x7a, 0x69, 0x70,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x10, 0x61,
	0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x10, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43,
	0x6f, 0x73, 0x74, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0f,
	0x79, 0x65, 0x61, 0x72, 0x73, 0x49, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x79, 0x65, 0x61, 0x72, 0x73, 0x49, 0x6e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73,
	0x12, 0x7c, 0x0a, 0x23, 0x61, 0x6e, 0x79, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x23, 0x61, 0x6e, 0x79, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x84,
	0x01, 0x0a, 0x27, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c,
	0x65, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x27, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x10, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x70, 0x0a, 0x1a, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x4f, 0x4f,
	0x53, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e,
	0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x1a, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x4f, 0x4f, 0x53, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x76, 0x0a, 0x1d, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x4e, 0x6f, 0x6e, 0x4f, 0x4f, 0x53, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x56, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x1d, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x6e, 0x4f, 0x4f, 0x53, 0x56, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x13, 0x0a, 0x11, 0x5f,
	0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65,
	0x1a, 0x88, 0x01, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4f, 0x77, 0x6e, 0x65,
	0x72, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x4d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e, 0x46,
	0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4f, 0x77, 0x6e, 0x65,
	0x72, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x8f, 0x0d, 0x0a, 0x16,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a, 0x15, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x75, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x48, 0x00, 0x52, 0x15, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x75, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x55, 0x0a,
	0x15, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x01, 0x52, 0x15, 0x44, 0x61, 0x74, 0x65,
	0x4f, 0x66, 0x4f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x17, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x45, 0x61,
	0x72, 0x6c, 0x69, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x48, 0x02, 0x52, 0x17, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x45, 0x61, 0x72, 0x6c, 0x69,
	0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x5d, 0x0a, 0x19, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63,
	0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x54, 0x72, 0x61, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x03,
	0x52, 0x19, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65,
	0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x54, 0x72, 0x61, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4a,
	0x0a, 0x20, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x67, 0x65, 0x41, 0x74, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x59, 0x65, 0x61,
	0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x20, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41,
	0x67, 0x65, 0x41, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x75, 0x6e, 0x49, 0x6e, 0x59, 0x65, 0x61, 0x72, 0x73, 0x12, 0x4e, 0x0a, 0x22, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x41, 0x67, 0x65, 0x41, 0x74, 0x45, 0x61, 0x72, 0x6c, 0x69, 0x65, 0x73, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x59, 0x65, 0x61, 0x72, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x22, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x67, 0x65,
	0x41, 0x74, 0x45, 0x61, 0x72, 0x6c, 0x69, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x49, 0x6e, 0x59, 0x65, 0x61, 0x72, 0x73, 0x12, 0x42, 0x0a, 0x1c, 0x41, 0x67,
	0x65, 0x4f, 0x66, 0x4f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x1c, 0x41, 0x67, 0x65, 0x4f, 0x66, 0x4f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x4a,
	0x0a, 0x20, 0x41, 0x67, 0x65, 0x4f, 0x66, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x41, 0x75, 0x74, 0x6f, 0x54, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x20, 0x41, 0x67, 0x65, 0x4f, 0x66, 0x4d,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x44, 0x0a, 0x1d, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x73, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63,
	0x65, 0x6e, 0x74, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x1d, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x4d, 0x6f,
	0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66,
	0x12, 0x64, 0x0a, 0x2d, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x4d,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x76,
	0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x65,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x2d, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x53,
	0x69, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x42, 0x61,
	0x6e, 0x6b, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x12, 0x4c, 0x0a, 0x21, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x4f, 0x66, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x61, 0x74, 0x69, 0x73, 0x66, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x21, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x53, 0x61, 0x74, 0x69, 0x73, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x73, 0x12, 0x44, 0x0a, 0x1d, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1d, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x57, 0x69, 0x74, 0x68, 0x44,
	0x65, 0x6c, 0x69, 0x6e, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x50, 0x0a, 0x23, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x44, 0x65, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x79,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x23, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f,
	0x66, 0x44, 0x65, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x12, 0x56, 0x0a, 0x26,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4c, 0x69, 0x6e,
	0x65, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x49, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x31, 0x32,
	0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x26, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x73,
	0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x49, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x31, 0x32, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x73, 0x12, 0x44, 0x0a, 0x1d, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66,
	0x4e, 0x6f, 0x6e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x71, 0x75,
	0x69, 0x72, 0x69, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1d, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x4f, 0x66, 0x4e, 0x6f, 0x6e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x69, 0x65, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x3b, 0x52,
	0x61, 0x74, 0x69, 0x6f, 0x4f, 0x66, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x54, 0x6f, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x48, 0x69,
	0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x76, 0x6f, 0x6c,
	0x76, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x3b, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x4f, 0x66, 0x52, 0x65, 0x76, 0x6f, 0x6c, 0x76, 0x69,
	0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x54, 0x6f, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x48, 0x69, 0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x52, 0x65,
	0x76, 0x6f, 0x6c, 0x76, 0x69, 0x6e, 0x67, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x12, 0x7e, 0x0a,
	0x3a, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x4f, 0x66, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x54, 0x6f, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x48, 0x69, 0x67, 0x68,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x4e, 0x6f, 0x6e, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x64, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x3a, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x4f, 0x66, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x54, 0x6f, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x48, 0x69,
	0x67, 0x68, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x4e, 0x6f,
	0x6e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x54, 0x72, 0x61, 0x64, 0x65, 0x73, 0x12, 0x1c, 0x0a,
	0x09, 0x4e, 0x6f, 0x48, 0x69, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x4e, 0x6f, 0x48, 0x69, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x54,
	0x68, 0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0c, 0x54, 0x68, 0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x42,
	0x18, 0x0a, 0x16, 0x5f, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x75, 0x6e, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x44, 0x61,
	0x74, 0x65, 0x4f, 0x66, 0x4f, 0x6c, 0x64, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x45, 0x61,
	0x72, 0x6c, 0x69, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x42,
	0x1c, 0x0a, 0x1a, 0x5f, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4d, 0x6f, 0x73, 0x74, 0x52, 0x65,
	0x63, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x54, 0x72, 0x61, 0x64, 0x65, 0x1a, 0x77, 0x0a,
	0x18, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x79, 0x65, 0x61,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x79, 0x65, 0x61, 0x72, 0x73, 0x12,
	0x45, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x10, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x1a, 0x52, 0x0a, 0x1e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x1a, 0xa1, 0x02, 0x0a, 0x10, 0x55,
	0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12,
	0x3d, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x3a,
	0x0a, 0x0a, 0x75, 0x73, 0x44, 0x4f, 0x54, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x55, 0x53, 0x44, 0x4f, 0x54, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x0a,
	0x75, 0x73, 0x44, 0x4f, 0x54, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x53, 0x0a, 0x15, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x3d, 0x0a, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61,
	0x6e, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x1a, 0xf4,
	0x02, 0x0a, 0x06, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x4f,
	0x75, 0x74, 0x4f, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x69, 0x73, 0x4f, 0x75, 0x74, 0x4f, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x69, 0x73, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x69, 0x73, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x64, 0x12, 0x42, 0x0a,
	0x1c, 0x68, 0x61, 0x73, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x4c,
	0x61, 0x73, 0x74, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x1c, 0x68, 0x61, 0x73, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x4c, 0x61, 0x73, 0x74, 0x54, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72,
	0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12,
	0x2c, 0x0a, 0x11, 0x79, 0x65, 0x61, 0x72, 0x73, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x79, 0x65, 0x61, 0x72,
	0x73, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x53, 0x0a,
	0x0e, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e,
	0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x56,
	0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52,
	0x0e, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x88,
	0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x9b, 0x02, 0x0a, 0x14, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x76, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x12, 0x74, 0x0a, 0x14, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x56, 0x69, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x56, 0x69, 0x6f, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x1a, 0x47, 0x0a, 0x19, 0x56, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0xf3, 0x02, 0x0a, 0x07, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69,
	0x6e, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x44, 0x0a, 0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0b, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x58, 0x0a, 0x14,
	0x6d, 0x61, 0x78, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6e, 0x6f, 0x6e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x78, 0x52,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x14, 0x6d, 0x61, 0x78, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x79, 0x65, 0x61, 0x72, 0x4d, 0x61,
	0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x79, 0x65, 0x61, 0x72, 0x4d, 0x61,
	0x64, 0x65, 0x12, 0x25, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0xcc, 0x01, 0x0a, 0x10, 0x55, 0x6e,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4a,
	0x0a, 0x20, 0x61, 0x6c, 0x6c, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x73, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d,
	0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x20, 0x61, 0x6c, 0x6c, 0x53, 0x75, 0x62,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x12, 0x52, 0x0a, 0x24, 0x69, 0x73,
	0x53, 0x70, 0x61, 0x72, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x24, 0x69, 0x73, 0x53, 0x70, 0x61, 0x72,
	0x65, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46,
	0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x68, 0x61, 0x73, 0x55, 0x49, 0x49, 0x41, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x68, 0x61, 0x73, 0x55, 0x49, 0x49, 0x41, 0x1a, 0xe9, 0x02, 0x0a, 0x13, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x42, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x0a, 0x75, 0x73, 0x44, 0x4f, 0x54, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x55, 0x53, 0x44, 0x4f, 0x54, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x52, 0x0a, 0x75, 0x73, 0x44, 0x4f, 0x54, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x75, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x70, 0x75, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x61, 0x0a, 0x10, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e,
	0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x10, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x45, 0x0a,
	0x0f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76,
	0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x64, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x42, 0x33, 0x5a, 0x31, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74,
	0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f,
	0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x70, 0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pricing_nonfleet_proto_rawDescOnce sync.Once
	file_pricing_nonfleet_proto_rawDescData = file_pricing_nonfleet_proto_rawDesc
)

func file_pricing_nonfleet_proto_rawDescGZIP() []byte {
	file_pricing_nonfleet_proto_rawDescOnce.Do(func() {
		file_pricing_nonfleet_proto_rawDescData = protoimpl.X.CompressGZIP(file_pricing_nonfleet_proto_rawDescData)
	})
	return file_pricing_nonfleet_proto_rawDescData
}

var file_pricing_nonfleet_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_pricing_nonfleet_proto_goTypes = []interface{}{
	(*NonFleet)(nil),                                     // 0: pricing.NonFleet
	(*NonFleet_BundleChunkSpecData)(nil),                 // 1: pricing.NonFleet.BundleChunkSpecData
	(*NonFleet_PolicyChunkSpecData)(nil),                 // 2: pricing.NonFleet.PolicyChunkSpecData
	(*NonFleet_CommoditiesInfo)(nil),                     // 3: pricing.NonFleet.CommoditiesInfo
	(*NonFleet_CommodityRecord)(nil),                     // 4: pricing.NonFleet.CommodityRecord
	(*NonFleet_Company)(nil),                             // 5: pricing.NonFleet.Company
	(*NonFleet_CompanyOwner)(nil),                        // 6: pricing.NonFleet.CompanyOwner
	(*NonFleet_CompanyOwnerCreditData)(nil),              // 7: pricing.NonFleet.CompanyOwnerCreditData
	(*NonFleet_ContinuousCoverageRecord)(nil),            // 8: pricing.NonFleet.ContinuousCoverageRecord
	(*NonFleet_CompanyAggregateViolationsInfo)(nil),      // 9: pricing.NonFleet.CompanyAggregateViolationsInfo
	(*NonFleet_UnderwriterInput)(nil),                    // 10: pricing.NonFleet.UnderwriterInput
	(*NonFleet_Driver)(nil),                              // 11: pricing.NonFleet.Driver
	(*NonFleet_DriverViolationsInfo)(nil),                // 12: pricing.NonFleet.DriverViolationsInfo
	(*NonFleet_Vehicle)(nil),                             // 13: pricing.NonFleet.Vehicle
	(*NonFleet_UnclassifiedData)(nil),                    // 14: pricing.NonFleet.UnclassifiedData
	(*NonFleet_ChunkOutputMetadata)(nil),                 // 15: pricing.NonFleet.ChunkOutputMetadata
	nil,                                                  // 16: pricing.NonFleet.DriverViolationsInfo.ViolationClassCountsEntry
	(*NonFleet_ChunkOutputMetadata_VehicleMetadata)(nil), // 17: pricing.NonFleet.ChunkOutputMetadata.VehicleMetadata
	(*CombinedDeductibleSpec)(nil),                       // 18: pricing.CombinedDeductibleSpec
	(*ArtifactConfig)(nil),                               // 19: pricing.ArtifactConfig
	(model.CommodityCategory)(0),                         // 20: nonfleet_model.CommodityCategory
	(*timestamppb.Timestamp)(nil),                        // 21: google.protobuf.Timestamp
	(*SubCoverageGroup)(nil),                             // 22: pricing.SubCoverageGroup
	(model.CreditScore)(0),                               // 23: nonfleet_model.CreditScore
	(model.USDOTScore)(0),                                // 24: nonfleet_model.USDOTScore
	(*ScheduleModification)(nil),                         // 25: pricing.ScheduleModification
	(model.PaymentPlan)(0),                               // 26: nonfleet_model.PaymentPlan
	(model.VehicleType)(0),                               // 27: nonfleet_model.VehicleType
	(model.VehicleWeightClass)(0),                        // 28: nonfleet_model.VehicleWeightClass
	(model.VehicleClass)(0),                              // 29: nonfleet_model.VehicleClass
	(model.MaxRadiusOfOperation)(0),                      // 30: nonfleet_model.MaxRadiusOfOperation
}
var file_pricing_nonfleet_proto_depIdxs = []int32{
	18, // 0: pricing.NonFleet.BundleChunkSpecData.combinedDeductibleSpecs:type_name -> pricing.CombinedDeductibleSpec
	3,  // 1: pricing.NonFleet.PolicyChunkSpecData.commoditiesInfo:type_name -> pricing.NonFleet.CommoditiesInfo
	5,  // 2: pricing.NonFleet.PolicyChunkSpecData.company:type_name -> pricing.NonFleet.Company
	10, // 3: pricing.NonFleet.PolicyChunkSpecData.underwriterInput:type_name -> pricing.NonFleet.UnderwriterInput
	11, // 4: pricing.NonFleet.PolicyChunkSpecData.drivers:type_name -> pricing.NonFleet.Driver
	13, // 5: pricing.NonFleet.PolicyChunkSpecData.vehicles:type_name -> pricing.NonFleet.Vehicle
	14, // 6: pricing.NonFleet.PolicyChunkSpecData.unclassifiedData:type_name -> pricing.NonFleet.UnclassifiedData
	19, // 7: pricing.NonFleet.PolicyChunkSpecData.artifactConfig:type_name -> pricing.ArtifactConfig
	20, // 8: pricing.NonFleet.CommoditiesInfo.primaryCommodityCategory:type_name -> nonfleet_model.CommodityCategory
	4,  // 9: pricing.NonFleet.CommoditiesInfo.records:type_name -> pricing.NonFleet.CommodityRecord
	20, // 10: pricing.NonFleet.CommodityRecord.commodityCategory:type_name -> nonfleet_model.CommodityCategory
	6,  // 11: pricing.NonFleet.Company.owner:type_name -> pricing.NonFleet.CompanyOwner
	8,  // 12: pricing.NonFleet.Company.anyCarrierContinuousCoverageRecords:type_name -> pricing.NonFleet.ContinuousCoverageRecord
	8,  // 13: pricing.NonFleet.Company.currentCarrierContinuousCoverageRecords:type_name -> pricing.NonFleet.ContinuousCoverageRecord
	9,  // 14: pricing.NonFleet.Company.AggregateOOSViolationsInfo:type_name -> pricing.NonFleet.CompanyAggregateViolationsInfo
	9,  // 15: pricing.NonFleet.Company.AggregateNonOOSViolationsInfo:type_name -> pricing.NonFleet.CompanyAggregateViolationsInfo
	7,  // 16: pricing.NonFleet.CompanyOwner.creditData:type_name -> pricing.NonFleet.CompanyOwnerCreditData
	21, // 17: pricing.NonFleet.CompanyOwnerCreditData.DateOfCreditReportRun:type_name -> google.protobuf.Timestamp
	21, // 18: pricing.NonFleet.CompanyOwnerCreditData.DateOfOldestTradeline:type_name -> google.protobuf.Timestamp
	21, // 19: pricing.NonFleet.CompanyOwnerCreditData.DateOfEarliestTradeline:type_name -> google.protobuf.Timestamp
	21, // 20: pricing.NonFleet.CompanyOwnerCreditData.DateOfMostRecentAutoTrade:type_name -> google.protobuf.Timestamp
	22, // 21: pricing.NonFleet.ContinuousCoverageRecord.subCoverageGroup:type_name -> pricing.SubCoverageGroup
	23, // 22: pricing.NonFleet.UnderwriterInput.creditScore:type_name -> nonfleet_model.CreditScore
	24, // 23: pricing.NonFleet.UnderwriterInput.usDOTScore:type_name -> nonfleet_model.USDOTScore
	25, // 24: pricing.NonFleet.UnderwriterInput.scheduleModifications:type_name -> pricing.ScheduleModification
	26, // 25: pricing.NonFleet.UnderwriterInput.paymentPlan:type_name -> nonfleet_model.PaymentPlan
	21, // 26: pricing.NonFleet.Driver.dateOfBirth:type_name -> google.protobuf.Timestamp
	12, // 27: pricing.NonFleet.Driver.violationsInfo:type_name -> pricing.NonFleet.DriverViolationsInfo
	16, // 28: pricing.NonFleet.DriverViolationsInfo.violationClassCounts:type_name -> pricing.NonFleet.DriverViolationsInfo.ViolationClassCountsEntry
	27, // 29: pricing.NonFleet.Vehicle.type:type_name -> nonfleet_model.VehicleType
	28, // 30: pricing.NonFleet.Vehicle.weightClass:type_name -> nonfleet_model.VehicleWeightClass
	29, // 31: pricing.NonFleet.Vehicle.class:type_name -> nonfleet_model.VehicleClass
	30, // 32: pricing.NonFleet.Vehicle.maxRadiusOfOperation:type_name -> nonfleet_model.MaxRadiusOfOperation
	23, // 33: pricing.NonFleet.ChunkOutputMetadata.creditScore:type_name -> nonfleet_model.CreditScore
	24, // 34: pricing.NonFleet.ChunkOutputMetadata.usDOTScore:type_name -> nonfleet_model.USDOTScore
	17, // 35: pricing.NonFleet.ChunkOutputMetadata.vehiclesMetadata:type_name -> pricing.NonFleet.ChunkOutputMetadata.VehicleMetadata
	36, // [36:36] is the sub-list for method output_type
	36, // [36:36] is the sub-list for method input_type
	36, // [36:36] is the sub-list for extension type_name
	36, // [36:36] is the sub-list for extension extendee
	0,  // [0:36] is the sub-list for field type_name
}

func init() { file_pricing_nonfleet_proto_init() }
func file_pricing_nonfleet_proto_init() {
	if File_pricing_nonfleet_proto != nil {
		return
	}
	file_pricing_configs_proto_init()
	file_pricing_sub_coverages_proto_init()
	file_pricing_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pricing_nonfleet_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_BundleChunkSpecData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_PolicyChunkSpecData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_CommoditiesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_CommodityRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_Company); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_CompanyOwner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_CompanyOwnerCreditData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_ContinuousCoverageRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_CompanyAggregateViolationsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_UnderwriterInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_Driver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_DriverViolationsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_Vehicle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_UnclassifiedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_ChunkOutputMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_nonfleet_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NonFleet_ChunkOutputMetadata_VehicleMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pricing_nonfleet_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_pricing_nonfleet_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_pricing_nonfleet_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_pricing_nonfleet_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_pricing_nonfleet_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_pricing_nonfleet_proto_msgTypes[15].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricing_nonfleet_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pricing_nonfleet_proto_goTypes,
		DependencyIndexes: file_pricing_nonfleet_proto_depIdxs,
		MessageInfos:      file_pricing_nonfleet_proto_msgTypes,
	}.Build()
	File_pricing_nonfleet_proto = out.File
	file_pricing_nonfleet_proto_rawDesc = nil
	file_pricing_nonfleet_proto_goTypes = nil
	file_pricing_nonfleet_proto_depIdxs = nil
}
