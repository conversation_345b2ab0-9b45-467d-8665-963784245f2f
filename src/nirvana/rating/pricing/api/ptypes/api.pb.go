// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pricing/api.proto

package ptypes

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	proto "nirvanatech.com/nirvana/common-go/proto"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	BundleSpec  *BundleSpec   `protobuf:"bytes,2,opt,name=bundleSpec,proto3" json:"bundleSpec,omitempty"`
	PolicySpecs []*PolicySpec `protobuf:"bytes,3,rep,name=policySpecs,proto3" json:"policySpecs,omitempty"`
	ProgramType ProgramType   `protobuf:"varint,4,opt,name=programType,proto3,enum=pricing.ProgramType" json:"programType,omitempty"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_pricing_api_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Request) GetBundleSpec() *BundleSpec {
	if x != nil {
		return x.BundleSpec
	}
	return nil
}

func (x *Request) GetPolicySpecs() []*PolicySpec {
	if x != nil {
		return x.PolicySpecs
	}
	return nil
}

func (x *Request) GetProgramType() ProgramType {
	if x != nil {
		return x.ProgramType
	}
	return ProgramType_ProgramType_Unspecified
}

type BundleSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChunkSpecs []*BundleSpec_ChunkSpec `protobuf:"bytes,1,rep,name=chunkSpecs,proto3" json:"chunkSpecs,omitempty"`
}

func (x *BundleSpec) Reset() {
	*x = BundleSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BundleSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BundleSpec) ProtoMessage() {}

func (x *BundleSpec) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BundleSpec.ProtoReflect.Descriptor instead.
func (*BundleSpec) Descriptor() ([]byte, []int) {
	return file_pricing_api_proto_rawDescGZIP(), []int{1}
}

func (x *BundleSpec) GetChunkSpecs() []*BundleSpec_ChunkSpec {
	if x != nil {
		return x.ChunkSpecs
	}
	return nil
}

type PolicySpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PolicyNumber    string                  `protobuf:"bytes,1,opt,name=policyNumber,proto3" json:"policyNumber,omitempty"`
	PolicyName      PolicyName              `protobuf:"varint,2,opt,name=policyName,proto3,enum=pricing.PolicyName" json:"policyName,omitempty"`
	PolicyDates     *proto.Interval         `protobuf:"bytes,3,opt,name=policyDates,proto3" json:"policyDates,omitempty"`
	ChunkSpecs      []*PolicySpec_ChunkSpec `protobuf:"bytes,4,rep,name=chunkSpecs,proto3" json:"chunkSpecs,omitempty"`
	ModelSpec       *ModelSpec              `protobuf:"bytes,5,opt,name=modelSpec,proto3" json:"modelSpec,omitempty"`
	OptionalPlugins []PluginID              `protobuf:"varint,6,rep,packed,name=optionalPlugins,proto3,enum=pricing.PluginID" json:"optionalPlugins,omitempty"`
}

func (x *PolicySpec) Reset() {
	*x = PolicySpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicySpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicySpec) ProtoMessage() {}

func (x *PolicySpec) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicySpec.ProtoReflect.Descriptor instead.
func (*PolicySpec) Descriptor() ([]byte, []int) {
	return file_pricing_api_proto_rawDescGZIP(), []int{2}
}

func (x *PolicySpec) GetPolicyNumber() string {
	if x != nil {
		return x.PolicyNumber
	}
	return ""
}

func (x *PolicySpec) GetPolicyName() PolicyName {
	if x != nil {
		return x.PolicyName
	}
	return PolicyName_PolicyName_Unspecified
}

func (x *PolicySpec) GetPolicyDates() *proto.Interval {
	if x != nil {
		return x.PolicyDates
	}
	return nil
}

func (x *PolicySpec) GetChunkSpecs() []*PolicySpec_ChunkSpec {
	if x != nil {
		return x.ChunkSpecs
	}
	return nil
}

func (x *PolicySpec) GetModelSpec() *ModelSpec {
	if x != nil {
		return x.ModelSpec
	}
	return nil
}

func (x *PolicySpec) GetOptionalPlugins() []PluginID {
	if x != nil {
		return x.OptionalPlugins
	}
	return nil
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PolicyOutputs []*PolicyOutput `protobuf:"bytes,1,rep,name=policyOutputs,proto3" json:"policyOutputs,omitempty"`
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_pricing_api_proto_rawDescGZIP(), []int{3}
}

func (x *Response) GetPolicyOutputs() []*PolicyOutput {
	if x != nil {
		return x.PolicyOutputs
	}
	return nil
}

type PolicyOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PolicyNumber string         `protobuf:"bytes,1,opt,name=policyNumber,proto3" json:"policyNumber,omitempty"`
	ChunkOutputs []*ChunkOutput `protobuf:"bytes,2,rep,name=chunkOutputs,proto3" json:"chunkOutputs,omitempty"`
}

func (x *PolicyOutput) Reset() {
	*x = PolicyOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicyOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyOutput) ProtoMessage() {}

func (x *PolicyOutput) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyOutput.ProtoReflect.Descriptor instead.
func (*PolicyOutput) Descriptor() ([]byte, []int) {
	return file_pricing_api_proto_rawDescGZIP(), []int{4}
}

func (x *PolicyOutput) GetPolicyNumber() string {
	if x != nil {
		return x.PolicyNumber
	}
	return ""
}

func (x *PolicyOutput) GetChunkOutputs() []*ChunkOutput {
	if x != nil {
		return x.ChunkOutputs
	}
	return nil
}

type ChunkOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChunkID  string                `protobuf:"bytes,1,opt,name=chunkID,proto3" json:"chunkID,omitempty"`
	Charges  []*Charge             `protobuf:"bytes,2,rep,name=charges,proto3" json:"charges,omitempty"`
	Metadata *ChunkOutput_Metadata `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *ChunkOutput) Reset() {
	*x = ChunkOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChunkOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChunkOutput) ProtoMessage() {}

func (x *ChunkOutput) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChunkOutput.ProtoReflect.Descriptor instead.
func (*ChunkOutput) Descriptor() ([]byte, []int) {
	return file_pricing_api_proto_rawDescGZIP(), []int{5}
}

func (x *ChunkOutput) GetChunkID() string {
	if x != nil {
		return x.ChunkID
	}
	return ""
}

func (x *ChunkOutput) GetCharges() []*Charge {
	if x != nil {
		return x.Charges
	}
	return nil
}

func (x *ChunkOutput) GetMetadata() *ChunkOutput_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type BundleSpec_ChunkSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChunkId string          `protobuf:"bytes,1,opt,name=chunkId,proto3" json:"chunkId,omitempty"`
	Dates   *proto.Interval `protobuf:"bytes,2,opt,name=dates,proto3" json:"dates,omitempty"`
	// Types that are assignable to Data:
	//
	//	*BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData
	//	*BundleSpec_ChunkSpec_FleetBundleChunkSpecData
	//	*BundleSpec_ChunkSpec_BusinessAutoBundleChunkSpecData
	Data isBundleSpec_ChunkSpec_Data `protobuf_oneof:"data"`
}

func (x *BundleSpec_ChunkSpec) Reset() {
	*x = BundleSpec_ChunkSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BundleSpec_ChunkSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BundleSpec_ChunkSpec) ProtoMessage() {}

func (x *BundleSpec_ChunkSpec) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BundleSpec_ChunkSpec.ProtoReflect.Descriptor instead.
func (*BundleSpec_ChunkSpec) Descriptor() ([]byte, []int) {
	return file_pricing_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BundleSpec_ChunkSpec) GetChunkId() string {
	if x != nil {
		return x.ChunkId
	}
	return ""
}

func (x *BundleSpec_ChunkSpec) GetDates() *proto.Interval {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (m *BundleSpec_ChunkSpec) GetData() isBundleSpec_ChunkSpec_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *BundleSpec_ChunkSpec) GetNonFleetBundleChunkSpecData() *NonFleet_BundleChunkSpecData {
	if x, ok := x.GetData().(*BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData); ok {
		return x.NonFleetBundleChunkSpecData
	}
	return nil
}

func (x *BundleSpec_ChunkSpec) GetFleetBundleChunkSpecData() *Fleet_BundleChunkSpecData {
	if x, ok := x.GetData().(*BundleSpec_ChunkSpec_FleetBundleChunkSpecData); ok {
		return x.FleetBundleChunkSpecData
	}
	return nil
}

func (x *BundleSpec_ChunkSpec) GetBusinessAutoBundleChunkSpecData() *BusinessAuto_BundleChunkSpecData {
	if x, ok := x.GetData().(*BundleSpec_ChunkSpec_BusinessAutoBundleChunkSpecData); ok {
		return x.BusinessAutoBundleChunkSpecData
	}
	return nil
}

type isBundleSpec_ChunkSpec_Data interface {
	isBundleSpec_ChunkSpec_Data()
}

type BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData struct {
	NonFleetBundleChunkSpecData *NonFleet_BundleChunkSpecData `protobuf:"bytes,3,opt,name=nonFleetBundleChunkSpecData,proto3,oneof"`
}

type BundleSpec_ChunkSpec_FleetBundleChunkSpecData struct {
	FleetBundleChunkSpecData *Fleet_BundleChunkSpecData `protobuf:"bytes,4,opt,name=fleetBundleChunkSpecData,proto3,oneof"`
}

type BundleSpec_ChunkSpec_BusinessAutoBundleChunkSpecData struct {
	BusinessAutoBundleChunkSpecData *BusinessAuto_BundleChunkSpecData `protobuf:"bytes,5,opt,name=businessAutoBundleChunkSpecData,proto3,oneof"`
}

func (*BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData) isBundleSpec_ChunkSpec_Data() {}

func (*BundleSpec_ChunkSpec_FleetBundleChunkSpecData) isBundleSpec_ChunkSpec_Data() {}

func (*BundleSpec_ChunkSpec_BusinessAutoBundleChunkSpecData) isBundleSpec_ChunkSpec_Data() {}

type PolicySpec_ChunkSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChunkId                                              string                                                 `protobuf:"bytes,1,opt,name=chunkId,proto3" json:"chunkId,omitempty"`
	BlanketRegularAdditionalInsured                      *BlanketRegularAdditionalInsured                       `protobuf:"bytes,2,opt,name=blanketRegularAdditionalInsured,proto3,oneof" json:"blanketRegularAdditionalInsured,omitempty"`
	BlanketPrimaryAndNonContributoryAdditionalInsured    *BlanketPrimaryAndNonContributoryAdditionalInsured     `protobuf:"bytes,3,opt,name=blanketPrimaryAndNonContributoryAdditionalInsured,proto3,oneof" json:"blanketPrimaryAndNonContributoryAdditionalInsured,omitempty"`
	BlanketThirdPartyWithWaiverOfSubrogation             *BlanketThirdPartyWithWaiverOfSubrogation              `protobuf:"bytes,4,opt,name=blanketThirdPartyWithWaiverOfSubrogation,proto3,oneof" json:"blanketThirdPartyWithWaiverOfSubrogation,omitempty"`
	SpecifiedRegularAdditionalInsureds                   []*SpecifiedRegularAdditionalInsured                   `protobuf:"bytes,5,rep,name=specifiedRegularAdditionalInsureds,proto3" json:"specifiedRegularAdditionalInsureds,omitempty"`
	SpecifiedPrimaryAndNonContributoryAdditionalInsureds []*SpecifiedPrimaryAndNonContributoryAdditionalInsured `protobuf:"bytes,6,rep,name=specifiedPrimaryAndNonContributoryAdditionalInsureds,proto3" json:"specifiedPrimaryAndNonContributoryAdditionalInsureds,omitempty"`
	SpecifiedThirdPartiesWithWOS                         []*SpecifiedThirdPartyWithWaiverOfSubrogation          `protobuf:"bytes,7,rep,name=specifiedThirdPartiesWithWOS,proto3" json:"specifiedThirdPartiesWithWOS,omitempty"`
	SubCoverages                                         []SubCoverageType                                      `protobuf:"varint,8,rep,packed,name=subCoverages,proto3,enum=pricing.SubCoverageType" json:"subCoverages,omitempty"`
	LimitSpecs                                           []*LimitSpec                                           `protobuf:"bytes,9,rep,name=limitSpecs,proto3" json:"limitSpecs,omitempty"`
	DeductibleSpecs                                      []*DeductibleSpec                                      `protobuf:"bytes,10,rep,name=deductibleSpecs,proto3" json:"deductibleSpecs,omitempty"`
	ArtifactConfig                                       *ArtifactConfig                                        `protobuf:"bytes,11,opt,name=artifactConfig,proto3" json:"artifactConfig,omitempty"`
	// Types that are assignable to Data:
	//
	//	*PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData
	//	*PolicySpec_ChunkSpec_FleetPolicyChunkSpecData
	//	*PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData
	Data isPolicySpec_ChunkSpec_Data `protobuf_oneof:"data"`
}

func (x *PolicySpec_ChunkSpec) Reset() {
	*x = PolicySpec_ChunkSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicySpec_ChunkSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicySpec_ChunkSpec) ProtoMessage() {}

func (x *PolicySpec_ChunkSpec) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicySpec_ChunkSpec.ProtoReflect.Descriptor instead.
func (*PolicySpec_ChunkSpec) Descriptor() ([]byte, []int) {
	return file_pricing_api_proto_rawDescGZIP(), []int{2, 0}
}

func (x *PolicySpec_ChunkSpec) GetChunkId() string {
	if x != nil {
		return x.ChunkId
	}
	return ""
}

func (x *PolicySpec_ChunkSpec) GetBlanketRegularAdditionalInsured() *BlanketRegularAdditionalInsured {
	if x != nil {
		return x.BlanketRegularAdditionalInsured
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetBlanketPrimaryAndNonContributoryAdditionalInsured() *BlanketPrimaryAndNonContributoryAdditionalInsured {
	if x != nil {
		return x.BlanketPrimaryAndNonContributoryAdditionalInsured
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetBlanketThirdPartyWithWaiverOfSubrogation() *BlanketThirdPartyWithWaiverOfSubrogation {
	if x != nil {
		return x.BlanketThirdPartyWithWaiverOfSubrogation
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetSpecifiedRegularAdditionalInsureds() []*SpecifiedRegularAdditionalInsured {
	if x != nil {
		return x.SpecifiedRegularAdditionalInsureds
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetSpecifiedPrimaryAndNonContributoryAdditionalInsureds() []*SpecifiedPrimaryAndNonContributoryAdditionalInsured {
	if x != nil {
		return x.SpecifiedPrimaryAndNonContributoryAdditionalInsureds
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetSpecifiedThirdPartiesWithWOS() []*SpecifiedThirdPartyWithWaiverOfSubrogation {
	if x != nil {
		return x.SpecifiedThirdPartiesWithWOS
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetSubCoverages() []SubCoverageType {
	if x != nil {
		return x.SubCoverages
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetLimitSpecs() []*LimitSpec {
	if x != nil {
		return x.LimitSpecs
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetDeductibleSpecs() []*DeductibleSpec {
	if x != nil {
		return x.DeductibleSpecs
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetArtifactConfig() *ArtifactConfig {
	if x != nil {
		return x.ArtifactConfig
	}
	return nil
}

func (m *PolicySpec_ChunkSpec) GetData() isPolicySpec_ChunkSpec_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetNonFleetPolicyChunkSpecData() *NonFleet_PolicyChunkSpecData {
	if x, ok := x.GetData().(*PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData); ok {
		return x.NonFleetPolicyChunkSpecData
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetFleetPolicyChunkSpecData() *Fleet_PolicyChunkSpecData {
	if x, ok := x.GetData().(*PolicySpec_ChunkSpec_FleetPolicyChunkSpecData); ok {
		return x.FleetPolicyChunkSpecData
	}
	return nil
}

func (x *PolicySpec_ChunkSpec) GetBusinessAutoPolicyChunkSpecData() *BusinessAuto_PolicyChunkSpecData {
	if x, ok := x.GetData().(*PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData); ok {
		return x.BusinessAutoPolicyChunkSpecData
	}
	return nil
}

type isPolicySpec_ChunkSpec_Data interface {
	isPolicySpec_ChunkSpec_Data()
}

type PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData struct {
	NonFleetPolicyChunkSpecData *NonFleet_PolicyChunkSpecData `protobuf:"bytes,12,opt,name=nonFleetPolicyChunkSpecData,proto3,oneof"`
}

type PolicySpec_ChunkSpec_FleetPolicyChunkSpecData struct {
	FleetPolicyChunkSpecData *Fleet_PolicyChunkSpecData `protobuf:"bytes,13,opt,name=fleetPolicyChunkSpecData,proto3,oneof"`
}

type PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData struct {
	BusinessAutoPolicyChunkSpecData *BusinessAuto_PolicyChunkSpecData `protobuf:"bytes,14,opt,name=businessAutoPolicyChunkSpecData,proto3,oneof"`
}

func (*PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData) isPolicySpec_ChunkSpec_Data() {}

func (*PolicySpec_ChunkSpec_FleetPolicyChunkSpecData) isPolicySpec_ChunkSpec_Data() {}

func (*PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData) isPolicySpec_ChunkSpec_Data() {}

type ChunkOutput_Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginsMetadata *PluginsMetadata `protobuf:"bytes,1,opt,name=pluginsMetadata,proto3" json:"pluginsMetadata,omitempty"`
	// Types that are assignable to ProgramSpecificMetadata:
	//
	//	*ChunkOutput_Metadata_NonFleetChunkOutputMetadata
	//	*ChunkOutput_Metadata_FleetChunkOutputMetadata
	//	*ChunkOutput_Metadata_BusinessAutoChunkOutputMetadata
	ProgramSpecificMetadata isChunkOutput_Metadata_ProgramSpecificMetadata `protobuf_oneof:"programSpecificMetadata"`
}

func (x *ChunkOutput_Metadata) Reset() {
	*x = ChunkOutput_Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChunkOutput_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChunkOutput_Metadata) ProtoMessage() {}

func (x *ChunkOutput_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChunkOutput_Metadata.ProtoReflect.Descriptor instead.
func (*ChunkOutput_Metadata) Descriptor() ([]byte, []int) {
	return file_pricing_api_proto_rawDescGZIP(), []int{5, 0}
}

func (x *ChunkOutput_Metadata) GetPluginsMetadata() *PluginsMetadata {
	if x != nil {
		return x.PluginsMetadata
	}
	return nil
}

func (m *ChunkOutput_Metadata) GetProgramSpecificMetadata() isChunkOutput_Metadata_ProgramSpecificMetadata {
	if m != nil {
		return m.ProgramSpecificMetadata
	}
	return nil
}

func (x *ChunkOutput_Metadata) GetNonFleetChunkOutputMetadata() *NonFleet_ChunkOutputMetadata {
	if x, ok := x.GetProgramSpecificMetadata().(*ChunkOutput_Metadata_NonFleetChunkOutputMetadata); ok {
		return x.NonFleetChunkOutputMetadata
	}
	return nil
}

func (x *ChunkOutput_Metadata) GetFleetChunkOutputMetadata() *Fleet_ChunkOutputMetadata {
	if x, ok := x.GetProgramSpecificMetadata().(*ChunkOutput_Metadata_FleetChunkOutputMetadata); ok {
		return x.FleetChunkOutputMetadata
	}
	return nil
}

func (x *ChunkOutput_Metadata) GetBusinessAutoChunkOutputMetadata() *BusinessAuto_ChunkOutputMetadata {
	if x, ok := x.GetProgramSpecificMetadata().(*ChunkOutput_Metadata_BusinessAutoChunkOutputMetadata); ok {
		return x.BusinessAutoChunkOutputMetadata
	}
	return nil
}

type isChunkOutput_Metadata_ProgramSpecificMetadata interface {
	isChunkOutput_Metadata_ProgramSpecificMetadata()
}

type ChunkOutput_Metadata_NonFleetChunkOutputMetadata struct {
	NonFleetChunkOutputMetadata *NonFleet_ChunkOutputMetadata `protobuf:"bytes,2,opt,name=nonFleetChunkOutputMetadata,proto3,oneof"`
}

type ChunkOutput_Metadata_FleetChunkOutputMetadata struct {
	FleetChunkOutputMetadata *Fleet_ChunkOutputMetadata `protobuf:"bytes,3,opt,name=fleetChunkOutputMetadata,proto3,oneof"`
}

type ChunkOutput_Metadata_BusinessAutoChunkOutputMetadata struct {
	BusinessAutoChunkOutputMetadata *BusinessAuto_ChunkOutputMetadata `protobuf:"bytes,4,opt,name=businessAutoChunkOutputMetadata,proto3,oneof"`
}

func (*ChunkOutput_Metadata_NonFleetChunkOutputMetadata) isChunkOutput_Metadata_ProgramSpecificMetadata() {
}

func (*ChunkOutput_Metadata_FleetChunkOutputMetadata) isChunkOutput_Metadata_ProgramSpecificMetadata() {
}

func (*ChunkOutput_Metadata_BusinessAutoChunkOutputMetadata) isChunkOutput_Metadata_ProgramSpecificMetadata() {
}

var File_pricing_api_proto protoreflect.FileDescriptor

var file_pricing_api_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x1a, 0x11, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x13, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x6e, 0x6f,
	0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61,
	0x75, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x16, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2f, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbd, 0x01, 0x0a, 0x07,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x0a, 0x62, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x52, 0x0a, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x35, 0x0a, 0x0b,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x53, 0x70,
	0x65, 0x63, 0x73, 0x12, 0x36, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0xe7, 0x03, 0x0a, 0x0a,
	0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x3d, 0x0a, 0x0a, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53,
	0x70, 0x65, 0x63, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0a, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x73, 0x1a, 0x99, 0x03, 0x0a, 0x09, 0x43, 0x68,
	0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x75, 0x6e, 0x6b,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x52, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x69, 0x0a, 0x1b, 0x6e, 0x6f, 0x6e,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b,
	0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65,
	0x74, 0x2e, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65,
	0x63, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1b, 0x6e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65,
	0x74, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x60, 0x0a, 0x18, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x42, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x18, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70,
	0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x75, 0x0a, 0x1f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x68, 0x75, 0x6e,
	0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x2e, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x42, 0x06, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x9e, 0x0f, 0x0a, 0x0a, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x53, 0x70, 0x65, 0x63, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x0a, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a,
	0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x61, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x52, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b,
	0x53, 0x70, 0x65, 0x63, 0x52, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x73,
	0x12, 0x30, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x70,
	0x65, 0x63, 0x12, 0x3b, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x44, 0x52, 0x0f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x1a,
	0xd4, 0x0c, 0x0a, 0x09, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x77, 0x0a, 0x1f, 0x62, 0x6c, 0x61, 0x6e, 0x6b,
	0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6c, 0x61, 0x6e, 0x6b,
	0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x48, 0x01, 0x52, 0x1f, 0x62, 0x6c,
	0x61, 0x6e, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x88, 0x01, 0x01,
	0x12, 0xad, 0x01, 0x0a, 0x31, 0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x50, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x50, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x48, 0x02, 0x52, 0x31, 0x62, 0x6c, 0x61, 0x6e,
	0x6b, 0x65, 0x74, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x92, 0x01, 0x0a, 0x28, 0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x54, 0x68, 0x69, 0x72,
	0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72,
	0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x6c,
	0x61, 0x6e, 0x6b, 0x65, 0x74, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x57,
	0x69, 0x74, 0x68, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x03, 0x52, 0x28, 0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65,
	0x74, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x57,
	0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x7a, 0x0a, 0x22, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x52, 0x22, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64,
	0x73, 0x12, 0xb0, 0x01, 0x0a, 0x34, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x52, 0x34,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f,
	0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x65, 0x64, 0x73, 0x12, 0x77, 0x0a, 0x1c, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x69, 0x65, 0x73, 0x57, 0x69, 0x74,
	0x68, 0x57, 0x4f, 0x53, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x68,
	0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x57, 0x61, 0x69, 0x76,
	0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x1c, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50,
	0x61, 0x72, 0x74, 0x69, 0x65, 0x73, 0x57, 0x69, 0x74, 0x68, 0x57, 0x4f, 0x53, 0x12, 0x3c, 0x0a,
	0x0c, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x75,
	0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73,
	0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x0a, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53,
	0x70, 0x65, 0x63, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x70, 0x65, 0x63, 0x73, 0x12,
	0x41, 0x0a, 0x0f, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x70, 0x65,
	0x63, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x70, 0x65,
	0x63, 0x52, 0x0f, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x70, 0x65,
	0x63, 0x73, 0x12, 0x3f, 0x0a, 0x0e, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0e, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x69, 0x0a, 0x1b, 0x6e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x1b, 0x6e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x60,
	0x0a, 0x18, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x68, 0x75,
	0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74,
	0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x18, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x75, 0x0a, 0x1f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x6f,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44,
	0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x6f,
	0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x41, 0x75, 0x74, 0x6f, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53,
	0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x42,
	0x22, 0x0a, 0x20, 0x5f, 0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c,
	0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x65, 0x64, 0x42, 0x34, 0x0a, 0x32, 0x5f, 0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x42, 0x2b, 0x0a, 0x29, 0x5f, 0x62, 0x6c,
	0x61, 0x6e, 0x6b, 0x65, 0x74, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x57,
	0x69, 0x74, 0x68, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x47, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3b, 0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x52, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x22,
	0x6c, 0x0a, 0x0c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12,
	0x22, 0x0a, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0c, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52,
	0x0c, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x22, 0xbd, 0x04,
	0x0a, 0x0b, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x44, 0x12, 0x29, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x07, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x12, 0x39, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0xad, 0x03,
	0x0a, 0x08, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x0f, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0f, 0x70,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x69,
	0x0a, 0x1b, 0x6e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4e, 0x6f,
	0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1b, 0x6e, 0x6f,
	0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x60, 0x0a, 0x18, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43, 0x68, 0x75, 0x6e,
	0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x18, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x75, 0x0a, 0x1f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x68, 0x75, 0x6e, 0x6b,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x75, 0x6e,
	0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x1f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x43,
	0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x42, 0x19, 0x0a, 0x17, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x32, 0x3c, 0x0a,
	0x07, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x31, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x10, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x33, 0x5a, 0x31, 0x6e,
	0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e,
	0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pricing_api_proto_rawDescOnce sync.Once
	file_pricing_api_proto_rawDescData = file_pricing_api_proto_rawDesc
)

func file_pricing_api_proto_rawDescGZIP() []byte {
	file_pricing_api_proto_rawDescOnce.Do(func() {
		file_pricing_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pricing_api_proto_rawDescData)
	})
	return file_pricing_api_proto_rawDescData
}

var file_pricing_api_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_pricing_api_proto_goTypes = []interface{}{
	(*Request)(nil),                          // 0: pricing.Request
	(*BundleSpec)(nil),                       // 1: pricing.BundleSpec
	(*PolicySpec)(nil),                       // 2: pricing.PolicySpec
	(*Response)(nil),                         // 3: pricing.Response
	(*PolicyOutput)(nil),                     // 4: pricing.PolicyOutput
	(*ChunkOutput)(nil),                      // 5: pricing.ChunkOutput
	(*BundleSpec_ChunkSpec)(nil),             // 6: pricing.BundleSpec.ChunkSpec
	(*PolicySpec_ChunkSpec)(nil),             // 7: pricing.PolicySpec.ChunkSpec
	(*ChunkOutput_Metadata)(nil),             // 8: pricing.ChunkOutput.Metadata
	(ProgramType)(0),                         // 9: pricing.ProgramType
	(PolicyName)(0),                          // 10: pricing.PolicyName
	(*proto.Interval)(nil),                   // 11: common.Interval
	(*ModelSpec)(nil),                        // 12: pricing.ModelSpec
	(PluginID)(0),                            // 13: pricing.PluginID
	(*Charge)(nil),                           // 14: pricing.Charge
	(*NonFleet_BundleChunkSpecData)(nil),     // 15: pricing.NonFleet.BundleChunkSpecData
	(*Fleet_BundleChunkSpecData)(nil),        // 16: pricing.Fleet.BundleChunkSpecData
	(*BusinessAuto_BundleChunkSpecData)(nil), // 17: pricing.BusinessAuto.BundleChunkSpecData
	(*BlanketRegularAdditionalInsured)(nil),  // 18: pricing.BlanketRegularAdditionalInsured
	(*BlanketPrimaryAndNonContributoryAdditionalInsured)(nil),   // 19: pricing.BlanketPrimaryAndNonContributoryAdditionalInsured
	(*BlanketThirdPartyWithWaiverOfSubrogation)(nil),            // 20: pricing.BlanketThirdPartyWithWaiverOfSubrogation
	(*SpecifiedRegularAdditionalInsured)(nil),                   // 21: pricing.SpecifiedRegularAdditionalInsured
	(*SpecifiedPrimaryAndNonContributoryAdditionalInsured)(nil), // 22: pricing.SpecifiedPrimaryAndNonContributoryAdditionalInsured
	(*SpecifiedThirdPartyWithWaiverOfSubrogation)(nil),          // 23: pricing.SpecifiedThirdPartyWithWaiverOfSubrogation
	(SubCoverageType)(0),                     // 24: pricing.SubCoverageType
	(*LimitSpec)(nil),                        // 25: pricing.LimitSpec
	(*DeductibleSpec)(nil),                   // 26: pricing.DeductibleSpec
	(*ArtifactConfig)(nil),                   // 27: pricing.ArtifactConfig
	(*NonFleet_PolicyChunkSpecData)(nil),     // 28: pricing.NonFleet.PolicyChunkSpecData
	(*Fleet_PolicyChunkSpecData)(nil),        // 29: pricing.Fleet.PolicyChunkSpecData
	(*BusinessAuto_PolicyChunkSpecData)(nil), // 30: pricing.BusinessAuto.PolicyChunkSpecData
	(*PluginsMetadata)(nil),                  // 31: pricing.PluginsMetadata
	(*NonFleet_ChunkOutputMetadata)(nil),     // 32: pricing.NonFleet.ChunkOutputMetadata
	(*Fleet_ChunkOutputMetadata)(nil),        // 33: pricing.Fleet.ChunkOutputMetadata
	(*BusinessAuto_ChunkOutputMetadata)(nil), // 34: pricing.BusinessAuto.ChunkOutputMetadata
}
var file_pricing_api_proto_depIdxs = []int32{
	1,  // 0: pricing.Request.bundleSpec:type_name -> pricing.BundleSpec
	2,  // 1: pricing.Request.policySpecs:type_name -> pricing.PolicySpec
	9,  // 2: pricing.Request.programType:type_name -> pricing.ProgramType
	6,  // 3: pricing.BundleSpec.chunkSpecs:type_name -> pricing.BundleSpec.ChunkSpec
	10, // 4: pricing.PolicySpec.policyName:type_name -> pricing.PolicyName
	11, // 5: pricing.PolicySpec.policyDates:type_name -> common.Interval
	7,  // 6: pricing.PolicySpec.chunkSpecs:type_name -> pricing.PolicySpec.ChunkSpec
	12, // 7: pricing.PolicySpec.modelSpec:type_name -> pricing.ModelSpec
	13, // 8: pricing.PolicySpec.optionalPlugins:type_name -> pricing.PluginID
	4,  // 9: pricing.Response.policyOutputs:type_name -> pricing.PolicyOutput
	5,  // 10: pricing.PolicyOutput.chunkOutputs:type_name -> pricing.ChunkOutput
	14, // 11: pricing.ChunkOutput.charges:type_name -> pricing.Charge
	8,  // 12: pricing.ChunkOutput.metadata:type_name -> pricing.ChunkOutput.Metadata
	11, // 13: pricing.BundleSpec.ChunkSpec.dates:type_name -> common.Interval
	15, // 14: pricing.BundleSpec.ChunkSpec.nonFleetBundleChunkSpecData:type_name -> pricing.NonFleet.BundleChunkSpecData
	16, // 15: pricing.BundleSpec.ChunkSpec.fleetBundleChunkSpecData:type_name -> pricing.Fleet.BundleChunkSpecData
	17, // 16: pricing.BundleSpec.ChunkSpec.businessAutoBundleChunkSpecData:type_name -> pricing.BusinessAuto.BundleChunkSpecData
	18, // 17: pricing.PolicySpec.ChunkSpec.blanketRegularAdditionalInsured:type_name -> pricing.BlanketRegularAdditionalInsured
	19, // 18: pricing.PolicySpec.ChunkSpec.blanketPrimaryAndNonContributoryAdditionalInsured:type_name -> pricing.BlanketPrimaryAndNonContributoryAdditionalInsured
	20, // 19: pricing.PolicySpec.ChunkSpec.blanketThirdPartyWithWaiverOfSubrogation:type_name -> pricing.BlanketThirdPartyWithWaiverOfSubrogation
	21, // 20: pricing.PolicySpec.ChunkSpec.specifiedRegularAdditionalInsureds:type_name -> pricing.SpecifiedRegularAdditionalInsured
	22, // 21: pricing.PolicySpec.ChunkSpec.specifiedPrimaryAndNonContributoryAdditionalInsureds:type_name -> pricing.SpecifiedPrimaryAndNonContributoryAdditionalInsured
	23, // 22: pricing.PolicySpec.ChunkSpec.specifiedThirdPartiesWithWOS:type_name -> pricing.SpecifiedThirdPartyWithWaiverOfSubrogation
	24, // 23: pricing.PolicySpec.ChunkSpec.subCoverages:type_name -> pricing.SubCoverageType
	25, // 24: pricing.PolicySpec.ChunkSpec.limitSpecs:type_name -> pricing.LimitSpec
	26, // 25: pricing.PolicySpec.ChunkSpec.deductibleSpecs:type_name -> pricing.DeductibleSpec
	27, // 26: pricing.PolicySpec.ChunkSpec.artifactConfig:type_name -> pricing.ArtifactConfig
	28, // 27: pricing.PolicySpec.ChunkSpec.nonFleetPolicyChunkSpecData:type_name -> pricing.NonFleet.PolicyChunkSpecData
	29, // 28: pricing.PolicySpec.ChunkSpec.fleetPolicyChunkSpecData:type_name -> pricing.Fleet.PolicyChunkSpecData
	30, // 29: pricing.PolicySpec.ChunkSpec.businessAutoPolicyChunkSpecData:type_name -> pricing.BusinessAuto.PolicyChunkSpecData
	31, // 30: pricing.ChunkOutput.Metadata.pluginsMetadata:type_name -> pricing.PluginsMetadata
	32, // 31: pricing.ChunkOutput.Metadata.nonFleetChunkOutputMetadata:type_name -> pricing.NonFleet.ChunkOutputMetadata
	33, // 32: pricing.ChunkOutput.Metadata.fleetChunkOutputMetadata:type_name -> pricing.Fleet.ChunkOutputMetadata
	34, // 33: pricing.ChunkOutput.Metadata.businessAutoChunkOutputMetadata:type_name -> pricing.BusinessAuto.ChunkOutputMetadata
	0,  // 34: pricing.Pricing.GetPrice:input_type -> pricing.Request
	3,  // 35: pricing.Pricing.GetPrice:output_type -> pricing.Response
	35, // [35:36] is the sub-list for method output_type
	34, // [34:35] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_pricing_api_proto_init() }
func file_pricing_api_proto_init() {
	if File_pricing_api_proto != nil {
		return
	}
	file_pricing_fleet_proto_init()
	file_pricing_nonfleet_proto_init()
	file_pricing_business_auto_proto_init()
	file_pricing_configs_proto_init()
	file_pricing_policies_proto_init()
	file_pricing_sub_coverages_proto_init()
	file_pricing_charges_proto_init()
	file_pricing_plugins_proto_init()
	file_pricing_programs_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pricing_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BundleSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicySpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicyOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChunkOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BundleSpec_ChunkSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicySpec_ChunkSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChunkOutput_Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pricing_api_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData)(nil),
		(*BundleSpec_ChunkSpec_FleetBundleChunkSpecData)(nil),
		(*BundleSpec_ChunkSpec_BusinessAutoBundleChunkSpecData)(nil),
	}
	file_pricing_api_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData)(nil),
		(*PolicySpec_ChunkSpec_FleetPolicyChunkSpecData)(nil),
		(*PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData)(nil),
	}
	file_pricing_api_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*ChunkOutput_Metadata_NonFleetChunkOutputMetadata)(nil),
		(*ChunkOutput_Metadata_FleetChunkOutputMetadata)(nil),
		(*ChunkOutput_Metadata_BusinessAutoChunkOutputMetadata)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricing_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pricing_api_proto_goTypes,
		DependencyIndexes: file_pricing_api_proto_depIdxs,
		MessageInfos:      file_pricing_api_proto_msgTypes,
	}.Build()
	File_pricing_api_proto = out.File
	file_pricing_api_proto_rawDesc = nil
	file_pricing_api_proto_goTypes = nil
	file_pricing_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// PricingClient is the client API for Pricing service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PricingClient interface {
	GetPrice(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error)
}

type pricingClient struct {
	cc grpc.ClientConnInterface
}

func NewPricingClient(cc grpc.ClientConnInterface) PricingClient {
	return &pricingClient{cc}
}

func (c *pricingClient) GetPrice(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, "/pricing.Pricing/GetPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PricingServer is the server API for Pricing service.
type PricingServer interface {
	GetPrice(context.Context, *Request) (*Response, error)
}

// UnimplementedPricingServer can be embedded to have forward compatible implementations.
type UnimplementedPricingServer struct {
}

func (*UnimplementedPricingServer) GetPrice(context.Context, *Request) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPrice not implemented")
}

func RegisterPricingServer(s *grpc.Server, srv PricingServer) {
	s.RegisterService(&_Pricing_serviceDesc, srv)
}

func _Pricing_GetPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).GetPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pricing.Pricing/GetPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).GetPrice(ctx, req.(*Request))
	}
	return interceptor(ctx, in, info, handler)
}

var _Pricing_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pricing.Pricing",
	HandlerType: (*PricingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPrice",
			Handler:    _Pricing_GetPrice_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pricing/api.proto",
}
