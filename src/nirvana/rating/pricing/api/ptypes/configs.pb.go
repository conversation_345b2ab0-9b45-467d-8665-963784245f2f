// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pricing/configs.proto

package ptypes

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ModelSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Provider string        `protobuf:"bytes,1,opt,name=provider,proto3" json:"provider,omitempty"`
	State    string        `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	Version  *ModelVersion `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *ModelSpec) Reset() {
	*x = ModelSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_configs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSpec) ProtoMessage() {}

func (x *ModelSpec) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_configs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSpec.ProtoReflect.Descriptor instead.
func (*ModelSpec) Descriptor() ([]byte, []int) {
	return file_pricing_configs_proto_rawDescGZIP(), []int{0}
}

func (x *ModelSpec) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *ModelSpec) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ModelSpec) GetVersion() *ModelVersion {
	if x != nil {
		return x.Version
	}
	return nil
}

type ModelVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Major int32 `protobuf:"varint,1,opt,name=major,proto3" json:"major,omitempty"`
	Minor int32 `protobuf:"varint,2,opt,name=minor,proto3" json:"minor,omitempty"`
	Patch int32 `protobuf:"varint,3,opt,name=patch,proto3" json:"patch,omitempty"`
}

func (x *ModelVersion) Reset() {
	*x = ModelVersion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_configs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelVersion) ProtoMessage() {}

func (x *ModelVersion) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_configs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelVersion.ProtoReflect.Descriptor instead.
func (*ModelVersion) Descriptor() ([]byte, []int) {
	return file_pricing_configs_proto_rawDescGZIP(), []int{1}
}

func (x *ModelVersion) GetMajor() int32 {
	if x != nil {
		return x.Major
	}
	return 0
}

func (x *ModelVersion) GetMinor() int32 {
	if x != nil {
		return x.Minor
	}
	return 0
}

func (x *ModelVersion) GetPatch() int32 {
	if x != nil {
		return x.Patch
	}
	return 0
}

type ArtifactConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileKey string `protobuf:"bytes,1,opt,name=fileKey,proto3" json:"fileKey,omitempty"`
}

func (x *ArtifactConfig) Reset() {
	*x = ArtifactConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_configs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArtifactConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtifactConfig) ProtoMessage() {}

func (x *ArtifactConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_configs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtifactConfig.ProtoReflect.Descriptor instead.
func (*ArtifactConfig) Descriptor() ([]byte, []int) {
	return file_pricing_configs_proto_rawDescGZIP(), []int{2}
}

func (x *ArtifactConfig) GetFileKey() string {
	if x != nil {
		return x.FileKey
	}
	return ""
}

var File_pricing_configs_proto protoreflect.FileDescriptor

var file_pricing_configs_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x22, 0x6e, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x2f, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0x50, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6e, 0x6f, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x69, 0x6e, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x22, 0x2a, 0x0a, 0x0e, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x42, 0x33,
	0x5a, 0x31, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pricing_configs_proto_rawDescOnce sync.Once
	file_pricing_configs_proto_rawDescData = file_pricing_configs_proto_rawDesc
)

func file_pricing_configs_proto_rawDescGZIP() []byte {
	file_pricing_configs_proto_rawDescOnce.Do(func() {
		file_pricing_configs_proto_rawDescData = protoimpl.X.CompressGZIP(file_pricing_configs_proto_rawDescData)
	})
	return file_pricing_configs_proto_rawDescData
}

var file_pricing_configs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_pricing_configs_proto_goTypes = []interface{}{
	(*ModelSpec)(nil),      // 0: pricing.ModelSpec
	(*ModelVersion)(nil),   // 1: pricing.ModelVersion
	(*ArtifactConfig)(nil), // 2: pricing.ArtifactConfig
}
var file_pricing_configs_proto_depIdxs = []int32{
	1, // 0: pricing.ModelSpec.version:type_name -> pricing.ModelVersion
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pricing_configs_proto_init() }
func file_pricing_configs_proto_init() {
	if File_pricing_configs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pricing_configs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_configs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelVersion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_configs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArtifactConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricing_configs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pricing_configs_proto_goTypes,
		DependencyIndexes: file_pricing_configs_proto_depIdxs,
		MessageInfos:      file_pricing_configs_proto_msgTypes,
	}.Build()
	File_pricing_configs_proto = out.File
	file_pricing_configs_proto_rawDesc = nil
	file_pricing_configs_proto_goTypes = nil
	file_pricing_configs_proto_depIdxs = nil
}
