// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pricing/common.proto

package ptypes

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ScheduleModification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubCoverageGroup *SubCoverageGroup `protobuf:"bytes,1,opt,name=subCoverageGroup,proto3" json:"subCoverageGroup,omitempty"`
	Percentage       float64           `protobuf:"fixed64,2,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *ScheduleModification) Reset() {
	*x = ScheduleModification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleModification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleModification) ProtoMessage() {}

func (x *ScheduleModification) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleModification.ProtoReflect.Descriptor instead.
func (*ScheduleModification) Descriptor() ([]byte, []int) {
	return file_pricing_common_proto_rawDescGZIP(), []int{0}
}

func (x *ScheduleModification) GetSubCoverageGroup() *SubCoverageGroup {
	if x != nil {
		return x.SubCoverageGroup
	}
	return nil
}

func (x *ScheduleModification) GetPercentage() float64 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type ExperienceRatingModification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubCoverageGroup *SubCoverageGroup `protobuf:"bytes,1,opt,name=subCoverageGroup,proto3" json:"subCoverageGroup,omitempty"`
	Percentage       float64           `protobuf:"fixed64,2,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *ExperienceRatingModification) Reset() {
	*x = ExperienceRatingModification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExperienceRatingModification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExperienceRatingModification) ProtoMessage() {}

func (x *ExperienceRatingModification) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExperienceRatingModification.ProtoReflect.Descriptor instead.
func (*ExperienceRatingModification) Descriptor() ([]byte, []int) {
	return file_pricing_common_proto_rawDescGZIP(), []int{1}
}

func (x *ExperienceRatingModification) GetSubCoverageGroup() *SubCoverageGroup {
	if x != nil {
		return x.SubCoverageGroup
	}
	return nil
}

func (x *ExperienceRatingModification) GetPercentage() float64 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type LossFreeModification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubCoverageGroup *SubCoverageGroup `protobuf:"bytes,1,opt,name=subCoverageGroup,proto3" json:"subCoverageGroup,omitempty"`
	Percentage       float64           `protobuf:"fixed64,2,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *LossFreeModification) Reset() {
	*x = LossFreeModification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LossFreeModification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LossFreeModification) ProtoMessage() {}

func (x *LossFreeModification) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LossFreeModification.ProtoReflect.Descriptor instead.
func (*LossFreeModification) Descriptor() ([]byte, []int) {
	return file_pricing_common_proto_rawDescGZIP(), []int{2}
}

func (x *LossFreeModification) GetSubCoverageGroup() *SubCoverageGroup {
	if x != nil {
		return x.SubCoverageGroup
	}
	return nil
}

func (x *LossFreeModification) GetPercentage() float64 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

var File_pricing_common_proto protoreflect.FileDescriptor

var file_pricing_common_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x1a,
	0x1b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7d, 0x0a, 0x14,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x10, 0x73, 0x75, 0x62, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x1c,
	0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x10,
	0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x10, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x22, 0x7d, 0x0a, 0x14, 0x4c, 0x6f, 0x73, 0x73, 0x46, 0x72, 0x65, 0x65, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x10, 0x73,
	0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e,
	0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x10, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x42, 0x33, 0x5a, 0x31, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63,
	0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x72, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x70, 0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pricing_common_proto_rawDescOnce sync.Once
	file_pricing_common_proto_rawDescData = file_pricing_common_proto_rawDesc
)

func file_pricing_common_proto_rawDescGZIP() []byte {
	file_pricing_common_proto_rawDescOnce.Do(func() {
		file_pricing_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_pricing_common_proto_rawDescData)
	})
	return file_pricing_common_proto_rawDescData
}

var file_pricing_common_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_pricing_common_proto_goTypes = []interface{}{
	(*ScheduleModification)(nil),         // 0: pricing.ScheduleModification
	(*ExperienceRatingModification)(nil), // 1: pricing.ExperienceRatingModification
	(*LossFreeModification)(nil),         // 2: pricing.LossFreeModification
	(*SubCoverageGroup)(nil),             // 3: pricing.SubCoverageGroup
}
var file_pricing_common_proto_depIdxs = []int32{
	3, // 0: pricing.ScheduleModification.subCoverageGroup:type_name -> pricing.SubCoverageGroup
	3, // 1: pricing.ExperienceRatingModification.subCoverageGroup:type_name -> pricing.SubCoverageGroup
	3, // 2: pricing.LossFreeModification.subCoverageGroup:type_name -> pricing.SubCoverageGroup
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pricing_common_proto_init() }
func file_pricing_common_proto_init() {
	if File_pricing_common_proto != nil {
		return
	}
	file_pricing_sub_coverages_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pricing_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScheduleModification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExperienceRatingModification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LossFreeModification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricing_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pricing_common_proto_goTypes,
		DependencyIndexes: file_pricing_common_proto_depIdxs,
		MessageInfos:      file_pricing_common_proto_msgTypes,
	}.Build()
	File_pricing_common_proto = out.File
	file_pricing_common_proto_rawDesc = nil
	file_pricing_common_proto_goTypes = nil
	file_pricing_common_proto_depIdxs = nil
}
