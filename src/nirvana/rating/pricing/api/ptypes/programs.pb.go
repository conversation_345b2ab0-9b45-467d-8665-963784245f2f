// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pricing/programs.proto

package ptypes

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProgramType int32

const (
	ProgramType_ProgramType_Unspecified  ProgramType = 0
	ProgramType_ProgramType_NonFleet     ProgramType = 1
	ProgramType_ProgramType_Fleet        ProgramType = 2
	ProgramType_ProgramType_BusinessAuto ProgramType = 3
)

// Enum value maps for ProgramType.
var (
	ProgramType_name = map[int32]string{
		0: "ProgramType_Unspecified",
		1: "ProgramType_NonFleet",
		2: "ProgramType_Fleet",
		3: "ProgramType_BusinessAuto",
	}
	ProgramType_value = map[string]int32{
		"ProgramType_Unspecified":  0,
		"ProgramType_NonFleet":     1,
		"ProgramType_Fleet":        2,
		"ProgramType_BusinessAuto": 3,
	}
)

func (x ProgramType) Enum() *ProgramType {
	p := new(ProgramType)
	*p = x
	return p
}

func (x ProgramType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProgramType) Descriptor() protoreflect.EnumDescriptor {
	return file_pricing_programs_proto_enumTypes[0].Descriptor()
}

func (ProgramType) Type() protoreflect.EnumType {
	return &file_pricing_programs_proto_enumTypes[0]
}

func (x ProgramType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProgramType.Descriptor instead.
func (ProgramType) EnumDescriptor() ([]byte, []int) {
	return file_pricing_programs_proto_rawDescGZIP(), []int{0}
}

var File_pricing_programs_proto protoreflect.FileDescriptor

var file_pricing_programs_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2a, 0x79, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x18, 0x0a,
	0x14, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x10, 0x02, 0x12, 0x1c,
	0x0a, 0x18, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x10, 0x03, 0x42, 0x33, 0x5a, 0x31,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pricing_programs_proto_rawDescOnce sync.Once
	file_pricing_programs_proto_rawDescData = file_pricing_programs_proto_rawDesc
)

func file_pricing_programs_proto_rawDescGZIP() []byte {
	file_pricing_programs_proto_rawDescOnce.Do(func() {
		file_pricing_programs_proto_rawDescData = protoimpl.X.CompressGZIP(file_pricing_programs_proto_rawDescData)
	})
	return file_pricing_programs_proto_rawDescData
}

var file_pricing_programs_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pricing_programs_proto_goTypes = []interface{}{
	(ProgramType)(0), // 0: pricing.ProgramType
}
var file_pricing_programs_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pricing_programs_proto_init() }
func file_pricing_programs_proto_init() {
	if File_pricing_programs_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricing_programs_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pricing_programs_proto_goTypes,
		DependencyIndexes: file_pricing_programs_proto_depIdxs,
		EnumInfos:         file_pricing_programs_proto_enumTypes,
	}.Build()
	File_pricing_programs_proto = out.File
	file_pricing_programs_proto_rawDesc = nil
	file_pricing_programs_proto_goTypes = nil
	file_pricing_programs_proto_depIdxs = nil
}
