// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pricing/policies.proto

package ptypes

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PolicyName int32

const (
	PolicyName_PolicyName_Unspecified       PolicyName = 0
	PolicyName_PolicyName_MOTOR_CARRIER     PolicyName = 1
	PolicyName_PolicyName_MOTOR_TRUCK_CARGO PolicyName = 2
	PolicyName_PolicyName_GENERAL_LIABILITY PolicyName = 3
	PolicyName_PolicyName_BUSINESS_AUTO     PolicyName = 4
)

// Enum value maps for PolicyName.
var (
	PolicyName_name = map[int32]string{
		0: "PolicyName_Unspecified",
		1: "PolicyName_MOTOR_CARRIER",
		2: "PolicyName_MOTOR_TRUCK_CARGO",
		3: "PolicyName_GENERAL_LIABILITY",
		4: "PolicyName_BUSINESS_AUTO",
	}
	PolicyName_value = map[string]int32{
		"PolicyName_Unspecified":       0,
		"PolicyName_MOTOR_CARRIER":     1,
		"PolicyName_MOTOR_TRUCK_CARGO": 2,
		"PolicyName_GENERAL_LIABILITY": 3,
		"PolicyName_BUSINESS_AUTO":     4,
	}
)

func (x PolicyName) Enum() *PolicyName {
	p := new(PolicyName)
	*p = x
	return p
}

func (x PolicyName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PolicyName) Descriptor() protoreflect.EnumDescriptor {
	return file_pricing_policies_proto_enumTypes[0].Descriptor()
}

func (PolicyName) Type() protoreflect.EnumType {
	return &file_pricing_policies_proto_enumTypes[0]
}

func (x PolicyName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PolicyName.Descriptor instead.
func (PolicyName) EnumDescriptor() ([]byte, []int) {
	return file_pricing_policies_proto_rawDescGZIP(), []int{0}
}

type BlanketRegularAdditionalInsured struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BlanketRegularAdditionalInsured) Reset() {
	*x = BlanketRegularAdditionalInsured{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_policies_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlanketRegularAdditionalInsured) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlanketRegularAdditionalInsured) ProtoMessage() {}

func (x *BlanketRegularAdditionalInsured) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_policies_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlanketRegularAdditionalInsured.ProtoReflect.Descriptor instead.
func (*BlanketRegularAdditionalInsured) Descriptor() ([]byte, []int) {
	return file_pricing_policies_proto_rawDescGZIP(), []int{0}
}

type BlanketPrimaryAndNonContributoryAdditionalInsured struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BlanketPrimaryAndNonContributoryAdditionalInsured) Reset() {
	*x = BlanketPrimaryAndNonContributoryAdditionalInsured{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_policies_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlanketPrimaryAndNonContributoryAdditionalInsured) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlanketPrimaryAndNonContributoryAdditionalInsured) ProtoMessage() {}

func (x *BlanketPrimaryAndNonContributoryAdditionalInsured) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_policies_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlanketPrimaryAndNonContributoryAdditionalInsured.ProtoReflect.Descriptor instead.
func (*BlanketPrimaryAndNonContributoryAdditionalInsured) Descriptor() ([]byte, []int) {
	return file_pricing_policies_proto_rawDescGZIP(), []int{1}
}

type BlanketThirdPartyWithWaiverOfSubrogation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BlanketThirdPartyWithWaiverOfSubrogation) Reset() {
	*x = BlanketThirdPartyWithWaiverOfSubrogation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_policies_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlanketThirdPartyWithWaiverOfSubrogation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlanketThirdPartyWithWaiverOfSubrogation) ProtoMessage() {}

func (x *BlanketThirdPartyWithWaiverOfSubrogation) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_policies_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlanketThirdPartyWithWaiverOfSubrogation.ProtoReflect.Descriptor instead.
func (*BlanketThirdPartyWithWaiverOfSubrogation) Descriptor() ([]byte, []int) {
	return file_pricing_policies_proto_rawDescGZIP(), []int{2}
}

type SpecifiedRegularAdditionalInsured struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SpecifiedRegularAdditionalInsured) Reset() {
	*x = SpecifiedRegularAdditionalInsured{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_policies_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpecifiedRegularAdditionalInsured) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecifiedRegularAdditionalInsured) ProtoMessage() {}

func (x *SpecifiedRegularAdditionalInsured) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_policies_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecifiedRegularAdditionalInsured.ProtoReflect.Descriptor instead.
func (*SpecifiedRegularAdditionalInsured) Descriptor() ([]byte, []int) {
	return file_pricing_policies_proto_rawDescGZIP(), []int{3}
}

func (x *SpecifiedRegularAdditionalInsured) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type SpecifiedPrimaryAndNonContributoryAdditionalInsured struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SpecifiedPrimaryAndNonContributoryAdditionalInsured) Reset() {
	*x = SpecifiedPrimaryAndNonContributoryAdditionalInsured{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_policies_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpecifiedPrimaryAndNonContributoryAdditionalInsured) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecifiedPrimaryAndNonContributoryAdditionalInsured) ProtoMessage() {}

func (x *SpecifiedPrimaryAndNonContributoryAdditionalInsured) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_policies_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecifiedPrimaryAndNonContributoryAdditionalInsured.ProtoReflect.Descriptor instead.
func (*SpecifiedPrimaryAndNonContributoryAdditionalInsured) Descriptor() ([]byte, []int) {
	return file_pricing_policies_proto_rawDescGZIP(), []int{4}
}

func (x *SpecifiedPrimaryAndNonContributoryAdditionalInsured) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type SpecifiedThirdPartyWithWaiverOfSubrogation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SpecifiedThirdPartyWithWaiverOfSubrogation) Reset() {
	*x = SpecifiedThirdPartyWithWaiverOfSubrogation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_policies_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpecifiedThirdPartyWithWaiverOfSubrogation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecifiedThirdPartyWithWaiverOfSubrogation) ProtoMessage() {}

func (x *SpecifiedThirdPartyWithWaiverOfSubrogation) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_policies_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecifiedThirdPartyWithWaiverOfSubrogation.ProtoReflect.Descriptor instead.
func (*SpecifiedThirdPartyWithWaiverOfSubrogation) Descriptor() ([]byte, []int) {
	return file_pricing_policies_proto_rawDescGZIP(), []int{5}
}

func (x *SpecifiedThirdPartyWithWaiverOfSubrogation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_pricing_policies_proto protoreflect.FileDescriptor

var file_pricing_policies_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x22, 0x21, 0x0a, 0x1f, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x65, 0x64, 0x22, 0x33, 0x0a, 0x31, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x22, 0x2a, 0x0a, 0x28, 0x42, 0x6c, 0x61,
	0x6e, 0x6b, 0x65, 0x74, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x57, 0x69,
	0x74, 0x68, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x33, 0x0a, 0x21, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x45, 0x0a, 0x33, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x6e,
	0x64, 0x4e, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x6f, 0x72, 0x79,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x3c, 0x0a, 0x2a, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x68,
	0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x57, 0x69, 0x74, 0x68, 0x57, 0x61, 0x69, 0x76,
	0x65, 0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x2a,
	0xa8, 0x01, 0x0a, 0x0a, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x16, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x5f, 0x55, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x5f, 0x4d, 0x4f, 0x54, 0x4f, 0x52, 0x5f, 0x43,
	0x41, 0x52, 0x52, 0x49, 0x45, 0x52, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x5f, 0x4d, 0x4f, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x55,
	0x43, 0x4b, 0x5f, 0x43, 0x41, 0x52, 0x47, 0x4f, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x4c,
	0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x10, 0x04, 0x42, 0x33, 0x5a, 0x31, 0x6e, 0x69,
	0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69,
	0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x74, 0x79, 0x70, 0x65, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pricing_policies_proto_rawDescOnce sync.Once
	file_pricing_policies_proto_rawDescData = file_pricing_policies_proto_rawDesc
)

func file_pricing_policies_proto_rawDescGZIP() []byte {
	file_pricing_policies_proto_rawDescOnce.Do(func() {
		file_pricing_policies_proto_rawDescData = protoimpl.X.CompressGZIP(file_pricing_policies_proto_rawDescData)
	})
	return file_pricing_policies_proto_rawDescData
}

var file_pricing_policies_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pricing_policies_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pricing_policies_proto_goTypes = []interface{}{
	(PolicyName)(0),                                             // 0: pricing.PolicyName
	(*BlanketRegularAdditionalInsured)(nil),                     // 1: pricing.BlanketRegularAdditionalInsured
	(*BlanketPrimaryAndNonContributoryAdditionalInsured)(nil),   // 2: pricing.BlanketPrimaryAndNonContributoryAdditionalInsured
	(*BlanketThirdPartyWithWaiverOfSubrogation)(nil),            // 3: pricing.BlanketThirdPartyWithWaiverOfSubrogation
	(*SpecifiedRegularAdditionalInsured)(nil),                   // 4: pricing.SpecifiedRegularAdditionalInsured
	(*SpecifiedPrimaryAndNonContributoryAdditionalInsured)(nil), // 5: pricing.SpecifiedPrimaryAndNonContributoryAdditionalInsured
	(*SpecifiedThirdPartyWithWaiverOfSubrogation)(nil),          // 6: pricing.SpecifiedThirdPartyWithWaiverOfSubrogation
}
var file_pricing_policies_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pricing_policies_proto_init() }
func file_pricing_policies_proto_init() {
	if File_pricing_policies_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pricing_policies_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlanketRegularAdditionalInsured); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_policies_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlanketPrimaryAndNonContributoryAdditionalInsured); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_policies_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlanketThirdPartyWithWaiverOfSubrogation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_policies_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpecifiedRegularAdditionalInsured); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_policies_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpecifiedPrimaryAndNonContributoryAdditionalInsured); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_policies_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpecifiedThirdPartyWithWaiverOfSubrogation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricing_policies_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pricing_policies_proto_goTypes,
		DependencyIndexes: file_pricing_policies_proto_depIdxs,
		EnumInfos:         file_pricing_policies_proto_enumTypes,
		MessageInfos:      file_pricing_policies_proto_msgTypes,
	}.Build()
	File_pricing_policies_proto = out.File
	file_pricing_policies_proto_rawDesc = nil
	file_pricing_policies_proto_goTypes = nil
	file_pricing_policies_proto_depIdxs = nil
}
