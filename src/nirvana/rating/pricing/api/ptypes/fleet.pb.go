// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pricing/fleet.proto

package ptypes

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	proto "nirvanatech.com/nirvana/common-go/proto"
	model "nirvanatech.com/nirvana/fleet/model"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Fleet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Fleet) Reset() {
	*x = Fleet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet) ProtoMessage() {}

func (x *Fleet) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet.ProtoReflect.Descriptor instead.
func (*Fleet) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0}
}

type Fleet_BundleChunkSpecData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Fleet_BundleChunkSpecData) Reset() {
	*x = Fleet_BundleChunkSpecData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_BundleChunkSpecData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_BundleChunkSpecData) ProtoMessage() {}

func (x *Fleet_BundleChunkSpecData) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_BundleChunkSpecData.ProtoReflect.Descriptor instead.
func (*Fleet_BundleChunkSpecData) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 0}
}

type Fleet_PolicyChunkSpecData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommoditiesInfo        *Fleet_CommoditiesInfo        `protobuf:"bytes,1,opt,name=commoditiesInfo,proto3" json:"commoditiesInfo,omitempty"`
	Company                *Fleet_Company                `protobuf:"bytes,2,opt,name=company,proto3" json:"company,omitempty"`
	UnderwriterInput       *Fleet_UnderwriterInput       `protobuf:"bytes,3,opt,name=underwriterInput,proto3" json:"underwriterInput,omitempty"`
	Drivers                []*Fleet_Driver               `protobuf:"bytes,5,rep,name=drivers,proto3" json:"drivers,omitempty"`
	Vehicles               []*Fleet_Vehicle              `protobuf:"bytes,6,rep,name=vehicles,proto3" json:"vehicles,omitempty"`
	LossSummaries          []*Fleet_LossSummary          `protobuf:"bytes,7,rep,name=lossSummaries,proto3" json:"lossSummaries,omitempty"`
	LargeLossesInfo        *Fleet_LargeLossesInfo        `protobuf:"bytes,8,opt,name=largeLossesInfo,proto3,oneof" json:"largeLossesInfo,omitempty"`
	NegotiatedPremiumsInfo *Fleet_NegotiatedPremiumsInfo `protobuf:"bytes,9,opt,name=negotiatedPremiumsInfo,proto3,oneof" json:"negotiatedPremiumsInfo,omitempty"`
	UnclassifiedData       *Fleet_UnclassifiedData       `protobuf:"bytes,10,opt,name=unclassifiedData,proto3" json:"unclassifiedData,omitempty"`
	ArtifactConfig         *ArtifactConfig               `protobuf:"bytes,11,opt,name=artifactConfig,proto3" json:"artifactConfig,omitempty"`
}

func (x *Fleet_PolicyChunkSpecData) Reset() {
	*x = Fleet_PolicyChunkSpecData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_PolicyChunkSpecData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_PolicyChunkSpecData) ProtoMessage() {}

func (x *Fleet_PolicyChunkSpecData) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_PolicyChunkSpecData.ProtoReflect.Descriptor instead.
func (*Fleet_PolicyChunkSpecData) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Fleet_PolicyChunkSpecData) GetCommoditiesInfo() *Fleet_CommoditiesInfo {
	if x != nil {
		return x.CommoditiesInfo
	}
	return nil
}

func (x *Fleet_PolicyChunkSpecData) GetCompany() *Fleet_Company {
	if x != nil {
		return x.Company
	}
	return nil
}

func (x *Fleet_PolicyChunkSpecData) GetUnderwriterInput() *Fleet_UnderwriterInput {
	if x != nil {
		return x.UnderwriterInput
	}
	return nil
}

func (x *Fleet_PolicyChunkSpecData) GetDrivers() []*Fleet_Driver {
	if x != nil {
		return x.Drivers
	}
	return nil
}

func (x *Fleet_PolicyChunkSpecData) GetVehicles() []*Fleet_Vehicle {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

func (x *Fleet_PolicyChunkSpecData) GetLossSummaries() []*Fleet_LossSummary {
	if x != nil {
		return x.LossSummaries
	}
	return nil
}

func (x *Fleet_PolicyChunkSpecData) GetLargeLossesInfo() *Fleet_LargeLossesInfo {
	if x != nil {
		return x.LargeLossesInfo
	}
	return nil
}

func (x *Fleet_PolicyChunkSpecData) GetNegotiatedPremiumsInfo() *Fleet_NegotiatedPremiumsInfo {
	if x != nil {
		return x.NegotiatedPremiumsInfo
	}
	return nil
}

func (x *Fleet_PolicyChunkSpecData) GetUnclassifiedData() *Fleet_UnclassifiedData {
	if x != nil {
		return x.UnclassifiedData
	}
	return nil
}

func (x *Fleet_PolicyChunkSpecData) GetArtifactConfig() *ArtifactConfig {
	if x != nil {
		return x.ArtifactConfig
	}
	return nil
}

type Fleet_CommoditiesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrimaryCommodityCategory   model.CommodityCategory  `protobuf:"varint,1,opt,name=primaryCommodityCategory,proto3,enum=fleet_model.CommodityCategory" json:"primaryCommodityCategory,omitempty"`
	Records                    []*Fleet_CommodityRecord `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
	OtherCommoditiesPercentage int64                    `protobuf:"varint,3,opt,name=otherCommoditiesPercentage,proto3" json:"otherCommoditiesPercentage,omitempty"`
}

func (x *Fleet_CommoditiesInfo) Reset() {
	*x = Fleet_CommoditiesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_CommoditiesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_CommoditiesInfo) ProtoMessage() {}

func (x *Fleet_CommoditiesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_CommoditiesInfo.ProtoReflect.Descriptor instead.
func (*Fleet_CommoditiesInfo) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 2}
}

func (x *Fleet_CommoditiesInfo) GetPrimaryCommodityCategory() model.CommodityCategory {
	if x != nil {
		return x.PrimaryCommodityCategory
	}
	return model.CommodityCategory(0)
}

func (x *Fleet_CommoditiesInfo) GetRecords() []*Fleet_CommodityRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *Fleet_CommoditiesInfo) GetOtherCommoditiesPercentage() int64 {
	if x != nil {
		return x.OtherCommoditiesPercentage
	}
	return 0
}

type Fleet_CommodityRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommodityCategory         model.CommodityCategory `protobuf:"varint,1,opt,name=commodityCategory,proto3,enum=fleet_model.CommodityCategory" json:"commodityCategory,omitempty"`
	PercentageOfHauls         int64                   `protobuf:"varint,2,opt,name=percentageOfHauls,proto3" json:"percentageOfHauls,omitempty"`
	AverageDollarValuePerHaul int64                   `protobuf:"varint,3,opt,name=averageDollarValuePerHaul,proto3" json:"averageDollarValuePerHaul,omitempty"`
	CommodityName             *model.CommodityName    `protobuf:"varint,4,opt,name=commodityName,proto3,enum=fleet_model.CommodityName,oneof" json:"commodityName,omitempty"`
}

func (x *Fleet_CommodityRecord) Reset() {
	*x = Fleet_CommodityRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_CommodityRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_CommodityRecord) ProtoMessage() {}

func (x *Fleet_CommodityRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_CommodityRecord.ProtoReflect.Descriptor instead.
func (*Fleet_CommodityRecord) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 3}
}

func (x *Fleet_CommodityRecord) GetCommodityCategory() model.CommodityCategory {
	if x != nil {
		return x.CommodityCategory
	}
	return model.CommodityCategory(0)
}

func (x *Fleet_CommodityRecord) GetPercentageOfHauls() int64 {
	if x != nil {
		return x.PercentageOfHauls
	}
	return 0
}

func (x *Fleet_CommodityRecord) GetAverageDollarValuePerHaul() int64 {
	if x != nil {
		return x.AverageDollarValuePerHaul
	}
	return 0
}

func (x *Fleet_CommodityRecord) GetCommodityName() model.CommodityName {
	if x != nil && x.CommodityName != nil {
		return *x.CommodityName
	}
	return model.CommodityName(0)
}

type Fleet_Company struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber                         string                           `protobuf:"bytes,1,opt,name=dotNumber,proto3" json:"dotNumber,omitempty"`
	AverageMiles                      float64                          `protobuf:"fixed64,2,opt,name=averageMiles,proto3" json:"averageMiles,omitempty"`
	AverageCombinedGrossVehicleWeight float64                          `protobuf:"fixed64,3,opt,name=averageCombinedGrossVehicleWeight,proto3" json:"averageCombinedGrossVehicleWeight,omitempty"`
	CrashFrequency                    float64                          `protobuf:"fixed64,4,opt,name=crashFrequency,proto3" json:"crashFrequency,omitempty"`
	VehicleInspectionRatio            float64                          `protobuf:"fixed64,5,opt,name=vehicleInspectionRatio,proto3" json:"vehicleInspectionRatio,omitempty"`
	MaintenanceViolationsRatio        float64                          `protobuf:"fixed64,6,opt,name=maintenanceViolationsRatio,proto3" json:"maintenanceViolationsRatio,omitempty"`
	UnsafeViolationRatio              float64                          `protobuf:"fixed64,7,opt,name=unsafeViolationRatio,proto3" json:"unsafeViolationRatio,omitempty"`
	YearsInBusiness                   int64                            `protobuf:"varint,8,opt,name=yearsInBusiness,proto3" json:"yearsInBusiness,omitempty"`
	InspectionIndicator               string                           `protobuf:"bytes,9,opt,name=inspectionIndicator,proto3" json:"inspectionIndicator,omitempty"`
	LargeMachineryIndicator           bool                             `protobuf:"varint,10,opt,name=largeMachineryIndicator,proto3" json:"largeMachineryIndicator,omitempty"`
	PowerUnitCount                    int64                            `protobuf:"varint,11,opt,name=powerUnitCount,proto3" json:"powerUnitCount,omitempty"`
	UsState                           string                           `protobuf:"bytes,12,opt,name=usState,proto3" json:"usState,omitempty"`
	TotalDriversLastYear              int64                            `protobuf:"varint,13,opt,name=totalDriversLastYear,proto3" json:"totalDriversLastYear,omitempty"`
	DriversHiredLastYear              int64                            `protobuf:"varint,14,opt,name=driversHiredLastYear,proto3" json:"driversHiredLastYear,omitempty"`
	RadiusOfOperationRecords          []*Fleet_RadiusOfOperationRecord `protobuf:"bytes,15,rep,name=radiusOfOperationRecords,proto3" json:"radiusOfOperationRecords,omitempty"`
	PrimaryOperationClass             model.OperationClass             `protobuf:"varint,16,opt,name=primaryOperationClass,proto3,enum=fleet_model.OperationClass" json:"primaryOperationClass,omitempty"`
	ObjectiveGrade                    *string                          `protobuf:"bytes,17,opt,name=objectiveGrade,proto3,oneof" json:"objectiveGrade,omitempty"`
	TaxRecords                        []*Fleet_TaxRecord               `protobuf:"bytes,18,rep,name=taxRecords,proto3" json:"taxRecords,omitempty"`
	NirvanaYearsRetained              *int64                           `protobuf:"varint,19,opt,name=nirvanaYearsRetained,proto3,oneof" json:"nirvanaYearsRetained,omitempty"`
	PriorCarrierYearsRetained         *int64                           `protobuf:"varint,20,opt,name=priorCarrierYearsRetained,proto3,oneof" json:"priorCarrierYearsRetained,omitempty"`
	Terminals                         []*Fleet_Terminal                `protobuf:"bytes,21,rep,name=terminals,proto3" json:"terminals,omitempty"`
	UnsafeDrivingScore                *float64                         `protobuf:"fixed64,22,opt,name=unsafeDrivingScore,proto3,oneof" json:"unsafeDrivingScore,omitempty"`
	ZipCode                           string                           `protobuf:"bytes,1000,opt,name=zipCode,proto3" json:"zipCode,omitempty"`
	ProjectedMiles                    int32                            `protobuf:"varint,1002,opt,name=projectedMiles,proto3" json:"projectedMiles,omitempty"`
	OperationClassRecords             []*Fleet_OperationClassRecord    `protobuf:"bytes,1004,rep,name=operationClassRecords,proto3" json:"operationClassRecords,omitempty"`
	VehicleZoneRecords                []*Fleet_VehicleZoneRecord       `protobuf:"bytes,1006,rep,name=vehicleZoneRecords,proto3" json:"vehicleZoneRecords,omitempty"`
	PercentageOfSubHaul               *float32                         `protobuf:"fixed32,1008,opt,name=percentageOfSubHaul,proto3,oneof" json:"percentageOfSubHaul,omitempty"`
}

func (x *Fleet_Company) Reset() {
	*x = Fleet_Company{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_Company) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_Company) ProtoMessage() {}

func (x *Fleet_Company) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_Company.ProtoReflect.Descriptor instead.
func (*Fleet_Company) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 4}
}

func (x *Fleet_Company) GetDotNumber() string {
	if x != nil {
		return x.DotNumber
	}
	return ""
}

func (x *Fleet_Company) GetAverageMiles() float64 {
	if x != nil {
		return x.AverageMiles
	}
	return 0
}

func (x *Fleet_Company) GetAverageCombinedGrossVehicleWeight() float64 {
	if x != nil {
		return x.AverageCombinedGrossVehicleWeight
	}
	return 0
}

func (x *Fleet_Company) GetCrashFrequency() float64 {
	if x != nil {
		return x.CrashFrequency
	}
	return 0
}

func (x *Fleet_Company) GetVehicleInspectionRatio() float64 {
	if x != nil {
		return x.VehicleInspectionRatio
	}
	return 0
}

func (x *Fleet_Company) GetMaintenanceViolationsRatio() float64 {
	if x != nil {
		return x.MaintenanceViolationsRatio
	}
	return 0
}

func (x *Fleet_Company) GetUnsafeViolationRatio() float64 {
	if x != nil {
		return x.UnsafeViolationRatio
	}
	return 0
}

func (x *Fleet_Company) GetYearsInBusiness() int64 {
	if x != nil {
		return x.YearsInBusiness
	}
	return 0
}

func (x *Fleet_Company) GetInspectionIndicator() string {
	if x != nil {
		return x.InspectionIndicator
	}
	return ""
}

func (x *Fleet_Company) GetLargeMachineryIndicator() bool {
	if x != nil {
		return x.LargeMachineryIndicator
	}
	return false
}

func (x *Fleet_Company) GetPowerUnitCount() int64 {
	if x != nil {
		return x.PowerUnitCount
	}
	return 0
}

func (x *Fleet_Company) GetUsState() string {
	if x != nil {
		return x.UsState
	}
	return ""
}

func (x *Fleet_Company) GetTotalDriversLastYear() int64 {
	if x != nil {
		return x.TotalDriversLastYear
	}
	return 0
}

func (x *Fleet_Company) GetDriversHiredLastYear() int64 {
	if x != nil {
		return x.DriversHiredLastYear
	}
	return 0
}

func (x *Fleet_Company) GetRadiusOfOperationRecords() []*Fleet_RadiusOfOperationRecord {
	if x != nil {
		return x.RadiusOfOperationRecords
	}
	return nil
}

func (x *Fleet_Company) GetPrimaryOperationClass() model.OperationClass {
	if x != nil {
		return x.PrimaryOperationClass
	}
	return model.OperationClass(0)
}

func (x *Fleet_Company) GetObjectiveGrade() string {
	if x != nil && x.ObjectiveGrade != nil {
		return *x.ObjectiveGrade
	}
	return ""
}

func (x *Fleet_Company) GetTaxRecords() []*Fleet_TaxRecord {
	if x != nil {
		return x.TaxRecords
	}
	return nil
}

func (x *Fleet_Company) GetNirvanaYearsRetained() int64 {
	if x != nil && x.NirvanaYearsRetained != nil {
		return *x.NirvanaYearsRetained
	}
	return 0
}

func (x *Fleet_Company) GetPriorCarrierYearsRetained() int64 {
	if x != nil && x.PriorCarrierYearsRetained != nil {
		return *x.PriorCarrierYearsRetained
	}
	return 0
}

func (x *Fleet_Company) GetTerminals() []*Fleet_Terminal {
	if x != nil {
		return x.Terminals
	}
	return nil
}

func (x *Fleet_Company) GetUnsafeDrivingScore() float64 {
	if x != nil && x.UnsafeDrivingScore != nil {
		return *x.UnsafeDrivingScore
	}
	return 0
}

func (x *Fleet_Company) GetZipCode() string {
	if x != nil {
		return x.ZipCode
	}
	return ""
}

func (x *Fleet_Company) GetProjectedMiles() int32 {
	if x != nil {
		return x.ProjectedMiles
	}
	return 0
}

func (x *Fleet_Company) GetOperationClassRecords() []*Fleet_OperationClassRecord {
	if x != nil {
		return x.OperationClassRecords
	}
	return nil
}

func (x *Fleet_Company) GetVehicleZoneRecords() []*Fleet_VehicleZoneRecord {
	if x != nil {
		return x.VehicleZoneRecords
	}
	return nil
}

func (x *Fleet_Company) GetPercentageOfSubHaul() float32 {
	if x != nil && x.PercentageOfSubHaul != nil {
		return *x.PercentageOfSubHaul
	}
	return 0
}

type Fleet_RadiusOfOperationRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RadiusOfOperationRange model.RadiusOfOperationRange `protobuf:"varint,1,opt,name=radiusOfOperationRange,proto3,enum=fleet_model.RadiusOfOperationRange" json:"radiusOfOperationRange,omitempty"`
	Percentage             int32                        `protobuf:"varint,2,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *Fleet_RadiusOfOperationRecord) Reset() {
	*x = Fleet_RadiusOfOperationRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_RadiusOfOperationRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_RadiusOfOperationRecord) ProtoMessage() {}

func (x *Fleet_RadiusOfOperationRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_RadiusOfOperationRecord.ProtoReflect.Descriptor instead.
func (*Fleet_RadiusOfOperationRecord) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 5}
}

func (x *Fleet_RadiusOfOperationRecord) GetRadiusOfOperationRange() model.RadiusOfOperationRange {
	if x != nil {
		return x.RadiusOfOperationRange
	}
	return model.RadiusOfOperationRange(0)
}

func (x *Fleet_RadiusOfOperationRecord) GetPercentage() int32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type Fleet_TaxRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JurisdictionType string     `protobuf:"bytes,1,opt,name=jurisdictionType,proto3" json:"jurisdictionType,omitempty"`
	JurisdictionName string     `protobuf:"bytes,2,opt,name=jurisdictionName,proto3" json:"jurisdictionName,omitempty"`
	TaxCode          string     `protobuf:"bytes,3,opt,name=taxCode,proto3" json:"taxCode,omitempty"`
	TaxValue         string     `protobuf:"bytes,4,opt,name=taxValue,proto3" json:"taxValue,omitempty"`
	PolicyName       PolicyName `protobuf:"varint,5,opt,name=policyName,proto3,enum=pricing.PolicyName" json:"policyName,omitempty"`
}

func (x *Fleet_TaxRecord) Reset() {
	*x = Fleet_TaxRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_TaxRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_TaxRecord) ProtoMessage() {}

func (x *Fleet_TaxRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_TaxRecord.ProtoReflect.Descriptor instead.
func (*Fleet_TaxRecord) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 6}
}

func (x *Fleet_TaxRecord) GetJurisdictionType() string {
	if x != nil {
		return x.JurisdictionType
	}
	return ""
}

func (x *Fleet_TaxRecord) GetJurisdictionName() string {
	if x != nil {
		return x.JurisdictionName
	}
	return ""
}

func (x *Fleet_TaxRecord) GetTaxCode() string {
	if x != nil {
		return x.TaxCode
	}
	return ""
}

func (x *Fleet_TaxRecord) GetTaxValue() string {
	if x != nil {
		return x.TaxValue
	}
	return ""
}

func (x *Fleet_TaxRecord) GetPolicyName() PolicyName {
	if x != nil {
		return x.PolicyName
	}
	return PolicyName_PolicyName_Unspecified
}

type Fleet_Terminal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                           string                                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	LimitSpecs                   []*LimitSpec                               `protobuf:"bytes,2,rep,name=limitSpecs,proto3" json:"limitSpecs,omitempty"`
	ConstructionClass            model.TerminalConstructionClass            `protobuf:"varint,3,opt,name=constructionClass,proto3,enum=fleet_model.TerminalConstructionClass" json:"constructionClass,omitempty"`
	PublicProtectionClass        model.TerminalPublicProtectionClass        `protobuf:"varint,4,opt,name=publicProtectionClass,proto3,enum=fleet_model.TerminalPublicProtectionClass" json:"publicProtectionClass,omitempty"`
	PrivateTheftProtectionSystem model.TerminalPrivateTheftProtectionSystem `protobuf:"varint,5,opt,name=privateTheftProtectionSystem,proto3,enum=fleet_model.TerminalPrivateTheftProtectionSystem" json:"privateTheftProtectionSystem,omitempty"`
	PrivateFireProtectionSystem  model.TerminalPrivateFireProtectionSystem  `protobuf:"varint,6,opt,name=privateFireProtectionSystem,proto3,enum=fleet_model.TerminalPrivateFireProtectionSystem" json:"privateFireProtectionSystem,omitempty"`
}

func (x *Fleet_Terminal) Reset() {
	*x = Fleet_Terminal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_Terminal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_Terminal) ProtoMessage() {}

func (x *Fleet_Terminal) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_Terminal.ProtoReflect.Descriptor instead.
func (*Fleet_Terminal) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 7}
}

func (x *Fleet_Terminal) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Fleet_Terminal) GetLimitSpecs() []*LimitSpec {
	if x != nil {
		return x.LimitSpecs
	}
	return nil
}

func (x *Fleet_Terminal) GetConstructionClass() model.TerminalConstructionClass {
	if x != nil {
		return x.ConstructionClass
	}
	return model.TerminalConstructionClass(0)
}

func (x *Fleet_Terminal) GetPublicProtectionClass() model.TerminalPublicProtectionClass {
	if x != nil {
		return x.PublicProtectionClass
	}
	return model.TerminalPublicProtectionClass(0)
}

func (x *Fleet_Terminal) GetPrivateTheftProtectionSystem() model.TerminalPrivateTheftProtectionSystem {
	if x != nil {
		return x.PrivateTheftProtectionSystem
	}
	return model.TerminalPrivateTheftProtectionSystem(0)
}

func (x *Fleet_Terminal) GetPrivateFireProtectionSystem() model.TerminalPrivateFireProtectionSystem {
	if x != nil {
		return x.PrivateFireProtectionSystem
	}
	return model.TerminalPrivateFireProtectionSystem(0)
}

type Fleet_OperationClassRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OperationClass model.OperationClass `protobuf:"varint,1,opt,name=operationClass,proto3,enum=fleet_model.OperationClass" json:"operationClass,omitempty"`
	Percentage     int32                `protobuf:"varint,2,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *Fleet_OperationClassRecord) Reset() {
	*x = Fleet_OperationClassRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_OperationClassRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_OperationClassRecord) ProtoMessage() {}

func (x *Fleet_OperationClassRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_OperationClassRecord.ProtoReflect.Descriptor instead.
func (*Fleet_OperationClassRecord) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 8}
}

func (x *Fleet_OperationClassRecord) GetOperationClass() model.OperationClass {
	if x != nil {
		return x.OperationClass
	}
	return model.OperationClass(0)
}

func (x *Fleet_OperationClassRecord) GetPercentage() int32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type Fleet_VehicleZoneRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartZone  int32 `protobuf:"varint,1,opt,name=startZone,proto3" json:"startZone,omitempty"`
	EndZone    int32 `protobuf:"varint,2,opt,name=endZone,proto3" json:"endZone,omitempty"`
	Percentage int32 `protobuf:"varint,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *Fleet_VehicleZoneRecord) Reset() {
	*x = Fleet_VehicleZoneRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_VehicleZoneRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_VehicleZoneRecord) ProtoMessage() {}

func (x *Fleet_VehicleZoneRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_VehicleZoneRecord.ProtoReflect.Descriptor instead.
func (*Fleet_VehicleZoneRecord) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 9}
}

func (x *Fleet_VehicleZoneRecord) GetStartZone() int32 {
	if x != nil {
		return x.StartZone
	}
	return 0
}

func (x *Fleet_VehicleZoneRecord) GetEndZone() int32 {
	if x != nil {
		return x.EndZone
	}
	return 0
}

func (x *Fleet_VehicleZoneRecord) GetPercentage() int32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type Fleet_UnderwriterInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScheduleMods []*ScheduleModification `protobuf:"bytes,2,rep,name=scheduleMods,proto3" json:"scheduleMods,omitempty"`
}

func (x *Fleet_UnderwriterInput) Reset() {
	*x = Fleet_UnderwriterInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_UnderwriterInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_UnderwriterInput) ProtoMessage() {}

func (x *Fleet_UnderwriterInput) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_UnderwriterInput.ProtoReflect.Descriptor instead.
func (*Fleet_UnderwriterInput) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 10}
}

func (x *Fleet_UnderwriterInput) GetScheduleMods() []*ScheduleModification {
	if x != nil {
		return x.ScheduleMods
	}
	return nil
}

type Fleet_Driver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	DateHired            *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=dateHired,proto3" json:"dateHired,omitempty"`
	MovingViolationCount int32                  `protobuf:"varint,3,opt,name=movingViolationCount,proto3" json:"movingViolationCount,omitempty"`
	AttractScore         int32                  `protobuf:"varint,4,opt,name=attractScore,proto3" json:"attractScore,omitempty"`
}

func (x *Fleet_Driver) Reset() {
	*x = Fleet_Driver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_Driver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_Driver) ProtoMessage() {}

func (x *Fleet_Driver) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_Driver.ProtoReflect.Descriptor instead.
func (*Fleet_Driver) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 11}
}

func (x *Fleet_Driver) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Fleet_Driver) GetDateHired() *timestamppb.Timestamp {
	if x != nil {
		return x.DateHired
	}
	return nil
}

func (x *Fleet_Driver) GetMovingViolationCount() int32 {
	if x != nil {
		return x.MovingViolationCount
	}
	return 0
}

func (x *Fleet_Driver) GetAttractScore() int32 {
	if x != nil {
		return x.AttractScore
	}
	return 0
}

type Fleet_Vehicle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin         string                   `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	StatedValue int32                    `protobuf:"varint,2,opt,name=statedValue,proto3" json:"statedValue,omitempty"`
	ModelYear   int32                    `protobuf:"varint,3,opt,name=modelYear,proto3" json:"modelYear,omitempty"`
	Type        model.VehicleType        `protobuf:"varint,4,opt,name=type,proto3,enum=fleet_model.VehicleType" json:"type,omitempty"`
	WeightClass model.VehicleWeightClass `protobuf:"varint,5,opt,name=weightClass,proto3,enum=fleet_model.VehicleWeightClass" json:"weightClass,omitempty"`
}

func (x *Fleet_Vehicle) Reset() {
	*x = Fleet_Vehicle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_Vehicle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_Vehicle) ProtoMessage() {}

func (x *Fleet_Vehicle) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_Vehicle.ProtoReflect.Descriptor instead.
func (*Fleet_Vehicle) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 12}
}

func (x *Fleet_Vehicle) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *Fleet_Vehicle) GetStatedValue() int32 {
	if x != nil {
		return x.StatedValue
	}
	return 0
}

func (x *Fleet_Vehicle) GetModelYear() int32 {
	if x != nil {
		return x.ModelYear
	}
	return 0
}

func (x *Fleet_Vehicle) GetType() model.VehicleType {
	if x != nil {
		return x.Type
	}
	return model.VehicleType(0)
}

func (x *Fleet_Vehicle) GetWeightClass() model.VehicleWeightClass {
	if x != nil {
		return x.WeightClass
	}
	return model.VehicleWeightClass(0)
}

type Fleet_LossSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dates        *proto.Interval   `protobuf:"bytes,1,opt,name=dates,proto3" json:"dates,omitempty"`
	SubCoverages []SubCoverageType `protobuf:"varint,2,rep,packed,name=subCoverages,proto3,enum=pricing.SubCoverageType" json:"subCoverages,omitempty"`
	PuCount      int32             `protobuf:"varint,3,opt,name=puCount,proto3" json:"puCount,omitempty"`
	ClaimsCount  int32             `protobuf:"varint,4,opt,name=claimsCount,proto3" json:"claimsCount,omitempty"`
	LossIncurred int32             `protobuf:"varint,5,opt,name=lossIncurred,proto3" json:"lossIncurred,omitempty"`
}

func (x *Fleet_LossSummary) Reset() {
	*x = Fleet_LossSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_LossSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_LossSummary) ProtoMessage() {}

func (x *Fleet_LossSummary) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_LossSummary.ProtoReflect.Descriptor instead.
func (*Fleet_LossSummary) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 13}
}

func (x *Fleet_LossSummary) GetDates() *proto.Interval {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *Fleet_LossSummary) GetSubCoverages() []SubCoverageType {
	if x != nil {
		return x.SubCoverages
	}
	return nil
}

func (x *Fleet_LossSummary) GetPuCount() int32 {
	if x != nil {
		return x.PuCount
	}
	return 0
}

func (x *Fleet_LossSummary) GetClaimsCount() int32 {
	if x != nil {
		return x.ClaimsCount
	}
	return 0
}

func (x *Fleet_LossSummary) GetLossIncurred() int32 {
	if x != nil {
		return x.LossIncurred
	}
	return 0
}

type Fleet_LargeLossesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AreLossesProxied bool               `protobuf:"varint,1,opt,name=areLossesProxied,proto3" json:"areLossesProxied,omitempty"`
	LargeLosses      []*Fleet_LargeLoss `protobuf:"bytes,2,rep,name=largeLosses,proto3" json:"largeLosses,omitempty"`
}

func (x *Fleet_LargeLossesInfo) Reset() {
	*x = Fleet_LargeLossesInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_LargeLossesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_LargeLossesInfo) ProtoMessage() {}

func (x *Fleet_LargeLossesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_LargeLossesInfo.ProtoReflect.Descriptor instead.
func (*Fleet_LargeLossesInfo) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 14}
}

func (x *Fleet_LargeLossesInfo) GetAreLossesProxied() bool {
	if x != nil {
		return x.AreLossesProxied
	}
	return false
}

func (x *Fleet_LargeLossesInfo) GetLargeLosses() []*Fleet_LargeLoss {
	if x != nil {
		return x.LargeLosses
	}
	return nil
}

type Fleet_LargeLoss struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date         *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	SubCoverages []SubCoverageType      `protobuf:"varint,2,rep,packed,name=subCoverages,proto3,enum=pricing.SubCoverageType" json:"subCoverages,omitempty"`
	LossIncurred int32                  `protobuf:"varint,3,opt,name=lossIncurred,proto3" json:"lossIncurred,omitempty"`
}

func (x *Fleet_LargeLoss) Reset() {
	*x = Fleet_LargeLoss{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_LargeLoss) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_LargeLoss) ProtoMessage() {}

func (x *Fleet_LargeLoss) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_LargeLoss.ProtoReflect.Descriptor instead.
func (*Fleet_LargeLoss) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 15}
}

func (x *Fleet_LargeLoss) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *Fleet_LargeLoss) GetSubCoverages() []SubCoverageType {
	if x != nil {
		return x.SubCoverages
	}
	return nil
}

func (x *Fleet_LargeLoss) GetLossIncurred() int32 {
	if x != nil {
		return x.LossIncurred
	}
	return 0
}

type Fleet_NegotiatedPremiumsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShouldUseNegotiatedRates bool                       `protobuf:"varint,1,opt,name=shouldUseNegotiatedRates,proto3" json:"shouldUseNegotiatedRates,omitempty"`
	NegotiatedPremiums       []*Fleet_NegotiatedPremium `protobuf:"bytes,2,rep,name=negotiatedPremiums,proto3" json:"negotiatedPremiums,omitempty"`
}

func (x *Fleet_NegotiatedPremiumsInfo) Reset() {
	*x = Fleet_NegotiatedPremiumsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_NegotiatedPremiumsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_NegotiatedPremiumsInfo) ProtoMessage() {}

func (x *Fleet_NegotiatedPremiumsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_NegotiatedPremiumsInfo.ProtoReflect.Descriptor instead.
func (*Fleet_NegotiatedPremiumsInfo) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 16}
}

func (x *Fleet_NegotiatedPremiumsInfo) GetShouldUseNegotiatedRates() bool {
	if x != nil {
		return x.ShouldUseNegotiatedRates
	}
	return false
}

func (x *Fleet_NegotiatedPremiumsInfo) GetNegotiatedPremiums() []*Fleet_NegotiatedPremium {
	if x != nil {
		return x.NegotiatedPremiums
	}
	return nil
}

type Fleet_NegotiatedPremium struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubCoverages []SubCoverageType `protobuf:"varint,1,rep,packed,name=subCoverages,proto3,enum=pricing.SubCoverageType" json:"subCoverages,omitempty"`
	Amount       int64             `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *Fleet_NegotiatedPremium) Reset() {
	*x = Fleet_NegotiatedPremium{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_NegotiatedPremium) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_NegotiatedPremium) ProtoMessage() {}

func (x *Fleet_NegotiatedPremium) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_NegotiatedPremium.ProtoReflect.Descriptor instead.
func (*Fleet_NegotiatedPremium) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 17}
}

func (x *Fleet_NegotiatedPremium) GetSubCoverages() []SubCoverageType {
	if x != nil {
		return x.SubCoverages
	}
	return nil
}

func (x *Fleet_NegotiatedPremium) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type Fleet_UnclassifiedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RatingTierDates                  *Fleet_RatingTierDates `protobuf:"bytes,1,opt,name=ratingTierDates,proto3" json:"ratingTierDates,omitempty"`
	PackageType                      *string                `protobuf:"bytes,2,opt,name=packageType,proto3,oneof" json:"packageType,omitempty"`
	AllSubCoveragesSafetyScheduleMod float64                `protobuf:"fixed64,3,opt,name=allSubCoveragesSafetyScheduleMod,proto3" json:"allSubCoveragesSafetyScheduleMod,omitempty"`
}

func (x *Fleet_UnclassifiedData) Reset() {
	*x = Fleet_UnclassifiedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_UnclassifiedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_UnclassifiedData) ProtoMessage() {}

func (x *Fleet_UnclassifiedData) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_UnclassifiedData.ProtoReflect.Descriptor instead.
func (*Fleet_UnclassifiedData) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 18}
}

func (x *Fleet_UnclassifiedData) GetRatingTierDates() *Fleet_RatingTierDates {
	if x != nil {
		return x.RatingTierDates
	}
	return nil
}

func (x *Fleet_UnclassifiedData) GetPackageType() string {
	if x != nil && x.PackageType != nil {
		return *x.PackageType
	}
	return ""
}

func (x *Fleet_UnclassifiedData) GetAllSubCoveragesSafetyScheduleMod() float64 {
	if x != nil {
		return x.AllSubCoveragesSafetyScheduleMod
	}
	return 0
}

type Fleet_RatingTierDates struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DumpDate   *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=dumpDate,proto3" json:"dumpDate,omitempty"`
	RecordDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=recordDate,proto3" json:"recordDate,omitempty"`
}

func (x *Fleet_RatingTierDates) Reset() {
	*x = Fleet_RatingTierDates{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_RatingTierDates) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_RatingTierDates) ProtoMessage() {}

func (x *Fleet_RatingTierDates) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_RatingTierDates.ProtoReflect.Descriptor instead.
func (*Fleet_RatingTierDates) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 19}
}

func (x *Fleet_RatingTierDates) GetDumpDate() *timestamppb.Timestamp {
	if x != nil {
		return x.DumpDate
	}
	return nil
}

func (x *Fleet_RatingTierDates) GetRecordDate() *timestamppb.Timestamp {
	if x != nil {
		return x.RecordDate
	}
	return nil
}

type Fleet_ChunkOutputMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Fleet_ChunkOutputMetadata) Reset() {
	*x = Fleet_ChunkOutputMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_fleet_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fleet_ChunkOutputMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fleet_ChunkOutputMetadata) ProtoMessage() {}

func (x *Fleet_ChunkOutputMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_fleet_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fleet_ChunkOutputMetadata.ProtoReflect.Descriptor instead.
func (*Fleet_ChunkOutputMetadata) Descriptor() ([]byte, []int) {
	return file_pricing_fleet_proto_rawDescGZIP(), []int{0, 20}
}

var File_pricing_fleet_proto protoreflect.FileDescriptor

var file_pricing_fleet_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x14, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa2, 0x2c, 0x0a, 0x05, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x1a, 0x15,
	0x0a, 0x13, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65,
	0x63, 0x44, 0x61, 0x74, 0x61, 0x1a, 0xf5, 0x05, 0x0a, 0x13, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x12, 0x48, 0x0a,
	0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69,
	0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x4b, 0x0a, 0x10, 0x75, 0x6e, 0x64,
	0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c,
	0x65, 0x65, 0x74, 0x2e, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x10, 0x75, 0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x2f, 0x0a, 0x07, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x07,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x12, 0x32, 0x0a, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x52, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x6c,
	0x6f, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65,
	0x65, 0x74, 0x2e, 0x4c, 0x6f, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x0d,
	0x6c, 0x6f, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x12, 0x4d, 0x0a,
	0x0f, 0x6c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x4c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f, 0x73, 0x73,
	0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0f, 0x6c, 0x61, 0x72, 0x67, 0x65, 0x4c,
	0x6f, 0x73, 0x73, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x62, 0x0a, 0x16,
	0x6e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75,
	0x6d, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x4e, 0x65, 0x67,
	0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x48, 0x01, 0x52, 0x16, 0x6e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x64, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01,
	0x12, 0x4b, 0x0a, 0x10, 0x75, 0x6e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x55, 0x6e, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x69, 0x66, 0x69, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x10, 0x75, 0x6e, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f, 0x0a,
	0x0e, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e,
	0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e,
	0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x12,
	0x0a, 0x10, 0x5f, 0x6c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x6e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x64, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0xe7, 0x01,
	0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x5a, 0x0a, 0x18, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x18, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x38, 0x0a,
	0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x3e, 0x0a, 0x1a, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x50, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1a, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x50, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x1a, 0xa4, 0x02, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x64, 0x69, 0x74, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x4c, 0x0a, 0x11, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74,
	0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x2c, 0x0a, 0x11, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x4f, 0x66, 0x48, 0x61, 0x75, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x4f, 0x66, 0x48, 0x61, 0x75, 0x6c, 0x73, 0x12, 0x3c, 0x0a, 0x19, 0x61, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x44, 0x6f, 0x6c, 0x6c, 0x61, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x65, 0x72,
	0x48, 0x61, 0x75, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x19, 0x61, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x44, 0x6f, 0x6c, 0x6c, 0x61, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x65,
	0x72, 0x48, 0x61, 0x75, 0x6c, 0x12, 0x45, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69,
	0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x64, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x64, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xd7,
	0x0c, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x6f,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64,
	0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c,
	0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x21,
	0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x47,
	0x72, 0x6f, 0x73, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x21, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x73, 0x73, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x72,
	0x61, 0x73, 0x68, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0e, 0x63, 0x72, 0x61, 0x73, 0x68, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x36, 0x0a, 0x16, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x16, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x3e, 0x0a, 0x1a, 0x6d, 0x61,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1a,
	0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x32, 0x0a, 0x14, 0x75, 0x6e,
	0x73, 0x61, 0x66, 0x65, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74,
	0x69, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x75, 0x6e, 0x73, 0x61, 0x66, 0x65,
	0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x28,
	0x0a, 0x0f, 0x79, 0x65, 0x61, 0x72, 0x73, 0x49, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x79, 0x65, 0x61, 0x72, 0x73, 0x49, 0x6e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x30, 0x0a, 0x13, 0x69, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x38, 0x0a, 0x17, 0x6c, 0x61,
	0x72, 0x67, 0x65, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x6c, 0x61, 0x72,
	0x67, 0x65, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69,
	0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x4c, 0x61, 0x73, 0x74, 0x59, 0x65, 0x61, 0x72, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x73, 0x4c, 0x61, 0x73, 0x74, 0x59, 0x65, 0x61, 0x72, 0x12, 0x32, 0x0a, 0x14, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x73, 0x48, 0x69, 0x72, 0x65, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x59, 0x65,
	0x61, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x73, 0x48, 0x69, 0x72, 0x65, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x59, 0x65, 0x61, 0x72, 0x12, 0x62,
	0x0a, 0x18, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74,
	0x2e, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x18, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73,
	0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x12, 0x51, 0x0a, 0x15, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1b, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x15,
	0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x2b, 0x0a, 0x0e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x0e, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x61, 0x64, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x38, 0x0a, 0x0a, 0x74, 0x61, 0x78, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x54, 0x61, 0x78, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x0a, 0x74, 0x61, 0x78, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x37, 0x0a, 0x14,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52, 0x65, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x14, 0x6e, 0x69,
	0x72, 0x76, 0x61, 0x6e, 0x61, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x19, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x19, 0x70, 0x72, 0x69, 0x6f,
	0x72, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x59, 0x65, 0x61, 0x72, 0x73, 0x52, 0x65, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x09, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x52, 0x09, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x12,
	0x33, 0x0a, 0x12, 0x75, 0x6e, 0x73, 0x61, 0x66, 0x65, 0x44, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x01, 0x48, 0x03, 0x52, 0x12, 0x75,
	0x6e, 0x73, 0x61, 0x66, 0x65, 0x44, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0xe8, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x27, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4d, 0x69, 0x6c, 0x65,
	0x73, 0x18, 0xea, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x5a, 0x0a, 0x15, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0xec, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x15, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x51, 0x0a, 0x12, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5a,
	0x6f, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0xee, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65,
	0x74, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x12, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5a, 0x6f, 0x6e, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x36, 0x0a, 0x13, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x48, 0x61, 0x75, 0x6c, 0x18, 0xf0,
	0x07, 0x20, 0x01, 0x28, 0x02, 0x48, 0x04, 0x52, 0x13, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x48, 0x61, 0x75, 0x6c, 0x88, 0x01, 0x01, 0x42,
	0x11, 0x0a, 0x0f, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x61,
	0x64, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x59, 0x65,
	0x61, 0x72, 0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x1c, 0x0a, 0x1a, 0x5f,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x43, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x59, 0x65, 0x61, 0x72,
	0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x75, 0x6e,
	0x73, 0x61, 0x66, 0x65, 0x44, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x42, 0x16, 0x0a, 0x14, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x4f,
	0x66, 0x53, 0x75, 0x62, 0x48, 0x61, 0x75, 0x6c, 0x1a, 0x96, 0x01, 0x0a, 0x17, 0x52, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x12, 0x5b, 0x0a, 0x16, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x16, 0x72, 0x61, 0x64, 0x69, 0x75,
	0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x1a, 0xce, 0x01, 0x0a, 0x09, 0x54, 0x61, 0x78, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x2a, 0x0a, 0x10, 0x6a, 0x75, 0x72, 0x69, 0x73, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6a, 0x75, 0x72, 0x69, 0x73,
	0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x6a,
	0x75, 0x72, 0x69, 0x73, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6a, 0x75, 0x72, 0x69, 0x73, 0x64, 0x69, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x78, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x78, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x78, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x33, 0x0a,
	0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x1a, 0xf1, 0x03, 0x0a, 0x08, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x32, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x70,
	0x65, 0x63, 0x73, 0x12, 0x54, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x60, 0x0a, 0x15, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x52, 0x15, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x75, 0x0a, 0x1c, 0x70,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x54, 0x68, 0x65, 0x66, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x31, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x54,
	0x68, 0x65, 0x66, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x52, 0x1c, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x54, 0x68, 0x65,
	0x66, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x12, 0x72, 0x0a, 0x1b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x1b, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x46, 0x69, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x1a, 0x7b, 0x0a, 0x14, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x43,
	0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x1a, 0x6b, 0x0a, 0x11, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5a, 0x6f,
	0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x5a, 0x6f, 0x6e,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x5a, 0x6f, 0x6e, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x1a, 0x55, 0x0a, 0x10, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x12, 0x41, 0x0a, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4d, 0x6f, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x73, 0x1a, 0xaa, 0x01, 0x0a, 0x06, 0x44, 0x72, 0x69, 0x76,
	0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x38, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x48, 0x69, 0x72, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x48, 0x69, 0x72, 0x65, 0x64, 0x12, 0x32, 0x0a, 0x14,
	0x6d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x6d, 0x6f, 0x76, 0x69,
	0x6e, 0x67, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x22, 0x0a, 0x0c, 0x61, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x1a, 0xcc, 0x01, 0x0a, 0x07, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76,
	0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x64, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x59, 0x65, 0x61,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x59, 0x65,
	0x61, 0x72, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x41, 0x0a, 0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x1a, 0xd3, 0x01, 0x0a, 0x0b, 0x4c, 0x6f, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x12, 0x26, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x52, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0c, 0x73,
	0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x75, 0x62,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x75, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x75, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x73, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x6f, 0x73, 0x73, 0x49, 0x6e, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6c, 0x6f, 0x73,
	0x73, 0x49, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x64, 0x1a, 0x79, 0x0a, 0x0f, 0x4c, 0x61, 0x72,
	0x67, 0x65, 0x4c, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x10,
	0x61, 0x72, 0x65, 0x4c, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x78, 0x69, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x61, 0x72, 0x65, 0x4c, 0x6f, 0x73, 0x73, 0x65,
	0x73, 0x50, 0x72, 0x6f, 0x78, 0x69, 0x65, 0x64, 0x12, 0x3a, 0x0a, 0x0b, 0x6c, 0x61, 0x72, 0x67,
	0x65, 0x4c, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x4c, 0x61,
	0x72, 0x67, 0x65, 0x4c, 0x6f, 0x73, 0x73, 0x52, 0x0b, 0x6c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f,
	0x73, 0x73, 0x65, 0x73, 0x1a, 0x9d, 0x01, 0x0a, 0x09, 0x4c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f,
	0x73, 0x73, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73,
	0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x6f, 0x73, 0x73, 0x49, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6c, 0x6f, 0x73, 0x73, 0x49, 0x6e, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x64, 0x1a, 0xa6, 0x01, 0x0a, 0x16, 0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x3a, 0x0a, 0x18, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x55, 0x73, 0x65, 0x4e, 0x65, 0x67, 0x6f,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x18, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x55, 0x73, 0x65, 0x4e, 0x65, 0x67, 0x6f,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x12, 0x6e,
	0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74,
	0x65, 0x64, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x52, 0x12, 0x6e, 0x65, 0x67, 0x6f, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x73, 0x1a, 0x69, 0x0a,
	0x11, 0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x50, 0x72, 0x65, 0x6d, 0x69,
	0x75, 0x6d, 0x12, 0x3c, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xdf, 0x01, 0x0a, 0x10, 0x55, 0x6e, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x48, 0x0a,
	0x0f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x2e, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x65, 0x73, 0x52, 0x0f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4a,
	0x0a, 0x20, 0x61, 0x6c, 0x6c, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x73, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d,
	0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x20, 0x61, 0x6c, 0x6c, 0x53, 0x75, 0x62,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x85, 0x01, 0x0a, 0x0f, 0x52,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x36,
	0x0a, 0x08, 0x64, 0x75, 0x6d, 0x70, 0x44, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x64, 0x75,
	0x6d, 0x70, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x1a, 0x15, 0x0a, 0x13, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x33, 0x5a, 0x31, 0x6e, 0x69, 0x72,
	0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72,
	0x76, 0x61, 0x6e, 0x61, 0x2f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pricing_fleet_proto_rawDescOnce sync.Once
	file_pricing_fleet_proto_rawDescData = file_pricing_fleet_proto_rawDesc
)

func file_pricing_fleet_proto_rawDescGZIP() []byte {
	file_pricing_fleet_proto_rawDescOnce.Do(func() {
		file_pricing_fleet_proto_rawDescData = protoimpl.X.CompressGZIP(file_pricing_fleet_proto_rawDescData)
	})
	return file_pricing_fleet_proto_rawDescData
}

var file_pricing_fleet_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_pricing_fleet_proto_goTypes = []interface{}{
	(*Fleet)(nil),                                   // 0: pricing.Fleet
	(*Fleet_BundleChunkSpecData)(nil),               // 1: pricing.Fleet.BundleChunkSpecData
	(*Fleet_PolicyChunkSpecData)(nil),               // 2: pricing.Fleet.PolicyChunkSpecData
	(*Fleet_CommoditiesInfo)(nil),                   // 3: pricing.Fleet.CommoditiesInfo
	(*Fleet_CommodityRecord)(nil),                   // 4: pricing.Fleet.CommodityRecord
	(*Fleet_Company)(nil),                           // 5: pricing.Fleet.Company
	(*Fleet_RadiusOfOperationRecord)(nil),           // 6: pricing.Fleet.RadiusOfOperationRecord
	(*Fleet_TaxRecord)(nil),                         // 7: pricing.Fleet.TaxRecord
	(*Fleet_Terminal)(nil),                          // 8: pricing.Fleet.Terminal
	(*Fleet_OperationClassRecord)(nil),              // 9: pricing.Fleet.OperationClassRecord
	(*Fleet_VehicleZoneRecord)(nil),                 // 10: pricing.Fleet.VehicleZoneRecord
	(*Fleet_UnderwriterInput)(nil),                  // 11: pricing.Fleet.UnderwriterInput
	(*Fleet_Driver)(nil),                            // 12: pricing.Fleet.Driver
	(*Fleet_Vehicle)(nil),                           // 13: pricing.Fleet.Vehicle
	(*Fleet_LossSummary)(nil),                       // 14: pricing.Fleet.LossSummary
	(*Fleet_LargeLossesInfo)(nil),                   // 15: pricing.Fleet.LargeLossesInfo
	(*Fleet_LargeLoss)(nil),                         // 16: pricing.Fleet.LargeLoss
	(*Fleet_NegotiatedPremiumsInfo)(nil),            // 17: pricing.Fleet.NegotiatedPremiumsInfo
	(*Fleet_NegotiatedPremium)(nil),                 // 18: pricing.Fleet.NegotiatedPremium
	(*Fleet_UnclassifiedData)(nil),                  // 19: pricing.Fleet.UnclassifiedData
	(*Fleet_RatingTierDates)(nil),                   // 20: pricing.Fleet.RatingTierDates
	(*Fleet_ChunkOutputMetadata)(nil),               // 21: pricing.Fleet.ChunkOutputMetadata
	(*ArtifactConfig)(nil),                          // 22: pricing.ArtifactConfig
	(model.CommodityCategory)(0),                    // 23: fleet_model.CommodityCategory
	(model.CommodityName)(0),                        // 24: fleet_model.CommodityName
	(model.OperationClass)(0),                       // 25: fleet_model.OperationClass
	(model.RadiusOfOperationRange)(0),               // 26: fleet_model.RadiusOfOperationRange
	(PolicyName)(0),                                 // 27: pricing.PolicyName
	(*LimitSpec)(nil),                               // 28: pricing.LimitSpec
	(model.TerminalConstructionClass)(0),            // 29: fleet_model.TerminalConstructionClass
	(model.TerminalPublicProtectionClass)(0),        // 30: fleet_model.TerminalPublicProtectionClass
	(model.TerminalPrivateTheftProtectionSystem)(0), // 31: fleet_model.TerminalPrivateTheftProtectionSystem
	(model.TerminalPrivateFireProtectionSystem)(0),  // 32: fleet_model.TerminalPrivateFireProtectionSystem
	(*ScheduleModification)(nil),                    // 33: pricing.ScheduleModification
	(*timestamppb.Timestamp)(nil),                   // 34: google.protobuf.Timestamp
	(model.VehicleType)(0),                          // 35: fleet_model.VehicleType
	(model.VehicleWeightClass)(0),                   // 36: fleet_model.VehicleWeightClass
	(*proto.Interval)(nil),                          // 37: common.Interval
	(SubCoverageType)(0),                            // 38: pricing.SubCoverageType
}
var file_pricing_fleet_proto_depIdxs = []int32{
	3,  // 0: pricing.Fleet.PolicyChunkSpecData.commoditiesInfo:type_name -> pricing.Fleet.CommoditiesInfo
	5,  // 1: pricing.Fleet.PolicyChunkSpecData.company:type_name -> pricing.Fleet.Company
	11, // 2: pricing.Fleet.PolicyChunkSpecData.underwriterInput:type_name -> pricing.Fleet.UnderwriterInput
	12, // 3: pricing.Fleet.PolicyChunkSpecData.drivers:type_name -> pricing.Fleet.Driver
	13, // 4: pricing.Fleet.PolicyChunkSpecData.vehicles:type_name -> pricing.Fleet.Vehicle
	14, // 5: pricing.Fleet.PolicyChunkSpecData.lossSummaries:type_name -> pricing.Fleet.LossSummary
	15, // 6: pricing.Fleet.PolicyChunkSpecData.largeLossesInfo:type_name -> pricing.Fleet.LargeLossesInfo
	17, // 7: pricing.Fleet.PolicyChunkSpecData.negotiatedPremiumsInfo:type_name -> pricing.Fleet.NegotiatedPremiumsInfo
	19, // 8: pricing.Fleet.PolicyChunkSpecData.unclassifiedData:type_name -> pricing.Fleet.UnclassifiedData
	22, // 9: pricing.Fleet.PolicyChunkSpecData.artifactConfig:type_name -> pricing.ArtifactConfig
	23, // 10: pricing.Fleet.CommoditiesInfo.primaryCommodityCategory:type_name -> fleet_model.CommodityCategory
	4,  // 11: pricing.Fleet.CommoditiesInfo.records:type_name -> pricing.Fleet.CommodityRecord
	23, // 12: pricing.Fleet.CommodityRecord.commodityCategory:type_name -> fleet_model.CommodityCategory
	24, // 13: pricing.Fleet.CommodityRecord.commodityName:type_name -> fleet_model.CommodityName
	6,  // 14: pricing.Fleet.Company.radiusOfOperationRecords:type_name -> pricing.Fleet.RadiusOfOperationRecord
	25, // 15: pricing.Fleet.Company.primaryOperationClass:type_name -> fleet_model.OperationClass
	7,  // 16: pricing.Fleet.Company.taxRecords:type_name -> pricing.Fleet.TaxRecord
	8,  // 17: pricing.Fleet.Company.terminals:type_name -> pricing.Fleet.Terminal
	9,  // 18: pricing.Fleet.Company.operationClassRecords:type_name -> pricing.Fleet.OperationClassRecord
	10, // 19: pricing.Fleet.Company.vehicleZoneRecords:type_name -> pricing.Fleet.VehicleZoneRecord
	26, // 20: pricing.Fleet.RadiusOfOperationRecord.radiusOfOperationRange:type_name -> fleet_model.RadiusOfOperationRange
	27, // 21: pricing.Fleet.TaxRecord.policyName:type_name -> pricing.PolicyName
	28, // 22: pricing.Fleet.Terminal.limitSpecs:type_name -> pricing.LimitSpec
	29, // 23: pricing.Fleet.Terminal.constructionClass:type_name -> fleet_model.TerminalConstructionClass
	30, // 24: pricing.Fleet.Terminal.publicProtectionClass:type_name -> fleet_model.TerminalPublicProtectionClass
	31, // 25: pricing.Fleet.Terminal.privateTheftProtectionSystem:type_name -> fleet_model.TerminalPrivateTheftProtectionSystem
	32, // 26: pricing.Fleet.Terminal.privateFireProtectionSystem:type_name -> fleet_model.TerminalPrivateFireProtectionSystem
	25, // 27: pricing.Fleet.OperationClassRecord.operationClass:type_name -> fleet_model.OperationClass
	33, // 28: pricing.Fleet.UnderwriterInput.scheduleMods:type_name -> pricing.ScheduleModification
	34, // 29: pricing.Fleet.Driver.dateHired:type_name -> google.protobuf.Timestamp
	35, // 30: pricing.Fleet.Vehicle.type:type_name -> fleet_model.VehicleType
	36, // 31: pricing.Fleet.Vehicle.weightClass:type_name -> fleet_model.VehicleWeightClass
	37, // 32: pricing.Fleet.LossSummary.dates:type_name -> common.Interval
	38, // 33: pricing.Fleet.LossSummary.subCoverages:type_name -> pricing.SubCoverageType
	16, // 34: pricing.Fleet.LargeLossesInfo.largeLosses:type_name -> pricing.Fleet.LargeLoss
	34, // 35: pricing.Fleet.LargeLoss.date:type_name -> google.protobuf.Timestamp
	38, // 36: pricing.Fleet.LargeLoss.subCoverages:type_name -> pricing.SubCoverageType
	18, // 37: pricing.Fleet.NegotiatedPremiumsInfo.negotiatedPremiums:type_name -> pricing.Fleet.NegotiatedPremium
	38, // 38: pricing.Fleet.NegotiatedPremium.subCoverages:type_name -> pricing.SubCoverageType
	20, // 39: pricing.Fleet.UnclassifiedData.ratingTierDates:type_name -> pricing.Fleet.RatingTierDates
	34, // 40: pricing.Fleet.RatingTierDates.dumpDate:type_name -> google.protobuf.Timestamp
	34, // 41: pricing.Fleet.RatingTierDates.recordDate:type_name -> google.protobuf.Timestamp
	42, // [42:42] is the sub-list for method output_type
	42, // [42:42] is the sub-list for method input_type
	42, // [42:42] is the sub-list for extension type_name
	42, // [42:42] is the sub-list for extension extendee
	0,  // [0:42] is the sub-list for field type_name
}

func init() { file_pricing_fleet_proto_init() }
func file_pricing_fleet_proto_init() {
	if File_pricing_fleet_proto != nil {
		return
	}
	file_pricing_common_proto_init()
	file_pricing_configs_proto_init()
	file_pricing_sub_coverages_proto_init()
	file_pricing_policies_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pricing_fleet_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_BundleChunkSpecData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_PolicyChunkSpecData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_CommoditiesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_CommodityRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_Company); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_RadiusOfOperationRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_TaxRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_Terminal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_OperationClassRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_VehicleZoneRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_UnderwriterInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_Driver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_Vehicle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_LossSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_LargeLossesInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_LargeLoss); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_NegotiatedPremiumsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_NegotiatedPremium); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_UnclassifiedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_RatingTierDates); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_fleet_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fleet_ChunkOutputMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pricing_fleet_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_pricing_fleet_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_pricing_fleet_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_pricing_fleet_proto_msgTypes[19].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricing_fleet_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pricing_fleet_proto_goTypes,
		DependencyIndexes: file_pricing_fleet_proto_depIdxs,
		MessageInfos:      file_pricing_fleet_proto_msgTypes,
	}.Build()
	File_pricing_fleet_proto = out.File
	file_pricing_fleet_proto_rawDesc = nil
	file_pricing_fleet_proto_goTypes = nil
	file_pricing_fleet_proto_depIdxs = nil
}
