// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pricing/business_auto.proto

package ptypes

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	proto "nirvanatech.com/nirvana/business-auto/model/proto"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BusinessAuto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BusinessAuto) Reset() {
	*x = BusinessAuto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_business_auto_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessAuto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessAuto) ProtoMessage() {}

func (x *BusinessAuto) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_business_auto_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessAuto.ProtoReflect.Descriptor instead.
func (*BusinessAuto) Descriptor() ([]byte, []int) {
	return file_pricing_business_auto_proto_rawDescGZIP(), []int{0}
}

type BusinessAuto_BundleChunkSpecData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BusinessAuto_BundleChunkSpecData) Reset() {
	*x = BusinessAuto_BundleChunkSpecData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_business_auto_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessAuto_BundleChunkSpecData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessAuto_BundleChunkSpecData) ProtoMessage() {}

func (x *BusinessAuto_BundleChunkSpecData) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_business_auto_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessAuto_BundleChunkSpecData.ProtoReflect.Descriptor instead.
func (*BusinessAuto_BundleChunkSpecData) Descriptor() ([]byte, []int) {
	return file_pricing_business_auto_proto_rawDescGZIP(), []int{0, 0}
}

type BusinessAuto_PolicyChunkSpecData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Company                       *BusinessAuto_Company           `protobuf:"bytes,1,opt,name=company,proto3" json:"company,omitempty"`
	Vehicles                      []*BusinessAuto_Vehicle         `protobuf:"bytes,2,rep,name=vehicles,proto3" json:"vehicles,omitempty"`
	DriverClassModification       float64                         `protobuf:"fixed64,3,opt,name=driverClassModification,proto3" json:"driverClassModification,omitempty"`
	ScheduleModifications         []*ScheduleModification         `protobuf:"bytes,4,rep,name=scheduleModifications,proto3" json:"scheduleModifications,omitempty"`
	ExperienceRatingModifications []*ExperienceRatingModification `protobuf:"bytes,5,rep,name=experienceRatingModifications,proto3" json:"experienceRatingModifications,omitempty"`
	LossFreeModifications         []*LossFreeModification         `protobuf:"bytes,6,rep,name=lossFreeModifications,proto3" json:"lossFreeModifications,omitempty"`
}

func (x *BusinessAuto_PolicyChunkSpecData) Reset() {
	*x = BusinessAuto_PolicyChunkSpecData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_business_auto_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessAuto_PolicyChunkSpecData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessAuto_PolicyChunkSpecData) ProtoMessage() {}

func (x *BusinessAuto_PolicyChunkSpecData) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_business_auto_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessAuto_PolicyChunkSpecData.ProtoReflect.Descriptor instead.
func (*BusinessAuto_PolicyChunkSpecData) Descriptor() ([]byte, []int) {
	return file_pricing_business_auto_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BusinessAuto_PolicyChunkSpecData) GetCompany() *BusinessAuto_Company {
	if x != nil {
		return x.Company
	}
	return nil
}

func (x *BusinessAuto_PolicyChunkSpecData) GetVehicles() []*BusinessAuto_Vehicle {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

func (x *BusinessAuto_PolicyChunkSpecData) GetDriverClassModification() float64 {
	if x != nil {
		return x.DriverClassModification
	}
	return 0
}

func (x *BusinessAuto_PolicyChunkSpecData) GetScheduleModifications() []*ScheduleModification {
	if x != nil {
		return x.ScheduleModifications
	}
	return nil
}

func (x *BusinessAuto_PolicyChunkSpecData) GetExperienceRatingModifications() []*ExperienceRatingModification {
	if x != nil {
		return x.ExperienceRatingModifications
	}
	return nil
}

func (x *BusinessAuto_PolicyChunkSpecData) GetLossFreeModifications() []*LossFreeModification {
	if x != nil {
		return x.LossFreeModifications
	}
	return nil
}

type BusinessAuto_Company struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                                                string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	State                                             string `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	AnnualCostOfHire                                  *int64 `protobuf:"varint,3,opt,name=annualCostOfHire,proto3,oneof" json:"annualCostOfHire,omitempty"`
	NumberOfEmployeesOperatingPersonalVehiclesForWork int64  `protobuf:"varint,4,opt,name=numberOfEmployeesOperatingPersonalVehiclesForWork,proto3" json:"numberOfEmployeesOperatingPersonalVehiclesForWork,omitempty"`
	HasMultiStateFilings                              bool   `protobuf:"varint,5,opt,name=hasMultiStateFilings,proto3" json:"hasMultiStateFilings,omitempty"`
	HasFMCSAFilings                                   bool   `protobuf:"varint,6,opt,name=hasFMCSAFilings,proto3" json:"hasFMCSAFilings,omitempty"`
	HasDOTFilings                                     bool   `protobuf:"varint,7,opt,name=hasDOTFilings,proto3" json:"hasDOTFilings,omitempty"`
	HasWorkersCompPolicy                              bool   `protobuf:"varint,10,opt,name=hasWorkersCompPolicy,proto3" json:"hasWorkersCompPolicy,omitempty"`
	IsIndividualNamedInsured                          bool   `protobuf:"varint,8,opt,name=isIndividualNamedInsured,proto3" json:"isIndividualNamedInsured,omitempty"`
	MaxValueOfHiredAutos                              int64  `protobuf:"varint,9,opt,name=maxValueOfHiredAutos,proto3" json:"maxValueOfHiredAutos,omitempty"`
}

func (x *BusinessAuto_Company) Reset() {
	*x = BusinessAuto_Company{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_business_auto_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessAuto_Company) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessAuto_Company) ProtoMessage() {}

func (x *BusinessAuto_Company) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_business_auto_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessAuto_Company.ProtoReflect.Descriptor instead.
func (*BusinessAuto_Company) Descriptor() ([]byte, []int) {
	return file_pricing_business_auto_proto_rawDescGZIP(), []int{0, 2}
}

func (x *BusinessAuto_Company) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BusinessAuto_Company) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *BusinessAuto_Company) GetAnnualCostOfHire() int64 {
	if x != nil && x.AnnualCostOfHire != nil {
		return *x.AnnualCostOfHire
	}
	return 0
}

func (x *BusinessAuto_Company) GetNumberOfEmployeesOperatingPersonalVehiclesForWork() int64 {
	if x != nil {
		return x.NumberOfEmployeesOperatingPersonalVehiclesForWork
	}
	return 0
}

func (x *BusinessAuto_Company) GetHasMultiStateFilings() bool {
	if x != nil {
		return x.HasMultiStateFilings
	}
	return false
}

func (x *BusinessAuto_Company) GetHasFMCSAFilings() bool {
	if x != nil {
		return x.HasFMCSAFilings
	}
	return false
}

func (x *BusinessAuto_Company) GetHasDOTFilings() bool {
	if x != nil {
		return x.HasDOTFilings
	}
	return false
}

func (x *BusinessAuto_Company) GetHasWorkersCompPolicy() bool {
	if x != nil {
		return x.HasWorkersCompPolicy
	}
	return false
}

func (x *BusinessAuto_Company) GetIsIndividualNamedInsured() bool {
	if x != nil {
		return x.IsIndividualNamedInsured
	}
	return false
}

func (x *BusinessAuto_Company) GetMaxValueOfHiredAutos() int64 {
	if x != nil {
		return x.MaxValueOfHiredAutos
	}
	return 0
}

type BusinessAuto_Vehicle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin               string                     `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	Type              proto.VehicleType          `protobuf:"varint,2,opt,name=type,proto3,enum=business_auto_model.VehicleType" json:"type,omitempty"`
	Size              proto.VehicleSize          `protobuf:"varint,3,opt,name=size,proto3,enum=business_auto_model.VehicleSize" json:"size,omitempty"`
	IndustryType      proto.IndustryType         `protobuf:"varint,4,opt,name=industryType,proto3,enum=business_auto_model.IndustryType" json:"industryType,omitempty"`
	StateUsage        proto.StateUsage           `protobuf:"varint,5,opt,name=stateUsage,proto3,enum=business_auto_model.StateUsage" json:"stateUsage,omitempty"`
	SpecialtyType     proto.VehicleSpecialtyType `protobuf:"varint,6,opt,name=specialtyType,proto3,enum=business_auto_model.VehicleSpecialtyType" json:"specialtyType,omitempty"`
	BusinessUse       proto.BusinessUse          `protobuf:"varint,7,opt,name=businessUse,proto3,enum=business_auto_model.BusinessUse" json:"businessUse,omitempty"`
	RadiusOfOperation proto.RadiusOfOperation    `protobuf:"varint,8,opt,name=radiusOfOperation,proto3,enum=business_auto_model.RadiusOfOperation" json:"radiusOfOperation,omitempty"`
	GarageZipCode     string                     `protobuf:"bytes,9,opt,name=garageZipCode,proto3" json:"garageZipCode,omitempty"`
	StatedValue       int64                      `protobuf:"varint,10,opt,name=statedValue,proto3" json:"statedValue,omitempty"`
	YearMade          int64                      `protobuf:"varint,11,opt,name=yearMade,proto3" json:"yearMade,omitempty"`
	IsGlassLined      bool                       `protobuf:"varint,12,opt,name=isGlassLined,proto3" json:"isGlassLined,omitempty"`
	IsRefrigerated    bool                       `protobuf:"varint,13,opt,name=isRefrigerated,proto3" json:"isRefrigerated,omitempty"`
	IsDoubleTrailer   bool                       `protobuf:"varint,14,opt,name=isDoubleTrailer,proto3" json:"isDoubleTrailer,omitempty"`
	SubCoverages      []SubCoverageType          `protobuf:"varint,15,rep,packed,name=subCoverages,proto3,enum=pricing.SubCoverageType" json:"subCoverages,omitempty"`
	LimitSpecs        []*LimitSpec               `protobuf:"bytes,16,rep,name=limitSpecs,proto3" json:"limitSpecs,omitempty"`
	DeductibleSpecs   []*DeductibleSpec          `protobuf:"bytes,17,rep,name=deductibleSpecs,proto3" json:"deductibleSpecs,omitempty"`
}

func (x *BusinessAuto_Vehicle) Reset() {
	*x = BusinessAuto_Vehicle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_business_auto_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessAuto_Vehicle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessAuto_Vehicle) ProtoMessage() {}

func (x *BusinessAuto_Vehicle) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_business_auto_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessAuto_Vehicle.ProtoReflect.Descriptor instead.
func (*BusinessAuto_Vehicle) Descriptor() ([]byte, []int) {
	return file_pricing_business_auto_proto_rawDescGZIP(), []int{0, 3}
}

func (x *BusinessAuto_Vehicle) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *BusinessAuto_Vehicle) GetType() proto.VehicleType {
	if x != nil {
		return x.Type
	}
	return proto.VehicleType(0)
}

func (x *BusinessAuto_Vehicle) GetSize() proto.VehicleSize {
	if x != nil {
		return x.Size
	}
	return proto.VehicleSize(0)
}

func (x *BusinessAuto_Vehicle) GetIndustryType() proto.IndustryType {
	if x != nil {
		return x.IndustryType
	}
	return proto.IndustryType(0)
}

func (x *BusinessAuto_Vehicle) GetStateUsage() proto.StateUsage {
	if x != nil {
		return x.StateUsage
	}
	return proto.StateUsage(0)
}

func (x *BusinessAuto_Vehicle) GetSpecialtyType() proto.VehicleSpecialtyType {
	if x != nil {
		return x.SpecialtyType
	}
	return proto.VehicleSpecialtyType(0)
}

func (x *BusinessAuto_Vehicle) GetBusinessUse() proto.BusinessUse {
	if x != nil {
		return x.BusinessUse
	}
	return proto.BusinessUse(0)
}

func (x *BusinessAuto_Vehicle) GetRadiusOfOperation() proto.RadiusOfOperation {
	if x != nil {
		return x.RadiusOfOperation
	}
	return proto.RadiusOfOperation(0)
}

func (x *BusinessAuto_Vehicle) GetGarageZipCode() string {
	if x != nil {
		return x.GarageZipCode
	}
	return ""
}

func (x *BusinessAuto_Vehicle) GetStatedValue() int64 {
	if x != nil {
		return x.StatedValue
	}
	return 0
}

func (x *BusinessAuto_Vehicle) GetYearMade() int64 {
	if x != nil {
		return x.YearMade
	}
	return 0
}

func (x *BusinessAuto_Vehicle) GetIsGlassLined() bool {
	if x != nil {
		return x.IsGlassLined
	}
	return false
}

func (x *BusinessAuto_Vehicle) GetIsRefrigerated() bool {
	if x != nil {
		return x.IsRefrigerated
	}
	return false
}

func (x *BusinessAuto_Vehicle) GetIsDoubleTrailer() bool {
	if x != nil {
		return x.IsDoubleTrailer
	}
	return false
}

func (x *BusinessAuto_Vehicle) GetSubCoverages() []SubCoverageType {
	if x != nil {
		return x.SubCoverages
	}
	return nil
}

func (x *BusinessAuto_Vehicle) GetLimitSpecs() []*LimitSpec {
	if x != nil {
		return x.LimitSpecs
	}
	return nil
}

func (x *BusinessAuto_Vehicle) GetDeductibleSpecs() []*DeductibleSpec {
	if x != nil {
		return x.DeductibleSpecs
	}
	return nil
}

type BusinessAuto_ChunkOutputMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumberOfPowerUnits int32 `protobuf:"varint,1,opt,name=numberOfPowerUnits,proto3" json:"numberOfPowerUnits,omitempty"`
}

func (x *BusinessAuto_ChunkOutputMetadata) Reset() {
	*x = BusinessAuto_ChunkOutputMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_business_auto_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessAuto_ChunkOutputMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessAuto_ChunkOutputMetadata) ProtoMessage() {}

func (x *BusinessAuto_ChunkOutputMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_business_auto_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessAuto_ChunkOutputMetadata.ProtoReflect.Descriptor instead.
func (*BusinessAuto_ChunkOutputMetadata) Descriptor() ([]byte, []int) {
	return file_pricing_business_auto_proto_rawDescGZIP(), []int{0, 4}
}

func (x *BusinessAuto_ChunkOutputMetadata) GetNumberOfPowerUnits() int32 {
	if x != nil {
		return x.NumberOfPowerUnits
	}
	return 0
}

var File_pricing_business_auto_proto protoreflect.FileDescriptor

var file_pricing_business_auto_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x1a, 0x14, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xe3, 0x0f, 0x0a, 0x0c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x75,
	0x74, 0x6f, 0x1a, 0x15, 0x0a, 0x13, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x43, 0x68, 0x75, 0x6e,
	0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74, 0x61, 0x1a, 0xda, 0x03, 0x0a, 0x13, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x70, 0x65, 0x63, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x37, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x39, 0x0a, 0x08, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41,
	0x75, 0x74, 0x6f, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x08, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x17, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x53, 0x0a, 0x15, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x6b, 0x0a, 0x1d, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65,
	0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x1d, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x53, 0x0a, 0x15, 0x6c, 0x6f, 0x73, 0x73, 0x46, 0x72, 0x65, 0x65, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x6f, 0x73, 0x73, 0x46,
	0x72, 0x65, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x15, 0x6c, 0x6f, 0x73, 0x73, 0x46, 0x72, 0x65, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x8b, 0x04, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x10, 0x61, 0x6e, 0x6e, 0x75,
	0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x10, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74,
	0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x88, 0x01, 0x01, 0x12, 0x6c, 0x0a, 0x31, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x4f, 0x66, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x46, 0x6f, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x31, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x45, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x46, 0x6f, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x12, 0x32, 0x0a, 0x14, 0x68, 0x61, 0x73, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x53, 0x74, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x68, 0x61, 0x73, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x68,
	0x61, 0x73, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x68, 0x61, 0x73, 0x46, 0x4d, 0x43, 0x53, 0x41, 0x46, 0x69,
	0x6c, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x68, 0x61, 0x73, 0x44, 0x4f, 0x54, 0x46,
	0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61,
	0x73, 0x44, 0x4f, 0x54, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x32, 0x0a, 0x14, 0x68,
	0x61, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x68, 0x61, 0x73, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12,
	0x3a, 0x0a, 0x18, 0x69, 0x73, 0x49, 0x6e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x18, 0x69, 0x73, 0x49, 0x6e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x75, 0x61, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x12, 0x32, 0x0a, 0x14, 0x6d,
	0x61, 0x78, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x64, 0x41, 0x75,
	0x74, 0x6f, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x6d, 0x61, 0x78, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x42,
	0x13, 0x0a, 0x11, 0x5f, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x4f, 0x66,
	0x48, 0x69, 0x72, 0x65, 0x1a, 0x89, 0x07, 0x0a, 0x07, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76,
	0x69, 0x6e, 0x12, 0x34, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x20, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x45,
	0x0a, 0x0c, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x64, 0x75, 0x73,
	0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4f, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x69,
	0x61, 0x6c, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x52, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x11, 0x72,
	0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11,
	0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x67, 0x61, 0x72, 0x61, 0x67, 0x65, 0x5a, 0x69, 0x70, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x67, 0x61, 0x72, 0x61, 0x67, 0x65,
	0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x79, 0x65, 0x61,
	0x72, 0x4d, 0x61, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x79, 0x65, 0x61,
	0x72, 0x4d, 0x61, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x47, 0x6c, 0x61, 0x73, 0x73,
	0x4c, 0x69, 0x6e, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x47,
	0x6c, 0x61, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x69, 0x73, 0x52,
	0x65, 0x66, 0x72, 0x69, 0x67, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x69, 0x73, 0x52, 0x65, 0x66, 0x72, 0x69, 0x67, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x12, 0x28, 0x0a, 0x0f, 0x69, 0x73, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x54, 0x72, 0x61,
	0x69, 0x6c, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x44, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0c, 0x73,
	0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x75, 0x62,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x0a, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x53, 0x70, 0x65, 0x63, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x70, 0x65, 0x63, 0x73, 0x12, 0x41, 0x0a,
	0x0f, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73,
	0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2e, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52,
	0x0f, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x73,
	0x1a, 0x45, 0x0a, 0x13, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x12, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x4f, 0x66, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x12, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x42, 0x33, 0x5a, 0x31, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x2f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pricing_business_auto_proto_rawDescOnce sync.Once
	file_pricing_business_auto_proto_rawDescData = file_pricing_business_auto_proto_rawDesc
)

func file_pricing_business_auto_proto_rawDescGZIP() []byte {
	file_pricing_business_auto_proto_rawDescOnce.Do(func() {
		file_pricing_business_auto_proto_rawDescData = protoimpl.X.CompressGZIP(file_pricing_business_auto_proto_rawDescData)
	})
	return file_pricing_business_auto_proto_rawDescData
}

var file_pricing_business_auto_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pricing_business_auto_proto_goTypes = []interface{}{
	(*BusinessAuto)(nil),                     // 0: pricing.BusinessAuto
	(*BusinessAuto_BundleChunkSpecData)(nil), // 1: pricing.BusinessAuto.BundleChunkSpecData
	(*BusinessAuto_PolicyChunkSpecData)(nil), // 2: pricing.BusinessAuto.PolicyChunkSpecData
	(*BusinessAuto_Company)(nil),             // 3: pricing.BusinessAuto.Company
	(*BusinessAuto_Vehicle)(nil),             // 4: pricing.BusinessAuto.Vehicle
	(*BusinessAuto_ChunkOutputMetadata)(nil), // 5: pricing.BusinessAuto.ChunkOutputMetadata
	(*ScheduleModification)(nil),             // 6: pricing.ScheduleModification
	(*ExperienceRatingModification)(nil),     // 7: pricing.ExperienceRatingModification
	(*LossFreeModification)(nil),             // 8: pricing.LossFreeModification
	(proto.VehicleType)(0),                   // 9: business_auto_model.VehicleType
	(proto.VehicleSize)(0),                   // 10: business_auto_model.VehicleSize
	(proto.IndustryType)(0),                  // 11: business_auto_model.IndustryType
	(proto.StateUsage)(0),                    // 12: business_auto_model.StateUsage
	(proto.VehicleSpecialtyType)(0),          // 13: business_auto_model.VehicleSpecialtyType
	(proto.BusinessUse)(0),                   // 14: business_auto_model.BusinessUse
	(proto.RadiusOfOperation)(0),             // 15: business_auto_model.RadiusOfOperation
	(SubCoverageType)(0),                     // 16: pricing.SubCoverageType
	(*LimitSpec)(nil),                        // 17: pricing.LimitSpec
	(*DeductibleSpec)(nil),                   // 18: pricing.DeductibleSpec
}
var file_pricing_business_auto_proto_depIdxs = []int32{
	3,  // 0: pricing.BusinessAuto.PolicyChunkSpecData.company:type_name -> pricing.BusinessAuto.Company
	4,  // 1: pricing.BusinessAuto.PolicyChunkSpecData.vehicles:type_name -> pricing.BusinessAuto.Vehicle
	6,  // 2: pricing.BusinessAuto.PolicyChunkSpecData.scheduleModifications:type_name -> pricing.ScheduleModification
	7,  // 3: pricing.BusinessAuto.PolicyChunkSpecData.experienceRatingModifications:type_name -> pricing.ExperienceRatingModification
	8,  // 4: pricing.BusinessAuto.PolicyChunkSpecData.lossFreeModifications:type_name -> pricing.LossFreeModification
	9,  // 5: pricing.BusinessAuto.Vehicle.type:type_name -> business_auto_model.VehicleType
	10, // 6: pricing.BusinessAuto.Vehicle.size:type_name -> business_auto_model.VehicleSize
	11, // 7: pricing.BusinessAuto.Vehicle.industryType:type_name -> business_auto_model.IndustryType
	12, // 8: pricing.BusinessAuto.Vehicle.stateUsage:type_name -> business_auto_model.StateUsage
	13, // 9: pricing.BusinessAuto.Vehicle.specialtyType:type_name -> business_auto_model.VehicleSpecialtyType
	14, // 10: pricing.BusinessAuto.Vehicle.businessUse:type_name -> business_auto_model.BusinessUse
	15, // 11: pricing.BusinessAuto.Vehicle.radiusOfOperation:type_name -> business_auto_model.RadiusOfOperation
	16, // 12: pricing.BusinessAuto.Vehicle.subCoverages:type_name -> pricing.SubCoverageType
	17, // 13: pricing.BusinessAuto.Vehicle.limitSpecs:type_name -> pricing.LimitSpec
	18, // 14: pricing.BusinessAuto.Vehicle.deductibleSpecs:type_name -> pricing.DeductibleSpec
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_pricing_business_auto_proto_init() }
func file_pricing_business_auto_proto_init() {
	if File_pricing_business_auto_proto != nil {
		return
	}
	file_pricing_common_proto_init()
	file_pricing_sub_coverages_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pricing_business_auto_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessAuto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_business_auto_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessAuto_BundleChunkSpecData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_business_auto_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessAuto_PolicyChunkSpecData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_business_auto_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessAuto_Company); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_business_auto_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessAuto_Vehicle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_business_auto_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessAuto_ChunkOutputMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pricing_business_auto_proto_msgTypes[3].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricing_business_auto_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pricing_business_auto_proto_goTypes,
		DependencyIndexes: file_pricing_business_auto_proto_depIdxs,
		MessageInfos:      file_pricing_business_auto_proto_msgTypes,
	}.Build()
	File_pricing_business_auto_proto = out.File
	file_pricing_business_auto_proto_rawDesc = nil
	file_pricing_business_auto_proto_goTypes = nil
	file_pricing_business_auto_proto_depIdxs = nil
}
