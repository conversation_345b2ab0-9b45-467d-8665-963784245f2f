//nolint:all
package models_release

import (
	"nirvanatech.com/nirvana/common-go/time_utils"
)

var (
	// effectiveDate20220809 has a date value of 2022-08-09
	effectiveDate20220809 = time_utils.NewDate(2022, 8, 9)
	// effectiveDate20220818 has a date value of 2022-08-18
	effectiveDate20220824 = time_utils.NewDate(2022, 8, 24)
	// effectiveDate20221031 has a date value of 2022-10-31
	effectiveDate20221031 = time_utils.NewDate(2022, 10, 31)
	// effectiveDate20221202 has a date value of 2022-12-02
	effectiveDate20221202 = time_utils.NewDate(2022, 12, 0o2)
	// effectiveDate20221207 has a date value of 2022-12-07
	effectiveDate20221207 = time_utils.NewDate(2022, 12, 0o7)
	// effectiveDate20221208 has a date value of 2022-12-08
	effectiveDate20221208 = time_utils.NewDate(2022, 12, 8)
	// effectiveDate20230119 has a date value of 2023-01-19
	effectiveDate20230119 = time_utils.NewDate(2023, 1, 19)
	// effectiveDate20230302 has a date value of 2023-03-02
	effectiveDate20230302 = time_utils.NewDate(2023, 3, 2)
	// effectiveDate20230307 has a date value of 2023-03-07
	effectiveDate20230307 = time_utils.NewDate(2023, 3, 0o7)
	// effectiveDate20230317 has a date value of 2023-03-17
	effectiveDate20230317 = time_utils.NewDate(2023, 3, 17)
	// effectiveDate20230501 has a date value of 2023-05-01
	effectiveDate20230501 = time_utils.NewDate(2023, 5, 1)
	// effectiveDate20230502 has a date value of 2023-05-02
	effectiveDate20230502 = time_utils.NewDate(2023, 5, 2)
	// effectiveDate20230613 has a date value of 2023-06-13
	effectiveDate20230613 = time_utils.NewDate(2023, 6, 13)
	// effectiveDate20230627 has a date value of 2023-06-27
	effectiveDate20230627 = time_utils.NewDate(2023, 6, 27)
	// effectiveDate20230628 has a date value of 2023-06-28
	effectiveDate20230628 = time_utils.NewDate(2023, 6, 28)
	// effectiveDate20230629 has a date value of 2023-06-29
	effectiveDate20230629 = time_utils.NewDate(2023, 6, 29)
	// effectiveDate20230906 has a date value of 2023-09-06
	effectiveDate20230906 = time_utils.NewDate(2023, 9, 6)
	// effectiveDate20230911 has a date value of 2023-09-11
	effectiveDate20230911 = time_utils.NewDate(2023, 9, 11)
	// effectiveDate20230912 has a date value of 2023-09-12
	effectiveDate20230912 = time_utils.NewDate(2023, 9, 12)
	// effectiveDate20230913 has a date value of 2023-09-13
	effectiveDate20230913 = time_utils.NewDate(2023, 9, 13)
	// effectiveDate20230915 has a date value of 2023-09-15
	effectiveDate20230915 = time_utils.NewDate(2023, 9, 15)
	// effectiveDate20230918 has a date value of 2023-09-18
	effectiveDate20230918 = time_utils.NewDate(2023, 9, 18)
	// effectiveDate20230927 has a date value of 2023-09-27
	effectiveDate20230927 = time_utils.NewDate(2023, 9, 27)
	// effectiveDate20230928 has a date value of 2023-09-28
	effectiveDate20230928 = time_utils.NewDate(2023, 9, 28)
	// effectiveDate20231023 has a date value of 2023-10-23
	effectiveDate20231023 = time_utils.NewDate(2023, 10, 23)
	// effectiveDate20231208 has a date value of 2023-12-08
	effectiveDate20231208 = time_utils.NewDate(2023, 12, 8)
	// effectiveDate20231212 has a date value of 2023-12-12
	effectiveDate20231212 = time_utils.NewDate(2023, 12, 12)
	// effectiveDate20231213 has a date value of 2023-12-13
	effectiveDate20231213 = time_utils.NewDate(2023, 12, 13)
	// effectiveDate20231214 has a date value of 2023-12-14
	effectiveDate20231214 = time_utils.NewDate(2023, 12, 14)
	// effectiveDate20231222 has a date value of 2023-12-22
	effectiveDate20231222 = time_utils.NewDate(2023, 12, 22)
	// effectiveDate20231226 has a date value of 2023-12-26
	effectiveDate20231226 = time_utils.NewDate(2023, 12, 26)
	// effectiveDate20231227 has a date value of 2023-12-27
	effectiveDate20231227 = time_utils.NewDate(2023, 12, 27)
	// effectiveDate20231229 has a date value of 2023-12-29
	effectiveDate20231229 = time_utils.NewDate(2023, 12, 29)
	// effectiveDate20240102 has a date value of 2024-01-02
	effectiveDate20240102 = time_utils.NewDate(2024, 0o1, 0o2)
	// effectiveDate20240103 has a date value of 2024-01-03
	effectiveDate20240103 = time_utils.NewDate(2024, 0o1, 0o3)
	// effectiveDate20240104 has a date value of 2024-01-04
	effectiveDate20240104 = time_utils.NewDate(2024, 0o1, 0o4)
	// effectiveDate20240118 has a date value of 2024-01-18
	effectiveDate20240118 = time_utils.NewDate(2024, 0o1, 18)
	// effectiveDate20240202 has a date value of 2024-02-02
	effectiveDate20240202 = time_utils.NewDate(2024, 0o2, 2)
	// effectiveDate20240209 has a date value of 2024-02-09
	effectiveDate20240209 = time_utils.NewDate(2024, 0o2, 9)
	// effectiveDate20240211 has a date value of 2024-02-11
	effectiveDate20240211 = time_utils.NewDate(2024, 0o2, 11)
	// effectiveDate20240212 has a date value of 2024-02-12
	effectiveDate20240212 = time_utils.NewDate(2024, 0o2, 12)
	// effectiveDate20240229 has a date value of 2024-02-29
	effectiveDate20240229 = time_utils.NewDate(2024, 0o2, 29)
	// effectiveDate20240302 has a date value of 2024-03-02
	effectiveDate20240302 = time_utils.NewDate(2024, 0o3, 0o2)
	// effectiveDate20240304 has a date value of 2024-03-04
	effectiveDate20240304 = time_utils.NewDate(2024, 0o3, 0o4)
	// effectiveDate20240325 has a date value of 2024-03-25
	effectiveDate20240325 = time_utils.NewDate(2024, 0o3, 25)
	// effectiveDate20240326 has a date value of 2024-03-26
	effectiveDate20240326 = time_utils.NewDate(2024, 0o3, 26)
	// effectiveDate20240331 has a date value of 2024-03-31
	effectiveDate20240331 = time_utils.NewDate(2024, 0o3, 31)
	// effectiveDate20240401 has a date value of 2024-04-01
	effectiveDate20240401 = time_utils.NewDate(2024, 0o4, 0o1)
	// effectiveDate20240228 has a date value of 2024-02-28
	effectiveDate20240228 = time_utils.NewDate(2024, 0o2, 28)
	// effectiveDate20240308 has a date value of 2024-03-08
	effectiveDate20240308 = time_utils.NewDate(2024, 0o3, 8)
	// effectiveDate20240312 has a date value of 2024-03-12
	effectiveDate20240312 = time_utils.NewDate(2024, 0o3, 12)
	// effectiveDate20240429 has a date value of 2024-04-29
	effectiveDate20240429 = time_utils.NewDate(2024, 0o4, 29)
	// effectiveDate20240501 has a date value of 2024-05-01
	effectiveDate20240501 = time_utils.NewDate(2024, 0o5, 0o1)
	// effectiveDate20240502 has a date value of 2024-05-02
	effectiveDate20240502 = time_utils.NewDate(2024, 0o5, 0o2)
	// effectiveDate20240410 has a date value of 2024-04-10
	effectiveDate20240410 = time_utils.NewDate(2024, 0o4, 10)
	// effectiveDate20240418 has a date value of 2024-04-18
	effectiveDate20240418 = time_utils.NewDate(2024, 0o4, 18)
	// effectiveDate20240503 has a date value of 2024-05-03
	effectiveDate20240503 = time_utils.NewDate(2024, 0o5, 0o3)
	// effectiveDate20240509 has a date value of 2024-05-09
	effectiveDate20240509 = time_utils.NewDate(2024, 0o5, 9)
	// effectiveDate20240510 has a date value of 2024-05-10
	effectiveDate20240510 = time_utils.NewDate(2024, 0o5, 10)
	// effectiveDate20240529 has a date value of 2024-05-29
	effectiveDate20240529 = time_utils.NewDate(2024, 0o5, 29)
	// effectiveDate20240602 has a date value of 2024-06-02
	effectiveDate20240602 = time_utils.NewDate(2024, 0o6, 0o2)
	// effectiveDate20240701 has a date value of 2024-07-01
	effectiveDate20240701 = time_utils.NewDate(2024, 0o7, 0o1)
	// effectiveDate20240531 has a date value of 2024-05-31
	effectiveDate20240531 = time_utils.NewDate(2024, 0o5, 31)
	// effectiveDate20240619 has a date value of 2024-06-19
	effectiveDate20240619 = time_utils.NewDate(2024, 0o6, 19)
	// effectiveDate20240805 has a date value of 2024-08-05
	effectiveDate20240805 = time_utils.NewDate(2024, 8, 5)
	// effectiveDate20240708 has a date value of 2024-07-08
	effectiveDate20240708 = time_utils.NewDate(2024, 7, 8)
	// effectiveDate20240716 has a date value of 2024-07-16
	effectiveDate20240716 = time_utils.NewDate(2024, 7, 16)
	// effectiveDate20240812 has a date value of 2024-08-12
	effectiveDate20240812 = time_utils.NewDate(2024, 8, 12)
	// effectiveDate20240823 has a date value of 2024-08-23
	effectiveDate20240823 = time_utils.NewDate(2024, 8, 23)
	// effectiveDate20240901 has a date value of 2024-09-01
	effectiveDate20240901 = time_utils.NewDate(2024, 9, 0o1)
	// effectiveDate20240904 has a date value of 2024-09-04
	effectiveDate20240904 = time_utils.NewDate(2024, 9, 0o4)
	// effectiveDate20240918 has a date value of 2024-09-18
	effectiveDate20240918 = time_utils.NewDate(2024, 9, 18)
	// effectiveDate20241108 has a date value of 2024-11-08
	effectiveDate20241108 = time_utils.NewDate(2024, 11, 8)
	// effectiveDate20241126 has a date value of 2024-11-26
	effectiveDate20241126 = time_utils.NewDate(2024, 11, 26)
	// effectiveDate20241201 has a date value of 2024-12-01
	effectiveDate20241201 = time_utils.NewDate(2024, 12, 0o1)
	// effectiveDate20241215 has a date value of 2024-12-15
	effectiveDate20241215 = time_utils.NewDate(2024, 12, 15)
	// effectiveDate20241211 has a date value of 2024-12-11
	effectiveDate20241211 = time_utils.NewDate(2024, 12, 11)
	// effectiveDate20250101 has a date value of 2025-01-01
	effectiveDate20250101 = time_utils.NewDate(2025, 0o1, 0o1)
	// effectiveDate20250109 has a date value of 2025-01-09
	effectiveDate20250109 = time_utils.NewDate(2025, 0o1, 9)
	// effectiveDate20250122 has a date value of 2025-01-22
	effectiveDate20250122 = time_utils.NewDate(2025, 0o1, 22)
	// effectiveDate20250130 has a date value of 2025-01-30
	effectiveDate20250130 = time_utils.NewDate(2025, 0o1, 30)
	// effectiveDate20250201 has a date value of 2025-02-01
	effectiveDate20250201 = time_utils.NewDate(2025, 0o2, 0o1)
	// effectiveDate20250205 has a date value of 2025-02-05
	effectiveDate20250205 = time_utils.NewDate(2025, 0o2, 0o5)
	// effectiveDate20250307 has a date value of 2025-03-07
	effectiveDate20250307 = time_utils.NewDate(2025, 0o3, 0o7)
	// effectiveDate20250313 has a date value of 2025-03-13
	effectiveDate20250313 = time_utils.NewDate(2025, 0o3, 13)
	// effectiveDate20250315 has a date value of 2025-03-15
	effectiveDate20250315 = time_utils.NewDate(2025, 0o3, 15)
	// effectiveDate20250318 has a date value of 2025-03-18
	effectiveDate20250318 = time_utils.NewDate(2025, 0o3, 18)
	// effectiveDate20250402 has a date value of 2025-04-02
	effectiveDate20250402 = time_utils.NewDate(2025, 0o4, 0o2)
	// effectiveDate20250408 has a date value of 2025-04-08
	effectiveDate20250408 = time_utils.NewDate(2025, 4, 8)
	// effectiveDate20250501 has a date value of 2025-05-01
	effectiveDate20250501 = time_utils.NewDate(2025, 5, 1)
	// effectiveDate20250601 has a date value of 2025-06-01
	effectiveDate20250601 = time_utils.NewDate(2025, 6, 1)
	// effectiveDate20250615 has a date value of 2025-06-15
	effectiveDate20250615 = time_utils.NewDate(2025, 6, 15)
	// effectiveDate20250701 has a date value of 2025-07-01
	effectiveDate20250701 = time_utils.NewDate(2025, 7, 1)
	// effectiveDate20250722 has a date value of 2025-07-22
	effectiveDate20250722 = time_utils.NewDate(2025, 7, 22)
	// effectiveDate20250801 has a date value of 2025-08-01
	effectiveDate20250801 = time_utils.NewDate(2025, 8, 1)
	// effectiveDate20250806 has a date value of 2025-08-06
	effectiveDate20250806 = time_utils.NewDate(2025, 8, 6)
	// effectiveDate20250807 has a date value of 2025-08-07
	effectiveDate20250807 = time_utils.NewDate(2025, 8, 7)
	// effectiveDate20250815 has a date value of 2025-08-15
	effectiveDate20250815 = time_utils.NewDate(2025, 8, 15)
	// effectiveDate20250908 has a date value of 2025-09-08
	effectiveDate20250908 = time_utils.NewDate(2025, 9, 8)
	// effectiveDate20250907 has a date value of 2025-09-07
	effectiveDate20250907 = time_utils.NewDate(2025, 9, 7)
	// effectiveDate20250923 has a date value of 2025-09-23
	effectiveDate20250923 = time_utils.NewDate(2025, 9, 23)
)
