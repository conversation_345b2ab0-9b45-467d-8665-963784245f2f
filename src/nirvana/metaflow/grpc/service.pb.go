// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: metaflow/service.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RunStatus_RunState int32

const (
	RunStatus_SCHEDULED RunStatus_RunState = 0
	RunStatus_RUNNING   RunStatus_RunState = 1
	RunStatus_FAILED    RunStatus_RunState = 2
	RunStatus_FINISHED  RunStatus_RunState = 3
)

// Enum value maps for RunStatus_RunState.
var (
	RunStatus_RunState_name = map[int32]string{
		0: "SCHEDULED",
		1: "RUNNING",
		2: "FAILED",
		3: "FINISHED",
	}
	RunStatus_RunState_value = map[string]int32{
		"SCHEDULED": 0,
		"RUNNING":   1,
		"FAILED":    2,
		"FINISHED":  3,
	}
)

func (x RunStatus_RunState) Enum() *RunStatus_RunState {
	p := new(RunStatus_RunState)
	*p = x
	return p
}

func (x RunStatus_RunState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RunStatus_RunState) Descriptor() protoreflect.EnumDescriptor {
	return file_metaflow_service_proto_enumTypes[0].Descriptor()
}

func (RunStatus_RunState) Type() protoreflect.EnumType {
	return &file_metaflow_service_proto_enumTypes[0]
}

func (x RunStatus_RunState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RunStatus_RunState.Descriptor instead.
func (RunStatus_RunState) EnumDescriptor() ([]byte, []int) {
	return file_metaflow_service_proto_rawDescGZIP(), []int{3, 0}
}

type TriggerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Flow      Flow       `protobuf:"varint,1,opt,name=flow,proto3,enum=Flow" json:"flow,omitempty"`
	Input     *anypb.Any `protobuf:"bytes,2,opt,name=input,proto3" json:"input,omitempty"`
	ExtraTags []string   `protobuf:"bytes,3,rep,name=extra_tags,json=extraTags,proto3" json:"extra_tags,omitempty"`
}

func (x *TriggerRequest) Reset() {
	*x = TriggerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerRequest) ProtoMessage() {}

func (x *TriggerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerRequest.ProtoReflect.Descriptor instead.
func (*TriggerRequest) Descriptor() ([]byte, []int) {
	return file_metaflow_service_proto_rawDescGZIP(), []int{0}
}

func (x *TriggerRequest) GetFlow() Flow {
	if x != nil {
		return x.Flow
	}
	return Flow_Flow_Unknown
}

func (x *TriggerRequest) GetInput() *anypb.Any {
	if x != nil {
		return x.Input
	}
	return nil
}

func (x *TriggerRequest) GetExtraTags() []string {
	if x != nil {
		return x.ExtraTags
	}
	return nil
}

type Execution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID string `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
}

func (x *Execution) Reset() {
	*x = Execution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Execution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Execution) ProtoMessage() {}

func (x *Execution) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Execution.ProtoReflect.Descriptor instead.
func (*Execution) Descriptor() ([]byte, []int) {
	return file_metaflow_service_proto_rawDescGZIP(), []int{1}
}

func (x *Execution) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

type ManyExecution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Executions []*Execution `protobuf:"bytes,1,rep,name=executions,proto3" json:"executions,omitempty"`
}

func (x *ManyExecution) Reset() {
	*x = ManyExecution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManyExecution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManyExecution) ProtoMessage() {}

func (x *ManyExecution) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManyExecution.ProtoReflect.Descriptor instead.
func (*ManyExecution) Descriptor() ([]byte, []int) {
	return file_metaflow_service_proto_rawDescGZIP(), []int{2}
}

func (x *ManyExecution) GetExecutions() []*Execution {
	if x != nil {
		return x.Executions
	}
	return nil
}

type RunStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID          string                 `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	MetaflowId  string                 `protobuf:"bytes,2,opt,name=metaflow_id,json=metaflowId,proto3" json:"metaflow_id,omitempty"`
	FlowName    string                 `protobuf:"bytes,3,opt,name=flow_name,json=flowName,proto3" json:"flow_name,omitempty"`
	FlowVersion string                 `protobuf:"bytes,4,opt,name=flow_version,json=flowVersion,proto3" json:"flow_version,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	FinishedAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=finishedAt,proto3" json:"finishedAt,omitempty"`
	State       RunStatus_RunState     `protobuf:"varint,7,opt,name=state,proto3,enum=RunStatus_RunState" json:"state,omitempty"`
}

func (x *RunStatus) Reset() {
	*x = RunStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunStatus) ProtoMessage() {}

func (x *RunStatus) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunStatus.ProtoReflect.Descriptor instead.
func (*RunStatus) Descriptor() ([]byte, []int) {
	return file_metaflow_service_proto_rawDescGZIP(), []int{3}
}

func (x *RunStatus) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *RunStatus) GetMetaflowId() string {
	if x != nil {
		return x.MetaflowId
	}
	return ""
}

func (x *RunStatus) GetFlowName() string {
	if x != nil {
		return x.FlowName
	}
	return ""
}

func (x *RunStatus) GetFlowVersion() string {
	if x != nil {
		return x.FlowVersion
	}
	return ""
}

func (x *RunStatus) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RunStatus) GetFinishedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FinishedAt
	}
	return nil
}

func (x *RunStatus) GetState() RunStatus_RunState {
	if x != nil {
		return x.State
	}
	return RunStatus_SCHEDULED
}

type Query struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Flow           Flow     `protobuf:"varint,1,opt,name=flow,proto3,enum=Flow" json:"flow,omitempty"`
	SuccessfulOnly bool     `protobuf:"varint,2,opt,name=successfulOnly,proto3" json:"successfulOnly,omitempty"`
	Tags           []string `protobuf:"bytes,3,rep,name=tags,proto3" json:"tags,omitempty"`
	MaxResults     int32    `protobuf:"varint,4,opt,name=maxResults,proto3" json:"maxResults,omitempty"`
}

func (x *Query) Reset() {
	*x = Query{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Query) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Query) ProtoMessage() {}

func (x *Query) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Query.ProtoReflect.Descriptor instead.
func (*Query) Descriptor() ([]byte, []int) {
	return file_metaflow_service_proto_rawDescGZIP(), []int{4}
}

func (x *Query) GetFlow() Flow {
	if x != nil {
		return x.Flow
	}
	return Flow_Flow_Unknown
}

func (x *Query) GetSuccessfulOnly() bool {
	if x != nil {
		return x.SuccessfulOnly
	}
	return false
}

func (x *Query) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Query) GetMaxResults() int32 {
	if x != nil {
		return x.MaxResults
	}
	return 0
}

var File_metaflow_service_proto protoreflect.FileDescriptor

var file_metaflow_service_proto_rawDesc = []byte{
	0x0a, 0x16, 0x6d, 0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x6d, 0x65, 0x74, 0x61, 0x66, 0x6c,
	0x6f, 0x77, 0x2f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x76, 0x0a, 0x0e, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a,
	0x04, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x05, 0x2e, 0x46, 0x6c,
	0x6f, 0x77, 0x52, 0x04, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x2a, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x05, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x54,
	0x61, 0x67, 0x73, 0x22, 0x1b, 0x0a, 0x09, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x44,
	0x22, 0x3b, 0x0a, 0x0d, 0x4d, 0x61, 0x6e, 0x79, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2a, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xdf, 0x02,
	0x0a, 0x09, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x44, 0x12, 0x1f, 0x0a, 0x0b, 0x6d,
	0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6d, 0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x66, 0x6c, 0x6f, 0x77, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3a, 0x0a, 0x0a, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x41, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x29, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x52, 0x75,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x40, 0x0a,
	0x08, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x43, 0x48,
	0x45, 0x44, 0x55, 0x4c, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x55, 0x4e, 0x4e,
	0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x03, 0x22,
	0x7e, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x19, 0x0a, 0x04, 0x66, 0x6c, 0x6f, 0x77,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x05, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x04, 0x66,
	0x6c, 0x6f, 0x77, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75,
	0x6c, 0x4f, 0x6e, 0x6c, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12,
	0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x32,
	0xa8, 0x01, 0x0a, 0x17, 0x4d, 0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x07, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x0f, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0a, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x09, 0x52, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x12, 0x0a, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x0a, 0x2e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x0a, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x0a,
	0x2e, 0x52, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x04, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x06, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x1a, 0x0e, 0x2e, 0x4d, 0x61, 0x6e,
	0x79, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_metaflow_service_proto_rawDescOnce sync.Once
	file_metaflow_service_proto_rawDescData = file_metaflow_service_proto_rawDesc
)

func file_metaflow_service_proto_rawDescGZIP() []byte {
	file_metaflow_service_proto_rawDescOnce.Do(func() {
		file_metaflow_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_metaflow_service_proto_rawDescData)
	})
	return file_metaflow_service_proto_rawDescData
}

var file_metaflow_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_metaflow_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_metaflow_service_proto_goTypes = []interface{}{
	(RunStatus_RunState)(0),       // 0: RunStatus.RunState
	(*TriggerRequest)(nil),        // 1: TriggerRequest
	(*Execution)(nil),             // 2: Execution
	(*ManyExecution)(nil),         // 3: ManyExecution
	(*RunStatus)(nil),             // 4: RunStatus
	(*Query)(nil),                 // 5: Query
	(Flow)(0),                     // 6: Flow
	(*anypb.Any)(nil),             // 7: google.protobuf.Any
	(*timestamppb.Timestamp)(nil), // 8: google.protobuf.Timestamp
}
var file_metaflow_service_proto_depIdxs = []int32{
	6,  // 0: TriggerRequest.flow:type_name -> Flow
	7,  // 1: TriggerRequest.input:type_name -> google.protobuf.Any
	2,  // 2: ManyExecution.executions:type_name -> Execution
	8,  // 3: RunStatus.createdAt:type_name -> google.protobuf.Timestamp
	8,  // 4: RunStatus.finishedAt:type_name -> google.protobuf.Timestamp
	0,  // 5: RunStatus.state:type_name -> RunStatus.RunState
	6,  // 6: Query.flow:type_name -> Flow
	1,  // 7: MetaflowPipelineManager.Trigger:input_type -> TriggerRequest
	2,  // 8: MetaflowPipelineManager.ReTrigger:input_type -> Execution
	2,  // 9: MetaflowPipelineManager.Status:input_type -> Execution
	5,  // 10: MetaflowPipelineManager.List:input_type -> Query
	2,  // 11: MetaflowPipelineManager.Trigger:output_type -> Execution
	2,  // 12: MetaflowPipelineManager.ReTrigger:output_type -> Execution
	4,  // 13: MetaflowPipelineManager.Status:output_type -> RunStatus
	3,  // 14: MetaflowPipelineManager.List:output_type -> ManyExecution
	11, // [11:15] is the sub-list for method output_type
	7,  // [7:11] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_metaflow_service_proto_init() }
func file_metaflow_service_proto_init() {
	if File_metaflow_service_proto != nil {
		return
	}
	file_metaflow_inputs_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_metaflow_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Execution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManyExecution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Query); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_metaflow_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_metaflow_service_proto_goTypes,
		DependencyIndexes: file_metaflow_service_proto_depIdxs,
		EnumInfos:         file_metaflow_service_proto_enumTypes,
		MessageInfos:      file_metaflow_service_proto_msgTypes,
	}.Build()
	File_metaflow_service_proto = out.File
	file_metaflow_service_proto_rawDesc = nil
	file_metaflow_service_proto_goTypes = nil
	file_metaflow_service_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// MetaflowPipelineManagerClient is the client API for MetaflowPipelineManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MetaflowPipelineManagerClient interface {
	Trigger(ctx context.Context, in *TriggerRequest, opts ...grpc.CallOption) (*Execution, error)
	ReTrigger(ctx context.Context, in *Execution, opts ...grpc.CallOption) (*Execution, error)
	Status(ctx context.Context, in *Execution, opts ...grpc.CallOption) (*RunStatus, error)
	List(ctx context.Context, in *Query, opts ...grpc.CallOption) (*ManyExecution, error)
}

type metaflowPipelineManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewMetaflowPipelineManagerClient(cc grpc.ClientConnInterface) MetaflowPipelineManagerClient {
	return &metaflowPipelineManagerClient{cc}
}

func (c *metaflowPipelineManagerClient) Trigger(ctx context.Context, in *TriggerRequest, opts ...grpc.CallOption) (*Execution, error) {
	out := new(Execution)
	err := c.cc.Invoke(ctx, "/MetaflowPipelineManager/Trigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metaflowPipelineManagerClient) ReTrigger(ctx context.Context, in *Execution, opts ...grpc.CallOption) (*Execution, error) {
	out := new(Execution)
	err := c.cc.Invoke(ctx, "/MetaflowPipelineManager/ReTrigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metaflowPipelineManagerClient) Status(ctx context.Context, in *Execution, opts ...grpc.CallOption) (*RunStatus, error) {
	out := new(RunStatus)
	err := c.cc.Invoke(ctx, "/MetaflowPipelineManager/Status", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metaflowPipelineManagerClient) List(ctx context.Context, in *Query, opts ...grpc.CallOption) (*ManyExecution, error) {
	out := new(ManyExecution)
	err := c.cc.Invoke(ctx, "/MetaflowPipelineManager/List", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MetaflowPipelineManagerServer is the server API for MetaflowPipelineManager service.
type MetaflowPipelineManagerServer interface {
	Trigger(context.Context, *TriggerRequest) (*Execution, error)
	ReTrigger(context.Context, *Execution) (*Execution, error)
	Status(context.Context, *Execution) (*RunStatus, error)
	List(context.Context, *Query) (*ManyExecution, error)
}

// UnimplementedMetaflowPipelineManagerServer can be embedded to have forward compatible implementations.
type UnimplementedMetaflowPipelineManagerServer struct {
}

func (*UnimplementedMetaflowPipelineManagerServer) Trigger(context.Context, *TriggerRequest) (*Execution, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Trigger not implemented")
}
func (*UnimplementedMetaflowPipelineManagerServer) ReTrigger(context.Context, *Execution) (*Execution, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReTrigger not implemented")
}
func (*UnimplementedMetaflowPipelineManagerServer) Status(context.Context, *Execution) (*RunStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Status not implemented")
}
func (*UnimplementedMetaflowPipelineManagerServer) List(context.Context, *Query) (*ManyExecution, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}

func RegisterMetaflowPipelineManagerServer(s *grpc.Server, srv MetaflowPipelineManagerServer) {
	s.RegisterService(&_MetaflowPipelineManager_serviceDesc, srv)
}

func _MetaflowPipelineManager_Trigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetaflowPipelineManagerServer).Trigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MetaflowPipelineManager/Trigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetaflowPipelineManagerServer).Trigger(ctx, req.(*TriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetaflowPipelineManager_ReTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Execution)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetaflowPipelineManagerServer).ReTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MetaflowPipelineManager/ReTrigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetaflowPipelineManagerServer).ReTrigger(ctx, req.(*Execution))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetaflowPipelineManager_Status_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Execution)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetaflowPipelineManagerServer).Status(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MetaflowPipelineManager/Status",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetaflowPipelineManagerServer).Status(ctx, req.(*Execution))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetaflowPipelineManager_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Query)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetaflowPipelineManagerServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/MetaflowPipelineManager/List",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetaflowPipelineManagerServer).List(ctx, req.(*Query))
	}
	return interceptor(ctx, in, info, handler)
}

var _MetaflowPipelineManager_serviceDesc = grpc.ServiceDesc{
	ServiceName: "MetaflowPipelineManager",
	HandlerType: (*MetaflowPipelineManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Trigger",
			Handler:    _MetaflowPipelineManager_Trigger_Handler,
		},
		{
			MethodName: "ReTrigger",
			Handler:    _MetaflowPipelineManager_ReTrigger_Handler,
		},
		{
			MethodName: "Status",
			Handler:    _MetaflowPipelineManager_Status_Handler,
		},
		{
			MethodName: "List",
			Handler:    _MetaflowPipelineManager_List_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "metaflow/service.proto",
}
