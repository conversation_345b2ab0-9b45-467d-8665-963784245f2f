// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: metaflow/inputs.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Flow int32

const (
	Flow_Flow_Unknown                       Flow = 0
	Flow_Flow_UWTelematicsDataFlow__v1      Flow = 3
	Flow_Flow_Billing__v0                   Flow = 5
	Flow_Flow_RiskScoringFlow__v0           Flow = 6
	Flow_Flow_UWApplicationDataFlow__v1     Flow = 8
	Flow_Flow_AppetiteLiteInferenceFlow__v1 Flow = 9
	Flow_Flow_GRPCTestFlow__v1              Flow = 10
)

// Enum value maps for Flow.
var (
	Flow_name = map[int32]string{
		0:  "Flow_Unknown",
		3:  "Flow_UWTelematicsDataFlow__v1",
		5:  "Flow_Billing__v0",
		6:  "Flow_RiskScoringFlow__v0",
		8:  "Flow_UWApplicationDataFlow__v1",
		9:  "Flow_AppetiteLiteInferenceFlow__v1",
		10: "Flow_GRPCTestFlow__v1",
	}
	Flow_value = map[string]int32{
		"Flow_Unknown":                       0,
		"Flow_UWTelematicsDataFlow__v1":      3,
		"Flow_Billing__v0":                   5,
		"Flow_RiskScoringFlow__v0":           6,
		"Flow_UWApplicationDataFlow__v1":     8,
		"Flow_AppetiteLiteInferenceFlow__v1": 9,
		"Flow_GRPCTestFlow__v1":              10,
	}
)

func (x Flow) Enum() *Flow {
	p := new(Flow)
	*p = x
	return p
}

func (x Flow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Flow) Descriptor() protoreflect.EnumDescriptor {
	return file_metaflow_inputs_proto_enumTypes[0].Descriptor()
}

func (Flow) Type() protoreflect.EnumType {
	return &file_metaflow_inputs_proto_enumTypes[0]
}

func (x Flow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Flow.Descriptor instead.
func (Flow) EnumDescriptor() ([]byte, []int) {
	return file_metaflow_inputs_proto_rawDescGZIP(), []int{0}
}

type UWTelematicsDataFlow_V1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConnectionId   *string  `protobuf:"bytes,1,opt,name=connection_id,json=connectionId,proto3,oneof" json:"connection_id,omitempty"`
	PipelineId     *string  `protobuf:"bytes,2,opt,name=pipeline_id,json=pipelineId,proto3,oneof" json:"pipeline_id,omitempty"`
	Tsp            *string  `protobuf:"bytes,3,opt,name=tsp,proto3,oneof" json:"tsp,omitempty"`
	PolicyState    *string  `protobuf:"bytes,4,opt,name=policy_state,json=policyState,proto3,oneof" json:"policy_state,omitempty"`
	DataProvider   *string  `protobuf:"bytes,5,opt,name=data_provider,json=dataProvider,proto3,oneof" json:"data_provider,omitempty"`
	AvailableStats []string `protobuf:"bytes,6,rep,name=available_stats,json=availableStats,proto3" json:"available_stats,omitempty"`
	PlatformConfig *string  `protobuf:"bytes,7,opt,name=platform_config,json=platformConfig,proto3,oneof" json:"platform_config,omitempty"`
	ProgramType    *string  `protobuf:"bytes,8,opt,name=program_type,json=programType,proto3,oneof" json:"program_type,omitempty"`
	BackfillRun    *bool    `protobuf:"varint,9,opt,name=backfill_run,json=backfillRun,proto3,oneof" json:"backfill_run,omitempty"`
}

func (x *UWTelematicsDataFlow_V1) Reset() {
	*x = UWTelematicsDataFlow_V1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_inputs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UWTelematicsDataFlow_V1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UWTelematicsDataFlow_V1) ProtoMessage() {}

func (x *UWTelematicsDataFlow_V1) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_inputs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UWTelematicsDataFlow_V1.ProtoReflect.Descriptor instead.
func (*UWTelematicsDataFlow_V1) Descriptor() ([]byte, []int) {
	return file_metaflow_inputs_proto_rawDescGZIP(), []int{0}
}

func (x *UWTelematicsDataFlow_V1) GetConnectionId() string {
	if x != nil && x.ConnectionId != nil {
		return *x.ConnectionId
	}
	return ""
}

func (x *UWTelematicsDataFlow_V1) GetPipelineId() string {
	if x != nil && x.PipelineId != nil {
		return *x.PipelineId
	}
	return ""
}

func (x *UWTelematicsDataFlow_V1) GetTsp() string {
	if x != nil && x.Tsp != nil {
		return *x.Tsp
	}
	return ""
}

func (x *UWTelematicsDataFlow_V1) GetPolicyState() string {
	if x != nil && x.PolicyState != nil {
		return *x.PolicyState
	}
	return ""
}

func (x *UWTelematicsDataFlow_V1) GetDataProvider() string {
	if x != nil && x.DataProvider != nil {
		return *x.DataProvider
	}
	return ""
}

func (x *UWTelematicsDataFlow_V1) GetAvailableStats() []string {
	if x != nil {
		return x.AvailableStats
	}
	return nil
}

func (x *UWTelematicsDataFlow_V1) GetPlatformConfig() string {
	if x != nil && x.PlatformConfig != nil {
		return *x.PlatformConfig
	}
	return ""
}

func (x *UWTelematicsDataFlow_V1) GetProgramType() string {
	if x != nil && x.ProgramType != nil {
		return *x.ProgramType
	}
	return ""
}

func (x *UWTelematicsDataFlow_V1) GetBackfillRun() bool {
	if x != nil && x.BackfillRun != nil {
		return *x.BackfillRun
	}
	return false
}

type Billing_V0 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConnectionId *string `protobuf:"bytes,1,opt,name=connection_id,json=connectionId,proto3,oneof" json:"connection_id,omitempty"`
	PipelineId   *string `protobuf:"bytes,2,opt,name=pipeline_id,json=pipelineId,proto3,oneof" json:"pipeline_id,omitempty"`
	Tsp          *string `protobuf:"bytes,3,opt,name=tsp,proto3,oneof" json:"tsp,omitempty"`
	PolicyState  *string `protobuf:"bytes,4,opt,name=policy_state,json=policyState,proto3,oneof" json:"policy_state,omitempty"`
}

func (x *Billing_V0) Reset() {
	*x = Billing_V0{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_inputs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Billing_V0) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Billing_V0) ProtoMessage() {}

func (x *Billing_V0) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_inputs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Billing_V0.ProtoReflect.Descriptor instead.
func (*Billing_V0) Descriptor() ([]byte, []int) {
	return file_metaflow_inputs_proto_rawDescGZIP(), []int{1}
}

func (x *Billing_V0) GetConnectionId() string {
	if x != nil && x.ConnectionId != nil {
		return *x.ConnectionId
	}
	return ""
}

func (x *Billing_V0) GetPipelineId() string {
	if x != nil && x.PipelineId != nil {
		return *x.PipelineId
	}
	return ""
}

func (x *Billing_V0) GetTsp() string {
	if x != nil && x.Tsp != nil {
		return *x.Tsp
	}
	return ""
}

func (x *Billing_V0) GetPolicyState() string {
	if x != nil && x.PolicyState != nil {
		return *x.PolicyState
	}
	return ""
}

type RiskScoringFlow_V0 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConnectionId   *string  `protobuf:"bytes,1,opt,name=connection_id,json=connectionId,proto3,oneof" json:"connection_id,omitempty"`
	PipelineId     *string  `protobuf:"bytes,2,opt,name=pipeline_id,json=pipelineId,proto3,oneof" json:"pipeline_id,omitempty"`
	Tsp            *string  `protobuf:"bytes,3,opt,name=tsp,proto3,oneof" json:"tsp,omitempty"`
	PolicyState    *string  `protobuf:"bytes,4,opt,name=policy_state,json=policyState,proto3,oneof" json:"policy_state,omitempty"`
	DataProvider   *string  `protobuf:"bytes,5,opt,name=data_provider,json=dataProvider,proto3,oneof" json:"data_provider,omitempty"`
	AvailableStats []string `protobuf:"bytes,6,rep,name=available_stats,json=availableStats,proto3" json:"available_stats,omitempty"`
}

func (x *RiskScoringFlow_V0) Reset() {
	*x = RiskScoringFlow_V0{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_inputs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskScoringFlow_V0) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskScoringFlow_V0) ProtoMessage() {}

func (x *RiskScoringFlow_V0) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_inputs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskScoringFlow_V0.ProtoReflect.Descriptor instead.
func (*RiskScoringFlow_V0) Descriptor() ([]byte, []int) {
	return file_metaflow_inputs_proto_rawDescGZIP(), []int{2}
}

func (x *RiskScoringFlow_V0) GetConnectionId() string {
	if x != nil && x.ConnectionId != nil {
		return *x.ConnectionId
	}
	return ""
}

func (x *RiskScoringFlow_V0) GetPipelineId() string {
	if x != nil && x.PipelineId != nil {
		return *x.PipelineId
	}
	return ""
}

func (x *RiskScoringFlow_V0) GetTsp() string {
	if x != nil && x.Tsp != nil {
		return *x.Tsp
	}
	return ""
}

func (x *RiskScoringFlow_V0) GetPolicyState() string {
	if x != nil && x.PolicyState != nil {
		return *x.PolicyState
	}
	return ""
}

func (x *RiskScoringFlow_V0) GetDataProvider() string {
	if x != nil && x.DataProvider != nil {
		return *x.DataProvider
	}
	return ""
}

func (x *RiskScoringFlow_V0) GetAvailableStats() []string {
	if x != nil {
		return x.AvailableStats
	}
	return nil
}

type UWApplicationDataFlow_V1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId       *string  `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3,oneof" json:"application_id,omitempty"`
	ApplicationReviewId *string  `protobuf:"bytes,2,opt,name=application_review_id,json=applicationReviewId,proto3,oneof" json:"application_review_id,omitempty"`
	YearsInBusiness     *float64 `protobuf:"fixed64,3,opt,name=years_in_business,json=yearsInBusiness,proto3,oneof" json:"years_in_business,omitempty"`
	InsuranceHistory    []byte   `protobuf:"bytes,4,opt,name=insurance_history,json=insuranceHistory,proto3,oneof" json:"insurance_history,omitempty"`
}

func (x *UWApplicationDataFlow_V1) Reset() {
	*x = UWApplicationDataFlow_V1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_inputs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UWApplicationDataFlow_V1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UWApplicationDataFlow_V1) ProtoMessage() {}

func (x *UWApplicationDataFlow_V1) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_inputs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UWApplicationDataFlow_V1.ProtoReflect.Descriptor instead.
func (*UWApplicationDataFlow_V1) Descriptor() ([]byte, []int) {
	return file_metaflow_inputs_proto_rawDescGZIP(), []int{3}
}

func (x *UWApplicationDataFlow_V1) GetApplicationId() string {
	if x != nil && x.ApplicationId != nil {
		return *x.ApplicationId
	}
	return ""
}

func (x *UWApplicationDataFlow_V1) GetApplicationReviewId() string {
	if x != nil && x.ApplicationReviewId != nil {
		return *x.ApplicationReviewId
	}
	return ""
}

func (x *UWApplicationDataFlow_V1) GetYearsInBusiness() float64 {
	if x != nil && x.YearsInBusiness != nil {
		return *x.YearsInBusiness
	}
	return 0
}

func (x *UWApplicationDataFlow_V1) GetInsuranceHistory() []byte {
	if x != nil {
		return x.InsuranceHistory
	}
	return nil
}

type AppetiteLiteInferenceFlow_V1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartDate        *string `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	EndDate          *string `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	SpecificPullDate *string `protobuf:"bytes,3,opt,name=specific_pull_date,json=specificPullDate,proto3,oneof" json:"specific_pull_date,omitempty"`
}

func (x *AppetiteLiteInferenceFlow_V1) Reset() {
	*x = AppetiteLiteInferenceFlow_V1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_inputs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppetiteLiteInferenceFlow_V1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppetiteLiteInferenceFlow_V1) ProtoMessage() {}

func (x *AppetiteLiteInferenceFlow_V1) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_inputs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppetiteLiteInferenceFlow_V1.ProtoReflect.Descriptor instead.
func (*AppetiteLiteInferenceFlow_V1) Descriptor() ([]byte, []int) {
	return file_metaflow_inputs_proto_rawDescGZIP(), []int{4}
}

func (x *AppetiteLiteInferenceFlow_V1) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *AppetiteLiteInferenceFlow_V1) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *AppetiteLiteInferenceFlow_V1) GetSpecificPullDate() string {
	if x != nil && x.SpecificPullDate != nil {
		return *x.SpecificPullDate
	}
	return ""
}

type GRPCTestFlow_V1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mybool            *bool         `protobuf:"varint,1,opt,name=mybool,proto3,oneof" json:"mybool,omitempty"`
	Myint             *int64        `protobuf:"varint,2,opt,name=myint,proto3,oneof" json:"myint,omitempty"`
	Mystr             *string       `protobuf:"bytes,3,opt,name=mystr,proto3,oneof" json:"mystr,omitempty"`
	OutputsToGenerate []*FlowOutput `protobuf:"bytes,4,rep,name=outputs_to_generate,json=outputsToGenerate,proto3" json:"outputs_to_generate,omitempty"`
}

func (x *GRPCTestFlow_V1) Reset() {
	*x = GRPCTestFlow_V1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_inputs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GRPCTestFlow_V1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GRPCTestFlow_V1) ProtoMessage() {}

func (x *GRPCTestFlow_V1) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_inputs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GRPCTestFlow_V1.ProtoReflect.Descriptor instead.
func (*GRPCTestFlow_V1) Descriptor() ([]byte, []int) {
	return file_metaflow_inputs_proto_rawDescGZIP(), []int{5}
}

func (x *GRPCTestFlow_V1) GetMybool() bool {
	if x != nil && x.Mybool != nil {
		return *x.Mybool
	}
	return false
}

func (x *GRPCTestFlow_V1) GetMyint() int64 {
	if x != nil && x.Myint != nil {
		return *x.Myint
	}
	return 0
}

func (x *GRPCTestFlow_V1) GetMystr() string {
	if x != nil && x.Mystr != nil {
		return *x.Mystr
	}
	return ""
}

func (x *GRPCTestFlow_V1) GetOutputsToGenerate() []*FlowOutput {
	if x != nil {
		return x.OutputsToGenerate
	}
	return nil
}

var File_metaflow_inputs_proto protoreflect.FileDescriptor

var file_metaflow_inputs_proto_rawDesc = []byte{
	0x0a, 0x15, 0x6d, 0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6d, 0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f,
	0x77, 0x2f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xfd, 0x03, 0x0a, 0x18, 0x55, 0x57, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x5f, 0x76, 0x31, 0x12, 0x28, 0x0a, 0x0d,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0a, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03,
	0x74, 0x73, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x03, 0x74, 0x73, 0x70,
	0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0b, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x04, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x2c,
	0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x06, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x62, 0x61, 0x63, 0x6b, 0x66, 0x69, 0x6c, 0x6c,
	0x5f, 0x72, 0x75, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x07, 0x52, 0x0b, 0x62, 0x61,
	0x63, 0x6b, 0x66, 0x69, 0x6c, 0x6c, 0x52, 0x75, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x06,
	0x0a, 0x04, 0x5f, 0x74, 0x73, 0x70, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x0f, 0x0a,
	0x0d, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x66, 0x69, 0x6c, 0x6c, 0x5f, 0x72, 0x75, 0x6e, 0x22,
	0xd7, 0x01, 0x0a, 0x0b, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x5f, 0x76, 0x30, 0x12,
	0x28, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01,
	0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x15, 0x0a, 0x03, 0x74, 0x73, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x03,
	0x74, 0x73, 0x70, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0b,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64,
	0x42, 0x06, 0x0a, 0x04, 0x5f, 0x74, 0x73, 0x70, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0xc4, 0x02, 0x0a, 0x13, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x5f, 0x76,
	0x30, 0x12, 0x28, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x70,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x01, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x15, 0x0a, 0x03, 0x74, 0x73, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02,
	0x52, 0x03, 0x74, 0x73, 0x70, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03,
	0x52, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x28, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x50,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x74, 0x73, 0x70, 0x42, 0x0f, 0x0a,
	0x0d, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x22, 0xbc, 0x02, 0x0a, 0x19, 0x55, 0x57, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x5f, 0x76, 0x31, 0x12, 0x2a,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x15, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x13, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x11, 0x79, 0x65, 0x61, 0x72, 0x73, 0x5f, 0x69, 0x6e, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x48, 0x02,
	0x52, 0x0f, 0x79, 0x65, 0x61, 0x72, 0x73, 0x49, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x11, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x48,
	0x03, 0x52, 0x10, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x69, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x73, 0x5f, 0x69, 0x6e,
	0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x22,
	0xc9, 0x01, 0x0a, 0x1d, 0x41, 0x70, 0x70, 0x65, 0x74, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x74, 0x65,
	0x49, 0x6e, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x5f, 0x76,
	0x31, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x12, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x5f, 0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x02, 0x52, 0x10, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x50, 0x75, 0x6c,
	0x6c, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x63, 0x5f, 0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xc1, 0x01, 0x0a, 0x10,
	0x47, 0x52, 0x50, 0x43, 0x54, 0x65, 0x73, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x5f, 0x76, 0x31,
	0x12, 0x1b, 0x0a, 0x06, 0x6d, 0x79, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x06, 0x6d, 0x79, 0x62, 0x6f, 0x6f, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a,
	0x05, 0x6d, 0x79, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x05,
	0x6d, 0x79, 0x69, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x6d, 0x79, 0x73, 0x74,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x05, 0x6d, 0x79, 0x73, 0x74, 0x72,
	0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x13, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x5f, 0x74,
	0x6f, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x11, 0x6f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x54, 0x6f, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6d, 0x79, 0x62, 0x6f, 0x6f, 0x6c, 0x42, 0x08, 0x0a, 0x06, 0x5f,
	0x6d, 0x79, 0x69, 0x6e, 0x74, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6d, 0x79, 0x73, 0x74, 0x72, 0x2a,
	0xee, 0x01, 0x0a, 0x04, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x6c, 0x6f, 0x77,
	0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x6c,
	0x6f, 0x77, 0x5f, 0x55, 0x57, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x5f, 0x76, 0x31, 0x10, 0x03, 0x12, 0x14, 0x0a,
	0x10, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x5f, 0x76,
	0x30, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x63, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x5f, 0x76, 0x30, 0x10,
	0x06, 0x12, 0x22, 0x0a, 0x1e, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x55, 0x57, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6c, 0x6f, 0x77, 0x5f,
	0x5f, 0x76, 0x31, 0x10, 0x08, 0x12, 0x26, 0x0a, 0x22, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x41, 0x70,
	0x70, 0x65, 0x74, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x5f, 0x76, 0x31, 0x10, 0x09, 0x12, 0x19, 0x0a,
	0x15, 0x46, 0x6c, 0x6f, 0x77, 0x5f, 0x47, 0x52, 0x50, 0x43, 0x54, 0x65, 0x73, 0x74, 0x46, 0x6c,
	0x6f, 0x77, 0x5f, 0x5f, 0x76, 0x31, 0x10, 0x0a, 0x22, 0x04, 0x08, 0x01, 0x10, 0x01, 0x22, 0x04,
	0x08, 0x02, 0x10, 0x02, 0x22, 0x04, 0x08, 0x04, 0x10, 0x04, 0x22, 0x04, 0x08, 0x07, 0x10, 0x07,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_metaflow_inputs_proto_rawDescOnce sync.Once
	file_metaflow_inputs_proto_rawDescData = file_metaflow_inputs_proto_rawDesc
)

func file_metaflow_inputs_proto_rawDescGZIP() []byte {
	file_metaflow_inputs_proto_rawDescOnce.Do(func() {
		file_metaflow_inputs_proto_rawDescData = protoimpl.X.CompressGZIP(file_metaflow_inputs_proto_rawDescData)
	})
	return file_metaflow_inputs_proto_rawDescData
}

var file_metaflow_inputs_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_metaflow_inputs_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_metaflow_inputs_proto_goTypes = []interface{}{
	(Flow)(0),                            // 0: Flow
	(*UWTelematicsDataFlow_V1)(nil),      // 1: UWTelematicsDataFlow__v1
	(*Billing_V0)(nil),                   // 2: Billing__v0
	(*RiskScoringFlow_V0)(nil),           // 3: RiskScoringFlow__v0
	(*UWApplicationDataFlow_V1)(nil),     // 4: UWApplicationDataFlow__v1
	(*AppetiteLiteInferenceFlow_V1)(nil), // 5: AppetiteLiteInferenceFlow__v1
	(*GRPCTestFlow_V1)(nil),              // 6: GRPCTestFlow__v1
	(*FlowOutput)(nil),                   // 7: FlowOutput
}
var file_metaflow_inputs_proto_depIdxs = []int32{
	7, // 0: GRPCTestFlow__v1.outputs_to_generate:type_name -> FlowOutput
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_metaflow_inputs_proto_init() }
func file_metaflow_inputs_proto_init() {
	if File_metaflow_inputs_proto != nil {
		return
	}
	file_metaflow_outputs_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_metaflow_inputs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UWTelematicsDataFlow_V1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_inputs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Billing_V0); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_inputs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskScoringFlow_V0); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_inputs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UWApplicationDataFlow_V1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_inputs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppetiteLiteInferenceFlow_V1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_inputs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GRPCTestFlow_V1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_metaflow_inputs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_metaflow_inputs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_metaflow_inputs_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_metaflow_inputs_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_metaflow_inputs_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_metaflow_inputs_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_metaflow_inputs_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_metaflow_inputs_proto_goTypes,
		DependencyIndexes: file_metaflow_inputs_proto_depIdxs,
		EnumInfos:         file_metaflow_inputs_proto_enumTypes,
		MessageInfos:      file_metaflow_inputs_proto_msgTypes,
	}.Build()
	File_metaflow_inputs_proto = out.File
	file_metaflow_inputs_proto_rawDesc = nil
	file_metaflow_inputs_proto_goTypes = nil
	file_metaflow_inputs_proto_depIdxs = nil
}
