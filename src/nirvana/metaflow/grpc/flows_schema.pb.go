// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: metaflow/flows_schema.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FlowInputType int32

const (
	FlowInputType_str     FlowInputType = 0
	FlowInputType_int     FlowInputType = 1
	FlowInputType_list    FlowInputType = 2
	FlowInputType_float   FlowInputType = 3
	FlowInputType_bool    FlowInputType = 4
	FlowInputType_s3path  FlowInputType = 5
	FlowInputType_outputs FlowInputType = 6
)

// Enum value maps for FlowInputType.
var (
	FlowInputType_name = map[int32]string{
		0: "str",
		1: "int",
		2: "list",
		3: "float",
		4: "bool",
		5: "s3path",
		6: "outputs",
	}
	FlowInputType_value = map[string]int32{
		"str":     0,
		"int":     1,
		"list":    2,
		"float":   3,
		"bool":    4,
		"s3path":  5,
		"outputs": 6,
	}
)

func (x FlowInputType) Enum() *FlowInputType {
	p := new(FlowInputType)
	*p = x
	return p
}

func (x FlowInputType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FlowInputType) Descriptor() protoreflect.EnumDescriptor {
	return file_metaflow_flows_schema_proto_enumTypes[0].Descriptor()
}

func (FlowInputType) Type() protoreflect.EnumType {
	return &file_metaflow_flows_schema_proto_enumTypes[0]
}

func (x FlowInputType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FlowInputType.Descriptor instead.
func (FlowInputType) EnumDescriptor() ([]byte, []int) {
	return file_metaflow_flows_schema_proto_rawDescGZIP(), []int{0}
}

type RollingWindowUnit int32

const (
	RollingWindowUnit_day   RollingWindowUnit = 0
	RollingWindowUnit_week  RollingWindowUnit = 1
	RollingWindowUnit_month RollingWindowUnit = 2
	RollingWindowUnit_year  RollingWindowUnit = 3
)

// Enum value maps for RollingWindowUnit.
var (
	RollingWindowUnit_name = map[int32]string{
		0: "day",
		1: "week",
		2: "month",
		3: "year",
	}
	RollingWindowUnit_value = map[string]int32{
		"day":   0,
		"week":  1,
		"month": 2,
		"year":  3,
	}
)

func (x RollingWindowUnit) Enum() *RollingWindowUnit {
	p := new(RollingWindowUnit)
	*p = x
	return p
}

func (x RollingWindowUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RollingWindowUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_metaflow_flows_schema_proto_enumTypes[1].Descriptor()
}

func (RollingWindowUnit) Type() protoreflect.EnumType {
	return &file_metaflow_flows_schema_proto_enumTypes[1]
}

func (x RollingWindowUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RollingWindowUnit.Descriptor instead.
func (RollingWindowUnit) EnumDescriptor() ([]byte, []int) {
	return file_metaflow_flows_schema_proto_rawDescGZIP(), []int{1}
}

type FlowsSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Flows     []*FlowDetails  `protobuf:"bytes,1,rep,name=flows,proto3" json:"flows,omitempty"`
	InputSets []*FlowInputSet `protobuf:"bytes,2,rep,name=input_sets,json=inputSets,proto3" json:"input_sets,omitempty"`
	Outputs   []*Output       `protobuf:"bytes,3,rep,name=outputs,proto3" json:"outputs,omitempty"`
}

func (x *FlowsSpec) Reset() {
	*x = FlowsSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_flows_schema_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowsSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowsSpec) ProtoMessage() {}

func (x *FlowsSpec) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_flows_schema_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowsSpec.ProtoReflect.Descriptor instead.
func (*FlowsSpec) Descriptor() ([]byte, []int) {
	return file_metaflow_flows_schema_proto_rawDescGZIP(), []int{0}
}

func (x *FlowsSpec) GetFlows() []*FlowDetails {
	if x != nil {
		return x.Flows
	}
	return nil
}

func (x *FlowsSpec) GetInputSets() []*FlowInputSet {
	if x != nil {
		return x.InputSets
	}
	return nil
}

func (x *FlowsSpec) GetOutputs() []*Output {
	if x != nil {
		return x.Outputs
	}
	return nil
}

type FlowDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FlowName string                `protobuf:"bytes,1,opt,name=flow_name,json=flowName,proto3" json:"flow_name,omitempty"`
	Versions []*FlowVersionDetails `protobuf:"bytes,2,rep,name=versions,proto3" json:"versions,omitempty"`
}

func (x *FlowDetails) Reset() {
	*x = FlowDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_flows_schema_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowDetails) ProtoMessage() {}

func (x *FlowDetails) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_flows_schema_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowDetails.ProtoReflect.Descriptor instead.
func (*FlowDetails) Descriptor() ([]byte, []int) {
	return file_metaflow_flows_schema_proto_rawDescGZIP(), []int{1}
}

func (x *FlowDetails) GetFlowName() string {
	if x != nil {
		return x.FlowName
	}
	return ""
}

func (x *FlowDetails) GetVersions() []*FlowVersionDetails {
	if x != nil {
		return x.Versions
	}
	return nil
}

type FlowVersionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version          int32              `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	Source           string             `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	Inputs           []*FlowInput       `protobuf:"bytes,3,rep,name=inputs,proto3" json:"inputs,omitempty"`
	SupportedOutputs []*SupportedOutput `protobuf:"bytes,4,rep,name=supported_outputs,json=supportedOutputs,proto3" json:"supported_outputs,omitempty"`
}

func (x *FlowVersionDetails) Reset() {
	*x = FlowVersionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_flows_schema_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowVersionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowVersionDetails) ProtoMessage() {}

func (x *FlowVersionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_flows_schema_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowVersionDetails.ProtoReflect.Descriptor instead.
func (*FlowVersionDetails) Descriptor() ([]byte, []int) {
	return file_metaflow_flows_schema_proto_rawDescGZIP(), []int{2}
}

func (x *FlowVersionDetails) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *FlowVersionDetails) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *FlowVersionDetails) GetInputs() []*FlowInput {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *FlowVersionDetails) GetSupportedOutputs() []*SupportedOutput {
	if x != nil {
		return x.SupportedOutputs
	}
	return nil
}

type FlowInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string        `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type     FlowInputType `protobuf:"varint,2,opt,name=type,proto3,enum=FlowInputType" json:"type,omitempty"`
	Required bool          `protobuf:"varint,3,opt,name=required,proto3" json:"required,omitempty"`
	Inherits string        `protobuf:"bytes,4,opt,name=inherits,proto3" json:"inherits,omitempty"`
}

func (x *FlowInput) Reset() {
	*x = FlowInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_flows_schema_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowInput) ProtoMessage() {}

func (x *FlowInput) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_flows_schema_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowInput.ProtoReflect.Descriptor instead.
func (*FlowInput) Descriptor() ([]byte, []int) {
	return file_metaflow_flows_schema_proto_rawDescGZIP(), []int{3}
}

func (x *FlowInput) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FlowInput) GetType() FlowInputType {
	if x != nil {
		return x.Type
	}
	return FlowInputType_str
}

func (x *FlowInput) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *FlowInput) GetInherits() string {
	if x != nil {
		return x.Inherits
	}
	return ""
}

type FlowInputSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string       `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Inputs []*FlowInput `protobuf:"bytes,2,rep,name=inputs,proto3" json:"inputs,omitempty"`
}

func (x *FlowInputSet) Reset() {
	*x = FlowInputSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_flows_schema_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowInputSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowInputSet) ProtoMessage() {}

func (x *FlowInputSet) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_flows_schema_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowInputSet.ProtoReflect.Descriptor instead.
func (*FlowInputSet) Descriptor() ([]byte, []int) {
	return file_metaflow_flows_schema_proto_rawDescGZIP(), []int{4}
}

func (x *FlowInputSet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FlowInputSet) GetInputs() []*FlowInput {
	if x != nil {
		return x.Inputs
	}
	return nil
}

type Output struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name              string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	SupportedVersions []int32            `protobuf:"varint,2,rep,packed,name=supported_versions,json=supportedVersions,proto3" json:"supported_versions,omitempty"`
	RollingWindowSize *RollingWindowSpec `protobuf:"bytes,3,opt,name=rolling_window_size,json=rollingWindowSize,proto3" json:"rolling_window_size,omitempty"`
}

func (x *Output) Reset() {
	*x = Output{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_flows_schema_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Output) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Output) ProtoMessage() {}

func (x *Output) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_flows_schema_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Output.ProtoReflect.Descriptor instead.
func (*Output) Descriptor() ([]byte, []int) {
	return file_metaflow_flows_schema_proto_rawDescGZIP(), []int{5}
}

func (x *Output) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Output) GetSupportedVersions() []int32 {
	if x != nil {
		return x.SupportedVersions
	}
	return nil
}

func (x *Output) GetRollingWindowSize() *RollingWindowSpec {
	if x != nil {
		return x.RollingWindowSize
	}
	return nil
}

type SupportedOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Versions []int32 `protobuf:"varint,2,rep,packed,name=versions,proto3" json:"versions,omitempty"`
}

func (x *SupportedOutput) Reset() {
	*x = SupportedOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_flows_schema_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SupportedOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportedOutput) ProtoMessage() {}

func (x *SupportedOutput) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_flows_schema_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportedOutput.ProtoReflect.Descriptor instead.
func (*SupportedOutput) Descriptor() ([]byte, []int) {
	return file_metaflow_flows_schema_proto_rawDescGZIP(), []int{6}
}

func (x *SupportedOutput) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SupportedOutput) GetVersions() []int32 {
	if x != nil {
		return x.Versions
	}
	return nil
}

type RollingWindowSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Length   int32             `protobuf:"varint,1,opt,name=length,proto3" json:"length,omitempty"`
	StepSize int32             `protobuf:"varint,2,opt,name=step_size,json=stepSize,proto3" json:"step_size,omitempty"`
	Unit     RollingWindowUnit `protobuf:"varint,3,opt,name=unit,proto3,enum=RollingWindowUnit" json:"unit,omitempty"`
}

func (x *RollingWindowSpec) Reset() {
	*x = RollingWindowSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_flows_schema_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollingWindowSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollingWindowSpec) ProtoMessage() {}

func (x *RollingWindowSpec) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_flows_schema_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollingWindowSpec.ProtoReflect.Descriptor instead.
func (*RollingWindowSpec) Descriptor() ([]byte, []int) {
	return file_metaflow_flows_schema_proto_rawDescGZIP(), []int{7}
}

func (x *RollingWindowSpec) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *RollingWindowSpec) GetStepSize() int32 {
	if x != nil {
		return x.StepSize
	}
	return 0
}

func (x *RollingWindowSpec) GetUnit() RollingWindowUnit {
	if x != nil {
		return x.Unit
	}
	return RollingWindowUnit_day
}

var File_metaflow_flows_schema_proto protoreflect.FileDescriptor

var file_metaflow_flows_schema_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x6d, 0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x66, 0x6c, 0x6f, 0x77, 0x73,
	0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x80, 0x01,
	0x0a, 0x09, 0x46, 0x6c, 0x6f, 0x77, 0x73, 0x53, 0x70, 0x65, 0x63, 0x12, 0x22, 0x0a, 0x05, 0x66,
	0x6c, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x46, 0x6c, 0x6f,
	0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x05, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x12,
	0x2c, 0x0a, 0x0a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53,
	0x65, 0x74, 0x52, 0x09, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x65, 0x74, 0x73, 0x12, 0x21, 0x0a,
	0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x07,
	0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73,
	0x22, 0x5b, 0x0a, 0x0b, 0x46, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x08,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa9, 0x01,
	0x0a, 0x12, 0x46, 0x6c, 0x6f, 0x77, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x11, 0x73, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x10, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x22, 0x7b, 0x0a, 0x09, 0x46, 0x6c, 0x6f,
	0x77, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e,
	0x68, 0x65, 0x72, 0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6e,
	0x68, 0x65, 0x72, 0x69, 0x74, 0x73, 0x22, 0x46, 0x0a, 0x0c, 0x46, 0x6c, 0x6f, 0x77, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x53, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x06, 0x69, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x46, 0x6c, 0x6f,
	0x77, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x22, 0x8f,
	0x01, 0x0a, 0x06, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a,
	0x12, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x11, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x42, 0x0a, 0x13,
	0x72, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x52, 0x6f, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x70, 0x65, 0x63, 0x52, 0x11, 0x72,
	0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x69, 0x7a, 0x65,
	0x22, 0x41, 0x0a, 0x0f, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x70, 0x0a, 0x11, 0x52, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x57, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x53, 0x70, 0x65, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x74, 0x65, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x26, 0x0a,
	0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x52, 0x6f,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x55, 0x6e, 0x69, 0x74, 0x52,
	0x04, 0x75, 0x6e, 0x69, 0x74, 0x2a, 0x59, 0x0a, 0x0d, 0x46, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x73, 0x74, 0x72, 0x10, 0x00, 0x12,
	0x07, 0x0a, 0x03, 0x69, 0x6e, 0x74, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x10, 0x03, 0x12, 0x08, 0x0a,
	0x04, 0x62, 0x6f, 0x6f, 0x6c, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x73, 0x33, 0x70, 0x61, 0x74,
	0x68, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x10, 0x06,
	0x2a, 0x3b, 0x0a, 0x11, 0x52, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x57, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x07, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x10, 0x00, 0x12, 0x08,
	0x0a, 0x04, 0x77, 0x65, 0x65, 0x6b, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74,
	0x68, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x10, 0x03, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_metaflow_flows_schema_proto_rawDescOnce sync.Once
	file_metaflow_flows_schema_proto_rawDescData = file_metaflow_flows_schema_proto_rawDesc
)

func file_metaflow_flows_schema_proto_rawDescGZIP() []byte {
	file_metaflow_flows_schema_proto_rawDescOnce.Do(func() {
		file_metaflow_flows_schema_proto_rawDescData = protoimpl.X.CompressGZIP(file_metaflow_flows_schema_proto_rawDescData)
	})
	return file_metaflow_flows_schema_proto_rawDescData
}

var file_metaflow_flows_schema_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_metaflow_flows_schema_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_metaflow_flows_schema_proto_goTypes = []interface{}{
	(FlowInputType)(0),         // 0: FlowInputType
	(RollingWindowUnit)(0),     // 1: RollingWindowUnit
	(*FlowsSpec)(nil),          // 2: FlowsSpec
	(*FlowDetails)(nil),        // 3: FlowDetails
	(*FlowVersionDetails)(nil), // 4: FlowVersionDetails
	(*FlowInput)(nil),          // 5: FlowInput
	(*FlowInputSet)(nil),       // 6: FlowInputSet
	(*Output)(nil),             // 7: Output
	(*SupportedOutput)(nil),    // 8: SupportedOutput
	(*RollingWindowSpec)(nil),  // 9: RollingWindowSpec
}
var file_metaflow_flows_schema_proto_depIdxs = []int32{
	3,  // 0: FlowsSpec.flows:type_name -> FlowDetails
	6,  // 1: FlowsSpec.input_sets:type_name -> FlowInputSet
	7,  // 2: FlowsSpec.outputs:type_name -> Output
	4,  // 3: FlowDetails.versions:type_name -> FlowVersionDetails
	5,  // 4: FlowVersionDetails.inputs:type_name -> FlowInput
	8,  // 5: FlowVersionDetails.supported_outputs:type_name -> SupportedOutput
	0,  // 6: FlowInput.type:type_name -> FlowInputType
	5,  // 7: FlowInputSet.inputs:type_name -> FlowInput
	9,  // 8: Output.rolling_window_size:type_name -> RollingWindowSpec
	1,  // 9: RollingWindowSpec.unit:type_name -> RollingWindowUnit
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_metaflow_flows_schema_proto_init() }
func file_metaflow_flows_schema_proto_init() {
	if File_metaflow_flows_schema_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_metaflow_flows_schema_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowsSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_flows_schema_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_flows_schema_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowVersionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_flows_schema_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_flows_schema_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowInputSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_flows_schema_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Output); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_flows_schema_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SupportedOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_metaflow_flows_schema_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollingWindowSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_metaflow_flows_schema_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_metaflow_flows_schema_proto_goTypes,
		DependencyIndexes: file_metaflow_flows_schema_proto_depIdxs,
		EnumInfos:         file_metaflow_flows_schema_proto_enumTypes,
		MessageInfos:      file_metaflow_flows_schema_proto_msgTypes,
	}.Build()
	File_metaflow_flows_schema_proto = out.File
	file_metaflow_flows_schema_proto_rawDesc = nil
	file_metaflow_flows_schema_proto_goTypes = nil
	file_metaflow_flows_schema_proto_depIdxs = nil
}
