// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: metaflow/outputs.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OutputType int32

const (
	OutputType_Output_Unknown OutputType = 0
	OutputType_GRPCTestOutput OutputType = 1
)

// Enum value maps for OutputType.
var (
	OutputType_name = map[int32]string{
		0: "Output_Unknown",
		1: "GRPCTestOutput",
	}
	OutputType_value = map[string]int32{
		"Output_Unknown": 0,
		"GRPCTestOutput": 1,
	}
)

func (x OutputType) Enum() *OutputType {
	p := new(OutputType)
	*p = x
	return p
}

func (x OutputType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OutputType) Descriptor() protoreflect.EnumDescriptor {
	return file_metaflow_outputs_proto_enumTypes[0].Descriptor()
}

func (OutputType) Type() protoreflect.EnumType {
	return &file_metaflow_outputs_proto_enumTypes[0]
}

func (x OutputType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OutputType.Descriptor instead.
func (OutputType) EnumDescriptor() ([]byte, []int) {
	return file_metaflow_outputs_proto_rawDescGZIP(), []int{0}
}

type FlowOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                   OutputType             `protobuf:"varint,1,opt,name=type,proto3,enum=OutputType" json:"type,omitempty"`
	Version                int32                  `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	RollingWindowBboxStart *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=rolling_window_bbox_start,json=rollingWindowBboxStart,proto3" json:"rolling_window_bbox_start,omitempty"`
	RollingWindowBboxEnd   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=rolling_window_bbox_end,json=rollingWindowBboxEnd,proto3" json:"rolling_window_bbox_end,omitempty"`
}

func (x *FlowOutput) Reset() {
	*x = FlowOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_metaflow_outputs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FlowOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlowOutput) ProtoMessage() {}

func (x *FlowOutput) ProtoReflect() protoreflect.Message {
	mi := &file_metaflow_outputs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlowOutput.ProtoReflect.Descriptor instead.
func (*FlowOutput) Descriptor() ([]byte, []int) {
	return file_metaflow_outputs_proto_rawDescGZIP(), []int{0}
}

func (x *FlowOutput) GetType() OutputType {
	if x != nil {
		return x.Type
	}
	return OutputType_Output_Unknown
}

func (x *FlowOutput) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *FlowOutput) GetRollingWindowBboxStart() *timestamppb.Timestamp {
	if x != nil {
		return x.RollingWindowBboxStart
	}
	return nil
}

func (x *FlowOutput) GetRollingWindowBboxEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.RollingWindowBboxEnd
	}
	return nil
}

var File_metaflow_outputs_proto protoreflect.FileDescriptor

var file_metaflow_outputs_proto_rawDesc = []byte{
	0x0a, 0x16, 0x6d, 0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x6f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf1, 0x01, 0x0a, 0x0a, 0x46, 0x6c,
	0x6f, 0x77, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0b, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x19, 0x72, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x62, 0x62, 0x6f, 0x78, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x16, 0x72, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x57, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x42, 0x62, 0x6f, 0x78, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x51, 0x0a, 0x17, 0x72, 0x6f,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x62, 0x62, 0x6f,
	0x78, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x72, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x42, 0x62, 0x6f, 0x78, 0x45, 0x6e, 0x64, 0x2a, 0x34, 0x0a,
	0x0a, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x4f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12,
	0x12, 0x0a, 0x0e, 0x47, 0x52, 0x50, 0x43, 0x54, 0x65, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x10, 0x01, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_metaflow_outputs_proto_rawDescOnce sync.Once
	file_metaflow_outputs_proto_rawDescData = file_metaflow_outputs_proto_rawDesc
)

func file_metaflow_outputs_proto_rawDescGZIP() []byte {
	file_metaflow_outputs_proto_rawDescOnce.Do(func() {
		file_metaflow_outputs_proto_rawDescData = protoimpl.X.CompressGZIP(file_metaflow_outputs_proto_rawDescData)
	})
	return file_metaflow_outputs_proto_rawDescData
}

var file_metaflow_outputs_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_metaflow_outputs_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_metaflow_outputs_proto_goTypes = []interface{}{
	(OutputType)(0),               // 0: OutputType
	(*FlowOutput)(nil),            // 1: FlowOutput
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_metaflow_outputs_proto_depIdxs = []int32{
	0, // 0: FlowOutput.type:type_name -> OutputType
	2, // 1: FlowOutput.rolling_window_bbox_start:type_name -> google.protobuf.Timestamp
	2, // 2: FlowOutput.rolling_window_bbox_end:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_metaflow_outputs_proto_init() }
func file_metaflow_outputs_proto_init() {
	if File_metaflow_outputs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_metaflow_outputs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FlowOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_metaflow_outputs_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_metaflow_outputs_proto_goTypes,
		DependencyIndexes: file_metaflow_outputs_proto_depIdxs,
		EnumInfos:         file_metaflow_outputs_proto_enumTypes,
		MessageInfos:      file_metaflow_outputs_proto_msgTypes,
	}.Build()
	File_metaflow_outputs_proto = out.File
	file_metaflow_outputs_proto_rawDesc = nil
	file_metaflow_outputs_proto_goTypes = nil
	file_metaflow_outputs_proto_depIdxs = nil
}
