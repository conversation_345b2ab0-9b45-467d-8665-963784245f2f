// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_bundle/model/endorsement/endorsement.proto

package endorsement

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	proto "nirvanatech.com/nirvana/common-go/proto"
	endorsement1 "nirvanatech.com/nirvana/fleet/model/endorsement"
	charges "nirvanatech.com/nirvana/insurance-bundle/model/charges"
	proto1 "nirvanatech.com/nirvana/insurance-core/proto"
	endorsement "nirvanatech.com/nirvana/nonfleet/model/endorsement"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChangeContainer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Changes           []*Change                      `protobuf:"bytes,1,rep,name=changes,proto3" json:"changes,omitempty"`
	Charges           map[string]*charges.ChargeList `protobuf:"bytes,2,rep,name=charges,proto3" json:"charges,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	EffectiveInterval *proto.Interval                `protobuf:"bytes,3,opt,name=effectiveInterval,proto3" json:"effectiveInterval,omitempty"`
	Id                string                         `protobuf:"bytes,4,opt,name=id,proto3" json:"id,omitempty"`
	FormChanges       *FormChangeList                `protobuf:"bytes,5,opt,name=formChanges,proto3" json:"formChanges,omitempty"`
}

func (x *ChangeContainer) Reset() {
	*x = ChangeContainer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeContainer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeContainer) ProtoMessage() {}

func (x *ChangeContainer) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeContainer.ProtoReflect.Descriptor instead.
func (*ChangeContainer) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_endorsement_proto_rawDescGZIP(), []int{0}
}

func (x *ChangeContainer) GetChanges() []*Change {
	if x != nil {
		return x.Changes
	}
	return nil
}

func (x *ChangeContainer) GetCharges() map[string]*charges.ChargeList {
	if x != nil {
		return x.Charges
	}
	return nil
}

func (x *ChangeContainer) GetEffectiveInterval() *proto.Interval {
	if x != nil {
		return x.EffectiveInterval
	}
	return nil
}

func (x *ChangeContainer) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ChangeContainer) GetFormChanges() *FormChangeList {
	if x != nil {
		return x.FormChanges
	}
	return nil
}

type Change struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SequenceNumber int64       `protobuf:"varint,1,opt,name=sequenceNumber,proto3" json:"sequenceNumber,omitempty"`
	PolicyNumbers  []string    `protobuf:"bytes,2,rep,name=policyNumbers,proto3" json:"policyNumbers,omitempty"`
	Data           *ChangeData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *Change) Reset() {
	*x = Change{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Change) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Change) ProtoMessage() {}

func (x *Change) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Change.ProtoReflect.Descriptor instead.
func (*Change) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_endorsement_proto_rawDescGZIP(), []int{1}
}

func (x *Change) GetSequenceNumber() int64 {
	if x != nil {
		return x.SequenceNumber
	}
	return 0
}

func (x *Change) GetPolicyNumbers() []string {
	if x != nil {
		return x.PolicyNumbers
	}
	return nil
}

func (x *Change) GetData() *ChangeData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ChangeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Data:
	//
	//	*ChangeData_CoreChange
	//	*ChangeData_NonFleetChange
	//	*ChangeData_FleetChange
	Data isChangeData_Data `protobuf_oneof:"data"`
}

func (x *ChangeData) Reset() {
	*x = ChangeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeData) ProtoMessage() {}

func (x *ChangeData) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeData.ProtoReflect.Descriptor instead.
func (*ChangeData) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_endorsement_proto_rawDescGZIP(), []int{2}
}

func (m *ChangeData) GetData() isChangeData_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *ChangeData) GetCoreChange() *CoreChange {
	if x, ok := x.GetData().(*ChangeData_CoreChange); ok {
		return x.CoreChange
	}
	return nil
}

func (x *ChangeData) GetNonFleetChange() *endorsement.NonFleetChange {
	if x, ok := x.GetData().(*ChangeData_NonFleetChange); ok {
		return x.NonFleetChange
	}
	return nil
}

func (x *ChangeData) GetFleetChange() *endorsement1.FleetChange {
	if x, ok := x.GetData().(*ChangeData_FleetChange); ok {
		return x.FleetChange
	}
	return nil
}

type isChangeData_Data interface {
	isChangeData_Data()
}

type ChangeData_CoreChange struct {
	CoreChange *CoreChange `protobuf:"bytes,1,opt,name=coreChange,proto3,oneof"`
}

type ChangeData_NonFleetChange struct {
	NonFleetChange *endorsement.NonFleetChange `protobuf:"bytes,2,opt,name=NonFleetChange,proto3,oneof"`
}

type ChangeData_FleetChange struct {
	FleetChange *endorsement1.FleetChange `protobuf:"bytes,3,opt,name=fleetChange,proto3,oneof"`
}

func (*ChangeData_CoreChange) isChangeData_Data() {}

func (*ChangeData_NonFleetChange) isChangeData_Data() {}

func (*ChangeData_FleetChange) isChangeData_Data() {}

type FormChangeList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FormChanges []*FormChanges `protobuf:"bytes,1,rep,name=formChanges,proto3" json:"formChanges,omitempty"`
}

func (x *FormChangeList) Reset() {
	*x = FormChangeList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormChangeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormChangeList) ProtoMessage() {}

func (x *FormChangeList) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormChangeList.ProtoReflect.Descriptor instead.
func (*FormChangeList) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_endorsement_proto_rawDescGZIP(), []int{3}
}

func (x *FormChangeList) GetFormChanges() []*FormChanges {
	if x != nil {
		return x.FormChanges
	}
	return nil
}

type FormChanges struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AddPolicyForm map[string]*proto1.FormCore `protobuf:"bytes,1,rep,name=addPolicyForm,proto3" json:"addPolicyForm,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FormChanges) Reset() {
	*x = FormChanges{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormChanges) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormChanges) ProtoMessage() {}

func (x *FormChanges) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormChanges.ProtoReflect.Descriptor instead.
func (*FormChanges) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_endorsement_proto_rawDescGZIP(), []int{4}
}

func (x *FormChanges) GetAddPolicyForm() map[string]*proto1.FormCore {
	if x != nil {
		return x.AddPolicyForm
	}
	return nil
}

var File_insurance_bundle_model_endorsement_endorsement_proto protoreflect.FileDescriptor

var file_insurance_bundle_model_endorsement_endorsement_proto_rawDesc = []byte{
	0x0a, 0x34, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x6f, 0x72,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2c, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x2f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe5,
	0x02, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x12, 0x2d, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x73, 0x12, 0x43, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x11, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x52, 0x11, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x6d, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0b, 0x66, 0x6f, 0x72, 0x6d, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x1a, 0x4f, 0x0a, 0x0c, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x29, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x83, 0x01, 0x0a, 0x06, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12,
	0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xe3, 0x01, 0x0a,
	0x0a, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x6f, 0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f,
	0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x6f, 0x72, 0x65,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x4e, 0x0a, 0x0e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x4c, 0x0a, 0x0e, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x6d, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x52, 0x0b, 0x66, 0x6f, 0x72, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73,
	0x22, 0xbc, 0x01, 0x0a, 0x0b, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73,
	0x12, 0x51, 0x0a, 0x0d, 0x61, 0x64, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x46, 0x6f, 0x72,
	0x6d, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x73, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x46, 0x6f, 0x72, 0x6d, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x61, 0x64, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x46,
	0x6f, 0x72, 0x6d, 0x1a, 0x5a, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x46, 0x6f, 0x72, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x46, 0x6f, 0x72, 0x6d,
	0x43, 0x6f, 0x72, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42,
	0x3c, 0x5a, 0x3a, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x2d, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insurance_bundle_model_endorsement_endorsement_proto_rawDescOnce sync.Once
	file_insurance_bundle_model_endorsement_endorsement_proto_rawDescData = file_insurance_bundle_model_endorsement_endorsement_proto_rawDesc
)

func file_insurance_bundle_model_endorsement_endorsement_proto_rawDescGZIP() []byte {
	file_insurance_bundle_model_endorsement_endorsement_proto_rawDescOnce.Do(func() {
		file_insurance_bundle_model_endorsement_endorsement_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_bundle_model_endorsement_endorsement_proto_rawDescData)
	})
	return file_insurance_bundle_model_endorsement_endorsement_proto_rawDescData
}

var file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_insurance_bundle_model_endorsement_endorsement_proto_goTypes = []interface{}{
	(*ChangeContainer)(nil),            // 0: endorsement.ChangeContainer
	(*Change)(nil),                     // 1: endorsement.Change
	(*ChangeData)(nil),                 // 2: endorsement.ChangeData
	(*FormChangeList)(nil),             // 3: endorsement.FormChangeList
	(*FormChanges)(nil),                // 4: endorsement.FormChanges
	nil,                                // 5: endorsement.ChangeContainer.ChargesEntry
	nil,                                // 6: endorsement.FormChanges.AddPolicyFormEntry
	(*proto.Interval)(nil),             // 7: common.Interval
	(*CoreChange)(nil),                 // 8: endorsement.CoreChange
	(*endorsement.NonFleetChange)(nil), // 9: nonfleet_endorsement.NonFleetChange
	(*endorsement1.FleetChange)(nil),   // 10: fleet_endorsement.FleetChange
	(*charges.ChargeList)(nil),         // 11: charges.ChargeList
	(*proto1.FormCore)(nil),            // 12: insurance_core.FormCore
}
var file_insurance_bundle_model_endorsement_endorsement_proto_depIdxs = []int32{
	1,  // 0: endorsement.ChangeContainer.changes:type_name -> endorsement.Change
	5,  // 1: endorsement.ChangeContainer.charges:type_name -> endorsement.ChangeContainer.ChargesEntry
	7,  // 2: endorsement.ChangeContainer.effectiveInterval:type_name -> common.Interval
	3,  // 3: endorsement.ChangeContainer.formChanges:type_name -> endorsement.FormChangeList
	2,  // 4: endorsement.Change.data:type_name -> endorsement.ChangeData
	8,  // 5: endorsement.ChangeData.coreChange:type_name -> endorsement.CoreChange
	9,  // 6: endorsement.ChangeData.NonFleetChange:type_name -> nonfleet_endorsement.NonFleetChange
	10, // 7: endorsement.ChangeData.fleetChange:type_name -> fleet_endorsement.FleetChange
	4,  // 8: endorsement.FormChangeList.formChanges:type_name -> endorsement.FormChanges
	6,  // 9: endorsement.FormChanges.addPolicyForm:type_name -> endorsement.FormChanges.AddPolicyFormEntry
	11, // 10: endorsement.ChangeContainer.ChargesEntry.value:type_name -> charges.ChargeList
	12, // 11: endorsement.FormChanges.AddPolicyFormEntry.value:type_name -> insurance_core.FormCore
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_insurance_bundle_model_endorsement_endorsement_proto_init() }
func file_insurance_bundle_model_endorsement_endorsement_proto_init() {
	if File_insurance_bundle_model_endorsement_endorsement_proto != nil {
		return
	}
	file_insurance_bundle_model_endorsement_change_data_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeContainer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Change); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormChangeList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormChanges); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*ChangeData_CoreChange)(nil),
		(*ChangeData_NonFleetChange)(nil),
		(*ChangeData_FleetChange)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_bundle_model_endorsement_endorsement_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_bundle_model_endorsement_endorsement_proto_goTypes,
		DependencyIndexes: file_insurance_bundle_model_endorsement_endorsement_proto_depIdxs,
		MessageInfos:      file_insurance_bundle_model_endorsement_endorsement_proto_msgTypes,
	}.Build()
	File_insurance_bundle_model_endorsement_endorsement_proto = out.File
	file_insurance_bundle_model_endorsement_endorsement_proto_rawDesc = nil
	file_insurance_bundle_model_endorsement_endorsement_proto_goTypes = nil
	file_insurance_bundle_model_endorsement_endorsement_proto_depIdxs = nil
}
