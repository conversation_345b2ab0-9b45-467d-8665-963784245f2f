// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_bundle/model/endorsement/change_data.proto

package endorsement

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	endorsement "nirvanatech.com/nirvana/fleet/model/endorsement"
	model "nirvanatech.com/nirvana/insurance-bundle/model"
	proto "nirvanatech.com/nirvana/insurance-core/proto"
	endorsement1 "nirvanatech.com/nirvana/nonfleet/model/endorsement"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CoreChangeType int32

const (
	CoreChangeType_CoreChangeType_Invalid              CoreChangeType = 0
	CoreChangeType_CoreChangeType_Limit                CoreChangeType = 1
	CoreChangeType_CoreChangeType_Deductible           CoreChangeType = 2
	CoreChangeType_CoreChangeType_Coverage             CoreChangeType = 3
	CoreChangeType_CoreChangeType_SpecialRequest       CoreChangeType = 4
	CoreChangeType_CoreChangeType_Insured              CoreChangeType = 5
	CoreChangeType_CoreChangeType_SubCoverage          CoreChangeType = 6
	CoreChangeType_CoreChangeType_Clause               CoreChangeType = 7
	CoreChangeType_CoreChangeType_InterimCancellation  CoreChangeType = 8
	CoreChangeType_CoreChangeType_PrimaryInsuredUpdate CoreChangeType = 9
)

// Enum value maps for CoreChangeType.
var (
	CoreChangeType_name = map[int32]string{
		0: "CoreChangeType_Invalid",
		1: "CoreChangeType_Limit",
		2: "CoreChangeType_Deductible",
		3: "CoreChangeType_Coverage",
		4: "CoreChangeType_SpecialRequest",
		5: "CoreChangeType_Insured",
		6: "CoreChangeType_SubCoverage",
		7: "CoreChangeType_Clause",
		8: "CoreChangeType_InterimCancellation",
		9: "CoreChangeType_PrimaryInsuredUpdate",
	}
	CoreChangeType_value = map[string]int32{
		"CoreChangeType_Invalid":              0,
		"CoreChangeType_Limit":                1,
		"CoreChangeType_Deductible":           2,
		"CoreChangeType_Coverage":             3,
		"CoreChangeType_SpecialRequest":       4,
		"CoreChangeType_Insured":              5,
		"CoreChangeType_SubCoverage":          6,
		"CoreChangeType_Clause":               7,
		"CoreChangeType_InterimCancellation":  8,
		"CoreChangeType_PrimaryInsuredUpdate": 9,
	}
)

func (x CoreChangeType) Enum() *CoreChangeType {
	p := new(CoreChangeType)
	*p = x
	return p
}

func (x CoreChangeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CoreChangeType) Descriptor() protoreflect.EnumDescriptor {
	return file_insurance_bundle_model_endorsement_change_data_proto_enumTypes[0].Descriptor()
}

func (CoreChangeType) Type() protoreflect.EnumType {
	return &file_insurance_bundle_model_endorsement_change_data_proto_enumTypes[0]
}

func (x CoreChangeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CoreChangeType.Descriptor instead.
func (CoreChangeType) EnumDescriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{0}
}

type ChangeType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Data:
	//
	//	*ChangeType_CoreChangeType
	//	*ChangeType_FleetChangeType
	//	*ChangeType_NonFleetChangeType
	Data isChangeType_Data `protobuf_oneof:"data"`
}

func (x *ChangeType) Reset() {
	*x = ChangeType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeType) ProtoMessage() {}

func (x *ChangeType) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeType.ProtoReflect.Descriptor instead.
func (*ChangeType) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{0}
}

func (m *ChangeType) GetData() isChangeType_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *ChangeType) GetCoreChangeType() CoreChangeType {
	if x, ok := x.GetData().(*ChangeType_CoreChangeType); ok {
		return x.CoreChangeType
	}
	return CoreChangeType_CoreChangeType_Invalid
}

func (x *ChangeType) GetFleetChangeType() endorsement.FleetChangeType {
	if x, ok := x.GetData().(*ChangeType_FleetChangeType); ok {
		return x.FleetChangeType
	}
	return endorsement.FleetChangeType(0)
}

func (x *ChangeType) GetNonFleetChangeType() endorsement1.NonFleetChangeType {
	if x, ok := x.GetData().(*ChangeType_NonFleetChangeType); ok {
		return x.NonFleetChangeType
	}
	return endorsement1.NonFleetChangeType(0)
}

type isChangeType_Data interface {
	isChangeType_Data()
}

type ChangeType_CoreChangeType struct {
	CoreChangeType CoreChangeType `protobuf:"varint,1,opt,name=coreChangeType,proto3,enum=endorsement.CoreChangeType,oneof"`
}

type ChangeType_FleetChangeType struct {
	FleetChangeType endorsement.FleetChangeType `protobuf:"varint,2,opt,name=fleetChangeType,proto3,enum=fleet_endorsement.FleetChangeType,oneof"`
}

type ChangeType_NonFleetChangeType struct {
	NonFleetChangeType endorsement1.NonFleetChangeType `protobuf:"varint,3,opt,name=NonFleetChangeType,proto3,enum=nonfleet_endorsement.NonFleetChangeType,oneof"`
}

func (*ChangeType_CoreChangeType) isChangeType_Data() {}

func (*ChangeType_FleetChangeType) isChangeType_Data() {}

func (*ChangeType_NonFleetChangeType) isChangeType_Data() {}

type CoreChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoreChangeType CoreChangeType `protobuf:"varint,1,opt,name=coreChangeType,proto3,enum=endorsement.CoreChangeType" json:"coreChangeType,omitempty"`
	// Types that are assignable to Data:
	//
	//	*CoreChange_LimitChange
	//	*CoreChange_DeductibleChange
	//	*CoreChange_SpecialRequestChange
	//	*CoreChange_InsuredChange
	//	*CoreChange_SubCoverageChange
	//	*CoreChange_ClauseChange
	//	*CoreChange_InterimCancellationChange
	//	*CoreChange_PrimaryInsuredUpdate
	Data isCoreChange_Data `protobuf_oneof:"data"`
}

func (x *CoreChange) Reset() {
	*x = CoreChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoreChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoreChange) ProtoMessage() {}

func (x *CoreChange) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoreChange.ProtoReflect.Descriptor instead.
func (*CoreChange) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{1}
}

func (x *CoreChange) GetCoreChangeType() CoreChangeType {
	if x != nil {
		return x.CoreChangeType
	}
	return CoreChangeType_CoreChangeType_Invalid
}

func (m *CoreChange) GetData() isCoreChange_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *CoreChange) GetLimitChange() *LimitChange {
	if x, ok := x.GetData().(*CoreChange_LimitChange); ok {
		return x.LimitChange
	}
	return nil
}

func (x *CoreChange) GetDeductibleChange() *DeductibleChange {
	if x, ok := x.GetData().(*CoreChange_DeductibleChange); ok {
		return x.DeductibleChange
	}
	return nil
}

func (x *CoreChange) GetSpecialRequestChange() *SpecialRequestChange {
	if x, ok := x.GetData().(*CoreChange_SpecialRequestChange); ok {
		return x.SpecialRequestChange
	}
	return nil
}

func (x *CoreChange) GetInsuredChange() *InsuredChange {
	if x, ok := x.GetData().(*CoreChange_InsuredChange); ok {
		return x.InsuredChange
	}
	return nil
}

func (x *CoreChange) GetSubCoverageChange() *SubCoverageChange {
	if x, ok := x.GetData().(*CoreChange_SubCoverageChange); ok {
		return x.SubCoverageChange
	}
	return nil
}

func (x *CoreChange) GetClauseChange() *ClauseChange {
	if x, ok := x.GetData().(*CoreChange_ClauseChange); ok {
		return x.ClauseChange
	}
	return nil
}

func (x *CoreChange) GetInterimCancellationChange() *InterimCancellationChange {
	if x, ok := x.GetData().(*CoreChange_InterimCancellationChange); ok {
		return x.InterimCancellationChange
	}
	return nil
}

func (x *CoreChange) GetPrimaryInsuredUpdate() *PrimaryInsuredUpdate {
	if x, ok := x.GetData().(*CoreChange_PrimaryInsuredUpdate); ok {
		return x.PrimaryInsuredUpdate
	}
	return nil
}

type isCoreChange_Data interface {
	isCoreChange_Data()
}

type CoreChange_LimitChange struct {
	LimitChange *LimitChange `protobuf:"bytes,2,opt,name=limitChange,proto3,oneof"`
}

type CoreChange_DeductibleChange struct {
	DeductibleChange *DeductibleChange `protobuf:"bytes,3,opt,name=deductibleChange,proto3,oneof"`
}

type CoreChange_SpecialRequestChange struct {
	SpecialRequestChange *SpecialRequestChange `protobuf:"bytes,4,opt,name=specialRequestChange,proto3,oneof"`
}

type CoreChange_InsuredChange struct {
	InsuredChange *InsuredChange `protobuf:"bytes,5,opt,name=insuredChange,proto3,oneof"`
}

type CoreChange_SubCoverageChange struct {
	SubCoverageChange *SubCoverageChange `protobuf:"bytes,6,opt,name=subCoverageChange,proto3,oneof"`
}

type CoreChange_ClauseChange struct {
	ClauseChange *ClauseChange `protobuf:"bytes,7,opt,name=clauseChange,proto3,oneof"`
}

type CoreChange_InterimCancellationChange struct {
	InterimCancellationChange *InterimCancellationChange `protobuf:"bytes,8,opt,name=interimCancellationChange,proto3,oneof"`
}

type CoreChange_PrimaryInsuredUpdate struct {
	PrimaryInsuredUpdate *PrimaryInsuredUpdate `protobuf:"bytes,9,opt,name=primaryInsuredUpdate,proto3,oneof"`
}

func (*CoreChange_LimitChange) isCoreChange_Data() {}

func (*CoreChange_DeductibleChange) isCoreChange_Data() {}

func (*CoreChange_SpecialRequestChange) isCoreChange_Data() {}

func (*CoreChange_InsuredChange) isCoreChange_Data() {}

func (*CoreChange_SubCoverageChange) isCoreChange_Data() {}

func (*CoreChange_ClauseChange) isCoreChange_Data() {}

func (*CoreChange_InterimCancellationChange) isCoreChange_Data() {}

func (*CoreChange_PrimaryInsuredUpdate) isCoreChange_Data() {}

type LimitChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Add    []*model.Limit `protobuf:"bytes,1,rep,name=add,proto3" json:"add,omitempty"`
	Remove []string       `protobuf:"bytes,2,rep,name=remove,proto3" json:"remove,omitempty"`
	Update []*model.Limit `protobuf:"bytes,3,rep,name=update,proto3" json:"update,omitempty"`
}

func (x *LimitChange) Reset() {
	*x = LimitChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitChange) ProtoMessage() {}

func (x *LimitChange) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitChange.ProtoReflect.Descriptor instead.
func (*LimitChange) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{2}
}

func (x *LimitChange) GetAdd() []*model.Limit {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *LimitChange) GetRemove() []string {
	if x != nil {
		return x.Remove
	}
	return nil
}

func (x *LimitChange) GetUpdate() []*model.Limit {
	if x != nil {
		return x.Update
	}
	return nil
}

type DeductibleKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubCoverageIds []string `protobuf:"bytes,1,rep,name=subCoverageIds,proto3" json:"subCoverageIds,omitempty"`
}

func (x *DeductibleKey) Reset() {
	*x = DeductibleKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeductibleKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeductibleKey) ProtoMessage() {}

func (x *DeductibleKey) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeductibleKey.ProtoReflect.Descriptor instead.
func (*DeductibleKey) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{3}
}

func (x *DeductibleKey) GetSubCoverageIds() []string {
	if x != nil {
		return x.SubCoverageIds
	}
	return nil
}

type DeductibleChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Add    []*model.Deductible `protobuf:"bytes,1,rep,name=add,proto3" json:"add,omitempty"`
	Remove []*DeductibleKey    `protobuf:"bytes,2,rep,name=remove,proto3" json:"remove,omitempty"`
	Update []*model.Deductible `protobuf:"bytes,3,rep,name=update,proto3" json:"update,omitempty"`
}

func (x *DeductibleChange) Reset() {
	*x = DeductibleChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeductibleChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeductibleChange) ProtoMessage() {}

func (x *DeductibleChange) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeductibleChange.ProtoReflect.Descriptor instead.
func (*DeductibleChange) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{4}
}

func (x *DeductibleChange) GetAdd() []*model.Deductible {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *DeductibleChange) GetRemove() []*DeductibleKey {
	if x != nil {
		return x.Remove
	}
	return nil
}

func (x *DeductibleChange) GetUpdate() []*model.Deductible {
	if x != nil {
		return x.Update
	}
	return nil
}

type SpecialRequestChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Note string `protobuf:"bytes,1,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *SpecialRequestChange) Reset() {
	*x = SpecialRequestChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpecialRequestChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecialRequestChange) ProtoMessage() {}

func (x *SpecialRequestChange) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecialRequestChange.ProtoReflect.Descriptor instead.
func (*SpecialRequestChange) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{5}
}

func (x *SpecialRequestChange) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

type InsuredChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuredType proto.InsuredType `protobuf:"varint,1,opt,name=insuredType,proto3,enum=insurance_core.InsuredType" json:"insuredType,omitempty"`
	Add         []*proto.Insured  `protobuf:"bytes,2,rep,name=add,proto3" json:"add,omitempty"`
	Remove      []string          `protobuf:"bytes,3,rep,name=remove,proto3" json:"remove,omitempty"`
	Update      []*proto.Insured  `protobuf:"bytes,4,rep,name=update,proto3" json:"update,omitempty"`
}

func (x *InsuredChange) Reset() {
	*x = InsuredChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsuredChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsuredChange) ProtoMessage() {}

func (x *InsuredChange) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsuredChange.ProtoReflect.Descriptor instead.
func (*InsuredChange) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{6}
}

func (x *InsuredChange) GetInsuredType() proto.InsuredType {
	if x != nil {
		return x.InsuredType
	}
	return proto.InsuredType(0)
}

func (x *InsuredChange) GetAdd() []*proto.Insured {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *InsuredChange) GetRemove() []string {
	if x != nil {
		return x.Remove
	}
	return nil
}

func (x *InsuredChange) GetUpdate() []*proto.Insured {
	if x != nil {
		return x.Update
	}
	return nil
}

type PrimaryInsuredUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Update *proto.Insured `protobuf:"bytes,1,opt,name=update,proto3" json:"update,omitempty"`
}

func (x *PrimaryInsuredUpdate) Reset() {
	*x = PrimaryInsuredUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrimaryInsuredUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrimaryInsuredUpdate) ProtoMessage() {}

func (x *PrimaryInsuredUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrimaryInsuredUpdate.ProtoReflect.Descriptor instead.
func (*PrimaryInsuredUpdate) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{7}
}

func (x *PrimaryInsuredUpdate) GetUpdate() *proto.Insured {
	if x != nil {
		return x.Update
	}
	return nil
}

type SubCoverageChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoverageId string               `protobuf:"bytes,1,opt,name=CoverageId,proto3" json:"CoverageId,omitempty"`
	Add        []*model.SubCoverage `protobuf:"bytes,2,rep,name=add,proto3" json:"add,omitempty"`
	Remove     []string             `protobuf:"bytes,3,rep,name=remove,proto3" json:"remove,omitempty"`
}

func (x *SubCoverageChange) Reset() {
	*x = SubCoverageChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubCoverageChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubCoverageChange) ProtoMessage() {}

func (x *SubCoverageChange) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubCoverageChange.ProtoReflect.Descriptor instead.
func (*SubCoverageChange) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{8}
}

func (x *SubCoverageChange) GetCoverageId() string {
	if x != nil {
		return x.CoverageId
	}
	return ""
}

func (x *SubCoverageChange) GetAdd() []*model.SubCoverage {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *SubCoverageChange) GetRemove() []string {
	if x != nil {
		return x.Remove
	}
	return nil
}

type ClauseChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Add    []*proto.Clause   `protobuf:"bytes,1,rep,name=add,proto3" json:"add,omitempty"`
	Remove []*proto.ClauseId `protobuf:"bytes,2,rep,name=remove,proto3" json:"remove,omitempty"`
	Update []*proto.Clause   `protobuf:"bytes,3,rep,name=update,proto3" json:"update,omitempty"`
}

func (x *ClauseChange) Reset() {
	*x = ClauseChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClauseChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClauseChange) ProtoMessage() {}

func (x *ClauseChange) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClauseChange.ProtoReflect.Descriptor instead.
func (*ClauseChange) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{9}
}

func (x *ClauseChange) GetAdd() []*proto.Clause {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *ClauseChange) GetRemove() []*proto.ClauseId {
	if x != nil {
		return x.Remove
	}
	return nil
}

func (x *ClauseChange) GetUpdate() []*proto.Clause {
	if x != nil {
		return x.Update
	}
	return nil
}

type InterimCancellationChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reason                 string                 `protobuf:"bytes,1,opt,name=reason,proto3" json:"reason,omitempty"`
	CancellationFilingTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=cancellationFilingTime,proto3" json:"cancellationFilingTime,omitempty"`
	CancellationTime       *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=cancellationTime,proto3" json:"cancellationTime,omitempty"`
	Price                  float32                `protobuf:"fixed32,4,opt,name=price,proto3" json:"price,omitempty"`
	CancellationNotes      *string                `protobuf:"bytes,5,opt,name=cancellationNotes,proto3,oneof" json:"cancellationNotes,omitempty"`
}

func (x *InterimCancellationChange) Reset() {
	*x = InterimCancellationChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InterimCancellationChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterimCancellationChange) ProtoMessage() {}

func (x *InterimCancellationChange) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterimCancellationChange.ProtoReflect.Descriptor instead.
func (*InterimCancellationChange) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP(), []int{10}
}

func (x *InterimCancellationChange) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *InterimCancellationChange) GetCancellationFilingTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CancellationFilingTime
	}
	return nil
}

func (x *InterimCancellationChange) GetCancellationTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CancellationTime
	}
	return nil
}

func (x *InterimCancellationChange) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *InterimCancellationChange) GetCancellationNotes() string {
	if x != nil && x.CancellationNotes != nil {
		return *x.CancellationNotes
	}
	return ""
}

var File_insurance_bundle_model_endorsement_change_data_proto protoreflect.FileDescriptor

var file_insurance_bundle_model_endorsement_change_data_proto_rawDesc = []byte{
	0x0a, 0x34, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x1a, 0x29, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c,
	0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65,
	0x2f, 0x63, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x02, 0x0a,
	0x0a, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x0e, 0x63,
	0x6f, 0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x48, 0x00, 0x52, 0x0e, 0x63, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x4e, 0x0a, 0x0f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48,
	0x00, 0x52, 0x0f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x5a, 0x0a, 0x12, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28,
	0x2e, 0x6e, 0x6f, 0x6e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x12, 0x4e, 0x6f, 0x6e, 0x46,
	0x6c, 0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x06,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd3, 0x05, 0x0a, 0x0a, 0x43, 0x6f, 0x72, 0x65, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x63, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x72, 0x65,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x63, 0x6f, 0x72, 0x65,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x4b, 0x0a, 0x10, 0x64, 0x65, 0x64, 0x75,
	0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x48, 0x00, 0x52, 0x10, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x57, 0x0a, 0x14, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x14, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x42,
	0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x48, 0x00, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x4e, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52,
	0x11, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x63, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x66, 0x0a, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x69, 0x6d, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x69, 0x6d, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00,
	0x52, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x69, 0x6d, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x57, 0x0a, 0x14, 0x70,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x49,
	0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x14,
	0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x6b, 0x0a, 0x0b,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x03, 0x61,
	0x64, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x03, 0x61, 0x64, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x12, 0x24, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0x37, 0x0a, 0x0d, 0x44, 0x65, 0x64,
	0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x75,
	0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x49,
	0x64, 0x73, 0x22, 0x96, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x03, 0x61, 0x64, 0x64, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x65, 0x64,
	0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x52, 0x03, 0x61, 0x64, 0x64, 0x12, 0x32, 0x0a, 0x06,
	0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x63,
	0x74, 0x69, 0x62, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x12, 0x29, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69,
	0x62, 0x6c, 0x65, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0x2a, 0x0a, 0x14, 0x53,
	0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x22, 0xc2, 0x01, 0x0a, 0x0d, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x65, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x3d, 0x0a, 0x0b, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x69, 0x6e, 0x73,
	0x75, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x03, 0x61, 0x64, 0x64, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x52, 0x03,
	0x61, 0x64, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0x47, 0x0a, 0x14,
	0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x52, 0x06, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0x71, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x03, 0x61, 0x64,
	0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x52, 0x03, 0x61, 0x64, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x22, 0x9a, 0x01, 0x0a, 0x0c, 0x43, 0x6c, 0x61,
	0x75, 0x73, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x03, 0x61, 0x64, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x52, 0x03,
	0x61, 0x64, 0x64, 0x12, 0x30, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x49, 0x64, 0x52, 0x06, 0x72,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x2e, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x6c, 0x61, 0x75, 0x73, 0x65, 0x52, 0x06, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0xae, 0x02, 0x0a, 0x19, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x69,
	0x6d, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x16, 0x63,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x69, 0x6e,
	0x67, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x16, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x46, 0x0a, 0x10, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x31, 0x0a,
	0x11, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x88, 0x01, 0x01,
	0x42, 0x14, 0x0a, 0x12, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x2a, 0xcd, 0x02, 0x0a, 0x0e, 0x43, 0x6f, 0x72, 0x65, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x6f, 0x72,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x01, 0x12,
	0x1d, 0x0a, 0x19, 0x43, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x10, 0x02, 0x12, 0x1b,
	0x0a, 0x17, 0x43, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x43,
	0x6f, 0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x10, 0x04, 0x12, 0x1a,
	0x0a, 0x16, 0x43, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x10, 0x05, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x6f,
	0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x75, 0x62,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x10, 0x06, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x6f,
	0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x6c, 0x61,
	0x75, 0x73, 0x65, 0x10, 0x07, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x69, 0x6d, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x08, 0x12, 0x27, 0x0a,
	0x23, 0x43, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x10, 0x09, 0x42, 0x3c, 0x5a, 0x3a, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e,
	0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e,
	0x61, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x2d, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insurance_bundle_model_endorsement_change_data_proto_rawDescOnce sync.Once
	file_insurance_bundle_model_endorsement_change_data_proto_rawDescData = file_insurance_bundle_model_endorsement_change_data_proto_rawDesc
)

func file_insurance_bundle_model_endorsement_change_data_proto_rawDescGZIP() []byte {
	file_insurance_bundle_model_endorsement_change_data_proto_rawDescOnce.Do(func() {
		file_insurance_bundle_model_endorsement_change_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_bundle_model_endorsement_change_data_proto_rawDescData)
	})
	return file_insurance_bundle_model_endorsement_change_data_proto_rawDescData
}

var file_insurance_bundle_model_endorsement_change_data_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_insurance_bundle_model_endorsement_change_data_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_insurance_bundle_model_endorsement_change_data_proto_goTypes = []interface{}{
	(CoreChangeType)(0),                  // 0: endorsement.CoreChangeType
	(*ChangeType)(nil),                   // 1: endorsement.ChangeType
	(*CoreChange)(nil),                   // 2: endorsement.CoreChange
	(*LimitChange)(nil),                  // 3: endorsement.LimitChange
	(*DeductibleKey)(nil),                // 4: endorsement.DeductibleKey
	(*DeductibleChange)(nil),             // 5: endorsement.DeductibleChange
	(*SpecialRequestChange)(nil),         // 6: endorsement.SpecialRequestChange
	(*InsuredChange)(nil),                // 7: endorsement.InsuredChange
	(*PrimaryInsuredUpdate)(nil),         // 8: endorsement.PrimaryInsuredUpdate
	(*SubCoverageChange)(nil),            // 9: endorsement.SubCoverageChange
	(*ClauseChange)(nil),                 // 10: endorsement.ClauseChange
	(*InterimCancellationChange)(nil),    // 11: endorsement.InterimCancellationChange
	(endorsement.FleetChangeType)(0),     // 12: fleet_endorsement.FleetChangeType
	(endorsement1.NonFleetChangeType)(0), // 13: nonfleet_endorsement.NonFleetChangeType
	(*model.Limit)(nil),                  // 14: model.Limit
	(*model.Deductible)(nil),             // 15: model.Deductible
	(proto.InsuredType)(0),               // 16: insurance_core.InsuredType
	(*proto.Insured)(nil),                // 17: insurance_core.Insured
	(*model.SubCoverage)(nil),            // 18: model.SubCoverage
	(*proto.Clause)(nil),                 // 19: insurance_core.Clause
	(*proto.ClauseId)(nil),               // 20: insurance_core.ClauseId
	(*timestamppb.Timestamp)(nil),        // 21: google.protobuf.Timestamp
}
var file_insurance_bundle_model_endorsement_change_data_proto_depIdxs = []int32{
	0,  // 0: endorsement.ChangeType.coreChangeType:type_name -> endorsement.CoreChangeType
	12, // 1: endorsement.ChangeType.fleetChangeType:type_name -> fleet_endorsement.FleetChangeType
	13, // 2: endorsement.ChangeType.NonFleetChangeType:type_name -> nonfleet_endorsement.NonFleetChangeType
	0,  // 3: endorsement.CoreChange.coreChangeType:type_name -> endorsement.CoreChangeType
	3,  // 4: endorsement.CoreChange.limitChange:type_name -> endorsement.LimitChange
	5,  // 5: endorsement.CoreChange.deductibleChange:type_name -> endorsement.DeductibleChange
	6,  // 6: endorsement.CoreChange.specialRequestChange:type_name -> endorsement.SpecialRequestChange
	7,  // 7: endorsement.CoreChange.insuredChange:type_name -> endorsement.InsuredChange
	9,  // 8: endorsement.CoreChange.subCoverageChange:type_name -> endorsement.SubCoverageChange
	10, // 9: endorsement.CoreChange.clauseChange:type_name -> endorsement.ClauseChange
	11, // 10: endorsement.CoreChange.interimCancellationChange:type_name -> endorsement.InterimCancellationChange
	8,  // 11: endorsement.CoreChange.primaryInsuredUpdate:type_name -> endorsement.PrimaryInsuredUpdate
	14, // 12: endorsement.LimitChange.add:type_name -> model.Limit
	14, // 13: endorsement.LimitChange.update:type_name -> model.Limit
	15, // 14: endorsement.DeductibleChange.add:type_name -> model.Deductible
	4,  // 15: endorsement.DeductibleChange.remove:type_name -> endorsement.DeductibleKey
	15, // 16: endorsement.DeductibleChange.update:type_name -> model.Deductible
	16, // 17: endorsement.InsuredChange.insuredType:type_name -> insurance_core.InsuredType
	17, // 18: endorsement.InsuredChange.add:type_name -> insurance_core.Insured
	17, // 19: endorsement.InsuredChange.update:type_name -> insurance_core.Insured
	17, // 20: endorsement.PrimaryInsuredUpdate.update:type_name -> insurance_core.Insured
	18, // 21: endorsement.SubCoverageChange.add:type_name -> model.SubCoverage
	19, // 22: endorsement.ClauseChange.add:type_name -> insurance_core.Clause
	20, // 23: endorsement.ClauseChange.remove:type_name -> insurance_core.ClauseId
	19, // 24: endorsement.ClauseChange.update:type_name -> insurance_core.Clause
	21, // 25: endorsement.InterimCancellationChange.cancellationFilingTime:type_name -> google.protobuf.Timestamp
	21, // 26: endorsement.InterimCancellationChange.cancellationTime:type_name -> google.protobuf.Timestamp
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_insurance_bundle_model_endorsement_change_data_proto_init() }
func file_insurance_bundle_model_endorsement_change_data_proto_init() {
	if File_insurance_bundle_model_endorsement_change_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoreChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeductibleKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeductibleChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpecialRequestChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsuredChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrimaryInsuredUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubCoverageChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClauseChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InterimCancellationChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ChangeType_CoreChangeType)(nil),
		(*ChangeType_FleetChangeType)(nil),
		(*ChangeType_NonFleetChangeType)(nil),
	}
	file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*CoreChange_LimitChange)(nil),
		(*CoreChange_DeductibleChange)(nil),
		(*CoreChange_SpecialRequestChange)(nil),
		(*CoreChange_InsuredChange)(nil),
		(*CoreChange_SubCoverageChange)(nil),
		(*CoreChange_ClauseChange)(nil),
		(*CoreChange_InterimCancellationChange)(nil),
		(*CoreChange_PrimaryInsuredUpdate)(nil),
	}
	file_insurance_bundle_model_endorsement_change_data_proto_msgTypes[10].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_bundle_model_endorsement_change_data_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_bundle_model_endorsement_change_data_proto_goTypes,
		DependencyIndexes: file_insurance_bundle_model_endorsement_change_data_proto_depIdxs,
		EnumInfos:         file_insurance_bundle_model_endorsement_change_data_proto_enumTypes,
		MessageInfos:      file_insurance_bundle_model_endorsement_change_data_proto_msgTypes,
	}.Build()
	File_insurance_bundle_model_endorsement_change_data_proto = out.File
	file_insurance_bundle_model_endorsement_change_data_proto_rawDesc = nil
	file_insurance_bundle_model_endorsement_change_data_proto_goTypes = nil
	file_insurance_bundle_model_endorsement_change_data_proto_depIdxs = nil
}
