// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_bundle/model/charges/charges.proto

package charges

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	ptypes "nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChargeList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Charges []*ptypes.Charge `protobuf:"bytes,1,rep,name=charges,proto3" json:"charges,omitempty"`
}

func (x *ChargeList) Reset() {
	*x = ChargeList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_charges_charges_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChargeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChargeList) ProtoMessage() {}

func (x *ChargeList) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_charges_charges_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChargeList.ProtoReflect.Descriptor instead.
func (*ChargeList) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_charges_charges_proto_rawDescGZIP(), []int{0}
}

func (x *ChargeList) GetCharges() []*ptypes.Charge {
	if x != nil {
		return x.Charges
	}
	return nil
}

type PolicyCharges struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PolicyCharges map[string]*ChargeList `protobuf:"bytes,1,rep,name=policyCharges,proto3" json:"policyCharges,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *PolicyCharges) Reset() {
	*x = PolicyCharges{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_model_charges_charges_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicyCharges) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyCharges) ProtoMessage() {}

func (x *PolicyCharges) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_model_charges_charges_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyCharges.ProtoReflect.Descriptor instead.
func (*PolicyCharges) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_model_charges_charges_proto_rawDescGZIP(), []int{1}
}

func (x *PolicyCharges) GetPolicyCharges() map[string]*ChargeList {
	if x != nil {
		return x.PolicyCharges
	}
	return nil
}

var File_insurance_bundle_model_charges_charges_proto protoreflect.FileDescriptor

var file_insurance_bundle_model_charges_charges_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x2f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x1a, 0x15, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x37,
	0x0a, 0x0a, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x07,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x07,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x22, 0xb7, 0x01, 0x0a, 0x0d, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x0d, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x1a, 0x55, 0x0a, 0x12, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x29, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x2e, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insurance_bundle_model_charges_charges_proto_rawDescOnce sync.Once
	file_insurance_bundle_model_charges_charges_proto_rawDescData = file_insurance_bundle_model_charges_charges_proto_rawDesc
)

func file_insurance_bundle_model_charges_charges_proto_rawDescGZIP() []byte {
	file_insurance_bundle_model_charges_charges_proto_rawDescOnce.Do(func() {
		file_insurance_bundle_model_charges_charges_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_bundle_model_charges_charges_proto_rawDescData)
	})
	return file_insurance_bundle_model_charges_charges_proto_rawDescData
}

var file_insurance_bundle_model_charges_charges_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_insurance_bundle_model_charges_charges_proto_goTypes = []interface{}{
	(*ChargeList)(nil),    // 0: charges.ChargeList
	(*PolicyCharges)(nil), // 1: charges.PolicyCharges
	nil,                   // 2: charges.PolicyCharges.PolicyChargesEntry
	(*ptypes.Charge)(nil), // 3: pricing.Charge
}
var file_insurance_bundle_model_charges_charges_proto_depIdxs = []int32{
	3, // 0: charges.ChargeList.charges:type_name -> pricing.Charge
	2, // 1: charges.PolicyCharges.policyCharges:type_name -> charges.PolicyCharges.PolicyChargesEntry
	0, // 2: charges.PolicyCharges.PolicyChargesEntry.value:type_name -> charges.ChargeList
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_insurance_bundle_model_charges_charges_proto_init() }
func file_insurance_bundle_model_charges_charges_proto_init() {
	if File_insurance_bundle_model_charges_charges_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_insurance_bundle_model_charges_charges_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChargeList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_model_charges_charges_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicyCharges); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_bundle_model_charges_charges_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_bundle_model_charges_charges_proto_goTypes,
		DependencyIndexes: file_insurance_bundle_model_charges_charges_proto_depIdxs,
		MessageInfos:      file_insurance_bundle_model_charges_charges_proto_msgTypes,
	}.Build()
	File_insurance_bundle_model_charges_charges_proto = out.File
	file_insurance_bundle_model_charges_charges_proto_rawDesc = nil
	file_insurance_bundle_model_charges_charges_proto_goTypes = nil
	file_insurance_bundle_model_charges_charges_proto_depIdxs = nil
}
