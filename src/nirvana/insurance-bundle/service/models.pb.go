// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_bundle/service/models.proto

package service

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	proto1 "nirvanatech.com/nirvana/common-go/proto"
	model "nirvanatech.com/nirvana/insurance-bundle/model"
	endorsement "nirvanatech.com/nirvana/insurance-bundle/model/endorsement"
	proto "nirvanatech.com/nirvana/insurance-core/proto"
	model1 "nirvanatech.com/nirvana/insured/model"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpsertInsuranceBundleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceBundle *model.InsuranceBundle `protobuf:"bytes,1,opt,name=insuranceBundle,proto3" json:"insuranceBundle,omitempty"`
	Insured         *model1.Insured        `protobuf:"bytes,100,opt,name=insured,proto3" json:"insured,omitempty"`
}

func (x *UpsertInsuranceBundleRequest) Reset() {
	*x = UpsertInsuranceBundleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertInsuranceBundleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertInsuranceBundleRequest) ProtoMessage() {}

func (x *UpsertInsuranceBundleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertInsuranceBundleRequest.ProtoReflect.Descriptor instead.
func (*UpsertInsuranceBundleRequest) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{0}
}

func (x *UpsertInsuranceBundleRequest) GetInsuranceBundle() *model.InsuranceBundle {
	if x != nil {
		return x.InsuranceBundle
	}
	return nil
}

func (x *UpsertInsuranceBundleRequest) GetInsured() *model1.Insured {
	if x != nil {
		return x.Insured
	}
	return nil
}

type UpsertInsuranceBundleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceBundleInternalId string `protobuf:"bytes,1,opt,name=insuranceBundleInternalId,proto3" json:"insuranceBundleInternalId,omitempty"`
	InsuredId                 string `protobuf:"bytes,2,opt,name=insuredId,proto3" json:"insuredId,omitempty"`
}

func (x *UpsertInsuranceBundleResponse) Reset() {
	*x = UpsertInsuranceBundleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertInsuranceBundleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertInsuranceBundleResponse) ProtoMessage() {}

func (x *UpsertInsuranceBundleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertInsuranceBundleResponse.ProtoReflect.Descriptor instead.
func (*UpsertInsuranceBundleResponse) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{1}
}

func (x *UpsertInsuranceBundleResponse) GetInsuranceBundleInternalId() string {
	if x != nil {
		return x.InsuranceBundleInternalId
	}
	return ""
}

func (x *UpsertInsuranceBundleResponse) GetInsuredId() string {
	if x != nil {
		return x.InsuredId
	}
	return ""
}

type ExecuteEndorsementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceBundleID   string                             `protobuf:"bytes,1,opt,name=insuranceBundleID,proto3" json:"insuranceBundleID,omitempty"`
	ChangeContainers    []*endorsement.ChangeContainer     `protobuf:"bytes,2,rep,name=changeContainers,proto3" json:"changeContainers,omitempty"`
	ToPersistEndorsedIB bool                               `protobuf:"varint,3,opt,name=toPersistEndorsedIB,proto3" json:"toPersistEndorsedIB,omitempty"`
	Metadata            *ExecuteEndorsementRequestMetadata `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *ExecuteEndorsementRequest) Reset() {
	*x = ExecuteEndorsementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecuteEndorsementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteEndorsementRequest) ProtoMessage() {}

func (x *ExecuteEndorsementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteEndorsementRequest.ProtoReflect.Descriptor instead.
func (*ExecuteEndorsementRequest) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{2}
}

func (x *ExecuteEndorsementRequest) GetInsuranceBundleID() string {
	if x != nil {
		return x.InsuranceBundleID
	}
	return ""
}

func (x *ExecuteEndorsementRequest) GetChangeContainers() []*endorsement.ChangeContainer {
	if x != nil {
		return x.ChangeContainers
	}
	return nil
}

func (x *ExecuteEndorsementRequest) GetToPersistEndorsedIB() bool {
	if x != nil {
		return x.ToPersistEndorsedIB
	}
	return false
}

func (x *ExecuteEndorsementRequest) GetMetadata() *ExecuteEndorsementRequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type ExecuteEndorsementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceBundle                                *model.InsuranceBundle `protobuf:"bytes,1,opt,name=insuranceBundle,proto3" json:"insuranceBundle,omitempty"`
	InsuranceBundleSegmentIDToChangeContainerIDMap map[string]string      `protobuf:"bytes,2,rep,name=insuranceBundleSegmentIDToChangeContainerIDMap,proto3" json:"insuranceBundleSegmentIDToChangeContainerIDMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ExecuteEndorsementRequestID                    string                 `protobuf:"bytes,3,opt,name=executeEndorsementRequestID,proto3" json:"executeEndorsementRequestID,omitempty"`
}

func (x *ExecuteEndorsementResponse) Reset() {
	*x = ExecuteEndorsementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecuteEndorsementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteEndorsementResponse) ProtoMessage() {}

func (x *ExecuteEndorsementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteEndorsementResponse.ProtoReflect.Descriptor instead.
func (*ExecuteEndorsementResponse) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{3}
}

func (x *ExecuteEndorsementResponse) GetInsuranceBundle() *model.InsuranceBundle {
	if x != nil {
		return x.InsuranceBundle
	}
	return nil
}

func (x *ExecuteEndorsementResponse) GetInsuranceBundleSegmentIDToChangeContainerIDMap() map[string]string {
	if x != nil {
		return x.InsuranceBundleSegmentIDToChangeContainerIDMap
	}
	return nil
}

func (x *ExecuteEndorsementResponse) GetExecuteEndorsementRequestID() string {
	if x != nil {
		return x.ExecuteEndorsementRequestID
	}
	return ""
}

type ListCondensedInsuranceBundlesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProgramType proto.ProgramType `protobuf:"varint,1,opt,name=programType,proto3,enum=insurance_core.ProgramType" json:"programType,omitempty"`
	SellerInfo  *proto.SellerInfo `protobuf:"bytes,2,opt,name=sellerInfo,proto3" json:"sellerInfo,omitempty"`
	Version     *int32            `protobuf:"varint,3,opt,name=version,proto3,oneof" json:"version,omitempty"`
	Pagination  *model.Pagination `protobuf:"bytes,4,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListCondensedInsuranceBundlesRequest) Reset() {
	*x = ListCondensedInsuranceBundlesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCondensedInsuranceBundlesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCondensedInsuranceBundlesRequest) ProtoMessage() {}

func (x *ListCondensedInsuranceBundlesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCondensedInsuranceBundlesRequest.ProtoReflect.Descriptor instead.
func (*ListCondensedInsuranceBundlesRequest) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{4}
}

func (x *ListCondensedInsuranceBundlesRequest) GetProgramType() proto.ProgramType {
	if x != nil {
		return x.ProgramType
	}
	return proto.ProgramType(0)
}

func (x *ListCondensedInsuranceBundlesRequest) GetSellerInfo() *proto.SellerInfo {
	if x != nil {
		return x.SellerInfo
	}
	return nil
}

func (x *ListCondensedInsuranceBundlesRequest) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *ListCondensedInsuranceBundlesRequest) GetPagination() *model.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type ListCondensedInsuranceBundlesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CondensedInsuranceBundles []*model.CondensedInsuranceBundle `protobuf:"bytes,1,rep,name=condensedInsuranceBundles,proto3" json:"condensedInsuranceBundles,omitempty"`
	NextCursor                *string                           `protobuf:"bytes,2,opt,name=next_cursor,json=nextCursor,proto3,oneof" json:"next_cursor,omitempty"`
}

func (x *ListCondensedInsuranceBundlesResponse) Reset() {
	*x = ListCondensedInsuranceBundlesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCondensedInsuranceBundlesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCondensedInsuranceBundlesResponse) ProtoMessage() {}

func (x *ListCondensedInsuranceBundlesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCondensedInsuranceBundlesResponse.ProtoReflect.Descriptor instead.
func (*ListCondensedInsuranceBundlesResponse) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{5}
}

func (x *ListCondensedInsuranceBundlesResponse) GetCondensedInsuranceBundles() []*model.CondensedInsuranceBundle {
	if x != nil {
		return x.CondensedInsuranceBundles
	}
	return nil
}

func (x *ListCondensedInsuranceBundlesResponse) GetNextCursor() string {
	if x != nil && x.NextCursor != nil {
		return *x.NextCursor
	}
	return ""
}

type GetInsuranceBundleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrimaryFilter   *GetInsuranceBundleRequest_PrimaryFilter   `protobuf:"bytes,1,opt,name=primaryFilter,proto3" json:"primaryFilter,omitempty"`
	SecondaryFilter *GetInsuranceBundleRequest_SecondaryFilter `protobuf:"bytes,2,opt,name=secondaryFilter,proto3" json:"secondaryFilter,omitempty"`
}

func (x *GetInsuranceBundleRequest) Reset() {
	*x = GetInsuranceBundleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInsuranceBundleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInsuranceBundleRequest) ProtoMessage() {}

func (x *GetInsuranceBundleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInsuranceBundleRequest.ProtoReflect.Descriptor instead.
func (*GetInsuranceBundleRequest) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{6}
}

func (x *GetInsuranceBundleRequest) GetPrimaryFilter() *GetInsuranceBundleRequest_PrimaryFilter {
	if x != nil {
		return x.PrimaryFilter
	}
	return nil
}

func (x *GetInsuranceBundleRequest) GetSecondaryFilter() *GetInsuranceBundleRequest_SecondaryFilter {
	if x != nil {
		return x.SecondaryFilter
	}
	return nil
}

type GetInsuranceBundleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceBundle *model.InsuranceBundle `protobuf:"bytes,1,opt,name=insuranceBundle,proto3" json:"insuranceBundle,omitempty"`
}

func (x *GetInsuranceBundleResponse) Reset() {
	*x = GetInsuranceBundleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInsuranceBundleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInsuranceBundleResponse) ProtoMessage() {}

func (x *GetInsuranceBundleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInsuranceBundleResponse.ProtoReflect.Descriptor instead.
func (*GetInsuranceBundleResponse) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{7}
}

func (x *GetInsuranceBundleResponse) GetInsuranceBundle() *model.InsuranceBundle {
	if x != nil {
		return x.InsuranceBundle
	}
	return nil
}

type ListInsuranceBundleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProgramType  proto.ProgramType `protobuf:"varint,1,opt,name=programType,proto3,enum=insurance_core.ProgramType" json:"programType,omitempty"`
	Interval     *proto1.Interval  `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
	Version      *int32            `protobuf:"varint,3,opt,name=version,proto3,oneof" json:"version,omitempty"`
	VersionRange *VersionRange     `protobuf:"bytes,4,opt,name=versionRange,proto3,oneof" json:"versionRange,omitempty"`
	ExternalId   *string           `protobuf:"bytes,5,opt,name=externalId,proto3,oneof" json:"externalId,omitempty"`
	Pagination   *model.Pagination `protobuf:"bytes,6,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListInsuranceBundleRequest) Reset() {
	*x = ListInsuranceBundleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListInsuranceBundleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInsuranceBundleRequest) ProtoMessage() {}

func (x *ListInsuranceBundleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInsuranceBundleRequest.ProtoReflect.Descriptor instead.
func (*ListInsuranceBundleRequest) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{8}
}

func (x *ListInsuranceBundleRequest) GetProgramType() proto.ProgramType {
	if x != nil {
		return x.ProgramType
	}
	return proto.ProgramType(0)
}

func (x *ListInsuranceBundleRequest) GetInterval() *proto1.Interval {
	if x != nil {
		return x.Interval
	}
	return nil
}

func (x *ListInsuranceBundleRequest) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *ListInsuranceBundleRequest) GetVersionRange() *VersionRange {
	if x != nil {
		return x.VersionRange
	}
	return nil
}

func (x *ListInsuranceBundleRequest) GetExternalId() string {
	if x != nil && x.ExternalId != nil {
		return *x.ExternalId
	}
	return ""
}

func (x *ListInsuranceBundleRequest) GetPagination() *model.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type VersionRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start *int32 `protobuf:"varint,1,opt,name=start,proto3,oneof" json:"start,omitempty"`
	End   *int32 `protobuf:"varint,2,opt,name=end,proto3,oneof" json:"end,omitempty"`
}

func (x *VersionRange) Reset() {
	*x = VersionRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionRange) ProtoMessage() {}

func (x *VersionRange) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionRange.ProtoReflect.Descriptor instead.
func (*VersionRange) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{9}
}

func (x *VersionRange) GetStart() int32 {
	if x != nil && x.Start != nil {
		return *x.Start
	}
	return 0
}

func (x *VersionRange) GetEnd() int32 {
	if x != nil && x.End != nil {
		return *x.End
	}
	return 0
}

type ListInsuranceBundlesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsuranceBundles []*model.InsuranceBundle `protobuf:"bytes,1,rep,name=insuranceBundles,proto3" json:"insuranceBundles,omitempty"`
	NextCursor       *string                  `protobuf:"bytes,2,opt,name=next_cursor,json=nextCursor,proto3,oneof" json:"next_cursor,omitempty"`
}

func (x *ListInsuranceBundlesResponse) Reset() {
	*x = ListInsuranceBundlesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListInsuranceBundlesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListInsuranceBundlesResponse) ProtoMessage() {}

func (x *ListInsuranceBundlesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListInsuranceBundlesResponse.ProtoReflect.Descriptor instead.
func (*ListInsuranceBundlesResponse) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{10}
}

func (x *ListInsuranceBundlesResponse) GetInsuranceBundles() []*model.InsuranceBundle {
	if x != nil {
		return x.InsuranceBundles
	}
	return nil
}

func (x *ListInsuranceBundlesResponse) GetNextCursor() string {
	if x != nil && x.NextCursor != nil {
		return *x.NextCursor
	}
	return ""
}

type ExecuteEndorsementRequestMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EndorsementRequestId string           `protobuf:"bytes,1,opt,name=endorsementRequestId,proto3" json:"endorsementRequestId,omitempty"`
	EndorsementReviewId  string           `protobuf:"bytes,2,opt,name=endorsementReviewId,proto3" json:"endorsementReviewId,omitempty"`
	AdditionalInfo       *structpb.Struct `protobuf:"bytes,3,opt,name=additionalInfo,proto3" json:"additionalInfo,omitempty"`
}

func (x *ExecuteEndorsementRequestMetadata) Reset() {
	*x = ExecuteEndorsementRequestMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecuteEndorsementRequestMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteEndorsementRequestMetadata) ProtoMessage() {}

func (x *ExecuteEndorsementRequestMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteEndorsementRequestMetadata.ProtoReflect.Descriptor instead.
func (*ExecuteEndorsementRequestMetadata) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{11}
}

func (x *ExecuteEndorsementRequestMetadata) GetEndorsementRequestId() string {
	if x != nil {
		return x.EndorsementRequestId
	}
	return ""
}

func (x *ExecuteEndorsementRequestMetadata) GetEndorsementReviewId() string {
	if x != nil {
		return x.EndorsementReviewId
	}
	return ""
}

func (x *ExecuteEndorsementRequestMetadata) GetAdditionalInfo() *structpb.Struct {
	if x != nil {
		return x.AdditionalInfo
	}
	return nil
}

type GetInsuranceBundleRequest_PrimaryFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetInsuranceBundleRequest_PrimaryFilter_InsuredId
	//	*GetInsuranceBundleRequest_PrimaryFilter_InternalId
	//	*GetInsuranceBundleRequest_PrimaryFilter_ExternalId
	//	*GetInsuranceBundleRequest_PrimaryFilter_ApplicationId
	Identifier isGetInsuranceBundleRequest_PrimaryFilter_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetInsuranceBundleRequest_PrimaryFilter) Reset() {
	*x = GetInsuranceBundleRequest_PrimaryFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInsuranceBundleRequest_PrimaryFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInsuranceBundleRequest_PrimaryFilter) ProtoMessage() {}

func (x *GetInsuranceBundleRequest_PrimaryFilter) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInsuranceBundleRequest_PrimaryFilter.ProtoReflect.Descriptor instead.
func (*GetInsuranceBundleRequest_PrimaryFilter) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{6, 0}
}

func (m *GetInsuranceBundleRequest_PrimaryFilter) GetIdentifier() isGetInsuranceBundleRequest_PrimaryFilter_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetInsuranceBundleRequest_PrimaryFilter) GetInsuredId() string {
	if x, ok := x.GetIdentifier().(*GetInsuranceBundleRequest_PrimaryFilter_InsuredId); ok {
		return x.InsuredId
	}
	return ""
}

func (x *GetInsuranceBundleRequest_PrimaryFilter) GetInternalId() string {
	if x, ok := x.GetIdentifier().(*GetInsuranceBundleRequest_PrimaryFilter_InternalId); ok {
		return x.InternalId
	}
	return ""
}

func (x *GetInsuranceBundleRequest_PrimaryFilter) GetExternalId() string {
	if x, ok := x.GetIdentifier().(*GetInsuranceBundleRequest_PrimaryFilter_ExternalId); ok {
		return x.ExternalId
	}
	return ""
}

func (x *GetInsuranceBundleRequest_PrimaryFilter) GetApplicationId() string {
	if x, ok := x.GetIdentifier().(*GetInsuranceBundleRequest_PrimaryFilter_ApplicationId); ok {
		return x.ApplicationId
	}
	return ""
}

type isGetInsuranceBundleRequest_PrimaryFilter_Identifier interface {
	isGetInsuranceBundleRequest_PrimaryFilter_Identifier()
}

type GetInsuranceBundleRequest_PrimaryFilter_InsuredId struct {
	InsuredId string `protobuf:"bytes,1,opt,name=insuredId,proto3,oneof"`
}

type GetInsuranceBundleRequest_PrimaryFilter_InternalId struct {
	InternalId string `protobuf:"bytes,2,opt,name=internalId,proto3,oneof"`
}

type GetInsuranceBundleRequest_PrimaryFilter_ExternalId struct {
	ExternalId string `protobuf:"bytes,3,opt,name=externalId,proto3,oneof"`
}

type GetInsuranceBundleRequest_PrimaryFilter_ApplicationId struct {
	ApplicationId string `protobuf:"bytes,4,opt,name=applicationId,proto3,oneof"`
}

func (*GetInsuranceBundleRequest_PrimaryFilter_InsuredId) isGetInsuranceBundleRequest_PrimaryFilter_Identifier() {
}

func (*GetInsuranceBundleRequest_PrimaryFilter_InternalId) isGetInsuranceBundleRequest_PrimaryFilter_Identifier() {
}

func (*GetInsuranceBundleRequest_PrimaryFilter_ExternalId) isGetInsuranceBundleRequest_PrimaryFilter_Identifier() {
}

func (*GetInsuranceBundleRequest_PrimaryFilter_ApplicationId) isGetInsuranceBundleRequest_PrimaryFilter_Identifier() {
}

type GetInsuranceBundleRequest_SecondaryFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LatestVersionPresentAt *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=latestVersionPresentAt,proto3" json:"latestVersionPresentAt,omitempty"`
	InsuranceBundleAt      *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=insuranceBundleAt,proto3" json:"insuranceBundleAt,omitempty"`
	ProgramType            *proto.ProgramType     `protobuf:"varint,3,opt,name=programType,proto3,enum=insurance_core.ProgramType,oneof" json:"programType,omitempty"`
	Version                *int32                 `protobuf:"varint,4,opt,name=version,proto3,oneof" json:"version,omitempty"`
}

func (x *GetInsuranceBundleRequest_SecondaryFilter) Reset() {
	*x = GetInsuranceBundleRequest_SecondaryFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insurance_bundle_service_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInsuranceBundleRequest_SecondaryFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInsuranceBundleRequest_SecondaryFilter) ProtoMessage() {}

func (x *GetInsuranceBundleRequest_SecondaryFilter) ProtoReflect() protoreflect.Message {
	mi := &file_insurance_bundle_service_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInsuranceBundleRequest_SecondaryFilter.ProtoReflect.Descriptor instead.
func (*GetInsuranceBundleRequest_SecondaryFilter) Descriptor() ([]byte, []int) {
	return file_insurance_bundle_service_models_proto_rawDescGZIP(), []int{6, 1}
}

func (x *GetInsuranceBundleRequest_SecondaryFilter) GetLatestVersionPresentAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LatestVersionPresentAt
	}
	return nil
}

func (x *GetInsuranceBundleRequest_SecondaryFilter) GetInsuranceBundleAt() *timestamppb.Timestamp {
	if x != nil {
		return x.InsuranceBundleAt
	}
	return nil
}

func (x *GetInsuranceBundleRequest_SecondaryFilter) GetProgramType() proto.ProgramType {
	if x != nil && x.ProgramType != nil {
		return *x.ProgramType
	}
	return proto.ProgramType(0)
}

func (x *GetInsuranceBundleRequest_SecondaryFilter) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

var File_insurance_bundle_service_models_proto protoreflect.FileDescriptor

var file_insurance_bundle_service_models_proto_rawDesc = []byte{
	0x0a, 0x25, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x1a, 0x2d, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f,
	0x72, 0x65, 0x2f, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x21, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8c, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x49,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x0f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x65, 0x64, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x65, 0x64, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x52, 0x07, 0x69, 0x6e, 0x73, 0x75,
	0x72, 0x65, 0x64, 0x22, 0x7b, 0x0a, 0x1d, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x19, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x64,
	0x22, 0x8d, 0x02, 0x0a, 0x19, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c,
	0x0a, 0x11, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x12, 0x48, 0x0a, 0x10,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x30, 0x0a, 0x13, 0x74, 0x6f, 0x50, 0x65, 0x72, 0x73,
	0x69, 0x73, 0x74, 0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x64, 0x49, 0x42, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x13, 0x74, 0x6f, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x45, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x65, 0x64, 0x49, 0x42, 0x12, 0x46, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x22, 0xc5, 0x03, 0x0a, 0x1a, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x40, 0x0a, 0x0f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x52, 0x0f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x12, 0xbf, 0x01, 0x0a, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x54, 0x6f,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49,
	0x44, 0x4d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x57, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x54, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x54, 0x6f, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x49, 0x44,
	0x4d, 0x61, 0x70, 0x12, 0x40, 0x0a, 0x1b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x45, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x44, 0x1a, 0x61, 0x0a, 0x33, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x44, 0x54, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x49, 0x44, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x93, 0x02, 0x0a, 0x24, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x73, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3d, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x3a, 0x0a, 0x0a, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0a, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xbc,
	0x01, 0x0a, 0x25, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x73, 0x65, 0x64,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x19, 0x63, 0x6f, 0x6e, 0x64,
	0x65, 0x6e, 0x73, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x73, 0x65, 0x64, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x19, 0x63, 0x6f,
	0x6e, 0x64, 0x65, 0x6e, 0x73, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a,
	0x6e, 0x65, 0x78, 0x74, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x22, 0xae, 0x05,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x56, 0x0a, 0x0d, 0x70,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x5c, 0x0a, 0x0f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x0f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x1a, 0xa9, 0x01, 0x0a, 0x0d, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x42,
	0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x1a, 0xae, 0x02,
	0x0a, 0x0f, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x52, 0x0a, 0x16, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x16, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x74, 0x41, 0x74, 0x12, 0x48, 0x0a, 0x11, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x74, 0x12,
	0x42, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x5e,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0f,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x0f, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x22, 0x80,
	0x03, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d, 0x0a,
	0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x08,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0c, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x01, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52,
	0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x36,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x03, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x52, 0x0a, 0x0c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x19, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x00, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03,
	0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x03, 0x65, 0x6e, 0x64,
	0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x42, 0x06, 0x0a,
	0x04, 0x5f, 0x65, 0x6e, 0x64, 0x22, 0x98, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x10, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x10, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x88, 0x01, 0x01,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72,
	0x22, 0xca, 0x01, 0x0a, 0x21, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x32, 0x0a, 0x14, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x13, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0e,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0e, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insurance_bundle_service_models_proto_rawDescOnce sync.Once
	file_insurance_bundle_service_models_proto_rawDescData = file_insurance_bundle_service_models_proto_rawDesc
)

func file_insurance_bundle_service_models_proto_rawDescGZIP() []byte {
	file_insurance_bundle_service_models_proto_rawDescOnce.Do(func() {
		file_insurance_bundle_service_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_insurance_bundle_service_models_proto_rawDescData)
	})
	return file_insurance_bundle_service_models_proto_rawDescData
}

var file_insurance_bundle_service_models_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_insurance_bundle_service_models_proto_goTypes = []interface{}{
	(*UpsertInsuranceBundleRequest)(nil),          // 0: service.UpsertInsuranceBundleRequest
	(*UpsertInsuranceBundleResponse)(nil),         // 1: service.UpsertInsuranceBundleResponse
	(*ExecuteEndorsementRequest)(nil),             // 2: service.ExecuteEndorsementRequest
	(*ExecuteEndorsementResponse)(nil),            // 3: service.ExecuteEndorsementResponse
	(*ListCondensedInsuranceBundlesRequest)(nil),  // 4: service.ListCondensedInsuranceBundlesRequest
	(*ListCondensedInsuranceBundlesResponse)(nil), // 5: service.ListCondensedInsuranceBundlesResponse
	(*GetInsuranceBundleRequest)(nil),             // 6: service.GetInsuranceBundleRequest
	(*GetInsuranceBundleResponse)(nil),            // 7: service.GetInsuranceBundleResponse
	(*ListInsuranceBundleRequest)(nil),            // 8: service.ListInsuranceBundleRequest
	(*VersionRange)(nil),                          // 9: service.VersionRange
	(*ListInsuranceBundlesResponse)(nil),          // 10: service.ListInsuranceBundlesResponse
	(*ExecuteEndorsementRequestMetadata)(nil),     // 11: service.ExecuteEndorsementRequestMetadata
	nil, // 12: service.ExecuteEndorsementResponse.InsuranceBundleSegmentIDToChangeContainerIDMapEntry
	(*GetInsuranceBundleRequest_PrimaryFilter)(nil),   // 13: service.GetInsuranceBundleRequest.PrimaryFilter
	(*GetInsuranceBundleRequest_SecondaryFilter)(nil), // 14: service.GetInsuranceBundleRequest.SecondaryFilter
	(*model.InsuranceBundle)(nil),                     // 15: model.InsuranceBundle
	(*model1.Insured)(nil),                            // 16: insured.Insured
	(*endorsement.ChangeContainer)(nil),               // 17: endorsement.ChangeContainer
	(proto.ProgramType)(0),                            // 18: insurance_core.ProgramType
	(*proto.SellerInfo)(nil),                          // 19: insurance_core.SellerInfo
	(*model.Pagination)(nil),                          // 20: model.Pagination
	(*model.CondensedInsuranceBundle)(nil),            // 21: model.CondensedInsuranceBundle
	(*proto1.Interval)(nil),                           // 22: common.Interval
	(*structpb.Struct)(nil),                           // 23: google.protobuf.Struct
	(*timestamppb.Timestamp)(nil),                     // 24: google.protobuf.Timestamp
}
var file_insurance_bundle_service_models_proto_depIdxs = []int32{
	15, // 0: service.UpsertInsuranceBundleRequest.insuranceBundle:type_name -> model.InsuranceBundle
	16, // 1: service.UpsertInsuranceBundleRequest.insured:type_name -> insured.Insured
	17, // 2: service.ExecuteEndorsementRequest.changeContainers:type_name -> endorsement.ChangeContainer
	11, // 3: service.ExecuteEndorsementRequest.metadata:type_name -> service.ExecuteEndorsementRequestMetadata
	15, // 4: service.ExecuteEndorsementResponse.insuranceBundle:type_name -> model.InsuranceBundle
	12, // 5: service.ExecuteEndorsementResponse.insuranceBundleSegmentIDToChangeContainerIDMap:type_name -> service.ExecuteEndorsementResponse.InsuranceBundleSegmentIDToChangeContainerIDMapEntry
	18, // 6: service.ListCondensedInsuranceBundlesRequest.programType:type_name -> insurance_core.ProgramType
	19, // 7: service.ListCondensedInsuranceBundlesRequest.sellerInfo:type_name -> insurance_core.SellerInfo
	20, // 8: service.ListCondensedInsuranceBundlesRequest.pagination:type_name -> model.Pagination
	21, // 9: service.ListCondensedInsuranceBundlesResponse.condensedInsuranceBundles:type_name -> model.CondensedInsuranceBundle
	13, // 10: service.GetInsuranceBundleRequest.primaryFilter:type_name -> service.GetInsuranceBundleRequest.PrimaryFilter
	14, // 11: service.GetInsuranceBundleRequest.secondaryFilter:type_name -> service.GetInsuranceBundleRequest.SecondaryFilter
	15, // 12: service.GetInsuranceBundleResponse.insuranceBundle:type_name -> model.InsuranceBundle
	18, // 13: service.ListInsuranceBundleRequest.programType:type_name -> insurance_core.ProgramType
	22, // 14: service.ListInsuranceBundleRequest.interval:type_name -> common.Interval
	9,  // 15: service.ListInsuranceBundleRequest.versionRange:type_name -> service.VersionRange
	20, // 16: service.ListInsuranceBundleRequest.pagination:type_name -> model.Pagination
	15, // 17: service.ListInsuranceBundlesResponse.insuranceBundles:type_name -> model.InsuranceBundle
	23, // 18: service.ExecuteEndorsementRequestMetadata.additionalInfo:type_name -> google.protobuf.Struct
	24, // 19: service.GetInsuranceBundleRequest.SecondaryFilter.latestVersionPresentAt:type_name -> google.protobuf.Timestamp
	24, // 20: service.GetInsuranceBundleRequest.SecondaryFilter.insuranceBundleAt:type_name -> google.protobuf.Timestamp
	18, // 21: service.GetInsuranceBundleRequest.SecondaryFilter.programType:type_name -> insurance_core.ProgramType
	22, // [22:22] is the sub-list for method output_type
	22, // [22:22] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_insurance_bundle_service_models_proto_init() }
func file_insurance_bundle_service_models_proto_init() {
	if File_insurance_bundle_service_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_insurance_bundle_service_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertInsuranceBundleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertInsuranceBundleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecuteEndorsementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecuteEndorsementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCondensedInsuranceBundlesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCondensedInsuranceBundlesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInsuranceBundleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInsuranceBundleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListInsuranceBundleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VersionRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListInsuranceBundlesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecuteEndorsementRequestMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInsuranceBundleRequest_PrimaryFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insurance_bundle_service_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInsuranceBundleRequest_SecondaryFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_insurance_bundle_service_models_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_insurance_bundle_service_models_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_insurance_bundle_service_models_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_insurance_bundle_service_models_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_insurance_bundle_service_models_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_insurance_bundle_service_models_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*GetInsuranceBundleRequest_PrimaryFilter_InsuredId)(nil),
		(*GetInsuranceBundleRequest_PrimaryFilter_InternalId)(nil),
		(*GetInsuranceBundleRequest_PrimaryFilter_ExternalId)(nil),
		(*GetInsuranceBundleRequest_PrimaryFilter_ApplicationId)(nil),
	}
	file_insurance_bundle_service_models_proto_msgTypes[14].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_bundle_service_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insurance_bundle_service_models_proto_goTypes,
		DependencyIndexes: file_insurance_bundle_service_models_proto_depIdxs,
		MessageInfos:      file_insurance_bundle_service_models_proto_msgTypes,
	}.Build()
	File_insurance_bundle_service_models_proto = out.File
	file_insurance_bundle_service_models_proto_rawDesc = nil
	file_insurance_bundle_service_models_proto_goTypes = nil
	file_insurance_bundle_service_models_proto_depIdxs = nil
}
