// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insurance_bundle/service/service.proto

package service

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_insurance_bundle_service_service_proto protoreflect.FileDescriptor

var file_insurance_bundle_service_service_proto_rawDesc = []byte{
	0x0a, 0x26, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x1a, 0x25, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xa2, 0x04, 0x0a, 0x16, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x4d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x12, 0x66, 0x0a, 0x15, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x25, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x12, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x1d, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x73, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x12, 0x2d, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x65, 0x6e,
	0x73, 0x65, 0x64, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x73,
	0x65, 0x64, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x12, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x14, 0x4c, 0x69, 0x73,
	0x74, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x73, 0x12, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x32, 0x5a,
	0x30, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x2d, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_insurance_bundle_service_service_proto_goTypes = []interface{}{
	(*UpsertInsuranceBundleRequest)(nil),          // 0: service.UpsertInsuranceBundleRequest
	(*ExecuteEndorsementRequest)(nil),             // 1: service.ExecuteEndorsementRequest
	(*ListCondensedInsuranceBundlesRequest)(nil),  // 2: service.ListCondensedInsuranceBundlesRequest
	(*GetInsuranceBundleRequest)(nil),             // 3: service.GetInsuranceBundleRequest
	(*ListInsuranceBundleRequest)(nil),            // 4: service.ListInsuranceBundleRequest
	(*UpsertInsuranceBundleResponse)(nil),         // 5: service.UpsertInsuranceBundleResponse
	(*ExecuteEndorsementResponse)(nil),            // 6: service.ExecuteEndorsementResponse
	(*ListCondensedInsuranceBundlesResponse)(nil), // 7: service.ListCondensedInsuranceBundlesResponse
	(*GetInsuranceBundleResponse)(nil),            // 8: service.GetInsuranceBundleResponse
	(*ListInsuranceBundlesResponse)(nil),          // 9: service.ListInsuranceBundlesResponse
}
var file_insurance_bundle_service_service_proto_depIdxs = []int32{
	0, // 0: service.InsuranceBundleManager.UpsertInsuranceBundle:input_type -> service.UpsertInsuranceBundleRequest
	1, // 1: service.InsuranceBundleManager.ExecuteEndorsement:input_type -> service.ExecuteEndorsementRequest
	2, // 2: service.InsuranceBundleManager.ListCondensedInsuranceBundles:input_type -> service.ListCondensedInsuranceBundlesRequest
	3, // 3: service.InsuranceBundleManager.GetInsuranceBundle:input_type -> service.GetInsuranceBundleRequest
	4, // 4: service.InsuranceBundleManager.ListInsuranceBundles:input_type -> service.ListInsuranceBundleRequest
	5, // 5: service.InsuranceBundleManager.UpsertInsuranceBundle:output_type -> service.UpsertInsuranceBundleResponse
	6, // 6: service.InsuranceBundleManager.ExecuteEndorsement:output_type -> service.ExecuteEndorsementResponse
	7, // 7: service.InsuranceBundleManager.ListCondensedInsuranceBundles:output_type -> service.ListCondensedInsuranceBundlesResponse
	8, // 8: service.InsuranceBundleManager.GetInsuranceBundle:output_type -> service.GetInsuranceBundleResponse
	9, // 9: service.InsuranceBundleManager.ListInsuranceBundles:output_type -> service.ListInsuranceBundlesResponse
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_insurance_bundle_service_service_proto_init() }
func file_insurance_bundle_service_service_proto_init() {
	if File_insurance_bundle_service_service_proto != nil {
		return
	}
	file_insurance_bundle_service_models_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insurance_bundle_service_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_insurance_bundle_service_service_proto_goTypes,
		DependencyIndexes: file_insurance_bundle_service_service_proto_depIdxs,
	}.Build()
	File_insurance_bundle_service_service_proto = out.File
	file_insurance_bundle_service_service_proto_rawDesc = nil
	file_insurance_bundle_service_service_proto_goTypes = nil
	file_insurance_bundle_service_service_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// InsuranceBundleManagerClient is the client API for InsuranceBundleManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type InsuranceBundleManagerClient interface {
	UpsertInsuranceBundle(ctx context.Context, in *UpsertInsuranceBundleRequest, opts ...grpc.CallOption) (*UpsertInsuranceBundleResponse, error)
	ExecuteEndorsement(ctx context.Context, in *ExecuteEndorsementRequest, opts ...grpc.CallOption) (*ExecuteEndorsementResponse, error)
	ListCondensedInsuranceBundles(ctx context.Context, in *ListCondensedInsuranceBundlesRequest, opts ...grpc.CallOption) (*ListCondensedInsuranceBundlesResponse, error)
	GetInsuranceBundle(ctx context.Context, in *GetInsuranceBundleRequest, opts ...grpc.CallOption) (*GetInsuranceBundleResponse, error)
	ListInsuranceBundles(ctx context.Context, in *ListInsuranceBundleRequest, opts ...grpc.CallOption) (*ListInsuranceBundlesResponse, error)
}

type insuranceBundleManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewInsuranceBundleManagerClient(cc grpc.ClientConnInterface) InsuranceBundleManagerClient {
	return &insuranceBundleManagerClient{cc}
}

func (c *insuranceBundleManagerClient) UpsertInsuranceBundle(ctx context.Context, in *UpsertInsuranceBundleRequest, opts ...grpc.CallOption) (*UpsertInsuranceBundleResponse, error) {
	out := new(UpsertInsuranceBundleResponse)
	err := c.cc.Invoke(ctx, "/service.InsuranceBundleManager/UpsertInsuranceBundle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *insuranceBundleManagerClient) ExecuteEndorsement(ctx context.Context, in *ExecuteEndorsementRequest, opts ...grpc.CallOption) (*ExecuteEndorsementResponse, error) {
	out := new(ExecuteEndorsementResponse)
	err := c.cc.Invoke(ctx, "/service.InsuranceBundleManager/ExecuteEndorsement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *insuranceBundleManagerClient) ListCondensedInsuranceBundles(ctx context.Context, in *ListCondensedInsuranceBundlesRequest, opts ...grpc.CallOption) (*ListCondensedInsuranceBundlesResponse, error) {
	out := new(ListCondensedInsuranceBundlesResponse)
	err := c.cc.Invoke(ctx, "/service.InsuranceBundleManager/ListCondensedInsuranceBundles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *insuranceBundleManagerClient) GetInsuranceBundle(ctx context.Context, in *GetInsuranceBundleRequest, opts ...grpc.CallOption) (*GetInsuranceBundleResponse, error) {
	out := new(GetInsuranceBundleResponse)
	err := c.cc.Invoke(ctx, "/service.InsuranceBundleManager/GetInsuranceBundle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *insuranceBundleManagerClient) ListInsuranceBundles(ctx context.Context, in *ListInsuranceBundleRequest, opts ...grpc.CallOption) (*ListInsuranceBundlesResponse, error) {
	out := new(ListInsuranceBundlesResponse)
	err := c.cc.Invoke(ctx, "/service.InsuranceBundleManager/ListInsuranceBundles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InsuranceBundleManagerServer is the server API for InsuranceBundleManager service.
type InsuranceBundleManagerServer interface {
	UpsertInsuranceBundle(context.Context, *UpsertInsuranceBundleRequest) (*UpsertInsuranceBundleResponse, error)
	ExecuteEndorsement(context.Context, *ExecuteEndorsementRequest) (*ExecuteEndorsementResponse, error)
	ListCondensedInsuranceBundles(context.Context, *ListCondensedInsuranceBundlesRequest) (*ListCondensedInsuranceBundlesResponse, error)
	GetInsuranceBundle(context.Context, *GetInsuranceBundleRequest) (*GetInsuranceBundleResponse, error)
	ListInsuranceBundles(context.Context, *ListInsuranceBundleRequest) (*ListInsuranceBundlesResponse, error)
}

// UnimplementedInsuranceBundleManagerServer can be embedded to have forward compatible implementations.
type UnimplementedInsuranceBundleManagerServer struct {
}

func (*UnimplementedInsuranceBundleManagerServer) UpsertInsuranceBundle(context.Context, *UpsertInsuranceBundleRequest) (*UpsertInsuranceBundleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertInsuranceBundle not implemented")
}
func (*UnimplementedInsuranceBundleManagerServer) ExecuteEndorsement(context.Context, *ExecuteEndorsementRequest) (*ExecuteEndorsementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteEndorsement not implemented")
}
func (*UnimplementedInsuranceBundleManagerServer) ListCondensedInsuranceBundles(context.Context, *ListCondensedInsuranceBundlesRequest) (*ListCondensedInsuranceBundlesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCondensedInsuranceBundles not implemented")
}
func (*UnimplementedInsuranceBundleManagerServer) GetInsuranceBundle(context.Context, *GetInsuranceBundleRequest) (*GetInsuranceBundleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInsuranceBundle not implemented")
}
func (*UnimplementedInsuranceBundleManagerServer) ListInsuranceBundles(context.Context, *ListInsuranceBundleRequest) (*ListInsuranceBundlesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInsuranceBundles not implemented")
}

func RegisterInsuranceBundleManagerServer(s *grpc.Server, srv InsuranceBundleManagerServer) {
	s.RegisterService(&_InsuranceBundleManager_serviceDesc, srv)
}

func _InsuranceBundleManager_UpsertInsuranceBundle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertInsuranceBundleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InsuranceBundleManagerServer).UpsertInsuranceBundle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/service.InsuranceBundleManager/UpsertInsuranceBundle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InsuranceBundleManagerServer).UpsertInsuranceBundle(ctx, req.(*UpsertInsuranceBundleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InsuranceBundleManager_ExecuteEndorsement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecuteEndorsementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InsuranceBundleManagerServer).ExecuteEndorsement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/service.InsuranceBundleManager/ExecuteEndorsement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InsuranceBundleManagerServer).ExecuteEndorsement(ctx, req.(*ExecuteEndorsementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InsuranceBundleManager_ListCondensedInsuranceBundles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCondensedInsuranceBundlesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InsuranceBundleManagerServer).ListCondensedInsuranceBundles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/service.InsuranceBundleManager/ListCondensedInsuranceBundles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InsuranceBundleManagerServer).ListCondensedInsuranceBundles(ctx, req.(*ListCondensedInsuranceBundlesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InsuranceBundleManager_GetInsuranceBundle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInsuranceBundleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InsuranceBundleManagerServer).GetInsuranceBundle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/service.InsuranceBundleManager/GetInsuranceBundle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InsuranceBundleManagerServer).GetInsuranceBundle(ctx, req.(*GetInsuranceBundleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _InsuranceBundleManager_ListInsuranceBundles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInsuranceBundleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InsuranceBundleManagerServer).ListInsuranceBundles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/service.InsuranceBundleManager/ListInsuranceBundles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InsuranceBundleManagerServer).ListInsuranceBundles(ctx, req.(*ListInsuranceBundleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _InsuranceBundleManager_serviceDesc = grpc.ServiceDesc{
	ServiceName: "service.InsuranceBundleManager",
	HandlerType: (*InsuranceBundleManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertInsuranceBundle",
			Handler:    _InsuranceBundleManager_UpsertInsuranceBundle_Handler,
		},
		{
			MethodName: "ExecuteEndorsement",
			Handler:    _InsuranceBundleManager_ExecuteEndorsement_Handler,
		},
		{
			MethodName: "ListCondensedInsuranceBundles",
			Handler:    _InsuranceBundleManager_ListCondensedInsuranceBundles_Handler,
		},
		{
			MethodName: "GetInsuranceBundle",
			Handler:    _InsuranceBundleManager_GetInsuranceBundle_Handler,
		},
		{
			MethodName: "ListInsuranceBundles",
			Handler:    _InsuranceBundleManager_ListInsuranceBundles_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "insurance_bundle/service/service.proto",
}
