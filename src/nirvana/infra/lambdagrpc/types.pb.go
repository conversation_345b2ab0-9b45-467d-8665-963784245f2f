// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: lambdagrpc/types.proto

package lambdagrpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Input struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address      string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	RpcFullName  string `protobuf:"bytes,3,opt,name=rpcFullName,proto3" json:"rpcFullName,omitempty"`
	ProtoRequest []byte `protobuf:"bytes,4,opt,name=protoRequest,proto3" json:"protoRequest,omitempty"`
}

func (x *Input) Reset() {
	*x = Input{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lambdagrpc_types_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Input) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Input) ProtoMessage() {}

func (x *Input) ProtoReflect() protoreflect.Message {
	mi := &file_lambdagrpc_types_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Input.ProtoReflect.Descriptor instead.
func (*Input) Descriptor() ([]byte, []int) {
	return file_lambdagrpc_types_proto_rawDescGZIP(), []int{0}
}

func (x *Input) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Input) GetRpcFullName() string {
	if x != nil {
		return x.RpcFullName
	}
	return ""
}

func (x *Input) GetProtoRequest() []byte {
	if x != nil {
		return x.ProtoRequest
	}
	return nil
}

type Output struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProtoResponse []byte `protobuf:"bytes,1,opt,name=protoResponse,proto3" json:"protoResponse,omitempty"`
}

func (x *Output) Reset() {
	*x = Output{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lambdagrpc_types_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Output) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Output) ProtoMessage() {}

func (x *Output) ProtoReflect() protoreflect.Message {
	mi := &file_lambdagrpc_types_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Output.ProtoReflect.Descriptor instead.
func (*Output) Descriptor() ([]byte, []int) {
	return file_lambdagrpc_types_proto_rawDescGZIP(), []int{1}
}

func (x *Output) GetProtoResponse() []byte {
	if x != nil {
		return x.ProtoResponse
	}
	return nil
}

type TestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TestInput                 int64                `protobuf:"varint,1,opt,name=testInput,proto3" json:"testInput,omitempty"`
	DeadOnNoHeartbeatInterval *durationpb.Duration `protobuf:"bytes,2,opt,name=deadOnNoHeartbeatInterval,proto3" json:"deadOnNoHeartbeatInterval,omitempty"`
}

func (x *TestRequest) Reset() {
	*x = TestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lambdagrpc_types_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRequest) ProtoMessage() {}

func (x *TestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_lambdagrpc_types_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRequest.ProtoReflect.Descriptor instead.
func (*TestRequest) Descriptor() ([]byte, []int) {
	return file_lambdagrpc_types_proto_rawDescGZIP(), []int{2}
}

func (x *TestRequest) GetTestInput() int64 {
	if x != nil {
		return x.TestInput
	}
	return 0
}

func (x *TestRequest) GetDeadOnNoHeartbeatInterval() *durationpb.Duration {
	if x != nil {
		return x.DeadOnNoHeartbeatInterval
	}
	return nil
}

type TestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TestOutput int64 `protobuf:"varint,1,opt,name=testOutput,proto3" json:"testOutput,omitempty"`
}

func (x *TestResponse) Reset() {
	*x = TestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lambdagrpc_types_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestResponse) ProtoMessage() {}

func (x *TestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_lambdagrpc_types_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestResponse.ProtoReflect.Descriptor instead.
func (*TestResponse) Descriptor() ([]byte, []int) {
	return file_lambdagrpc_types_proto_rawDescGZIP(), []int{3}
}

func (x *TestResponse) GetTestOutput() int64 {
	if x != nil {
		return x.TestOutput
	}
	return 0
}

var File_lambdagrpc_types_proto protoreflect.FileDescriptor

var file_lambdagrpc_types_proto_rawDesc = []byte{
	0x0a, 0x16, 0x6c, 0x61, 0x6d, 0x62, 0x64, 0x61, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6c, 0x61, 0x6d, 0x62, 0x64, 0x61,
	0x67, 0x72, 0x70, 0x63, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x67, 0x0a, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x70, 0x63, 0x46, 0x75,
	0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x70,
	0x63, 0x46, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0c, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x2e, 0x0a,
	0x06, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x84, 0x01,
	0x0a, 0x0b, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x74, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x57, 0x0a, 0x19, 0x64,
	0x65, 0x61, 0x64, 0x4f, 0x6e, 0x4e, 0x6f, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x19, 0x64, 0x65, 0x61, 0x64, 0x4f,
	0x6e, 0x4e, 0x6f, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x22, 0x2e, 0x0a, 0x0c, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x73, 0x74, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x32, 0x4a, 0x0a, 0x08, 0x54, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x6c, 0x79,
	0x12, 0x3e, 0x0a, 0x07, 0x74, 0x65, 0x73, 0x74, 0x52, 0x70, 0x63, 0x12, 0x17, 0x2e, 0x6c, 0x61,
	0x6d, 0x62, 0x64, 0x61, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x6c, 0x61, 0x6d, 0x62, 0x64, 0x61, 0x67, 0x72, 0x70,
	0x63, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_lambdagrpc_types_proto_rawDescOnce sync.Once
	file_lambdagrpc_types_proto_rawDescData = file_lambdagrpc_types_proto_rawDesc
)

func file_lambdagrpc_types_proto_rawDescGZIP() []byte {
	file_lambdagrpc_types_proto_rawDescOnce.Do(func() {
		file_lambdagrpc_types_proto_rawDescData = protoimpl.X.CompressGZIP(file_lambdagrpc_types_proto_rawDescData)
	})
	return file_lambdagrpc_types_proto_rawDescData
}

var file_lambdagrpc_types_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_lambdagrpc_types_proto_goTypes = []interface{}{
	(*Input)(nil),               // 0: lambdagrpc.Input
	(*Output)(nil),              // 1: lambdagrpc.Output
	(*TestRequest)(nil),         // 2: lambdagrpc.testRequest
	(*TestResponse)(nil),        // 3: lambdagrpc.testResponse
	(*durationpb.Duration)(nil), // 4: google.protobuf.Duration
}
var file_lambdagrpc_types_proto_depIdxs = []int32{
	4, // 0: lambdagrpc.testRequest.deadOnNoHeartbeatInterval:type_name -> google.protobuf.Duration
	2, // 1: lambdagrpc.TestOnly.testRpc:input_type -> lambdagrpc.testRequest
	3, // 2: lambdagrpc.TestOnly.testRpc:output_type -> lambdagrpc.testResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_lambdagrpc_types_proto_init() }
func file_lambdagrpc_types_proto_init() {
	if File_lambdagrpc_types_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_lambdagrpc_types_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Input); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lambdagrpc_types_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Output); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lambdagrpc_types_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lambdagrpc_types_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_lambdagrpc_types_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_lambdagrpc_types_proto_goTypes,
		DependencyIndexes: file_lambdagrpc_types_proto_depIdxs,
		MessageInfos:      file_lambdagrpc_types_proto_msgTypes,
	}.Build()
	File_lambdagrpc_types_proto = out.File
	file_lambdagrpc_types_proto_rawDesc = nil
	file_lambdagrpc_types_proto_goTypes = nil
	file_lambdagrpc_types_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// TestOnlyClient is the client API for TestOnly service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TestOnlyClient interface {
	TestRpc(ctx context.Context, in *TestRequest, opts ...grpc.CallOption) (*TestResponse, error)
}

type testOnlyClient struct {
	cc grpc.ClientConnInterface
}

func NewTestOnlyClient(cc grpc.ClientConnInterface) TestOnlyClient {
	return &testOnlyClient{cc}
}

func (c *testOnlyClient) TestRpc(ctx context.Context, in *TestRequest, opts ...grpc.CallOption) (*TestResponse, error) {
	out := new(TestResponse)
	err := c.cc.Invoke(ctx, "/lambdagrpc.TestOnly/testRpc", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TestOnlyServer is the server API for TestOnly service.
type TestOnlyServer interface {
	TestRpc(context.Context, *TestRequest) (*TestResponse, error)
}

// UnimplementedTestOnlyServer can be embedded to have forward compatible implementations.
type UnimplementedTestOnlyServer struct {
}

func (*UnimplementedTestOnlyServer) TestRpc(context.Context, *TestRequest) (*TestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TestRpc not implemented")
}

func RegisterTestOnlyServer(s *grpc.Server, srv TestOnlyServer) {
	s.RegisterService(&_TestOnly_serviceDesc, srv)
}

func _TestOnly_TestRpc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestOnlyServer).TestRpc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/lambdagrpc.TestOnly/TestRpc",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestOnlyServer).TestRpc(ctx, req.(*TestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _TestOnly_serviceDesc = grpc.ServiceDesc{
	ServiceName: "lambdagrpc.TestOnly",
	HandlerType: (*TestOnlyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "testRpc",
			Handler:    _TestOnly_TestRpc_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lambdagrpc/types.proto",
}
