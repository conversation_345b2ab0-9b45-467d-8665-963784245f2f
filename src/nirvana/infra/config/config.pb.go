// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: config/config.proto

package config

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DBSSLMode int32

const (
	DBSSLMode_DISABLED    DBSSLMode = 0
	DBSSLMode_REQUIRED    DBSSLMode = 1
	DBSSLMode_VERIFY_CA   DBSSLMode = 2
	DBSSLMode_VERIFY_FULL DBSSLMode = 3
)

// Enum value maps for DBSSLMode.
var (
	DBSSLMode_name = map[int32]string{
		0: "DISABLED",
		1: "REQUIRED",
		2: "VERIFY_CA",
		3: "VERIFY_FULL",
	}
	DBSSLMode_value = map[string]int32{
		"DISABLED":    0,
		"REQUIRED":    1,
		"VERIFY_CA":   2,
		"VERIFY_FULL": 3,
	}
)

func (x DBSSLMode) Enum() *DBSSLMode {
	p := new(DBSSLMode)
	*p = x
	return p
}

func (x DBSSLMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DBSSLMode) Descriptor() protoreflect.EnumDescriptor {
	return file_config_config_proto_enumTypes[0].Descriptor()
}

func (DBSSLMode) Type() protoreflect.EnumType {
	return &file_config_config_proto_enumTypes[0]
}

func (x DBSSLMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DBSSLMode.Descriptor instead.
func (DBSSLMode) EnumDescriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{0}
}

type Env int32

const (
	Env_DEV     Env = 0
	Env_TEST    Env = 1
	Env_PROD    Env = 2
	Env_STAGING Env = 3
)

// Enum value maps for Env.
var (
	Env_name = map[int32]string{
		0: "DEV",
		1: "TEST",
		2: "PROD",
		3: "STAGING",
	}
	Env_value = map[string]int32{
		"DEV":     0,
		"TEST":    1,
		"PROD":    2,
		"STAGING": 3,
	}
)

func (x Env) Enum() *Env {
	p := new(Env)
	*p = x
	return p
}

func (x Env) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Env) Descriptor() protoreflect.EnumDescriptor {
	return file_config_config_proto_enumTypes[1].Descriptor()
}

func (Env) Type() protoreflect.EnumType {
	return &file_config_config_proto_enumTypes[1]
}

func (x Env) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Env.Descriptor instead.
func (Env) EnumDescriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{1}
}

type ObjectBackend int32

const (
	ObjectBackend_InMemory ObjectBackend = 0
	ObjectBackend_S3       ObjectBackend = 1
)

// Enum value maps for ObjectBackend.
var (
	ObjectBackend_name = map[int32]string{
		0: "InMemory",
		1: "S3",
	}
	ObjectBackend_value = map[string]int32{
		"InMemory": 0,
		"S3":       1,
	}
)

func (x ObjectBackend) Enum() *ObjectBackend {
	p := new(ObjectBackend)
	*p = x
	return p
}

func (x ObjectBackend) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ObjectBackend) Descriptor() protoreflect.EnumDescriptor {
	return file_config_config_proto_enumTypes[2].Descriptor()
}

func (ObjectBackend) Type() protoreflect.EnumType {
	return &file_config_config_proto_enumTypes[2]
}

func (x ObjectBackend) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ObjectBackend.Descriptor instead.
func (ObjectBackend) EnumDescriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{2}
}

type TelematicsDataProvider int32

const (
	TelematicsDataProvider_NexTraq     TelematicsDataProvider = 0
	TelematicsDataProvider_Samsara     TelematicsDataProvider = 1
	TelematicsDataProvider_SpeedGauge  TelematicsDataProvider = 2
	TelematicsDataProvider_KeepTruckin TelematicsDataProvider = 3
	TelematicsDataProvider_Terminal    TelematicsDataProvider = 5
	TelematicsDataProvider_SmartDrive  TelematicsDataProvider = 6
)

// Enum value maps for TelematicsDataProvider.
var (
	TelematicsDataProvider_name = map[int32]string{
		0: "NexTraq",
		1: "Samsara",
		2: "SpeedGauge",
		3: "KeepTruckin",
		5: "Terminal",
		6: "SmartDrive",
	}
	TelematicsDataProvider_value = map[string]int32{
		"NexTraq":     0,
		"Samsara":     1,
		"SpeedGauge":  2,
		"KeepTruckin": 3,
		"Terminal":    5,
		"SmartDrive":  6,
	}
)

func (x TelematicsDataProvider) Enum() *TelematicsDataProvider {
	p := new(TelematicsDataProvider)
	*p = x
	return p
}

func (x TelematicsDataProvider) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TelematicsDataProvider) Descriptor() protoreflect.EnumDescriptor {
	return file_config_config_proto_enumTypes[3].Descriptor()
}

func (TelematicsDataProvider) Type() protoreflect.EnumType {
	return &file_config_config_proto_enumTypes[3]
}

func (x TelematicsDataProvider) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TelematicsDataProvider.Descriptor instead.
func (TelematicsDataProvider) EnumDescriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{3}
}

type TelematicsDecorator int32

const (
	TelematicsDecorator_HereMaps  TelematicsDecorator = 0
	TelematicsDecorator_Geography TelematicsDecorator = 1
	TelematicsDecorator_OpenMeteo TelematicsDecorator = 2
)

// Enum value maps for TelematicsDecorator.
var (
	TelematicsDecorator_name = map[int32]string{
		0: "HereMaps",
		1: "Geography",
		2: "OpenMeteo",
	}
	TelematicsDecorator_value = map[string]int32{
		"HereMaps":  0,
		"Geography": 1,
		"OpenMeteo": 2,
	}
)

func (x TelematicsDecorator) Enum() *TelematicsDecorator {
	p := new(TelematicsDecorator)
	*p = x
	return p
}

func (x TelematicsDecorator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TelematicsDecorator) Descriptor() protoreflect.EnumDescriptor {
	return file_config_config_proto_enumTypes[4].Descriptor()
}

func (TelematicsDecorator) Type() protoreflect.EnumType {
	return &file_config_config_proto_enumTypes[4]
}

func (x TelematicsDecorator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TelematicsDecorator.Descriptor instead.
func (TelematicsDecorator) EnumDescriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{4}
}

type JobberProcessorConfig_StoreConfig_Type int32

const (
	JobberProcessorConfig_StoreConfig_Embedded      JobberProcessorConfig_StoreConfig_Type = 0
	JobberProcessorConfig_StoreConfig_PgTableBacked JobberProcessorConfig_StoreConfig_Type = 1
)

// Enum value maps for JobberProcessorConfig_StoreConfig_Type.
var (
	JobberProcessorConfig_StoreConfig_Type_name = map[int32]string{
		0: "Embedded",
		1: "PgTableBacked",
	}
	JobberProcessorConfig_StoreConfig_Type_value = map[string]int32{
		"Embedded":      0,
		"PgTableBacked": 1,
	}
)

func (x JobberProcessorConfig_StoreConfig_Type) Enum() *JobberProcessorConfig_StoreConfig_Type {
	p := new(JobberProcessorConfig_StoreConfig_Type)
	*p = x
	return p
}

func (x JobberProcessorConfig_StoreConfig_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobberProcessorConfig_StoreConfig_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_config_config_proto_enumTypes[5].Descriptor()
}

func (JobberProcessorConfig_StoreConfig_Type) Type() protoreflect.EnumType {
	return &file_config_config_proto_enumTypes[5]
}

func (x JobberProcessorConfig_StoreConfig_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobberProcessorConfig_StoreConfig_Type.Descriptor instead.
func (JobberProcessorConfig_StoreConfig_Type) EnumDescriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 0, 0}
}

type JobberProcessorConfig_MonitorConfig_Type int32

const (
	JobberProcessorConfig_MonitorConfig_Embedded        JobberProcessorConfig_MonitorConfig_Type = 0
	JobberProcessorConfig_MonitorConfig_EcsMonitor      JobberProcessorConfig_MonitorConfig_Type = 1
	JobberProcessorConfig_MonitorConfig_DbBackedMonitor JobberProcessorConfig_MonitorConfig_Type = 2
)

// Enum value maps for JobberProcessorConfig_MonitorConfig_Type.
var (
	JobberProcessorConfig_MonitorConfig_Type_name = map[int32]string{
		0: "Embedded",
		1: "EcsMonitor",
		2: "DbBackedMonitor",
	}
	JobberProcessorConfig_MonitorConfig_Type_value = map[string]int32{
		"Embedded":        0,
		"EcsMonitor":      1,
		"DbBackedMonitor": 2,
	}
)

func (x JobberProcessorConfig_MonitorConfig_Type) Enum() *JobberProcessorConfig_MonitorConfig_Type {
	p := new(JobberProcessorConfig_MonitorConfig_Type)
	*p = x
	return p
}

func (x JobberProcessorConfig_MonitorConfig_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobberProcessorConfig_MonitorConfig_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_config_config_proto_enumTypes[6].Descriptor()
}

func (JobberProcessorConfig_MonitorConfig_Type) Type() protoreflect.EnumType {
	return &file_config_config_proto_enumTypes[6]
}

func (x JobberProcessorConfig_MonitorConfig_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobberProcessorConfig_MonitorConfig_Type.Descriptor instead.
func (JobberProcessorConfig_MonitorConfig_Type) EnumDescriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 1, 0}
}

type JobberProcessorConfig_RegistryConfig_TaskMetric_Name int32

const (
	JobberProcessorConfig_RegistryConfig_TaskMetric_Duration JobberProcessorConfig_RegistryConfig_TaskMetric_Name = 0
)

// Enum value maps for JobberProcessorConfig_RegistryConfig_TaskMetric_Name.
var (
	JobberProcessorConfig_RegistryConfig_TaskMetric_Name_name = map[int32]string{
		0: "Duration",
	}
	JobberProcessorConfig_RegistryConfig_TaskMetric_Name_value = map[string]int32{
		"Duration": 0,
	}
)

func (x JobberProcessorConfig_RegistryConfig_TaskMetric_Name) Enum() *JobberProcessorConfig_RegistryConfig_TaskMetric_Name {
	p := new(JobberProcessorConfig_RegistryConfig_TaskMetric_Name)
	*p = x
	return p
}

func (x JobberProcessorConfig_RegistryConfig_TaskMetric_Name) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobberProcessorConfig_RegistryConfig_TaskMetric_Name) Descriptor() protoreflect.EnumDescriptor {
	return file_config_config_proto_enumTypes[7].Descriptor()
}

func (JobberProcessorConfig_RegistryConfig_TaskMetric_Name) Type() protoreflect.EnumType {
	return &file_config_config_proto_enumTypes[7]
}

func (x JobberProcessorConfig_RegistryConfig_TaskMetric_Name) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobberProcessorConfig_RegistryConfig_TaskMetric_Name.Descriptor instead.
func (JobberProcessorConfig_RegistryConfig_TaskMetric_Name) EnumDescriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 2, 0, 0}
}

type JobberProcessorConfig_RegistryConfig_JobMetric_Name int32

const (
	JobberProcessorConfig_RegistryConfig_JobMetric_Duration   JobberProcessorConfig_RegistryConfig_JobMetric_Name = 0
	JobberProcessorConfig_RegistryConfig_JobMetric_StartDelay JobberProcessorConfig_RegistryConfig_JobMetric_Name = 1
)

// Enum value maps for JobberProcessorConfig_RegistryConfig_JobMetric_Name.
var (
	JobberProcessorConfig_RegistryConfig_JobMetric_Name_name = map[int32]string{
		0: "Duration",
		1: "StartDelay",
	}
	JobberProcessorConfig_RegistryConfig_JobMetric_Name_value = map[string]int32{
		"Duration":   0,
		"StartDelay": 1,
	}
)

func (x JobberProcessorConfig_RegistryConfig_JobMetric_Name) Enum() *JobberProcessorConfig_RegistryConfig_JobMetric_Name {
	p := new(JobberProcessorConfig_RegistryConfig_JobMetric_Name)
	*p = x
	return p
}

func (x JobberProcessorConfig_RegistryConfig_JobMetric_Name) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobberProcessorConfig_RegistryConfig_JobMetric_Name) Descriptor() protoreflect.EnumDescriptor {
	return file_config_config_proto_enumTypes[8].Descriptor()
}

func (JobberProcessorConfig_RegistryConfig_JobMetric_Name) Type() protoreflect.EnumType {
	return &file_config_config_proto_enumTypes[8]
}

func (x JobberProcessorConfig_RegistryConfig_JobMetric_Name) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobberProcessorConfig_RegistryConfig_JobMetric_Name.Descriptor instead.
func (JobberProcessorConfig_RegistryConfig_JobMetric_Name) EnumDescriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 2, 1, 0}
}

type JobberProcessorConfig_StuckJobDetectorConfig_RuleId int32

const (
	JobberProcessorConfig_StuckJobDetectorConfig_NotPicked                          JobberProcessorConfig_StuckJobDetectorConfig_RuleId = 0
	JobberProcessorConfig_StuckJobDetectorConfig_StuckAfterStart                    JobberProcessorConfig_StuckJobDetectorConfig_RuleId = 1
	JobberProcessorConfig_StuckJobDetectorConfig_StuckAfterPickup                   JobberProcessorConfig_StuckJobDetectorConfig_RuleId = 2
	JobberProcessorConfig_StuckJobDetectorConfig_Omit                               JobberProcessorConfig_StuckJobDetectorConfig_RuleId = 3
	JobberProcessorConfig_StuckJobDetectorConfig_TimeElapsedSinceRequestedStartTime JobberProcessorConfig_StuckJobDetectorConfig_RuleId = 4
)

// Enum value maps for JobberProcessorConfig_StuckJobDetectorConfig_RuleId.
var (
	JobberProcessorConfig_StuckJobDetectorConfig_RuleId_name = map[int32]string{
		0: "NotPicked",
		1: "StuckAfterStart",
		2: "StuckAfterPickup",
		3: "Omit",
		4: "TimeElapsedSinceRequestedStartTime",
	}
	JobberProcessorConfig_StuckJobDetectorConfig_RuleId_value = map[string]int32{
		"NotPicked":                          0,
		"StuckAfterStart":                    1,
		"StuckAfterPickup":                   2,
		"Omit":                               3,
		"TimeElapsedSinceRequestedStartTime": 4,
	}
)

func (x JobberProcessorConfig_StuckJobDetectorConfig_RuleId) Enum() *JobberProcessorConfig_StuckJobDetectorConfig_RuleId {
	p := new(JobberProcessorConfig_StuckJobDetectorConfig_RuleId)
	*p = x
	return p
}

func (x JobberProcessorConfig_StuckJobDetectorConfig_RuleId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobberProcessorConfig_StuckJobDetectorConfig_RuleId) Descriptor() protoreflect.EnumDescriptor {
	return file_config_config_proto_enumTypes[9].Descriptor()
}

func (JobberProcessorConfig_StuckJobDetectorConfig_RuleId) Type() protoreflect.EnumType {
	return &file_config_config_proto_enumTypes[9]
}

func (x JobberProcessorConfig_StuckJobDetectorConfig_RuleId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobberProcessorConfig_StuckJobDetectorConfig_RuleId.Descriptor instead.
func (JobberProcessorConfig_StuckJobDetectorConfig_RuleId) EnumDescriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 3, 0}
}

type JobberProcessorConfig_StuckJobDetectorConfig_ActionId int32

const (
	JobberProcessorConfig_StuckJobDetectorConfig_LogAction       JobberProcessorConfig_StuckJobDetectorConfig_ActionId = 0
	JobberProcessorConfig_StuckJobDetectorConfig_SlackAction     JobberProcessorConfig_StuckJobDetectorConfig_ActionId = 1
	JobberProcessorConfig_StuckJobDetectorConfig_PagerdutyAction JobberProcessorConfig_StuckJobDetectorConfig_ActionId = 2
)

// Enum value maps for JobberProcessorConfig_StuckJobDetectorConfig_ActionId.
var (
	JobberProcessorConfig_StuckJobDetectorConfig_ActionId_name = map[int32]string{
		0: "LogAction",
		1: "SlackAction",
		2: "PagerdutyAction",
	}
	JobberProcessorConfig_StuckJobDetectorConfig_ActionId_value = map[string]int32{
		"LogAction":       0,
		"SlackAction":     1,
		"PagerdutyAction": 2,
	}
)

func (x JobberProcessorConfig_StuckJobDetectorConfig_ActionId) Enum() *JobberProcessorConfig_StuckJobDetectorConfig_ActionId {
	p := new(JobberProcessorConfig_StuckJobDetectorConfig_ActionId)
	*p = x
	return p
}

func (x JobberProcessorConfig_StuckJobDetectorConfig_ActionId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobberProcessorConfig_StuckJobDetectorConfig_ActionId) Descriptor() protoreflect.EnumDescriptor {
	return file_config_config_proto_enumTypes[10].Descriptor()
}

func (JobberProcessorConfig_StuckJobDetectorConfig_ActionId) Type() protoreflect.EnumType {
	return &file_config_config_proto_enumTypes[10]
}

func (x JobberProcessorConfig_StuckJobDetectorConfig_ActionId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobberProcessorConfig_StuckJobDetectorConfig_ActionId.Descriptor instead.
func (JobberProcessorConfig_StuckJobDetectorConfig_ActionId) EnumDescriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 3, 1}
}

type DBSSLConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SslMode             DBSSLMode `protobuf:"varint,1,opt,name=sslMode,proto3,enum=config.DBSSLMode" json:"sslMode,omitempty"`
	SslRootCertFilename *string   `protobuf:"bytes,2,opt,name=sslRootCertFilename,proto3,oneof" json:"sslRootCertFilename,omitempty"`
}

func (x *DBSSLConfig) Reset() {
	*x = DBSSLConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DBSSLConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DBSSLConfig) ProtoMessage() {}

func (x *DBSSLConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DBSSLConfig.ProtoReflect.Descriptor instead.
func (*DBSSLConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{0}
}

func (x *DBSSLConfig) GetSslMode() DBSSLMode {
	if x != nil {
		return x.SslMode
	}
	return DBSSLMode_DISABLED
}

func (x *DBSSLConfig) GetSslRootCertFilename() string {
	if x != nil && x.SslRootCertFilename != nil {
		return *x.SslRootCertFilename
	}
	return ""
}

type DBConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host      string       `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Port      int64        `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Name      string       `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Username  string       `protobuf:"bytes,5,opt,name=username,proto3" json:"username,omitempty"`
	Password  string       `protobuf:"bytes,6,opt,name=password,proto3" json:"password,omitempty"`
	SslConfig *DBSSLConfig `protobuf:"bytes,7,opt,name=sslConfig,proto3,oneof" json:"sslConfig,omitempty"`
}

func (x *DBConfig) Reset() {
	*x = DBConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DBConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DBConfig) ProtoMessage() {}

func (x *DBConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DBConfig.ProtoReflect.Descriptor instead.
func (*DBConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{1}
}

func (x *DBConfig) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *DBConfig) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *DBConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DBConfig) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *DBConfig) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *DBConfig) GetSslConfig() *DBSSLConfig {
	if x != nil {
		return x.SslConfig
	}
	return nil
}

type SnowflakeConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dbname    string  `protobuf:"bytes,1,opt,name=dbname,proto3" json:"dbname,omitempty"`
	Schema    string  `protobuf:"bytes,2,opt,name=schema,proto3" json:"schema,omitempty"`
	Warehouse *string `protobuf:"bytes,3,opt,name=warehouse,proto3,oneof" json:"warehouse,omitempty"`
}

func (x *SnowflakeConfig) Reset() {
	*x = SnowflakeConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnowflakeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnowflakeConfig) ProtoMessage() {}

func (x *SnowflakeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnowflakeConfig.ProtoReflect.Descriptor instead.
func (*SnowflakeConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{2}
}

func (x *SnowflakeConfig) GetDbname() string {
	if x != nil {
		return x.Dbname
	}
	return ""
}

func (x *SnowflakeConfig) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *SnowflakeConfig) GetWarehouse() string {
	if x != nil && x.Warehouse != nil {
		return *x.Warehouse
	}
	return ""
}

// Deprecated: Marked as deprecated in config/config.proto.
type SnowflakeUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Accountname string  `protobuf:"bytes,1,opt,name=accountname,proto3" json:"accountname,omitempty"`
	Username    string  `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Password    string  `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	Role        *string `protobuf:"bytes,4,opt,name=role,proto3,oneof" json:"role,omitempty"`
}

func (x *SnowflakeUser) Reset() {
	*x = SnowflakeUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnowflakeUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnowflakeUser) ProtoMessage() {}

func (x *SnowflakeUser) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnowflakeUser.ProtoReflect.Descriptor instead.
func (*SnowflakeUser) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{3}
}

func (x *SnowflakeUser) GetAccountname() string {
	if x != nil {
		return x.Accountname
	}
	return ""
}

func (x *SnowflakeUser) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *SnowflakeUser) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SnowflakeUser) GetRole() string {
	if x != nil && x.Role != nil {
		return *x.Role
	}
	return ""
}

type SnowflakeKeyPairUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Accountname string  `protobuf:"bytes,1,opt,name=accountname,proto3" json:"accountname,omitempty"`
	Username    string  `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	PrivateKey  string  `protobuf:"bytes,3,opt,name=privateKey,proto3" json:"privateKey,omitempty"`
	Role        *string `protobuf:"bytes,4,opt,name=role,proto3,oneof" json:"role,omitempty"`
}

func (x *SnowflakeKeyPairUser) Reset() {
	*x = SnowflakeKeyPairUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnowflakeKeyPairUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnowflakeKeyPairUser) ProtoMessage() {}

func (x *SnowflakeKeyPairUser) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnowflakeKeyPairUser.ProtoReflect.Descriptor instead.
func (*SnowflakeKeyPairUser) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{4}
}

func (x *SnowflakeKeyPairUser) GetAccountname() string {
	if x != nil {
		return x.Accountname
	}
	return ""
}

func (x *SnowflakeKeyPairUser) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *SnowflakeKeyPairUser) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *SnowflakeKeyPairUser) GetRole() string {
	if x != nil && x.Role != nil {
		return *x.Role
	}
	return ""
}

type Databases struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nirvana       *DBConfig                    `protobuf:"bytes,1,opt,name=nirvana,proto3" json:"nirvana,omitempty"`
	Nhtsa         *DBConfig                    `protobuf:"bytes,2,opt,name=nhtsa,proto3" json:"nhtsa,omitempty"`
	Fmcsa         *DBConfig                    `protobuf:"bytes,3,opt,name=fmcsa,proto3" json:"fmcsa,omitempty"`
	Snowflake     *Databases_SnowflakeSettings `protobuf:"bytes,6,opt,name=snowflake,proto3" json:"snowflake,omitempty"`
	Neo4J         *DBConfig                    `protobuf:"bytes,7,opt,name=neo4j,proto3" json:"neo4j,omitempty"`
	Redis         *DBConfig                    `protobuf:"bytes,8,opt,name=redis,proto3" json:"redis,omitempty"`
	FmcsaReadOnly *DBConfig                    `protobuf:"bytes,9,opt,name=fmcsaReadOnly,proto3" json:"fmcsaReadOnly,omitempty"`
	FmcsaWrite    *DBConfig                    `protobuf:"bytes,10,opt,name=fmcsaWrite,proto3" json:"fmcsaWrite,omitempty"`
	Ds            *DBConfig                    `protobuf:"bytes,11,opt,name=ds,proto3" json:"ds,omitempty"`
}

func (x *Databases) Reset() {
	*x = Databases{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Databases) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Databases) ProtoMessage() {}

func (x *Databases) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Databases.ProtoReflect.Descriptor instead.
func (*Databases) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{5}
}

func (x *Databases) GetNirvana() *DBConfig {
	if x != nil {
		return x.Nirvana
	}
	return nil
}

func (x *Databases) GetNhtsa() *DBConfig {
	if x != nil {
		return x.Nhtsa
	}
	return nil
}

func (x *Databases) GetFmcsa() *DBConfig {
	if x != nil {
		return x.Fmcsa
	}
	return nil
}

func (x *Databases) GetSnowflake() *Databases_SnowflakeSettings {
	if x != nil {
		return x.Snowflake
	}
	return nil
}

func (x *Databases) GetNeo4J() *DBConfig {
	if x != nil {
		return x.Neo4J
	}
	return nil
}

func (x *Databases) GetRedis() *DBConfig {
	if x != nil {
		return x.Redis
	}
	return nil
}

func (x *Databases) GetFmcsaReadOnly() *DBConfig {
	if x != nil {
		return x.FmcsaReadOnly
	}
	return nil
}

func (x *Databases) GetFmcsaWrite() *DBConfig {
	if x != nil {
		return x.FmcsaWrite
	}
	return nil
}

func (x *Databases) GetDs() *DBConfig {
	if x != nil {
		return x.Ds
	}
	return nil
}

type Scrapers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fmcsa string `protobuf:"bytes,2,opt,name=fmcsa,proto3" json:"fmcsa,omitempty"`
}

func (x *Scrapers) Reset() {
	*x = Scrapers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scrapers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scrapers) ProtoMessage() {}

func (x *Scrapers) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scrapers.ProtoReflect.Descriptor instead.
func (*Scrapers) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{6}
}

func (x *Scrapers) GetFmcsa() string {
	if x != nil {
		return x.Fmcsa
	}
	return ""
}

type MVRService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MvrCacheServerAddress string `protobuf:"bytes,1,opt,name=mvrCacheServerAddress,proto3" json:"mvrCacheServerAddress,omitempty"`
	MvrCacheServerPort    int64  `protobuf:"varint,2,opt,name=mvrCacheServerPort,proto3" json:"mvrCacheServerPort,omitempty"`
}

func (x *MVRService) Reset() {
	*x = MVRService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MVRService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MVRService) ProtoMessage() {}

func (x *MVRService) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MVRService.ProtoReflect.Descriptor instead.
func (*MVRService) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{7}
}

func (x *MVRService) GetMvrCacheServerAddress() string {
	if x != nil {
		return x.MvrCacheServerAddress
	}
	return ""
}

func (x *MVRService) GetMvrCacheServerPort() int64 {
	if x != nil {
		return x.MvrCacheServerPort
	}
	return 0
}

type OAuthServices struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Samsara           string `protobuf:"bytes,1,opt,name=Samsara,proto3" json:"Samsara,omitempty"`
	SamsaraSafety     string `protobuf:"bytes,2,opt,name=SamsaraSafety,proto3" json:"SamsaraSafety,omitempty"`
	KeepTruckin       string `protobuf:"bytes,3,opt,name=KeepTruckin,proto3" json:"KeepTruckin,omitempty"`
	KeepTruckinSafety string `protobuf:"bytes,4,opt,name=KeepTruckinSafety,proto3" json:"KeepTruckinSafety,omitempty"`
}

func (x *OAuthServices) Reset() {
	*x = OAuthServices{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OAuthServices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OAuthServices) ProtoMessage() {}

func (x *OAuthServices) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OAuthServices.ProtoReflect.Descriptor instead.
func (*OAuthServices) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{8}
}

func (x *OAuthServices) GetSamsara() string {
	if x != nil {
		return x.Samsara
	}
	return ""
}

func (x *OAuthServices) GetSamsaraSafety() string {
	if x != nil {
		return x.SamsaraSafety
	}
	return ""
}

func (x *OAuthServices) GetKeepTruckin() string {
	if x != nil {
		return x.KeepTruckin
	}
	return ""
}

func (x *OAuthServices) GetKeepTruckinSafety() string {
	if x != nil {
		return x.KeepTruckinSafety
	}
	return ""
}

type Services struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OtelExporterOTLPEndpoint  string         `protobuf:"bytes,2,opt,name=otelExporterOTLPEndpoint,proto3" json:"otelExporterOTLPEndpoint,omitempty"`
	MetaflowServiceAddr       string         `protobuf:"bytes,3,opt,name=metaflowServiceAddr,proto3" json:"metaflowServiceAddr,omitempty"`
	MvrService                *MVRService    `protobuf:"bytes,4,opt,name=mvrService,proto3" json:"mvrService,omitempty"`
	TelematicsGRPCAddr        string         `protobuf:"bytes,5,opt,name=telematicsGRPCAddr,proto3" json:"telematicsGRPCAddr,omitempty"`
	DataInfraJobberAddr       string         `protobuf:"bytes,6,opt,name=dataInfraJobberAddr,proto3" json:"dataInfraJobberAddr,omitempty"`
	Oauth                     *OAuthServices `protobuf:"bytes,7,opt,name=oauth,proto3" json:"oauth,omitempty"`
	QuotingJobberAddr         string         `protobuf:"bytes,8,opt,name=quotingJobberAddr,proto3" json:"quotingJobberAddr,omitempty"`
	VehiclesServiceGRPCAddr   string         `protobuf:"bytes,9,opt,name=vehiclesServiceGRPCAddr,proto3" json:"vehiclesServiceGRPCAddr,omitempty"`
	DistsemSingletonAddr      string         `protobuf:"bytes,11,opt,name=distsemSingletonAddr,proto3" json:"distsemSingletonAddr,omitempty"`
	QuoteScraperAddr          string         `protobuf:"bytes,12,opt,name=quoteScraperAddr,proto3" json:"quoteScraperAddr,omitempty"`
	PdfGen                    *PDFGen        `protobuf:"bytes,13,opt,name=pdfGen,proto3" json:"pdfGen,omitempty"`
	QuotaManagerGRPCAddr      string         `protobuf:"bytes,14,opt,name=quotaManagerGRPCAddr,proto3" json:"quotaManagerGRPCAddr,omitempty"`
	FmcsaDataProviderGRPCAddr string         `protobuf:"bytes,15,opt,name=fmcsaDataProviderGRPCAddr,proto3" json:"fmcsaDataProviderGRPCAddr,omitempty"`
	LlmOpsServiceAddr         string         `protobuf:"bytes,16,opt,name=llmOpsServiceAddr,proto3" json:"llmOpsServiceAddr,omitempty"`
	LlmOpsServiceGRPCAddr     string         `protobuf:"bytes,17,opt,name=llmOpsServiceGRPCAddr,proto3" json:"llmOpsServiceGRPCAddr,omitempty"`
	UwAIServiceGRPCAddr       string         `protobuf:"bytes,18,opt,name=uwAIServiceGRPCAddr,proto3" json:"uwAIServiceGRPCAddr,omitempty"`
}

func (x *Services) Reset() {
	*x = Services{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Services) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Services) ProtoMessage() {}

func (x *Services) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Services.ProtoReflect.Descriptor instead.
func (*Services) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{9}
}

func (x *Services) GetOtelExporterOTLPEndpoint() string {
	if x != nil {
		return x.OtelExporterOTLPEndpoint
	}
	return ""
}

func (x *Services) GetMetaflowServiceAddr() string {
	if x != nil {
		return x.MetaflowServiceAddr
	}
	return ""
}

func (x *Services) GetMvrService() *MVRService {
	if x != nil {
		return x.MvrService
	}
	return nil
}

func (x *Services) GetTelematicsGRPCAddr() string {
	if x != nil {
		return x.TelematicsGRPCAddr
	}
	return ""
}

func (x *Services) GetDataInfraJobberAddr() string {
	if x != nil {
		return x.DataInfraJobberAddr
	}
	return ""
}

func (x *Services) GetOauth() *OAuthServices {
	if x != nil {
		return x.Oauth
	}
	return nil
}

func (x *Services) GetQuotingJobberAddr() string {
	if x != nil {
		return x.QuotingJobberAddr
	}
	return ""
}

func (x *Services) GetVehiclesServiceGRPCAddr() string {
	if x != nil {
		return x.VehiclesServiceGRPCAddr
	}
	return ""
}

func (x *Services) GetDistsemSingletonAddr() string {
	if x != nil {
		return x.DistsemSingletonAddr
	}
	return ""
}

func (x *Services) GetQuoteScraperAddr() string {
	if x != nil {
		return x.QuoteScraperAddr
	}
	return ""
}

func (x *Services) GetPdfGen() *PDFGen {
	if x != nil {
		return x.PdfGen
	}
	return nil
}

func (x *Services) GetQuotaManagerGRPCAddr() string {
	if x != nil {
		return x.QuotaManagerGRPCAddr
	}
	return ""
}

func (x *Services) GetFmcsaDataProviderGRPCAddr() string {
	if x != nil {
		return x.FmcsaDataProviderGRPCAddr
	}
	return ""
}

func (x *Services) GetLlmOpsServiceAddr() string {
	if x != nil {
		return x.LlmOpsServiceAddr
	}
	return ""
}

func (x *Services) GetLlmOpsServiceGRPCAddr() string {
	if x != nil {
		return x.LlmOpsServiceGRPCAddr
	}
	return ""
}

func (x *Services) GetUwAIServiceGRPCAddr() string {
	if x != nil {
		return x.UwAIServiceGRPCAddr
	}
	return ""
}

type PDFGen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SemaphoreWeight int64  `protobuf:"varint,1,opt,name=semaphoreWeight,proto3" json:"semaphoreWeight,omitempty"`
	PdfgenGRPCAddr  string `protobuf:"bytes,2,opt,name=pdfgenGRPCAddr,proto3" json:"pdfgenGRPCAddr,omitempty"`
}

func (x *PDFGen) Reset() {
	*x = PDFGen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PDFGen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PDFGen) ProtoMessage() {}

func (x *PDFGen) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PDFGen.ProtoReflect.Descriptor instead.
func (*PDFGen) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{10}
}

func (x *PDFGen) GetSemaphoreWeight() int64 {
	if x != nil {
		return x.SemaphoreWeight
	}
	return 0
}

func (x *PDFGen) GetPdfgenGRPCAddr() string {
	if x != nil {
		return x.PdfgenGRPCAddr
	}
	return ""
}

type Tracing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled    bool    `protobuf:"varint,1,opt,name=Enabled,proto3" json:"Enabled,omitempty"`
	SampleRate float64 `protobuf:"fixed64,2,opt,name=SampleRate,proto3" json:"SampleRate,omitempty"`
}

func (x *Tracing) Reset() {
	*x = Tracing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tracing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tracing) ProtoMessage() {}

func (x *Tracing) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tracing.ProtoReflect.Descriptor instead.
func (*Tracing) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{11}
}

func (x *Tracing) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Tracing) GetSampleRate() float64 {
	if x != nil {
		return x.SampleRate
	}
	return 0
}

type Logging struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HumanReadable bool   `protobuf:"varint,1,opt,name=HumanReadable,proto3" json:"HumanReadable,omitempty"`
	Level         string `protobuf:"bytes,2,opt,name=Level,proto3" json:"Level,omitempty"`
	PanicLevel    string `protobuf:"bytes,3,opt,name=PanicLevel,proto3" json:"PanicLevel,omitempty"`
	FxLevel       string `protobuf:"bytes,4,opt,name=FxLevel,proto3" json:"FxLevel,omitempty"`
}

func (x *Logging) Reset() {
	*x = Logging{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Logging) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Logging) ProtoMessage() {}

func (x *Logging) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Logging.ProtoReflect.Descriptor instead.
func (*Logging) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{12}
}

func (x *Logging) GetHumanReadable() bool {
	if x != nil {
		return x.HumanReadable
	}
	return false
}

func (x *Logging) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *Logging) GetPanicLevel() string {
	if x != nil {
		return x.PanicLevel
	}
	return ""
}

func (x *Logging) GetFxLevel() string {
	if x != nil {
		return x.FxLevel
	}
	return ""
}

type Metrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuntimeEnabled       bool  `protobuf:"varint,1,opt,name=runtime_enabled,json=runtimeEnabled,proto3" json:"runtime_enabled,omitempty"`
	RuntimeCollectorSecs int64 `protobuf:"varint,2,opt,name=runtime_collector_secs,json=runtimeCollectorSecs,proto3" json:"runtime_collector_secs,omitempty"`
}

func (x *Metrics) Reset() {
	*x = Metrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metrics) ProtoMessage() {}

func (x *Metrics) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metrics.ProtoReflect.Descriptor instead.
func (*Metrics) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{13}
}

func (x *Metrics) GetRuntimeEnabled() bool {
	if x != nil {
		return x.RuntimeEnabled
	}
	return false
}

func (x *Metrics) GetRuntimeCollectorSecs() int64 {
	if x != nil {
		return x.RuntimeCollectorSecs
	}
	return 0
}

type LambdaGrpc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FunctionName string `protobuf:"bytes,1,opt,name=functionName,proto3" json:"functionName,omitempty"`
}

func (x *LambdaGrpc) Reset() {
	*x = LambdaGrpc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LambdaGrpc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LambdaGrpc) ProtoMessage() {}

func (x *LambdaGrpc) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LambdaGrpc.ProtoReflect.Descriptor instead.
func (*LambdaGrpc) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{14}
}

func (x *LambdaGrpc) GetFunctionName() string {
	if x != nil {
		return x.FunctionName
	}
	return ""
}

type Infra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tracing          *Tracing    `protobuf:"bytes,1,opt,name=tracing,proto3" json:"tracing,omitempty"`
	Logging          *Logging    `protobuf:"bytes,2,opt,name=logging,proto3" json:"logging,omitempty"`
	LambdaGrpc       *LambdaGrpc `protobuf:"bytes,3,opt,name=lambdaGrpc,proto3" json:"lambdaGrpc,omitempty"`
	Metrics          *Metrics    `protobuf:"bytes,4,opt,name=metrics,proto3" json:"metrics,omitempty"`
	SlackEventsToken string      `protobuf:"bytes,5,opt,name=slackEventsToken,proto3" json:"slackEventsToken,omitempty"`
}

func (x *Infra) Reset() {
	*x = Infra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Infra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Infra) ProtoMessage() {}

func (x *Infra) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Infra.ProtoReflect.Descriptor instead.
func (*Infra) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{15}
}

func (x *Infra) GetTracing() *Tracing {
	if x != nil {
		return x.Tracing
	}
	return nil
}

func (x *Infra) GetLogging() *Logging {
	if x != nil {
		return x.Logging
	}
	return nil
}

func (x *Infra) GetLambdaGrpc() *LambdaGrpc {
	if x != nil {
		return x.LambdaGrpc
	}
	return nil
}

func (x *Infra) GetMetrics() *Metrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

func (x *Infra) GetSlackEventsToken() string {
	if x != nil {
		return x.SlackEventsToken
	}
	return ""
}

type AWS struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Workspace string `protobuf:"bytes,1,opt,name=workspace,proto3" json:"workspace,omitempty"`
}

func (x *AWS) Reset() {
	*x = AWS{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AWS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AWS) ProtoMessage() {}

func (x *AWS) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AWS.ProtoReflect.Descriptor instead.
func (*AWS) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{16}
}

func (x *AWS) GetWorkspace() string {
	if x != nil {
		return x.Workspace
	}
	return ""
}

type Verisk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UseCertAuth       bool   `protobuf:"varint,1,opt,name=useCertAuth,proto3" json:"useCertAuth,omitempty"`
	BasicAuthProdUrl  string `protobuf:"bytes,2,opt,name=basicAuthProdUrl,proto3" json:"basicAuthProdUrl,omitempty"`
	CertAuthProdUrl   string `protobuf:"bytes,3,opt,name=certAuthProdUrl,proto3" json:"certAuthProdUrl,omitempty"`
	LexisNexisProdUrl string `protobuf:"bytes,4,opt,name=lexisNexisProdUrl,proto3" json:"lexisNexisProdUrl,omitempty"`
	LexisNexisTestUrl string `protobuf:"bytes,5,opt,name=lexisNexisTestUrl,proto3" json:"lexisNexisTestUrl,omitempty"`
}

func (x *Verisk) Reset() {
	*x = Verisk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Verisk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Verisk) ProtoMessage() {}

func (x *Verisk) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Verisk.ProtoReflect.Descriptor instead.
func (*Verisk) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{17}
}

func (x *Verisk) GetUseCertAuth() bool {
	if x != nil {
		return x.UseCertAuth
	}
	return false
}

func (x *Verisk) GetBasicAuthProdUrl() string {
	if x != nil {
		return x.BasicAuthProdUrl
	}
	return ""
}

func (x *Verisk) GetCertAuthProdUrl() string {
	if x != nil {
		return x.CertAuthProdUrl
	}
	return ""
}

func (x *Verisk) GetLexisNexisProdUrl() string {
	if x != nil {
		return x.LexisNexisProdUrl
	}
	return ""
}

func (x *Verisk) GetLexisNexisTestUrl() string {
	if x != nil {
		return x.LexisNexisTestUrl
	}
	return ""
}

type S3Credentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bucket          string `protobuf:"bytes,1,opt,name=Bucket,proto3" json:"Bucket,omitempty"`
	AccessKeyId     string `protobuf:"bytes,2,opt,name=AccessKeyId,proto3" json:"AccessKeyId,omitempty"`
	SecretAccessKey string `protobuf:"bytes,3,opt,name=SecretAccessKey,proto3" json:"SecretAccessKey,omitempty"`
}

func (x *S3Credentials) Reset() {
	*x = S3Credentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S3Credentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S3Credentials) ProtoMessage() {}

func (x *S3Credentials) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S3Credentials.ProtoReflect.Descriptor instead.
func (*S3Credentials) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{18}
}

func (x *S3Credentials) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *S3Credentials) GetAccessKeyId() string {
	if x != nil {
		return x.AccessKeyId
	}
	return ""
}

func (x *S3Credentials) GetSecretAccessKey() string {
	if x != nil {
		return x.SecretAccessKey
	}
	return ""
}

type TelematicsOAuthAppCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	AppSecret string `protobuf:"bytes,2,opt,name=appSecret,proto3" json:"appSecret,omitempty"`
}

func (x *TelematicsOAuthAppCredentials) Reset() {
	*x = TelematicsOAuthAppCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsOAuthAppCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsOAuthAppCredentials) ProtoMessage() {}

func (x *TelematicsOAuthAppCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsOAuthAppCredentials.ProtoReflect.Descriptor instead.
func (*TelematicsOAuthAppCredentials) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{19}
}

func (x *TelematicsOAuthAppCredentials) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *TelematicsOAuthAppCredentials) GetAppSecret() string {
	if x != nil {
		return x.AppSecret
	}
	return ""
}

type DBTCloudCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountID string `protobuf:"bytes,1,opt,name=accountID,proto3" json:"accountID,omitempty"`
	ApiKey    string `protobuf:"bytes,2,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
}

func (x *DBTCloudCredentials) Reset() {
	*x = DBTCloudCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DBTCloudCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DBTCloudCredentials) ProtoMessage() {}

func (x *DBTCloudCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DBTCloudCredentials.ProtoReflect.Descriptor instead.
func (*DBTCloudCredentials) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{20}
}

func (x *DBTCloudCredentials) GetAccountID() string {
	if x != nil {
		return x.AccountID
	}
	return ""
}

func (x *DBTCloudCredentials) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

type TerminalCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SecretKey      string `protobuf:"bytes,1,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	PublishableKey string `protobuf:"bytes,2,opt,name=publishable_key,json=publishableKey,proto3" json:"publishable_key,omitempty"`
}

func (x *TerminalCredentials) Reset() {
	*x = TerminalCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TerminalCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalCredentials) ProtoMessage() {}

func (x *TerminalCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalCredentials.ProtoReflect.Descriptor instead.
func (*TerminalCredentials) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{21}
}

func (x *TerminalCredentials) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *TerminalCredentials) GetPublishableKey() string {
	if x != nil {
		return x.PublishableKey
	}
	return ""
}

type HereMapsCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiKey string `protobuf:"bytes,1,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
}

func (x *HereMapsCredentials) Reset() {
	*x = HereMapsCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HereMapsCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HereMapsCredentials) ProtoMessage() {}

func (x *HereMapsCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HereMapsCredentials.ProtoReflect.Descriptor instead.
func (*HereMapsCredentials) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{22}
}

func (x *HereMapsCredentials) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

type OpenMeteoCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiKey string `protobuf:"bytes,1,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
}

func (x *OpenMeteoCredentials) Reset() {
	*x = OpenMeteoCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenMeteoCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenMeteoCredentials) ProtoMessage() {}

func (x *OpenMeteoCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenMeteoCredentials.ProtoReflect.Descriptor instead.
func (*OpenMeteoCredentials) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{23}
}

func (x *OpenMeteoCredentials) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

type CmtCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiKey     string `protobuf:"bytes,1,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
	Passphrase string `protobuf:"bytes,2,opt,name=passphrase,proto3" json:"passphrase,omitempty"`
}

func (x *CmtCredentials) Reset() {
	*x = CmtCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmtCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmtCredentials) ProtoMessage() {}

func (x *CmtCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmtCredentials.ProtoReflect.Descriptor instead.
func (*CmtCredentials) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{24}
}

func (x *CmtCredentials) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *CmtCredentials) GetPassphrase() string {
	if x != nil {
		return x.Passphrase
	}
	return ""
}

type TelematicsCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Samsara           *TelematicsOAuthAppCredentials `protobuf:"bytes,1,opt,name=samsara,proto3" json:"samsara,omitempty"`
	SpeedGauge        *S3Credentials                 `protobuf:"bytes,2,opt,name=speedGauge,proto3" json:"speedGauge,omitempty"`
	MillimanApiKey    string                         `protobuf:"bytes,3,opt,name=millimanApiKey,proto3" json:"millimanApiKey,omitempty"`
	KeepTruckin       *TelematicsOAuthAppCredentials `protobuf:"bytes,4,opt,name=keepTruckin,proto3" json:"keepTruckin,omitempty"`
	SamsaraSafety     *TelematicsOAuthAppCredentials `protobuf:"bytes,5,opt,name=samsaraSafety,proto3" json:"samsaraSafety,omitempty"`
	KeepTruckinSafety *TelematicsOAuthAppCredentials `protobuf:"bytes,6,opt,name=keepTruckinSafety,proto3" json:"keepTruckinSafety,omitempty"`
	Terminal          *TerminalCredentials           `protobuf:"bytes,8,opt,name=terminal,proto3" json:"terminal,omitempty"`
	Heremaps          *HereMapsCredentials           `protobuf:"bytes,9,opt,name=heremaps,proto3" json:"heremaps,omitempty"`
	Openmeteo         *OpenMeteoCredentials          `protobuf:"bytes,10,opt,name=openmeteo,proto3" json:"openmeteo,omitempty"`
	CarfaxUsername    string                         `protobuf:"bytes,11,opt,name=carfaxUsername,proto3" json:"carfaxUsername,omitempty"`
	Cmt               *CmtCredentials                `protobuf:"bytes,12,opt,name=cmt,proto3" json:"cmt,omitempty"`
}

func (x *TelematicsCredentials) Reset() {
	*x = TelematicsCredentials{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsCredentials) ProtoMessage() {}

func (x *TelematicsCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsCredentials.ProtoReflect.Descriptor instead.
func (*TelematicsCredentials) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{25}
}

func (x *TelematicsCredentials) GetSamsara() *TelematicsOAuthAppCredentials {
	if x != nil {
		return x.Samsara
	}
	return nil
}

func (x *TelematicsCredentials) GetSpeedGauge() *S3Credentials {
	if x != nil {
		return x.SpeedGauge
	}
	return nil
}

func (x *TelematicsCredentials) GetMillimanApiKey() string {
	if x != nil {
		return x.MillimanApiKey
	}
	return ""
}

func (x *TelematicsCredentials) GetKeepTruckin() *TelematicsOAuthAppCredentials {
	if x != nil {
		return x.KeepTruckin
	}
	return nil
}

func (x *TelematicsCredentials) GetSamsaraSafety() *TelematicsOAuthAppCredentials {
	if x != nil {
		return x.SamsaraSafety
	}
	return nil
}

func (x *TelematicsCredentials) GetKeepTruckinSafety() *TelematicsOAuthAppCredentials {
	if x != nil {
		return x.KeepTruckinSafety
	}
	return nil
}

func (x *TelematicsCredentials) GetTerminal() *TerminalCredentials {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *TelematicsCredentials) GetHeremaps() *HereMapsCredentials {
	if x != nil {
		return x.Heremaps
	}
	return nil
}

func (x *TelematicsCredentials) GetOpenmeteo() *OpenMeteoCredentials {
	if x != nil {
		return x.Openmeteo
	}
	return nil
}

func (x *TelematicsCredentials) GetCarfaxUsername() string {
	if x != nil {
		return x.CarfaxUsername
	}
	return ""
}

func (x *TelematicsCredentials) GetCmt() *CmtCredentials {
	if x != nil {
		return x.Cmt
	}
	return nil
}

type KeepTruckinIntegration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxConcurrentReqs int32 `protobuf:"varint,6,opt,name=maxConcurrentReqs,proto3" json:"maxConcurrentReqs,omitempty"`
}

func (x *KeepTruckinIntegration) Reset() {
	*x = KeepTruckinIntegration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepTruckinIntegration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepTruckinIntegration) ProtoMessage() {}

func (x *KeepTruckinIntegration) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepTruckinIntegration.ProtoReflect.Descriptor instead.
func (*KeepTruckinIntegration) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{26}
}

func (x *KeepTruckinIntegration) GetMaxConcurrentReqs() int32 {
	if x != nil {
		return x.MaxConcurrentReqs
	}
	return 0
}

type RateLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rate  float32 `protobuf:"fixed32,1,opt,name=rate,proto3" json:"rate,omitempty"`
	Burst int32   `protobuf:"varint,2,opt,name=burst,proto3" json:"burst,omitempty"`
}

func (x *RateLimit) Reset() {
	*x = RateLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RateLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RateLimit) ProtoMessage() {}

func (x *RateLimit) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RateLimit.ProtoReflect.Descriptor instead.
func (*RateLimit) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{27}
}

func (x *RateLimit) GetRate() float32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *RateLimit) GetBurst() int32 {
	if x != nil {
		return x.Burst
	}
	return 0
}

type SamsaraIntegration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Global    *RateLimit            `protobuf:"bytes,1,opt,name=global,proto3" json:"global,omitempty"`
	Endpoints map[string]*RateLimit `protobuf:"bytes,2,rep,name=endpoints,proto3" json:"endpoints,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SamsaraIntegration) Reset() {
	*x = SamsaraIntegration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SamsaraIntegration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SamsaraIntegration) ProtoMessage() {}

func (x *SamsaraIntegration) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SamsaraIntegration.ProtoReflect.Descriptor instead.
func (*SamsaraIntegration) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{28}
}

func (x *SamsaraIntegration) GetGlobal() *RateLimit {
	if x != nil {
		return x.Global
	}
	return nil
}

func (x *SamsaraIntegration) GetEndpoints() map[string]*RateLimit {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

type URL struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scheme string `protobuf:"bytes,1,opt,name=scheme,proto3" json:"scheme,omitempty"`
	Host   string `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	Path   string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *URL) Reset() {
	*x = URL{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *URL) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*URL) ProtoMessage() {}

func (x *URL) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use URL.ProtoReflect.Descriptor instead.
func (*URL) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{29}
}

func (x *URL) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *URL) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *URL) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type MillimanIntegration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url                  *URL    `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	MaxRequestsPerSecond float64 `protobuf:"fixed64,3,opt,name=maxRequestsPerSecond,proto3" json:"maxRequestsPerSecond,omitempty"`
	MaxRetryCount        int32   `protobuf:"varint,4,opt,name=maxRetryCount,proto3" json:"maxRetryCount,omitempty"`
}

func (x *MillimanIntegration) Reset() {
	*x = MillimanIntegration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MillimanIntegration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MillimanIntegration) ProtoMessage() {}

func (x *MillimanIntegration) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MillimanIntegration.ProtoReflect.Descriptor instead.
func (*MillimanIntegration) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{30}
}

func (x *MillimanIntegration) GetUrl() *URL {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *MillimanIntegration) GetMaxRequestsPerSecond() float64 {
	if x != nil {
		return x.MaxRequestsPerSecond
	}
	return 0
}

func (x *MillimanIntegration) GetMaxRetryCount() int32 {
	if x != nil {
		return x.MaxRetryCount
	}
	return 0
}

type TerminalIntegration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url                         *URL                  `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	GlobalRateLimit             *RateLimit            `protobuf:"bytes,3,opt,name=globalRateLimit,proto3" json:"globalRateLimit,omitempty"`
	ConnectionEndpointRateLimit map[string]*RateLimit `protobuf:"bytes,4,rep,name=connectionEndpointRateLimit,proto3" json:"connectionEndpointRateLimit,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *TerminalIntegration) Reset() {
	*x = TerminalIntegration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TerminalIntegration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalIntegration) ProtoMessage() {}

func (x *TerminalIntegration) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalIntegration.ProtoReflect.Descriptor instead.
func (*TerminalIntegration) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{31}
}

func (x *TerminalIntegration) GetUrl() *URL {
	if x != nil {
		return x.Url
	}
	return nil
}

func (x *TerminalIntegration) GetGlobalRateLimit() *RateLimit {
	if x != nil {
		return x.GlobalRateLimit
	}
	return nil
}

func (x *TerminalIntegration) GetConnectionEndpointRateLimit() map[string]*RateLimit {
	if x != nil {
		return x.ConnectionEndpointRateLimit
	}
	return nil
}

type OpenMeteoIntegration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HistoricalForecastHostname string     `protobuf:"bytes,1,opt,name=historical_forecast_hostname,json=historicalForecastHostname,proto3" json:"historical_forecast_hostname,omitempty"`
	ArchiveHostname            string     `protobuf:"bytes,2,opt,name=archive_hostname,json=archiveHostname,proto3" json:"archive_hostname,omitempty"`
	Ratelimit                  *RateLimit `protobuf:"bytes,3,opt,name=ratelimit,proto3" json:"ratelimit,omitempty"`
	HighResolutionCutoffDate   string     `protobuf:"bytes,4,opt,name=high_resolution_cutoff_date,json=highResolutionCutoffDate,proto3" json:"high_resolution_cutoff_date,omitempty"`
	MaxBatchSize               int32      `protobuf:"varint,5,opt,name=max_batch_size,json=maxBatchSize,proto3" json:"max_batch_size,omitempty"`
}

func (x *OpenMeteoIntegration) Reset() {
	*x = OpenMeteoIntegration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenMeteoIntegration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenMeteoIntegration) ProtoMessage() {}

func (x *OpenMeteoIntegration) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenMeteoIntegration.ProtoReflect.Descriptor instead.
func (*OpenMeteoIntegration) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{32}
}

func (x *OpenMeteoIntegration) GetHistoricalForecastHostname() string {
	if x != nil {
		return x.HistoricalForecastHostname
	}
	return ""
}

func (x *OpenMeteoIntegration) GetArchiveHostname() string {
	if x != nil {
		return x.ArchiveHostname
	}
	return ""
}

func (x *OpenMeteoIntegration) GetRatelimit() *RateLimit {
	if x != nil {
		return x.Ratelimit
	}
	return nil
}

func (x *OpenMeteoIntegration) GetHighResolutionCutoffDate() string {
	if x != nil {
		return x.HighResolutionCutoffDate
	}
	return ""
}

func (x *OpenMeteoIntegration) GetMaxBatchSize() int32 {
	if x != nil {
		return x.MaxBatchSize
	}
	return 0
}

type CmtIntegration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host         string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	InputBucket  string `protobuf:"bytes,2,opt,name=inputBucket,proto3" json:"inputBucket,omitempty"`
	OutputBucket string `protobuf:"bytes,3,opt,name=outputBucket,proto3" json:"outputBucket,omitempty"`
}

func (x *CmtIntegration) Reset() {
	*x = CmtIntegration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmtIntegration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmtIntegration) ProtoMessage() {}

func (x *CmtIntegration) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmtIntegration.ProtoReflect.Descriptor instead.
func (*CmtIntegration) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{33}
}

func (x *CmtIntegration) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *CmtIntegration) GetInputBucket() string {
	if x != nil {
		return x.InputBucket
	}
	return ""
}

func (x *CmtIntegration) GetOutputBucket() string {
	if x != nil {
		return x.OutputBucket
	}
	return ""
}

type TelematicsIntegrationConfigurations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KeepTruckin *KeepTruckinIntegration `protobuf:"bytes,1,opt,name=keepTruckin,proto3" json:"keepTruckin,omitempty"`
	Milliman    *MillimanIntegration    `protobuf:"bytes,2,opt,name=milliman,proto3" json:"milliman,omitempty"`
	Samsara     *SamsaraIntegration     `protobuf:"bytes,3,opt,name=samsara,proto3" json:"samsara,omitempty"`
	Terminal    *TerminalIntegration    `protobuf:"bytes,5,opt,name=terminal,proto3" json:"terminal,omitempty"`
	Openmeteo   *OpenMeteoIntegration   `protobuf:"bytes,6,opt,name=openmeteo,proto3" json:"openmeteo,omitempty"`
	CarfaxHost  string                  `protobuf:"bytes,7,opt,name=carfaxHost,proto3" json:"carfaxHost,omitempty"`
	Cmt         *CmtIntegration         `protobuf:"bytes,8,opt,name=cmt,proto3" json:"cmt,omitempty"`
}

func (x *TelematicsIntegrationConfigurations) Reset() {
	*x = TelematicsIntegrationConfigurations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsIntegrationConfigurations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsIntegrationConfigurations) ProtoMessage() {}

func (x *TelematicsIntegrationConfigurations) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsIntegrationConfigurations.ProtoReflect.Descriptor instead.
func (*TelematicsIntegrationConfigurations) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{34}
}

func (x *TelematicsIntegrationConfigurations) GetKeepTruckin() *KeepTruckinIntegration {
	if x != nil {
		return x.KeepTruckin
	}
	return nil
}

func (x *TelematicsIntegrationConfigurations) GetMilliman() *MillimanIntegration {
	if x != nil {
		return x.Milliman
	}
	return nil
}

func (x *TelematicsIntegrationConfigurations) GetSamsara() *SamsaraIntegration {
	if x != nil {
		return x.Samsara
	}
	return nil
}

func (x *TelematicsIntegrationConfigurations) GetTerminal() *TerminalIntegration {
	if x != nil {
		return x.Terminal
	}
	return nil
}

func (x *TelematicsIntegrationConfigurations) GetOpenmeteo() *OpenMeteoIntegration {
	if x != nil {
		return x.Openmeteo
	}
	return nil
}

func (x *TelematicsIntegrationConfigurations) GetCarfaxHost() string {
	if x != nil {
		return x.CarfaxHost
	}
	return ""
}

func (x *TelematicsIntegrationConfigurations) GetCmt() *CmtIntegration {
	if x != nil {
		return x.Cmt
	}
	return nil
}

type SlidingWindowMillimanAccurateScorePipeline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountMonths int32 `protobuf:"varint,1,opt,name=countMonths,proto3" json:"countMonths,omitempty"`
}

func (x *SlidingWindowMillimanAccurateScorePipeline) Reset() {
	*x = SlidingWindowMillimanAccurateScorePipeline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlidingWindowMillimanAccurateScorePipeline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlidingWindowMillimanAccurateScorePipeline) ProtoMessage() {}

func (x *SlidingWindowMillimanAccurateScorePipeline) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlidingWindowMillimanAccurateScorePipeline.ProtoReflect.Descriptor instead.
func (*SlidingWindowMillimanAccurateScorePipeline) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{35}
}

func (x *SlidingWindowMillimanAccurateScorePipeline) GetCountMonths() int32 {
	if x != nil {
		return x.CountMonths
	}
	return 0
}

type TelematicsPipelines struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SlidingWindowMillimanAccurateScore  *SlidingWindowMillimanAccurateScorePipeline `protobuf:"bytes,2,opt,name=slidingWindowMillimanAccurateScore,proto3" json:"slidingWindowMillimanAccurateScore,omitempty"`
	TelematicsPipelineRetryAttemptLimit int32                                       `protobuf:"varint,3,opt,name=telematicsPipelineRetryAttemptLimit,proto3" json:"telematicsPipelineRetryAttemptLimit,omitempty"`
}

func (x *TelematicsPipelines) Reset() {
	*x = TelematicsPipelines{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsPipelines) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsPipelines) ProtoMessage() {}

func (x *TelematicsPipelines) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsPipelines.ProtoReflect.Descriptor instead.
func (*TelematicsPipelines) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{36}
}

func (x *TelematicsPipelines) GetSlidingWindowMillimanAccurateScore() *SlidingWindowMillimanAccurateScorePipeline {
	if x != nil {
		return x.SlidingWindowMillimanAccurateScore
	}
	return nil
}

func (x *TelematicsPipelines) GetTelematicsPipelineRetryAttemptLimit() int32 {
	if x != nil {
		return x.TelematicsPipelineRetryAttemptLimit
	}
	return 0
}

type Telematics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataPlatformBackend ObjectBackend                        `protobuf:"varint,1,opt,name=DataPlatformBackend,proto3,enum=config.ObjectBackend" json:"DataPlatformBackend,omitempty"`
	EnabledProviders    []TelematicsDataProvider             `protobuf:"varint,3,rep,packed,name=enabledProviders,proto3,enum=config.TelematicsDataProvider" json:"enabledProviders,omitempty"`
	Credentials         *TelematicsCredentials               `protobuf:"bytes,4,opt,name=credentials,proto3" json:"credentials,omitempty"`
	Pipelines           *TelematicsPipelines                 `protobuf:"bytes,5,opt,name=pipelines,proto3" json:"pipelines,omitempty"`
	Integrations        *TelematicsIntegrationConfigurations `protobuf:"bytes,6,opt,name=integrations,proto3" json:"integrations,omitempty"`
	EnabledDecorators   []TelematicsDecorator                `protobuf:"varint,7,rep,packed,name=enabledDecorators,proto3,enum=config.TelematicsDecorator" json:"enabledDecorators,omitempty"`
}

func (x *Telematics) Reset() {
	*x = Telematics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Telematics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Telematics) ProtoMessage() {}

func (x *Telematics) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Telematics.ProtoReflect.Descriptor instead.
func (*Telematics) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{37}
}

func (x *Telematics) GetDataPlatformBackend() ObjectBackend {
	if x != nil {
		return x.DataPlatformBackend
	}
	return ObjectBackend_InMemory
}

func (x *Telematics) GetEnabledProviders() []TelematicsDataProvider {
	if x != nil {
		return x.EnabledProviders
	}
	return nil
}

func (x *Telematics) GetCredentials() *TelematicsCredentials {
	if x != nil {
		return x.Credentials
	}
	return nil
}

func (x *Telematics) GetPipelines() *TelematicsPipelines {
	if x != nil {
		return x.Pipelines
	}
	return nil
}

func (x *Telematics) GetIntegrations() *TelematicsIntegrationConfigurations {
	if x != nil {
		return x.Integrations
	}
	return nil
}

func (x *Telematics) GetEnabledDecorators() []TelematicsDecorator {
	if x != nil {
		return x.EnabledDecorators
	}
	return nil
}

type PagerDutyKeys struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InfraP0RoutingKey                    string `protobuf:"bytes,1,opt,name=infraP0RoutingKey,proto3" json:"infraP0RoutingKey,omitempty"`
	DatascienceP0RoutingKey              string `protobuf:"bytes,2,opt,name=datascienceP0RoutingKey,proto3" json:"datascienceP0RoutingKey,omitempty"`
	AgentsP0RoutingKey                   string `protobuf:"bytes,3,opt,name=agentsP0RoutingKey,proto3" json:"agentsP0RoutingKey,omitempty"`
	SafetyP0RoutingKey                   string `protobuf:"bytes,4,opt,name=safetyP0RoutingKey,proto3" json:"safetyP0RoutingKey,omitempty"`
	InsuranceEngTestRoutingKey           string `protobuf:"bytes,5,opt,name=insuranceEngTestRoutingKey,proto3" json:"insuranceEngTestRoutingKey,omitempty"`
	BillingP0RoutingKey                  string `protobuf:"bytes,6,opt,name=billingP0RoutingKey,proto3" json:"billingP0RoutingKey,omitempty"`
	NonFleetRoutingKey                   string `protobuf:"bytes,7,opt,name=nonFleetRoutingKey,proto3" json:"nonFleetRoutingKey,omitempty"`
	NonFleetTestRoutingKey               string `protobuf:"bytes,8,opt,name=nonFleetTestRoutingKey,proto3" json:"nonFleetTestRoutingKey,omitempty"`
	TelematicsPipelineFailuresRoutingKey string `protobuf:"bytes,9,opt,name=telematicsPipelineFailuresRoutingKey,proto3" json:"telematicsPipelineFailuresRoutingKey,omitempty"`
	DataPlatformRoutingKey               string `protobuf:"bytes,10,opt,name=dataPlatformRoutingKey,proto3" json:"dataPlatformRoutingKey,omitempty"`
}

func (x *PagerDutyKeys) Reset() {
	*x = PagerDutyKeys{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PagerDutyKeys) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PagerDutyKeys) ProtoMessage() {}

func (x *PagerDutyKeys) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PagerDutyKeys.ProtoReflect.Descriptor instead.
func (*PagerDutyKeys) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{38}
}

func (x *PagerDutyKeys) GetInfraP0RoutingKey() string {
	if x != nil {
		return x.InfraP0RoutingKey
	}
	return ""
}

func (x *PagerDutyKeys) GetDatascienceP0RoutingKey() string {
	if x != nil {
		return x.DatascienceP0RoutingKey
	}
	return ""
}

func (x *PagerDutyKeys) GetAgentsP0RoutingKey() string {
	if x != nil {
		return x.AgentsP0RoutingKey
	}
	return ""
}

func (x *PagerDutyKeys) GetSafetyP0RoutingKey() string {
	if x != nil {
		return x.SafetyP0RoutingKey
	}
	return ""
}

func (x *PagerDutyKeys) GetInsuranceEngTestRoutingKey() string {
	if x != nil {
		return x.InsuranceEngTestRoutingKey
	}
	return ""
}

func (x *PagerDutyKeys) GetBillingP0RoutingKey() string {
	if x != nil {
		return x.BillingP0RoutingKey
	}
	return ""
}

func (x *PagerDutyKeys) GetNonFleetRoutingKey() string {
	if x != nil {
		return x.NonFleetRoutingKey
	}
	return ""
}

func (x *PagerDutyKeys) GetNonFleetTestRoutingKey() string {
	if x != nil {
		return x.NonFleetTestRoutingKey
	}
	return ""
}

func (x *PagerDutyKeys) GetTelematicsPipelineFailuresRoutingKey() string {
	if x != nil {
		return x.TelematicsPipelineFailuresRoutingKey
	}
	return ""
}

func (x *PagerDutyKeys) GetDataPlatformRoutingKey() string {
	if x != nil {
		return x.DataPlatformRoutingKey
	}
	return ""
}

type GoogleAppConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId     string `protobuf:"bytes,1,opt,name=clientId,proto3" json:"clientId,omitempty"`
	ClientSecret string `protobuf:"bytes,2,opt,name=clientSecret,proto3" json:"clientSecret,omitempty"`
}

func (x *GoogleAppConfig) Reset() {
	*x = GoogleAppConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoogleAppConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoogleAppConfig) ProtoMessage() {}

func (x *GoogleAppConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoogleAppConfig.ProtoReflect.Descriptor instead.
func (*GoogleAppConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{39}
}

func (x *GoogleAppConfig) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *GoogleAppConfig) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

type ClerkConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SecretKey string `protobuf:"bytes,1,opt,name=secretKey,proto3" json:"secretKey,omitempty"`
}

func (x *ClerkConfig) Reset() {
	*x = ClerkConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClerkConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClerkConfig) ProtoMessage() {}

func (x *ClerkConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClerkConfig.ProtoReflect.Descriptor instead.
func (*ClerkConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{40}
}

func (x *ClerkConfig) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

type PibitAiConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UrlPrefix string `protobuf:"bytes,1,opt,name=urlPrefix,proto3" json:"urlPrefix,omitempty"`
	ApiKey    string `protobuf:"bytes,2,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
}

func (x *PibitAiConfig) Reset() {
	*x = PibitAiConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PibitAiConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PibitAiConfig) ProtoMessage() {}

func (x *PibitAiConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PibitAiConfig.ProtoReflect.Descriptor instead.
func (*PibitAiConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{41}
}

func (x *PibitAiConfig) GetUrlPrefix() string {
	if x != nil {
		return x.UrlPrefix
	}
	return ""
}

func (x *PibitAiConfig) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

type VeriskConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiKey       string `protobuf:"bytes,1,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
	TestApiKey   string `protobuf:"bytes,2,opt,name=testApiKey,proto3" json:"testApiKey,omitempty"`
	OrgId        string `protobuf:"bytes,3,opt,name=orgId,proto3" json:"orgId,omitempty"`
	TestOrgId    string `protobuf:"bytes,4,opt,name=testOrgId,proto3" json:"testOrgId,omitempty"`
	ShipId       string `protobuf:"bytes,5,opt,name=shipId,proto3" json:"shipId,omitempty"`
	TestShipId   string `protobuf:"bytes,6,opt,name=testShipId,proto3" json:"testShipId,omitempty"`
	Url          string `protobuf:"bytes,7,opt,name=url,proto3" json:"url,omitempty"`
	TestUrl      string `protobuf:"bytes,8,opt,name=testUrl,proto3" json:"testUrl,omitempty"`
	TokenUrl     string `protobuf:"bytes,9,opt,name=tokenUrl,proto3" json:"tokenUrl,omitempty"`
	TestTokenUrl string `protobuf:"bytes,10,opt,name=testTokenUrl,proto3" json:"testTokenUrl,omitempty"`
}

func (x *VeriskConfig) Reset() {
	*x = VeriskConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VeriskConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VeriskConfig) ProtoMessage() {}

func (x *VeriskConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VeriskConfig.ProtoReflect.Descriptor instead.
func (*VeriskConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{42}
}

func (x *VeriskConfig) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *VeriskConfig) GetTestApiKey() string {
	if x != nil {
		return x.TestApiKey
	}
	return ""
}

func (x *VeriskConfig) GetOrgId() string {
	if x != nil {
		return x.OrgId
	}
	return ""
}

func (x *VeriskConfig) GetTestOrgId() string {
	if x != nil {
		return x.TestOrgId
	}
	return ""
}

func (x *VeriskConfig) GetShipId() string {
	if x != nil {
		return x.ShipId
	}
	return ""
}

func (x *VeriskConfig) GetTestShipId() string {
	if x != nil {
		return x.TestShipId
	}
	return ""
}

func (x *VeriskConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *VeriskConfig) GetTestUrl() string {
	if x != nil {
		return x.TestUrl
	}
	return ""
}

func (x *VeriskConfig) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *VeriskConfig) GetTestTokenUrl() string {
	if x != nil {
		return x.TestTokenUrl
	}
	return ""
}

type Workramp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scheme    string `protobuf:"bytes,1,opt,name=scheme,proto3" json:"scheme,omitempty"`
	Host      string `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	ApiKey    string `protobuf:"bytes,3,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
	JwtSecret string `protobuf:"bytes,4,opt,name=jwtSecret,proto3" json:"jwtSecret,omitempty"`
	AcademyID string `protobuf:"bytes,5,opt,name=academyID,proto3" json:"academyID,omitempty"`
}

func (x *Workramp) Reset() {
	*x = Workramp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Workramp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workramp) ProtoMessage() {}

func (x *Workramp) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workramp.ProtoReflect.Descriptor instead.
func (*Workramp) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{43}
}

func (x *Workramp) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *Workramp) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Workramp) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *Workramp) GetJwtSecret() string {
	if x != nil {
		return x.JwtSecret
	}
	return ""
}

func (x *Workramp) GetAcademyID() string {
	if x != nil {
		return x.AcademyID
	}
	return ""
}

type Hubspot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scheme    string `protobuf:"bytes,1,opt,name=scheme,proto3" json:"scheme,omitempty"`
	Host      string `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	JwtSecret string `protobuf:"bytes,4,opt,name=jwtSecret,proto3" json:"jwtSecret,omitempty"`
}

func (x *Hubspot) Reset() {
	*x = Hubspot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Hubspot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Hubspot) ProtoMessage() {}

func (x *Hubspot) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Hubspot.ProtoReflect.Descriptor instead.
func (*Hubspot) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{44}
}

func (x *Hubspot) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *Hubspot) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Hubspot) GetJwtSecret() string {
	if x != nil {
		return x.JwtSecret
	}
	return ""
}

type Knock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiKey     string `protobuf:"bytes,1,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
	WebhookKey string `protobuf:"bytes,2,opt,name=webhookKey,proto3" json:"webhookKey,omitempty"`
}

func (x *Knock) Reset() {
	*x = Knock{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Knock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Knock) ProtoMessage() {}

func (x *Knock) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Knock.ProtoReflect.Descriptor instead.
func (*Knock) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{45}
}

func (x *Knock) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *Knock) GetWebhookKey() string {
	if x != nil {
		return x.WebhookKey
	}
	return ""
}

type SalesforceConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TestUrl          string `protobuf:"bytes,1,opt,name=testUrl,proto3" json:"testUrl,omitempty"`
	TestUsername     string `protobuf:"bytes,2,opt,name=testUsername,proto3" json:"testUsername,omitempty"`
	TestPassword     string `protobuf:"bytes,3,opt,name=testPassword,proto3" json:"testPassword,omitempty"`
	TestClientId     string `protobuf:"bytes,4,opt,name=testClientId,proto3" json:"testClientId,omitempty"`
	TestClientSecret string `protobuf:"bytes,5,opt,name=testClientSecret,proto3" json:"testClientSecret,omitempty"`
	Url              string `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`
	UserName         string `protobuf:"bytes,7,opt,name=userName,proto3" json:"userName,omitempty"`
	Password         string `protobuf:"bytes,8,opt,name=password,proto3" json:"password,omitempty"`
	ClientId         string `protobuf:"bytes,9,opt,name=clientId,proto3" json:"clientId,omitempty"`
	ClientSecret     string `protobuf:"bytes,10,opt,name=clientSecret,proto3" json:"clientSecret,omitempty"`
}

func (x *SalesforceConfig) Reset() {
	*x = SalesforceConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SalesforceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SalesforceConfig) ProtoMessage() {}

func (x *SalesforceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SalesforceConfig.ProtoReflect.Descriptor instead.
func (*SalesforceConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{46}
}

func (x *SalesforceConfig) GetTestUrl() string {
	if x != nil {
		return x.TestUrl
	}
	return ""
}

func (x *SalesforceConfig) GetTestUsername() string {
	if x != nil {
		return x.TestUsername
	}
	return ""
}

func (x *SalesforceConfig) GetTestPassword() string {
	if x != nil {
		return x.TestPassword
	}
	return ""
}

func (x *SalesforceConfig) GetTestClientId() string {
	if x != nil {
		return x.TestClientId
	}
	return ""
}

func (x *SalesforceConfig) GetTestClientSecret() string {
	if x != nil {
		return x.TestClientSecret
	}
	return ""
}

func (x *SalesforceConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *SalesforceConfig) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *SalesforceConfig) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SalesforceConfig) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *SalesforceConfig) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

type JiraConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host        string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	ApiToken    string `protobuf:"bytes,2,opt,name=apiToken,proto3" json:"apiToken,omitempty"`
	MailAddress string `protobuf:"bytes,3,opt,name=mailAddress,proto3" json:"mailAddress,omitempty"`
}

func (x *JiraConfig) Reset() {
	*x = JiraConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JiraConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JiraConfig) ProtoMessage() {}

func (x *JiraConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JiraConfig.ProtoReflect.Descriptor instead.
func (*JiraConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{47}
}

func (x *JiraConfig) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *JiraConfig) GetApiToken() string {
	if x != nil {
		return x.ApiToken
	}
	return ""
}

func (x *JiraConfig) GetMailAddress() string {
	if x != nil {
		return x.MailAddress
	}
	return ""
}

type Flatfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseUrl      string `protobuf:"bytes,1,opt,name=baseUrl,proto3" json:"baseUrl,omitempty"`
	ApiKey       string `protobuf:"bytes,2,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
	ClientSecret string `protobuf:"bytes,3,opt,name=clientSecret,proto3" json:"clientSecret,omitempty"`
}

func (x *Flatfile) Reset() {
	*x = Flatfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Flatfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Flatfile) ProtoMessage() {}

func (x *Flatfile) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Flatfile.ProtoReflect.Descriptor instead.
func (*Flatfile) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{48}
}

func (x *Flatfile) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *Flatfile) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *Flatfile) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

type Impler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseUrl     string `protobuf:"bytes,1,opt,name=baseUrl,proto3" json:"baseUrl,omitempty"`
	AccessToken string `protobuf:"bytes,2,opt,name=accessToken,proto3" json:"accessToken,omitempty"`
}

func (x *Impler) Reset() {
	*x = Impler{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Impler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Impler) ProtoMessage() {}

func (x *Impler) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Impler.ProtoReflect.Descriptor instead.
func (*Impler) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{49}
}

func (x *Impler) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *Impler) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

type PostHog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PersonalApiKey string `protobuf:"bytes,1,opt,name=personalApiKey,proto3" json:"personalApiKey,omitempty"`
	PublicApiKey   string `protobuf:"bytes,2,opt,name=publicApiKey,proto3" json:"publicApiKey,omitempty"`
}

func (x *PostHog) Reset() {
	*x = PostHog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostHog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostHog) ProtoMessage() {}

func (x *PostHog) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostHog.ProtoReflect.Descriptor instead.
func (*PostHog) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{50}
}

func (x *PostHog) GetPersonalApiKey() string {
	if x != nil {
		return x.PersonalApiKey
	}
	return ""
}

func (x *PostHog) GetPublicApiKey() string {
	if x != nil {
		return x.PublicApiKey
	}
	return ""
}

type Openmeter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host   string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Apikey string `protobuf:"bytes,2,opt,name=apikey,proto3" json:"apikey,omitempty"`
}

func (x *Openmeter) Reset() {
	*x = Openmeter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Openmeter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Openmeter) ProtoMessage() {}

func (x *Openmeter) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Openmeter.ProtoReflect.Descriptor instead.
func (*Openmeter) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{51}
}

func (x *Openmeter) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Openmeter) GetApikey() string {
	if x != nil {
		return x.Apikey
	}
	return ""
}

type Ascend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiKey        string `protobuf:"bytes,1,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
	Host          string `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	SandboxApiKey string `protobuf:"bytes,3,opt,name=sandboxApiKey,proto3" json:"sandboxApiKey,omitempty"`
	SandboxHost   string `protobuf:"bytes,4,opt,name=sandboxHost,proto3" json:"sandboxHost,omitempty"`
}

func (x *Ascend) Reset() {
	*x = Ascend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ascend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ascend) ProtoMessage() {}

func (x *Ascend) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ascend.ProtoReflect.Descriptor instead.
func (*Ascend) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{52}
}

func (x *Ascend) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *Ascend) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Ascend) GetSandboxApiKey() string {
	if x != nil {
		return x.SandboxApiKey
	}
	return ""
}

func (x *Ascend) GetSandboxHost() string {
	if x != nil {
		return x.SandboxHost
	}
	return ""
}

type Snapsheet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SandboxSecret       string `protobuf:"bytes,1,opt,name=sandboxSecret,proto3" json:"sandboxSecret,omitempty"`
	SandboxHost         string `protobuf:"bytes,2,opt,name=sandboxHost,proto3" json:"sandboxHost,omitempty"`
	ProductionSecret    string `protobuf:"bytes,3,opt,name=productionSecret,proto3" json:"productionSecret,omitempty"`
	ProductionHost      string `protobuf:"bytes,4,opt,name=productionHost,proto3" json:"productionHost,omitempty"`
	SandboxPublicKey    string `protobuf:"bytes,5,opt,name=sandboxPublicKey,proto3" json:"sandboxPublicKey,omitempty"`
	ProductionPublicKey string `protobuf:"bytes,6,opt,name=productionPublicKey,proto3" json:"productionPublicKey,omitempty"`
}

func (x *Snapsheet) Reset() {
	*x = Snapsheet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Snapsheet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Snapsheet) ProtoMessage() {}

func (x *Snapsheet) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Snapsheet.ProtoReflect.Descriptor instead.
func (*Snapsheet) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{53}
}

func (x *Snapsheet) GetSandboxSecret() string {
	if x != nil {
		return x.SandboxSecret
	}
	return ""
}

func (x *Snapsheet) GetSandboxHost() string {
	if x != nil {
		return x.SandboxHost
	}
	return ""
}

func (x *Snapsheet) GetProductionSecret() string {
	if x != nil {
		return x.ProductionSecret
	}
	return ""
}

func (x *Snapsheet) GetProductionHost() string {
	if x != nil {
		return x.ProductionHost
	}
	return ""
}

func (x *Snapsheet) GetSandboxPublicKey() string {
	if x != nil {
		return x.SandboxPublicKey
	}
	return ""
}

func (x *Snapsheet) GetProductionPublicKey() string {
	if x != nil {
		return x.ProductionPublicKey
	}
	return ""
}

type ProductTools struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SegmentAPIKey       string               `protobuf:"bytes,1,opt,name=segmentAPIKey,proto3" json:"segmentAPIKey,omitempty"`
	UnipdfApiKey        string               `protobuf:"bytes,2,opt,name=unipdfApiKey,proto3" json:"unipdfApiKey,omitempty"`
	LaunchdarklyApiKey  string               `protobuf:"bytes,3,opt,name=launchdarklyApiKey,proto3" json:"launchdarklyApiKey,omitempty"`
	Pagerduty           *PagerDutyKeys       `protobuf:"bytes,4,opt,name=pagerduty,proto3" json:"pagerduty,omitempty"`
	SendgridApiKey      string               `protobuf:"bytes,5,opt,name=sendgridApiKey,proto3" json:"sendgridApiKey,omitempty"`
	GoogleAppConfig     *GoogleAppConfig     `protobuf:"bytes,6,opt,name=googleAppConfig,proto3" json:"googleAppConfig,omitempty"`
	PibitAiConfig       *PibitAiConfig       `protobuf:"bytes,7,opt,name=pibitAiConfig,proto3" json:"pibitAiConfig,omitempty"`
	NarsApiKey          string               `protobuf:"bytes,8,opt,name=narsApiKey,proto3" json:"narsApiKey,omitempty"`
	NarsTestApiKey      string               `protobuf:"bytes,9,opt,name=narsTestApiKey,proto3" json:"narsTestApiKey,omitempty"`
	DBTCloudCredentials *DBTCloudCredentials `protobuf:"bytes,14,opt,name=DBTCloudCredentials,proto3" json:"DBTCloudCredentials,omitempty"`
	SmartProxyConfig    *SmartProxyConfig    `protobuf:"bytes,15,opt,name=smartProxyConfig,proto3" json:"smartProxyConfig,omitempty"`
	VeriskConfig        *VeriskConfig        `protobuf:"bytes,16,opt,name=veriskConfig,proto3" json:"veriskConfig,omitempty"`
	Workramp            *Workramp            `protobuf:"bytes,17,opt,name=workramp,proto3" json:"workramp,omitempty"`
	SalesforceConfig    *SalesforceConfig    `protobuf:"bytes,18,opt,name=salesforceConfig,proto3" json:"salesforceConfig,omitempty"`
	Hubspot             *Hubspot             `protobuf:"bytes,19,opt,name=hubspot,proto3" json:"hubspot,omitempty"`
	Knock               *Knock               `protobuf:"bytes,20,opt,name=knock,proto3" json:"knock,omitempty"`
	Flatfile            *Flatfile            `protobuf:"bytes,21,opt,name=flatfile,proto3" json:"flatfile,omitempty"`
	Impler              *Impler              `protobuf:"bytes,22,opt,name=impler,proto3" json:"impler,omitempty"`
	PostHog             *PostHog             `protobuf:"bytes,23,opt,name=postHog,proto3" json:"postHog,omitempty"`
	Jira                *JiraConfig          `protobuf:"bytes,24,opt,name=jira,proto3" json:"jira,omitempty"`
	OpenaiApiKey        string               `protobuf:"bytes,25,opt,name=openaiApiKey,proto3" json:"openaiApiKey,omitempty"`
	Openmeter           *Openmeter           `protobuf:"bytes,26,opt,name=openmeter,proto3" json:"openmeter,omitempty"`
	Ascend              *Ascend              `protobuf:"bytes,27,opt,name=ascend,proto3" json:"ascend,omitempty"`
	Twilio              *Twilio              `protobuf:"bytes,28,opt,name=twilio,proto3" json:"twilio,omitempty"`
	Snapsheet           *Snapsheet           `protobuf:"bytes,29,opt,name=snapsheet,proto3" json:"snapsheet,omitempty"`
	ClerkConfig         *ClerkConfig         `protobuf:"bytes,30,opt,name=clerkConfig,proto3" json:"clerkConfig,omitempty"`
	DsModelConfig       *DSModelServer       `protobuf:"bytes,31,opt,name=dsModelConfig,proto3" json:"dsModelConfig,omitempty"`
	GorulesServer       *GorulesServer       `protobuf:"bytes,32,opt,name=gorulesServer,proto3" json:"gorulesServer,omitempty"`
}

func (x *ProductTools) Reset() {
	*x = ProductTools{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductTools) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductTools) ProtoMessage() {}

func (x *ProductTools) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductTools.ProtoReflect.Descriptor instead.
func (*ProductTools) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{54}
}

func (x *ProductTools) GetSegmentAPIKey() string {
	if x != nil {
		return x.SegmentAPIKey
	}
	return ""
}

func (x *ProductTools) GetUnipdfApiKey() string {
	if x != nil {
		return x.UnipdfApiKey
	}
	return ""
}

func (x *ProductTools) GetLaunchdarklyApiKey() string {
	if x != nil {
		return x.LaunchdarklyApiKey
	}
	return ""
}

func (x *ProductTools) GetPagerduty() *PagerDutyKeys {
	if x != nil {
		return x.Pagerduty
	}
	return nil
}

func (x *ProductTools) GetSendgridApiKey() string {
	if x != nil {
		return x.SendgridApiKey
	}
	return ""
}

func (x *ProductTools) GetGoogleAppConfig() *GoogleAppConfig {
	if x != nil {
		return x.GoogleAppConfig
	}
	return nil
}

func (x *ProductTools) GetPibitAiConfig() *PibitAiConfig {
	if x != nil {
		return x.PibitAiConfig
	}
	return nil
}

func (x *ProductTools) GetNarsApiKey() string {
	if x != nil {
		return x.NarsApiKey
	}
	return ""
}

func (x *ProductTools) GetNarsTestApiKey() string {
	if x != nil {
		return x.NarsTestApiKey
	}
	return ""
}

func (x *ProductTools) GetDBTCloudCredentials() *DBTCloudCredentials {
	if x != nil {
		return x.DBTCloudCredentials
	}
	return nil
}

func (x *ProductTools) GetSmartProxyConfig() *SmartProxyConfig {
	if x != nil {
		return x.SmartProxyConfig
	}
	return nil
}

func (x *ProductTools) GetVeriskConfig() *VeriskConfig {
	if x != nil {
		return x.VeriskConfig
	}
	return nil
}

func (x *ProductTools) GetWorkramp() *Workramp {
	if x != nil {
		return x.Workramp
	}
	return nil
}

func (x *ProductTools) GetSalesforceConfig() *SalesforceConfig {
	if x != nil {
		return x.SalesforceConfig
	}
	return nil
}

func (x *ProductTools) GetHubspot() *Hubspot {
	if x != nil {
		return x.Hubspot
	}
	return nil
}

func (x *ProductTools) GetKnock() *Knock {
	if x != nil {
		return x.Knock
	}
	return nil
}

func (x *ProductTools) GetFlatfile() *Flatfile {
	if x != nil {
		return x.Flatfile
	}
	return nil
}

func (x *ProductTools) GetImpler() *Impler {
	if x != nil {
		return x.Impler
	}
	return nil
}

func (x *ProductTools) GetPostHog() *PostHog {
	if x != nil {
		return x.PostHog
	}
	return nil
}

func (x *ProductTools) GetJira() *JiraConfig {
	if x != nil {
		return x.Jira
	}
	return nil
}

func (x *ProductTools) GetOpenaiApiKey() string {
	if x != nil {
		return x.OpenaiApiKey
	}
	return ""
}

func (x *ProductTools) GetOpenmeter() *Openmeter {
	if x != nil {
		return x.Openmeter
	}
	return nil
}

func (x *ProductTools) GetAscend() *Ascend {
	if x != nil {
		return x.Ascend
	}
	return nil
}

func (x *ProductTools) GetTwilio() *Twilio {
	if x != nil {
		return x.Twilio
	}
	return nil
}

func (x *ProductTools) GetSnapsheet() *Snapsheet {
	if x != nil {
		return x.Snapsheet
	}
	return nil
}

func (x *ProductTools) GetClerkConfig() *ClerkConfig {
	if x != nil {
		return x.ClerkConfig
	}
	return nil
}

func (x *ProductTools) GetDsModelConfig() *DSModelServer {
	if x != nil {
		return x.DsModelConfig
	}
	return nil
}

func (x *ProductTools) GetGorulesServer() *GorulesServer {
	if x != nil {
		return x.GorulesServer
	}
	return nil
}

type SmartProxyConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url      string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	User     string `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	Port     int32  `protobuf:"varint,4,opt,name=port,proto3" json:"port,omitempty"`
}

func (x *SmartProxyConfig) Reset() {
	*x = SmartProxyConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmartProxyConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmartProxyConfig) ProtoMessage() {}

func (x *SmartProxyConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmartProxyConfig.ProtoReflect.Descriptor instead.
func (*SmartProxyConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{55}
}

func (x *SmartProxyConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *SmartProxyConfig) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *SmartProxyConfig) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SmartProxyConfig) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

type JobberProcessorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClusterId string `protobuf:"bytes,1,opt,name=clusterId,proto3" json:"clusterId,omitempty"`
	// Deprecated: Marked as deprecated in config/config.proto.
	UseEmbeddedStore bool  `protobuf:"varint,2,opt,name=useEmbeddedStore,proto3" json:"useEmbeddedStore,omitempty"`
	MaxResources     int64 `protobuf:"varint,3,opt,name=maxResources,proto3" json:"maxResources,omitempty"`
	// Deprecated: Marked as deprecated in config/config.proto.
	UseECSMonitor                 bool                                          `protobuf:"varint,4,opt,name=useECSMonitor,proto3" json:"useECSMonitor,omitempty"`
	StableAddress                 string                                        `protobuf:"bytes,5,opt,name=stableAddress,proto3" json:"stableAddress,omitempty"`
	UseECSOpsDirectorForProcessor bool                                          `protobuf:"varint,6,opt,name=useECSOpsDirectorForProcessor,proto3" json:"useECSOpsDirectorForProcessor,omitempty"`
	StoreConfig                   *JobberProcessorConfig_StoreConfig            `protobuf:"bytes,7,opt,name=storeConfig,proto3" json:"storeConfig,omitempty"`
	MonitorConfig                 *JobberProcessorConfig_MonitorConfig          `protobuf:"bytes,8,opt,name=monitorConfig,proto3" json:"monitorConfig,omitempty"`
	RegisterGrpcServers           bool                                          `protobuf:"varint,9,opt,name=registerGrpcServers,proto3" json:"registerGrpcServers,omitempty"`
	RegistryConfig                *JobberProcessorConfig_RegistryConfig         `protobuf:"bytes,10,opt,name=registryConfig,proto3" json:"registryConfig,omitempty"`
	EmitProcessorTelemetry        bool                                          `protobuf:"varint,11,opt,name=emitProcessorTelemetry,proto3" json:"emitProcessorTelemetry,omitempty"`
	StuckJobDetectorConfig        *JobberProcessorConfig_StuckJobDetectorConfig `protobuf:"bytes,12,opt,name=stuckJobDetectorConfig,proto3" json:"stuckJobDetectorConfig,omitempty"`
}

func (x *JobberProcessorConfig) Reset() {
	*x = JobberProcessorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig) ProtoMessage() {}

func (x *JobberProcessorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56}
}

func (x *JobberProcessorConfig) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

// Deprecated: Marked as deprecated in config/config.proto.
func (x *JobberProcessorConfig) GetUseEmbeddedStore() bool {
	if x != nil {
		return x.UseEmbeddedStore
	}
	return false
}

func (x *JobberProcessorConfig) GetMaxResources() int64 {
	if x != nil {
		return x.MaxResources
	}
	return 0
}

// Deprecated: Marked as deprecated in config/config.proto.
func (x *JobberProcessorConfig) GetUseECSMonitor() bool {
	if x != nil {
		return x.UseECSMonitor
	}
	return false
}

func (x *JobberProcessorConfig) GetStableAddress() string {
	if x != nil {
		return x.StableAddress
	}
	return ""
}

func (x *JobberProcessorConfig) GetUseECSOpsDirectorForProcessor() bool {
	if x != nil {
		return x.UseECSOpsDirectorForProcessor
	}
	return false
}

func (x *JobberProcessorConfig) GetStoreConfig() *JobberProcessorConfig_StoreConfig {
	if x != nil {
		return x.StoreConfig
	}
	return nil
}

func (x *JobberProcessorConfig) GetMonitorConfig() *JobberProcessorConfig_MonitorConfig {
	if x != nil {
		return x.MonitorConfig
	}
	return nil
}

func (x *JobberProcessorConfig) GetRegisterGrpcServers() bool {
	if x != nil {
		return x.RegisterGrpcServers
	}
	return false
}

func (x *JobberProcessorConfig) GetRegistryConfig() *JobberProcessorConfig_RegistryConfig {
	if x != nil {
		return x.RegistryConfig
	}
	return nil
}

func (x *JobberProcessorConfig) GetEmitProcessorTelemetry() bool {
	if x != nil {
		return x.EmitProcessorTelemetry
	}
	return false
}

func (x *JobberProcessorConfig) GetStuckJobDetectorConfig() *JobberProcessorConfig_StuckJobDetectorConfig {
	if x != nil {
		return x.StuckJobDetectorConfig
	}
	return nil
}

type JobberSingletonsConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EcsMonitorAddr string `protobuf:"bytes,1,opt,name=ecsMonitorAddr,proto3" json:"ecsMonitorAddr,omitempty"`
}

func (x *JobberSingletonsConfig) Reset() {
	*x = JobberSingletonsConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberSingletonsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberSingletonsConfig) ProtoMessage() {}

func (x *JobberSingletonsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberSingletonsConfig.ProtoReflect.Descriptor instead.
func (*JobberSingletonsConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{57}
}

func (x *JobberSingletonsConfig) GetEcsMonitorAddr() string {
	if x != nil {
		return x.EcsMonitorAddr
	}
	return ""
}

type JobberProcessors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Standard  *JobberProcessorConfig `protobuf:"bytes,1,opt,name=standard,proto3" json:"standard,omitempty"`
	DataInfra *JobberProcessorConfig `protobuf:"bytes,2,opt,name=dataInfra,proto3" json:"dataInfra,omitempty"`
	Quoting   *JobberProcessorConfig `protobuf:"bytes,3,opt,name=quoting,proto3" json:"quoting,omitempty"`
	Event     *JobberProcessorConfig `protobuf:"bytes,4,opt,name=event,proto3" json:"event,omitempty"`
	Safety    *JobberProcessorConfig `protobuf:"bytes,5,opt,name=safety,proto3" json:"safety,omitempty"`
	Test1     *JobberProcessorConfig `protobuf:"bytes,6,opt,name=test1,proto3" json:"test1,omitempty"`
	Test2     *JobberProcessorConfig `protobuf:"bytes,7,opt,name=test2,proto3" json:"test2,omitempty"`
}

func (x *JobberProcessors) Reset() {
	*x = JobberProcessors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessors) ProtoMessage() {}

func (x *JobberProcessors) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessors.ProtoReflect.Descriptor instead.
func (*JobberProcessors) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{58}
}

func (x *JobberProcessors) GetStandard() *JobberProcessorConfig {
	if x != nil {
		return x.Standard
	}
	return nil
}

func (x *JobberProcessors) GetDataInfra() *JobberProcessorConfig {
	if x != nil {
		return x.DataInfra
	}
	return nil
}

func (x *JobberProcessors) GetQuoting() *JobberProcessorConfig {
	if x != nil {
		return x.Quoting
	}
	return nil
}

func (x *JobberProcessors) GetEvent() *JobberProcessorConfig {
	if x != nil {
		return x.Event
	}
	return nil
}

func (x *JobberProcessors) GetSafety() *JobberProcessorConfig {
	if x != nil {
		return x.Safety
	}
	return nil
}

func (x *JobberProcessors) GetTest1() *JobberProcessorConfig {
	if x != nil {
		return x.Test1
	}
	return nil
}

func (x *JobberProcessors) GetTest2() *JobberProcessorConfig {
	if x != nil {
		return x.Test2
	}
	return nil
}

type Senture struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *Senture) Reset() {
	*x = Senture{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Senture) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Senture) ProtoMessage() {}

func (x *Senture) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Senture.ProtoReflect.Descriptor instead.
func (*Senture) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{59}
}

func (x *Senture) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Senture) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type Safety struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Senture *Senture `protobuf:"bytes,1,opt,name=senture,proto3" json:"senture,omitempty"`
}

func (x *Safety) Reset() {
	*x = Safety{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Safety) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Safety) ProtoMessage() {}

func (x *Safety) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Safety.ProtoReflect.Descriptor instead.
func (*Safety) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{60}
}

func (x *Safety) GetSenture() *Senture {
	if x != nil {
		return x.Senture
	}
	return nil
}

type Fmcsa struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DbtTransformationJobName string `protobuf:"bytes,2,opt,name=dbtTransformationJobName,proto3" json:"dbtTransformationJobName,omitempty"`
}

func (x *Fmcsa) Reset() {
	*x = Fmcsa{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fmcsa) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fmcsa) ProtoMessage() {}

func (x *Fmcsa) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fmcsa.ProtoReflect.Descriptor instead.
func (*Fmcsa) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{61}
}

func (x *Fmcsa) GetDbtTransformationJobName() string {
	if x != nil {
		return x.DbtTransformationJobName
	}
	return ""
}

type RefresherConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DBTDirectory        string `protobuf:"bytes,1,opt,name=DBTDirectory,proto3" json:"DBTDirectory,omitempty"`
	SnowflakeS3Bucket   string `protobuf:"bytes,2,opt,name=snowflakeS3Bucket,proto3" json:"snowflakeS3Bucket,omitempty"`
	PublicFilesS3Bucket string `protobuf:"bytes,3,opt,name=publicFilesS3Bucket,proto3" json:"publicFilesS3Bucket,omitempty"`
}

func (x *RefresherConfig) Reset() {
	*x = RefresherConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefresherConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefresherConfig) ProtoMessage() {}

func (x *RefresherConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefresherConfig.ProtoReflect.Descriptor instead.
func (*RefresherConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{62}
}

func (x *RefresherConfig) GetDBTDirectory() string {
	if x != nil {
		return x.DBTDirectory
	}
	return ""
}

func (x *RefresherConfig) GetSnowflakeS3Bucket() string {
	if x != nil {
		return x.SnowflakeS3Bucket
	}
	return ""
}

func (x *RefresherConfig) GetPublicFilesS3Bucket() string {
	if x != nil {
		return x.PublicFilesS3Bucket
	}
	return ""
}

type Runtime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CI bool `protobuf:"varint,1,opt,name=CI,proto3" json:"CI,omitempty"`
}

func (x *Runtime) Reset() {
	*x = Runtime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Runtime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Runtime) ProtoMessage() {}

func (x *Runtime) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Runtime.ProtoReflect.Descriptor instead.
func (*Runtime) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{63}
}

func (x *Runtime) GetCI() bool {
	if x != nil {
		return x.CI
	}
	return false
}

type Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Env                    Env                     `protobuf:"varint,1,opt,name=Env,proto3,enum=config.Env" json:"Env,omitempty"`
	Databases              *Databases              `protobuf:"bytes,2,opt,name=Databases,proto3" json:"Databases,omitempty"`
	Scrapers               *Scrapers               `protobuf:"bytes,3,opt,name=Scrapers,proto3" json:"Scrapers,omitempty"`
	Verisk                 *Verisk                 `protobuf:"bytes,4,opt,name=Verisk,proto3" json:"Verisk,omitempty"`
	AWS                    *AWS                    `protobuf:"bytes,5,opt,name=AWS,proto3" json:"AWS,omitempty"`
	Telematics             *Telematics             `protobuf:"bytes,6,opt,name=Telematics,proto3" json:"Telematics,omitempty"`
	ProductTools           *ProductTools           `protobuf:"bytes,7,opt,name=ProductTools,proto3" json:"ProductTools,omitempty"`
	Services               *Services               `protobuf:"bytes,8,opt,name=Services,proto3" json:"Services,omitempty"`
	Safety                 *Safety                 `protobuf:"bytes,10,opt,name=Safety,proto3" json:"Safety,omitempty"`
	Infra                  *Infra                  `protobuf:"bytes,11,opt,name=infra,proto3" json:"infra,omitempty"`
	Fmcsa                  *Fmcsa                  `protobuf:"bytes,12,opt,name=Fmcsa,proto3" json:"Fmcsa,omitempty"`
	JobberProcessors       *JobberProcessors       `protobuf:"bytes,13,opt,name=JobberProcessors,proto3" json:"JobberProcessors,omitempty"`
	RefresherConfig        *RefresherConfig        `protobuf:"bytes,14,opt,name=RefresherConfig,proto3" json:"RefresherConfig,omitempty"`
	JobberSingletonsConfig *JobberSingletonsConfig `protobuf:"bytes,15,opt,name=JobberSingletonsConfig,proto3" json:"JobberSingletonsConfig,omitempty"`
	Runtime                *Runtime                `protobuf:"bytes,16,opt,name=Runtime,proto3" json:"Runtime,omitempty"`
	NirvanaUrls            *NirvanaUrls            `protobuf:"bytes,17,opt,name=NirvanaUrls,proto3" json:"NirvanaUrls,omitempty"`
}

func (x *Config) Reset() {
	*x = Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{64}
}

func (x *Config) GetEnv() Env {
	if x != nil {
		return x.Env
	}
	return Env_DEV
}

func (x *Config) GetDatabases() *Databases {
	if x != nil {
		return x.Databases
	}
	return nil
}

func (x *Config) GetScrapers() *Scrapers {
	if x != nil {
		return x.Scrapers
	}
	return nil
}

func (x *Config) GetVerisk() *Verisk {
	if x != nil {
		return x.Verisk
	}
	return nil
}

func (x *Config) GetAWS() *AWS {
	if x != nil {
		return x.AWS
	}
	return nil
}

func (x *Config) GetTelematics() *Telematics {
	if x != nil {
		return x.Telematics
	}
	return nil
}

func (x *Config) GetProductTools() *ProductTools {
	if x != nil {
		return x.ProductTools
	}
	return nil
}

func (x *Config) GetServices() *Services {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *Config) GetSafety() *Safety {
	if x != nil {
		return x.Safety
	}
	return nil
}

func (x *Config) GetInfra() *Infra {
	if x != nil {
		return x.Infra
	}
	return nil
}

func (x *Config) GetFmcsa() *Fmcsa {
	if x != nil {
		return x.Fmcsa
	}
	return nil
}

func (x *Config) GetJobberProcessors() *JobberProcessors {
	if x != nil {
		return x.JobberProcessors
	}
	return nil
}

func (x *Config) GetRefresherConfig() *RefresherConfig {
	if x != nil {
		return x.RefresherConfig
	}
	return nil
}

func (x *Config) GetJobberSingletonsConfig() *JobberSingletonsConfig {
	if x != nil {
		return x.JobberSingletonsConfig
	}
	return nil
}

func (x *Config) GetRuntime() *Runtime {
	if x != nil {
		return x.Runtime
	}
	return nil
}

func (x *Config) GetNirvanaUrls() *NirvanaUrls {
	if x != nil {
		return x.NirvanaUrls
	}
	return nil
}

type Twilio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountSid string `protobuf:"bytes,1,opt,name=accountSid,proto3" json:"accountSid,omitempty"`
	AuthToken  string `protobuf:"bytes,2,opt,name=authToken,proto3" json:"authToken,omitempty"`
	BaseURL    string `protobuf:"bytes,3,opt,name=baseURL,proto3" json:"baseURL,omitempty"`
}

func (x *Twilio) Reset() {
	*x = Twilio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Twilio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Twilio) ProtoMessage() {}

func (x *Twilio) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Twilio.ProtoReflect.Descriptor instead.
func (*Twilio) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{65}
}

func (x *Twilio) GetAccountSid() string {
	if x != nil {
		return x.AccountSid
	}
	return ""
}

func (x *Twilio) GetAuthToken() string {
	if x != nil {
		return x.AuthToken
	}
	return ""
}

func (x *Twilio) GetBaseURL() string {
	if x != nil {
		return x.BaseURL
	}
	return ""
}

type NirvanaUrls struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LandingPage string `protobuf:"bytes,1,opt,name=landingPage,proto3" json:"landingPage,omitempty"`
}

func (x *NirvanaUrls) Reset() {
	*x = NirvanaUrls{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NirvanaUrls) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NirvanaUrls) ProtoMessage() {}

func (x *NirvanaUrls) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NirvanaUrls.ProtoReflect.Descriptor instead.
func (*NirvanaUrls) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{66}
}

func (x *NirvanaUrls) GetLandingPage() string {
	if x != nil {
		return x.LandingPage
	}
	return ""
}

type DSModelServer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Port string `protobuf:"bytes,2,opt,name=port,proto3" json:"port,omitempty"`
}

func (x *DSModelServer) Reset() {
	*x = DSModelServer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DSModelServer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DSModelServer) ProtoMessage() {}

func (x *DSModelServer) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DSModelServer.ProtoReflect.Descriptor instead.
func (*DSModelServer) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{67}
}

func (x *DSModelServer) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *DSModelServer) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

type GorulesServer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	ApiKey string `protobuf:"bytes,2,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
}

func (x *GorulesServer) Reset() {
	*x = GorulesServer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GorulesServer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GorulesServer) ProtoMessage() {}

func (x *GorulesServer) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GorulesServer.ProtoReflect.Descriptor instead.
func (*GorulesServer) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{68}
}

func (x *GorulesServer) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *GorulesServer) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

type Databases_SnowflakeSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users       *Databases_SnowflakeSettings_Users       `protobuf:"bytes,1,opt,name=users,proto3" json:"users,omitempty"`
	Connections *Databases_SnowflakeSettings_Connections `protobuf:"bytes,2,opt,name=connections,proto3" json:"connections,omitempty"`
}

func (x *Databases_SnowflakeSettings) Reset() {
	*x = Databases_SnowflakeSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Databases_SnowflakeSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Databases_SnowflakeSettings) ProtoMessage() {}

func (x *Databases_SnowflakeSettings) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Databases_SnowflakeSettings.ProtoReflect.Descriptor instead.
func (*Databases_SnowflakeSettings) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{5, 0}
}

func (x *Databases_SnowflakeSettings) GetUsers() *Databases_SnowflakeSettings_Users {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *Databases_SnowflakeSettings) GetConnections() *Databases_SnowflakeSettings_Connections {
	if x != nil {
		return x.Connections
	}
	return nil
}

type Databases_SnowflakeSettings_Users struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Admin            *SnowflakeUser        `protobuf:"bytes,1,opt,name=admin,proto3" json:"admin,omitempty"`
	Datascience      *SnowflakeUser        `protobuf:"bytes,2,opt,name=datascience,proto3" json:"datascience,omitempty"`
	Fmcsa            *SnowflakeUser        `protobuf:"bytes,3,opt,name=fmcsa,proto3" json:"fmcsa,omitempty"`
	FmcsaLocalPuller *SnowflakeUser        `protobuf:"bytes,4,opt,name=fmcsaLocalPuller,proto3" json:"fmcsaLocalPuller,omitempty"`
	AnalyticsCore    *SnowflakeUser        `protobuf:"bytes,5,opt,name=analytics_core,json=analyticsCore,proto3" json:"analytics_core,omitempty"`
	Snapsheet        *SnowflakeKeyPairUser `protobuf:"bytes,6,opt,name=snapsheet,proto3" json:"snapsheet,omitempty"`
}

func (x *Databases_SnowflakeSettings_Users) Reset() {
	*x = Databases_SnowflakeSettings_Users{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Databases_SnowflakeSettings_Users) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Databases_SnowflakeSettings_Users) ProtoMessage() {}

func (x *Databases_SnowflakeSettings_Users) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Databases_SnowflakeSettings_Users.ProtoReflect.Descriptor instead.
func (*Databases_SnowflakeSettings_Users) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{5, 0, 0}
}

func (x *Databases_SnowflakeSettings_Users) GetAdmin() *SnowflakeUser {
	if x != nil {
		return x.Admin
	}
	return nil
}

func (x *Databases_SnowflakeSettings_Users) GetDatascience() *SnowflakeUser {
	if x != nil {
		return x.Datascience
	}
	return nil
}

func (x *Databases_SnowflakeSettings_Users) GetFmcsa() *SnowflakeUser {
	if x != nil {
		return x.Fmcsa
	}
	return nil
}

func (x *Databases_SnowflakeSettings_Users) GetFmcsaLocalPuller() *SnowflakeUser {
	if x != nil {
		return x.FmcsaLocalPuller
	}
	return nil
}

func (x *Databases_SnowflakeSettings_Users) GetAnalyticsCore() *SnowflakeUser {
	if x != nil {
		return x.AnalyticsCore
	}
	return nil
}

func (x *Databases_SnowflakeSettings_Users) GetSnapsheet() *SnowflakeKeyPairUser {
	if x != nil {
		return x.Snapsheet
	}
	return nil
}

type Databases_SnowflakeSettings_Connections struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FmcsaDbt         *SnowflakeConfig `protobuf:"bytes,1,opt,name=fmcsa_dbt,json=fmcsaDbt,proto3" json:"fmcsa_dbt,omitempty"`
	Datascience      *SnowflakeConfig `protobuf:"bytes,2,opt,name=datascience,proto3" json:"datascience,omitempty"`
	Smartdrive       *SnowflakeConfig `protobuf:"bytes,3,opt,name=smartdrive,proto3" json:"smartdrive,omitempty"`
	FmcsaLocalPuller *SnowflakeConfig `protobuf:"bytes,4,opt,name=fmcsaLocalPuller,proto3" json:"fmcsaLocalPuller,omitempty"`
	AnalyticsCore    *SnowflakeConfig `protobuf:"bytes,5,opt,name=analytics_core,json=analyticsCore,proto3" json:"analytics_core,omitempty"`
	Snapsheet        *SnowflakeConfig `protobuf:"bytes,6,opt,name=snapsheet,proto3" json:"snapsheet,omitempty"`
}

func (x *Databases_SnowflakeSettings_Connections) Reset() {
	*x = Databases_SnowflakeSettings_Connections{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Databases_SnowflakeSettings_Connections) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Databases_SnowflakeSettings_Connections) ProtoMessage() {}

func (x *Databases_SnowflakeSettings_Connections) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Databases_SnowflakeSettings_Connections.ProtoReflect.Descriptor instead.
func (*Databases_SnowflakeSettings_Connections) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{5, 0, 1}
}

func (x *Databases_SnowflakeSettings_Connections) GetFmcsaDbt() *SnowflakeConfig {
	if x != nil {
		return x.FmcsaDbt
	}
	return nil
}

func (x *Databases_SnowflakeSettings_Connections) GetDatascience() *SnowflakeConfig {
	if x != nil {
		return x.Datascience
	}
	return nil
}

func (x *Databases_SnowflakeSettings_Connections) GetSmartdrive() *SnowflakeConfig {
	if x != nil {
		return x.Smartdrive
	}
	return nil
}

func (x *Databases_SnowflakeSettings_Connections) GetFmcsaLocalPuller() *SnowflakeConfig {
	if x != nil {
		return x.FmcsaLocalPuller
	}
	return nil
}

func (x *Databases_SnowflakeSettings_Connections) GetAnalyticsCore() *SnowflakeConfig {
	if x != nil {
		return x.AnalyticsCore
	}
	return nil
}

func (x *Databases_SnowflakeSettings_Connections) GetSnapsheet() *SnowflakeConfig {
	if x != nil {
		return x.Snapsheet
	}
	return nil
}

type JobberProcessorConfig_StoreConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type              JobberProcessorConfig_StoreConfig_Type               `protobuf:"varint,1,opt,name=type,proto3,enum=config.JobberProcessorConfig_StoreConfig_Type" json:"type,omitempty"`
	PgTableBackedSpec *JobberProcessorConfig_StoreConfig_PgTableBackedSpec `protobuf:"bytes,2,opt,name=pgTableBackedSpec,proto3" json:"pgTableBackedSpec,omitempty"`
}

func (x *JobberProcessorConfig_StoreConfig) Reset() {
	*x = JobberProcessorConfig_StoreConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_StoreConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_StoreConfig) ProtoMessage() {}

func (x *JobberProcessorConfig_StoreConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_StoreConfig.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_StoreConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 0}
}

func (x *JobberProcessorConfig_StoreConfig) GetType() JobberProcessorConfig_StoreConfig_Type {
	if x != nil {
		return x.Type
	}
	return JobberProcessorConfig_StoreConfig_Embedded
}

func (x *JobberProcessorConfig_StoreConfig) GetPgTableBackedSpec() *JobberProcessorConfig_StoreConfig_PgTableBackedSpec {
	if x != nil {
		return x.PgTableBackedSpec
	}
	return nil
}

type JobberProcessorConfig_MonitorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type           JobberProcessorConfig_MonitorConfig_Type            `protobuf:"varint,1,opt,name=type,proto3,enum=config.JobberProcessorConfig_MonitorConfig_Type" json:"type,omitempty"`
	EcsMonitorSpec *JobberProcessorConfig_MonitorConfig_EcsMonitorSpec `protobuf:"bytes,2,opt,name=ecsMonitorSpec,proto3" json:"ecsMonitorSpec,omitempty"`
}

func (x *JobberProcessorConfig_MonitorConfig) Reset() {
	*x = JobberProcessorConfig_MonitorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_MonitorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_MonitorConfig) ProtoMessage() {}

func (x *JobberProcessorConfig_MonitorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_MonitorConfig.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_MonitorConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 1}
}

func (x *JobberProcessorConfig_MonitorConfig) GetType() JobberProcessorConfig_MonitorConfig_Type {
	if x != nil {
		return x.Type
	}
	return JobberProcessorConfig_MonitorConfig_Embedded
}

func (x *JobberProcessorConfig_MonitorConfig) GetEcsMonitorSpec() *JobberProcessorConfig_MonitorConfig_EcsMonitorSpec {
	if x != nil {
		return x.EcsMonitorSpec
	}
	return nil
}

type JobberProcessorConfig_RegistryConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobsMetrics        []*JobberProcessorConfig_RegistryConfig_JobMetrics `protobuf:"bytes,1,rep,name=jobsMetrics,proto3" json:"jobsMetrics,omitempty"`
	DefaultJobMetrics  []*JobberProcessorConfig_RegistryConfig_JobMetric  `protobuf:"bytes,2,rep,name=defaultJobMetrics,proto3" json:"defaultJobMetrics,omitempty"`
	DefaultTaskMetrics []*JobberProcessorConfig_RegistryConfig_TaskMetric `protobuf:"bytes,3,rep,name=defaultTaskMetrics,proto3" json:"defaultTaskMetrics,omitempty"`
}

func (x *JobberProcessorConfig_RegistryConfig) Reset() {
	*x = JobberProcessorConfig_RegistryConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_RegistryConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_RegistryConfig) ProtoMessage() {}

func (x *JobberProcessorConfig_RegistryConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_RegistryConfig.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_RegistryConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 2}
}

func (x *JobberProcessorConfig_RegistryConfig) GetJobsMetrics() []*JobberProcessorConfig_RegistryConfig_JobMetrics {
	if x != nil {
		return x.JobsMetrics
	}
	return nil
}

func (x *JobberProcessorConfig_RegistryConfig) GetDefaultJobMetrics() []*JobberProcessorConfig_RegistryConfig_JobMetric {
	if x != nil {
		return x.DefaultJobMetrics
	}
	return nil
}

func (x *JobberProcessorConfig_RegistryConfig) GetDefaultTaskMetrics() []*JobberProcessorConfig_RegistryConfig_TaskMetric {
	if x != nil {
		return x.DefaultTaskMetrics
	}
	return nil
}

type JobberProcessorConfig_StuckJobDetectorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StuckJobRules      []*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule   `protobuf:"bytes,4,rep,name=stuckJobRules,proto3" json:"stuckJobRules,omitempty"`
	DefaultStuckRules  []*JobberProcessorConfig_StuckJobDetectorConfig_StuckRule      `protobuf:"bytes,5,rep,name=defaultStuckRules,proto3" json:"defaultStuckRules,omitempty"`
	StuckJobActions    []*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction `protobuf:"bytes,6,rep,name=stuckJobActions,proto3" json:"stuckJobActions,omitempty"`
	DefaultStuckAction *JobberProcessorConfig_StuckJobDetectorConfig_StuckAction      `protobuf:"bytes,7,opt,name=defaultStuckAction,proto3" json:"defaultStuckAction,omitempty"`
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig) Reset() {
	*x = JobberProcessorConfig_StuckJobDetectorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_StuckJobDetectorConfig) ProtoMessage() {}

func (x *JobberProcessorConfig_StuckJobDetectorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_StuckJobDetectorConfig.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_StuckJobDetectorConfig) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 3}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig) GetStuckJobRules() []*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule {
	if x != nil {
		return x.StuckJobRules
	}
	return nil
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig) GetDefaultStuckRules() []*JobberProcessorConfig_StuckJobDetectorConfig_StuckRule {
	if x != nil {
		return x.DefaultStuckRules
	}
	return nil
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig) GetStuckJobActions() []*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction {
	if x != nil {
		return x.StuckJobActions
	}
	return nil
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig) GetDefaultStuckAction() *JobberProcessorConfig_StuckJobDetectorConfig_StuckAction {
	if x != nil {
		return x.DefaultStuckAction
	}
	return nil
}

type JobberProcessorConfig_StoreConfig_EmbeddedSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *JobberProcessorConfig_StoreConfig_EmbeddedSpec) Reset() {
	*x = JobberProcessorConfig_StoreConfig_EmbeddedSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_StoreConfig_EmbeddedSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_StoreConfig_EmbeddedSpec) ProtoMessage() {}

func (x *JobberProcessorConfig_StoreConfig_EmbeddedSpec) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_StoreConfig_EmbeddedSpec.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_StoreConfig_EmbeddedSpec) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 0, 0}
}

type JobberProcessorConfig_StoreConfig_PgTableBackedSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TableNameWithSchema string `protobuf:"bytes,1,opt,name=tableNameWithSchema,proto3" json:"tableNameWithSchema,omitempty"`
	StoreId             string `protobuf:"bytes,2,opt,name=storeId,proto3" json:"storeId,omitempty"`
}

func (x *JobberProcessorConfig_StoreConfig_PgTableBackedSpec) Reset() {
	*x = JobberProcessorConfig_StoreConfig_PgTableBackedSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_StoreConfig_PgTableBackedSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_StoreConfig_PgTableBackedSpec) ProtoMessage() {}

func (x *JobberProcessorConfig_StoreConfig_PgTableBackedSpec) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_StoreConfig_PgTableBackedSpec.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_StoreConfig_PgTableBackedSpec) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 0, 1}
}

func (x *JobberProcessorConfig_StoreConfig_PgTableBackedSpec) GetTableNameWithSchema() string {
	if x != nil {
		return x.TableNameWithSchema
	}
	return ""
}

func (x *JobberProcessorConfig_StoreConfig_PgTableBackedSpec) GetStoreId() string {
	if x != nil {
		return x.StoreId
	}
	return ""
}

type JobberProcessorConfig_MonitorConfig_EcsMonitorSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *JobberProcessorConfig_MonitorConfig_EcsMonitorSpec) Reset() {
	*x = JobberProcessorConfig_MonitorConfig_EcsMonitorSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_MonitorConfig_EcsMonitorSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_MonitorConfig_EcsMonitorSpec) ProtoMessage() {}

func (x *JobberProcessorConfig_MonitorConfig_EcsMonitorSpec) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_MonitorConfig_EcsMonitorSpec.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_MonitorConfig_EcsMonitorSpec) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 1, 0}
}

func (x *JobberProcessorConfig_MonitorConfig_EcsMonitorSpec) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type JobberProcessorConfig_RegistryConfig_TaskMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       JobberProcessorConfig_RegistryConfig_TaskMetric_Name `protobuf:"varint,1,opt,name=name,proto3,enum=config.JobberProcessorConfig_RegistryConfig_TaskMetric_Name" json:"name,omitempty"`
	Enabled    *bool                                                `protobuf:"varint,2,opt,name=enabled,proto3,oneof" json:"enabled,omitempty"`
	SampleRate *float32                                             `protobuf:"fixed32,3,opt,name=sampleRate,proto3,oneof" json:"sampleRate,omitempty"`
}

func (x *JobberProcessorConfig_RegistryConfig_TaskMetric) Reset() {
	*x = JobberProcessorConfig_RegistryConfig_TaskMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_RegistryConfig_TaskMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_RegistryConfig_TaskMetric) ProtoMessage() {}

func (x *JobberProcessorConfig_RegistryConfig_TaskMetric) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_RegistryConfig_TaskMetric.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_RegistryConfig_TaskMetric) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 2, 0}
}

func (x *JobberProcessorConfig_RegistryConfig_TaskMetric) GetName() JobberProcessorConfig_RegistryConfig_TaskMetric_Name {
	if x != nil {
		return x.Name
	}
	return JobberProcessorConfig_RegistryConfig_TaskMetric_Duration
}

func (x *JobberProcessorConfig_RegistryConfig_TaskMetric) GetEnabled() bool {
	if x != nil && x.Enabled != nil {
		return *x.Enabled
	}
	return false
}

func (x *JobberProcessorConfig_RegistryConfig_TaskMetric) GetSampleRate() float32 {
	if x != nil && x.SampleRate != nil {
		return *x.SampleRate
	}
	return 0
}

type JobberProcessorConfig_RegistryConfig_JobMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       JobberProcessorConfig_RegistryConfig_JobMetric_Name `protobuf:"varint,1,opt,name=name,proto3,enum=config.JobberProcessorConfig_RegistryConfig_JobMetric_Name" json:"name,omitempty"`
	Enabled    *bool                                               `protobuf:"varint,2,opt,name=enabled,proto3,oneof" json:"enabled,omitempty"`
	SampleRate *float32                                            `protobuf:"fixed32,3,opt,name=sampleRate,proto3,oneof" json:"sampleRate,omitempty"`
}

func (x *JobberProcessorConfig_RegistryConfig_JobMetric) Reset() {
	*x = JobberProcessorConfig_RegistryConfig_JobMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_RegistryConfig_JobMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_RegistryConfig_JobMetric) ProtoMessage() {}

func (x *JobberProcessorConfig_RegistryConfig_JobMetric) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_RegistryConfig_JobMetric.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_RegistryConfig_JobMetric) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 2, 1}
}

func (x *JobberProcessorConfig_RegistryConfig_JobMetric) GetName() JobberProcessorConfig_RegistryConfig_JobMetric_Name {
	if x != nil {
		return x.Name
	}
	return JobberProcessorConfig_RegistryConfig_JobMetric_Duration
}

func (x *JobberProcessorConfig_RegistryConfig_JobMetric) GetEnabled() bool {
	if x != nil && x.Enabled != nil {
		return *x.Enabled
	}
	return false
}

func (x *JobberProcessorConfig_RegistryConfig_JobMetric) GetSampleRate() float32 {
	if x != nil && x.SampleRate != nil {
		return *x.SampleRate
	}
	return 0
}

type JobberProcessorConfig_RegistryConfig_TaskMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId  string                                             `protobuf:"bytes,1,opt,name=taskId,proto3" json:"taskId,omitempty"`
	Metrics []*JobberProcessorConfig_RegistryConfig_TaskMetric `protobuf:"bytes,2,rep,name=metrics,proto3" json:"metrics,omitempty"`
}

func (x *JobberProcessorConfig_RegistryConfig_TaskMetrics) Reset() {
	*x = JobberProcessorConfig_RegistryConfig_TaskMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_RegistryConfig_TaskMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_RegistryConfig_TaskMetrics) ProtoMessage() {}

func (x *JobberProcessorConfig_RegistryConfig_TaskMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_RegistryConfig_TaskMetrics.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_RegistryConfig_TaskMetrics) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 2, 2}
}

func (x *JobberProcessorConfig_RegistryConfig_TaskMetrics) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *JobberProcessorConfig_RegistryConfig_TaskMetrics) GetMetrics() []*JobberProcessorConfig_RegistryConfig_TaskMetric {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type JobberProcessorConfig_RegistryConfig_JobMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobRegistryKey string                                              `protobuf:"bytes,1,opt,name=jobRegistryKey,proto3" json:"jobRegistryKey,omitempty"`
	Metrics        []*JobberProcessorConfig_RegistryConfig_JobMetric   `protobuf:"bytes,2,rep,name=metrics,proto3" json:"metrics,omitempty"`
	TasksMetrics   []*JobberProcessorConfig_RegistryConfig_TaskMetrics `protobuf:"bytes,3,rep,name=tasksMetrics,proto3" json:"tasksMetrics,omitempty"`
}

func (x *JobberProcessorConfig_RegistryConfig_JobMetrics) Reset() {
	*x = JobberProcessorConfig_RegistryConfig_JobMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_RegistryConfig_JobMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_RegistryConfig_JobMetrics) ProtoMessage() {}

func (x *JobberProcessorConfig_RegistryConfig_JobMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_RegistryConfig_JobMetrics.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_RegistryConfig_JobMetrics) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 2, 3}
}

func (x *JobberProcessorConfig_RegistryConfig_JobMetrics) GetJobRegistryKey() string {
	if x != nil {
		return x.JobRegistryKey
	}
	return ""
}

func (x *JobberProcessorConfig_RegistryConfig_JobMetrics) GetMetrics() []*JobberProcessorConfig_RegistryConfig_JobMetric {
	if x != nil {
		return x.Metrics
	}
	return nil
}

func (x *JobberProcessorConfig_RegistryConfig_JobMetrics) GetTasksMetrics() []*JobberProcessorConfig_RegistryConfig_TaskMetrics {
	if x != nil {
		return x.TasksMetrics
	}
	return nil
}

type JobberProcessorConfig_StuckJobDetectorConfig_StuckRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleId             JobberProcessorConfig_StuckJobDetectorConfig_RuleId `protobuf:"varint,1,opt,name=ruleId,proto3,enum=config.JobberProcessorConfig_StuckJobDetectorConfig_RuleId" json:"ruleId,omitempty"`
	ThresholdInSeconds int32                                               `protobuf:"varint,2,opt,name=thresholdInSeconds,proto3" json:"thresholdInSeconds,omitempty"`
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckRule) Reset() {
	*x = JobberProcessorConfig_StuckJobDetectorConfig_StuckRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_StuckJobDetectorConfig_StuckRule) ProtoMessage() {}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckRule) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_StuckJobDetectorConfig_StuckRule.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_StuckJobDetectorConfig_StuckRule) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 3, 0}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckRule) GetRuleId() JobberProcessorConfig_StuckJobDetectorConfig_RuleId {
	if x != nil {
		return x.RuleId
	}
	return JobberProcessorConfig_StuckJobDetectorConfig_NotPicked
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckRule) GetThresholdInSeconds() int32 {
	if x != nil {
		return x.ThresholdInSeconds
	}
	return 0
}

type JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobRegistryKey string                                                    `protobuf:"bytes,1,opt,name=jobRegistryKey,proto3" json:"jobRegistryKey,omitempty"`
	StuckRules     []*JobberProcessorConfig_StuckJobDetectorConfig_StuckRule `protobuf:"bytes,2,rep,name=stuckRules,proto3" json:"stuckRules,omitempty"`
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule) Reset() {
	*x = JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule) ProtoMessage() {}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 3, 1}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule) GetJobRegistryKey() string {
	if x != nil {
		return x.JobRegistryKey
	}
	return ""
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule) GetStuckRules() []*JobberProcessorConfig_StuckJobDetectorConfig_StuckRule {
	if x != nil {
		return x.StuckRules
	}
	return nil
}

type JobberProcessorConfig_StuckJobDetectorConfig_StuckAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActionId          JobberProcessorConfig_StuckJobDetectorConfig_ActionId           `protobuf:"varint,1,opt,name=actionId,proto3,enum=config.JobberProcessorConfig_StuckJobDetectorConfig_ActionId" json:"actionId,omitempty"`
	SlackActionParams *JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams `protobuf:"bytes,2,opt,name=slackActionParams,proto3" json:"slackActionParams,omitempty"`
	LogActionParams   *JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams   `protobuf:"bytes,3,opt,name=logActionParams,proto3" json:"logActionParams,omitempty"`
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckAction) Reset() {
	*x = JobberProcessorConfig_StuckJobDetectorConfig_StuckAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_StuckJobDetectorConfig_StuckAction) ProtoMessage() {}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckAction) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_StuckJobDetectorConfig_StuckAction.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_StuckJobDetectorConfig_StuckAction) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 3, 2}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckAction) GetActionId() JobberProcessorConfig_StuckJobDetectorConfig_ActionId {
	if x != nil {
		return x.ActionId
	}
	return JobberProcessorConfig_StuckJobDetectorConfig_LogAction
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckAction) GetSlackActionParams() *JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams {
	if x != nil {
		return x.SlackActionParams
	}
	return nil
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckAction) GetLogActionParams() *JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams {
	if x != nil {
		return x.LogActionParams
	}
	return nil
}

type JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Channel string `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams) Reset() {
	*x = JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams) ProtoMessage() {}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 3, 3}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

type JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoggerName string `protobuf:"bytes,1,opt,name=loggerName,proto3" json:"loggerName,omitempty"`
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams) Reset() {
	*x = JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams) ProtoMessage() {}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 3, 4}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams) GetLoggerName() string {
	if x != nil {
		return x.LoggerName
	}
	return ""
}

type JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobRegistryKey string                                                    `protobuf:"bytes,1,opt,name=jobRegistryKey,proto3" json:"jobRegistryKey,omitempty"`
	StuckAction    *JobberProcessorConfig_StuckJobDetectorConfig_StuckAction `protobuf:"bytes,2,opt,name=stuckAction,proto3" json:"stuckAction,omitempty"`
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction) Reset() {
	*x = JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_config_config_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction) ProtoMessage() {}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction.ProtoReflect.Descriptor instead.
func (*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{56, 3, 5}
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction) GetJobRegistryKey() string {
	if x != nil {
		return x.JobRegistryKey
	}
	return ""
}

func (x *JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction) GetStuckAction() *JobberProcessorConfig_StuckJobDetectorConfig_StuckAction {
	if x != nil {
		return x.StuckAction
	}
	return nil
}

var File_config_config_proto protoreflect.FileDescriptor

var file_config_config_proto_rawDesc = []byte{
	0x0a, 0x13, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x89, 0x01,
	0x0a, 0x0b, 0x44, 0x42, 0x53, 0x53, 0x4c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2b, 0x0a,
	0x07, 0x73, 0x73, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x42, 0x53, 0x53, 0x4c, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x07, 0x73, 0x73, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x13, 0x73, 0x73,
	0x6c, 0x52, 0x6f, 0x6f, 0x74, 0x43, 0x65, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x13, 0x73, 0x73, 0x6c, 0x52, 0x6f,
	0x6f, 0x74, 0x43, 0x65, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x73, 0x73, 0x6c, 0x52, 0x6f, 0x6f, 0x74, 0x43, 0x65, 0x72,
	0x74, 0x46, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xcf, 0x01, 0x0a, 0x08, 0x44, 0x42,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x36, 0x0a, 0x09, 0x73, 0x73,
	0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x42, 0x53, 0x53, 0x4c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x48, 0x00, 0x52, 0x09, 0x73, 0x73, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88,
	0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x73, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x22, 0x72, 0x0a, 0x0f, 0x53,
	0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x62, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x62, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x21,
	0x0a, 0x09, 0x77, 0x61, 0x72, 0x65, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x09, 0x77, 0x61, 0x72, 0x65, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x77, 0x61, 0x72, 0x65, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x22,
	0x8f, 0x01, 0x0a, 0x0d, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x72,
	0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x88, 0x01, 0x01, 0x3a, 0x02, 0x18, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x72, 0x6f, 0x6c,
	0x65, 0x22, 0x96, 0x01, 0x0a, 0x14, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x4b,
	0x65, 0x79, 0x50, 0x61, 0x69, 0x72, 0x55, 0x73, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x17, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0xac, 0x0a, 0x0a, 0x09, 0x44,
	0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x07, 0x6e, 0x69, 0x72, 0x76,
	0x61, 0x6e, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x44, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x07, 0x6e, 0x69, 0x72,
	0x76, 0x61, 0x6e, 0x61, 0x12, 0x26, 0x0a, 0x05, 0x6e, 0x68, 0x74, 0x73, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x42, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x6e, 0x68, 0x74, 0x73, 0x61, 0x12, 0x26, 0x0a, 0x05,
	0x66, 0x6d, 0x63, 0x73, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x66,
	0x6d, 0x63, 0x73, 0x61, 0x12, 0x41, 0x0a, 0x09, 0x73, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x53, 0x6e, 0x6f, 0x77, 0x66,
	0x6c, 0x61, 0x6b, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x09, 0x73, 0x6e,
	0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x12, 0x26, 0x0a, 0x05, 0x6e, 0x65, 0x6f, 0x34, 0x6a,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x44, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x6e, 0x65, 0x6f, 0x34, 0x6a, 0x12,
	0x26, 0x0a, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73, 0x12, 0x36, 0x0a, 0x0d, 0x66, 0x6d, 0x63, 0x73, 0x61,
	0x52, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0d, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x52, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c, 0x79, 0x12,
	0x30, 0x0a, 0x0a, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x57, 0x72, 0x69, 0x74, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x42, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x57, 0x72, 0x69, 0x74,
	0x65, 0x12, 0x20, 0x0a, 0x02, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x02, 0x64, 0x73, 0x1a, 0xf7, 0x06, 0x0a, 0x11, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3f, 0x0a, 0x05, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x73, 0x2e, 0x53, 0x6e, 0x6f, 0x77,
	0x66, 0x6c, 0x61, 0x6b, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x51, 0x0a, 0x0b, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73,
	0x65, 0x73, 0x2e, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xd7, 0x02,
	0x0a, 0x05, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x2b, 0x0a, 0x05, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x12, 0x37, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x73, 0x63, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x73, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x2b, 0x0a,
	0x05, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x05, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x12, 0x41, 0x0a, 0x10, 0x66, 0x6d,
	0x63, 0x73, 0x61, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x50, 0x75, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x6e,
	0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x10, 0x66, 0x6d, 0x63,
	0x73, 0x61, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x50, 0x75, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x3c, 0x0a,
	0x0e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53,
	0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0d, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x72, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x73,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x65, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b,
	0x65, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x69, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x09, 0x73, 0x6e,
	0x61, 0x70, 0x73, 0x68, 0x65, 0x65, 0x74, 0x1a, 0xf3, 0x02, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x66, 0x6d, 0x63, 0x73, 0x61,
	0x5f, 0x64, 0x62, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x08, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x44, 0x62, 0x74, 0x12, 0x39, 0x0a,
	0x0b, 0x64, 0x61, 0x74, 0x61, 0x73, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x6e, 0x6f, 0x77,
	0x66, 0x6c, 0x61, 0x6b, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x73, 0x6d, 0x61, 0x72,
	0x74, 0x64, 0x72, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x64, 0x72, 0x69, 0x76,
	0x65, 0x12, 0x43, 0x0a, 0x10, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x50,
	0x75, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x4c, 0x6f, 0x63, 0x61, 0x6c,
	0x50, 0x75, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x0e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x43, 0x6f, 0x72, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x65, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x53, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x09, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x65, 0x65, 0x74, 0x4a, 0x04, 0x08,
	0x04, 0x10, 0x05, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x22, 0x26, 0x0a, 0x08, 0x53, 0x63, 0x72,
	0x61, 0x70, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x4a, 0x04, 0x08, 0x01, 0x10,
	0x02, 0x22, 0x72, 0x0a, 0x0a, 0x4d, 0x56, 0x52, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x34, 0x0a, 0x15, 0x6d, 0x76, 0x72, 0x43, 0x61, 0x63, 0x68, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15,
	0x6d, 0x76, 0x72, 0x43, 0x61, 0x63, 0x68, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x6d, 0x76, 0x72, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x12, 0x6d, 0x76, 0x72, 0x43, 0x61, 0x63, 0x68, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x50, 0x6f, 0x72, 0x74, 0x22, 0x9f, 0x01, 0x0a, 0x0d, 0x4f, 0x41, 0x75, 0x74, 0x68, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x61, 0x6d, 0x73, 0x61,
	0x72, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x53, 0x61, 0x6d, 0x73, 0x61, 0x72,
	0x61, 0x12, 0x24, 0x0a, 0x0d, 0x53, 0x61, 0x6d, 0x73, 0x61, 0x72, 0x61, 0x53, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x53, 0x61, 0x6d, 0x73, 0x61, 0x72,
	0x61, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x4b, 0x65, 0x65, 0x70, 0x54,
	0x72, 0x75, 0x63, 0x6b, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4b, 0x65,
	0x65, 0x70, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x69, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x4b, 0x65, 0x65,
	0x70, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x69, 0x6e, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x4b, 0x65, 0x65, 0x70, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x69,
	0x6e, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x22, 0xf7, 0x06, 0x0a, 0x08, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x18, 0x6f, 0x74, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x72, 0x4f, 0x54, 0x4c, 0x50, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x6f, 0x74, 0x65, 0x6c, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x72, 0x4f, 0x54, 0x4c, 0x50, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x12, 0x30, 0x0a, 0x13, 0x6d, 0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6d,
	0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x64,
	0x64, 0x72, 0x12, 0x32, 0x0a, 0x0a, 0x6d, 0x76, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x4d, 0x56, 0x52, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0a, 0x6d, 0x76, 0x72, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x47, 0x52, 0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x47, 0x52,
	0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x12, 0x30, 0x0a, 0x13, 0x64, 0x61, 0x74, 0x61, 0x49, 0x6e,
	0x66, 0x72, 0x61, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x64, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x72, 0x61, 0x4a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x12, 0x2b, 0x0a, 0x05, 0x6f, 0x61, 0x75, 0x74,
	0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x4f, 0x41, 0x75, 0x74, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x05,
	0x6f, 0x61, 0x75, 0x74, 0x68, 0x12, 0x2c, 0x0a, 0x11, 0x71, 0x75, 0x6f, 0x74, 0x69, 0x6e, 0x67,
	0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x71, 0x75, 0x6f, 0x74, 0x69, 0x6e, 0x67, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x41,
	0x64, 0x64, 0x72, 0x12, 0x38, 0x0a, 0x17, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x47, 0x52, 0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x47, 0x52, 0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x12, 0x32, 0x0a,
	0x14, 0x64, 0x69, 0x73, 0x74, 0x73, 0x65, 0x6d, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x74, 0x6f,
	0x6e, 0x41, 0x64, 0x64, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x64, 0x69, 0x73,
	0x74, 0x73, 0x65, 0x6d, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x74, 0x6f, 0x6e, 0x41, 0x64, 0x64,
	0x72, 0x12, 0x2a, 0x0a, 0x10, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65,
	0x72, 0x41, 0x64, 0x64, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x71, 0x75, 0x6f,
	0x74, 0x65, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x12, 0x26, 0x0a,
	0x06, 0x70, 0x64, 0x66, 0x47, 0x65, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50, 0x44, 0x46, 0x47, 0x65, 0x6e, 0x52, 0x06, 0x70,
	0x64, 0x66, 0x47, 0x65, 0x6e, 0x12, 0x32, 0x0a, 0x14, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x47, 0x52, 0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x47, 0x52, 0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x12, 0x3c, 0x0a, 0x19, 0x66, 0x6d, 0x63,
	0x73, 0x61, 0x44, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x47, 0x52,
	0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x66, 0x6d,
	0x63, 0x73, 0x61, 0x44, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x47,
	0x52, 0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x6c, 0x6c, 0x6d, 0x4f, 0x70,
	0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x6c, 0x6c, 0x6d, 0x4f, 0x70, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x64, 0x64, 0x72, 0x12, 0x34, 0x0a, 0x15, 0x6c, 0x6c, 0x6d, 0x4f, 0x70, 0x73, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x47, 0x52, 0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6c, 0x6c, 0x6d, 0x4f, 0x70, 0x73, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x47, 0x52, 0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x12, 0x30, 0x0a, 0x13, 0x75,
	0x77, 0x41, 0x49, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x47, 0x52, 0x50, 0x43, 0x41, 0x64,
	0x64, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x75, 0x77, 0x41, 0x49, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x47, 0x52, 0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x4a, 0x04, 0x08,
	0x01, 0x10, 0x02, 0x4a, 0x04, 0x08, 0x0a, 0x10, 0x0b, 0x52, 0x14, 0x74, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x74, 0x41, 0x50, 0x49, 0x55, 0x72, 0x6c, 0x52,
	0x20, 0x74, 0x72, 0x75, 0x63, 0x6b, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x41, 0x75, 0x74,
	0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x64, 0x64,
	0x72, 0x22, 0x5a, 0x0a, 0x06, 0x50, 0x44, 0x46, 0x47, 0x65, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x73,
	0x65, 0x6d, 0x61, 0x70, 0x68, 0x6f, 0x72, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x65, 0x6d, 0x61, 0x70, 0x68, 0x6f, 0x72, 0x65, 0x57,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x64, 0x66, 0x67, 0x65, 0x6e, 0x47,
	0x52, 0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70,
	0x64, 0x66, 0x67, 0x65, 0x6e, 0x47, 0x52, 0x50, 0x43, 0x41, 0x64, 0x64, 0x72, 0x22, 0x43, 0x0a,
	0x07, 0x54, 0x72, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x61,
	0x74, 0x65, 0x22, 0x7f, 0x0a, 0x07, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x24, 0x0a,
	0x0d, 0x48, 0x75, 0x6d, 0x61, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x48, 0x75, 0x6d, 0x61, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x6e,
	0x69, 0x63, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50,
	0x61, 0x6e, 0x69, 0x63, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x46, 0x78, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x46, 0x78, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x22, 0x68, 0x0a, 0x07, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x65, 0x63,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x65, 0x63, 0x73, 0x22, 0x30, 0x0a,
	0x0a, 0x4c, 0x61, 0x6d, 0x62, 0x64, 0x61, 0x47, 0x72, 0x70, 0x63, 0x12, 0x22, 0x0a, 0x0c, 0x66,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0xe8, 0x01, 0x0a, 0x05, 0x49, 0x6e, 0x66, 0x72, 0x61, 0x12, 0x29, 0x0a, 0x07, 0x74, 0x72, 0x61,
	0x63, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x74, 0x72, 0x61,
	0x63, 0x69, 0x6e, 0x67, 0x12, 0x29, 0x0a, 0x07, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c,
	0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x12,
	0x32, 0x0a, 0x0a, 0x6c, 0x61, 0x6d, 0x62, 0x64, 0x61, 0x47, 0x72, 0x70, 0x63, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x61, 0x6d,
	0x62, 0x64, 0x61, 0x47, 0x72, 0x70, 0x63, 0x52, 0x0a, 0x6c, 0x61, 0x6d, 0x62, 0x64, 0x61, 0x47,
	0x72, 0x70, 0x63, 0x12, 0x29, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x2a,
	0x0a, 0x10, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x23, 0x0a, 0x03, 0x41, 0x57,
	0x53, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22,
	0xdc, 0x01, 0x0a, 0x06, 0x56, 0x65, 0x72, 0x69, 0x73, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x73,
	0x65, 0x43, 0x65, 0x72, 0x74, 0x41, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x75, 0x73, 0x65, 0x43, 0x65, 0x72, 0x74, 0x41, 0x75, 0x74, 0x68, 0x12, 0x2a, 0x0a, 0x10,
	0x62, 0x61, 0x73, 0x69, 0x63, 0x41, 0x75, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x64, 0x55, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x61, 0x73, 0x69, 0x63, 0x41, 0x75, 0x74,
	0x68, 0x50, 0x72, 0x6f, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x65, 0x72, 0x74,
	0x41, 0x75, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x64, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x63, 0x65, 0x72, 0x74, 0x41, 0x75, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x64, 0x55,
	0x72, 0x6c, 0x12, 0x2c, 0x0a, 0x11, 0x6c, 0x65, 0x78, 0x69, 0x73, 0x4e, 0x65, 0x78, 0x69, 0x73,
	0x50, 0x72, 0x6f, 0x64, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6c,
	0x65, 0x78, 0x69, 0x73, 0x4e, 0x65, 0x78, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x64, 0x55, 0x72, 0x6c,
	0x12, 0x2c, 0x0a, 0x11, 0x6c, 0x65, 0x78, 0x69, 0x73, 0x4e, 0x65, 0x78, 0x69, 0x73, 0x54, 0x65,
	0x73, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6c, 0x65, 0x78,
	0x69, 0x73, 0x4e, 0x65, 0x78, 0x69, 0x73, 0x54, 0x65, 0x73, 0x74, 0x55, 0x72, 0x6c, 0x22, 0x73,
	0x0a, 0x0d, 0x53, 0x33, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x4b, 0x65, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x4b, 0x65, 0x79, 0x22, 0x53, 0x0a, 0x1d, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x4f, 0x41, 0x75, 0x74, 0x68, 0x41, 0x70, 0x70, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x70,
	0x70, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x70, 0x70, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0x4b, 0x0a, 0x13, 0x44, 0x42, 0x54, 0x43,
	0x6c, 0x6f, 0x75, 0x64, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12,
	0x1c, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x70, 0x69, 0x4b, 0x65, 0x79, 0x22, 0x5d, 0x0a, 0x13, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x61, 0x62, 0x6c,
	0x65, 0x4b, 0x65, 0x79, 0x22, 0x2d, 0x0a, 0x13, 0x48, 0x65, 0x72, 0x65, 0x4d, 0x61, 0x70, 0x73,
	0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69,
	0x4b, 0x65, 0x79, 0x22, 0x2e, 0x0a, 0x14, 0x4f, 0x70, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x65, 0x6f,
	0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69,
	0x4b, 0x65, 0x79, 0x22, 0x48, 0x0a, 0x0e, 0x43, 0x6d, 0x74, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x1e, 0x0a,
	0x0a, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x22, 0xb6, 0x05,
	0x0a, 0x15, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x72, 0x65, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x3f, 0x0a, 0x07, 0x73, 0x61, 0x6d, 0x73, 0x61,
	0x72, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x4f, 0x41, 0x75, 0x74,
	0x68, 0x41, 0x70, 0x70, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52,
	0x07, 0x73, 0x61, 0x6d, 0x73, 0x61, 0x72, 0x61, 0x12, 0x35, 0x0a, 0x0a, 0x73, 0x70, 0x65, 0x65,
	0x64, 0x47, 0x61, 0x75, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x33, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x61, 0x6c, 0x73, 0x52, 0x0a, 0x73, 0x70, 0x65, 0x65, 0x64, 0x47, 0x61, 0x75, 0x67, 0x65, 0x12,
	0x26, 0x0a, 0x0e, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x6e, 0x41, 0x70, 0x69, 0x4b, 0x65,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x61,
	0x6e, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x0b, 0x6b, 0x65, 0x65, 0x70, 0x54,
	0x72, 0x75, 0x63, 0x6b, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x4f, 0x41, 0x75, 0x74, 0x68, 0x41, 0x70, 0x70, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x61, 0x6c, 0x73, 0x52, 0x0b, 0x6b, 0x65, 0x65, 0x70, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x69, 0x6e,
	0x12, 0x4b, 0x0a, 0x0d, 0x73, 0x61, 0x6d, 0x73, 0x61, 0x72, 0x61, 0x53, 0x61, 0x66, 0x65, 0x74,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x4f, 0x41, 0x75, 0x74, 0x68,
	0x41, 0x70, 0x70, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x0d,
	0x73, 0x61, 0x6d, 0x73, 0x61, 0x72, 0x61, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x12, 0x53, 0x0a,
	0x11, 0x6b, 0x65, 0x65, 0x70, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x69, 0x6e, 0x53, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x4f, 0x41, 0x75, 0x74,
	0x68, 0x41, 0x70, 0x70, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52,
	0x11, 0x6b, 0x65, 0x65, 0x70, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x69, 0x6e, 0x53, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x12, 0x37, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x73, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x37, 0x0a, 0x08, 0x68,
	0x65, 0x72, 0x65, 0x6d, 0x61, 0x70, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x48, 0x65, 0x72, 0x65, 0x4d, 0x61, 0x70, 0x73, 0x43,
	0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x08, 0x68, 0x65, 0x72, 0x65,
	0x6d, 0x61, 0x70, 0x73, 0x12, 0x3a, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x6d, 0x65, 0x74, 0x65,
	0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x65, 0x6f, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x6d, 0x65, 0x74, 0x65, 0x6f,
	0x12, 0x26, 0x0a, 0x0e, 0x63, 0x61, 0x72, 0x66, 0x61, 0x78, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61, 0x72, 0x66, 0x61, 0x78,
	0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x03, 0x63, 0x6d, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43,
	0x6d, 0x74, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x03, 0x63,
	0x6d, 0x74, 0x4a, 0x04, 0x08, 0x07, 0x10, 0x08, 0x52, 0x0c, 0x74, 0x72, 0x75, 0x63, 0x6b, 0x65,
	0x72, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x22, 0x98, 0x01, 0x0a, 0x16, 0x4b, 0x65, 0x65, 0x70, 0x54,
	0x72, 0x75, 0x63, 0x6b, 0x69, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6d, 0x61,
	0x78, 0x43, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x73, 0x4a,
	0x04, 0x08, 0x01, 0x10, 0x02, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x03, 0x10,
	0x04, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x05, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x52, 0x0c, 0x70,
	0x72, 0x6f, 0x78, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x13, 0x70, 0x72, 0x6f,
	0x78, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x52, 0x0f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x35, 0x0a, 0x09, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x72, 0x61,
	0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x75, 0x72, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x62, 0x75, 0x72, 0x73, 0x74, 0x22, 0xd9, 0x01, 0x0a, 0x12, 0x53, 0x61, 0x6d,
	0x73, 0x61, 0x72, 0x61, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x29, 0x0a, 0x06, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x52, 0x06, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x12, 0x47, 0x0a, 0x09, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x61, 0x6d, 0x73, 0x61, 0x72, 0x61, 0x49, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x1a, 0x4f, 0x0a, 0x0e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x27, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x45, 0x0a, 0x03, 0x55, 0x52, 0x4c, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x8e, 0x01, 0x0a, 0x13,
	0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x55, 0x52, 0x4c, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x12, 0x32, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x50, 0x65, 0x72, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x14, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x50, 0x65, 0x72,
	0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d,
	0x61, 0x78, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xe5, 0x02, 0x0a,
	0x13, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x55, 0x52, 0x4c, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x3b, 0x0a, 0x0f, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x52, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52,
	0x0f, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x7e, 0x0a, 0x1b, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x1b, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x1a, 0x61, 0x0a, 0x20, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x27, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x52, 0x09, 0x72, 0x61, 0x74, 0x65, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x22, 0x99, 0x02, 0x0a, 0x14, 0x4f, 0x70, 0x65, 0x6e, 0x4d, 0x65, 0x74,
	0x65, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a,
	0x1c, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65,
	0x63, 0x61, 0x73, 0x74, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x1a, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x63, 0x61, 0x6c, 0x46,
	0x6f, 0x72, 0x65, 0x63, 0x61, 0x73, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x29, 0x0a, 0x10, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x72, 0x63, 0x68, 0x69,
	0x76, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x72, 0x61,
	0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x52, 0x09, 0x72, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x3d, 0x0a, 0x1b, 0x68,
	0x69, 0x67, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x75, 0x74, 0x6f, 0x66, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x18, 0x68, 0x69, 0x67, 0x68, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x75, 0x74, 0x6f, 0x66, 0x66, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x61,
	0x78, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x6d, 0x61, 0x78, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x69, 0x7a, 0x65,
	0x22, 0x6a, 0x0a, 0x0e, 0x43, 0x6d, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x42,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x22, 0xa9, 0x03, 0x0a,
	0x23, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x6b, 0x65, 0x65, 0x70, 0x54, 0x72, 0x75, 0x63,
	0x6b, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x4b, 0x65, 0x65, 0x70, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x69, 0x6e, 0x49, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6b, 0x65, 0x65, 0x70, 0x54,
	0x72, 0x75, 0x63, 0x6b, 0x69, 0x6e, 0x12, 0x37, 0x0a, 0x08, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x6d,
	0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x6e, 0x12,
	0x34, 0x0a, 0x07, 0x73, 0x61, 0x6d, 0x73, 0x61, 0x72, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x61, 0x6d, 0x73, 0x61, 0x72,
	0x61, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x73, 0x61,
	0x6d, 0x73, 0x61, 0x72, 0x61, 0x12, 0x37, 0x0a, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x3a,
	0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x6d, 0x65, 0x74, 0x65, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x4d,
	0x65, 0x74, 0x65, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x09, 0x6f, 0x70, 0x65, 0x6e, 0x6d, 0x65, 0x74, 0x65, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x61,
	0x72, 0x66, 0x61, 0x78, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x61, 0x72, 0x66, 0x61, 0x78, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x03, 0x63, 0x6d,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x43, 0x6d, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x03, 0x63, 0x6d, 0x74, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x05, 0x52, 0x0c, 0x74, 0x72, 0x75, 0x63,
	0x6b, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x22, 0x4e, 0x0a, 0x2a, 0x53, 0x6c, 0x69, 0x64,
	0x69, 0x6e, 0x67, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x74, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x22, 0xf2, 0x01, 0x0a, 0x13, 0x54, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73,
	0x12, 0x82, 0x01, 0x0a, 0x22, 0x73, 0x6c, 0x69, 0x64, 0x69, 0x6e, 0x67, 0x57, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61,
	0x74, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x6c, 0x69, 0x64, 0x69, 0x6e, 0x67, 0x57, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x75,
	0x72, 0x61, 0x74, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x52, 0x22, 0x73, 0x6c, 0x69, 0x64, 0x69, 0x6e, 0x67, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x74, 0x65,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x50, 0x0a, 0x23, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79,
	0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x23, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x4a, 0x04, 0x08, 0x01, 0x10, 0x02, 0x22, 0xd8, 0x03,
	0x0a, 0x0a, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x12, 0x47, 0x0a, 0x13,
	0x44, 0x61, 0x74, 0x61, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x42, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x52, 0x13, 0x44, 0x61, 0x74, 0x61, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x42, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x12, 0x4a, 0x0a, 0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x1e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x52,
	0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x3f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61,
	0x6c, 0x73, 0x12, 0x39, 0x0a, 0x09, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x73, 0x52, 0x09, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x4f, 0x0a,
	0x0c, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x49,
	0x0a, 0x11, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x44, 0x65, 0x63, 0x6f, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x44, 0x65, 0x63,
	0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x11, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x44,
	0x65, 0x63, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x52,
	0x17, 0x53, 0x70, 0x65, 0x65, 0x64, 0x47, 0x61, 0x75, 0x67, 0x65, 0x53, 0x33, 0x43, 0x72, 0x65,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x22, 0xbd, 0x04, 0x0a, 0x0d, 0x50, 0x61, 0x67,
	0x65, 0x72, 0x44, 0x75, 0x74, 0x79, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x69, 0x6e,
	0x66, 0x72, 0x61, 0x50, 0x30, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x69, 0x6e, 0x66, 0x72, 0x61, 0x50, 0x30, 0x52, 0x6f,
	0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x12, 0x38, 0x0a, 0x17, 0x64, 0x61, 0x74, 0x61,
	0x73, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x30, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67,
	0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x63, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x30, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b,
	0x65, 0x79, 0x12, 0x2e, 0x0a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x30, 0x52, 0x6f,
	0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x30, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b,
	0x65, 0x79, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x50, 0x30, 0x52, 0x6f,
	0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x50, 0x30, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b,
	0x65, 0x79, 0x12, 0x3e, 0x0a, 0x1a, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x45,
	0x6e, 0x67, 0x54, 0x65, 0x73, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x45, 0x6e, 0x67, 0x54, 0x65, 0x73, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b,
	0x65, 0x79, 0x12, 0x30, 0x0a, 0x13, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x50, 0x30, 0x52,
	0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x50, 0x30, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e,
	0x67, 0x4b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x12, 0x6e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74,
	0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x6e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e,
	0x67, 0x4b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x16, 0x6e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74,
	0x54, 0x65, 0x73, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x6e, 0x6f, 0x6e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x54, 0x65,
	0x73, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x12, 0x52, 0x0a, 0x24,
	0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e,
	0x67, 0x4b, 0x65, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x24, 0x74, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79,
	0x12, 0x36, 0x0a, 0x16, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x16, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x6f,
	0x75, 0x74, 0x69, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x22, 0x51, 0x0a, 0x0f, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0x2b, 0x0a, 0x0b, 0x43,
	0x6c, 0x65, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x22, 0x45, 0x0a, 0x0d, 0x50, 0x69, 0x62, 0x69,
	0x74, 0x41, 0x69, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x72, 0x6c,
	0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x72,
	0x6c, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x22,
	0x9e, 0x02, 0x0a, 0x0c, 0x56, 0x65, 0x72, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x73, 0x74,
	0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65,
	0x73, 0x74, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x72, 0x67, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x65, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x74, 0x65, 0x73, 0x74, 0x4f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x68, 0x69, 0x70, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68,
	0x69, 0x70, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x73, 0x74, 0x53, 0x68, 0x69, 0x70,
	0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x73, 0x74, 0x53, 0x68,
	0x69, 0x70, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x65, 0x73, 0x74, 0x55, 0x72,
	0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x65, 0x73, 0x74, 0x55, 0x72, 0x6c,
	0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c,
	0x74, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x6c,
	0x22, 0x8a, 0x01, 0x0a, 0x08, 0x57, 0x6f, 0x72, 0x6b, 0x72, 0x61, 0x6d, 0x70, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69,
	0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65,
	0x79, 0x12, 0x1c, 0x0a, 0x09, 0x6a, 0x77, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x77, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x61, 0x63, 0x61, 0x64, 0x65, 0x6d, 0x79, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x61, 0x64, 0x65, 0x6d, 0x79, 0x49, 0x44, 0x22, 0x53, 0x0a,
	0x07, 0x48, 0x75, 0x62, 0x73, 0x70, 0x6f, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x6f, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6a, 0x77, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x77, 0x74, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x22, 0x3f, 0x0a, 0x05, 0x4b, 0x6e, 0x6f, 0x63, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69,
	0x4b, 0x65, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x4b, 0x65,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x4b, 0x65, 0x79, 0x22, 0xce, 0x02, 0x0a, 0x10, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x66, 0x6f, 0x72,
	0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x65, 0x73, 0x74,
	0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x65, 0x73, 0x74, 0x55,
	0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x65, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x73, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x65, 0x73, 0x74, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x65,
	0x73, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x65,
	0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x74, 0x65, 0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x10, 0x74, 0x65, 0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x65, 0x73, 0x74, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x22, 0x5e, 0x0a, 0x0a, 0x4a, 0x69, 0x72, 0x61, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x70, 0x69, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x22, 0x60, 0x0a, 0x08, 0x46, 0x6c, 0x61, 0x74, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70,
	0x69, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b,
	0x65, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0x44, 0x0a, 0x06, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x72,
	0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x55, 0x0a, 0x07,
	0x50, 0x6f, 0x73, 0x74, 0x48, 0x6f, 0x67, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x61, 0x6c, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12,
	0x22, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x70, 0x69,
	0x4b, 0x65, 0x79, 0x22, 0x37, 0x0a, 0x09, 0x4f, 0x70, 0x65, 0x6e, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x6f, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x6b, 0x65, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x6b, 0x65, 0x79, 0x22, 0x7c, 0x0a, 0x06,
	0x41, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x41, 0x70, 0x69,
	0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x61, 0x6e, 0x64, 0x62,
	0x6f, 0x78, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x61, 0x6e, 0x64,
	0x62, 0x6f, 0x78, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x48, 0x6f, 0x73, 0x74, 0x22, 0x85, 0x02, 0x0a, 0x09, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x65, 0x65, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x61, 0x6e, 0x64,
	0x62, 0x6f, 0x78, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x20,
	0x0a, 0x0b, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x48, 0x6f, 0x73, 0x74,
	0x12, 0x2a, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x0e,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x48, 0x6f, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79,
	0x12, 0x30, 0x0a, 0x13, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b,
	0x65, 0x79, 0x22, 0xf0, 0x0a, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x6f,
	0x6f, 0x6c, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x50,
	0x49, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x41, 0x50, 0x49, 0x4b, 0x65, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x75, 0x6e, 0x69,
	0x70, 0x64, 0x66, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x75, 0x6e, 0x69, 0x70, 0x64, 0x66, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x2e, 0x0a,
	0x12, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x64, 0x61, 0x72, 0x6b, 0x6c, 0x79, 0x41, 0x70, 0x69,
	0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x61, 0x75, 0x6e, 0x63,
	0x68, 0x64, 0x61, 0x72, 0x6b, 0x6c, 0x79, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x33, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x72, 0x64, 0x75, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x72, 0x44,
	0x75, 0x74, 0x79, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x72, 0x64, 0x75,
	0x74, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x6e, 0x64, 0x67, 0x72, 0x69, 0x64, 0x41, 0x70,
	0x69, 0x4b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x6e, 0x64,
	0x67, 0x72, 0x69, 0x64, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x41, 0x0a, 0x0f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3b, 0x0a,
	0x0d, 0x70, 0x69, 0x62, 0x69, 0x74, 0x41, 0x69, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50, 0x69,
	0x62, 0x69, 0x74, 0x41, 0x69, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x70, 0x69, 0x62,
	0x69, 0x74, 0x41, 0x69, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x6e, 0x61,
	0x72, 0x73, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6e, 0x61, 0x72, 0x73, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x6e, 0x61,
	0x72, 0x73, 0x54, 0x65, 0x73, 0x74, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x6e, 0x61, 0x72, 0x73, 0x54, 0x65, 0x73, 0x74, 0x41, 0x70, 0x69, 0x4b,
	0x65, 0x79, 0x12, 0x4d, 0x0a, 0x13, 0x44, 0x42, 0x54, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x72,
	0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x42, 0x54, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x13, 0x44, 0x42,
	0x54, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x73, 0x12, 0x44, 0x0a, 0x10, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x78,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x38, 0x0a, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x73,
	0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x2c, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x72, 0x61, 0x6d, 0x70, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x57, 0x6f, 0x72,
	0x6b, 0x72, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x72, 0x61, 0x6d, 0x70, 0x12,
	0x44, 0x0a, 0x10, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x10, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x29, 0x0a, 0x07, 0x68, 0x75, 0x62, 0x73, 0x70, 0x6f, 0x74,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x48, 0x75, 0x62, 0x73, 0x70, 0x6f, 0x74, 0x52, 0x07, 0x68, 0x75, 0x62, 0x73, 0x70, 0x6f, 0x74,
	0x12, 0x23, 0x0a, 0x05, 0x6b, 0x6e, 0x6f, 0x63, 0x6b, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4b, 0x6e, 0x6f, 0x63, 0x6b, 0x52, 0x05,
	0x6b, 0x6e, 0x6f, 0x63, 0x6b, 0x12, 0x2c, 0x0a, 0x08, 0x66, 0x6c, 0x61, 0x74, 0x66, 0x69, 0x6c,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x46, 0x6c, 0x61, 0x74, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x08, 0x66, 0x6c, 0x61, 0x74, 0x66,
	0x69, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x72, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x6d, 0x70,
	0x6c, 0x65, 0x72, 0x52, 0x06, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x07, 0x70,
	0x6f, 0x73, 0x74, 0x48, 0x6f, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x48, 0x6f, 0x67, 0x52, 0x07, 0x70,
	0x6f, 0x73, 0x74, 0x48, 0x6f, 0x67, 0x12, 0x26, 0x0a, 0x04, 0x6a, 0x69, 0x72, 0x61, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x69,
	0x72, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x04, 0x6a, 0x69, 0x72, 0x61, 0x12, 0x22,
	0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x69, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x69, 0x41, 0x70, 0x69, 0x4b,
	0x65, 0x79, 0x12, 0x2f, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4f,
	0x70, 0x65, 0x6e, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x06, 0x61, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x41, 0x73, 0x63,
	0x65, 0x6e, 0x64, 0x52, 0x06, 0x61, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x12, 0x26, 0x0a, 0x06, 0x74,
	0x77, 0x69, 0x6c, 0x69, 0x6f, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x52, 0x06, 0x74, 0x77, 0x69,
	0x6c, 0x69, 0x6f, 0x12, 0x2f, 0x0a, 0x09, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x65, 0x65, 0x74,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x65, 0x65, 0x74, 0x52, 0x09, 0x73, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x65, 0x65, 0x74, 0x12, 0x35, 0x0a, 0x0b, 0x63, 0x6c, 0x65, 0x72, 0x6b, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x43, 0x6c, 0x65, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b,
	0x63, 0x6c, 0x65, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3b, 0x0a, 0x0d, 0x64,
	0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x53, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x0d, 0x64, 0x73, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3b, 0x0a, 0x0d, 0x67, 0x6f, 0x72, 0x75,
	0x6c, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47, 0x6f, 0x72, 0x75, 0x6c, 0x65, 0x73,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x0d, 0x67, 0x6f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0x68, 0x0a, 0x10, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x50, 0x72,
	0x6f, 0x78, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x22,
	0xc9, 0x1f, 0x0a, 0x15, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x10, 0x75, 0x73, 0x65, 0x45, 0x6d,
	0x62, 0x65, 0x64, 0x64, 0x65, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x75, 0x73, 0x65, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64,
	0x65, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d,
	0x61, 0x78, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x0d, 0x75,
	0x73, 0x65, 0x45, 0x43, 0x53, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x45, 0x43, 0x53, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x44, 0x0a, 0x1d, 0x75,
	0x73, 0x65, 0x45, 0x43, 0x53, 0x4f, 0x70, 0x73, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x46, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x1d, 0x75, 0x73, 0x65, 0x45, 0x43, 0x53, 0x4f, 0x70, 0x73, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x46, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f,
	0x72, 0x12, 0x4b, 0x0a, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x51,
	0x0a, 0x0d, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a,
	0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0d, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x30, 0x0a, 0x13, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x47, 0x72, 0x70,
	0x63, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x47, 0x72, 0x70, 0x63, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x73, 0x12, 0x54, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x36, 0x0a, 0x16, 0x65, 0x6d, 0x69,
	0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x74, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x65, 0x6d, 0x69, 0x74, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72,
	0x79, 0x12, 0x6c, 0x0a, 0x16, 0x73, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x16, 0x73, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f,
	0x62, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a,
	0xd6, 0x02, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x42, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x69, 0x0a, 0x11, 0x70, 0x67, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x53, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50, 0x67, 0x54, 0x61, 0x62, 0x6c,
	0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x70, 0x65, 0x63, 0x52, 0x11, 0x70, 0x67, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x70, 0x65, 0x63, 0x1a, 0x0e,
	0x0a, 0x0c, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x65, 0x64, 0x53, 0x70, 0x65, 0x63, 0x1a, 0x5f,
	0x0a, 0x11, 0x50, 0x67, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53,
	0x70, 0x65, 0x63, 0x12, 0x30, 0x0a, 0x13, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x57, 0x69, 0x74, 0x68, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x57, 0x69, 0x74, 0x68, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x22,
	0x27, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x6d, 0x62, 0x65, 0x64,
	0x64, 0x65, 0x64, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x67, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x42, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x10, 0x01, 0x1a, 0xa0, 0x02, 0x0a, 0x0d, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x44, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x62, 0x0a, 0x0e, 0x65, 0x63, 0x73, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x70,
	0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45, 0x63, 0x73, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x53, 0x70, 0x65, 0x63, 0x52, 0x0e, 0x65, 0x63, 0x73, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x53, 0x70, 0x65, 0x63, 0x1a, 0x2a, 0x0a, 0x0e, 0x45, 0x63, 0x73, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x22, 0x39, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x6d, 0x62, 0x65,
	0x64, 0x64, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x63, 0x73, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x62, 0x42, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x10, 0x02, 0x1a, 0xd5, 0x08, 0x0a, 0x0e,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x59,
	0x0a, 0x0b, 0x6a, 0x6f, 0x62, 0x73, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62,
	0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x0b, 0x6a, 0x6f,
	0x62, 0x73, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x64, 0x0a, 0x11, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x11, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12,
	0x67, 0x0a, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x52, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x1a, 0xd3, 0x01, 0x0a, 0x0a, 0x54, 0x61, 0x73,
	0x6b, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x50, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a,
	0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a, 0x73, 0x61, 0x6d, 0x70,
	0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x48, 0x01, 0x52, 0x0a,
	0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x22, 0x14, 0x0a,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x10, 0x00, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x1a, 0xe1,
	0x01, 0x0a, 0x09, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x4f, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00,
	0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a,
	0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02,
	0x48, 0x01, 0x52, 0x0a, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x22, 0x24, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x65, 0x6c, 0x61, 0x79, 0x10, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x61,
	0x74, 0x65, 0x1a, 0x78, 0x0a, 0x0b, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x51, 0x0a, 0x07, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x1a, 0xe4, 0x01, 0x0a,
	0x0a, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x6a,
	0x6f, 0x62, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x6a, 0x6f, 0x62, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79,
	0x4b, 0x65, 0x79, 0x12, 0x50, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x07, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x5c, 0x0a, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x1a, 0x85, 0x0c, 0x0a, 0x16, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x67,
	0x0a, 0x0d, 0x73, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a,
	0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63,
	0x6b, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0d, 0x73, 0x74, 0x75, 0x63, 0x6b, 0x4a,
	0x6f, 0x62, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x6c, 0x0a, 0x11, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x11, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x74, 0x75, 0x63, 0x6b,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x6d, 0x0a, 0x0f, 0x73, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f,
	0x62, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74,
	0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x73, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x70, 0x0a, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53,
	0x74, 0x75, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x40, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x74, 0x75, 0x63, 0x6b,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x90, 0x01, 0x0a, 0x09, 0x53, 0x74, 0x75, 0x63, 0x6b,
	0x52, 0x75, 0x6c, 0x65, 0x12, 0x53, 0x0a, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x74, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x49, 0x6e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x1a, 0x96, 0x01, 0x0a, 0x0c, 0x53, 0x74,
	0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x6a, 0x6f,
	0x62, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x6a, 0x6f, 0x62, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x12, 0x5e, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x63, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75,
	0x63, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0a, 0x73, 0x74, 0x75, 0x63, 0x6b, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x1a, 0xce, 0x02, 0x0a, 0x0b, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x59, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x52, 0x08, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x74, 0x0a,
	0x11, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53,
	0x6c, 0x61, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x52, 0x11, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x6e, 0x0a, 0x0f, 0x6c, 0x6f, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63,
	0x6b, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x4c, 0x6f, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x52, 0x0f, 0x6c, 0x6f, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x0a, 0x11, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x1a, 0x31, 0x0a, 0x0f, 0x4c, 0x6f, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x67, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x6f, 0x67, 0x67, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x9c, 0x01, 0x0a, 0x0e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a,
	0x6f, 0x62, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x6a, 0x6f, 0x62, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6a, 0x6f, 0x62, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x4b, 0x65, 0x79,
	0x12, 0x62, 0x0a, 0x0b, 0x73, 0x74, 0x75, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a,
	0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x63,
	0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x73, 0x74, 0x75, 0x63, 0x6b, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x74, 0x0a, 0x06, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x0d,
	0x0a, 0x09, 0x4e, 0x6f, 0x74, 0x50, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x41, 0x66, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x74, 0x75, 0x63, 0x6b, 0x41, 0x66, 0x74, 0x65, 0x72,
	0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x4f, 0x6d, 0x69, 0x74,
	0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x69, 0x6d, 0x65, 0x45, 0x6c, 0x61, 0x70, 0x73, 0x65,
	0x64, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x10, 0x04, 0x22, 0x3f, 0x0a, 0x08, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x6f, 0x67, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x61, 0x67, 0x65, 0x72, 0x64,
	0x75, 0x74, 0x79, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x02, 0x22, 0x40, 0x0a, 0x16, 0x4a,
	0x6f, 0x62, 0x62, 0x65, 0x72, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x74, 0x6f, 0x6e, 0x73, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x63, 0x73, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x41, 0x64, 0x64, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65,
	0x63, 0x73, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x41, 0x64, 0x64, 0x72, 0x22, 0x99, 0x03,
	0x0a, 0x10, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f,
	0x72, 0x73, 0x12, 0x39, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f,
	0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x08, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x12, 0x3b, 0x0a,
	0x09, 0x64, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x72, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x09, 0x64, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x72, 0x61, 0x12, 0x37, 0x0a, 0x07, 0x71, 0x75,
	0x6f, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x07, 0x71, 0x75, 0x6f, 0x74,
	0x69, 0x6e, 0x67, 0x12, 0x33, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x12,
	0x33, 0x0a, 0x05, 0x74, 0x65, 0x73, 0x74, 0x31, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x74,
	0x65, 0x73, 0x74, 0x31, 0x12, 0x33, 0x0a, 0x05, 0x74, 0x65, 0x73, 0x74, 0x32, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62,
	0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x05, 0x74, 0x65, 0x73, 0x74, 0x32, 0x22, 0x41, 0x0a, 0x07, 0x53, 0x65, 0x6e,
	0x74, 0x75, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0xc4, 0x01, 0x0a,
	0x06, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x74, 0x75,
	0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x53, 0x65, 0x6e, 0x74, 0x75, 0x72, 0x65, 0x52, 0x07, 0x73, 0x65, 0x6e, 0x74, 0x75,
	0x72, 0x65, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x4a, 0x04,
	0x08, 0x04, 0x10, 0x05, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x4a, 0x04, 0x08, 0x06, 0x10, 0x07,
	0x4a, 0x04, 0x08, 0x07, 0x10, 0x08, 0x52, 0x17, 0x75, 0x73, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x61, 0x64, 0x73, 0x52,
	0x0c, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x0f, 0x6e,
	0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x07,
	0x63, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x0f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x53,
	0x68, 0x65, 0x65, 0x74, 0x73, 0x4b, 0x65, 0x79, 0x52, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x53, 0x68, 0x65, 0x65, 0x74, 0x73, 0x53, 0x70, 0x72, 0x65, 0x61, 0x64, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x49, 0x64, 0x22, 0x43, 0x0a, 0x05, 0x46, 0x6d, 0x63, 0x73, 0x61, 0x12, 0x3a, 0x0a, 0x18,
	0x64, 0x62, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18,
	0x64, 0x62, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4a, 0x6f, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x0f, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x0c,
	0x44, 0x42, 0x54, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x44, 0x42, 0x54, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x12, 0x2c, 0x0a, 0x11, 0x73, 0x6e, 0x6f, 0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x53, 0x33, 0x42,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x6e, 0x6f,
	0x77, 0x66, 0x6c, 0x61, 0x6b, 0x65, 0x53, 0x33, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x30,
	0x0a, 0x13, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x53, 0x33, 0x42,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x53, 0x33, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x22, 0x19, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x43,
	0x49, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x02, 0x43, 0x49, 0x22, 0xb5, 0x06, 0x0a, 0x06,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x03, 0x45, 0x6e, 0x76, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45, 0x6e, 0x76,
	0x52, 0x03, 0x45, 0x6e, 0x76, 0x12, 0x2f, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x73, 0x52, 0x09, 0x44, 0x61, 0x74,
	0x61, 0x62, 0x61, 0x73, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x08, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65,
	0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x73, 0x52, 0x08, 0x53, 0x63, 0x72, 0x61,
	0x70, 0x65, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x06, 0x56, 0x65, 0x72, 0x69, 0x73, 0x6b, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x65,
	0x72, 0x69, 0x73, 0x6b, 0x52, 0x06, 0x56, 0x65, 0x72, 0x69, 0x73, 0x6b, 0x12, 0x1d, 0x0a, 0x03,
	0x41, 0x57, 0x53, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x41, 0x57, 0x53, 0x52, 0x03, 0x41, 0x57, 0x53, 0x12, 0x32, 0x0a, 0x0a, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x52, 0x0a, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x12,
	0x38, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x52, 0x0c, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x08, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x08, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x06, 0x53, 0x61, 0x66, 0x65, 0x74,
	0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x52, 0x06, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x12,
	0x23, 0x0a, 0x05, 0x69, 0x6e, 0x66, 0x72, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x6e, 0x66, 0x72, 0x61, 0x52, 0x05, 0x69,
	0x6e, 0x66, 0x72, 0x61, 0x12, 0x23, 0x0a, 0x05, 0x46, 0x6d, 0x63, 0x73, 0x61, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x46, 0x6d, 0x63,
	0x73, 0x61, 0x52, 0x05, 0x46, 0x6d, 0x63, 0x73, 0x61, 0x12, 0x44, 0x0a, 0x10, 0x4a, 0x6f, 0x62,
	0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x73, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62,
	0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x73, 0x52, 0x10, 0x4a,
	0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x73, 0x12,
	0x41, 0x0a, 0x0f, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0f, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x56, 0x0a, 0x16, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x53, 0x69, 0x6e, 0x67,
	0x6c, 0x65, 0x74, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4a, 0x6f, 0x62, 0x62,
	0x65, 0x72, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x74, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x16, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65,
	0x74, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x29, 0x0a, 0x07, 0x52, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x07, 0x52, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x0b, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61,
	0x55, 0x72, 0x6c, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x55, 0x72, 0x6c, 0x73, 0x52,
	0x0b, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x55, 0x72, 0x6c, 0x73, 0x4a, 0x04, 0x08, 0x09,
	0x10, 0x0a, 0x52, 0x0f, 0x4a, 0x6f, 0x62, 0x62, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x6f, 0x72, 0x22, 0x60, 0x0a, 0x06, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x12, 0x1e, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x61, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x62,
	0x61, 0x73, 0x65, 0x55, 0x52, 0x4c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61,
	0x73, 0x65, 0x55, 0x52, 0x4c, 0x22, 0x2f, 0x0a, 0x0b, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61,
	0x55, 0x72, 0x6c, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x22, 0x37, 0x0a, 0x0d, 0x44, 0x53, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x22,
	0x39, 0x0a, 0x0d, 0x47, 0x6f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x2a, 0x47, 0x0a, 0x09, 0x44, 0x42,
	0x53, 0x53, 0x4c, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53, 0x41, 0x42,
	0x4c, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45,
	0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x43, 0x41,
	0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x46, 0x55, 0x4c,
	0x4c, 0x10, 0x03, 0x2a, 0x2f, 0x0a, 0x03, 0x45, 0x6e, 0x76, 0x12, 0x07, 0x0a, 0x03, 0x44, 0x45,
	0x56, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x53, 0x54, 0x10, 0x01, 0x12, 0x08, 0x0a,
	0x04, 0x50, 0x52, 0x4f, 0x44, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x54, 0x41, 0x47, 0x49,
	0x4e, 0x47, 0x10, 0x03, 0x2a, 0x25, 0x0a, 0x0d, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x6e, 0x4d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x53, 0x33, 0x10, 0x01, 0x2a, 0x85, 0x01, 0x0a, 0x16,
	0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x50, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x65, 0x78, 0x54, 0x72, 0x61,
	0x71, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x61, 0x6d, 0x73, 0x61, 0x72, 0x61, 0x10, 0x01,
	0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x70, 0x65, 0x65, 0x64, 0x47, 0x61, 0x75, 0x67, 0x65, 0x10, 0x02,
	0x12, 0x0f, 0x0a, 0x0b, 0x4b, 0x65, 0x65, 0x70, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x69, 0x6e, 0x10,
	0x03, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x10, 0x05, 0x12,
	0x0e, 0x0a, 0x0a, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x44, 0x72, 0x69, 0x76, 0x65, 0x10, 0x06, 0x22,
	0x04, 0x08, 0x04, 0x10, 0x04, 0x2a, 0x0c, 0x54, 0x72, 0x75, 0x63, 0x6b, 0x65, 0x72, 0x43, 0x6c,
	0x6f, 0x75, 0x64, 0x2a, 0x41, 0x0a, 0x13, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x44, 0x65, 0x63, 0x6f, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x0c, 0x0a, 0x08, 0x48, 0x65,
	0x72, 0x65, 0x4d, 0x61, 0x70, 0x73, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x47, 0x65, 0x6f, 0x67,
	0x72, 0x61, 0x70, 0x68, 0x79, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x70, 0x65, 0x6e, 0x4d,
	0x65, 0x74, 0x65, 0x6f, 0x10, 0x02, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_config_config_proto_rawDescOnce sync.Once
	file_config_config_proto_rawDescData = file_config_config_proto_rawDesc
)

func file_config_config_proto_rawDescGZIP() []byte {
	file_config_config_proto_rawDescOnce.Do(func() {
		file_config_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_config_config_proto_rawDescData)
	})
	return file_config_config_proto_rawDescData
}

var file_config_config_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_config_config_proto_msgTypes = make([]protoimpl.MessageInfo, 91)
var file_config_config_proto_goTypes = []interface{}{
	(DBSSLMode)(0),              // 0: config.DBSSLMode
	(Env)(0),                    // 1: config.Env
	(ObjectBackend)(0),          // 2: config.ObjectBackend
	(TelematicsDataProvider)(0), // 3: config.TelematicsDataProvider
	(TelematicsDecorator)(0),    // 4: config.TelematicsDecorator
	(JobberProcessorConfig_StoreConfig_Type)(0),                // 5: config.JobberProcessorConfig.StoreConfig.Type
	(JobberProcessorConfig_MonitorConfig_Type)(0),              // 6: config.JobberProcessorConfig.MonitorConfig.Type
	(JobberProcessorConfig_RegistryConfig_TaskMetric_Name)(0),  // 7: config.JobberProcessorConfig.RegistryConfig.TaskMetric.Name
	(JobberProcessorConfig_RegistryConfig_JobMetric_Name)(0),   // 8: config.JobberProcessorConfig.RegistryConfig.JobMetric.Name
	(JobberProcessorConfig_StuckJobDetectorConfig_RuleId)(0),   // 9: config.JobberProcessorConfig.StuckJobDetectorConfig.RuleId
	(JobberProcessorConfig_StuckJobDetectorConfig_ActionId)(0), // 10: config.JobberProcessorConfig.StuckJobDetectorConfig.ActionId
	(*DBSSLConfig)(nil),                                // 11: config.DBSSLConfig
	(*DBConfig)(nil),                                   // 12: config.DBConfig
	(*SnowflakeConfig)(nil),                            // 13: config.SnowflakeConfig
	(*SnowflakeUser)(nil),                              // 14: config.SnowflakeUser
	(*SnowflakeKeyPairUser)(nil),                       // 15: config.SnowflakeKeyPairUser
	(*Databases)(nil),                                  // 16: config.Databases
	(*Scrapers)(nil),                                   // 17: config.Scrapers
	(*MVRService)(nil),                                 // 18: config.MVRService
	(*OAuthServices)(nil),                              // 19: config.OAuthServices
	(*Services)(nil),                                   // 20: config.Services
	(*PDFGen)(nil),                                     // 21: config.PDFGen
	(*Tracing)(nil),                                    // 22: config.Tracing
	(*Logging)(nil),                                    // 23: config.Logging
	(*Metrics)(nil),                                    // 24: config.Metrics
	(*LambdaGrpc)(nil),                                 // 25: config.LambdaGrpc
	(*Infra)(nil),                                      // 26: config.Infra
	(*AWS)(nil),                                        // 27: config.AWS
	(*Verisk)(nil),                                     // 28: config.Verisk
	(*S3Credentials)(nil),                              // 29: config.S3Credentials
	(*TelematicsOAuthAppCredentials)(nil),              // 30: config.TelematicsOAuthAppCredentials
	(*DBTCloudCredentials)(nil),                        // 31: config.DBTCloudCredentials
	(*TerminalCredentials)(nil),                        // 32: config.TerminalCredentials
	(*HereMapsCredentials)(nil),                        // 33: config.HereMapsCredentials
	(*OpenMeteoCredentials)(nil),                       // 34: config.OpenMeteoCredentials
	(*CmtCredentials)(nil),                             // 35: config.CmtCredentials
	(*TelematicsCredentials)(nil),                      // 36: config.TelematicsCredentials
	(*KeepTruckinIntegration)(nil),                     // 37: config.KeepTruckinIntegration
	(*RateLimit)(nil),                                  // 38: config.RateLimit
	(*SamsaraIntegration)(nil),                         // 39: config.SamsaraIntegration
	(*URL)(nil),                                        // 40: config.URL
	(*MillimanIntegration)(nil),                        // 41: config.MillimanIntegration
	(*TerminalIntegration)(nil),                        // 42: config.TerminalIntegration
	(*OpenMeteoIntegration)(nil),                       // 43: config.OpenMeteoIntegration
	(*CmtIntegration)(nil),                             // 44: config.CmtIntegration
	(*TelematicsIntegrationConfigurations)(nil),        // 45: config.TelematicsIntegrationConfigurations
	(*SlidingWindowMillimanAccurateScorePipeline)(nil), // 46: config.SlidingWindowMillimanAccurateScorePipeline
	(*TelematicsPipelines)(nil),                        // 47: config.TelematicsPipelines
	(*Telematics)(nil),                                 // 48: config.Telematics
	(*PagerDutyKeys)(nil),                              // 49: config.PagerDutyKeys
	(*GoogleAppConfig)(nil),                            // 50: config.GoogleAppConfig
	(*ClerkConfig)(nil),                                // 51: config.ClerkConfig
	(*PibitAiConfig)(nil),                              // 52: config.PibitAiConfig
	(*VeriskConfig)(nil),                               // 53: config.VeriskConfig
	(*Workramp)(nil),                                   // 54: config.Workramp
	(*Hubspot)(nil),                                    // 55: config.Hubspot
	(*Knock)(nil),                                      // 56: config.Knock
	(*SalesforceConfig)(nil),                           // 57: config.SalesforceConfig
	(*JiraConfig)(nil),                                 // 58: config.JiraConfig
	(*Flatfile)(nil),                                   // 59: config.Flatfile
	(*Impler)(nil),                                     // 60: config.Impler
	(*PostHog)(nil),                                    // 61: config.PostHog
	(*Openmeter)(nil),                                  // 62: config.Openmeter
	(*Ascend)(nil),                                     // 63: config.Ascend
	(*Snapsheet)(nil),                                  // 64: config.Snapsheet
	(*ProductTools)(nil),                               // 65: config.ProductTools
	(*SmartProxyConfig)(nil),                           // 66: config.SmartProxyConfig
	(*JobberProcessorConfig)(nil),                      // 67: config.JobberProcessorConfig
	(*JobberSingletonsConfig)(nil),                     // 68: config.JobberSingletonsConfig
	(*JobberProcessors)(nil),                           // 69: config.JobberProcessors
	(*Senture)(nil),                                    // 70: config.Senture
	(*Safety)(nil),                                     // 71: config.Safety
	(*Fmcsa)(nil),                                      // 72: config.Fmcsa
	(*RefresherConfig)(nil),                            // 73: config.RefresherConfig
	(*Runtime)(nil),                                    // 74: config.Runtime
	(*Config)(nil),                                     // 75: config.Config
	(*Twilio)(nil),                                     // 76: config.Twilio
	(*NirvanaUrls)(nil),                                // 77: config.NirvanaUrls
	(*DSModelServer)(nil),                              // 78: config.DSModelServer
	(*GorulesServer)(nil),                              // 79: config.GorulesServer
	(*Databases_SnowflakeSettings)(nil),                // 80: config.Databases.SnowflakeSettings
	(*Databases_SnowflakeSettings_Users)(nil),          // 81: config.Databases.SnowflakeSettings.Users
	(*Databases_SnowflakeSettings_Connections)(nil),    // 82: config.Databases.SnowflakeSettings.Connections
	nil, // 83: config.SamsaraIntegration.EndpointsEntry
	nil, // 84: config.TerminalIntegration.ConnectionEndpointRateLimitEntry
	(*JobberProcessorConfig_StoreConfig)(nil),                              // 85: config.JobberProcessorConfig.StoreConfig
	(*JobberProcessorConfig_MonitorConfig)(nil),                            // 86: config.JobberProcessorConfig.MonitorConfig
	(*JobberProcessorConfig_RegistryConfig)(nil),                           // 87: config.JobberProcessorConfig.RegistryConfig
	(*JobberProcessorConfig_StuckJobDetectorConfig)(nil),                   // 88: config.JobberProcessorConfig.StuckJobDetectorConfig
	(*JobberProcessorConfig_StoreConfig_EmbeddedSpec)(nil),                 // 89: config.JobberProcessorConfig.StoreConfig.EmbeddedSpec
	(*JobberProcessorConfig_StoreConfig_PgTableBackedSpec)(nil),            // 90: config.JobberProcessorConfig.StoreConfig.PgTableBackedSpec
	(*JobberProcessorConfig_MonitorConfig_EcsMonitorSpec)(nil),             // 91: config.JobberProcessorConfig.MonitorConfig.EcsMonitorSpec
	(*JobberProcessorConfig_RegistryConfig_TaskMetric)(nil),                // 92: config.JobberProcessorConfig.RegistryConfig.TaskMetric
	(*JobberProcessorConfig_RegistryConfig_JobMetric)(nil),                 // 93: config.JobberProcessorConfig.RegistryConfig.JobMetric
	(*JobberProcessorConfig_RegistryConfig_TaskMetrics)(nil),               // 94: config.JobberProcessorConfig.RegistryConfig.TaskMetrics
	(*JobberProcessorConfig_RegistryConfig_JobMetrics)(nil),                // 95: config.JobberProcessorConfig.RegistryConfig.JobMetrics
	(*JobberProcessorConfig_StuckJobDetectorConfig_StuckRule)(nil),         // 96: config.JobberProcessorConfig.StuckJobDetectorConfig.StuckRule
	(*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule)(nil),      // 97: config.JobberProcessorConfig.StuckJobDetectorConfig.StuckJobRule
	(*JobberProcessorConfig_StuckJobDetectorConfig_StuckAction)(nil),       // 98: config.JobberProcessorConfig.StuckJobDetectorConfig.StuckAction
	(*JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams)(nil), // 99: config.JobberProcessorConfig.StuckJobDetectorConfig.SlackActionParams
	(*JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams)(nil),   // 100: config.JobberProcessorConfig.StuckJobDetectorConfig.LogActionParams
	(*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction)(nil),    // 101: config.JobberProcessorConfig.StuckJobDetectorConfig.StuckJobAction
}
var file_config_config_proto_depIdxs = []int32{
	0,   // 0: config.DBSSLConfig.sslMode:type_name -> config.DBSSLMode
	11,  // 1: config.DBConfig.sslConfig:type_name -> config.DBSSLConfig
	12,  // 2: config.Databases.nirvana:type_name -> config.DBConfig
	12,  // 3: config.Databases.nhtsa:type_name -> config.DBConfig
	12,  // 4: config.Databases.fmcsa:type_name -> config.DBConfig
	80,  // 5: config.Databases.snowflake:type_name -> config.Databases.SnowflakeSettings
	12,  // 6: config.Databases.neo4j:type_name -> config.DBConfig
	12,  // 7: config.Databases.redis:type_name -> config.DBConfig
	12,  // 8: config.Databases.fmcsaReadOnly:type_name -> config.DBConfig
	12,  // 9: config.Databases.fmcsaWrite:type_name -> config.DBConfig
	12,  // 10: config.Databases.ds:type_name -> config.DBConfig
	18,  // 11: config.Services.mvrService:type_name -> config.MVRService
	19,  // 12: config.Services.oauth:type_name -> config.OAuthServices
	21,  // 13: config.Services.pdfGen:type_name -> config.PDFGen
	22,  // 14: config.Infra.tracing:type_name -> config.Tracing
	23,  // 15: config.Infra.logging:type_name -> config.Logging
	25,  // 16: config.Infra.lambdaGrpc:type_name -> config.LambdaGrpc
	24,  // 17: config.Infra.metrics:type_name -> config.Metrics
	30,  // 18: config.TelematicsCredentials.samsara:type_name -> config.TelematicsOAuthAppCredentials
	29,  // 19: config.TelematicsCredentials.speedGauge:type_name -> config.S3Credentials
	30,  // 20: config.TelematicsCredentials.keepTruckin:type_name -> config.TelematicsOAuthAppCredentials
	30,  // 21: config.TelematicsCredentials.samsaraSafety:type_name -> config.TelematicsOAuthAppCredentials
	30,  // 22: config.TelematicsCredentials.keepTruckinSafety:type_name -> config.TelematicsOAuthAppCredentials
	32,  // 23: config.TelematicsCredentials.terminal:type_name -> config.TerminalCredentials
	33,  // 24: config.TelematicsCredentials.heremaps:type_name -> config.HereMapsCredentials
	34,  // 25: config.TelematicsCredentials.openmeteo:type_name -> config.OpenMeteoCredentials
	35,  // 26: config.TelematicsCredentials.cmt:type_name -> config.CmtCredentials
	38,  // 27: config.SamsaraIntegration.global:type_name -> config.RateLimit
	83,  // 28: config.SamsaraIntegration.endpoints:type_name -> config.SamsaraIntegration.EndpointsEntry
	40,  // 29: config.MillimanIntegration.url:type_name -> config.URL
	40,  // 30: config.TerminalIntegration.url:type_name -> config.URL
	38,  // 31: config.TerminalIntegration.globalRateLimit:type_name -> config.RateLimit
	84,  // 32: config.TerminalIntegration.connectionEndpointRateLimit:type_name -> config.TerminalIntegration.ConnectionEndpointRateLimitEntry
	38,  // 33: config.OpenMeteoIntegration.ratelimit:type_name -> config.RateLimit
	37,  // 34: config.TelematicsIntegrationConfigurations.keepTruckin:type_name -> config.KeepTruckinIntegration
	41,  // 35: config.TelematicsIntegrationConfigurations.milliman:type_name -> config.MillimanIntegration
	39,  // 36: config.TelematicsIntegrationConfigurations.samsara:type_name -> config.SamsaraIntegration
	42,  // 37: config.TelematicsIntegrationConfigurations.terminal:type_name -> config.TerminalIntegration
	43,  // 38: config.TelematicsIntegrationConfigurations.openmeteo:type_name -> config.OpenMeteoIntegration
	44,  // 39: config.TelematicsIntegrationConfigurations.cmt:type_name -> config.CmtIntegration
	46,  // 40: config.TelematicsPipelines.slidingWindowMillimanAccurateScore:type_name -> config.SlidingWindowMillimanAccurateScorePipeline
	2,   // 41: config.Telematics.DataPlatformBackend:type_name -> config.ObjectBackend
	3,   // 42: config.Telematics.enabledProviders:type_name -> config.TelematicsDataProvider
	36,  // 43: config.Telematics.credentials:type_name -> config.TelematicsCredentials
	47,  // 44: config.Telematics.pipelines:type_name -> config.TelematicsPipelines
	45,  // 45: config.Telematics.integrations:type_name -> config.TelematicsIntegrationConfigurations
	4,   // 46: config.Telematics.enabledDecorators:type_name -> config.TelematicsDecorator
	49,  // 47: config.ProductTools.pagerduty:type_name -> config.PagerDutyKeys
	50,  // 48: config.ProductTools.googleAppConfig:type_name -> config.GoogleAppConfig
	52,  // 49: config.ProductTools.pibitAiConfig:type_name -> config.PibitAiConfig
	31,  // 50: config.ProductTools.DBTCloudCredentials:type_name -> config.DBTCloudCredentials
	66,  // 51: config.ProductTools.smartProxyConfig:type_name -> config.SmartProxyConfig
	53,  // 52: config.ProductTools.veriskConfig:type_name -> config.VeriskConfig
	54,  // 53: config.ProductTools.workramp:type_name -> config.Workramp
	57,  // 54: config.ProductTools.salesforceConfig:type_name -> config.SalesforceConfig
	55,  // 55: config.ProductTools.hubspot:type_name -> config.Hubspot
	56,  // 56: config.ProductTools.knock:type_name -> config.Knock
	59,  // 57: config.ProductTools.flatfile:type_name -> config.Flatfile
	60,  // 58: config.ProductTools.impler:type_name -> config.Impler
	61,  // 59: config.ProductTools.postHog:type_name -> config.PostHog
	58,  // 60: config.ProductTools.jira:type_name -> config.JiraConfig
	62,  // 61: config.ProductTools.openmeter:type_name -> config.Openmeter
	63,  // 62: config.ProductTools.ascend:type_name -> config.Ascend
	76,  // 63: config.ProductTools.twilio:type_name -> config.Twilio
	64,  // 64: config.ProductTools.snapsheet:type_name -> config.Snapsheet
	51,  // 65: config.ProductTools.clerkConfig:type_name -> config.ClerkConfig
	78,  // 66: config.ProductTools.dsModelConfig:type_name -> config.DSModelServer
	79,  // 67: config.ProductTools.gorulesServer:type_name -> config.GorulesServer
	85,  // 68: config.JobberProcessorConfig.storeConfig:type_name -> config.JobberProcessorConfig.StoreConfig
	86,  // 69: config.JobberProcessorConfig.monitorConfig:type_name -> config.JobberProcessorConfig.MonitorConfig
	87,  // 70: config.JobberProcessorConfig.registryConfig:type_name -> config.JobberProcessorConfig.RegistryConfig
	88,  // 71: config.JobberProcessorConfig.stuckJobDetectorConfig:type_name -> config.JobberProcessorConfig.StuckJobDetectorConfig
	67,  // 72: config.JobberProcessors.standard:type_name -> config.JobberProcessorConfig
	67,  // 73: config.JobberProcessors.dataInfra:type_name -> config.JobberProcessorConfig
	67,  // 74: config.JobberProcessors.quoting:type_name -> config.JobberProcessorConfig
	67,  // 75: config.JobberProcessors.event:type_name -> config.JobberProcessorConfig
	67,  // 76: config.JobberProcessors.safety:type_name -> config.JobberProcessorConfig
	67,  // 77: config.JobberProcessors.test1:type_name -> config.JobberProcessorConfig
	67,  // 78: config.JobberProcessors.test2:type_name -> config.JobberProcessorConfig
	70,  // 79: config.Safety.senture:type_name -> config.Senture
	1,   // 80: config.Config.Env:type_name -> config.Env
	16,  // 81: config.Config.Databases:type_name -> config.Databases
	17,  // 82: config.Config.Scrapers:type_name -> config.Scrapers
	28,  // 83: config.Config.Verisk:type_name -> config.Verisk
	27,  // 84: config.Config.AWS:type_name -> config.AWS
	48,  // 85: config.Config.Telematics:type_name -> config.Telematics
	65,  // 86: config.Config.ProductTools:type_name -> config.ProductTools
	20,  // 87: config.Config.Services:type_name -> config.Services
	71,  // 88: config.Config.Safety:type_name -> config.Safety
	26,  // 89: config.Config.infra:type_name -> config.Infra
	72,  // 90: config.Config.Fmcsa:type_name -> config.Fmcsa
	69,  // 91: config.Config.JobberProcessors:type_name -> config.JobberProcessors
	73,  // 92: config.Config.RefresherConfig:type_name -> config.RefresherConfig
	68,  // 93: config.Config.JobberSingletonsConfig:type_name -> config.JobberSingletonsConfig
	74,  // 94: config.Config.Runtime:type_name -> config.Runtime
	77,  // 95: config.Config.NirvanaUrls:type_name -> config.NirvanaUrls
	81,  // 96: config.Databases.SnowflakeSettings.users:type_name -> config.Databases.SnowflakeSettings.Users
	82,  // 97: config.Databases.SnowflakeSettings.connections:type_name -> config.Databases.SnowflakeSettings.Connections
	14,  // 98: config.Databases.SnowflakeSettings.Users.admin:type_name -> config.SnowflakeUser
	14,  // 99: config.Databases.SnowflakeSettings.Users.datascience:type_name -> config.SnowflakeUser
	14,  // 100: config.Databases.SnowflakeSettings.Users.fmcsa:type_name -> config.SnowflakeUser
	14,  // 101: config.Databases.SnowflakeSettings.Users.fmcsaLocalPuller:type_name -> config.SnowflakeUser
	14,  // 102: config.Databases.SnowflakeSettings.Users.analytics_core:type_name -> config.SnowflakeUser
	15,  // 103: config.Databases.SnowflakeSettings.Users.snapsheet:type_name -> config.SnowflakeKeyPairUser
	13,  // 104: config.Databases.SnowflakeSettings.Connections.fmcsa_dbt:type_name -> config.SnowflakeConfig
	13,  // 105: config.Databases.SnowflakeSettings.Connections.datascience:type_name -> config.SnowflakeConfig
	13,  // 106: config.Databases.SnowflakeSettings.Connections.smartdrive:type_name -> config.SnowflakeConfig
	13,  // 107: config.Databases.SnowflakeSettings.Connections.fmcsaLocalPuller:type_name -> config.SnowflakeConfig
	13,  // 108: config.Databases.SnowflakeSettings.Connections.analytics_core:type_name -> config.SnowflakeConfig
	13,  // 109: config.Databases.SnowflakeSettings.Connections.snapsheet:type_name -> config.SnowflakeConfig
	38,  // 110: config.SamsaraIntegration.EndpointsEntry.value:type_name -> config.RateLimit
	38,  // 111: config.TerminalIntegration.ConnectionEndpointRateLimitEntry.value:type_name -> config.RateLimit
	5,   // 112: config.JobberProcessorConfig.StoreConfig.type:type_name -> config.JobberProcessorConfig.StoreConfig.Type
	90,  // 113: config.JobberProcessorConfig.StoreConfig.pgTableBackedSpec:type_name -> config.JobberProcessorConfig.StoreConfig.PgTableBackedSpec
	6,   // 114: config.JobberProcessorConfig.MonitorConfig.type:type_name -> config.JobberProcessorConfig.MonitorConfig.Type
	91,  // 115: config.JobberProcessorConfig.MonitorConfig.ecsMonitorSpec:type_name -> config.JobberProcessorConfig.MonitorConfig.EcsMonitorSpec
	95,  // 116: config.JobberProcessorConfig.RegistryConfig.jobsMetrics:type_name -> config.JobberProcessorConfig.RegistryConfig.JobMetrics
	93,  // 117: config.JobberProcessorConfig.RegistryConfig.defaultJobMetrics:type_name -> config.JobberProcessorConfig.RegistryConfig.JobMetric
	92,  // 118: config.JobberProcessorConfig.RegistryConfig.defaultTaskMetrics:type_name -> config.JobberProcessorConfig.RegistryConfig.TaskMetric
	97,  // 119: config.JobberProcessorConfig.StuckJobDetectorConfig.stuckJobRules:type_name -> config.JobberProcessorConfig.StuckJobDetectorConfig.StuckJobRule
	96,  // 120: config.JobberProcessorConfig.StuckJobDetectorConfig.defaultStuckRules:type_name -> config.JobberProcessorConfig.StuckJobDetectorConfig.StuckRule
	101, // 121: config.JobberProcessorConfig.StuckJobDetectorConfig.stuckJobActions:type_name -> config.JobberProcessorConfig.StuckJobDetectorConfig.StuckJobAction
	98,  // 122: config.JobberProcessorConfig.StuckJobDetectorConfig.defaultStuckAction:type_name -> config.JobberProcessorConfig.StuckJobDetectorConfig.StuckAction
	7,   // 123: config.JobberProcessorConfig.RegistryConfig.TaskMetric.name:type_name -> config.JobberProcessorConfig.RegistryConfig.TaskMetric.Name
	8,   // 124: config.JobberProcessorConfig.RegistryConfig.JobMetric.name:type_name -> config.JobberProcessorConfig.RegistryConfig.JobMetric.Name
	92,  // 125: config.JobberProcessorConfig.RegistryConfig.TaskMetrics.metrics:type_name -> config.JobberProcessorConfig.RegistryConfig.TaskMetric
	93,  // 126: config.JobberProcessorConfig.RegistryConfig.JobMetrics.metrics:type_name -> config.JobberProcessorConfig.RegistryConfig.JobMetric
	94,  // 127: config.JobberProcessorConfig.RegistryConfig.JobMetrics.tasksMetrics:type_name -> config.JobberProcessorConfig.RegistryConfig.TaskMetrics
	9,   // 128: config.JobberProcessorConfig.StuckJobDetectorConfig.StuckRule.ruleId:type_name -> config.JobberProcessorConfig.StuckJobDetectorConfig.RuleId
	96,  // 129: config.JobberProcessorConfig.StuckJobDetectorConfig.StuckJobRule.stuckRules:type_name -> config.JobberProcessorConfig.StuckJobDetectorConfig.StuckRule
	10,  // 130: config.JobberProcessorConfig.StuckJobDetectorConfig.StuckAction.actionId:type_name -> config.JobberProcessorConfig.StuckJobDetectorConfig.ActionId
	99,  // 131: config.JobberProcessorConfig.StuckJobDetectorConfig.StuckAction.slackActionParams:type_name -> config.JobberProcessorConfig.StuckJobDetectorConfig.SlackActionParams
	100, // 132: config.JobberProcessorConfig.StuckJobDetectorConfig.StuckAction.logActionParams:type_name -> config.JobberProcessorConfig.StuckJobDetectorConfig.LogActionParams
	98,  // 133: config.JobberProcessorConfig.StuckJobDetectorConfig.StuckJobAction.stuckAction:type_name -> config.JobberProcessorConfig.StuckJobDetectorConfig.StuckAction
	134, // [134:134] is the sub-list for method output_type
	134, // [134:134] is the sub-list for method input_type
	134, // [134:134] is the sub-list for extension type_name
	134, // [134:134] is the sub-list for extension extendee
	0,   // [0:134] is the sub-list for field type_name
}

func init() { file_config_config_proto_init() }
func file_config_config_proto_init() {
	if File_config_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_config_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DBSSLConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DBConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnowflakeConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnowflakeUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnowflakeKeyPairUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Databases); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scrapers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MVRService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OAuthServices); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Services); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PDFGen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Tracing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Logging); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LambdaGrpc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Infra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AWS); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Verisk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S3Credentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsOAuthAppCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DBTCloudCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TerminalCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HereMapsCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenMeteoCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmtCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsCredentials); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepTruckinIntegration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RateLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SamsaraIntegration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*URL); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MillimanIntegration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TerminalIntegration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenMeteoIntegration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmtIntegration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsIntegrationConfigurations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlidingWindowMillimanAccurateScorePipeline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsPipelines); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Telematics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PagerDutyKeys); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoogleAppConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClerkConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PibitAiConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VeriskConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Workramp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Hubspot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Knock); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SalesforceConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JiraConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Flatfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Impler); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostHog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Openmeter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ascend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Snapsheet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductTools); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmartProxyConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberSingletonsConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Senture); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Safety); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fmcsa); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefresherConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Runtime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Twilio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NirvanaUrls); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DSModelServer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GorulesServer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Databases_SnowflakeSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Databases_SnowflakeSettings_Users); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Databases_SnowflakeSettings_Connections); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_StoreConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_MonitorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_RegistryConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_StuckJobDetectorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_StoreConfig_EmbeddedSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_StoreConfig_PgTableBackedSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_MonitorConfig_EcsMonitorSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_RegistryConfig_TaskMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_RegistryConfig_JobMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_RegistryConfig_TaskMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_RegistryConfig_JobMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_StuckJobDetectorConfig_StuckRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_StuckJobDetectorConfig_StuckAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_StuckJobDetectorConfig_SlackActionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_StuckJobDetectorConfig_LogActionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_config_config_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobberProcessorConfig_StuckJobDetectorConfig_StuckJobAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_config_config_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_config_config_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_config_config_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_config_config_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_config_config_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_config_config_proto_msgTypes[81].OneofWrappers = []interface{}{}
	file_config_config_proto_msgTypes[82].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_config_config_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   91,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_config_config_proto_goTypes,
		DependencyIndexes: file_config_config_proto_depIdxs,
		EnumInfos:         file_config_config_proto_enumTypes,
		MessageInfos:      file_config_config_proto_msgTypes,
	}.Build()
	File_config_config_proto = out.File
	file_config_config_proto_rawDesc = nil
	file_config_config_proto_goTypes = nil
	file_config_config_proto_depIdxs = nil
}
