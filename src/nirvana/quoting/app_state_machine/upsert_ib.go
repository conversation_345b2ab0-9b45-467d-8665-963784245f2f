package app_state_machine

import (
	"context"
	"fmt"
	"slices"
	"strconv"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/timestamppb"

	billing_enums "nirvanatech.com/nirvana/billing/enums"
	"nirvanatech.com/nirvana/billing/legacy/billinginfo"
	authutil "nirvanatech.com/nirvana/common-go/auth-util"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums/old_enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/entity_license"
	entity_license_enums "nirvanatech.com/nirvana/db-api/db_wrappers/entity_license/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	fleetmodel "nirvanatech.com/nirvana/fleet/model"
	"nirvanatech.com/nirvana/fmcsa/models"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-bundle/model/charges"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	"nirvanatech.com/nirvana/insurance-core/coverage"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
	insuredmodel "nirvanatech.com/nirvana/insured/model"
	policyconstants "nirvanatech.com/nirvana/policy/constants"
	fleet_policy_utils "nirvanatech.com/nirvana/policy/fleet"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"

	common_utils "nirvanatech.com/nirvana/underwriting/common-utils"
)

const (
	defaultFleetIBMigrationPhase2Enablement   = false
	limitIDDelimiter                          = "$"
	cargoAtScheduledTerminalsLimitDisplayName = "%s for terminal %s"
)

func (a *appStateMachineHelper) upsertIB(ctx context.Context, args *createPolicyArgs) error {
	if args == nil {
		return errors.New("nil args")
	}
	if args.appObj == nil {
		return errors.New("application object is nil")
	}

	if isIBMigrationPhase2Enabled(ctx, a.deps, args.appObj) {
		if err := a.upsertFleetIB(ctx, args); err != nil {
			log.Error(ctx, "failed to upsert fleet insurance bundle", log.Err(err), log.Any("args", args))
			return errors.Wrapf(err, "failed to upsert fleet insurance bundle")
		}
	} else {
		log.Info(
			ctx, "Skipping fleet IB creation as feature flag is not enabled",
			log.String("appID", args.appObj.ID),
		)
	}

	return nil
}

// We do not fail the flow if we have issues determining if the feature flag is enabled.
func isIBMigrationPhase2Enabled(ctx context.Context, deps ASMDeps, appObj *app.Application) bool {
	appCreator, err := authutil.GetUserFromUserID(ctx, deps.AuthWrapper, appObj.MarketerID.String())
	if err != nil {
		log.Error(ctx, "failed to get app creator user", log.Err(err), log.String("appID", appObj.ID))
		return defaultFleetIBMigrationPhase2Enablement
	}

	isEnabled, err := deps.FeatureFlag.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(*appCreator),
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		defaultFleetIBMigrationPhase2Enablement,
	)
	if err != nil {
		log.Error(
			ctx, "failed to get feature flag value", log.Err(err), log.Any("appCreator", appCreator),
		)
		return defaultFleetIBMigrationPhase2Enablement
	}

	return isEnabled
}

func (a *appStateMachineHelper) upsertFleetIB(ctx context.Context, args *createPolicyArgs) error {
	upsertIBRequest, err := newFleetUpsertIBRequest(ctx, a.deps, args)
	if err != nil {
		return errors.Wrap(err, "failed to create fleet IB request")
	}

	resp, err := a.deps.IBService.UpsertInsuranceBundle(ctx, upsertIBRequest)
	if err != nil {
		return errors.Wrap(err, "UpsertInsuranceBundle service call failed")
	}

	log.Info(ctx, "successfully created fleet insurance bundle", log.Any("response", resp))

	return nil
}

func newFleetUpsertIBRequest(
	ctx context.Context, deps ASMDeps, args *createPolicyArgs,
) (*service.UpsertInsuranceBundleRequest, error) {
	appObj := args.appObj
	subObj := args.subObj

	if subObj == nil {
		return nil, errors.New("submission object is nil")
	}
	if subObj.EffectiveDateTo == nil {
		return nil, errors.New("submission effective date to is nil")
	}

	primaryInsuredAddress := getProtoAddress(args.dotDetails)

	insuranceBundle, err := getInsuranceBundle(ctx, deps, args, primaryInsuredAddress)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get insurance bundle")
	}

	return &service.UpsertInsuranceBundleRequest{
		InsuranceBundle: insuranceBundle,
		Insured: &insuredmodel.Insured{
			Name: &insurancecoreproto.InsuredName{
				BusinessName: appObj.CompanyInfo.Name,
			},
			Address: primaryInsuredAddress,
			ContactInfo: &insuredmodel.ContactInfo{
				Email: getEmailFromAdditionalInsuredInfo(subObj.AdditionalInsuredInfo),
				Phone: "", // Not captured today, hence empty.
			},
			ExternalIdentifier: &insurancecoreproto.InsuredIdentifier{
				Type:  insurancecoreproto.InsuredIdentifierType_InsuredIdentifierType_DOTNumber,
				Value: []string{strconv.FormatInt(subObj.CompanyInfo.DOTNumber, 10)},
			},
		},
	}, nil
}

func getInsuranceBundle(
	ctx context.Context, deps ASMDeps, args *createPolicyArgs, primaryInsuredAddress *proto.Address,
) (*model.InsuranceBundle, error) {
	segment, err := getIBSegment(ctx, deps, args, primaryInsuredAddress)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get IB segments")
	}

	return &model.InsuranceBundle{
		DefaultCarrier: insurancecoreproto.InsuranceCarrierFromHumanReadableString(
			args.subObj.ModelPinConfig.Application.InsuranceCarrier.String(),
		),
		DefaultSeller: &insurancecoreproto.SellerInfo{
			AgencyID: args.appObj.AgencyID.String(),
		},
		DefaultEffectiveDuration: &proto.Interval{
			Start: timestamppb.New(args.subObj.CoverageInfo.EffectiveDate),
			End:   timestamppb.New(*args.subObj.EffectiveDateTo),
		},
		ProgramType: insurancecoreproto.ProgramType_ProgramType_Fleet,
		Metadata: &model.InsuranceBundleMetadata{
			RootBindableSubmissionId: args.subObj.ID,
			RootApplicationId:        args.appObj.ID,
		},
		FormInfo: insurancecoreproto.GetFormInfoFromFormCompilation(
			ctx,
			args.signPacketFormComp,
			insurancecoreproto.FormCompilationType_FormCompilationTypeSignaturePacket,
			pointer_utils.StringerValOr(args.subObj.QuotePDFHandleId, ""),
		),
		Segments: []*model.InsuranceBundleSegment{
			segment,
		},
		CarrierAdmittedType: getCarrierAdmittedType(args.appObj.ModelPinConfig.Application.IsNonAdmitted),
	}, nil
}

func getEmailFromAdditionalInsuredInfo(additionalInsuredInfo *app.AdditionalInsuredInfo) string {
	if additionalInsuredInfo == nil || !additionalInsuredInfo.Email.Valid {
		return ""
	}
	return additionalInsuredInfo.Email.String
}

func getCarrierAdmittedType(isNonAdmitted bool) insurancecoreproto.CarrierAdmittedType {
	if isNonAdmitted {
		return insurancecoreproto.CarrierAdmittedType_CarrierAdmittedType_NonAdmitted
	}
	return insurancecoreproto.CarrierAdmittedType_CarrierAdmittedType_Admitted
}

func getIBSegment(
	ctx context.Context,
	deps ASMDeps,
	args *createPolicyArgs,
	primaryInsuredAddress *proto.Address,
) (*model.InsuranceBundleSegment, error) {
	terminalLocations, cargoAtTerminalLimits, err := getTerminalLocationsAndCargoAtTerminalLimitsForIB(
		args.subObj.UnderwriterInput, args.subObj.CompanyInfo.TerminalLocations,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get terminal locations and cargo at terminal limits")
	}

	policies, err := getPolicies(ctx, deps, args, primaryInsuredAddress, terminalLocations)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get policies")
	}

	limits, err := getLimits(args.subObj.CoverageInfo, cargoAtTerminalLimits)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get limits")
	}

	return &model.InsuranceBundleSegment{
		Id: uuid.NewString(),
		Interval: &proto.Interval{
			Start: timestamppb.New(args.subObj.CoverageInfo.EffectiveDate),
			End:   timestamppb.New(*args.subObj.EffectiveDateTo),
		},
		PrimaryInsured: &insurancecoreproto.Insured{
			Type: insurancecoreproto.InsuredType_InsuredType_PrimaryInsured,
			Name: &insurancecoreproto.InsuredName{
				BusinessName: args.subObj.CompanyInfo.Name,
			},
			Address: getProtoAddress(args.dotDetails),
			ExternalIdentifier: &insurancecoreproto.InsuredIdentifier{
				Type:  insurancecoreproto.InsuredIdentifierType_InsuredIdentifierType_DOTNumber,
				Value: []string{strconv.FormatInt(args.subObj.CompanyInfo.DOTNumber, 10)},
			},
		},
		Policies: policies,
		CoverageCriteria: &model.CoverageCriteria{
			Limits:              limits,
			Deductibles:         getDeductibles(args.subObj.CoverageInfo),
			CombinedDeductibles: getCombinedDeductibles(args.subObj.CoverageInfo),
		},
	}, nil
}

func getProtoAddress(dotDetails *models.DotDetails) *proto.Address {
	census := dotDetails.Census
	return &proto.Address{
		Nation:     pointer_utils.ToPointer(us_states.DefaultCountry),
		State:      census.PhysicalAddressState,
		City:       census.PhysicalAddressCity,
		CountyCode: census.PhysicalAddressCountyCode,
		Street:     census.PhysicalAddressStreet,
		ZipCode:    census.PhysicalAddressZipCode,
	}
}

func getLimits(coverageInfo *app.CoverageInfo, cargoAtTerminalLimits []*model.Limit) ([]*model.Limit, error) {
	limits := make([]*model.Limit, 0)
	for _, cov := range coverageInfo.Coverages {
		if coverage.IsAppCoverageAPolicyModifier(cov.CoverageType) {
			continue
		}
		if cov.Limit == nil || *cov.Limit == 0 {
			continue
		}

		if cov.CoverageType == appenums.CoverageCargoAtScheduledTerminals {
			return nil, errors.Newf("limit for %s coverage should not be set", cov.CoverageType)
		}

		subCovIds := []string{cov.CoverageType.String()}
		grouping := model.LimitGrouping_LimitGrouping_Single
		if cov.CoverageType.IsPrimaryCoverage() {
			subCovIds = coverage.GetSubCoverageStringFromPrimaryCoverage(cov.CoverageType)
			if cov.CoverageType == appenums.CoverageAutoLiability {
				grouping = model.LimitGrouping_LimitGrouping_Combined
			}
		}

		limits = append(limits, getLimit(cov, subCovIds, grouping))
	}

	limits = append(limits, cargoAtTerminalLimits...)

	return limits, nil
}

func getLimit(cov app.CoverageDetails, subCovs []string, limitGrouping model.LimitGrouping) *model.Limit {
	covId := cov.CoverageType.String()
	if limitGrouping == model.LimitGrouping_LimitGrouping_Single && len(subCovs) == 1 {
		// For APD, which has 2 single limits for Coll and Comp, we use the coll and comp as ids.
		covId = subCovs[0]
	}
	return &model.Limit{
		Id:             covId,
		DisplayName:    appenums.GetCoverageLabel(cov.CoverageType),
		SubCoverageIds: subCovs,
		Amount:         float64(*cov.Limit),
		Grouping:       limitGrouping,
	}
}

func getDeductibles(coverageInfo *app.CoverageInfo) []*model.Deductible {
	deductibles := make([]*model.Deductible, 0)

	for _, cov := range coverageInfo.Coverages {
		if coverage.IsAppCoverageAPolicyModifier(cov.CoverageType) || cov.Deductible == nil {
			continue
		}

		subCovIds := []string{cov.CoverageType.String()}
		if cov.CoverageType.IsPrimaryCoverage() {
			subCovIds = coverage.GetSubCoverageStringFromPrimaryCoverage(cov.CoverageType)
		}
		deductibles = updateDeductibleForCoverages(deductibles, cov, subCovIds)
	}

	return deductibles
}

func updateDeductibleForCoverages(
	deductibles []*model.Deductible,
	cov app.CoverageDetails,
	subCovs []string,
) []*model.Deductible {
	if cov.CoverageType == appenums.CoverageAutoLiability {
		// For Auto Liability, BI PD is getting treated as a group deductible.
		deductibles = append(deductibles, &model.Deductible{
			Amount:         float64(*cov.Deductible),
			SubCoverageIds: subCovs,
		})
	} else {
		for _, subCov := range subCovs {
			deductibles = append(deductibles, &model.Deductible{
				Amount:         float64(*cov.Deductible),
				SubCoverageIds: []string{subCov},
			})
		}
	}
	return deductibles
}

func getCombinedDeductibles(coverageInfo *app.CoverageInfo) []*model.CombinedDeductible {
	if coverageInfo.CoveragesWithCombinedDeductibles == nil {
		return nil
	}

	combinedDeductibles := make([]*model.CombinedDeductible, 0)
	for _, combinedCoverages := range coverageInfo.CoveragesWithCombinedDeductibles.CombinedCoveragesList {
		subCovIds := make([]string, 0)
		for cov := range combinedCoverages {
			if cov.IsPrimaryCoverage() {
				subCovIds = append(subCovIds, coverage.GetSubCoverageStringFromPrimaryCoverage(cov)...)
			} else {
				subCovIds = append(subCovIds, cov.String())
			}
		}

		// We sort the subCoverageIds to ensure consistent ordering. This helps with testing.
		slices.Sort(subCovIds)

		combinedDeductibles = append(combinedDeductibles, &model.CombinedDeductible{
			SubCoverageIds: subCovIds,
		})
	}

	return combinedDeductibles
}

func getPolicies(
	ctx context.Context,
	deps ASMDeps,
	args *createPolicyArgs,
	primaryInsuredAddress *proto.Address,
	terminalLocations []*fleetmodel.TerminalLocation,
) (map[string]*model.Policy, error) {
	policyMap := make(map[string]*model.Policy)
	fcCoverageIdMap := args.signPacketFormComp.Metadata().CoverageIdsMap
	if fcCoverageIdMap == nil {
		return nil, errors.Newf("form compilation coverage ids map is nil")
	}

	for _, cov := range args.subObj.CoverageInfo.Coverages {
		if !slice_utils.Contains(policyconstants.CoveragesWithPolicies, cov.CoverageType) {
			continue
		}

		args.coverage = cov.CoverageType
		policy, err := getPolicy(ctx, deps, args, fcCoverageIdMap, primaryInsuredAddress, terminalLocations)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to create policy for coverage %s", cov.CoverageType)
		}
		policyMap[policy.PolicyNumber] = policy
	}

	if len(policyMap) == 0 {
		return nil, errors.New("no policies created, policy map is empty")
	}

	return policyMap, nil
}

func getPolicy(
	ctx context.Context,
	deps ASMDeps,
	args *createPolicyArgs,
	fcCoverageIdMap *compilation.CoverageIdsMap,
	primaryInsuredAddress *proto.Address,
	terminalLocations []*fleetmodel.TerminalLocation,
) (*model.Policy, error) {
	mainCov := args.coverage

	programData, err := getProgramDataForIB(ctx, args, primaryInsuredAddress, terminalLocations)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get fleet program data for IB")
	}

	policyNumberStruct, err := fleet_policy_utils.GeneratePolicyNumber(
		mainCov,
		args.subObj.CoverageInfo.EffectiveDate,
		args.policySetIdentifier,
		args.subObj.ModelPinConfig.Application.InsuranceCarrier,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to generate policy number for coverage %s", mainCov)
	}

	formInfo, err := getPolicyFormInfo(ctx, deps, fcCoverageIdMap, mainCov)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get policy form info for coverage %s", mainCov)
	}

	return &model.Policy{
		PolicyNumber: policyNumberStruct.String(),
		State:        model.PolicyState_PolicyState_Created,
		Coverages:    getCoverages(ctx, mainCov, args.subObj.CoverageInfo),
		EffectiveInterval: &proto.Interval{
			Start: timestamppb.New(args.subObj.CoverageInfo.EffectiveDate),
			End:   timestamppb.New(*args.subObj.EffectiveDateTo),
		},
		Metadata: &model.PolicyMetadata{
			ApplicationId:        args.appObj.ID,
			BindableSubmissionId: args.subObj.ID,
		},
		ProgramData: programData,
		WrittenExposure: &model.Exposure{
			Exposures: &model.Exposure_FleetExposure{
				FleetExposure: &fleetmodel.Exposure{
					ProjectedMileage: int32(args.subObj.CompanyInfo.ProjectedMileage),
					QuotedMileage:    int32(args.appObj.CompanyInfo.ProjectedMileage),
				},
			},
		},
		FormInfo: formInfo,
		Charges:  &charges.ChargeList{}, // Charges will be added when the new fleet pricing API is integrated.
		Clauses:  getClausesForPolicy(args.subObj.CoverageInfo),
	}, nil
}

func getCoverages(
	ctx context.Context, mainCov appenums.Coverage, coverageInfo *app.CoverageInfo,
) []*model.Coverage {
	coverages := []*model.Coverage{
		{
			Id:           mainCov.String(),
			DisplayName:  appenums.GetCoverageLabel(mainCov),
			SubCoverages: getFleetSubCoverages(ctx, mainCov, coverageInfo),
		},
	}

	if mainCov == appenums.CoverageAutoLiability &&
		coverageInfo.ContainsCoverage(appenums.CoverageAutoPhysicalDamage) {
		coverages = append(coverages, &model.Coverage{
			Id:           appenums.CoverageAutoPhysicalDamage.String(),
			DisplayName:  appenums.GetCoverageLabel(appenums.CoverageAutoPhysicalDamage),
			SubCoverages: getFleetSubCoverages(ctx, appenums.CoverageAutoPhysicalDamage, coverageInfo),
		})
	}
	return coverages
}

func getPolicyFormInfo(
	ctx context.Context,
	deps ASMDeps,
	fcCoverageIdMap *compilation.CoverageIdsMap,
	mainCov appenums.Coverage,
) (*insurancecoreproto.FormInfo, error) {
	policyFormCompilationID := fcCoverageIdMap.GetFormCompilationID(mainCov)
	if policyFormCompilationID == nil {
		return nil, errors.Newf("form compilation id not found for coverage type %s", mainCov)
	}
	policyFormCompilation, err := deps.FormsWrapper.GetFormCompilationById(ctx, *policyFormCompilationID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get policy form compilation with id %s",
			policyFormCompilationID)
	}

	return insurancecoreproto.GetFormInfoFromFormCompilation(
		ctx,
		*policyFormCompilation,
		insurancecoreproto.FormCompilationType_FormCompilationTypePolicy,
		"",
	), nil
}

func getProgramDataForIB(
	ctx context.Context, args *createPolicyArgs, primaryInsuredAddress *proto.Address,
	terminalLocations []*fleetmodel.TerminalLocation,
) (*model.ProgramData, error) {
	minimumMileageGuaranteed := isMinimumMileageGuaranteeApplicable(args.subObj.UnderwriterInput)

	primaryCommodity, err := getCommodityDetailsForIB(args.subObj)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get primary commodity details for IB")
	}

	company, err := getCompanyForIB(args, primaryInsuredAddress, terminalLocations)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get company for IB")
	}

	operation, err := getOperationForIB(args)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get operation for IB")
	}

	negotiatedRates, err := getNegotiatedRatesForIB(args.subObj.UnderwriterInput.NegotiatedRates)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get negotiated rates for IB")
	}

	brokerLicense, err := getBrokerLicenseForIB(ctx, args.appObj.ID, args.entityLicense)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get broker license for IB")
	}

	billing, err := getBillingForIB(args.billingInfo)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get billing for IB")
	}

	return &model.ProgramData{
		Data: &model.ProgramData_FleetData{
			FleetData: &fleetmodel.FleetProgramData{
				Company:                             company,
				Operation:                           operation,
				Drivers:                             getDriversForIB(args.subObj.DriversInfo.Drivers),
				Equipment:                           getEquipmentForIB(args),
				LossHistory:                         getLossHistoryForIBFromSubmission(args.subObj),
				NegotiatedRates:                     negotiatedRates,
				CommodityDetails:                    primaryCommodity,
				BrokerLicense:                       brokerLicense,
				CameraSubsidy:                       getCameraSubsidyForIB(args.appReview.CameraSubsidyDetails),
				Billing:                             billing,
				IsMinimumMileageGuaranteeApplicable: minimumMileageGuaranteed,
				ScheduleModification: getScheduleModsForIB(
					args.subObj.RateMLUnderwritingEntityInputs,
				),
			},
		},
	}, nil
}

func isMinimumMileageGuaranteeApplicable(uwInput *app.UnderwriterInput) bool {
	return uwInput != nil &&
		uwInput.MinimumMileageGuarantee != nil &&
		uwInput.MinimumMileageGuarantee.IsMinimumMileageGuaranteed
}

func getLossHistoryForIBFromSubmission(sub *app.SubmissionObject) *fleetmodel.LossHistory {
	var largeLosses []app.LargeLoss
	if sub.UnderwriterInput != nil {
		largeLosses = sub.UnderwriterInput.LargeLosses
	}

	var lossSummary []app.LossRunSummaryPerCoverage
	if sub.LossInfo != nil {
		lossSummary = sub.LossInfo.LossRunSummary
	}

	return getLossHistoryForIB(lossSummary, largeLosses)
}

func getCompanyForIB(
	args *createPolicyArgs, primaryInsuredAddress *proto.Address, terminalLocations []*fleetmodel.TerminalLocation,
) (*fleetmodel.Company, error) {
	return &fleetmodel.Company{
		DoingBusinessAs:   args.dotDetails.Census.Dba,
		UsState:           args.subObj.CompanyInfo.USState.ToCode(),
		TerminalLocations: terminalLocations,
		MailingAddress:    primaryInsuredAddress,
		TaxRecords:        getTaxRecordsForIB(args.subObj.CompanyInfo.PremiumTaxRecords),
	}, nil
}

func getOperationForIB(args *createPolicyArgs) (*fleetmodel.Operation, error) {
	primaryOperatingClass, err := appenums.TransformApplicationOperatingClassToProto(
		*args.subObj.EquipmentInfo.PrimaryOperatingClass,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to transform primary operating class to proto")
	}

	operationClassRecords, err := getOperationClassRecords(args.subObj.EquipmentInfo.OperatingClassDistribution)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get operation class records for IB")
	}

	radiusOfOperationRecords, err := getRadiusOfOperationRecords(args.subObj.CompanyInfo.RadiusOfOperation)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get radius of operation records for IB")
	}

	var percOfSubHaul *float64
	if args.subObj.AdditionalInfoExtraMetadata != nil &&
		args.subObj.AdditionalInfoExtraMetadata.PercentageOfSubhaul != nil {
		percOfSubHaul = pointer_utils.ToPointer(
			float64(*args.subObj.AdditionalInfoExtraMetadata.PercentageOfSubhaul),
		)
	}

	return &fleetmodel.Operation{
		PrimaryOperationClass:    primaryOperatingClass,
		OperationClassRecords:    operationClassRecords,
		RadiusOfOperationRecords: radiusOfOperationRecords,
		VehicleZoneRecords: getVehicleZoneRecords(
			args.subObj.UnderwriterInput.VehicleZoneDistribution,
		),
		PercentageOfSubHaul: percOfSubHaul,
	}, nil
}

func getEquipmentForIB(args *createPolicyArgs) *fleetmodel.Equipment {
	var ownerOperatorUnits *int32
	if args.subObj.AdditionalInfoExtraMetadata.NumOwnerOperatorUnits.Valid {
		ownerOperatorUnits = pointer_utils.ToPointer(
			int32(args.subObj.AdditionalInfoExtraMetadata.NumOwnerOperatorUnits.Int),
		)
	}

	return &fleetmodel.Equipment{
		OwnerOperatorUnits: ownerOperatorUnits,
		Vehicles:           getVehiclesForIB(args.subObj.EquipmentInfo.EquipmentList.Info),
	}
}

func getFleetSubCoverages(
	ctx context.Context, primaryCov appenums.Coverage, coverageInfo *app.CoverageInfo,
) []*model.SubCoverage {
	var subCoverages []*model.SubCoverage

	ancCovFromPrimaryCoverage := coverage.GetSubCoverageFromPrimaryCoverage(primaryCov)
	for _, ancCoverage := range ancCovFromPrimaryCoverage {
		subCov := &model.SubCoverage{
			Id:          ancCoverage.String(),
			DisplayName: appenums.GetCoverageLabel(ancCoverage),
		}
		subCoverages = append(subCoverages, subCov)
	}

	for _, cov := range coverageInfo.Coverages {
		if coverage.IsAppCoverageAPolicyModifier(cov.CoverageType) || cov.CoverageType.IsPrimaryCoverage() {
			continue
		}

		pCovFromAncCoverage, ok := appenums.AncCoverageToPrimaryCoverage[cov.CoverageType]
		if !ok {
			log.Error(
				ctx, "sub coverage not found in AncCoverageToPrimaryCoverage map",
				log.Stringer("ancCoverageType", cov.CoverageType),
			)
			continue
		}
		if pCovFromAncCoverage != primaryCov {
			continue
		}

		subCoverage := &model.SubCoverage{
			Id:          cov.CoverageType.String(),
			DisplayName: appenums.GetCoverageLabel(cov.CoverageType),
		}
		subCoverages = append(subCoverages, subCoverage)
	}

	return subCoverages
}

func getTerminalLocationsAndCargoAtTerminalLimitsForIB(
	uwInput *app.UnderwriterInput, terminalLocations *[]app.TerminalLocation,
) ([]*fleetmodel.TerminalLocation, []*model.Limit, error) {
	if terminalLocations == nil || len(*terminalLocations) == 0 {
		return nil, nil, nil // nolint:nilnil
	}

	ratingAddressIdxInTerminalLocations := -1
	if uwInput != nil && uwInput.RatingAddress != nil {
		ratingAddressIdxInTerminalLocations = common_utils.FindRatingAddressIndexInTerminalLocations(
			terminalLocations, uwInput.RatingAddress,
		)
		if ratingAddressIdxInTerminalLocations == -1 {
			return nil, nil, errors.New("rating address not found in terminal locations")
		}
	}

	converterTerminalLocations := make([]*fleetmodel.TerminalLocation, len(*terminalLocations))
	var cargoAtTerminalLimits []*model.Limit
	for i, tl := range *terminalLocations {
		typeOfTerminal, err := appenums.TransformApplicationTypeOfTerminalToProto(tl.TypeOfTerminal)
		if err != nil {
			return nil, nil, errors.Wrapf(
				err, "failed to transform type of terminal %s to proto", tl.TypeOfTerminal,
			)
		}

		converterTerminalLocations[i] = &fleetmodel.TerminalLocation{
			Address: &proto.Address{
				Nation:  pointer_utils.ToPointer(us_states.DefaultCountry),
				State:   pointer_utils.ToPointer(tl.UsState.String()),
				Street:  pointer_utils.ToPointer(tl.AddressLineOne),
				Street2: tl.AddressLineTwo,
				ZipCode: pointer_utils.ToPointer(tl.ZipCodeString),
			},
			IsGated:        tl.IsGated,
			IsGuarded:      tl.IsGuarded,
			TypeOfTerminal: typeOfTerminal,
		}

		if i == ratingAddressIdxInTerminalLocations {
			converterTerminalLocations[i].IsRatingAddress = true
		}

		cargoAtTerminalLimit, err := populateCargoTerminalScheduleFieldsAndGetLimits(
			converterTerminalLocations[i].GetKey(), tl.CargoTerminalSchedule, converterTerminalLocations[i],
		)
		if err != nil {
			return nil, nil, errors.Wrapf(
				err, "failed to populate cargo terminal schedule fields for terminal location at index %d", i,
			)
		}
		if cargoAtTerminalLimit != nil {
			cargoAtTerminalLimits = append(cargoAtTerminalLimits, cargoAtTerminalLimit)
		}
	}

	return converterTerminalLocations, cargoAtTerminalLimits, nil
}

func populateCargoTerminalScheduleFieldsAndGetLimits(
	terminalKey string,
	cargoTerminalSchedule *app.CargoTerminalSchedule, convertedTerminalLocation *fleetmodel.TerminalLocation,
) (*model.Limit, error) {
	var limit *model.Limit

	if cargoTerminalSchedule != nil {
		convertedCargoScheduleEnums, err := cargoTerminalSchedule.TransformEnumsToProto()
		if err != nil {
			return nil, errors.Wrapf(
				err, "failed to transform cargo terminal schedule %s to proto", cargoTerminalSchedule.ID,
			)
		}
		convertedTerminalLocation.ConstructionClass = &convertedCargoScheduleEnums.ConstructionClass
		convertedTerminalLocation.PublicProtectionClass = &convertedCargoScheduleEnums.PublicProtectionClass
		convertedTerminalLocation.PrivateTheftProtection = &convertedCargoScheduleEnums.PrivateTheftProtection
		convertedTerminalLocation.PrivateFireProtection = &convertedCargoScheduleEnums.PrivateFireProtection

		if cargoTerminalSchedule.Limit != nil && *cargoTerminalSchedule.Limit != 0 {
			limit = &model.Limit{
				Id: appenums.CoverageCargoAtScheduledTerminals.String() + limitIDDelimiter + terminalKey,
				DisplayName: fmt.Sprintf(
					cargoAtScheduledTerminalsLimitDisplayName,
					appenums.GetCoverageLabel(appenums.CoverageCargoAtScheduledTerminals), terminalKey,
				),
				SubCoverageIds: []string{appenums.CoverageCargoAtScheduledTerminals.String()},
				Amount:         float64(*cargoTerminalSchedule.Limit),
				Grouping:       model.LimitGrouping_LimitGrouping_Single,
				ExposureEntities: []*model.ExposureEntity{
					{
						Type: model.ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
						Id:   terminalKey,
					},
				},
			}
		}
	}

	return limit, nil
}

func getTaxRecordsForIB(taxRecords []app.PremiumTaxRecord) []*fleetmodel.TaxRecord {
	result := make([]*fleetmodel.TaxRecord, len(taxRecords))
	for i, tr := range taxRecords {
		result[i] = &fleetmodel.TaxRecord{
			JurisdictionType: tr.JurisdictionType,
			JurisdictionName: tr.JurisdictionName,
			TaxCode:          tr.TaxCode,
			LinesOfBusiness:  getLinesOfBusinessForIB(tr.LineOfBusinessDetails),
		}
	}
	return result
}

func getLinesOfBusinessForIB(lobDetails []app.LineOfBusinessDetail) []*fleetmodel.TaxRecordLineOfBusiness {
	result := make([]*fleetmodel.TaxRecordLineOfBusiness, len(lobDetails))
	for i, lob := range lobDetails {
		result[i] = &fleetmodel.TaxRecordLineOfBusiness{
			Coverage: lob.CoverageType.String(),
			TaxValue: lob.TaxValue,
		}
	}
	return result
}

func getOperationClassRecords(
	distribution []app.OperatingClassDistributionRecord,
) ([]*fleetmodel.OperationClassRecord, error) {
	result := make([]*fleetmodel.OperationClassRecord, len(distribution))
	for i, record := range distribution {
		operatingClass, err := appenums.TransformApplicationOperatingClassToProto(record.Class)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to transform operating class %s to proto", record.Class)
		}
		result[i] = &fleetmodel.OperationClassRecord{
			OperationClass: operatingClass,
			Percentage:     record.PercentageOfFleet,
		}
	}
	return result, nil
}

func getRadiusOfOperationRecords(
	radiusRecords []*app.MileageRadiusRecord,
) ([]*fleetmodel.RadiusOfOperationRecord, error) {
	result := make([]*fleetmodel.RadiusOfOperationRecord, len(radiusRecords))
	for i, record := range radiusRecords {
		radiusBucket, err := appenums.TransformApplicationMileageRadiusBucketToProto(record.RadiusBucket)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to transform radius bucket %s to proto", record.RadiusBucket)
		}
		result[i] = &fleetmodel.RadiusOfOperationRecord{
			RadiusOfOperationRange: radiusBucket,
			Percentage:             record.PercentageOfFleet,
		}
	}
	return result, nil
}

func getVehicleZoneRecords(vehicleZones *[]app.VehicleZoneRecord) []*fleetmodel.VehicleZoneRecord {
	if vehicleZones == nil || len(*vehicleZones) == 0 {
		return nil
	}
	result := make([]*fleetmodel.VehicleZoneRecord, len(*vehicleZones))
	for i, vz := range *vehicleZones {
		result[i] = &fleetmodel.VehicleZoneRecord{
			StartZone:  vz.StartZone,
			EndZone:    vz.EndZone,
			Percentage: vz.PercentageOfVehicles,
		}
	}
	return result
}

func getDriversForIB(drivers []app.DriverListRecord) []*fleetmodel.Driver {
	result := make([]*fleetmodel.Driver, len(drivers))
	for i, driver := range drivers {
		protoDriver := &fleetmodel.Driver{
			LicenseNumber: driver.DriverLicenseNumber,
			LicenseState:  driver.UsState,
			DateOfHire:    timestamppb.New(driver.DateHired),
		}

		if driver.DateOfBirth.Valid {
			protoDriver.DateOfBirth = timestamppb.New(driver.DateOfBirth.Time)
		}
		if driver.FirstName.Valid {
			protoDriver.FirstName = pointer_utils.ToPointer(driver.FirstName.String)
		}
		if driver.LastName.Valid {
			protoDriver.LastName = pointer_utils.ToPointer(driver.LastName.String)
		}
		if driver.YearsOfExperience != nil {
			protoDriver.YearsOfExperience = pointer_utils.ToPointer(float64(*driver.YearsOfExperience))
		}

		result[i] = protoDriver
	}
	return result
}

func getVehiclesForIB(equipmentList []app.EquipmentListRecord) []*fleetmodel.Vehicle {
	result := make([]*fleetmodel.Vehicle, len(equipmentList))
	for i, equipment := range equipmentList {
		vehicle := &fleetmodel.Vehicle{
			Vin: equipment.VIN,
		}
		if equipment.StatedValue != nil {
			vehicle.StatedValue = equipment.StatedValue
		}
		result[i] = vehicle
	}
	return result
}

func getLossHistoryForIB(
	lossRunSummary []app.LossRunSummaryPerCoverage, largeLosses []app.LargeLoss,
) *fleetmodel.LossHistory {
	lossSummaries := make(map[string]*fleetmodel.LossSummaryRecords)
	for _, coverageSummary := range lossRunSummary {
		coverageID := coverageSummary.CoverageType.String()
		var lossSummaryRecords []*fleetmodel.LossSummary
		for _, record := range coverageSummary.Summary {

			lossSummary := &fleetmodel.LossSummary{
				PolicyInterval: &proto.Interval{
					Start: timestamppb.New(record.PolicyPeriodStartDate),
					End:   timestamppb.New(record.PolicyPeriodEndDate),
				},
				NumberOfPowerUnits: record.NumberOfPowerUnits,
				NumberOfClaims:     record.NumberOfClaims,
				LossIncurred:       record.LossIncurred,
				IsNirvanaPeriod:    record.IsNirvanaPeriod,
			}

			lossSummaryRecords = append(lossSummaryRecords, lossSummary)
		}
		lossSummaries[coverageID] = &fleetmodel.LossSummaryRecords{
			LossSummaries: lossSummaryRecords,
		}
	}

	var fleetLargeLosses []*fleetmodel.LargeLoss
	for _, largeLoss := range largeLosses {
		fleetLargeLoss := &fleetmodel.LargeLoss{
			LossDate:     timestamppb.New(largeLoss.Date),
			CoverageID:   largeLoss.CoverageType.String(),
			LossIncurred: largeLoss.LossIncurred,
			Description:  largeLoss.Description,
		}
		fleetLargeLosses = append(fleetLargeLosses, fleetLargeLoss)
	}

	return &fleetmodel.LossHistory{
		LossSummaries: lossSummaries,
		LargeLosses:   fleetLargeLosses,
	}
}

func getNegotiatedRatesForIB(negotiatedRates *app.NegotiatedRates) (*fleetmodel.NegotiatedRates, error) {
	if negotiatedRates == nil {
		return nil, nil // nolint:nilnil
	}

	coverageIDs := slice_utils.ToString(negotiatedRates.Coverages)

	// Convert rules
	rules := make([]*fleetmodel.NegotiatedRatesRule, 0, len(negotiatedRates.Rules))
	for _, rule := range negotiatedRates.Rules {
		ruleType, err := appenums.TransformApplicationNegotiatedRatesExemptionToProto(rule.RuleType)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to transform rule type %s to proto", rule.RuleType)
		}
		rules = append(rules, &fleetmodel.NegotiatedRatesRule{
			RuleType:     ruleType,
			IsApplicable: rule.IsApplicable,
		})
	}

	// Determine exemption - use details exemption if available, otherwise use unspecified
	exemption := fleetmodel.NegotiatedRatesExemption_NEGOTIATED_RATES_EXEMPTION_UNSPECIFIED
	if negotiatedRates.Details != nil {
		var err error
		exemption, err = appenums.TransformApplicationNegotiatedRatesExemptionToProto(negotiatedRates.Details.Exemption)
		if err != nil {
			return nil, errors.Wrapf(
				err, "failed to transform exemption %s to proto", negotiatedRates.Details.Exemption,
			)
		}
	}

	// Build the fleet model negotiated rates structure
	fleetNegotiatedRates := &fleetmodel.NegotiatedRates{
		IsApplicable:     negotiatedRates.IsNegotiatedRatesApplicable,
		IsApplied:        negotiatedRates.IsNegotiatedRatesApplied,
		BaseLimitPremium: negotiatedRates.BaseLimitPremium,
		ThresholdPremium: negotiatedRates.ThresholdPremium,
		CoverageIDs:      coverageIDs,
		Rules:            rules,
		Exemption:        exemption,
	}

	// Set rate details if available
	if negotiatedRates.Details != nil {
		fleetNegotiatedRates.AlNegotiatedRate = negotiatedRates.Details.AlNegotiatedRate
		fleetNegotiatedRates.AlTraditionalRate = negotiatedRates.Details.AlTraditionalRate
		fleetNegotiatedRates.ApdNegotiatedRate = negotiatedRates.Details.ApdNegotiatedRate
		fleetNegotiatedRates.ApdTraditionalRate = negotiatedRates.Details.ApdTraditionalRate
	}

	return fleetNegotiatedRates, nil
}

func getBrokerLicenseForIB(
	ctx context.Context, appID string, entityLicense *entity_license.EntityLicense,
) (*fleetmodel.BrokerLicense, error) {
	if entityLicense == nil {
		log.Info(ctx, "entity license is nil", log.String("appID", appID))
		return nil, nil // nolint:nilnil
	}

	entityType, err := entity_license_enums.TransformEntityTypeToLicenseHolderType(entityLicense.EntityType)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to transform entity type %s to license holder type", entityLicense.EntityType,
		)
	}

	return &fleetmodel.BrokerLicense{
		Id:                entityLicense.EntityID.String(),
		LicenseHolderType: entityType,
		LicenseNumber:     entityLicense.LicenseNumber,
		Address:           getProtoAddressFromAgencyAddress(entityLicense.BusinessAddress),
		LicenseEffectiveInterval: &proto.Interval{
			Start: timestamppb.New(*entityLicense.EffectiveDate),
			End:   timestamppb.New(*entityLicense.ExpirationDate),
		},
	}, nil
}

func getProtoAddressFromAgencyAddress(address *agency.Address) *proto.Address {
	if address == nil {
		return nil
	}

	return &proto.Address{
		Nation:  pointer_utils.ToPointer(us_states.DefaultCountry),
		State:   pointer_utils.ToPointer(address.State),
		City:    pointer_utils.ToPointer(address.City),
		Street:  pointer_utils.ToPointer(address.Street1),
		Street2: address.Street2,
		ZipCode: pointer_utils.ToPointer(address.Zip),
	}
}

func getCameraSubsidyForIB(cameraSubsidyDetails *uw.CameraSubsidyDetails) *fleetmodel.CameraSubsidy {
	if cameraSubsidyDetails == nil {
		return nil
	}
	return &fleetmodel.CameraSubsidy{
		NumberOfCameras: int32(cameraSubsidyDetails.NumberOfCameras),
		SubsidyAmount:   cameraSubsidyDetails.SubsidyAmount,
		CameraProvider:  cameraSubsidyDetails.CameraProvider.String(),
	}
}

func getBillingForIB(billingInfo *billinginfo.Info) (*fleetmodel.Billing, error) {
	paymentMethod, err := billing_enums.TransformBillingPaymentMethodToProto(billingInfo.PaymentMethod)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to transform payment method %s to proto", billingInfo.PaymentMethod,
		)
	}
	depositPaymentMethod, err := billing_enums.TransformBillingDepositPaymentMethodToProto(
		billingInfo.DepositPaymentMethod,
	)
	if err != nil {
		return nil, errors.Wrapf(
			err, "failed to transform deposit payment method %s to proto", billingInfo.DepositPaymentMethod,
		)
	}

	return &fleetmodel.Billing{
		DepositAmount:           billingInfo.DepositAmount,
		AgencyCommissionRate:    billingInfo.CommissionRate,
		PaymentFrequency:        paymentMethod,
		DepositPaymentMethod:    depositPaymentMethod,
		AllowedCollateralAmount: billingInfo.CollateralAmount,
	}, nil
}

func getScheduleModsForIB(rateMLInputs app.RateMLUnderwritingEntityInputs) *fleetmodel.ScheduleModification {
	return &fleetmodel.ScheduleModification{
		SafetyModAllCoverages: rateMLInputs.SafetyModAllCov,
		AlCredit:              rateMLInputs.AlCredit,
		ApdCredit:             rateMLInputs.ApdCredit,
		MtcCredit:             rateMLInputs.MtcCredit,
	}
}

func getClausesForPolicy(coverageInfo *app.CoverageInfo) *insurancecoreproto.ClauseList {
	clauses := insurancecoreproto.ClauseList{}
	for _, cov := range coverageInfo.Coverages {
		switch cov.CoverageType { //nolint:exhaustive
		case appenums.CoverageBlanketWaiverOfSubrogation:
			clauses.Clauses = append(
				clauses.Clauses, getInsuranceClause(insurancecoreproto.ClauseType_ClauseTypeWaiverOfSubrogation),
			)
		case appenums.CoverageBlanketAdditional:
			clauses.Clauses = append(
				clauses.Clauses, getInsuranceClause(insurancecoreproto.ClauseType_ClauseTypeAdditionalInsured),
			)
		case appenums.CoverageHiredAuto:
			clauses.Clauses = append(
				clauses.Clauses,
				getInsuranceClause(insurancecoreproto.ClauseType_ClauseTypeUIIA),
			)
		}
	}
	return &clauses
}

func getInsuranceClause(clauseType insurancecoreproto.ClauseType) *insurancecoreproto.Clause {
	return &insurancecoreproto.Clause{
		Id: &insurancecoreproto.ClauseId{
			Id: clauseType.String(),
		},
		Type: clauseType,
		ParticipantScope: &insurancecoreproto.ParticipantScope{
			Type: insurancecoreproto.ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeBlanket,
		},
	}
}

func getCommodityDetailsForIB(subObj *app.SubmissionObject) (*fleetmodel.CommodityDetails, error) {
	cd := &fleetmodel.CommodityDetails{}

	// Additional Commodity Type
	if subObj.AdditionalCommodityInfo != nil && len(subObj.AdditionalCommodityInfo.Commodities) > 0 {
		convertedAddlCommodityType, err := appenums.TransformApplicationAdditionalInformationCommodityToProto(
			subObj.AdditionalCommodityInfo.Commodities[0],
		)
		if err != nil {
			return nil, errors.Wrapf(
				err, "failed to transform additional commodity type %s",
				subObj.AdditionalCommodityInfo.Commodities[0],
			)
		}
		cd.AdditionalCommodityType = convertedAddlCommodityType
	}

	// Primary Commodity Category
	if subObj.EquipmentInfo.PrimaryCategory != nil {
		convertedPrimaryCategory, err := appenums.TransformApplicationCommodityCategoryToProto(
			*subObj.EquipmentInfo.PrimaryCategory,
		)
		if err != nil {
			return nil, errors.Wrapf(
				err, "failed to transform primary commodity category %s",
				*subObj.EquipmentInfo.PrimaryCategory,
			)
		}
		cd.PrimaryCommodityCategory = pointer_utils.ToPointer(convertedPrimaryCategory)
	}

	if subObj.EquipmentInfo.PrimaryCommodity != nil {
		pc := *subObj.EquipmentInfo.PrimaryCommodity
		primaryCommodity, err := old_enums.TransformPrimaryCommodityHauledToProto(pc)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to transform primary commodity hauled %s", pc)
		}
		cd.PrimaryCommodity = pointer_utils.ToPointer(primaryCommodity)
	}

	// Commodity Distribution
	var err error
	cd.CommodityDistribution, err = getCommodityDistributionForIB(subObj.EquipmentInfo.CommodityDistribution)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get commodity distribution for IB")
	}

	return cd, nil
}

func getCommodityDistributionForIB(
	commodityDistribution *app.CommodityDistribution,
) (*fleetmodel.CommodityDistribution, error) {
	if commodityDistribution == nil {
		return nil, nil //nolint:nilnil
	}

	// Convert commodities
	commodities := make([]*fleetmodel.WeightedCommodityRecord, 0, len(commodityDistribution.Commodities))
	for _, commodity := range commodityDistribution.Commodities {
		convertedConvertedCategory, err := appenums.TransformApplicationCommodityCategoryToProto(commodity.Category)
		if err != nil {
			return nil, errors.Wrapf(
				err, "failed to transform commodity category %s", commodity.Category,
			)
		}
		convertedCommodityRecord := &fleetmodel.WeightedCommodityRecord{
			CommodityCategory:         convertedConvertedCategory,
			PercentageOfHauls:         commodity.PercentageOfHauls,
			AverageDollarValuePerHaul: commodity.AvgDollarValueHauled,
			MaximumDollarValuePerHaul: commodity.MaxDollarValueHauled,
			Commodity: &fleetmodel.Commodity{
				Label: commodity.Commodity.Label,
			},
		}
		if commodity.Commodity.Type != nil {
			convertedCommodityName, err := appenums.TransformApplicationCommodityHauledToProto(
				*commodity.Commodity.Type,
			)
			if err != nil {
				return nil, errors.Wrapf(
					err, "failed to transform commodity type %s", *commodity.Commodity.Type,
				)
			}
			convertedCommodityRecord.Commodity.CommodityName = pointer_utils.ToPointer(convertedCommodityName)
		}
		commodities = append(commodities, convertedCommodityRecord)
	}

	// Convert additional commodities
	var additionalCommodities *fleetmodel.AdditionalCommodities
	if commodityDistribution.AdditionalCommodities != nil {
		additionalCommodities = &fleetmodel.AdditionalCommodities{
			PercentageOfHauls: commodityDistribution.AdditionalCommodities.PercentageOfHauls,
			Commodities:       commodityDistribution.AdditionalCommodities.Commodities,
		}
	}

	return &fleetmodel.CommodityDistribution{
		WeightedCommodityRecords: commodities,
		AdditionalCommodities:    additionalCommodities,
	}, nil
}
