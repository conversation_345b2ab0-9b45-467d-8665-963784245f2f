package app_state_machine

import (
	"context"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	authutil "nirvanatech.com/nirvana/common-go/auth-util"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/events/underwriter_events/assignment_events"
	event_enums "nirvanatech.com/nirvana/external_client/salesforce/jobs/enums"
	"nirvanatech.com/nirvana/external_client/salesforce/wrapper"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/jtypes"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	"nirvanatech.com/nirvana/quoting/app_state_machine/cerrors"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	app_chkr "nirvanatech.com/nirvana/quoting/appetite_checker"
	quoting_jobs "nirvanatech.com/nirvana/quoting/jobs"
	"nirvanatech.com/nirvana/quoting/utils"
	"nirvanatech.com/nirvana/underwriting/jobs"
	risk_factors_client "nirvanatech.com/nirvana/underwriting/risk_factors"
	"nirvanatech.com/nirvana/underwriting/scheduler"
	tsm "nirvanatech.com/nirvana/underwriting/state_machine/telematics_connection_state_machine"
)

type AppStateIndicationGenerated struct {
	appStateMachineHelper
	machine AppStateMachine
}

func newAppStateIndicationGenerated(
	deps ASMDeps, appId string,
) ApplicationState {
	return &AppStateIndicationGenerated{
		appStateMachineHelper: appStateMachineHelper{
			appId:    appId,
			deps:     deps,
			appState: state_enums.AppStateIndicationGenerated,
		},
	}
}

func (s *AppStateIndicationGenerated) CreateRenewalApplication(ctx context.Context, appId string) (*string, *app_chkr.AppetiteCheckerResult, error) {
	return nil, nil, errors.Wrapf(cerrors.ErrActionNotSupported,
		"Cannot set create renewal application from indication generated state for app %s", appId)
}

func (s *AppStateIndicationGenerated) CreateRenewalApplicationV2(ctx context.Context, appId string) (*string, *ApplicationEvaluation, error) {
	return nil, nil, errors.Wrapf(cerrors.ErrActionNotSupported,
		"Cannot set create renewal application from indication generated state for app %s", appId)
}

func (s *AppStateIndicationGenerated) UpdateRenewalFields(ctx context.Context, form *oapi_app.PatchRenewalApplicationForm) error {
	return errors.Wrap(cerrors.ErrActionNotSupported,
		"Cannot update renewal application from IndicationGenerated state for app")
}

func (s *AppStateIndicationGenerated) SetPanic(ctx context.Context, metadata app.StateMetadata) error {
	metadata.PreviousState = state_enums.AppStateIndicationGenerated
	err := s.setPanic(ctx, metadata)
	if err != nil {
		return errors.Wrapf(err, "failed to set panic for app %s from state %s", s.appId, s.appState)
	}
	return nil
}

func (s *AppStateIndicationGenerated) SetDeclined(
	ctx context.Context,
	metadata app.StateMetadata,
) error {
	metadata.PreviousState = state_enums.AppStateIndicationGenerated
	err := s.setDeclined(ctx, metadata)
	if err != nil {
		return errors.Wrapf(err, "failed to set declined for app %s from state %s", s.appId, s.appState)
	}
	return nil
}

func (s *AppStateIndicationGenerated) Rollback(
	ctx context.Context,
	metadata app.StateMetadata,
) (*app.StateMetadata, error) {
	return nil, errors.Wrapf(
		cerrors.ErrActionNotSupported,
		"Cannot rollback from IndicationGenerated state for app %s from state %s", s.appId, s.appState)
}

func (s *AppStateIndicationGenerated) GetApplicationDetails(ctx context.Context) (
	*oapi_app.ApplicationDetail, error,
) {
	appObj, err := s.loadApp(ctx)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to load app %s", s.appId)
	}
	appDetails, err := s.bindAppDetailsFromDbToRest(ctx, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to bind app details from db to REST for app %s from state %s",
			s.appId, s.appState)
	}
	appSummaryState, err := s.getApplicationSummaryState(ctx, *appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get application summary state for app %s from state %s",
			s.appId, s.appState)
	}
	appDetails.Summary.State = *appSummaryState
	return appDetails, nil
}

func (s *AppStateIndicationGenerated) getApplicationSummaryState(
	ctx context.Context,
	appObj app.Application,
) (*oapi_app.ApplicationState, error) {
	// IndicationSubmissionID should be valid in this state
	sub, err := s.loadSub(ctx, appObj.IndicationSubmissionID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to load submission for id %s", appObj.IndicationSubmissionID)
	}
	var isUWSubJobRunning bool
	if appObj.UwSubmissionID != nil {
		jobRun, err := s.getJobStatus(ctx, appObj.UwSubmissionID)
		if err != nil {
			return nil, errors.Wrapf(
				err, "failed to get jobRunStatus for submission with id %s", appObj.UwSubmissionID,
			)
		}
		isUWSubJobRunning = !jobRun.HasTerminalStatus()
	}

	switch {
	// We want to return ensure that agents don't see the in-progress screen. This helps in reducing duplicate
	// resubmissions because of incorrect status being shown to the agents.
	case isUWSubJobRunning:
		return pointer_utils.ToPointer(oapi_app.ApplicationStatePendingELDTelematics), nil
	case sub.SelectedIndicationID != uuid.Nil.String():
		return pointer_utils.ToPointer(oapi_app.ApplicationStateIndicationSelected), nil
	default:
		return pointer_utils.ToPointer(oapi_app.ApplicationStateIndicationGenerated), nil
	}
}

func (s *AppStateIndicationGenerated) GetApplicationSummary(
	ctx context.Context,
	appObj app.Application,
) (*oapi_app.ApplicationSummary, error) {
	appSummary, err := s.bindApplicationSummaryFromDbToRest(ctx, &appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to bind application summary from db to REST for app %s from state %s",
			s.appId, s.appState)
	}
	appSummaryState, err := s.getApplicationSummaryState(ctx, appObj)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get application summary state for app %s from state %s",
			s.appId, s.appState)
	}
	appSummary.State = *appSummaryState
	return appSummary, nil
}

func (s *AppStateIndicationGenerated) GetStateMetadata(ctx context.Context) (*app.StateMetadata, error) {
	return nil, errors.Wrapf(cerrors.ErrActionNotSupported, "Cannot get state metadata for app %s", s.appId)
}

func (s *AppStateIndicationGenerated) GetIndicationOptions(ctx context.Context) (*oapi_app.IndicationOptions, error) {
	indication, err := s.getIndicationOptions(ctx)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get indication options for app %s from state %s",
			s.appId, s.appState)
	}
	return indication, nil
}

func (s *AppStateIndicationGenerated) UpdateIndicationForm(
	ctx context.Context,
	form *oapi_app.IndicationForm,
) error {
	expectedState := state_enums.AppStateIndicationGenerated
	// Unconditionally move to Unsubmitted
	err := s.updateIndicationFormAndMoveToUnsubmitted(ctx, expectedState, form, nil)
	if err != nil {
		return errors.Wrapf(err, "failed to update indication form for app %s from state %s",
			s.appId, s.appState)
	}
	return nil
}

func (s *AppStateIndicationGenerated) UpdateBasicInfoFields(
	ctx context.Context,
	basicInfo *oapi_app.ApplicationBasicInfoForm,
) error {
	err := s.updateApplicationBasicInfoFields(ctx,
		state_enums.AppStateIndicationGenerated,
		basicInfo, nil)
	if err != nil {
		return errors.Wrapf(err, "failed to update basic info fields for app %s from state %s",
			s.appId, s.appState)
	}
	return nil
}

func (s *AppStateIndicationGenerated) BeginIndicationGeneration(ctx context.Context) (string, error) {
	return "", errors.Wrapf(cerrors.ErrActionNotValid,
		"Cannot begin indication generation in IndicationGenerated state for app %s from state %s",
		s.appId, s.appState)
}

func (s *AppStateIndicationGenerated) SetIndication(ctx context.Context, subId string) error {
	return errors.Wrapf(
		cerrors.ErrActionNotValid,
		"Cannot set indication in IndicationGenerated state for app %s from state %s", s.appId, s.appState)
}

func (s *AppStateIndicationGenerated) FinalizeIndicationOption(ctx context.Context, indicationOptionId string) error {
	expectedState := state_enums.AppStateIndicationGenerated
	err := s.finalizeIndicationOptionAndMoveToIndicationGenerated(
		ctx,
		expectedState,
		indicationOptionId,
		nil,
	)
	if err != nil {
		return errors.Wrapf(
			err, "failed to finalize indication option for app %s from state %s", s.appId, s.appState)
	}
	return nil
}

func (s *AppStateIndicationGenerated) UpdateAdditionalInfoForm(
	ctx context.Context, form *oapi_app.AdditionalInformationForm,
) error {
	expectedState := state_enums.AppStateIndicationGenerated
	err := s.updateAddlInfoFormAndMoveToIndicationGenerated(ctx,
		expectedState,
		form,
		nil,
	)
	if err != nil {
		return errors.Wrapf(err, "failed to update additional info form for app %s from state %s",
			s.appId, s.appState)
	}
	return nil
}

func (s *AppStateIndicationGenerated) GetQuote(ctx context.Context) (*oapi_app.QuoteDetails, error) {
	return nil, errors.Wrapf(cerrors.ErrActionNotSupported,
		"Cannot get quote from Indication Generated state for app %s from state %s", s.appId, s.appState)
}

func (s *AppStateIndicationGenerated) BeginQuoteGeneration(ctx context.Context) (string, error) {
	expectedState := state_enums.AppStateIndicationGenerated
	subId, err := s.beginQuoteGeneration(ctx, expectedState, func(app app.Application) error {
		switch {
		case app.IndicationSubmissionID == nil:
			return errors.Newf("missing indication submission id for app %s", s.appId)
		case app.PackageType == nil:
			return errors.Newf("missing package type for app %s", s.appId)
		}
		return nil
	}, notBindable, nil)
	if err != nil {
		return "", errors.Wrapf(err, "failed to begin quote generation for app %s from state %s",
			s.appId, s.appState)
	}
	return subId, nil
}

func (s *AppStateIndicationGenerated) BeginRenewalQuoteGeneration(ctx context.Context) (string, error) {
	return "", errors.Wrap(cerrors.ErrActionNotSupported, "Cannot begin renewal quote generation from indication generated state")
}

func (s *AppStateIndicationGenerated) SetQuote(ctx context.Context, subId string, negotiatedRates *app.NegotiatedRates) error {
	application, err := s.loadApp(ctx)
	if err != nil {
		return errors.Wrapf(err, "unable to load app %s", s.appId)
	}
	latestPendingAppReview, err := s.deps.AppReviewWrapper.GetLatestPendingReview(ctx, application.ID)
	if err != nil && !errors.Is(err, uw.ErrAppReviewNotFound) {
		return errors.Wrapf(err, "unable to get latest pending app review by app id %s", application.ID)
	}
	err = s.MarkPendingAppReviewsToStale(ctx, application.ID)
	if err != nil {
		return errors.Wrapf(err, "unable to mark pending app reviews for app id %s", application.ID)
	}
	sub, err := s.deps.AppWrapper.GetSubmissionById(ctx, subId)
	if err != nil {
		return errors.Wrapf(err, "unable to get submission by id %s", subId)
	}

	var underwriterID uuid.UUID
	if sub.UnderwriterID == uuid.Nil {
		underwriter, err := s.deps.UwScheduler.GetAssignableUnderwriter(ctx, scheduler.UWAssignmentParams{
			AppID:         application.ID,
			AgencyID:      application.AgencyID,
			DotNumber:     application.CompanyInfo.DOTNumber,
			EffectiveDate: application.CoverageInfo.EffectiveDate,
			ProgramType:   enums.ProgramTypeFleet,
			PUCount:       application.CompanyInfo.NumberOfPowerUnits,
		})
		if err != nil {
			return errors.Wrapf(err, "unable to get assignable underwriter for app %s", s.appId)
		}
		underwriterID = underwriter.ID
	} else {
		underwriterID = sub.UnderwriterID
	}

	if negotiatedRates == nil {
		return errors.New("negotiated rates were found nil")
	}
	if sub.CoverageInfo == nil {
		return errors.New("coverage info was found nil")
	}

	var agentUser, underwriterUser *authz.User
	agentUser, err = authutil.GetUserFromUserID(ctx, s.deps.AuthWrapper, application.MarketerID.String())
	if err != nil {
		return errors.Wrapf(err, "failed to get dev user for app Id: %s", s.appId)
	}
	underwriterUser, err = authutil.GetUserFromUserID(ctx, s.deps.AuthWrapper, underwriterID.String())
	if err != nil {
		return errors.Wrapf(err, "failed to get underwriter user for app Id: %s", s.appId)
	}
	shouldCreateOverview, err := shouldCreateOverviewPanel(ctx, s.deps.FeatureFlag, agentUser, underwriterUser)
	if err != nil {
		return errors.Wrapf(err, "unable to determine if overview panel should be created for app Id: %s", s.appId)
	}

	appetiteFactorsLogicResolver, err := s.deps.AppetiteFactorsLogicResolverFactory.GetLogicResolver(enums.ProgramTypeFleet)
	if err != nil {
		return errors.Wrapf(err, "unable to create logic resolver for app Id: %s", s.appId)
	}

	appetiteScoreVersion, err := getAppetiteScoreVersion(ctx, s.deps.FeatureFlag, appetiteFactorsLogicResolver, application.CompanyInfo.DOTNumber, s.deps.UWSafetyFetcher, agentUser)
	if err != nil {
		return errors.Wrapf(err, "unable to get appetite score version for app Id: %s", s.appId)
	}

	telematicsConnectionState, err := tsm.CalculateInitialTelematicsConnectionState(ctx, s.deps.AppWrapper, *s.deps.TspConnManager, s.deps.TelematicsDataPlatformClient, s.deps.Jobber, s.deps.AppReviewWrapper, application.ID)
	if err != nil {
		return errors.Wrapf(err, "unable to calculate initial telematics connection state for app id %s", application.ID)
	}

	review := uw.NewAppReviewFromSubmission(
		*sub, negotiatedRates, underwriterID, uuid.New().String(), time.Now(), shouldCreateOverview, *appetiteScoreVersion,
		uw.AppReviewReadinessStateNotReady, *telematicsConnectionState,
	)
	err = s.deps.AppReviewWrapper.CreateReview(ctx, review)
	if err != nil {
		return errors.Wrapf(err, "unable to create review for app id %s", application.ID)
	}

	// Assign quoting experiments on review creation
	appId, err := uuid.Parse(application.ID)
	if err != nil {
		return errors.Wrapf(err, "unable to parse app id %s", application.ID)
	}

	err = s.deps.QuotingExperimentsManager.AssignExperiments(
		ctx,
		appId,
	)
	if err != nil {
		return errors.Wrapf(err, "unable to assign pricing experiments for app id %s", application.ID)
	}

	if latestPendingAppReview != nil {
		err = s.deps.AppReviewWrapper.UpdateAppReview(ctx, review.Id,
			func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
				err = s.copyReviewDataFromLatestPendingAppReview(ctx, &review, latestPendingAppReview)
				if err != nil {
					return review, errors.Wrapf(err, "error while copying review data from latest pending app review, latestPendingAppReview ID: %s", latestPendingAppReview.Id)
				}
				return review, nil
			})
		if err != nil {
			return errors.Wrapf(err, "unable to update app review for app id %s and review id %s",
				application.ID, review.Id)
		}

		// keeping it out of copyReviewDataFromLatestPendingAppReview to avoid nested transaction
		if err = s.copyReviewNotes(ctx, latestPendingAppReview.Id, review.Id); err != nil {
			return errors.Wrapf(err, "unabled to copy review notes, app id %s and review id %s",
				application.ID, review.Id)
		}
	}

	if underwriterUser != nil {
		if err = utils.TriggerUpdateSalesforceOpportunity(
			ctx,
			s.deps.Jobber,
			s.deps.MetricsClient,
			wrapper.SalesforceEventUpdateApplicationArgs{
				ApplicationID:    application.ID,
				UnderwriterEmail: underwriterUser.Email,
				EventName:        event_enums.UnderwriterApplicationReviewAssigned,
			}); err != nil {
			// just log the error
			log.Error(
				ctx, "failed to update salesforce opportunity",
				log.String("eventName", event_enums.UnderwriterApplicationReviewAssigned.String()),
				log.Err(err),
			)
		}
	}

	// Creating Event for Underwriter Assigned
	event, err := assignment_events.NewUWAppReviewAssigned(ctx, s.deps.AppReviewWrapper, s.deps.AuthWrapper, review.Id)
	if err != nil {
		log.Warn(ctx, "unable to create underwriter assigned event", log.Err(err), log.String("review_id", review.Id))
	} else if err := s.deps.EventHandler.UploadEvent(ctx, event); err != nil {
		log.Warn(ctx, "unable to upload event", log.String("event", event.UserVisibleName()), log.Err(err))
	}

	expectedStates := map[state_enums.AppState]struct{}{
		state_enums.AppStateIndicationGenerated: {},
		state_enums.AppStateUnderUWReview:       {},
	}
	nextState := state_enums.AppStateUnderUWReview
	err = s.deps.AppWrapper.UpdateApp(ctx, s.appId, func(app app.Application) (app.Application, error) {
		// Check if submission is still valid/the latest submission for
		// application
		_, ok := expectedStates[app.State]
		switch {
		case !ok:
			return app, errors.Wrapf(cerrors.ErrActionNotValid, "Unexpected state %s. Expected some of %s",
				app.State, expectedStates)
		case *app.UwSubmissionID != subId:
			return app, errors.Newf(
				cerrors.SubNoLongerValid+
					": Submission %s is no longer valid, since it is not the latest submission %s for application %s",
				subId,
				*app.UwSubmissionID,
				app.ID)
		}
		app.State = nextState
		return app, nil
	})
	if err != nil {
		return errors.Wrapf(err, cerrors.DbPersistingErr+": Unable to update app %s to state %s", s.appId, nextState)
	}

	// Trigger CheckClearance job
	addJobRunParams := jobber.NewAddJobRunParams(
		quoting_jobs.CheckClearance,
		&quoting_jobs.CheckClearanceArgs{ApplicationID: s.appId},
		jtypes.NewMetadata(jtypes.Immediate),
	)
	jobRunId, err := s.deps.Jobber.AddJobRun(ctx, addJobRunParams)
	if err != nil {
		log.Error(ctx, "failed to add CheckClearance job", log.Err(err),
			log.Any("addJobRunParams", addJobRunParams),
		)
		return errors.Wrap(err, "failed to add CheckClearance job")
	}
	log.Info(ctx, "Added CheckClearance job", log.Stringer("JobRunId", jobRunId))
	// Trigger underwriting app-review widget generation job
	jobRunId, err = s.deps.Jobber.AddJobRun(ctx, jobber.NewAddJobRunParams(jobs.GenerateWidgetsForAppReview,
		&jobs.GenerateAppReviewWidgetsArgs{AppReviewId: review.Id}, jtypes.NewMetadata(jtypes.Immediate)))
	if err != nil {
		log.Error(ctx, "unable to add generate widgets job", log.Err(err))
		return nil
	}
	log.Info(ctx, "generate widgets job added", log.String("job_run_id", jobRunId.String()))
	// Persist job run id in panels info
	err = s.deps.AppReviewWrapper.UpdateAppReview(ctx, review.Id,
		func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
			panelsInfo := review.PanelsInfo
			if panelsInfo == nil {
				panelsInfo = new(uw.PanelsInfo)
			}
			panelsInfo.JobRunId = &jobRunId
			review.PanelsInfo = panelsInfo
			return review, nil
		})
	if err != nil {
		return errors.Wrapf(err, "unable to update app review for app id %s and review id %s",
			application.ID, review.Id)
	}

	err = s.deps.Jobber.WaitForJobRunCompletion(ctx, jobRunId)
	if err != nil {
		return errors.Wrapf(err, "failed to wait for job run completion for job run id %s", jobRunId)
	}
	err = s.createTasksAndUpdateReviewReadinessState(ctx, &review, latestPendingAppReview)
	if err != nil {
		return errors.Wrapf(err, "unable to create tasks and update review readiness state for app id %s", application.ID)
	}
	// Trigger worksheet creation calls
	worksheetID, err := s.deps.RiskFactorsClient.CreateWorksheet(ctx,
		&risk_factors_client.CreateWorksheetRequest{ReviewId: review.Id})
	if err != nil {
		log.Error(ctx, "failed to create worksheet", log.Err(err))
		return errors.Wrapf(err, "failed to create worksheet for app %s", s.appId)
	}
	log.Info(ctx, "Created worksheet", log.String("worksheetID", worksheetID.WorksheetId),
		log.String("underwriter name", underwriterUser.FullName()))

	return nil
}

func (s *AppStateIndicationGenerated) FinalizeQuote(ctx context.Context) (*[]oapi_app.PolicyDetails, error) {
	return nil, errors.Wrapf(
		cerrors.ErrActionNotValid,
		"Cannot finalize quote from Indication Generate state for app %s from state %s", s.appId, s.appState)
}

func (s *AppStateIndicationGenerated) ReleaseQuote(ctx context.Context, compilationID uuid.UUID) error {
	return errors.Wrapf(
		cerrors.ErrActionNotValid,
		"Cannot release quote from Indication Generate state for app %s from state %s", s.appId, s.appState)
}

func (s *AppStateIndicationGenerated) State() state_enums.AppState {
	return state_enums.AppStateIndicationGenerated
}

func (s *AppStateIndicationGenerated) GetStateInOAPIFormat(
	ctx context.Context,
	appObj app.Application,
) (*oapi_app.ApplicationState, error) {
	return s.getApplicationSummaryState(ctx, appObj)
}

var _ ApplicationState = &AppStateIndicationGenerated{}
