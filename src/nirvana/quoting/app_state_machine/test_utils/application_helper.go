package test_utils

import (
	"context"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	openapi_types "github.com/oapi-codegen/runtime/types"
	"github.com/stretchr/testify/suite"

	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/random_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	"nirvanatech.com/nirvana/policy_common/constants"
	"nirvanatech.com/nirvana/rating/models/models_release"
	"nirvanatech.com/nirvana/rating/rtypes"
	"nirvanatech.com/nirvana/rating/utils"
)

type AppStateMachineHelper struct{}

type BasicAppData struct {
	DotNumber     int64
	companyName   string
	effectiveDate time.Time
	PUCount       int
}

func (a *AppStateMachineHelper) MockStateMetadata() *app.StateMetadata {
	return &app.StateMetadata{
		Description: "I am a fake description",
		Time:        Now,
	}
}

func enableVehiclesService(ctx context.Context, userID uuid.UUID, ffClient feature_flag_lib.Client, authWrapper auth.DataWrapper) (bool, error) {
	authzUser, err := authWrapper.FetchAuthzUser(ctx, userID)
	if err != nil {
		return false, errors.Wrapf(err, "failed to fetch user %s", userID)
	}
	useVehiclesService, err := ffClient.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(*authzUser),
		feature_flag_lib.FeatureVehiclesServiceVINDecoder,
		false)
	if err != nil {
		return false, errors.Wrapf(err, "failed to get vehicles service feature flag for user %s", authzUser.Email)
	}
	return useVehiclesService, nil
}

// This function populates a new app with the information from the
// appetite check.
// TODO: Should remove this due to duplication in
// api-server/handlers/application/application_helper.go
func (a *AppStateMachineHelper) populateNewApplicationWithBasicInfo(
	ctx context.Context,
	deps *asmDeps, appObj *app.Application, basicAppData BasicAppData, users mockUsers,
) (*app.Application, error) {
	companyInfo := app.NewCompanyInfo()
	companyInfo.DOTNumber = basicAppData.DotNumber
	companyInfo.Name = basicAppData.companyName
	companyInfo.NumberOfPowerUnits = basicAppData.PUCount
	appObj.CompanyInfo = companyInfo
	equipmentInfo := app.NewEquipmentInfo()
	appObj.EquipmentInfo = equipmentInfo
	lossInfo := app.NewLossInfo()
	appObj.LossInfo = lossInfo
	coverageInfo := app.NewCoverageInfo()
	coverageInfo.EffectiveDate = basicAppData.effectiveDate
	appObj.CoverageInfo = coverageInfo

	// Set UW and creator
	appObj.AgencyID = users.DefaultAgencyId
	appObj.UnderwriterID = users.DefaultUwId
	appObj.CreatedBy = users.DefaultUserId.String()
	appObj.MarketerID = &users.DefaultUserId

	dotDetails, err := deps.FmcsaWrapper.GetDetailsByDot(ctx, basicAppData.DotNumber)
	if err != nil {
		return nil, errors.Wrapf(err, "Unable to get fmcsa dot details for dotNumber %d", basicAppData.DotNumber)
	}
	census := dotDetails.Census
	if census.PhysicalAddressState == nil {
		return nil, errors.New("census data missing physical address state")
	}

	// Assign default underwriter
	uw, err := deps.UwScheduler.GetFirstUW(ctx)
	if err != nil {
		return nil, err
	}
	appObj.UnderwriterID = uw.ID

	usState, err := us_states.StrToUSState(*census.PhysicalAddressState)
	if err != nil {
		return nil, errors.Newf("unable to parse app state %s to us state", *census.PhysicalAddressState)
	}
	appObj.CompanyInfo.USState = usState

	user := authz.UserFromContext(ctx)
	version, err := models_release.GetActiveVersion(
		ctx,
		deps.FeatureFlagClient,
		&user,
		rtypes.ProviderSentry,
		usState, coverageInfo.EffectiveDate,
	)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get the latest version")
	}
	useVehiclesService, err := enableVehiclesService(ctx, users.DefaultUserId, deps.FeatureFlagClient, deps.AuthWrapper)
	if err != nil {
		return nil, err
	}
	prefillAndAutoReviewFlagEnabled, err := deps.FeatureFlagClient.BoolVariation(
		// test util, just using the created user
		feature_flag_lib.BuildLookupAttributes(user),
		feature_flag_lib.FeaturePrefillAndAutoReviewVinProblems,
		false,
	)
	if err != nil {
		return nil, err
	}
	getDriverYoeFromAgentInputFlag, err := deps.FeatureFlagClient.BoolVariation(
		// test util, just using the created user
		feature_flag_lib.BuildLookupAttributes(user),
		feature_flag_lib.FeatureDriverYoeFromAgentInput,
		false,
	)
	if err != nil {
		return nil, err
	}

	ratingRecordDates := utils.NewDefaultRecordDatesV1()

	modelPinConfig := app.NewModelPinConfig(
		rtypes.ProviderSentry,
		usState,
		version,
		useVehiclesService,
		ratingRecordDates.RecordDate.Time,
		ratingRecordDates.DumpDate.Time,
		constants.InsuranceCarrierFalseLake,
		false,
		prefillAndAutoReviewFlagEnabled,
		getDriverYoeFromAgentInputFlag,
	)
	appObj.ModelPinConfig = modelPinConfig
	return appObj, nil
}

func (a *AppStateMachineHelper) MockBasicAppData() BasicAppData {
	return BasicAppData{
		DotNumber:     536917,
		companyName:   "SBS TRANSPORTATION INC",
		effectiveDate: Now,
	}
}

func (a *AppStateMachineHelper) MockBasicAppDataC(dotNumber int64) BasicAppData {
	d := a.MockBasicAppData()
	d.DotNumber = dotNumber
	return d
}

func (a *AppStateMachineHelper) MockBasicInfoFieldsData() oapi_app.ApplicationBasicInfoForm {
	email := openapi_types.Email(uuid.NewString())
	date := openapi_types.Date{time.Now()}
	return oapi_app.ApplicationBasicInfoForm{
		CompanyName:             pointer_utils.String(random_utils.GenerateRandomString(10)),
		EffectiveDateOfCoverage: &date,
		AgencyID:                pointer_utils.String(uuid.NewString()),
		ProducerID:              pointer_utils.String(uuid.NewString()),
		InsuredName:             pointer_utils.String(uuid.NewString()),
		InsuredEmail:            &email,
	}
}

func ValidateSummaryInfo(
	s *suite.Suite,
	basicAppData BasicAppData,
	summary oapi_app.ApplicationSummary,
) {
	s.Require().Equal(summary.DotNumber, basicAppData.DotNumber)
	s.Require().Equal(summary.CompanyName, basicAppData.companyName)
	s.Require().True(summary.EffectiveDate.Time.Equal(basicAppData.effectiveDate))
}

func ValidateIndicationFormInfo(
	s *suite.Suite,
	form *oapi_app.IndicationForm,
	details oapi_app.ApplicationDetail,
) {
	//// CoverageEnhancedPackageTowingLimit is not added as part of indication form
	//// We add it when APD is present. So we need to filter it out from details to match with form
	//covs := slices_util.Filter(
	//	*details.IndicationForm.OperationsForm.CoveragesRequired,
	//	func(cov oapi_app.CoverageRecord) bool {
	//		return !(cov.CoverageType == oapi_common.CoverageEnhancedPackageTowingLimit)
	//	},
	//)
	s.Require().Equal(
		details.IndicationForm.OperationsForm.CoveragesRequired,
		form.OperationsForm.CoveragesRequired)
	s.Require().Equal(
		details.IndicationForm.OperationsForm.NumberOfPowerUnits,
		form.OperationsForm.NumberOfPowerUnits)
	s.Require().Equal(
		details.IndicationForm.OperationsForm.EquipmentList,
		form.OperationsForm.EquipmentList)
	s.Require().Equal(
		details.IndicationForm.OperationsForm.RadiusOfOperation,
		form.OperationsForm.RadiusOfOperation)
	s.Require().Equal(
		details.IndicationForm.OperationsForm.ProjectedMileage,
		form.OperationsForm.ProjectedMileage)
	s.Require().Equal(
		details.IndicationForm.OperationsForm.ProducerId,
		form.OperationsForm.ProducerId)
	s.Require().Equal(
		details.IndicationForm.ClassesAndCommoditiesForm.PrimaryCategory.Type,
		form.ClassesAndCommoditiesForm.PrimaryCategory.Type)
	ValidateCommodityDistribution(
		s, details.IndicationForm.ClassesAndCommoditiesForm.CommodityDistribution,
		form.ClassesAndCommoditiesForm.CommodityDistribution)
	s.Require().Equal(
		details.IndicationForm.ClassesAndCommoditiesForm.PrimaryOperatingClass,
		form.ClassesAndCommoditiesForm.PrimaryOperatingClass)
	s.Require().Equal(
		details.IndicationForm.ClassesAndCommoditiesForm.OperatingClassDistribution,
		form.ClassesAndCommoditiesForm.OperatingClassDistribution)
	s.Require().Equal(
		details.IndicationForm.LossRunSummaryForm,
		form.LossRunSummaryForm)
}

func ValidateCommodityDistribution(
	s *suite.Suite, commodityDistributionFromDB, commodityDistributionFromRest *oapi_app.CommodityDistribution,
) {
	if commodityDistributionFromDB == nil && commodityDistributionFromRest == nil {
		s.Assert().Equal(commodityDistributionFromDB, commodityDistributionFromRest)
	} else {
		// In db, we don't store the type for commodity, so setting that nil
		for i := 0; i < len(commodityDistributionFromDB.Commodities); i++ {
			commodityDistributionFromDB.Commodities[i].Commodity.Type = nil
		}

		// In db, we don't store the label for category, so setting that nil
		for i := 0; i < len(commodityDistributionFromDB.Commodities); i++ {
			commodityDistributionFromDB.Commodities[i].Category.Label = nil
		}
		s.Require().Equal(commodityDistributionFromDB, commodityDistributionFromRest)
	}
}

func ValidateAddlInfoForm(
	s *suite.Suite,
	form *oapi_app.AdditionalInformationForm,
	details *oapi_app.AdditionalInformationForm,
) {
	s.Require().NotNil(details.DriverList)
	s.Require().Equal(details.DriverList, form.DriverList)
	s.Require().NotNil(details.Commodities)
	s.Require().Equal(details.Commodities, form.Commodities)
	s.Require().NotNil(details.CommoditiesComment)
	s.Require().Equal(details.CommoditiesComment, form.CommoditiesComment)
	s.Require().NotNil(details.LargeLossComment)
	s.Require().Equal(details.LargeLossComment, form.LargeLossComment)
	s.Require().NotNil(details.LossRunFiles)
	s.Require().Equal(details.LossRunFiles, form.LossRunFiles)
	s.Require().NotNil(details.NumOwnerOperatorUnits)
	s.Require().Equal(details.NumOwnerOperatorUnits, form.NumOwnerOperatorUnits)
	s.Require().NotNil(details.OverallComment)
	s.Require().Equal(details.OverallComment, form.OverallComment)
}
