package app_state_machine

import (
	"context"
	"database/sql"
	"time"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/quoting/utils"
	"nirvanatech.com/nirvana/telematics"
	rsm "nirvanatech.com/nirvana/underwriting/state_machine/review_readiness_state_machine"

	"github.com/google/uuid"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums/old_enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/jobber/jtypes"

	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	oapi_app "nirvanatech.com/nirvana/openapi-specs/components/application"
	"nirvanatech.com/nirvana/quoting/app_state_machine/app_logic"
	"nirvanatech.com/nirvana/quoting/app_state_machine/cerrors"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
)

const (
	notBindable = false
	bindable    = true
)

type appStateMachineHelper struct {
	deps       ASMDeps
	appId      string
	appState   state_enums.AppState
	asmWrapper AppStateMachineWrapper
}

// ########### CONVENIENCE HELPER FNS ################
// ###################################################

func (a *appStateMachineHelper) loadApp(ctx context.Context) (*app.Application, error) {
	appObj, err := a.deps.AppWrapper.GetAppById(ctx, a.appId)
	switch {
	case errors.Is(err, sql.ErrNoRows):
		return nil, cerrors.WrapUserVisibleError(errors.Wrap(err, cerrors.AppDoesntExist))
	case err != nil:
		return nil, cerrors.WrapRetryableError(errors.Wrap(err, cerrors.DbLoadingErr))
	}
	return appObj, nil
}

// loadSub is only intended to be used by the states
func (a *appStateMachineHelper) loadSub(ctx context.Context, subId *string) (
	*app.SubmissionObject, error,
) {
	if subId == nil {
		return nil, errors.New("nil sub id")
	}
	subObj, err := a.deps.AppWrapper.GetSubmissionById(ctx, *subId)
	switch {
	case errors.Is(err, sql.ErrNoRows):
		return nil, errors.Wrap(err, cerrors.SubDoesntExist)
	case err != nil:
		return nil, cerrors.WrapRetryableError(errors.Wrap(err, cerrors.DbLoadingErr))
	}
	return subObj, nil
}

func (a *appStateMachineHelper) loadIndicationOption(ctx context.Context, indicationId string) (*app.IndicationOption, error) {
	indicationOption, err := a.deps.AppWrapper.GetIndOptionById(ctx, indicationId)
	switch {
	case errors.Is(err, sql.ErrNoRows):
		return nil, errors.Wrap(err, cerrors.IndOptDoesntExist)
	case err != nil:
		return nil, cerrors.WrapRetryableError(errors.Wrap(err, cerrors.DbLoadingErr))
	}
	return indicationOption, nil
}

func (a *appStateMachineHelper) loadAppReviewFromLastUWSub(ctx context.Context) (*uw.ApplicationReview, error) {
	appObj, err := a.loadApp(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to load app")
	}
	subObj, err := a.loadSub(ctx, appObj.UwSubmissionID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to load last sub %s", appObj.UwSubmissionID)
	}
	appReview, err := a.deps.AppReviewWrapper.GetReviewFromSub(ctx, subObj.ID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get review from last sub %s", appObj.UwSubmissionID)
	}
	return appReview, nil
}

func (a *appStateMachineHelper) getIndicationOptions(ctx context.Context) (*oapi_app.IndicationOptions, error) {
	appObj, err := a.loadApp(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to load app")
	}
	subObj, err := a.loadSub(ctx, appObj.IndicationSubmissionID)
	if err != nil {
		return nil, errors.Wrap(err, "unable to load sub")
	}

	currDed := a.bindDeductiblesFromDbToRest(subObj.CoverageInfo.Coverages)
	dedOptions := a.bindDeductibleOptionsFromDbToRest(
		subObj.CoverageInfo.Coverages, app_logic.GetAllDeductibleOptionsFromState(appObj.State))
	covVars, err := a.bindCovVarsFromDbToRest(subObj.CoverageInfo, subObj.CompanyInfo)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind coverage variables")
	}
	optionsObjs, err := a.deps.AppWrapper.GetIndOptionsBySubId(ctx, subObj.ID)
	if err != nil {
		return nil, errors.Wrap(err, "unable to fetch options for submission")
	}
	options := a.bindIndOptionsFromDbToRest(optionsObjs)
	coveragesWithCombinedDeductibles := a.bindCombinedCoveragesFromDBToRest(subObj.CoverageInfo.
		CoveragesWithCombinedDeductibles)

	retval := &oapi_app.IndicationOptions{
		Options:                  options,
		CurrentDeductibles:       currDed,
		DeductiblesOptions:       dedOptions,
		CoverageVariablesOptions: covVars,
	}

	if coveragesWithCombinedDeductibles != nil {
		retval.CoveragesWithCombinedDeductibles = *coveragesWithCombinedDeductibles
	}

	return retval, nil
}

func (a *appStateMachineHelper) getRenewalIndicationOptions(ctx context.Context) (*oapi_app.IndicationOptions, error) {
	appObj, err := a.loadApp(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to load app")
	}
	currDed := a.bindDeductiblesFromDbToRest(appObj.CoverageInfo.Coverages)

	// Renewal flows requires all options and variable options.
	formCoverages := []app.CoverageDetails{
		{
			CoverageType: app_enums.CoverageAutoLiability,
		},
		{
			CoverageType: app_enums.CoverageAutoPhysicalDamage,
		},
		{
			CoverageType: app_enums.CoverageMotorTruckCargo,
		},
	}
	// Update the form to include the current values for deductibles and limits
	updateRenewalFormCoverages(&formCoverages, appObj.CoverageInfo.Coverages)
	covVars, err := a.bindRenewalCovVarsFromDbToRest(formCoverages, appObj.CompanyInfo)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind coverage variables")
	}
	// GL doesn't have CoverageVariablesOptions so we add for DeductiblesOptions
	formCoverages = append(formCoverages, app.CoverageDetails{
		CoverageType: app_enums.CoverageGeneralLiability,
	})
	dedOptions := a.bindDeductibleOptionsFromDbToRest(
		formCoverages, app_logic.GetAllDeductibleOptionsFromState(appObj.State))
	coveragesWithCombinedDeductibles := a.bindCombinedCoveragesFromDBToRest(appObj.CoverageInfo.
		CoveragesWithCombinedDeductibles)

	limitOptions, err := a.bindLimitsFromDbToRest(
		formCoverages, appObj.CompanyInfo.USState,
	)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind coverage limits")
	}

	retval := &oapi_app.IndicationOptions{
		// We don't generate an indication in renewal so options are nil
		Options:                  nil,
		CurrentDeductibles:       currDed,
		DeductiblesOptions:       dedOptions,
		CoverageVariablesOptions: covVars,
		LimitsOptions:            &limitOptions,
	}

	if coveragesWithCombinedDeductibles != nil {
		retval.CoveragesWithCombinedDeductibles = *coveragesWithCombinedDeductibles
	}

	return retval, nil
}

func (a *appStateMachineHelper) getJobStatus(ctx context.Context, subId *string) (
	*jtypes.JobRun, error,
) {
	sub, err := a.loadSub(ctx, subId)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to load submission %s", subId)
	}

	if sub.JobRunId == nil {
		return nil, errors.Newf("job run id is nil for sub %s", subId)
	}
	return a.deps.Jobber.GetJobRun(ctx, *sub.JobRunId)
}

func (a *appStateMachineHelper) isOperationSupported(
	operatingClass app_enums.OperatingClass, commodityHauled old_enums.PrimaryCommodityHauled,
) error {
	commodities, isOpSupported := app_logic.SupportedOperations[app_enums.CoverageAutoLiability][operatingClass]

	if !isOpSupported {
		return errors.Newf("Operating class %s not supported", operatingClass)
	}
	for _, com := range commodities {
		if com.EnumValue == commodityHauled {
			return nil
		}
	}
	return errors.Newf("commodity hauled %s not supported by operating class %s",
		commodityHauled.String(), operatingClass.String())
}

// This function checks if the application stored in the DB contains
// some Operations data stored. We compare each value with their zero-values
// to check if they have been changed.
func (a *appStateMachineHelper) isOperationsAvailableInDB(
	appObj *app.Application,
) bool {
	if appObj.CompanyInfo.NumberOfPowerUnits == 0 &&
		appObj.CompanyInfo.ProjectedMileage == 0 &&
		len(appObj.CompanyInfo.RadiusOfOperation) == 0 &&
		len(appObj.EquipmentInfo.EquipmentList.Info) == 0 &&
		len(appObj.CoverageInfo.Coverages) == 0 &&
		appObj.ProducerID == nil {
		return false
	}
	return true
}

// This function checks if the application stored in the DB contains
// some Classes and Commodities data stored. We compare each value with their
// zero-value to check if they have been changed.
func (a *appStateMachineHelper) isClsAndComAvailableInDB(
	appObj *app.Application,
) bool {
	if len(appObj.EquipmentInfo.OperatingClassDistribution) == 0 &&
		appObj.EquipmentInfo.PrimaryOperatingClass == nil &&
		appObj.EquipmentInfo.PrimaryCommodity == nil {
		return false
	}
	return true
}

// This function checks if the application stored in the DB contains
// some Loss Run Summary data stored. We compare each value with their
// zero-value to check if they have been changed.
func (a *appStateMachineHelper) isLossRunSummaryAvailableInDB(appObj *app.Application) bool {
	if len(appObj.LossInfo.LossRunSummary) == 0 {
		return false
	}
	return true
}

// ############### VALIDATION FNS ####################
// ###################################################

// validateAppForAppetiteCheck validates if the application's data is complete
// enough for an appetite check. Each entry is checked against their zero-value
func (a *appStateMachineHelper) validateAppForAppetiteCheck(appObj app.Application) error {
	switch {
	case appObj.CompanyInfo.DOTNumber == 0:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing DOT Number for appetite check")
	case appObj.CompanyInfo.Name == "":
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing Company name for appetite check")
	case appObj.CoverageInfo.EffectiveDate == time.Time{}:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing effective date for appetite check")
	default:
		return nil
	}
}

// validateAppForIndicationSubmission validates if the application's data is
// complete enough to be submitted for indication. Each entry is checked
// against their zero-value.
func (a *appStateMachineHelper) validateAppForIndicationSubmission(appObj app.Application) error {
	err := a.validateAppForAppetiteCheck(appObj)
	if err != nil {
		return errors.Wrap(err, "app validation for appetite check failed")
	}

	if appObj.ModelPinConfig.Application.Flags.UsePrimaryCategory {
		if appObj.EquipmentInfo.PrimaryCategory == nil {
			return errors.Wrap(cerrors.ErrActionNotValid, "app missing Primary Category for submission")
		}
	} else {
		if appObj.EquipmentInfo.PrimaryCommodity == nil {
			return errors.Wrap(cerrors.ErrActionNotValid, "app missing Primary Commodity for submission")
		}
	}

	switch {
	// Operations
	case len(appObj.CompanyInfo.RadiusOfOperation) == 0:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing Radius Of Operation for submission")
	case appObj.CompanyInfo.NumberOfPowerUnits == 0:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing Projected Number Of Power Units for submission")
	case appObj.CompanyInfo.ProjectedMileage == 0:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing Projected Mileage for submission")
	case len(appObj.EquipmentInfo.EquipmentList.Info) == 0:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing Equipment List for submission")
	case len(appObj.CoverageInfo.Coverages) == 0:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing Coverages for submission")
	// Classes and Commodities
	case len(appObj.EquipmentInfo.OperatingClassDistribution) == 0:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing Classes and Commodities for submission")
	case appObj.EquipmentInfo.PrimaryOperatingClass == nil:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing Primary Operating Class for submission")
	// Loss Run Summary
	case len(appObj.LossInfo.LossRunSummary) == 0:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing Loss Run Summary for submission")
	// Validation to check if there is a loss claim, then the dollar amount should be non-zero
	case checkZeroDollarLossClaims(appObj.LossInfo.LossRunSummary):
		return errors.Wrap(cerrors.ErrActionNotValid, "app Loss Run Summary contains zero dollar loss claims")
	default:
		return nil
	}
}

// checkZeroDollarLossClaims checks if there were any claims made which had zero dollar loss incurred
func checkZeroDollarLossClaims(summaries []app.LossRunSummaryPerCoverage) bool {
	for _, lrspc := range summaries {
		for _, lrsr := range lrspc.Summary {
			if lrsr.NumberOfClaims > 0 {
				if lrsr.LossIncurred <= 0 {
					return true
				}
			}
		}
	}
	return false
}

// validateAppForQuoteSubmission validates if the application's data is
// complete enough to be submitted for quote. Each entry is checked
// against their zero-value.
func (a *appStateMachineHelper) validateAppForQuoteSubmission(appObj app.Application) error {
	err := a.validateAppForIndicationSubmission(appObj)
	if err != nil {
		return errors.Wrap(err, "app validation for indication submission failed")
	}
	switch {
	// Drivers
	case appObj.DriversInfo == nil || len(appObj.DriversInfo.Drivers) == 0:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing drivers list")
	// Loss Runs
	case appObj.AdditionalLossInfo == nil || len(appObj.AdditionalLossInfo.Files) == 0:
		return errors.Wrap(cerrors.ErrActionNotValid, "app missing loss runs")
	default:
		return nil
	}
}

// isTSPConnEstablished is a helper func to understand if a TSP connection is
// established. This func is not aware of the status of the handle, just checks
// if that's present.
func (a *appStateMachineHelper) isTSPConnEstablished(
	ctx context.Context, handleId *string,
) (bool, error) {
	retval, err := utils.HasTSPConnectionEverBeenEstablished(ctx, a.deps.TspConnManager, handleId)
	if err != nil {
		return false, errors.Wrap(err, "failed to check if TSP connection is established")
	}
	return retval, nil
}

// isTSPConnActive is a helper func to understand if a TSP connection is
// active, meaning that data should be flowing.
func (a *appStateMachineHelper) isTSPConnActive(
	ctx context.Context, handleId *string, tspEnum *telematics.TSP,
) (bool, error) {
	if handleId == nil || tspEnum == nil {
		return false, nil
	}
	handleIdUUID, err := uuid.Parse(*handleId)
	if err != nil {
		return false, errors.Wrapf(err, "failed to parse handleId %s", *handleId)
	}
	connInfo, err := a.deps.TspConnManager.GetConnectionInfo(ctx, handleIdUUID)
	if err != nil {
		return false, errors.Wrapf(err, "failed to get connection info for handleId %s", *handleId)
	}
	return connInfo.Status == telematics.ConnectionStatusConnected, nil
}

func (a *appStateMachineHelper) validateQuoteForForms(ctx context.Context, appObj *app.Application) error {
	switch {
	case appObj.PackageType == nil:
		return errors.New("unable to get quote, missing package type")
	case appObj.CoverageInfo == nil:
		return errors.New("unable to get quote, missing coverage info")
	case appObj.CompanyInfo == nil:
		return errors.New("unable to get quote, missing company info")
	}
	return nil
}

func (a *appStateMachineHelper) copyReviewDataFromLatestPendingAppReview(ctx context.Context, review, latestPendingAppReview *uw.ApplicationReview) error {
	review.AccountGrade = latestPendingAppReview.AccountGrade
	review.ReviewInfo.Operations.Comments = latestPendingAppReview.ReviewInfo.Operations.Comments
	review.ReviewInfo.Equipments.Comments = latestPendingAppReview.ReviewInfo.Equipments.Comments
	review.ReviewInfo.Drivers.Comments = latestPendingAppReview.ReviewInfo.Drivers.Comments
	review.ReviewInfo.Safety.Comments = latestPendingAppReview.ReviewInfo.Safety.Comments
	review.ReviewInfo.Financials.Comments = latestPendingAppReview.ReviewInfo.Financials.Comments
	review.ReviewInfo.Losses.Comments = latestPendingAppReview.ReviewInfo.Losses.Comments
	review.ReviewInfo.Global.Comments = latestPendingAppReview.ReviewInfo.Global.Comments
	review.Notes = latestPendingAppReview.Notes
	review.QuoteInfo.PullMvrs = latestPendingAppReview.QuoteInfo.PullMvrs
	review.UnderwriterID = latestPendingAppReview.UnderwriterID
	review.Overrides.VehicleZoneDistribution = latestPendingAppReview.Overrides.VehicleZoneDistribution
	review.AdditionalDocuments = latestPendingAppReview.AdditionalDocuments
	review.EffectiveDate = latestPendingAppReview.EffectiveDate
	review.QuoteInfo.FetchAttractScore = latestPendingAppReview.QuoteInfo.FetchAttractScore
	if latestPendingAppReview.Overrides.ModelPinConfig != nil {
		review.Overrides.ModelPinConfig = latestPendingAppReview.Overrides.ModelPinConfig
	}
	if review.ReviewInfo.Overview != nil && latestPendingAppReview.ReviewInfo.Overview != nil {
		review.ReviewInfo.Overview.Comments = latestPendingAppReview.ReviewInfo.Overview.Comments
	}
	review.Overrides.PremiumTaxRecords = latestPendingAppReview.Overrides.PremiumTaxRecords
	review.Overrides.DriverList = latestPendingAppReview.Overrides.DriverList
	review.Overrides.RadiusOfOperation = latestPendingAppReview.Overrides.RadiusOfOperation
	review.Overrides.TargetPrice = latestPendingAppReview.Overrides.TargetPrice

	// After resubmission review readiness state of review is preserved i.e. if previous review was "Ready For Review"
	// then new review will also be "Ready For Review", therefore we should also copy the ReviewReadinessSubID from previous review.
	review.ReviewReadinessSubID = latestPendingAppReview.ReviewReadinessSubID
	return nil
}

func (a *appStateMachineHelper) createReviewReadinessTasksForAppReview(ctx context.Context, review, latestPendingReview *uw.ApplicationReview) error {
	shouldCreateTasks, err := a.deps.ReviewReadinessTaskManager.ShouldCreateTasksForReview(ctx, review.Id)
	if err != nil {
		return errors.Wrap(err, "failed to check if tasks should be created for review or not")
	}

	isPreviousReviewPresent := latestPendingReview != nil
	areTasksPresentInPrevReview := false
	if isPreviousReviewPresent {
		tasksForPreviousReview, err := a.deps.ReviewReadinessTaskManager.GetTasks(ctx, latestPendingReview.Id)
		if err != nil {
			return errors.Wrapf(err, "failed to get tasks for latest pending review %s", latestPendingReview.Id)
		}
		areTasksPresentInPrevReview = len(tasksForPreviousReview) > 0
	}

	log.Info(ctx, "flags for deciding whether to create tasks or not",
		log.AppReviewID(review.Id),
		log.Bool("shouldCreateTasks", shouldCreateTasks),
		log.Bool("isPreviousReviewPresent", isPreviousReviewPresent),
		log.Bool("areTasksPresentInPrevReview", areTasksPresentInPrevReview),
	)

	// if an app review had tasks earlier then after rollback new review should also have tasks irrespective of whether
	// shouldCreateTasks returns true or false. This is to avoid reviews of same application going through new as well as old flow
	if areTasksPresentInPrevReview {
		// copy from prev review irrespective of shouldCreateTasks
		err := a.deps.ReviewReadinessTaskManager.CopyTasks(ctx, latestPendingReview.Id, review.Id)
		if err != nil {
			return errors.Wrapf(err, "failed to copy tasks from latest pending review %s to new review %s", latestPendingReview.Id, review.Id)
		}
		return nil
	} else if isPreviousReviewPresent {
		// previous review is present but it did not have any tasks, so don't create tasks now also
		return nil
	} else if shouldCreateTasks {
		// previous review is not present, create tasks from scratch
		_, err = a.deps.ReviewReadinessTaskManager.BuildTasks(ctx, review.Id)
		if err != nil {
			return errors.Wrap(err, "failed to build tasks")
		}
		return nil
	} else {
		// previous review is not present, and shouldCreateTasks is also false, so don't create tasks
		return nil
	}
}

func (a *appStateMachineHelper) createTasksAndUpdateReviewReadinessState(ctx context.Context, review, latestPendingReview *uw.ApplicationReview) error {
	// create review readiness tasks for app review
	// IMP - tasks should be created only after GenerateWidgetsForAppReview job is complete because
	// task creation logic depends on the data generated by this job.
	// This is valid only till review readiness flow is rolled out 100%, after that logic will not depend on data generated by this job.
	err := a.createReviewReadinessTasksForAppReview(ctx, review, latestPendingReview)
	if err != nil {
		return errors.Wrapf(err, "unable to create review readiness tasks for app review with ID %s", review.Id)
	}

	// after task creation, re-calculate the initial review-readiness state
	// this is because initial review readiness state can be different based on app review should go through new or old review readiness flow
	appID := review.ApplicationID
	reviewReadinessState, err := a.calculateInitialReviewReadinessState(ctx, review, latestPendingReview)
	if err != nil {
		return errors.Wrapf(err, "unable to calculate initial app review readiness state for app id %s", appID)
	}
	// update review readiness state in database
	err = a.deps.AppReviewWrapper.UpdateAppReview(ctx, review.Id,
		func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
			review.ReviewReadinessState = *reviewReadinessState
			return review, nil
		},
	)
	if err != nil {
		return errors.Wrapf(err, "unable to update review readiness state for app review with ID %s", review.Id)
	}
	return nil
}

func (a *appStateMachineHelper) calculateInitialReviewReadinessState(ctx context.Context, review, latestPendingReview *uw.ApplicationReview) (*uw.AppReviewReadinessState, error) {
	isTaskWorkflowEnabled, err := a.deps.ReviewReadinessTaskManager.IsTaskWorkflowEnabled(ctx, review.Id)
	if err != nil {
		return nil, errors.Wrap(err, "failed to check if task workflow is enabled")
	}
	if isTaskWorkflowEnabled {
		if latestPendingReview != nil {
			// copy review readiness state from latest pending review
			return &latestPendingReview.ReviewReadinessState, nil
		}
		return pointer_utils.ToPointer(uw.AppReviewReadinessStateNotReady), nil
	}

	// calculate initial state using old state machine
	reviewReadinessState, err := rsm.CalculateInitialReviewReadinessState(
		ctx, a.deps.AppWrapper, *a.deps.TspConnManager, a.deps.TelematicsDataPlatformClient,
		a.deps.Jobber, a.deps.AppReviewWrapper, review.ApplicationID,
	)
	return reviewReadinessState, err
}

func (a *appStateMachineHelper) copyReviewNotes(ctx context.Context, reviewID, newReviewID string) error {
	panelNotes, err := a.deps.AppReviewWrapper.GetPanelNotes(ctx, reviewID)
	if err != nil {
		if errors.Is(err, uw.ErrPanelNotesNotFound) {
			return nil // no notes present, could be roll back before telematics connection
		}
		return errors.Wrap(err, "failed fetching review panel notes")
	}

	notesCopy := uw.PanelNotes{
		Drivers: panelNotes.Drivers,
	}

	err = a.deps.AppReviewWrapper.InsertPanelNotes(ctx, newReviewID, &notesCopy)
	if err != nil {
		return errors.Wrap(err, "failed inserting review panel notes")
	}

	return nil
}
