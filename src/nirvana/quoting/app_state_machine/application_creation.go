//nolint:all
package app_state_machine

import (
	"context"
	"database/sql"
	"slices"
	"time"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency_bd_mapping"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/events/quoting_events"
	event_enums "nirvanatech.com/nirvana/external_client/salesforce/jobs/enums"
	"nirvanatech.com/nirvana/external_client/salesforce/wrapper"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/policy"
	policy_constants "nirvanatech.com/nirvana/policy_common/constants"
	"nirvanatech.com/nirvana/quoting/app_state_machine/app_logic"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	app_chkr "nirvanatech.com/nirvana/quoting/appetite_checker"
	app_chkr_enums "nirvanatech.com/nirvana/quoting/appetite_checker/enums"
	"nirvanatech.com/nirvana/quoting/clearance"
	"nirvanatech.com/nirvana/quoting/clearance/emails"
	clearance_enums "nirvanatech.com/nirvana/quoting/clearance/enums"
	"nirvanatech.com/nirvana/quoting/experiments"
	"nirvanatech.com/nirvana/quoting/tags"
	"nirvanatech.com/nirvana/quoting/utils"
	"nirvanatech.com/nirvana/rating/data_processing/lni_processing"
	"nirvanatech.com/nirvana/rating/data_processing/rating_tier"
	"nirvanatech.com/nirvana/rating/models/models_release"
	"nirvanatech.com/nirvana/rating/rtypes"
	tsp_connections "nirvanatech.com/nirvana/telematics/connections"
	"nirvanatech.com/nirvana/underwriting/scheduler"
)

const (
	undesiredOperations                     = "true"
	salesforceDeclineReason                 = "Operations"
	salesforceUndesiredDeclineSubReason     = "Operations - All other"
	salesforceInactiveStateDeclineSubReason = "State"
	ClearanceRenewalCutoffTime              = time.Hour * 24 * 90
)

type deps struct {
	fx.In

	FmcsaWrapper              fmcsa.DataWrapper
	UwScheduler               scheduler.UwScheduler
	AppWrapper                application.DataWrapper
	AuthWrapper               auth.DataWrapper
	FeatureFlagClient         feature_flag_lib.Client
	AgencyWrapper             agency.DataWrapper
	AppetiteChecker           *app_chkr.AppetiteChecker
	EventHandler              events.EventsHandler
	JobberClient              quoting_jobber.Client
	MetricsClient             statsd.Statter
	ClearanceEmailer          emails.EmailWrapper
	PolicyClient              policy.Client
	FetcherClientFactory      data_fetching.FetcherClientFactory
	ProcessorClientFactory    data_processing.ProcessorClientFactory
	AgencyBdWrapper           agency_bd_mapping.Wrapper
	Clock                     clock.Clock
	TagsManager               *tags.Manager
	TspConnManager            *tsp_connections.TSPConnManager
	QuotingExperimentsManager experiments.Manager
}

type CreateApplicationHandler struct {
	deps deps
}

func newCreateApplicationHandler(d deps) *CreateApplicationHandler {
	return &CreateApplicationHandler{
		deps: d,
	}
}

// CreateApplication is a method that is meant to be used by the
// POST /applications endpoint and RenewApplication ASM action as well.
// It takes a ApplicationCreation struct and creates a new application based on
// the info provided.
func (h *CreateApplicationHandler) CreateApplication(
	ctx context.Context,
	request ApplicationCreation,
	shouldProtectApp bool,
) (*uuid.UUID, *ApplicationEvaluation, error) {
	// Validate the request for the effective date
	// TODO: Add this back when we handle this correctly in endorsements
	//if !time_utils.IsDateGreaterThanOrEqualToToday(request.EffectiveDate) {
	//	return nil, nil, errors.New("effective date cannot be in the past")
	//}

	err := validateAppetiteFormFromRest(request)
	if err != nil {
		log.Error(
			ctx, "HandlePostApplication: Error validating appetite form",
			log.Err(err),
			log.Any("request", request),
		)
		return nil, nil, errors.Wrap(err, "Error validating appetite form")
	}

	// Create application and run appetite check. Applications are created
	// with a default state_enums.AppStateUnsubmitted state, and then
	// moved into app_enums.AppStateDeclined if appetite check fails

	appID := uuid.New()
	shortID := app_logic.GenerateShortId(ctx, h.deps.AppWrapper, appID)
	dataContextID := uuid.New()

	agencyBDMapping, err := h.deps.AgencyBdWrapper.GetBDForAgency(ctx, request.AgencyId, enums.ProgramTypeFleet)
	if err != nil {
		log.Error(
			ctx, "HandlePostApplication: Error getting BD for agency",
			log.Err(err),
			log.Any("request", request),
		)
	}
	var assignedBd *uuid.UUID
	if agencyBDMapping != nil {
		assignedBd = &agencyBDMapping.UserID
	}
	// TODO: Remove application creation logic in a separate package
	appObj := application.NewApplication(appID.String(), shortID, dataContextID, assignedBd)
	appObj, err = populateNewApplicationFromAppetiteForm(ctx, h.deps.FmcsaWrapper, appObj, request)
	if err != nil {
		// Log the error but continue with the application creation
		log.Error(
			ctx, "HandlePostApplication: Error populating application from appetite form",
			log.Err(err),
			log.Any("request", request),
		)
		return nil, nil, errors.Wrap(err, "unable to populate application from appetite form")
	}

	appObj.AgencyID = request.AgencyId
	marketerID, err := getMarketerId(ctx, request)
	if err != nil {
		log.Error(
			ctx, "HandlePostApplication: Error parsing marketer ID",
			log.Err(err),
			log.Any("request", request),
		)
		return nil, nil, errors.Wrap(err, "unable to get marketer ID")
	}
	appObj.MarketerID = marketerID

	agent := authz.UserFromContext(ctx)

	appObj.CreatedBy = getCreatedBy(ctx, request)
	now := h.deps.Clock.Now()
	appObj.CreatedAt, appObj.UpdatedAt = now, now
	// Even though Unsubmitted is the default state, we enforce it to avoid
	// potential bugs.
	appObj.State = state_enums.AppStateUnsubmitted

	ctx = log.ContextWithFields(ctx, log.String("app_id", appID.String()))

	// Assign UW to app
	uw, err := h.deps.UwScheduler.GetAssignableUnderwriter(ctx, scheduler.UWAssignmentParams{
		AppID:                 appObj.ID,
		AgencyID:              appObj.AgencyID,
		DotNumber:             request.DotNumber,
		EffectiveDate:         request.EffectiveDate,
		ProgramType:           enums.ProgramTypeFleet,
		OriginalApplicationID: request.OriginalApplicationID,
		PUCount:               request.NumberOfPowerUnits,
	})
	if err != nil {
		log.Error(
			ctx, "HandlePostApplication: Error getting available UW",
			log.Err(err),
			log.Any("request", request),
		)
		return nil, nil, errors.Wrap(err, "unable to assign underwriter to app")

	}
	appObj.UnderwriterID = uw.ID
	appObj.ProducerID = request.ProducerId

	clearanceStatus, err := getClearanceStatusAndSendEmail(
		ctx,
		request,
		appObj,
		shouldProtectApp,
		h.deps.AppWrapper,
		h.deps.ClearanceEmailer,
		h.deps.PolicyClient,
	)
	if err != nil {
		return nil, nil, err
	}

	appObj.ClearanceStatus = clearanceStatus

	err = h.deps.AppWrapper.InsertApp(ctx, *appObj)
	if err != nil {
		// TODO: This should be a 500
		log.Error(
			ctx, "HandlePostApplication: Error inserting application",
			log.Err(err),
			log.Any("request", request),
		)
		return nil, nil, errors.Wrap(err, "Unable to persist application")
	}

	if *clearanceStatus == clearance_enums.ApplicationCleared && shouldProtectApp {
		err = h.deps.TagsManager.SaveTag(ctx, appID.String(), app_enums.ApplicationTagClearanceProtected, true)
		if err != nil {
			return nil, nil, errors.Wrap(err, "failed to save tag in Check Clearance job")
		}
	}

	effectiveDate := appObj.CoverageInfo.EffectiveDate
	// log ApplicationCreated event
	isRenewal := request.IsRenewal != nil && *request.IsRenewal
	renewalOriginalApplicationID := ""
	if request.OriginalApplicationID != nil {
		renewalOriginalApplicationID = *request.OriginalApplicationID
	}

	insuranceCarrier := policy_constants.InsuranceCarrierFalseLake
	provider := rtypes.ProviderSentry

	// Check if the insurance carrier and provider needs to be changed
	// This feature flag look up is to check whether we should use Fronter-x for an application
	// TODO: Remove this flag when this is completely launched and rollback is not an option.
	newFronterAvailable, err := h.deps.FeatureFlagClient.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(agent), feature_flag_lib.FeatureFronterX, true)
	if err != nil {
		log.Error(ctx, "Feature flag lookup failed", log.Err(err))
		// Proceeding with the default value here in case of failure
	}

	canUseNewCarrier := models_release.IsCarrierActiveForStateOnDate(
		ctx,
		policy_constants.InsuranceCarrierMSTransverse,
		rtypes.ProviderSentryMST,
		appObj.CompanyInfo.USState,
		effectiveDate,
		request.AgencyId,
		newFronterAvailable,
	)

	isNonAdmittedApp := false
	if canUseNewCarrier {
		insuranceCarrier = policy_constants.GetCarrier(appObj.CompanyInfo.USState, enums.ProgramTypeFleet)
		provider = rtypes.ProviderSentryMST
		if insuranceCarrier == policy_constants.InsuranceCarrierMSTSpeciality {
			isNonAdmittedApp = true
		}
		log.Info(ctx, "Changing provider to SentryMST")
	}

	fetcherClient, closerF, err := h.deps.FetcherClientFactory()
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to create data fetcher client")
	}
	defer func() { _ = closerF() }()

	processorClient, closerP, err := h.deps.ProcessorClientFactory(fetcherClient)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to create data processor client")
	}
	defer func() { _ = closerP() }()

	var yearsInBusiness *float64
	yib, err := lni_processing.GetYearsInBusinessFromAuthorityHistoryV1(
		ctx,
		processorClient,
		appObj.CompanyInfo.DOTNumber,
		appObj.CoverageInfo.EffectiveDate,
	)
	if err != nil {
		log.Error(ctx, "failed to get years in business from authority history v1")
	} else {
		yearsInBusiness = pointer_utils.ToPointer(yib)
	}

	var currentInsurance *quoting_events.CurrentInsurance
	insurances, err := fetcherClient.GetBIPDActiveOrPendingInsuranceV1(
		ctx,
		&data_fetching.BIPDActiveOrPendingInsuranceRequestV1{DotNumber: appObj.CompanyInfo.DOTNumber},
	)
	if err != nil {
		log.Error(ctx, "failed to get current BIPD Active or Pending Insurance")
	} else {
		if len(insurances.Records) == 0 || insurances.Records[0].EffectiveDate == nil {
			log.Error(ctx, "no valid active or pending insurance")
		} else {
			record := insurances.Records[0]
			currentInsurance = &quoting_events.CurrentInsurance{
				Carrier:       record.GetInsuranceCompanyName(),
				EffectiveDate: record.EffectiveDate.AsTime(),
			}
			if record.CancelEffectiveDate != nil {
				currentInsurance.CancellationDate = pointer_utils.ToPointer(record.CancelEffectiveDate.AsTime())
			}
		}
	}

	logApplicationEvent(
		ctx,
		h.deps.AppWrapper,
		h.deps.AuthWrapper,
		h.deps.EventHandler,
		h.deps.AgencyWrapper,
		Created,
		appID.String(),
		nil,
		yearsInBusiness,
		currentInsurance,
		isRenewal,
		renewalOriginalApplicationID,
		insuranceCarrier,
		isNonAdmittedApp,
	)
	version, err := models_release.GetActiveVersion(
		ctx,
		h.deps.FeatureFlagClient,
		&agent,
		provider, appObj.CompanyInfo.USState,
		effectiveDate,
	)
	if err != nil {
		log.Error(ctx,
			"HandlePostApplication: Could not get rateml model version for application",
			log.Err(err),
			log.String("provider", provider.String()),
			log.String("appId", appID.String()),
			log.Any("state", appObj.CompanyInfo.USState),
		)
		// Not returning error because we still want to persist an invalid
		// rateml version.
	}

	// Persist RateML Model Pin config in application
	// Storing this after running the appetite check, otherwise
	// non-supported states will give an error instead of us declining the
	// application
	useVehiclesService, err := h.deps.FeatureFlagClient.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(agent),
		feature_flag_lib.FeatureVehiclesServiceVINDecoder,
		false)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "failed to get Vehicles Service VIN Problems feature flag for agent %s", agent.Email)
	}
	prefillAndAutoReviewVinProblemsFlag, err := h.deps.FeatureFlagClient.BoolVariation(
		// performing lookup on agent, so flag can be enabled just for QA.
		feature_flag_lib.BuildLookupAttributes(agent),
		feature_flag_lib.FeaturePrefillAndAutoReviewVinProblems,
		false,
	)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to fetch PrefillAndAutoReviewVinProblemsFlag")
	}
	getDriverYoeFromAgentInputFlag, err := h.deps.FeatureFlagClient.BoolVariation(
		// performing lookup on agent, so flag can be enabled just for QA.
		feature_flag_lib.BuildLookupAttributes(agent),
		feature_flag_lib.FeatureDriverYoeFromAgentInput,
		false,
	)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to fetch FeatureDriverYoeFromAgentInput flag")
	}

	recordDate, recordDumpDate := rating_tier.GetLatestValidRatingTierRecordDateAndDumpDateV1(
		ctx,
		fetcherClient,
		processorClient,
		request.DotNumber,
	)
	log.Info(
		ctx,
		"using the following rating tier dates",
		log.Stringer("recordDumpDate", recordDumpDate),
		log.Stringer("recordDate", recordDate),
	)
	if err := h.deps.AppWrapper.UpdateApp(ctx, appID.String(), func(a application.Application) (application.Application, error) {
		sc := application.NewModelPinConfig(
			provider,
			appObj.CompanyInfo.USState,
			version,
			useVehiclesService,
			recordDate,
			recordDumpDate,
			insuranceCarrier,
			isNonAdmittedApp,
			prefillAndAutoReviewVinProblemsFlag,
			getDriverYoeFromAgentInputFlag,
		)
		a.ModelPinConfig = sc
		return a, nil
	}); err != nil {
		log.Error(ctx,
			"HandlePostApplication: Could not persist rateml model version for application",
			log.Err(err),
			log.Any("provider", provider),
			log.String("version", version.String()),
			log.Any("state", appObj.CompanyInfo.USState),
		)
		return nil, nil, errors.Wrap(err, "Unable to persist model pin config")
	}

	// We want to unblock CA uw approval for now
	// TODO (Tanzeem): rethink about this with new CA agency license requirements
	if err = h.deps.TagsManager.SaveTag(ctx, appID.String(), app_enums.ApplicationTagHasValidLicenseForNonAdmitted, true); err != nil {
		log.Error(ctx,
			"HandlePostApplication: Could not save hasValidAgentLicense tag to the application",
			log.Err(err),
			log.Bool("hasValidAgentLicense", true),
		)
		return nil, nil, errors.Wrap(err, "Unable to save hasValidAgentLicense tag to the application")
	}

	// Reject applications with undesired operations
	if request.HasUndesiredOperations {
		result := ApplicationEvaluation{
			AppetiteCheckResult: app_chkr.NewAppetiteCheckerResult(app_chkr_enums.AppetiteCheckerDeclined, []app_chkr.DeclinedRule{
				app_chkr.NewDeclinedRule(
					app_chkr_enums.AppetiteCheckRuleHasUndesiredOperations,
					map[string]string{"undesiredOperations": undesiredOperations},
				),
			}),
		}
		err = h.declineApplication(
			ctx,
			appID,
			application.StateMetadata{
				Description:   result.AppetiteCheckResult.UserVisible(),
				PreviousState: state_enums.AppStateUnsubmitted,
			},
		)
		if err != nil {
			log.Error(ctx,
				"HandlePostApplication: Error setting application state to declined",
				log.Err(err),
			)
			return nil, nil, errors.Wrap(err, "unable to decline application")
		}

		// Log ApplicationDeclined event
		declinedRules, err := events.IndentAndToString(quoting_events.DeclinedRulesForEvents(result.AppetiteCheckResult.DeclinedRules), `"`)
		if err != nil {
			// just log the error
			log.Error(ctx, "unable to marshal declined rules", log.Err(err))
		}

		if err = utils.TriggerUpdateSalesforceOpportunity(
			ctx,
			h.deps.JobberClient,
			h.deps.MetricsClient,
			wrapper.SalesforceEventUpdateApplicationArgs{
				ApplicationID:    appID.String(),
				DeclinedRules:    declinedRules,
				DeclineReason:    salesforceDeclineReason,
				DeclineSubReason: salesforceUndesiredDeclineSubReason,
				EventName:        event_enums.QuotingApplicationDeclined,
			}); err != nil {
			// just log the error
			log.Error(
				ctx, "failed to update salesforce opportunity",
				log.String("eventName", event_enums.QuotingApplicationDeclined.String()),
				log.Err(err),
			)
		}

		logApplicationEvent(
			ctx,
			h.deps.AppWrapper,
			h.deps.AuthWrapper,
			h.deps.EventHandler,
			h.deps.AgencyWrapper,
			Declined,
			appID.String(),
			result.AppetiteCheckResult.DeclinedRules,
			yearsInBusiness,
			currentInsurance,
			isRenewal,
			renewalOriginalApplicationID,
			insuranceCarrier,
			isNonAdmittedApp,
		)
		return nil, &result, nil
	}

	// For Protected Apps we do not want to run the appetite checks since we are creating this on behalf
	// of our champion agents and we want to protect them from any issues that might arise from the appetite checks
	if shouldProtectApp {
		return &appID, nil, nil
	}

	err = h.deps.QuotingExperimentsManager.AssignExperiments(
		ctx,
		appID,
	)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to assign pricing experiments")
	}

	appetiteCheckResult, err := h.runAppetiteChecks(
		ctx,
		fetcherClient,
		appID,
		request,
		now,
		effectiveDate,
		yearsInBusiness,
		currentInsurance,
		isRenewal,
		renewalOriginalApplicationID,
		insuranceCarrier,
		isNonAdmittedApp,
		provider,
	)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to run appetite checks")
	}

	return pointer_utils.ToPointer(appID), appetiteCheckResult, nil
}

// runAppetiteChecks performs appetite checks on the application
func (h *CreateApplicationHandler) runAppetiteChecks(
	ctx context.Context,
	fetcherClient data_fetching.FetcherClient,
	appID uuid.UUID,
	request ApplicationCreation,
	now time.Time,
	effectiveDate time.Time,
	yearsInBusiness *float64,
	currentInsurance *quoting_events.CurrentInsurance,
	isRenewal bool,
	renewalOriginalApplicationID string,
	insuranceCarrier policy_constants.InsuranceCarrier,
	isNonAdmittedApp bool,
	provider rtypes.RatemlModelProvider,
) (*ApplicationEvaluation, error) {
	dotInformation, err := getAppetiteCheckerDOTInformation(
		ctx,
		h.deps.FmcsaWrapper,
		fetcherClient,
		request.DotNumber,
		now,
		yearsInBusiness,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get appetite checker dot information")
	}

	appetiteResult, problems, err := h.deps.AppetiteChecker.Score(
		ctx,
		*dotInformation,
		pointer_utils.ToPointer(appID),
		effectiveDate,
		now,
		provider,
	)
	if err != nil {
		log.Error(ctx,
			"HandlePostApplication: Error running appetite check",
			log.Err(err),
		)
		return nil, errors.Wrap(err, "Unable to run appetite checker")

	}
	// Store problems in application
	if err := h.deps.AppWrapper.UpdateApp(ctx, appID.String(), func(a application.Application) (application.Application, error) {
		a.Problems.AddBulk(problems)
		return a, nil
	}); err != nil {
		log.Error(ctx,
			"HandlePostApplication: Could not persist problems for application",
			log.Err(err),
		)
		return nil, errors.Wrap(err, "Unable to persist problems")

	}

	// TODO: send problems to UW
	// If appetite check failed, change state to state_enums.AppStateDeclined
	if appetiteResult.Status == app_chkr_enums.AppetiteCheckerDeclined {
		err = h.declineApplication(
			ctx,
			appID,
			application.StateMetadata{
				Description:   appetiteResult.UserVisible(),
				PreviousState: state_enums.AppStateUnsubmitted,
			},
		)
		if err != nil {
			log.Error(ctx,
				"HandlePostApplication: Error setting application state to declined",
				log.Err(err),
			)
			return nil, errors.Wrap(err, "unable to decline application")

		}
		log.Error(
			ctx, "Appetite check failed",
			log.Int64("DOTNumber", request.DotNumber),
			log.String("app id", appID.String()),
		)

		// Log ApplicationDeclined event
		declinedRules, err := events.IndentAndToString(quoting_events.DeclinedRulesForEvents(appetiteResult.DeclinedRules), `"`)
		if err != nil {
			// just log the error
			log.Error(ctx, "unable to marshal declined rules", log.Err(err))
		}

		if err = utils.TriggerUpdateSalesforceOpportunity(
			ctx,
			h.deps.JobberClient,
			h.deps.MetricsClient,
			wrapper.SalesforceEventUpdateApplicationArgs{
				ApplicationID:    appID.String(),
				DeclinedRules:    declinedRules,
				DeclineReason:    salesforceDeclineReason,
				DeclineSubReason: salesforceInactiveStateDeclineSubReason,
				EventName:        event_enums.QuotingApplicationDeclined,
			}); err != nil {
			// just log the error
			log.Error(
				ctx, "failed to update salesforce opportunity",
				log.String("eventName", event_enums.QuotingApplicationDeclined.String()),
				log.Err(err),
			)
		}

		logApplicationEvent(ctx,
			h.deps.AppWrapper,
			h.deps.AuthWrapper,
			h.deps.EventHandler,
			h.deps.AgencyWrapper,
			Declined,
			appID.String(),
			appetiteResult.DeclinedRules,
			yearsInBusiness,
			currentInsurance,
			isRenewal,
			renewalOriginalApplicationID,
			insuranceCarrier,
			isNonAdmittedApp,
		)
		return &ApplicationEvaluation{AppetiteCheckResult: appetiteResult}, nil
	}

	if problems != nil {
		// Log ApplicationFlag event
		problems, err := events.IndentAndToString(quoting_events.AppetiteCheckProblemsForEvents(problems), `\`)
		if err != nil {
			// just log the error
			log.Error(ctx, "unable to marshal problems", log.Err(err))
		}

		if err = utils.TriggerUpdateSalesforceOpportunity(
			ctx,
			h.deps.JobberClient,
			h.deps.MetricsClient,
			wrapper.SalesforceEventUpdateApplicationArgs{
				ApplicationID: appID.String(),
				Flags:         problems,
				EventName:     event_enums.QuotingApplicationFlagged,
			}); err != nil {
			// just log the error
			log.Error(
				ctx, "failed to update salesforce opportunity",
				log.String("eventName", event_enums.QuotingApplicationFlagged.String()),
				log.Err(err),
			)
		}

		logApplicationEvent(
			ctx,
			h.deps.AppWrapper,
			h.deps.AuthWrapper,
			h.deps.EventHandler,
			h.deps.AgencyWrapper,
			Flagged,
			appID.String(),
			nil,
			yearsInBusiness,
			currentInsurance,
			isRenewal,
			renewalOriginalApplicationID,
			insuranceCarrier,
			isNonAdmittedApp,
		)
	}

	flags, err := GetFlagsFromProblems(problems)
	if err != nil {
		log.Error(ctx,
			"HandlePostApplication: Error getting application flags",
			log.Err(err),
		)
		return nil, errors.Wrap(err, "unable to get application flags")

	}

	return &ApplicationEvaluation{EvaluationFlags: flags}, nil
}

type ApplicationCreation struct {
	AgencyId               uuid.UUID
	CompanyName            string
	DotNumber              int64
	HasUndesiredOperations bool
	EffectiveDate          time.Time
	OriginalApplicationID  *string
	IsRenewal              *bool
	ProducerId             *string
	NumberOfPowerUnits     int
	MarketerId             *string
}

type EvaluationFlags struct {
	ClearanceConflict bool
}

type ExistingApplicationMetadata struct {
	ApplicationID string
	State         state_enums.AppState
	IsRenewal     bool
}

type ApplicationEvaluation struct {
	AppetiteCheckResult         *app_chkr.AppetiteCheckerResult
	EvaluationFlags             *EvaluationFlags
	ExistingApplicationMetadata *ExistingApplicationMetadata
}

// ValidateExistingApplication function validates if the requested application is in conflict with an existing application.
func ValidateExistingApplication(
	ctx context.Context,
	appWrapper application.DataWrapper,
	producerId string,
	dotNumber int64,
	agencyId uuid.UUID,
	validStatesForApplicationCreation []state_enums.AppState,
) (*application.Application, error) {
	// Validate if there is an existing application for the same producer and
	// DOT number
	existingApp, err := appWrapper.GetApplicationByProducerIdAndDotNumber(ctx, producerId, dotNumber, agencyId.String())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, errors.Wrap(err, "Unable to fetch existing application")
	}

	if existingApp != nil {
		// If the previous app is declined, we allow the user to create a new application
		if slices.Contains(validStatesForApplicationCreation, existingApp.State) {
			return nil, nil
		}

		// If the effective date has passed for the existing application, we allow the user to create a new application
		currentTime := time.Now().Truncate(24 * time.Hour)
		effectiveDate := existingApp.CoverageInfo.EffectiveDate.Truncate(24 * time.Hour)
		if currentTime.After(effectiveDate) {
			return nil, nil
		}

		return existingApp, nil
	}

	return nil, nil
}

// This function validates if the request contains the necessary information
// for the appetite check.
func validateAppetiteFormFromRest(
	appetiteForm ApplicationCreation,
) error {
	// Validate required info for appetite check
	switch {
	case appetiteForm.DotNumber == 0:
		return errors.New("Missing DOTNumber for appetite check")
	case appetiteForm.CompanyName == "":
		return errors.New("Missing Company Name for appetite check")
	case appetiteForm.EffectiveDate.IsZero():
		return errors.New("Missing Effective Date for appetite check")
	}
	return nil
}

// getStateFromDot returns the us_states.USState for the given dotNumber
func getStateFromDot(
	ctx context.Context,
	fmcsaWrapper fmcsa.DataWrapper,
	dotNumber int64,
) (us_states.USState, error) {
	details, err := fmcsaWrapper.GetDetailsByDot(ctx, dotNumber)
	if err != nil {
		return nil, errors.Wrapf(err, "Unable to get dot details for dotNumber %d", dotNumber)
	}
	if !details.IsValidCensus || details.Census.Name == nil ||
		details.Census.TotalPowerUnits == nil {
		return nil, errors.New("invalid Census of this dot number")
	}

	census := details.Census
	if census.PhysicalAddressState == nil {
		return nil, errors.New("census data missing physical address state")
	}

	usState, err := us_states.StrToUSState(*census.PhysicalAddressState)
	if err != nil {
		return nil, errors.Newf("unable to parse app state %s to us state", *census.PhysicalAddressState)
	}
	return usState, nil
}

// This function populates a new application with the information from the
// appetite check.
// NOTE: Complex structs are initialized with constructors so that they can
// be shown with their appropriate structure in the frontend, improving
// readability.
func populateNewApplicationFromAppetiteForm(
	ctx context.Context,
	fmcsaWrapper fmcsa.DataWrapper,
	appObj *application.Application,
	appetiteForm ApplicationCreation,
) (*application.Application, error) {
	usState, err := getStateFromDot(ctx, fmcsaWrapper, appetiteForm.DotNumber)
	if err != nil {
		return nil, errors.Wrap(err, "unable to fetch state from dot number")
	}
	if usState == nil {
		return nil, errors.Wrap(err, "company info doesn't have valid USState")
	}

	companyInfo := &application.CompanyInfo{
		DOTNumber:          appetiteForm.DotNumber,
		Name:               appetiteForm.CompanyName,
		NumberOfPowerUnits: appetiteForm.NumberOfPowerUnits,
		USState:            usState,
	}

	appObj.CompanyInfo = companyInfo
	appObj.EquipmentInfo = application.NewEquipmentInfo()
	appObj.LossInfo = application.NewLossInfo()
	appObj.CoverageInfo = &application.CoverageInfo{
		EffectiveDate: appetiteForm.EffectiveDate,
	}
	return appObj, nil
}

func getAppetiteCheckerDOTInformation(
	ctx context.Context,
	fmcsaWrapper fmcsa.DataWrapper,
	fetcherClient data_fetching.FetcherClient,
	dotNumber int64,
	now time.Time,
	yib *float64,
) (*app_chkr.DOTInformation, error) {
	dotDetails, err := fmcsaWrapper.GetDetailsByDot(ctx, dotNumber)
	if err != nil && !errors.Is(err, fmcsa.ErrDotNumberNotFound) {
		return nil, errors.Wrap(err, "Unable to fetch dot details")
	}

	startDate := time_utils.DateFromTime(now.AddDate(-5, 0, 0))
	endDate := time_utils.DateFromTime(now)
	carriers, err := lni_processing.GetBIPDUniqueCarriersBetweenV1(
		ctx,
		fetcherClient,
		dotNumber,
		startDate,
		endDate,
	)
	if err != nil {
		log.Warn(
			ctx,
			"failed to get unique BIPD Carriers",
			log.Int64("DOTNumber", dotNumber),
			log.Stringer("StartDate", startDate),
			log.Err(err),
		)
	}

	grantedAuthorityHistory, err := fetcherClient.GetGrantedAuthorityHistoryV1(
		ctx,
		&data_fetching.GrantedAuthorityHistoryRequestV1{DotNumber: dotNumber},
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to get granted authority history",
			log.Int64("DOTNumber", dotNumber),
			log.Err(err),
		)
		return nil, errors.Wrap(err, "failed to get granted authority history")
	}

	involuntaryRevocations, err := fetcherClient.GetInvoluntaryRevocationsV1(
		ctx,
		&data_fetching.InvoluntaryRevocationsRequestV1{
			DotNumber: dotNumber,
			EndDate:   timestamppb.New(endDate.ToTime()),
		})
	if err != nil {
		log.Warn(
			ctx,
			"failed to get involuntary revocations",
			log.Int64("DOTNumber", dotNumber),
			log.Err(err),
		)
		return nil, errors.Wrap(err, "failed to get involuntary revocations")
	}

	return &app_chkr.DOTInformation{
		Details:                dotDetails,
		YearsInBusiness:        yib,
		UniqueBIPDCarriers:     carriers,
		GratedAuthorityHistory: grantedAuthorityHistory.GetRecords(),
		InvoluntaryRevocations: involuntaryRevocations.GetRecords(),
	}, nil
}

func getCreatedBy(ctx context.Context, request ApplicationCreation) string {
	return authz.UserFromContext(ctx).ID.String()
}

func getMarketerId(ctx context.Context, request ApplicationCreation) (*uuid.UUID, error) {
	if request.MarketerId != nil {
		// Parse the string MarketerId from the request to UUID
		marketerID, err := uuid.Parse(*request.MarketerId)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to parse marketer ID: %s", *request.MarketerId)
		}
		return &marketerID, nil
	}
	userID := authz.UserFromContext(ctx).ID
	return &userID, nil
}

func getClearanceStatusAndSendEmail(
	ctx context.Context,
	request ApplicationCreation,
	appObj *application.Application,
	shouldProtectApp bool,
	appWrapper application.DataWrapper,
	clearanceEmailer emails.EmailWrapper,
	policyClient policy.Client,
) (*clearance_enums.ClearanceState, error) {
	clearanceStatus := clearance_enums.ApplicationClearanceIncomplete

	// If we already have an application cleared for the same policy period,
	// we can mark this application as RequiringClearance

	canClear, clearedApplication, err := clearance.CanClearApplication(
		ctx,
		appWrapper,
		request.DotNumber,
		request.EffectiveDate,
		appObj,
	)
	if err != nil {
		log.Error(
			ctx, "HandlePostApplication: Error checking for clearance",
			log.Err(err),
			log.Any("request", request),
		)
		return nil, errors.Wrap(err, "unable to check for clearance")
	}

	if !canClear {

		clearanceStatus = clearance_enums.ApplicationRequiresClearance

		if clearedApplication.ClearanceStatus != nil &&
			*clearedApplication.ClearanceStatus == clearance_enums.ApplicationCleared &&
			clearedApplication.State == state_enums.AppStateDeclined {
			log.Info(ctx, "Marking application as requiring clearance and sending declined email",
				log.String("app_id", appObj.ID))
			err = clearanceEmailer.SendExistingClearedApplicationDeclinedEmail(ctx, *appObj)
			if err != nil {
				log.Error(
					ctx, "HandlePostApplication: Error sending existing cleared application email",
					log.Err(err),
					log.Any("request", request),
				)
				return nil, errors.Wrap(err, "unable to send existing cleared application email")
			}
		} else {
			if clearedApplication.AgencyID == appObj.AgencyID {
				clearanceStatus = clearance_enums.ApplicationRequiresClearance
				log.Info(ctx, "Marking application as requiring clearance and sending email",
					log.String("app_id", appObj.ID))
				err = clearanceEmailer.SendSameAgenyClearanceConflictNewBusinessEmail(ctx, *appObj)
				if err != nil {
					log.Error(
						ctx, "HandlePostApplication: Error sending existing cleared application email",
						log.Err(err),
						log.Any("request", request),
					)
					return nil, errors.Wrap(err, "unable to send existing cleared application email")
				}
			} else {
				log.Info(ctx, "Marking application as requiring clearance and sending email",
					log.String("app_id", appObj.ID))
				err = clearanceEmailer.SendAgentRequiresBORForNewSubmission(ctx, *appObj)
				if err != nil {
					log.Error(
						ctx, "HandlePostApplication: Error sending existing cleared application email",
						log.Err(err),
						log.Any("request", request),
					)
					return nil, errors.Wrap(err, "unable to send existing cleared application email")
				}
			}
		}
	} else {

		// Check if any other application exists with same DOT number in last year
		// which is in the state of policy created
		// If yes, mark this application as RequiringClearance

		if request.ProducerId == nil {
			log.Error(ctx, "HandlePostApplication: ProducerId is required for clearance", log.Any("request", request))
			return nil, errors.New("ProducerId is required for clearance")
		}

		policy, err := clearance.HasActivePolicy(
			ctx,
			appWrapper,
			policyClient,
			request.DotNumber,
			*request.ProducerId,
		)
		if err != nil {
			log.Error(ctx, "HandlePostApplication: Error checking for active policy with different agent", log.Err(err))
			return nil, errors.Wrap(err, "unable to check for active policy with different agent")
		}

		if policy != nil {
			if policy.AgencyID != appObj.AgencyID {
				clearanceStatus = clearance_enums.ApplicationRequiresClearance
				log.Info(ctx, "Marking application as requiring clearance and sending email",
					log.String("app_id", appObj.ID))
				err = clearanceEmailer.SendExistingPolicyDifferentAgencyEmail(ctx, *appObj)
				if err != nil {
					log.Error(
						ctx, "HandlePostApplication: Error sending existing cleared application email",
						log.Err(err),
						log.Any("request", request),
					)
					return nil, errors.Wrap(err, "unable to send existing cleared application email")
				}
			} else {
				clearanceStatus = clearance_enums.ApplicationCleared
			}
		} else {
			if shouldProtectApp {
				clearanceStatus = clearance_enums.ApplicationCleared
			}
		}
	}
	return &clearanceStatus, nil
}

func (h *CreateApplicationHandler) declineApplication(
	ctx context.Context,
	appID uuid.UUID,
	metadata application.StateMetadata,
) error {
	// when transitioning to declined state, we also update the connection's metadata *if necessary*.
	// we can not do both connection and app updates in a single transaction, so we choose to do connection's update
	// first as the update is idempotent to retries, whereas the app update is not.
	app, err := h.deps.AppWrapper.GetAppById(ctx, appID.String())
	if err != nil {
		return errors.Wrap(err, "failed to get application")
	}
	if app.TSPConnHandleId != nil && app.TSPEnum != nil {
		handleId := uuid.MustParse(*app.TSPConnHandleId)
		if handleId != uuid.Nil {
			shouldUpdate, err := shouldUpdateLegalDataPullLimit(
				ctx,
				h.deps.AppWrapper,
				h.deps.PolicyClient,
				handleId,
				app.AgencyID,
			)
			if err != nil {
				return errors.Wrap(
					err,
					"failed to check if should update legal data pull limit")
			}
			log.Info(
				ctx,
				"shouldUpdateLegalDataPullLimit resulted in:",
				log.Bool("evaluation", shouldUpdate),
				log.String("appId", app.ID),
				log.Stringer("handleId", handleId),
				log.Stringer("agencyId", app.AgencyID),
			)
			if shouldUpdate {
				if err := h.deps.TspConnManager.UpdateLegalDataPullLimit(
					ctx,
					handleId,
					null.TimeFrom(metadata.Time),
				); err != nil {
					return errors.Wrap(err, "failed to update legal data pull limit on application decline")
				}
				log.Info(
					ctx,
					"Successfully set data pull legal limit",
					log.Stringer("handleId", handleId),
					log.String("appId", app.ID),
				)
			}
		}
	}
	if metadata.Time.IsZero() {
		metadata.Time = time.Now()
	}
	err = h.deps.AppWrapper.UpdateApp(ctx, appID.String(), func(app application.Application) (application.Application, error) {
		app.State = state_enums.AppStateDeclined
		app.StateMetadata = metadata
		return app, nil
	})
	if err != nil {
		return errors.Wrap(err, "unable to update app")
	}

	return nil
}
