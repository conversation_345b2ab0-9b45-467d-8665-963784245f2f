package app_state_machine

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/cactus/go-statsd-client/v5/statsd"

	"nirvanatech.com/nirvana/quoting/clearance/enums"
	"nirvanatech.com/nirvana/quoting/quote_generator"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/volatiletech/null/v8"

	policyconstants "nirvanatech.com/nirvana/policy/constants"

	applicationutil "nirvanatech.com/nirvana/common-go/application-util"
	authutil "nirvanatech.com/nirvana/common-go/auth-util"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	formenums "nirvanatech.com/nirvana/db-api/db_wrappers/forms/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/jtypes"
	oapiapp "nirvanatech.com/nirvana/openapi-specs/components/application"
	"nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
	"nirvanatech.com/nirvana/policy_common/forms_generator/forms"
	"nirvanatech.com/nirvana/quoting/app_state_machine/app_logic"
	"nirvanatech.com/nirvana/quoting/app_state_machine/cerrors"
	stateenums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	appchkr "nirvanatech.com/nirvana/quoting/appetite_checker"
	"nirvanatech.com/nirvana/quoting/jobs"
	quotingjobs "nirvanatech.com/nirvana/quoting/jobs"
)

// ########### COMMON IMPLs ################
// #########################################

var (
	clearance_error_renewal_app_not_found = "renewal_app_not_found.error"
	successTagKey                         = "success"
	failureTagValue                       = "false"
)

type coverageRejectionReason string

// TODO : Replace these with actual rejection notes from UW app
const (
	glRejectionReason  coverageRejectionReason = "General Liability was not included due to unacceptable exposure: Locations accessible to the public, Customer performing repair work for 3rd parties"
	apdRejectionReason coverageRejectionReason = "Auto Physical Damage was not included due to unacceptable exposure: Average age of owned equipment"
)

func (a *appStateMachineHelper) setPanic(ctx context.Context, metadata app.StateMetadata) error {
	err := a.deps.AppWrapper.UpdateApp(ctx, a.appId, func(app app.Application) (app.Application, error) {
		app.State = stateenums.AppStatePanic
		app.StateMetadata = metadata
		return app, nil
	})
	if err != nil {
		return errors.Wrap(err, "unable to update app state into panic")
	}
	return nil
}

func (a *appStateMachineHelper) updateIndicationFormAndMoveToUnsubmitted(ctx context.Context, expectedState stateenums.AppState,
	form *oapiapp.IndicationForm, perStateCheckFn func(app app.Application) error,
) error {
	// Updated app in DB
	err := a.deps.AppWrapper.UpdateApp(ctx,
		a.appId,
		func(app app.Application) (app.Application, error) {
			if perStateCheckFn != nil {
				if err := perStateCheckFn(app); err != nil {
					return app, errors.Wrap(err, cerrors.PerStateCheckFailed)
				}
			}

			// TODO: Consider using state_uuid equivalent for stricter checks
			if app.State != expectedState {
				return app, errors.Wrapf(cerrors.ErrActionNotValid,
					"unexpected state: %s. Expected %s", app.State, expectedState)
			}

			err := a.bindIndicationFormFromRestToDB(form, &app)
			if err != nil {
				return app, errors.Wrap(err, "unable to bind indication form from REST to db")
			}

			app.PackageType = nil
			app.IndicationSubmissionID = nil
			app.UwSubmissionID = nil
			app.BindableSubmissionID = nil
			app.State = stateenums.AppStateUnsubmitted

			if app.ProducerID != nil {
				logProducerAddedEvent(ctx, app, a.deps.AuthWrapper, a.deps.AgencyWrapper, a.deps.EventHandler)
			}

			return app, nil
		})
	// TODO: Handle DB errors separately once we define error type
	if err != nil {
		return errors.Wrap(err, "Unable to update app")
	}
	return nil
}

func (a *appStateMachineHelper) updateRenewalsFieldsAndMoveToUnsubmitted(ctx context.Context, expectedState stateenums.AppState,
	form *oapiapp.PatchRenewalApplicationForm, perStateCheckFn func(app app.Application) error,
) error {
	// Updated app in DB
	err := a.deps.AppWrapper.UpdateApp(ctx,
		a.appId,
		func(app app.Application) (app.Application, error) {
			if perStateCheckFn != nil {
				if err := perStateCheckFn(app); err != nil {
					return app, errors.Wrap(err, cerrors.PerStateCheckFailed)
				}
			}

			// TODO: Consider using state_uuid equivalent for stricter checks
			if app.State != expectedState {
				return app, errors.Wrapf(cerrors.ErrActionNotValid,
					"unexpected state: %s. Expected %s", app.State, expectedState)
			}

			_, err := a.bindRenewalFormFromRestToDB(form, &app)
			if err != nil {
				return app, errors.Wrap(err, "unable to bind renewals form from REST to db")
			}

			app.IndicationSubmissionID = nil
			app.UwSubmissionID = nil
			app.BindableSubmissionID = nil
			app.State = stateenums.AppStateUnsubmitted

			return app, nil
		})
	// TODO: Handle DB errors separately once we define error type
	if err != nil {
		return errors.Wrap(err, "Unable to update app")
	}
	return nil
}

func (a *appStateMachineHelper) updateAddlInfoFormInUnsubmittedState(
	ctx context.Context,
	expectedState stateenums.AppState,
	form *oapiapp.AdditionalInformationForm,
	perStateCheckFn func(app app.Application) error,
) error {
	// Updated app in DB
	err := a.deps.AppWrapper.UpdateApp(ctx,
		a.appId,
		func(app app.Application) (app.Application, error) {
			if perStateCheckFn != nil {
				if err := perStateCheckFn(app); err != nil {
					return app, errors.Wrap(err, cerrors.PerStateCheckFailed)
				}
			}

			if app.State != expectedState {
				return app, errors.Wrapf(cerrors.ErrActionNotValid, "unexpected state: %s. Expected %s",
					app.State.String(), expectedState.String())
			}

			appPtr, err := a.bindAddlInfoFromRestToDb(*form, &app)
			if err != nil {
				return app, errors.Wrap(err, "unable to bind addl info from REST to db")
			}
			return *appPtr, nil
		})
	if err != nil {
		return errors.Wrap(err, "unable to update app")
	}
	return nil
}

func (a *appStateMachineHelper) updateAddlInfoFormAndMoveToIndicationGenerated(ctx context.Context, expectedState stateenums.AppState,
	form *oapiapp.AdditionalInformationForm, perStateCheckFn func(app app.Application) error,
) error {
	// Updated app in DB
	err := a.deps.AppWrapper.UpdateApp(ctx,
		a.appId,
		func(app app.Application) (app.Application, error) {
			if perStateCheckFn != nil {
				if err := perStateCheckFn(app); err != nil {
					return app, errors.Wrap(err, cerrors.PerStateCheckFailed)
				}
			}

			// TODO: Consider using state_uuid equivalent for stricter checks
			if app.State != expectedState {
				return app, errors.Wrapf(cerrors.ErrActionNotValid, "unexpected state: %s. Expected %s",
					app.State.String(), expectedState.String())
			}

			// Block update if there isn't a submission or indication option selected
			// or if package type selection is missing
			sub, err := a.deps.AppWrapper.GetSubmissionById(ctx, *app.IndicationSubmissionID)
			if err != nil {
				return app, errors.Wrap(cerrors.ErrActionNotValid, "unable to get submission by id")
			}
			_, err = a.deps.AppWrapper.GetIndOptionById(ctx, sub.SelectedIndicationID)
			if err != nil {
				return app, errors.Wrapf(cerrors.ErrActionNotValid, "unable to get ind option by id %s",
					sub.SelectedIndicationID)
			}
			if app.PackageType == nil {
				return app, errors.Wrapf(cerrors.ErrActionNotValid, "app %s missing package type", app.ID)
			}

			appPtr := &app
			appPtr, err = a.bindAddlInfoFromRestToDb(*form, appPtr)
			if err != nil {
				return app, errors.Wrap(err, "unable to bind addl info from REST to db")
			}

			app.State = stateenums.AppStateIndicationGenerated

			return app, nil
		})
	// TODO: Wrap DB errors as internal when we migrate errors from wrappers
	if err != nil {
		return errors.Wrap(err, "unable to update app")
	}
	return nil
}

func (a *appStateMachineHelper) updateApplicationBasicInfoFields(
	ctx context.Context,
	expectedState stateenums.AppState,
	form *oapiapp.ApplicationBasicInfoForm,
	perStateCheckFn func(app app.Application) error,
) error {
	if form == nil {
		return errors.New("basic info fields form is nil")
	}
	// Update Fields in application sent through BasicInformationForm
	err := a.deps.AppWrapper.UpdateApp(ctx,
		a.appId,
		func(app app.Application) (app.Application, error) {
			if perStateCheckFn != nil {
				if err := perStateCheckFn(app); err != nil {
					return app, errors.Wrap(err, cerrors.PerStateCheckFailed)
				}
			}
			if app.State != expectedState {
				return app, errors.Wrapf(cerrors.ErrActionNotValid,
					"unexpected state: %s. Expected %s", app.State, expectedState)
			}
			appPtr := &app
			appPtr, err := a.bindBasicInfoFieldsFromRestToDB(ctx, *form, appPtr)
			if err != nil {
				return app, errors.Wrap(err, "unable to bind basic info from REST to db")
			}

			return app, nil
		})
	if err != nil {
		return errors.Wrap(err, "unable to update app")
	}
	return nil
}

func (a *appStateMachineHelper) finalizeIndicationOptionAndMoveToIndicationGenerated(
	ctx context.Context,
	expectedState stateenums.AppState, indicationOptionId string, perStateCheckFn func(app app.Application) error,
) error {
	// Updated app in DB
	appObj, err := a.loadApp(ctx)
	if err != nil {
		return errors.Wrap(err, "unable to load app")
	}

	err = a.deps.AppWrapper.UpdateSub(ctx, *appObj.IndicationSubmissionID,
		func(sub app.SubmissionObject) (app.SubmissionObject, error) {
			found := false
			for _, optionId := range sub.IndicationOptionsIDs {
				if optionId == indicationOptionId {
					found = true
					break
				}
			}

			if !found {
				return sub, errors.Newf(
					cerrors.SubNoLongerValid+": provided indication option id %s doesn't belong to latest submission %s",
					indicationOptionId, *appObj.IndicationSubmissionID,
				)
			}

			sub.SelectedIndicationID = indicationOptionId
			return sub, nil
		})
	if err != nil {
		return errors.Wrap(err, "unable to update submission")
	}

	err = a.deps.AppWrapper.UpdateApp(ctx, a.appId, func(app app.Application) (app.Application, error) {
		if perStateCheckFn != nil {
			if err := perStateCheckFn(app); err != nil {
				return app, errors.Wrap(err, cerrors.PerStateCheckFailed)
			}
		}

		// TODO: Consider using state_uuid equivalent for stricter checks
		if app.State != expectedState {
			return app, errors.Wrapf(cerrors.ErrActionNotValid, "unexpected state: %s. Expected %s",
				app.State.String(), expectedState.String())
		}

		indicationOption, err := a.loadIndicationOption(ctx, indicationOptionId)
		if err != nil {
			return app, errors.Wrap(err, "unable to load indication option")
		}
		app.PackageType = &indicationOption.OptionTag
		app.State = stateenums.AppStateIndicationGenerated

		return app, nil
	})
	if err != nil {
		return errors.Wrap(err, "unable to update app")
	}
	return nil
}

func (a *appStateMachineHelper) rollbackToUnderUwReview(ctx context.Context, metadata app.StateMetadata) error {
	appReview, err := a.loadAppReviewFromLastUWSub(ctx)
	if err != nil {
		return errors.Wrap(err, "unable to load app review from bindable sub")
	}
	subInfo := appReview.BindableSubInfo
	err = a.deps.AppReviewWrapper.UpdateBindableSubInfo(ctx,
		appReview.Id,
		func(input uw.BindableSubInfo) (uw.BindableSubInfo, error) {
			input.SubmissionID = ""
			input.JobRunId = nil
			return input, nil
		},
	)
	if err != nil {
		return errors.Wrap(err, "unable to update bindable sub info")
	}

	err = a.deps.AppWrapper.UpdateApp(ctx, a.appId, func(app app.Application) (app.Application, error) {
		app.StateMetadata = metadata
		app.State = stateenums.AppStateUnderUWReview
		return app, nil
	})
	if err != nil {
		errUpdatingBindableSubInfo := a.deps.AppReviewWrapper.UpdateBindableSubInfo(ctx,
			appReview.Id,
			func(input uw.BindableSubInfo) (uw.BindableSubInfo, error) {
				input.SubmissionID = subInfo.SubmissionID
				input.JobRunId = subInfo.JobRunId
				return input, nil
			},
		)
		if errUpdatingBindableSubInfo != nil {
			return errors.Wrap(
				errUpdatingBindableSubInfo,
				"unable to reset bindable sub info to original when rollback was aborted",
			)
		}
		return errors.Wrap(err, "unable to update app")
	}

	if len(metadata.Description) != 0 {
		err = a.deps.AppReviewWrapper.UpdateAppReview(ctx, appReview.Id, func(appReview uw.ApplicationReview) (uw.ApplicationReview, error) {
			appReview.EndStateReasons = &uw.EndStateReasons{
				RollbackReasons: &uw.RollbackReasonsStruct{Comments: metadata.Description},
			}

			return appReview, nil
		})
		if err != nil {
			return errors.Wrap(err, "Unable to update app review with rollback reasons")
		}
	} else {
		return errors.Wrap(err, "Unable to update app review without rollback reasons")
	}

	return nil
}

func (a *appStateMachineHelper) setDeclined(ctx context.Context, metadata app.StateMetadata) error {
	// when transitioning to declined state, we also update the connection's metadata *if necessary*.
	// we can not do both connection and app updates in a single transaction, so we choose to do connection's update
	// first as the update is idempotent to retries, whereas the app update is not.
	appl, err := a.deps.AppWrapper.GetAppById(ctx, a.appId)
	if err != nil {
		return errors.Wrap(err, "failed to get application")
	}
	if appl.TSPConnHandleId != nil && appl.TSPEnum != nil {
		handleId := uuid.MustParse(*appl.TSPConnHandleId)
		if handleId != uuid.Nil {
			shouldUpdate, err := shouldUpdateLegalDataPullLimit(
				ctx,
				a.deps.AppWrapper,
				a.deps.PolicyClient,
				handleId,
				appl.AgencyID,
			)
			if err != nil {
				return errors.Wrap(
					err,
					"failed to check if should update legal data pull limit")
			}
			log.Info(
				ctx,
				"shouldUpdateLegalDataPullLimit resulted in:",
				log.Bool("evaluation", shouldUpdate),
				log.String("appId", appl.ID),
				log.Stringer("handleId", handleId),
				log.Stringer("agencyId", appl.AgencyID),
			)
			if shouldUpdate {
				if err := a.deps.TspConnManager.UpdateLegalDataPullLimit(
					ctx,
					handleId,
					null.TimeFrom(metadata.Time),
				); err != nil {
					return errors.Wrap(err, "failed to update legal data pull limit on application decline")
				}
				log.Info(
					ctx,
					"Successfully set data pull legal limit",
					log.Stringer("handleId", handleId),
					log.String("appId", appl.ID),
				)
			}
		}
	}
	err = a.deps.AppWrapper.UpdateApp(ctx, a.appId, func(app app.Application) (app.Application, error) {
		app.State = stateenums.AppStateDeclined
		app.StateMetadata = metadata
		return app, nil
	})
	if err != nil {
		return errors.Wrap(err, "unable to update app")
	}
	return nil
}

func (a *appStateMachineHelper) beginQuoteGeneration(ctx context.Context,
	expectedState stateenums.AppState, perStateCheckFn func(app app.Application) error,
	bindable bool, updateSubFn func(sub app.SubmissionObject) (app.SubmissionObject, error),
) (string, error) {
	subId := uuid.New().String()
	isFirstTimeApplicationSubmission := true
	ctx = log.ContextWithFields(ctx, log.String("applicationID", a.appId))
	err := a.deps.AppWrapper.InsertSubmissionFromApplicationSnapshot(ctx,
		a.appId, subId, bindable,
		func(sub app.SubmissionObject, app app.Application) (
			app.SubmissionObject, app.Application, error,
		) {
			if err := a.validateAppForQuoteSubmission(app); err != nil {
				return sub, app, errors.Wrap(err, "app validation for quote submission failed")
			}

			if perStateCheckFn != nil {
				if err := perStateCheckFn(app); err != nil {
					return sub, app, errors.Wrap(err, cerrors.PerStateCheckFailed)
				}
			}

			if app.State != expectedState {
				return sub, app, errors.Wrapf(cerrors.ErrActionNotValid,
					"unexpected state %v. Expected %v", app.State, expectedState)
			}
			if app.UwSubmissionID != nil {
				isFirstTimeApplicationSubmission = false
			}
			sub.EffectiveDateTo = pointer_utils.Time(
				time_utils.AddYears(sub.CoverageInfo.EffectiveDate, app_logic.DefaultPolicyDurationInYears),
			)
			return sub, app, nil
		})
	if err != nil {
		return "", errors.Wrap(err, "unable to insert sub from app snapshot")
	}

	// Update submission
	if updateSubFn != nil {
		err = a.deps.AppWrapper.UpdateSub(ctx, subId, updateSubFn)
		if err != nil {
			return "", errors.Wrap(err, "unable to update sub from with updateSubFn")
		}
	}

	shouldSendEmail, err := shouldSendSubAckEmail(ctx, a.deps, a.appId, isFirstTimeApplicationSubmission)
	if err != nil {
		log.Error(ctx, "failed to determine if SubmissionAcknowledgementEmail should be sent", log.Err(err))
		return "", errors.Wrap(err, "failed to determine if SubmissionAcknowledgementEmail should be sent")
	}
	if shouldSendEmail {
		if err = triggerSendSubmissionAcknowledgementEmailJob(ctx, a.deps, a.appId); err != nil {
			log.Error(ctx, "failed to trigger SendSubmissionAcknowledgementEmailJob", log.Err(err))
			return "", errors.Wrap(err, "failed to trigger SendSubmissionAcknowledgementEmailJob")
		}
	} else {
		log.Warn(ctx, "skipping triggering SendSubmissionAcknowledgementEmail job")
	}
	var appReviewId string
	if bindable {
		appReview, err := a.deps.AppReviewWrapper.GetLatestPendingReview(ctx, a.appId)
		if err != nil {
			return "", errors.Wrap(err, "unable to get latest pending review")
		}
		appReviewId = appReview.Id
	}

	jobRunId, err := a.addJobRun(ctx, subId, appReviewId, bindable)
	if err != nil {
		return "", errors.Wrap(err, "unable to add job run")
	}

	err = a.deps.AppWrapper.UpdateApp(ctx, a.appId, func(app app.Application) (app.Application, error) {
		if bindable {
			app.BindableSubmissionID = &subId
		} else {
			app.UwSubmissionID = &subId
		}
		return app, nil
	})
	if err != nil {
		return "", errors.Wrap(err, "unable to update app with submission ID")
	}

	// Updated sub in DB
	err = a.deps.AppWrapper.UpdateSub(ctx, subId, func(sub app.SubmissionObject) (app.SubmissionObject, error) {
		sub.JobRunId = jobRunId
		return sub, nil
	})
	if err != nil {
		return "", errors.Wrap(err, "unable to update submission and store job run")
	}
	return subId, nil
}

func (a *appStateMachineHelper) addJobRun(
	ctx context.Context,
	subId, reviewId string,
	bindable bool,
) (*jtypes.JobRunId, error) {
	if bindable {
		jobRunId, err := a.deps.Jobber.AddJobRun(ctx, jobber.NewAddJobRunParams(
			jobs.GenerateQuoteWorkflow,
			&jobs.GenerateQuoteWorkflowMessage{
				SubmissionID: subId,
				AppReviewID:  reviewId,
				IndicationOptionSpecs: []jobs.IndicationOptionSpec{
					{
						ID:          uuid.New(),
						PackageType: appenums.IndicationOptionTagBasic,
					},
					{
						ID:          uuid.New(),
						PackageType: appenums.IndicationOptionTagStandard,
					},
					{
						ID:          uuid.New(),
						PackageType: appenums.IndicationOptionTagComplete,
					},
				},
			},
			jtypes.NewMetadata(jtypes.Immediate),
		))
		if err != nil {
			return nil, errors.Wrap(err, "unable to add job run")
		}
		return &jobRunId, nil
	}
	subIdUUID, err := uuid.Parse(subId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse subId %s", subId)
	}
	jobRunId, err := a.deps.Jobber.AddJobRun(ctx, jobber.NewAddJobRunParams(
		jobs.GenerateSubmissionForUWWorkflow,
		&jobs.IndicationWorkflowMessage{
			SubmissionID: subIdUUID,
			IndicationOptionSpecs: []jobs.IndicationOptionSpec{
				{
					ID:          uuid.New(),
					PackageType: appenums.IndicationOptionTagBasic,
				},
				{
					ID:          uuid.New(),
					PackageType: appenums.IndicationOptionTagStandard,
				},
				{
					ID:          uuid.New(),
					PackageType: appenums.IndicationOptionTagComplete,
				},
			},
		},
		jtypes.NewMetadata(jtypes.Immediate)),
	)
	if err != nil {
		return nil, errors.Wrap(err, "unable to add job run")
	}
	return &jobRunId, nil
}

func shouldSendSubAckEmail(
	ctx context.Context, deps ASMDeps, appID string, isFirstTimeApplicationSubmission bool,
) (bool, error) {
	application, err := deps.AppWrapper.GetAppById(ctx, appID)
	if err != nil {
		log.Error(ctx, "failed to GetAppById")
		return false, errors.Wrapf(err, "failed to GetAppById for %s", appID)
	}
	creator, err := authutil.GetUserFromUserID(ctx, deps.AuthWrapper, application.MarketerID.String())
	if err != nil {
		log.Error(ctx, "failed to Get creator user", log.Err(err))
		return false, errors.Wrap(err, "failed to Get creator user")
	}

	shouldProcessEmail, err := applicationutil.ShouldProcessEmail(
		ctx, deps.FeatureFlag, application, creator, feature_flag_lib.FeatureSubmissionAcknowledgementEmail,
	)
	if err != nil {
		log.Error(ctx, "failed to determine if the email should be processed further", log.Err(err))
		return false, errors.Wrap(err, "failed to determine if the email should be processed further")
	}

	log.Info(
		ctx, "logging parameters for determining if submission acknowledgement email should be sent",
		log.Bool("isFirstTimeApplicationSubmission", isFirstTimeApplicationSubmission),
		log.Bool("shouldProcessEmail", shouldProcessEmail),
	)
	return isFirstTimeApplicationSubmission && shouldProcessEmail, nil
}

func triggerSendSubmissionAcknowledgementEmailJob(
	ctx context.Context, deps ASMDeps, appID string,
) error {
	appUUID, err := uuid.Parse(appID)
	if err != nil {
		return errors.Wrap(err, "failed to parse appID")
	}
	creatorUUID := uuid.NewSHA1(appUUID, []byte(jobs.SendSubmissionAcknowledgmentEmail))
	addJobRunParams := jobber.NewAddJobRunParams(
		jobs.SendSubmissionAcknowledgmentEmail,
		&jobs.SendSubmissionAcknowledgmentEmailArgs{ApplicationID: appID},
		jtypes.NewMetadata(jtypes.Immediate),
	).WithCreatorUUID(creatorUUID)
	jobRunId, err := deps.Jobber.AddJobRun(ctx, addJobRunParams)
	if err != nil {
		alreadyExistsErr := jtypes.ErrJobRunAlreadyExists
		if errors.Is(err, alreadyExistsErr) {
			log.Warn(ctx, "job already exists", log.Err(err), log.Stringer("existingJobRunID", jobRunId))
			return nil
		}
		log.Error(ctx, "failed to add SendSubmissionAcknowledgmentEmail job", log.Err(err),
			log.Any("addJobRunParams", addJobRunParams),
		)
		return errors.Wrap(err, "failed to add SendSubmissionAcknowledgmentEmail job")
	}
	log.Info(ctx, "Added SendSubmissionAcknowledgmentEmail job", log.Stringer("JobRunId", jobRunId))
	return nil
}

func (a *appStateMachineHelper) getQuote(ctx context.Context) (*oapiapp.QuoteDetails, error) {
	appObj, err := a.loadApp(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to load app")
	}
	applicationSummary, err := a.bindApplicationSummaryFromDbToRest(ctx, appObj)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind app summary from db to REST")
	}
	sub, err := a.loadSub(ctx, appObj.BindableSubmissionID)
	if err != nil {
		return nil, errors.Wrap(err, "unable to load bindable sub")
	}
	option, err := a.loadIndicationOption(ctx, sub.SelectedIndicationID)
	if err != nil {
		return nil, errors.Wrap(err, "unable to load selected option for bindable sub")
	}
	if err = a.validateQuoteForForms(ctx, appObj); err != nil {
		return nil, errors.Wrap(err, "validating quote for forms failed")
	}
	var f forms.FormSet
	var docSummary oapiapp.DocumentSummary
	sigPacketId := sub.SignaturePacketFormID
	if sigPacketId != nil {
		sigId := *sigPacketId
		sigPacket, err := a.deps.FormsGenerator.GetFormsCompilation(ctx, sigId)
		if err != nil {
			return nil, errors.Wrap(err, "Couldn't fetch signature packet")
		}
		if sigPacket == nil {
			return nil, errors.New("signature packet nil")
		}
		metadata := sigPacket.Metadata()
		if metadata.CoverageIdsMap == nil {
			return nil, errors.New("signature packet missing coverage ids map")
		}
		coverageIdMaps := *metadata.CoverageIdsMap
		// Iterate over the coverage map and gather their ids
		b := forms.NewFormSet()
		// Iterate over the coverage map and append all the forms
		for coverage, coverageId := range coverageIdMaps {
			// we don't generate a separate policy for auto physical damage,
			// but instead we club it together with auto liability policy.
			// Our FE also uses this info to reflect if APD is also added or not
			// in the UI.
			if coverage == appenums.CoverageAutoPhysicalDamage {
				continue
			}
			if coverageId == nil {
				return nil,
					errors.Newf("signature packet coverage id map is nil for coverage %s", coverage)
			}
			formComp, err := a.deps.FormsWrapper.GetFormCompilationById(ctx, *coverageId)
			if err != nil {
				return nil, errors.Wrap(err, "Couldn't fetch form compilation")
			}
			if formComp == nil {
				return nil, errors.New("form compilation nil")
			}
			b.Add((*formComp).FlattenAllForms().ToSlice()...)

		}

		f = b
		// ensure that the signature packet is in released state to ensure that
		// we don't release the sig packet for apps which haven't been
		// released through the new forms tool
		if sigPacket.CompilationState().Matches(
			formenums.FormCompilationStateReleased,
			formenums.FormCompilationStateBound,
		) {
			if sigPacket.DocumentHandleId() != nil {
				docSummary = oapiapp.DocumentSummary{
					SignaturePacketHandleId: sigPacket.DocumentHandleId().String(),
				}
				if sigPacket.Metadata().ZipHandleId != nil {
					docSummary.SignaturePacketZipHandleId = pointer_utils.String(sigPacket.Metadata().ZipHandleId.String())
				}
			}
		}

	}

	acceptedCovs := a.bindCoveragesFromDbToRest(option.Coverages)
	covDetails := oapiapp.CoverageDetails{ApprovedCoverages: &acceptedCovs}
	if sub.CoverageInfo != nil && sub.CoverageInfo.CoveragesWithCombinedDeductibles != nil {
		covDetails.CoveragesWithCombinedDeductibles = a.bindCombinedCoveragesFromDBToRest(
			sub.CoverageInfo.CoveragesWithCombinedDeductibles,
		)
	}
	covDetails.RejectedCoverages = &[]oapiapp.RejectedCoverage{}
	subObj, err := a.loadSub(ctx, appObj.UwSubmissionID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to load last submission for id %s", appObj.UwSubmissionID)
	}
	appReview, err := a.deps.AppReviewWrapper.GetReviewFromSub(ctx, subObj.ID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to load review from sub %s", sub.ID)
	}
	if appReview.CoverageReview.AutoPhysicalDamage != nil {
		if appReview.CoverageReview.AutoPhysicalDamage.State == uw.ApplicationReviewCoverageStateDeclined {
			// This means it was submitted and then declined
			*covDetails.RejectedCoverages = append(*covDetails.RejectedCoverages, oapiapp.RejectedCoverage{
				CoverageType: common.CoverageAutoPhysicalDamage,
				Reason:       string(apdRejectionReason),
			})
		}
	}
	if appReview.CoverageReview.GeneralLiability != nil {
		if appReview.CoverageReview.GeneralLiability.State == uw.ApplicationReviewCoverageStateDeclined {
			// This means it was submitted and then declined
			*covDetails.RejectedCoverages = append(*covDetails.RejectedCoverages, oapiapp.RejectedCoverage{
				CoverageType: common.CoverageGeneralLiability,
				Reason:       string(glRejectionReason),
			})
		}
	}

	formsSlice := f.ToSlice()
	sort.Sort(forms.ByOrderCategory(formsSlice))
	formsForQuote, err := a.bindFormsFromDbToRest(formsSlice)
	if err != nil {
		return nil, errors.Wrap(err, "unable to bind forms")
	}
	quote := &oapiapp.QuoteDetails{
		AppSummary:      *applicationSummary,
		CoverageDetails: covDetails,
		FormsDetails:    formsForQuote,
		// TODO: implement payment options
		PaymentOptions:  nil,
		PremiumDetails:  a.bindPremiumDetailsFromIndOptionDbToRest(*option),
		QuoteSummary:    a.bindQuoteSummaryFromDbToRest(*option, *sub),
		DocumentSummary: docSummary,
	}

	quote.PremiumDetails.TotalSurplusLinesTax = quote_generator.GetTotalSurplusLinesTax(*option)
	quote.PremiumDetails.TotalStampingFee = quote_generator.GetTotalStampingFee(*option)

	return quote, nil
}

func (a *appStateMachineHelper) createPoliciesAndMoveToPolicyCreated(
	ctx context.Context,
	expectedState stateenums.AppState,
	nextState stateenums.AppState,
) (*[]oapiapp.PolicyDetails, error) {
	args, err := a.getCreatePolicyArgs(ctx)
	if err != nil {
		log.Error(ctx, "failed to get createPolicy args", log.Err(err))
		return nil, errors.Wrap(err, "failed to get createPolicy args")
	}

	// Create policy set
	policysetId, err := a.createPolicySet(ctx, args.appObj, args.subObj, args.indOpt, args.policySetIdentifier)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to create policy set for appID %v ", args.appObj.ID)
	}
	if policysetId == nil {
		return nil, errors.Newf("failed to obtain Policy set id for appIdd %v", args.appObj.ID)
	}
	args.policySetId = *policysetId

	var policies []oapiapp.PolicyDetails
	for _, cov := range args.subObj.CoverageInfo.Coverages {
		if slice_utils.Contains[appenums.Coverage](policyconstants.CoveragesWithPolicies, cov.CoverageType) {
			args.coverage = cov.CoverageType
			policyDetails, err := a.createPolicy(ctx, args)
			if err != nil {
				return nil, errors.Wrapf(err, "unable to create policy for appID %v with coverage %v", args.appObj.ID,
					cov.CoverageType)
			}
			policies = append(policies, *policyDetails)
		}
	}

	if len(policies) == 0 {
		return nil, errors.Newf("no valid coverages found for appID: %v", args.appObj.ID)
	}

	if err = a.upsertIB(ctx, args); err != nil {
		log.Error(ctx, "failed to upsert insurance bundle", log.Err(err))
		metricsClientErr := a.deps.MetricsClient.Inc("fleet_upsert_ib_failure", 1, 1.0)
		if metricsClientErr != nil {
			log.Error(ctx, "failed to increment fleet_upsert_ib_failure metric", log.Err(metricsClientErr))
		}
		// TODO: Uncomment the error return and start returning an error post fleet IB migration launch.
		// return nil, errors.Wrapf(err, "unable to upsert insurance bundle for appID: %s", args.appObj.ID)
	}

	// Update the state of the signature packet associated with the app
	sigPacket := args.subObj.SignaturePacketFormID
	//if sigPacket == nil {
	//	return nil, errors.Newf("No signature packet found for appID: %v", appObj.ID)
	//}
	// TODO remove the check above once the tests refactoring is done
	if sigPacket != nil {
		err = a.deps.FormsWrapper.UpdateFormCompilation(ctx, *sigPacket,
			func(formComp compilation.FormsCompilationImpl) (*compilation.FormsCompilationImpl, error) {
				// check if the form compilation which is being updated is a signature packet
				if formComp.FormType == nil {
					return nil, errors.Newf("form compilation with id %v has type nil", *sigPacket)
				}
				if *formComp.FormType != compilation.CompilationTypeSignaturePacket {
					return nil, errors.Newf("form compilation with id %v is not a signature packet, type %s",
						*sigPacket, formComp.FormType.String())
				}
				// we can't bind a signature packet to a policy if it's not in the released state
				if !formComp.State.Matches(formenums.FormCompilationStateReleased) {
					return nil,
						errors.Newf("signature packet is not in released state, state %s id %s",
							formComp.State.String(), formComp.ID)
				}
				formComp.State = formenums.FormCompilationStateBound
				return &formComp, nil
			},
		)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to update compilation with id %v", *sigPacket)
		}
	}
	err = a.deps.AppWrapper.UpdateApp(ctx, args.appObj.ID, func(appObj app.Application) (app.Application, error) {
		if appObj.State != expectedState {
			return appObj, errors.Wrapf(cerrors.ErrActionNotValid, "Unexpected state %v. Expected %v",
				appObj.State, expectedState)
		}
		appObj.State = nextState
		return appObj, nil
	})
	if err != nil {
		// TODO: Handle Rollback in case State Transition fails
		// Ideally we would want to do this in a single DB Transaction itself
		return nil, errors.Wrapf(err, cerrors.DbPersistingErr+": Unable to update app")
	}

	return &policies, nil
}

func (a *appStateMachineHelper) releaseQuote(
	ctx context.Context,
	expectedState, nextState stateenums.AppState,
	compilationID uuid.UUID,
) error {
	appObj, err := a.loadApp(ctx)
	if err != nil {
		return errors.Wrap(err, "unable to load app")
	}
	if appObj.BindableSubmissionID == nil {
		return errors.New("bindable submission id is needed to release a quote")
	}
	// Get the form compilation
	comp, err := a.deps.FormsGenerator.GetFormsCompilation(ctx, compilationID)
	if err != nil {
		return errors.Wrap(err, "unable to fetch compilatin")
	}
	// determine whether the compilation is signature packet
	// or a policy
	if comp.Type() == nil {
		return errors.Newf("compilation type is nil %s", compilationID)
	}
	compilationType := *comp.Type()
	state := comp.CompilationState()
	// only compilations which are in frozen state can be released
	if state.Matches(formenums.FormCompilationStateFilled, formenums.FormCompilationStateInitialized) {
		return errors.Newf("compilation state %s is not in a valid to release %s", state, compilationID)
	}
	if compilationType == compilation.CompilationTypeSignaturePacket {
		// if the compilation type is signature packet then we add its id
		// to bindable submission and update app state
		subId := comp.Metadata().SubmissionId
		if subId == nil {
			return errors.Newf("submission id is nil for the compilation %s", compilationID)
		}
		submissionID := (*subId).String()
		if err = a.deps.AppWrapper.UpdateSub(
			ctx,
			submissionID,
			func(object app.SubmissionObject) (app.SubmissionObject, error) {
				object.SignaturePacketFormID = pointer_utils.UUID(compilationID)
				return object, nil
			},
		); err != nil {
			return errors.Wrap(err, "unable to update submission")
		}
		if err = a.deps.AppWrapper.UpdateApp(
			ctx,
			a.appId,
			func(appObj app.Application) (app.Application, error) {
				if appObj.State != expectedState {
					return appObj, errors.Wrapf(cerrors.ErrActionNotValid, "Unexpected state %v. Expected %v",
						appObj.State, expectedState)
				}
				appObj.State = nextState
				// Update the bindable submission id
				appObj.BindableSubmissionID = &submissionID
				return appObj, nil
			},
		); err != nil {
			return errors.Wrap(err, "unable to update application")
		}
		// update the state of the compilation to released
		err = a.deps.FormsWrapper.UpdateFormCompilation(
			ctx,
			compilationID,
			func(formComp compilation.FormsCompilationImpl) (
				*compilation.FormsCompilationImpl, error,
			) {
				formComp.State = formenums.FormCompilationStateReleased
				formComp.FormMetadata.ReleasedDate = pointer_utils.Time(time.Now())
				return &formComp, nil
			},
		)
		if err != nil {
			return errors.Wrapf(err, "unable to update form compilation %s state to released", compilationID.String())
		}
	} else {
		return errors.Newf("compilation type %s is not supported", compilationType)
	}
	return nil
}

// Deprecated: This method is deprecated and should not be used.
func (a *appStateMachineHelper) createRenewalApplication(ctx context.Context, appId string) (*string, *appchkr.AppetiteCheckerResult, error) {
	originalApp, err := a.deps.AppWrapper.GetAppById(ctx, appId)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to get app by id %s", appId)
	}
	if originalApp.State != stateenums.AppStatePolicyCreated {
		return nil, nil, errors.Newf("app state %s is not  AppStatePolicyCreated", originalApp.State)
	}

	bindableSub, err := a.deps.AppWrapper.GetSubmissionById(ctx, *originalApp.BindableSubmissionID)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to get bindable submission by id %s", *originalApp.BindableSubmissionID)
	}
	request := ApplicationCreation{
		AgencyId:              originalApp.AgencyID,
		CompanyName:           originalApp.CompanyInfo.Name,
		DotNumber:             originalApp.CompanyInfo.DOTNumber,
		EffectiveDate:         bindableSub.CoverageInfo.EffectiveDate.AddDate(1, 0, 0),
		OriginalApplicationID: &appId,
		IsRenewal:             pointer_utils.Bool(true),
		ProducerId:            originalApp.ProducerID,
		NumberOfPowerUnits:    originalApp.CompanyInfo.NumberOfPowerUnits,
	}

	// Skip UW Assignment for renewal application
	renewalAppId, applicationEvaluation, err := a.deps.CreateApplicationHandler.CreateApplication(ctx, request, false)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to create renewal app for app %s", appId)
	}
	if applicationEvaluation.AppetiteCheckResult != nil {
		return nil, applicationEvaluation.AppetiteCheckResult, nil
	}
	// Update the new app renewal metadata ,tsp connection details and
	// producer id
	err = a.deps.AppWrapper.UpdateApp(
		ctx, renewalAppId.String(), func(renewalAppObj app.Application) (app.Application, error) {
			renewalAppObj.TSPEnum = originalApp.TSPEnum
			renewalAppObj.TSPConnHandleId = originalApp.TSPConnHandleId
			renewalAppObj.ProducerID = originalApp.ProducerID
			renewalAppObj.RenewalMetadata = &app.RenewalMetadata{
				OriginalApplicationId: appId,
			}
			renewalAppObj.ClearanceStatus = pointer_utils.ToPointer(enums.ApplicationCleared)
			renewalAppObj.AdditionalInsuredInfo = originalApp.AdditionalInsuredInfo
			return renewalAppObj, nil
		},
	)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to update renewal app %s", renewalAppId.String())
	}
	renewalApp, err := a.deps.AppWrapper.GetAppById(ctx, renewalAppId.String())
	if err != nil {
		_ = a.deps.MetricsClient.Inc(clearance_error_renewal_app_not_found, 1, 1.0, statsd.Tag{successTagKey, failureTagValue})
		return nil, nil, errors.Wrapf(err, "unable to get renewal app by id %s", renewalAppId.String())
	}

	if renewalApp != nil {
		log.Info(ctx, "Sending application cleared email for renewal app", log.String("app_id", renewalAppId.String()))
		// TODO: DIFFERENT MAIL TO BE SENT HERE
		err = a.deps.ClearanceEmailer.SendNewBusinessClearanceEstablishedEmail(ctx, *renewalApp)
		if err != nil {
			log.Error(ctx, "failed to send application cleared email", log.Err(err))
		}
	} else {
		_ = a.deps.MetricsClient.Inc(clearance_error_renewal_app_not_found, 1, 1.0, statsd.Tag{successTagKey, failureTagValue})
		log.Error(ctx, "Renewal originalApp not found", log.String("renewalAppId", renewalAppId.String()))
	}

	log.Info(ctx, "Renewal originalApp created", log.String("renewalAppId", renewalAppId.String()))
	return pointer_utils.String(renewalAppId.String()), nil, nil
}

func (a *appStateMachineHelper) createRenewalApplicationV2(ctx context.Context, appId string) (*string, *ApplicationEvaluation, error) {
	originalApp, err := a.deps.AppWrapper.GetAppById(ctx, appId)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to get app by id %s", appId)
	}
	if originalApp.State != stateenums.AppStatePolicyCreated {
		return nil, nil, errors.Newf("app state %s is not AppStatePolicyCreated", originalApp.State)
	}
	if originalApp.BindableSubmissionID == nil {
		return nil, nil, errors.Newf("app %s must have BindableSubmissionID to be renewed", originalApp.ID)
	}
	bindableSub, err := a.deps.AppWrapper.GetSubmissionById(ctx, *originalApp.BindableSubmissionID)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to get bindable submission by id %s", *originalApp.BindableSubmissionID)
	}
	indicationOption, err := a.deps.AppWrapper.GetIndOptionById(ctx, bindableSub.SelectedIndicationID)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to get indication options for id %s", bindableSub.SelectedIndicationID)
	}

	var (
		coverages                   []app.CoverageDetails
		hasMTCCoverageInBindableSub bool
	)
	for _, c := range indicationOption.Coverages {
		if c.CoverageType.IsPrimaryCoverage() {
			coverages = append(coverages, c)
			if c.CoverageType.IsMTCCoverage() {
				hasMTCCoverageInBindableSub = true
			}
		} else if c.CoverageType.IsEligibleAncCovForRenewal() {
			coverages = append(coverages, c)
		}
	}

	request := ApplicationCreation{
		CompanyName:           originalApp.CompanyInfo.Name,
		AgencyId:              originalApp.AgencyID,
		DotNumber:             originalApp.CompanyInfo.DOTNumber,
		EffectiveDate:         bindableSub.CoverageInfo.EffectiveDate.AddDate(1, 0, 0),
		OriginalApplicationID: &appId,
		ProducerId:            originalApp.ProducerID,
		IsRenewal:             pointer_utils.Bool(true),
		NumberOfPowerUnits:    originalApp.CompanyInfo.NumberOfPowerUnits,
	}

	// Skip UW Assignment for renewal application
	renewalAppId, applicationEvaluation, err := a.deps.CreateApplicationHandler.CreateApplication(ctx, request, false)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to create renewal app for app %s", appId)
	}
	if applicationEvaluation.ExistingApplicationMetadata != nil {
		return nil, &ApplicationEvaluation{
			ExistingApplicationMetadata: applicationEvaluation.ExistingApplicationMetadata,
		}, nil
	}
	if applicationEvaluation.AppetiteCheckResult != nil {
		return nil, &ApplicationEvaluation{
			AppetiteCheckResult: applicationEvaluation.AppetiteCheckResult,
		}, nil
	}
	// Update the new app renewal metadata, and copy over fields from original application
	var previousApplicationsIds []string
	originalAppId := appId
	// We always keep the original application ID if it exists
	if originalApp.IsRenewal() {
		originalAppId = originalApp.RenewalMetadata.OriginalApplicationId
	}
	// For renewals, we keep history of applicationIds
	if originalApp.IsRenewalV2() {
		if originalApp.RenewalMetadata.PreviousApplicationsIds != nil {
			previousApplicationsIds = *originalApp.RenewalMetadata.PreviousApplicationsIds
		}
	}
	previousApplicationsIds = append(previousApplicationsIds, appId)

	renewalApp, err := a.deps.AppWrapper.GetAppById(ctx, renewalAppId.String())
	if err != nil {
		_ = a.deps.MetricsClient.Inc(clearance_error_renewal_app_not_found, 1, 1.0, statsd.Tag{successTagKey, failureTagValue})
		return nil, nil, errors.Wrapf(err, "unable to get renewal app by id %s", renewalAppId.String())

	}

	var clearanceStatus enums.ClearanceState
	if renewalApp.ClearanceStatus != nil && *renewalApp.ClearanceStatus == enums.ApplicationRequiresClearance {
		clearanceStatus = enums.ApplicationRequiresClearance
	} else {
		clearanceStatus = enums.ApplicationCleared
	}

	err = a.deps.AppWrapper.UpdateApp(
		ctx, renewalAppId.String(), func(renewalAppObj app.Application) (app.Application, error) {
			renewalUWId := renewalAppObj.UnderwriterID
			renewalAppObj.RenewalMetadata = app.NewRenewalMetadata(originalAppId, previousApplicationsIds)
			if err := copier.CopyWithOption(&renewalAppObj, &originalApp, copier.Option{DeepCopy: true}); err != nil {
				return renewalAppObj, errors.Wrapf(err, "failed to copy details for renewal app %s", renewalAppId)
			}
			// Overwrite the ModelInput of the renewalAppObj with the ModelInput from bindableSub
			if err := copier.CopyWithOption(&renewalAppObj.ModelInput, &bindableSub.ModelInput, copier.Option{DeepCopy: true}); err != nil {
				return renewalAppObj, errors.Wrapf(err, "failed to copy ModelInput from bindableSub for renewal app %s", renewalAppId)
			}
			renewalAppObj.PackageType = bindableSub.PackageType
			renewalAppObj.LossInfo = bindableSub.LossInfo
			renewalAppObj.CoverageInfo.Coverages = coverages
			renewalAppObj.UnderwriterID = renewalUWId
			renewalAppObj.LossInfo.LossRunSummary = CreateLossRunSummaryForRenewalApplication(bindableSub.LossInfo.LossRunSummary, bindableSub.CoverageInfo.EffectiveDate)
			renewalAppObj.ClearanceStatus = pointer_utils.ToPointer(clearanceStatus)

			// Carryover Commodities distribution from bindable submission
			if bindableSub.EquipmentInfo.CommodityDistribution != nil {
				commoditiesDistribution := &app.CommodityDistribution{}
				err := copier.Copy(commoditiesDistribution, bindableSub.EquipmentInfo.CommodityDistribution)
				if err != nil {
					log.Error(ctx, "Error copying commodities distribution", log.Err(err))
					return renewalAppObj, errors.Wrapf(err, "failed to copy commodities distribution from bindableSub for renewal app %s", renewalAppId)
				}
				renewalAppObj.EquipmentInfo.CommodityDistribution = commoditiesDistribution
			}

			hasMTCCoverageInOrgApp := false
			for _, coverage := range originalApp.CoverageInfo.Coverages {
				if coverage.CoverageType.IsMTCCoverage() {
					hasMTCCoverageInOrgApp = true
					break
				}
			}
			if hasMTCCoverageInOrgApp && !hasMTCCoverageInBindableSub {
				// Carryover LossInfo from original application if MTC was rejected by UA
				renewalAppObj.LossInfo.LossRunSummary = CreateMTCLossInfoForRenewalApplication(*originalApp, renewalAppObj.LossInfo.LossRunSummary, bindableSub.CoverageInfo.EffectiveDate)
			}
			return renewalAppObj, nil
		},
	)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "unable to update renewal app %s", renewalAppId.String())
	}

	log.Info(ctx, "Renewal originalApp created", log.String("renewalAppId", renewalAppId.String()))
	return pointer_utils.String(renewalAppId.String()), nil, nil
}

func (a *appStateMachineHelper) beginRenewalQuoteGeneration(ctx context.Context,
	expectedState stateenums.AppState, perStateCheckFn func(app app.Application) error,
	bindable bool, updateSubFn func(sub app.SubmissionObject) (app.SubmissionObject, error),
) (string, error) {
	subId := uuid.New().String()
	ctx = log.ContextWithFields(ctx, log.String("applicationID", a.appId))
	err := a.deps.AppWrapper.InsertSubmissionFromApplicationSnapshot(ctx,
		a.appId, subId, bindable,
		func(sub app.SubmissionObject, app app.Application) (
			app.SubmissionObject, app.Application, error,
		) {
			if err := a.validateAppForQuoteSubmission(app); err != nil {
				return sub, app, errors.Wrap(err, "app validation for quote submission failed")
			}

			if perStateCheckFn != nil {
				if err := perStateCheckFn(app); err != nil {
					return sub, app, errors.Wrap(err, cerrors.PerStateCheckFailed)
				}
			}

			if app.State != expectedState {
				return sub, app, errors.Wrapf(cerrors.ErrActionNotValid,
					"unexpected state %v. Expected %v", app.State, expectedState)
			}
			sub.EffectiveDateTo = pointer_utils.Time(time_utils.AddYears(sub.CoverageInfo.EffectiveDate, app_logic.DefaultPolicyDurationInYears))
			return sub, app, nil
		})
	if err != nil {
		return "", errors.Wrap(err, "unable to insert sub from app snapshot")
	}

	// Update submission
	if updateSubFn != nil {
		err = a.deps.AppWrapper.UpdateSub(ctx, subId, updateSubFn)
		if err != nil {
			return "", errors.Wrap(err, "unable to update sub from with updateSubFn")
		}
	}
	var appReviewId string
	if bindable {
		appReview, err := a.deps.AppReviewWrapper.GetLatestPendingReview(ctx, a.appId)
		if err != nil {
			return "", errors.Wrap(err, "unable to get latest pending review")
		}
		appReviewId = appReview.Id
	}

	jobRunId, err := a.addJobRun(ctx, subId, appReviewId, bindable)
	if err != nil {
		return "", errors.Wrap(err, "unable to add job run")
	}

	err = a.deps.AppWrapper.UpdateApp(ctx, a.appId, func(app app.Application) (app.Application, error) {
		if bindable {
			app.BindableSubmissionID = &subId
		} else {
			app.UwSubmissionID = &subId
		}
		return app, nil
	})
	if err != nil {
		return "", errors.Wrap(err, "unable to update app with submission ID")
	}

	// Updated sub in DB
	err = a.deps.AppWrapper.UpdateSub(ctx, subId, func(sub app.SubmissionObject) (app.SubmissionObject, error) {
		sub.JobRunId = jobRunId
		return sub, nil
	})
	if err != nil {
		return "", errors.Wrap(err, "unable to update submission and store job run")
	}
	return subId, nil
}

// MarkPendingAppReviewsToStale Triggers a job to mark all app-reviews to stale state.
// We use Jobber here to circumvent golang's cyclic dependency issues
// with the asmWrapper and appReviewStateMachine dependency.
func (a *appStateMachineHelper) MarkPendingAppReviewsToStale(ctx context.Context, appId string) error {
	jobId, err := a.deps.Jobber.AddJobRun(ctx,
		jobber.NewAddJobRunParams(
			quotingjobs.MarkPendingAppReviewsToStale,
			&quotingjobs.MarkPendingAppReviewsToStaleArgs{
				ApplicationId: appId,
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	if err != nil {
		return errors.Wrapf(err, "unable to add transition app reviews to pending state job for application %s", appId)
	}
	err = a.deps.Jobber.WaitForJobRunCompletion(ctx, jobId)
	if err != nil {
		return errors.Wrapf(err, "failed to wait for job run to complete for pending state job for application %s", appId)
	}
	job, err := a.deps.Jobber.GetJobRun(ctx, jobId)
	if err != nil {
		return errors.Wrapf(err, "unable to fetch transition to stale job %s", jobId)
	}
	if job.Status != jtypes.JobRunStatusSucceeded {
		return errors.New(fmt.Sprintf("transition to stale job failed with status %s and error %s", job.Status, job.Error))
	}
	return nil
}
