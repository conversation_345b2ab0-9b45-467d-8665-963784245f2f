package app_state_machine

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"github.com/volatiletech/null/v8"
	"go.uber.org/mock/gomock"
	protolib "google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	billing_enums "nirvanatech.com/nirvana/billing/enums"
	"nirvanatech.com/nirvana/billing/legacy/billinginfo"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	mock_feature_flag_lib "nirvanatech.com/nirvana/common-go/feature_flag_lib/mock"
	commongrpc "nirvanatech.com/nirvana/common-go/grpc"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums/old_enums"
	authmock "nirvanatech.com/nirvana/db-api/db_wrappers/auth/mock"
	entitylicense "nirvanatech.com/nirvana/db-api/db_wrappers/entity_license"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	mock_forms "nirvanatech.com/nirvana/db-api/db_wrappers/forms/mock"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	fleetmodel "nirvanatech.com/nirvana/fleet/model"
	"nirvanatech.com/nirvana/fmcsa/models"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"
	insuredmodel "nirvanatech.com/nirvana/insured/model"
	"nirvanatech.com/nirvana/policy_common/constants"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
	"nirvanatech.com/nirvana/rating/data_processing/vin_processing/iso_utils"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/telematics"
)

// UpsertIBTestSuite refactors the original table-driven TestUpsertIB into a testify/suite based
// test suite. Common test data, mock dependencies and frequently used mock objects are initialised
// in SetupTest. Each original table-driven test case is now a dedicated test method on the suite.
type UpsertIBTestSuite struct {
	suite.Suite

	// Common test data
	mockCreatedBy           uuid.UUID
	userLDContextMatcher    gomock.Matcher
	mockAgencyID            uuid.UUID
	mockCompanyState        us_states.USState
	mockSigPacketFormComp   compilation.FormsCompilation
	mockALPolicyFormCompID  uuid.UUID
	mockALPolicyFormComp    compilation.FormsCompilation
	mockGLPolicyFormCompID  uuid.UUID
	mockGLPolicyFormComp    compilation.FormsCompilation
	mockEntityLicense       *entitylicense.EntityLicense
	expectedUpsertIBRequest *service.UpsertInsuranceBundleRequest
	upsertIBRequestMatcher  gomock.Matcher

	// gomock controller & mocks
	ctrl     *gomock.Controller
	mockAuth *authmock.MockDataWrapper
	mockIB   *service.MockInsuranceBundleManagerClient
	mockFF   *mock_feature_flag_lib.MockClient
	mockFW   *mock_forms.MockFormWrapper

	// assembled deps, helper and args under test
	asmDeps ASMDeps
	helper  *appStateMachineHelper
	args    *createPolicyArgs
}

// SetupTest configures common data and fresh mocks before every test run.
func (suite *UpsertIBTestSuite) SetupTest() {
	suite.mockCreatedBy = uuid.New()
	suite.userLDContextMatcher = gomock.Cond(func(x feature_flag_lib.LDContexts) bool {
		return x[0].Key() == suite.mockCreatedBy.String()
	})

	suite.mockAgencyID = uuid.New()
	suite.mockCompanyState = us_states.OH

	// Mock form compilations / entity licence that several tests rely on
	suite.mockSigPacketFormComp = forms.MockFormCompilation(
		compilation.CompilationTypeSignaturePacket, uuid.New())
	suite.mockALPolicyFormCompID = *(*suite.mockSigPacketFormComp.Metadata().CoverageIdsMap)[appenums.CoverageAutoLiability]
	suite.mockALPolicyFormComp = forms.MockFormCompilation(
		compilation.CompilationTypePolicy, suite.mockALPolicyFormCompID)
	suite.mockGLPolicyFormCompID = *(*suite.mockSigPacketFormComp.Metadata().CoverageIdsMap)[appenums.CoverageGeneralLiability]
	suite.mockGLPolicyFormComp = forms.MockFormCompilation(
		compilation.CompilationTypePolicy, suite.mockGLPolicyFormCompID)
	suite.mockEntityLicense = entitylicense.MockEntityLicense(suite.mockAgencyID, suite.mockCompanyState)

	// gomock controller & mocks
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockAuth = authmock.NewMockDataWrapper(suite.ctrl)
	suite.mockIB = service.NewMockInsuranceBundleManagerClient(suite.ctrl)
	suite.mockFF = mock_feature_flag_lib.NewMockClient(suite.ctrl)
	suite.mockFW = mock_forms.NewMockFormWrapper(suite.ctrl)

	suite.asmDeps = ASMDeps{
		AuthWrapper:  suite.mockAuth,
		FeatureFlag:  suite.mockFF,
		IBService:    suite.mockIB,
		FormsWrapper: suite.mockFW,
	}
	suite.helper = &appStateMachineHelper{deps: suite.asmDeps}
	suite.args = createValidCreatePolicyArgs(
		suite.mockCreatedBy, suite.mockSigPacketFormComp, suite.mockAgencyID, suite.mockCompanyState,
	)

	suite.expectedUpsertIBRequest = createExpectedUpsertIBRequest(
		suite.mockAgencyID.String(), suite.args.subObj.ID, suite.args.appObj.ID,
		suite.mockSigPacketFormComp.Id().String(),
		suite.mockGLPolicyFormCompID.String(), suite.mockALPolicyFormCompID.String(),
	)
	suite.upsertIBRequestMatcher = gomock.Cond(func(x *service.UpsertInsuranceBundleRequest) bool {
		x.GetInsuranceBundle().GetSegments()[0].Id = suite.expectedUpsertIBRequest.GetInsuranceBundle().GetSegments()[0].Id

		isEqual := protolib.Equal(x, suite.expectedUpsertIBRequest)
		if !isEqual {
			suite.T().Logf(
				"[%s] UpsertIB request doesn't match.\nDiff:\n%s",
				suite.T().Name(),
				commongrpc.Diff(suite.expectedUpsertIBRequest, x, "expected", "got"),
			)
		}

		return isEqual
	})
}

// 1. Feature flag disabled – skips IB creation.
func (suite *UpsertIBTestSuite) TestFeatureFlagDisabledSkipsIBCreation() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(false, nil)

	args := &createPolicyArgs{
		appObj: &app.Application{
			ID:         uuid.NewString(),
			CreatedBy:  suite.mockCreatedBy.String(),
			MarketerID: &suite.mockCreatedBy,
		},
	}

	err := suite.helper.upsertIB(context.Background(), args)
	suite.Require().NoError(err)
}

// 2. Feature flag enabled – happy path IB service call succeeds.
func (suite *UpsertIBTestSuite) TestFeatureFlagEnabledIBServiceSuccess() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)
	suite.mockIB.EXPECT().UpsertInsuranceBundle(gomock.Any(), suite.upsertIBRequestMatcher).Return(
		&service.UpsertInsuranceBundleResponse{
			InsuranceBundleInternalId: uuid.NewString(),
			InsuredId:                 uuid.NewString(),
		}, nil)
	suite.mockFW.EXPECT().GetFormCompilationById(gomock.Any(), suite.mockALPolicyFormCompID).Return(
		&suite.mockALPolicyFormComp, nil)
	suite.mockFW.EXPECT().GetFormCompilationById(gomock.Any(), suite.mockGLPolicyFormCompID).Return(
		&suite.mockGLPolicyFormComp, nil)

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().NoError(err)
}

// 3. Feature flag enabled – IB service call succeeds with large nil / empty optional fields.
func (suite *UpsertIBTestSuite) TestFeatureFlagEnabledIBServiceSuccessNilEmpty() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)
	suite.mockIB.EXPECT().UpsertInsuranceBundle(gomock.Any(), suite.upsertIBRequestMatcher).Return(
		&service.UpsertInsuranceBundleResponse{
			InsuranceBundleInternalId: uuid.NewString(),
			InsuredId:                 uuid.NewString(),
		}, nil)

	// entity license with nil business address
	entityLicenseNilAddress := *suite.mockEntityLicense
	entityLicenseNilAddress.BusinessAddress = nil
	suite.mockFW.EXPECT().GetFormCompilationById(gomock.Any(), suite.mockALPolicyFormCompID).Return(
		&suite.mockALPolicyFormComp, nil)
	suite.mockFW.EXPECT().GetFormCompilationById(gomock.Any(), suite.mockGLPolicyFormCompID).Return(
		&suite.mockGLPolicyFormComp, nil)

	// prune optional fields
	suite.args.subObj.CompanyInfo.TerminalLocations = nil
	suite.args.subObj.CompanyInfo.PremiumTaxRecords = []app.PremiumTaxRecord{}
	suite.args.subObj.UnderwriterInput.NegotiatedRates = nil
	suite.args.appReview.CameraSubsidyDetails = nil
	suite.args.subObj.EquipmentInfo.CommodityDistribution = nil
	suite.args.subObj.CoverageInfo.CoveragesWithCombinedDeductibles = nil
	suite.args.subObj.ModelInput.UnderwriterInput.VehicleZoneDistribution = nil

	// change expected IB request to match the pruned fields
	expectedPolicies := suite.expectedUpsertIBRequest.GetInsuranceBundle().GetSegments()[0].
		GetPolicies()
	for _, expectedPolicy := range expectedPolicies {
		expectedFleetPD := expectedPolicy.GetProgramData().GetFleetData()
		expectedFleetPD.GetCompany().TerminalLocations = nil
		expectedFleetPD.GetCompany().TaxRecords = nil
		expectedFleetPD.NegotiatedRates = nil
		expectedFleetPD.CameraSubsidy = nil
		expectedFleetPD.GetCommodityDetails().CommodityDistribution = nil
		expectedFleetPD.GetOperation().VehicleZoneRecords = nil
	}
	suite.expectedUpsertIBRequest.GetInsuranceBundle().GetSegments()[0].GetCoverageCriteria().CombinedDeductibles = nil
	// Remove the 5th element (CoverageCargoAtScheduledTerminals) from the limits array
	limits := suite.expectedUpsertIBRequest.GetInsuranceBundle().GetSegments()[0].GetCoverageCriteria().Limits
	suite.expectedUpsertIBRequest.GetInsuranceBundle().GetSegments()[0].GetCoverageCriteria().Limits = append(limits[:4], limits[5:]...)

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().NoError(err)
}

// 4. Feature flag enabled – non-admitted carrier success.
func (suite *UpsertIBTestSuite) TestFeatureFlagEnabledNonAdmittedCarrierSuccess() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)

	suite.mockIB.EXPECT().UpsertInsuranceBundle(gomock.Any(), suite.upsertIBRequestMatcher).Return(
		&service.UpsertInsuranceBundleResponse{
			InsuranceBundleInternalId: uuid.NewString(),
			InsuredId:                 uuid.NewString(),
		}, nil)
	suite.mockFW.EXPECT().GetFormCompilationById(gomock.Any(), suite.mockALPolicyFormCompID).Return(
		&suite.mockALPolicyFormComp, nil)
	suite.mockFW.EXPECT().GetFormCompilationById(gomock.Any(), suite.mockGLPolicyFormCompID).Return(
		&suite.mockGLPolicyFormComp, nil)

	suite.args.appObj.ModelPinConfig.Application.IsNonAdmitted = true
	suite.args.subObj.ModelPinConfig.Application.IsNonAdmitted = true
	suite.expectedUpsertIBRequest.InsuranceBundle.CarrierAdmittedType = insurancecoreproto.CarrierAdmittedType_CarrierAdmittedType_NonAdmitted

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().NoError(err)
}

// 6. Feature flag enabled – IB service returns error.
func (suite *UpsertIBTestSuite) TestFeatureFlagEnabledIBServiceError() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)
	suite.mockIB.EXPECT().UpsertInsuranceBundle(gomock.Any(), suite.upsertIBRequestMatcher).Return(
		nil, errors.New("IB service error"))
	suite.mockFW.EXPECT().GetFormCompilationById(gomock.Any(), suite.mockALPolicyFormCompID).Return(
		&suite.mockALPolicyFormComp, nil)
	suite.mockFW.EXPECT().GetFormCompilationById(gomock.Any(), suite.mockGLPolicyFormCompID).Return(
		&suite.mockGLPolicyFormComp, nil)

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().Error(err)
	suite.Require().ErrorContains(err,
		"failed to upsert fleet insurance bundle: UpsertInsuranceBundle service call failed: IB service error")
}

// 7. Auth wrapper error – feature flag not evaluated; defaults to disabled.
func (suite *UpsertIBTestSuite) TestAuthWrapperErrorDefaultsDisabled() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(nil, errors.New("auth error"))

	args := &createPolicyArgs{
		appObj: &app.Application{
			ID:         uuid.NewString(),
			CreatedBy:  suite.mockCreatedBy.String(),
			MarketerID: &suite.mockCreatedBy,
		},
	}

	err := suite.helper.upsertIB(context.Background(), args)
	suite.Require().NoError(err)
}

// 8. Nil args error.
func (suite *UpsertIBTestSuite) TestNilArgsError() {
	err := suite.helper.upsertIB(context.Background(), nil)
	suite.Require().Error(err)
	suite.Require().Contains(err.Error(), "nil args")
}

// 9. Nil application object error.
func (suite *UpsertIBTestSuite) TestNilAppObjectError() {
	args := &createPolicyArgs{}
	err := suite.helper.upsertIB(context.Background(), args)
	suite.Require().Error(err)
	suite.Require().Contains(err.Error(), "application object is nil")
}

// 10. Nil submission object error.
func (suite *UpsertIBTestSuite) TestNilSubmissionObjectError() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)

	suite.args.subObj = nil

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().Error(err)
	suite.Require().Contains(err.Error(), "submission object is nil")
}

// 11. Feature flag BoolVariation returns error – defaults to disabled.
func (suite *UpsertIBTestSuite) TestFeatureFlagBoolVariationErrorDefaultsDisabled() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(false, errors.New("ld error"))

	args := &createPolicyArgs{
		appObj: &app.Application{
			ID:         uuid.NewString(),
			CreatedBy:  suite.mockCreatedBy.String(),
			MarketerID: &suite.mockCreatedBy,
		},
	}

	err := suite.helper.upsertIB(context.Background(), args)
	suite.Require().NoError(err)
}

// 12. Nil EffectiveDateTo error.
func (suite *UpsertIBTestSuite) TestNilEffectiveDateToError() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)

	suite.args.subObj.EffectiveDateTo = nil

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().Error(err)
	suite.Require().Contains(err.Error(), "submission effective date to is nil")
}

// 13. Nil form compilation coverage ids map error.
func (suite *UpsertIBTestSuite) TestNilCoverageIdsMapError() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)

	// Modify form compilation to have nil coverage map
	customFormComp := forms.MockFormCompilation(compilation.CompilationTypeSignaturePacket, uuid.New())
	customFormComp.(*compilation.FormsCompilationImpl).FormMetadata.CoverageIdsMap = nil
	suite.args.signPacketFormComp = customFormComp

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().Error(err)
	suite.Require().Contains(err.Error(), "form compilation coverage ids map is nil")
}

// 15. Policy form compilation fetch error.
func (suite *UpsertIBTestSuite) TestPolicyFormCompilationFetchError() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)
	suite.mockFW.EXPECT().GetFormCompilationById(gomock.Any(), suite.mockALPolicyFormCompID).Return(
		nil, errors.New("form compilation error"))

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().Error(err)
	suite.Require().Contains(err.Error(), "failed to get policy form compilation")
}

// 16. Form compilation ID not found for coverage error.
func (suite *UpsertIBTestSuite) TestFormCompilationIDNotFoundForCoverageError() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)

	// Form compilation with empty coverage map – ID missing
	emptyMapComp := forms.MockFormCompilation(compilation.CompilationTypeSignaturePacket, uuid.New())
	empty := compilation.CoverageIdsMap{}
	emptyMapComp.(*compilation.FormsCompilationImpl).FormMetadata.CoverageIdsMap = &empty

	args := createValidCreatePolicyArgs(
		suite.mockCreatedBy, emptyMapComp, suite.mockAgencyID, suite.mockCompanyState)

	err := suite.helper.upsertIB(context.Background(), args)
	suite.Require().Error(err)
	suite.Require().Contains(err.Error(), "form compilation id not found")
}

// 17. Policy number generation error (empty policy set identifier).
func (suite *UpsertIBTestSuite) TestPolicyNumberGenerationError() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)

	suite.args.policySetIdentifier = ""

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().Error(err)
	suite.Require().Contains(err.Error(), "failed to generate policy number")
}

// 18. Terminal locations rating address not found.
func (suite *UpsertIBTestSuite) TestTerminalLocationsRatingAddressNotFoundError() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)

	// terminal location differs from rating address
	terminalLocations := []app.TerminalLocation{{
		UsState:        us_states.CA,
		AddressLineOne: "123 Test St",
		ZipCodeString:  "90210",
		TypeOfTerminal: appenums.TypeOfTerminalOffice,
		IsGated:        false,
		IsGuarded:      false,
	}}
	suite.args.subObj.CompanyInfo.TerminalLocations = &terminalLocations
	suite.args.subObj.UnderwriterInput = &app.UnderwriterInput{
		RatingAddress: &app.TerminalLocation{
			UsState:        us_states.TX,
			AddressLineOne: "456 Different",
			ZipCodeString:  "75001",
			TypeOfTerminal: appenums.TypeOfTerminalOffice,
		},
	}

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().Error(err)
	suite.Require().Contains(err.Error(), "rating address not found in terminal locations")
}

// 19. Empty coverages list in coverage info.
func (suite *UpsertIBTestSuite) TestEmptyCoveragesListError() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)

	suite.args.subObj.CoverageInfo.Coverages = []app.CoverageDetails{}

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().Error(err)
	suite.Require().Contains(err.Error(), "no policies created")
}

// 20. Coverage not in CoveragesWithPolicies list.
func (suite *UpsertIBTestSuite) TestCoverageNotInPoliciesListError() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)

	suite.args.subObj.CoverageInfo.Coverages = []app.CoverageDetails{{
		CoverageType: appenums.CoverageBlanketWaiverOfSubrogation,
		Limit:        pointer_utils.ToPointer(int32(1000000)),
		Deductible:   pointer_utils.ToPointer(int32(1000)),
	}}

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().Error(err)
	suite.Require().Contains(err.Error(), "no policies created")
}

// 21. Nil additional insured info email still succeeds.
func (suite *UpsertIBTestSuite) TestNilAdditionalInsuredInfoEmailSuccess() {
	suite.mockAuth.EXPECT().FetchAuthzUser(gomock.Any(), suite.mockCreatedBy).Return(
		createMockUser(suite.mockCreatedBy), nil)
	suite.mockFF.EXPECT().BoolVariation(
		suite.userLDContextMatcher,
		feature_flag_lib.FeatureInsuranceBundleFleetMigrationPhase2,
		false,
	).Return(true, nil)

	suite.expectedUpsertIBRequest.Insured.ContactInfo.Email = ""

	suite.mockIB.EXPECT().UpsertInsuranceBundle(gomock.Any(), suite.upsertIBRequestMatcher).Return(
		&service.UpsertInsuranceBundleResponse{
			InsuranceBundleInternalId: uuid.NewString(),
			InsuredId:                 uuid.NewString(),
		}, nil)
	suite.mockFW.EXPECT().GetFormCompilationById(gomock.Any(), suite.mockALPolicyFormCompID).Return(
		&suite.mockALPolicyFormComp, nil)
	suite.mockFW.EXPECT().GetFormCompilationById(gomock.Any(), suite.mockGLPolicyFormCompID).Return(
		&suite.mockGLPolicyFormComp, nil)

	suite.args.subObj.AdditionalInsuredInfo = nil

	err := suite.helper.upsertIB(context.Background(), suite.args)
	suite.Require().NoError(err)
}

// Test runner
func TestUpsertIBTestSuite(t *testing.T) {
	t.Parallel()
	suite.Run(t, new(UpsertIBTestSuite))
}

func createValidCreatePolicyArgs(
	mockCreatedBy uuid.UUID, mockSignaturePacketFormCompilation compilation.FormsCompilation,
	agencyID uuid.UUID, companyState us_states.USState,
) *createPolicyArgs {
	effectiveDate := time_utils.NewDate(2024, 12, 1).ToTime()
	effectiveDateTo := time_utils.NewDate(2025, 12, 1).ToTime()

	return &createPolicyArgs{
		coverage: appenums.CoverageAutoLiability,
		appObj: &app.Application{
			ID:         uuid.NewString(),
			CreatedBy:  mockCreatedBy.String(),
			MarketerID: &mockCreatedBy,
			AgencyID:   agencyID,
			ModelInput: app.ModelInput{
				CompanyInfo: &app.CompanyInfo{
					Name:              "Test Company",
					USState:           companyState,
					ProjectedMileage:  100000,
					DOTNumber:         123456,
					TerminalLocations: &[]app.TerminalLocation{},
				},
			},
			ModelPinConfig: &app.ModelPinConfig{
				Application: app.ApplicationConfig{
					InsuranceCarrier: constants.InsuranceCarrierFalseLake,
					IsNonAdmitted:    false,
				},
			},
		},
		subObj: &app.SubmissionObject{
			ID:              uuid.NewString(),
			EffectiveDateTo: &effectiveDateTo,
			ModelInput: app.ModelInput{
				CompanyInfo: &app.CompanyInfo{
					Name:             "Test Company",
					USState:          companyState,
					ProjectedMileage: 200000,
					DOTNumber:        123456,
					TerminalLocations: &[]app.TerminalLocation{
						{
							UsState:        companyState,
							AddressLineOne: "123 Test St",
							AddressLineTwo: pointer_utils.ToPointer("123 Test St"),
							ZipCodeString:  "90210",
							ZipCode:        90210,
							TypeOfTerminal: appenums.TypeOfTerminalOffice,
							IsGated:        false,
							IsGuarded:      false,
							CargoTerminalSchedule: &app.CargoTerminalSchedule{
								ID:                     uuid.New(),
								Limit:                  pointer_utils.ToPointer(int32(420000)),
								ConstructionClass:      pointer_utils.ToPointer(appenums.ConstructionClassFrame),
								PublicProtectionClass:  pointer_utils.ToPointer(appenums.PublicProtectionClass1),
								PrivateTheftProtection: appenums.PrivateTheftProtectionLocalAlarm,
								PrivateFireProtection:  appenums.PrivateFireProtectionIncompleteSuppression,
							},
						},
						{
							UsState:        us_states.NY,
							AddressLineOne: "456 Another St",
							AddressLineTwo: pointer_utils.ToPointer("456 Another St"),
							ZipCodeString:  "10001",
							ZipCode:        10001,
							TypeOfTerminal: appenums.TypeOfTerminalDock,
							IsGated:        true,
							IsGuarded:      true,
						},
					},
					PremiumTaxRecords: []app.PremiumTaxRecord{
						{
							TaxCode:          "random-tax-code",
							JurisdictionName: "Random Jurisdiction",
							JurisdictionType: "State",
							LineOfBusinessDetails: []app.LineOfBusinessDetail{
								{
									CoverageType: appenums.CoverageAutoLiability,
									TaxValue:     "1000.0",
								},
								{
									CoverageType: appenums.CoverageAutoPhysicalDamage,
									TaxValue:     "500.0",
								},
							},
						},
						{
							TaxCode:          "another-tax-code",
							JurisdictionName: "Another Jurisdiction",
							JurisdictionType: "Federal",
							LineOfBusinessDetails: []app.LineOfBusinessDetail{
								{
									CoverageType: appenums.CoverageGeneralLiability,
									TaxValue:     "200.0",
								},
							},
						},
					},
					RadiusOfOperation: []*app.MileageRadiusRecord{
						{
							PercentageOfFleet: 25.0,
							RadiusBucket:      appenums.MileageRadiusBucketFiftyToTwoHundred,
						},
						{
							PercentageOfFleet: 75.0,
							RadiusBucket:      appenums.MileageRadiusBucketFiveHundredPlus,
						},
					},
				},
				LossInfo: &app.LossInfo{
					LossRunSummary: []app.LossRunSummaryPerCoverage{
						{
							CoverageType: appenums.CoverageAutoLiability,
							Summary: []app.LossRunSummaryRecord{
								{
									PolicyPeriodStartDate: effectiveDate,
									PolicyPeriodEndDate:   effectiveDateTo,
									PeriodTag:             appenums.LossRunSummaryPeriodCurrent,
									NumberOfPowerUnits:    10,
									NumberOfClaims:        2,
									LossIncurred:          50000.0,
									IsNirvanaPeriod:       pointer_utils.ToPointer(true),
								},
							},
						},
					},
				},
				CoverageInfo: &app.CoverageInfo{
					EffectiveDate: effectiveDate,
					Coverages: []app.CoverageDetails{
						{
							CoverageType: appenums.CoverageAutoLiability,
							Limit:        pointer_utils.ToPointer(int32(1000000)),
							Deductible:   pointer_utils.ToPointer(int32(1000)),
						},
						{
							CoverageType: appenums.CoverageAutoPhysicalDamage,
							Limit:        pointer_utils.ToPointer(int32(100000)),
							Deductible:   pointer_utils.ToPointer(int32(1000)),
						},
						{
							CoverageType: appenums.CoverageBlanketWaiverOfSubrogation,
							Limit:        pointer_utils.ToPointer(int32(50000)),
							Deductible:   pointer_utils.ToPointer(int32(500)),
						},
						{
							CoverageType: appenums.CoverageHiredAuto,
							Limit:        nil,
							Deductible:   pointer_utils.ToPointer(int32(2000)),
						},
						{
							CoverageType: appenums.CoverageUninsuredMotoristBodilyInjury,
							Limit:        pointer_utils.ToPointer(int32(0)),
							Deductible:   pointer_utils.ToPointer(int32(1500)),
						},
						{
							CoverageType: appenums.CoverageStopGap,
							Limit:        pointer_utils.ToPointer(int32(25000)),
							Deductible:   nil,
						},
						{
							CoverageType: appenums.CoverageCargoAtScheduledTerminals,
							Limit:        nil,
							Deductible:   pointer_utils.ToPointer(int32(10000)),
						},
						{
							CoverageType: appenums.CoverageBlanketAdditional,
							Limit:        pointer_utils.ToPointer(int32(10000)),
							Deductible:   pointer_utils.ToPointer(int32(250)),
						},
						{
							CoverageType: appenums.CoverageGeneralLiability,
							Limit:        pointer_utils.ToPointer(int32(300000)),
							Deductible:   pointer_utils.ToPointer(int32(1000)),
						},
						{
							CoverageType: appenums.Coverage(-1), // to test not found in AncCoverageToPrimaryCoverage
						},
					},
					CoveragesWithCombinedDeductibles: &app.CombinedDeductibleCoverages{
						CombinedCoveragesList: []app.CombinedCoverages{
							{
								appenums.CoverageAutoPhysicalDamage: true,
								appenums.CoverageMotorTruckCargo:    true,
							},
							{
								appenums.CoverageHiredAuto:                     true,
								appenums.CoverageUninsuredMotoristBodilyInjury: true,
							},
						},
					},
				},
				EquipmentInfo: &app.EquipmentInfo{
					PrimaryOperatingClass: pointer_utils.ToPointer(appenums.OperatingClassDryVan),
					PrimaryCommodity:      pointer_utils.ToPointer(old_enums.CommodityHauledEnginesOrMachinery),
					PrimaryCategory:       pointer_utils.ToPointer(appenums.CommodityCategoryAutoPartsAndAccessories),
					EquipmentList: app.EquipmentList{
						Info: []app.EquipmentListRecord{
							{
								VIN:         "1HGBH41JXMN109186",
								StatedValue: pointer_utils.ToPointer(int32(25000)),
							},
							{
								VIN:         "2T3BF4DV4BW123456",
								StatedValue: pointer_utils.ToPointer(int32(35000)),
							},
							{
								VIN:         "3C6UR5DL8FG123789",
								StatedValue: pointer_utils.ToPointer(int32(45000)),
							},
						},
					},
					OperatingClassDistribution: []app.OperatingClassDistributionRecord{
						{
							Class:             appenums.OperatingClassDryVan,
							PercentageOfFleet: 35.0,
						},
						{
							Class:             appenums.OperatingClassDump,
							PercentageOfFleet: 65.0,
						},
					},
					CommodityDistribution: &app.CommodityDistribution{
						Commodities: []app.WeightedCommodityRecord{
							{
								Category: appenums.CommodityCategoryAutoPartsAndAccessories,
								Commodity: app.Commodity{
									Type:  pointer_utils.ToPointer(appenums.CommodityHauledElectronics),
									Label: "Electronics & Auto Parts",
								},
								AvgDollarValueHauled: 50000,
								MaxDollarValueHauled: 100000,
								PercentageOfHauls:    60,
							},
							{
								Category: appenums.CommodityCategoryAgriculturalProducts,
								Commodity: app.Commodity{
									Type:  pointer_utils.ToPointer(appenums.CommodityHauledDryFoods),
									Label: "Dry Foods & Beverages",
								},
								AvgDollarValueHauled: 30000,
								MaxDollarValueHauled: 75000,
								PercentageOfHauls:    40,
							},
						},
						AdditionalCommodities: &app.AdditionalCommoditiyRecord{
							Commodities:       "Other miscellaneous commodities",
							PercentageOfHauls: 15,
						},
					},
				},
				DriversInfo: &app.DriversInfo{
					Drivers: []app.DriverListRecord{
						{
							DriverLicenseNumber: "D123456789",
							UsState:             us_states.OH.String(),
							DateHired:           effectiveDate.AddDate(-2, 0, 0), // 2 years ago
							DateOfBirth:         null.TimeFrom(time.Date(1985, 5, 15, 0, 0, 0, 0, time.UTC)),
							FirstName:           null.StringFrom("John"),
							LastName:            null.StringFrom("Doe"),
							YearsOfExperience:   pointer_utils.ToPointer(float32(8.5)),
						},
						{
							DriverLicenseNumber: "D987654321",
							UsState:             us_states.NY.String(),
							DateHired:           effectiveDate.AddDate(-1, -3, 0), // 1 year 3 months ago
							DateOfBirth:         null.TimeFrom(time.Date(1978, 11, 22, 0, 0, 0, 0, time.UTC)),
							FirstName:           null.StringFrom("Jane"),
							LastName:            null.StringFrom("Smith"),
							YearsOfExperience:   pointer_utils.ToPointer(float32(12.0)),
						},
						{
							DriverLicenseNumber: "D555666777",
							UsState:             us_states.CA.String(),
							DateHired:           effectiveDate.AddDate(0, -6, 0), // 6 months ago
							DateOfBirth:         null.TimeFrom(time.Date(1990, 3, 10, 0, 0, 0, 0, time.UTC)),
							FirstName:           null.StringFrom("Mike"),
							LastName:            null.StringFrom("Johnson"),
							YearsOfExperience:   pointer_utils.ToPointer(float32(5.5)),
						},
					},
				},
				UnderwriterInput: &app.UnderwriterInput{
					RatingAddress: &app.TerminalLocation{
						AddressLineOne: "456 Another St",
						AddressLineTwo: pointer_utils.ToPointer("something else"),
						UsState:        us_states.AZ,
						ZipCodeString:  "85001",
						ZipCode:        85001,
						TypeOfTerminal: appenums.TypeOfTerminalDock,
						IsGated:        false,
						IsGuarded:      false,
					},
					VehicleZoneDistribution: &[]app.VehicleZoneRecord{
						{
							StartZone:            40,
							EndZone:              44,
							PercentageOfVehicles: 65,
						},
						{
							StartZone:            45,
							EndZone:              48,
							PercentageOfVehicles: 35,
						},
					},
					LargeLosses: []app.LargeLoss{
						{
							Date:         time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC),
							CoverageType: appenums.CoverageAutoLiability,
							LossIncurred: 125000,
							Description:  "Multi-vehicle accident on highway",
						},
						{
							Date:         time.Date(2023, 9, 22, 0, 0, 0, 0, time.UTC),
							CoverageType: appenums.CoverageAutoPhysicalDamage,
							LossIncurred: 75000,
							Description:  "Collision damage to truck and trailer",
						},
					},
					NegotiatedRates: &app.NegotiatedRates{
						IsNegotiatedRatesApplicable: true,
						IsNegotiatedRatesApplied:    false,
						BaseLimitPremium:            pointer_utils.ToPointer(int64(50000)),
						ThresholdPremium:            pointer_utils.ToPointer(int64(75000)),
						Coverages: []appenums.Coverage{
							appenums.CoverageAutoLiability,
							appenums.CoverageAutoPhysicalDamage,
						},
						Details: &app.NegotiatedRatesDetails{
							AlNegotiatedRate:   45000,
							AlTraditionalRate:  pointer_utils.ToPointer(int64(50000)),
							ApdNegotiatedRate:  pointer_utils.ToPointer(int64(15000)),
							ApdTraditionalRate: pointer_utils.ToPointer(int64(18000)),
							Exemption:          appenums.NegotiatedRatesExemptionConsentToRate,
							CaseDescription:    "Standard negotiated rates for established customer",
						},
						Rules: []app.NegotiatedRatesRule{
							{
								RuleType:     appenums.NegotiatedRatesExemptionLargeFleet,
								IsApplicable: true,
							},
						},
					},
				},
				AdditionalInfoExtraMetadata: &app.AdditionalInfoExtraMetadata{
					PercentageOfSubhaul:   pointer_utils.ToPointer(float32(25.5)),
					NumOwnerOperatorUnits: null.IntFrom(5),
				},
				AdditionalCommodityInfo: &app.AdditionalCommodityInfo{
					Commodities: []appenums.AdditionalInformationCommodity{
						appenums.AddlInfoHazardousMaterialsInclClass9,
						appenums.AddlInfoLiftGateOrWhiteGloveService,
					},
					Comment: null.StringFrom("Special handling required for certain loads"),
				},
				RateMLUnderwritingEntityInputs: app.RateMLUnderwritingEntityInputs{
					SafetyModAllCov: 1.5,
					AlCredit:        2.5,
					ApdCredit:       3.5,
					MtcCredit:       4.5,
				},
				ExtraPricingInfo: &app.ExtraPricingInfo{
					DecodedVehicles: []*app.DecodedVehicle{
						{
							VIN:            "1HGBH41JXMN109186",
							YearMade:       2016,
							IsoVehicleType: iso_utils.VehicleTypeTruck,
							IsoWeightGroup: iso_utils.WeightGroupLight,
						},
						{
							VIN:            "2T3BF4DV4BW123456",
							YearMade:       2017,
							IsoVehicleType: iso_utils.VehicleTypeTrailer,
							IsoWeightGroup: iso_utils.WeightGroupMedium,
						},
						{
							VIN:            "3C6UR5DL8FG123789",
							YearMade:       2018,
							IsoVehicleType: iso_utils.VehicleTypeSpareTrailer,
							IsoWeightGroup: iso_utils.WeightGroupHeavy,
						},
					},
				},
			},
			ModelPinConfig: &app.ModelPinConfig{
				Application: app.ApplicationConfig{
					InsuranceCarrier: constants.InsuranceCarrierFalseLake,
					IsNonAdmitted:    false,
				},
			},
			AdditionalInsuredInfo: &app.AdditionalInsuredInfo{
				Email: null.StringFrom("<EMAIL>"),
			},
		},
		billingInfo: &billinginfo.Info{
			MTCRate:              pointer_utils.ToPointer(3.0),
			FlatCharge:           pointer_utils.ToPointer(1000.0),
			PaymentMethod:        billing_enums.PaymentMethodMonthlyReporter,
			ProjectedMiles:       5000,
			DepositPaymentMethod: pointer_utils.ToPointer(billing_enums.DepositPaymentInvoiceCash),
		},
		initialBillingStaticParams: []billing_enums.TypedValue{},
		indOpt:                     &app.IndicationOption{ID: uuid.NewString()},
		agencyObj:                  &agency.Agency{ID: agencyID},
		dotDetails: &models.DotDetails{
			Census: models.Census{
				PhysicalAddressState: pointer_utils.String("CA"),
				PhysicalAddressCity:  pointer_utils.String("Los Angeles"),
			},
		},
		signPacketFormComp:  mockSignaturePacketFormCompilation,
		policySetIdentifier: "2145612",
		policySetId:         uuid.New(),
		appReview: &uw.ApplicationReview{
			Id: uuid.New().String(),
			CameraSubsidyDetails: &uw.CameraSubsidyDetails{
				NumberOfCameras: 5,
				SubsidyAmount:   pointer_utils.ToPointer(2500.0),
				CameraProvider:  telematics.TSPSamsara,
			},
		},
	}
}

func createMockUser(userID uuid.UUID) *authz.User {
	return &authz.User{UserInfo: authz.UserInfo{ID: userID, Email: "<EMAIL>"}}
}

func createExpectedUpsertIBRequest(
	agencyID, bindableSubID, appID, sigPacketFormComID, glPolicyFormCompilationID, alPolicyFormCompID string,
) *service.UpsertInsuranceBundleRequest {
	// ---------- Core InsuranceBundle ----------
	ib := model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).
		WithExternalId("").
		WithInternalId("").
		WithDefaultCarrier(insurancecoreproto.InsuranceCarrier_InsuranceCarrierFallsLake).
		WithAgencyID(agencyID).
		WithState(model.InsuranceBundleState_InsuranceBundleState_Invalid).
		WithDefaultEffectiveDuration(&proto.Interval{
			Start: timestamppb.New(time.Date(2024, 12, 1, 0, 0, 0, 0, time.UTC)),
			End:   timestamppb.New(time.Date(2025, 12, 1, 0, 0, 0, 0, time.UTC)),
		}).
		WithMetadata(&model.InsuranceBundleMetadata{
			RootBindableSubmissionId: bindableSubID,
			RootApplicationId:        appID,
		}).
		WithFormInfo(&insurancecoreproto.FormInfo{CoreForm: &insurancecoreproto.FormCore{
			FormCompilationId:   sigPacketFormComID,
			FormCompilationType: insurancecoreproto.FormCompilationType_FormCompilationTypeSignaturePacket,
			FormCodes:           []string{"CA 00 20 11 20", "CAP 70 81 00 01 22", "CA 23 17 11 20", "IL N 082 09 03", "IL N 120 10 05"},
		}}).
		WithCreatedAt(nil).
		WithUpdatedAt(nil).
		Build()

	// ---------- Primary Insured ----------
	primaryInsured := insurancecoreproto.NewInsuredBuilder().
		WithType(insurancecoreproto.InsuredType_InsuredType_PrimaryInsured).
		WithName(&insurancecoreproto.InsuredName{BusinessName: "Test Company"}).
		WithId("").
		Build()
	// Override address & external identifier exactly as expected.
	primaryInsured.Address = &proto.Address{
		Nation: pointer_utils.String("US"),
		State:  pointer_utils.String("CA"),
		City:   pointer_utils.String("Los Angeles"),
	}
	primaryInsured.ExternalIdentifier = &insurancecoreproto.InsuredIdentifier{
		Type:  insurancecoreproto.InsuredIdentifierType_InsuredIdentifierType_DOTNumber,
		Value: []string{"123456"},
	}

	// ---------- Common Effective Interval ----------
	effInterval := &proto.Interval{
		Start: timestamppb.New(time.Date(2024, 12, 1, 0, 0, 0, 0, time.UTC)),
		End:   timestamppb.New(time.Date(2025, 12, 1, 0, 0, 0, 0, time.UTC)),
	}

	// ---------- Auto Liability Policy ----------
	alPolicy := model.NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).
		WithPolicyNumber("NISTK2145612-24").
		WithState(model.PolicyState_PolicyState_Created).
		WithId("").
		WithCoverages([]*model.Coverage{
			{
				Id:          "CoverageAutoLiability",
				DisplayName: "Auto Liability",
				SubCoverages: []*model.SubCoverage{
					{
						Id:          "CoverageBodilyInjury",
						DisplayName: "Bodily Injury",
					},
					{
						Id:          "CoveragePropertyDamage",
						DisplayName: "Property Damage",
					},
					{
						Id:          "CoverageHiredAuto",
						DisplayName: "Hired Auto Liability",
					},
					{
						Id:          "CoverageUninsuredMotoristBodilyInjury",
						DisplayName: "Uninsured Motorist Bodily Injury",
					},
				},
			},
			{
				Id:          "CoverageAutoPhysicalDamage",
				DisplayName: "Auto Physical Damage",
				SubCoverages: []*model.SubCoverage{
					{
						Id:          "CoverageCollision",
						DisplayName: "Collision",
					},
					{
						Id:          "CoverageComprehensive",
						DisplayName: "Comprehensive",
					},
				},
			},
		}).
		WithEffectiveInterval(effInterval).
		WithBindableSubmissionId(uuid.MustParse(bindableSubID)).
		WithApplicationId(uuid.MustParse(appID)).
		WithCoreForm(&insurancecoreproto.FormCore{
			FormCompilationId:   alPolicyFormCompID,
			FormCompilationType: insurancecoreproto.FormCompilationType_FormCompilationTypePolicy,
			FormCodes:           []string{"CA 00 20 11 20", "CAP 70 81 00 01 22", "CA 23 17 11 20", "IL N 082 09 03", "IL N 120 10 05"},
		}).
		WithCharges([]*ptypes.Charge{}).
		WithClauses(buildStandardClauseList()).
		WithWrittenExposure(
			&model.Exposure{Exposures: &model.Exposure_FleetExposure{FleetExposure: &fleetmodel.Exposure{
				ProjectedMileage: 200000,
				QuotedMileage:    100000,
			}}},
		).
		UnsetChargeAdjustments().
		UnsetNonPrimaryInsureds().
		WithAdditionalForms(nil).
		Build()

	alPolicy.WrittenExposure = &model.Exposure{Exposures: &model.Exposure_FleetExposure{FleetExposure: &fleetmodel.Exposure{
		ProjectedMileage: 200000,
		QuotedMileage:    100000,
	}}}

	// ---------- General Liability Policy ----------
	glPolicy := model.NewPolicyBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).
		WithId("").
		WithPolicyNumber("NISGL2145612-24").
		WithState(model.PolicyState_PolicyState_Created).
		WithId("").
		UnsetNonPrimaryInsureds().
		UnsetChargeAdjustments().
		WithCoverages([]*model.Coverage{{
			Id:          "CoverageGeneralLiability",
			DisplayName: "General Liability",
			SubCoverages: []*model.SubCoverage{{
				Id:          "CoverageGeneralLiability",
				DisplayName: "General Liability",
			}, {
				Id:          "CoverageStopGap",
				DisplayName: "Stop Gap",
			}},
		}}).
		WithEffectiveInterval(effInterval).
		WithBindableSubmissionId(uuid.MustParse(bindableSubID)).
		WithApplicationId(uuid.MustParse(appID)).
		WithCoreForm(&insurancecoreproto.FormCore{
			FormCompilationId:   glPolicyFormCompilationID,
			FormCompilationType: insurancecoreproto.FormCompilationType_FormCompilationTypePolicy,
			FormCodes:           []string{"CA 00 20 11 20", "CAP 70 81 00 01 22", "CA 23 17 11 20", "IL N 082 09 03", "IL N 120 10 05"},
		}).
		WithAdditionalForms(nil).
		WithCharges([]*ptypes.Charge{}).
		WithClauses(buildStandardClauseList()).
		WithWrittenExposure(
			&model.Exposure{Exposures: &model.Exposure_FleetExposure{FleetExposure: &fleetmodel.Exposure{
				ProjectedMileage: 200000,
				QuotedMileage:    100000,
			}}},
		).
		Build()

	// ---------- Segment ----------
	segment := model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).
		WithId("a762d9c4-8a10-4a70-94c0-584d3a842bd4").
		WithInterval(effInterval).
		WithPrimaryInsured(primaryInsured).
		WithPolicies(map[string]*model.Policy{
			alPolicy.PolicyNumber: alPolicy,
			glPolicy.PolicyNumber: glPolicy,
		}).
		WithCoverageCriteria(buildCoverageCriteria()).
		Build()
	ib.Segments = []*model.InsuranceBundleSegment{segment}

	// ---------- Insured ----------
	insured := &insuredmodel.Insured{
		Name: &insurancecoreproto.InsuredName{BusinessName: "Test Company"},
		Address: &proto.Address{
			Nation: pointer_utils.String("US"),
			State:  pointer_utils.String("CA"),
			City:   pointer_utils.String("Los Angeles"),
		},
		ExternalIdentifier: &insurancecoreproto.InsuredIdentifier{
			Type:  insurancecoreproto.InsuredIdentifierType_InsuredIdentifierType_DOTNumber,
			Value: []string{"123456"},
		},
		ContactInfo: &insuredmodel.ContactInfo{
			Email: "<EMAIL>",
		},
	}

	return &service.UpsertInsuranceBundleRequest{InsuranceBundle: ib, Insured: insured}
}

// buildStandardClauseList centralises the boiler-plate blanket clauses used across policies.
func buildStandardClauseList() *insurancecoreproto.ClauseList {
	return &insurancecoreproto.ClauseList{Clauses: []*insurancecoreproto.Clause{{
		Id:   &insurancecoreproto.ClauseId{Id: "ClauseTypeWaiverOfSubrogation"},
		Type: insurancecoreproto.ClauseType_ClauseTypeWaiverOfSubrogation,
		ParticipantScope: &insurancecoreproto.ParticipantScope{
			Type: insurancecoreproto.ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeBlanket,
		},
	}, {
		Id:   &insurancecoreproto.ClauseId{Id: "ClauseTypeUIIA"},
		Type: insurancecoreproto.ClauseType_ClauseTypeUIIA,
		ParticipantScope: &insurancecoreproto.ParticipantScope{
			Type: insurancecoreproto.ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeBlanket,
		},
	}, {
		Id:   &insurancecoreproto.ClauseId{Id: "ClauseTypeAdditionalInsured"},
		Type: insurancecoreproto.ClauseType_ClauseTypeAdditionalInsured,
		ParticipantScope: &insurancecoreproto.ParticipantScope{
			Type: insurancecoreproto.ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeBlanket,
		},
	}}}
}

// buildCoverageCriteria mirrors the previous inline CoverageCriteria definition but has been
// extracted for clarity and to avoid duplicating code between the GL & TK policies.
func buildCoverageCriteria() *model.CoverageCriteria {
	return &model.CoverageCriteria{
		Limits: []*model.Limit{{
			Id:             "CoverageAutoLiability",
			DisplayName:    "Auto Liability",
			SubCoverageIds: []string{"CoverageBodilyInjury", "CoveragePropertyDamage"},
			Amount:         1000000,
			Grouping:       model.LimitGrouping_LimitGrouping_Combined,
		}, {
			Id:             "CoverageAutoPhysicalDamage",
			DisplayName:    "Auto Physical Damage",
			SubCoverageIds: []string{"CoverageCollision", "CoverageComprehensive"},
			Amount:         100000,
			Grouping:       model.LimitGrouping_LimitGrouping_Single,
		}, {
			Id:             "CoverageStopGap",
			DisplayName:    "Stop Gap",
			SubCoverageIds: []string{"CoverageStopGap"},
			Amount:         25000,
			Grouping:       model.LimitGrouping_LimitGrouping_Single,
		}, {
			Id:             "CoverageGeneralLiability",
			DisplayName:    "General Liability",
			SubCoverageIds: []string{"CoverageGeneralLiability"},
			Amount:         300000,
			Grouping:       model.LimitGrouping_LimitGrouping_Single,
		}, {
			Id:             "CoverageCargoAtScheduledTerminals$123 Test St",
			DisplayName:    "Cargo At Scheduled Terminals for terminal 123 Test St",
			SubCoverageIds: []string{"CoverageCargoAtScheduledTerminals"},
			Amount:         420000,
			Grouping:       model.LimitGrouping_LimitGrouping_Single,
			ExposureEntities: []*model.ExposureEntity{{
				Id:   "123 Test St",
				Type: model.ExposureEntityType_EXPOSURE_ENTITY_TYPE_TERMINAL_CARGO,
			}},
		}},
		Deductibles: []*model.Deductible{{
			SubCoverageIds: []string{"CoverageBodilyInjury", "CoveragePropertyDamage"},
			Amount:         1000,
		}, {
			SubCoverageIds: []string{"CoverageCollision"},
			Amount:         1000,
		}, {
			SubCoverageIds: []string{"CoverageComprehensive"},
			Amount:         1000,
		}, {
			SubCoverageIds: []string{"CoverageHiredAuto"},
			Amount:         2000,
		}, {
			SubCoverageIds: []string{"CoverageUninsuredMotoristBodilyInjury"},
			Amount:         1500,
		}, {
			SubCoverageIds: []string{"CoverageCargoAtScheduledTerminals"},
			Amount:         10000,
		}, {
			SubCoverageIds: []string{"CoverageGeneralLiability"},
			Amount:         1000,
		}},
		CombinedDeductibles: []*model.CombinedDeductible{{
			SubCoverageIds: []string{"CoverageCollision", "CoverageComprehensive", "CoverageMotorTruckCargo"},
		}, {
			SubCoverageIds: []string{"CoverageHiredAuto", "CoverageUninsuredMotoristBodilyInjury"},
		}},
	}
}
