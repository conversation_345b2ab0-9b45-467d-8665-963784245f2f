package emails

import (
	"context"
	"fmt"
	"time"

	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"

	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/handlers/utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/emailer/models"
	"nirvanatech.com/nirvana/infra/authz"
)

const (
	dotNumber        = "dot_num"
	agentName        = "agent_name"
	effectiveDate    = "eff_date"
	application_link = "application_link"
	subject          = "subject"
	company_name     = "company_name"

	submittedApplicationStatus = "submitted"
	createApplicationStatus    = "create"
	renewalApplicationStatus   = "renew"

	// metrics data
	clearanceEmailErrorMetric = "send_clearance_email.error"
	successTagKey             = "success"
	failureTagValue           = "false"
)

type EmailWrapperImpl struct {
	Deps *Deps
}

func NewEmailsWrapper(deps Deps) EmailWrapper {
	return EmailWrapperImpl{
		Deps: &deps,
	}
}

func (e EmailWrapperImpl) SendExistingPolicyDifferentAgencyEmail(ctx context.Context, application application.Application) error {
	subject := fmt.Sprintf("Action Required: Clearance needed for %s (%d)", application.CompanyInfo.Name, application.CompanyInfo.DOTNumber)

	return e.sendEmailWithType(ctx, application, subject, models.ExistingPolicyUpcomingRenewalDifferentAgencyEmail)
}

func (e EmailWrapperImpl) SendAgentLostClearanceEmail(ctx context.Context, application application.Application) error {
	subject := fmt.Sprintf("BOR Accepted from another agent for %s (%d)", application.CompanyInfo.Name, application.CompanyInfo.DOTNumber)

	return e.sendEmailWithType(ctx, application, subject, models.AgentLostClearance)
}

func (e EmailWrapperImpl) SendAgentAchievedBOR(ctx context.Context, application application.Application) error {
	subject := fmt.Sprintf("BOR Accepted for %s (%d)", application.CompanyInfo.Name, application.CompanyInfo.DOTNumber)

	return e.sendEmailWithType(ctx, application, subject, models.AgentAchievedBOR)
}

func (e EmailWrapperImpl) SendNewBusinessClearanceEstablishedEmail(ctx context.Context, application application.Application) error {
	subject := fmt.Sprintf("Clearance Established for %s (%d)", application.CompanyInfo.Name, application.CompanyInfo.DOTNumber)

	return e.sendEmailWithType(ctx, application, subject, models.ClearanceEstablishedEmail)
}

func (e EmailWrapperImpl) SendAnotherAgentClearedSubmittedApplication(ctx context.Context, application application.Application) error {
	subject := fmt.Sprintf("Action Required: Clearance Status Change for %d", application.CompanyInfo.DOTNumber)

	return e.sendEmailWithType(ctx, application, subject, models.AnotherAgentClearedSubmittedEmail)
}

// Different Agency `RequiresClearance` Emails
func (e EmailWrapperImpl) SendAgentRequiresBORForExistingSubmission(ctx context.Context, application application.Application) error {
	subject := fmt.Sprintf("Action Required: Clearance needed for %s (%d)", application.CompanyInfo.Name, application.CompanyInfo.DOTNumber)

	return e.sendEmailWithType(ctx, application, subject, models.AgentRequiresBORForExistingSubmission)
}

func (e EmailWrapperImpl) SendAgentRequiresBORForNewSubmission(ctx context.Context, application application.Application) error {
	subject := fmt.Sprintf("Action Required: Clearance needed for %s (%d)", application.CompanyInfo.Name, application.CompanyInfo.DOTNumber)

	return e.sendEmailWithType(ctx, application, subject, models.SendAgentRequiresBORForNewSubmission)
}

// Same Agency `RequiresClearance` Emails
func (e EmailWrapperImpl) SendSameAgencyClearanceConflictEmail(ctx context.Context, application application.Application) error {
	subject := fmt.Sprintf("Clearance established by your agency for %s (%d)", application.CompanyInfo.Name, application.CompanyInfo.DOTNumber)

	return e.sendEmailWithType(ctx, application, subject, models.SameAgencyClearanceConflictEmail)
}

func (e EmailWrapperImpl) SendSameAgenyClearanceConflictNewBusinessEmail(ctx context.Context, application application.Application) error {
	subject := fmt.Sprintf("Action Required: Clearance needed for %s (%d)", application.CompanyInfo.Name, application.CompanyInfo.DOTNumber)

	return e.sendEmailWithType(ctx, application, subject, models.SameAgencyClearanceConflictNewBusinessEmail)
}

func (e EmailWrapperImpl) SendExistingClearedApplicationDeclinedEmail(ctx context.Context, application application.Application) error {
	subject := fmt.Sprintf("Previously declined account for %s (%d)", application.CompanyInfo.Name, application.CompanyInfo.DOTNumber)

	return e.sendEmailWithType(ctx, application, subject, models.ExistingApplicationClearedDeclinedEmail)
}

func (e EmailWrapperImpl) GetUser(ctx context.Context, userID string) (*authz.User, error) {
	userUUID, err := uuid.Parse(userID)
	ctx = log.ContextWithFields(ctx, log.String("userID", userID))
	if err != nil {
		_ = e.Deps.MetricsClient.Inc(clearanceEmailErrorMetric, 1.0, 1, statsd.Tag{successTagKey, failureTagValue})
		log.Error(ctx, "unable to parse userID", log.Err(err))
		return nil, errors.Wrap(err, "unable to parse userID")
	}
	user, err := e.Deps.AuthWrapper.FetchAuthzUser(ctx, userUUID)
	if err != nil {
		_ = e.Deps.MetricsClient.Inc(clearanceEmailErrorMetric, 1.0, 1, statsd.Tag{successTagKey, failureTagValue})
		log.Error(ctx, "unable to fetch authz user", log.Err(err))
		return nil, errors.Wrap(err, "unable to fetch authz user")
	}
	if user == nil {
		_ = e.Deps.MetricsClient.Inc(clearanceEmailErrorMetric, 1.0, 1, statsd.Tag{successTagKey, failureTagValue})
		log.Error(ctx, "fetched authz user is nil")
		return nil, errors.Wrap(err, "fetched authz user is nil")
	}
	return user, nil
}

func (e EmailWrapperImpl) sendEmailWithType(ctx context.Context, application application.Application, subject string, emailType models.Email) error {
	if !utils.ShouldRunValidation(application.AgencyID) {
		return nil
	}

	if application.CompanyInfo != nil {
		if utils.ShouldSkipClearance([]uuid.UUID{application.AgencyID}) {
			log.Info(ctx, "Skipping clearance email for whitelisted DOT number", log.Int64("dot_number", application.CompanyInfo.DOTNumber))
			return nil
		}
	}

	// Get application agent for template data
	applicationAgent, err := e.GetUser(ctx, *application.ProducerID)
	if err != nil {
		_ = e.Deps.MetricsClient.Inc(clearanceEmailErrorMetric, 1.0, 1, statsd.Tag{successTagKey, failureTagValue})
		log.Error(ctx, "Unable to get application agent", log.Err(err))
		return errors.Wrap(err, "Unable to get application agent")
	}

	applicationIdUUID, err := uuid.Parse(application.ID)
	if err != nil {
		_ = e.Deps.MetricsClient.Inc(clearanceEmailErrorMetric, 1.0, 1, statsd.Tag{successTagKey, failureTagValue})
		log.Error(ctx, "Unable to parse application ID", log.Err(err))
		return errors.Wrap(err, "Unable to parse application ID")
	}

	effectiveDate := application.CoverageInfo.EffectiveDate
	// Get latest application review
	applicationReview, err := e.Deps.ApplicationReviewWrapper.GetLatestPendingReview(ctx, applicationIdUUID.String())
	if err == nil {
		effectiveDate = applicationReview.EffectiveDate
	} else {
		if !errors.Is(err, uw.ErrAppReviewNotFound) {
			_ = e.Deps.MetricsClient.Inc(clearanceEmailErrorMetric, 1.0, 1, statsd.Tag{successTagKey, failureTagValue})
			log.Error(ctx, "Unable to get latest application review", log.Err(err))
		}
	}

	templateData, err := getTemplateDate(application, applicationAgent, subject, emailType, effectiveDate)
	if err != nil {
		_ = e.Deps.MetricsClient.Inc(clearanceEmailErrorMetric, 1.0, 1, statsd.Tag{successTagKey, failureTagValue})
		log.Error(ctx, "Unable to get template data", log.Err(err))
		return errors.Wrap(err, "Unable to get template data")
	}

	recipients, err := e.getRecipients(ctx, application, emailType)
	if err != nil {
		_ = e.Deps.MetricsClient.Inc(clearanceEmailErrorMetric, 1.0, 1, statsd.Tag{successTagKey, failureTagValue})
		log.Error(ctx, "Unable to get email recipients", log.Err(err))
		return errors.Wrap(err, "Unable to get email recipients")
	}

	emailRequest := &models.SendRequest{
		EmailType:    emailType,
		Sender:       models.NirvanaSupportContact,
		Subject:      subject,
		Recipients:   recipients,
		TemplateData: templateData,
	}

	_, err = e.Deps.Emailer.Send(ctx, emailRequest)
	if err != nil {
		_ = e.Deps.MetricsClient.Inc(clearanceEmailErrorMetric, 1.0, 1, statsd.Tag{successTagKey, failureTagValue})
		log.Error(ctx, "Unable to send BOR email", log.Err(err))
		return errors.Wrap(err, "Unable to send BOR email")
	}

	_ = e.Deps.MetricsClient.Inc(clearanceEmailErrorMetric, 1.0, 1, statsd.Tag{successTagKey, successTagKey})

	return nil
}

func (e EmailWrapperImpl) getRecipients(ctx context.Context, application application.Application, emailType models.Email) ([]models.Contact, error) {
	// Track unique email addresses
	uniqueEmails := make(map[string]bool)
	var recipients []models.Contact

	// Helper function to add recipient if email is unique
	addRecipient := func(contact models.Contact) {
		if !uniqueEmails[contact.EmailAddress] {
			uniqueEmails[contact.EmailAddress] = true
			recipients = append(recipients, contact)
		}
	}

	marketer, err := e.GetUser(ctx, application.MarketerID.String())
	if err != nil {
		return nil, errors.Wrap(err, "Unable to get marketer")
	}
	addRecipient(models.Contact{Name: marketer.FullName(), EmailAddress: marketer.Email})

	// Get Producer
	if application.ProducerID != nil {
		producer, err := e.GetUser(ctx, *application.ProducerID)
		if err != nil {
			return nil, errors.Wrap(err, "Unable to get producer")
		}
		addRecipient(models.Contact{Name: producer.FullName(), EmailAddress: producer.Email})
	}

	// Get BD
	currentProgramType := enums.ProgramTypeFleet
	bdUser, err := e.Deps.AgencyBDWrapper.GetBDForAgency(ctx, application.AgencyID, currentProgramType)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to get BD for agency")
	}
	bd, err := e.Deps.AuthWrapper.FetchAuthzUser(ctx, bdUser.UserID)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to get BD")
	}
	addRecipient(models.Contact{Name: bd.FullName(), EmailAddress: bd.Email})

	return recipients, nil
}

func getTemplateDate(
	application application.Application,
	applicationAgent *authz.User,
	emailSubject string,
	emailType models.Email,
	effectiveDateVal time.Time,
) (map[string]any, error) {
	applicationStatus := submittedApplicationStatus
	if emailType == models.AgentRequiresBORForExistingSubmission ||
		emailType == models.SendAgentRequiresBORForNewSubmission ||
		emailType == models.SameAgencyClearanceConflictNewBusinessEmail ||
		emailType == models.SameAgencyClearanceConflictEmail ||
		emailType == models.ExistingPolicyUpcomingRenewalDifferentAgencyEmail ||
		emailType == models.ExistingApplicationClearedDeclinedEmail {
		applicationStatus = createApplicationStatus
	}

	if application.IsRenewal() {
		applicationStatus = renewalApplicationStatus
	}

	applicationLink := fmt.Sprintf("https://agents.nirvanatech.com/applications/%s/%s", application.ID, applicationStatus)

	templateData := map[string]any{
		dotNumber:        application.CompanyInfo.DOTNumber,
		agentName:        applicationAgent.FullName(),
		effectiveDate:    effectiveDateVal.Format("2006-01-02"),
		application_link: applicationLink,
		subject:          emailSubject,
		company_name:     application.CompanyInfo.Name,
	}

	return templateData, nil
}
