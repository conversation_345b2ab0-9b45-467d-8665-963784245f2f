package impl

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/multierr"
	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	applicationutil "nirvanatech.com/nirvana/common-go/application-util"
	authutil "nirvanatech.com/nirvana/common-go/auth-util"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/url_util"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	sharing_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/sharing"
	"nirvanatech.com/nirvana/emailer/models"
	message "nirvanatech.com/nirvana/external_client/salesforce/jobs"
	"nirvanatech.com/nirvana/external_data_management/clients_management"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	fmcsa_models "nirvanatech.com/nirvana/fmcsa/models"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/constants"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/event"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/pricing/explainability/jobs/process_entities"
	"nirvanatech.com/nirvana/pricing/explainability/jobs/process_entities/messages"
	"nirvanatech.com/nirvana/rating/rateml/program"
	"nirvanatech.com/nirvana/sharing"
)

const (
	scheme                = "https"
	host                  = "agents.nirvanatech.com"
	getApplicationURLPath = "applications/%s/submitted"
	successTagKey         = "success"
	quotingPrefix         = "quoting"
	counterSuffix         = "counter"
	latencySuffix         = "latency"

	metricTagKeyStatus          = "status"
	metricTagKeyIsTest          = "isTest"
	metricTagKeyIsNil           = "isNil"
	metricTagKeyIsRaceCondition = "isRaceCondition"
	metricTagValueFailed        = "failed"
	metricTagValueSucceeded     = "succeeded"
	metricTagValueTrue          = "true"
	metricTagValueFalse         = "false"
	defaultMaxRetry             = 2
	defaultBDEmail              = "<EMAIL>"
)

type CommonAppData struct {
	AppID           uuid.UUID
	EffectiveDate   time.Time
	AgencyID        uuid.UUID
	UnderwriterID   uuid.UUID
	CreatedBy       uuid.UUID
	MarketerID      *uuid.UUID
	ProducerID      *uuid.UUID
	AssignedBD      *uuid.UUID
	IsRenewal       bool
	DOTNumber       int64
	CompanyName     string
	TSPConnHandleId *string
	InsuredEmail    string
	InsuredName     string
}

type commonEmailArgs struct {
	underwriter        *authz.User
	underwriterDetails *applicationutil.UnderwriterDetails
	creator            *authz.User
	producer           *authz.User
	assignedBD         *authz.User
	applicationLink    string
}

func GetProducerFromAppObj(
	ctx context.Context,
	appObj *application.Application,
	deps *Deps,
) (*authz.User, error) {
	if appObj.ProducerID == nil {
		return nil, errors.Newf("producerID for appID: %v was found nil", appObj.ID)
	}
	user, err := GetUser(ctx, *appObj.ProducerID, deps.AuthWrapper)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get user")
	}
	return user, nil
}

type DefaultQuotingTaskRetryable[T jtypes.Message] struct{}

func (d *DefaultQuotingTaskRetryable[T]) ShouldRetry(
	_ jtypes.Context,
	_ T,
	failureError error,
	retryAttempt int,
) (bool, time.Duration) {
	if retryAttempt < defaultMaxRetry && (failureError == nil || errors.Is(failureError, program.MissingEntityTypeError)) {
		return true, 0
	}
	return false, 0
}

func GetUser(
	ctx context.Context,
	userID string,
	authWrapper auth.DataWrapper,
) (*authz.User, error) {
	userUUID, err := uuid.Parse(userID)
	ctx = log.ContextWithFields(ctx, log.String("userID", userID))
	if err != nil {
		log.Error(ctx, "unable to parse userID", log.Err(err))
		return nil, errors.Wrap(err, "unable to parse userID")
	}
	user, err := authWrapper.FetchAuthzUser(ctx, userUUID)
	if err != nil {
		log.Error(ctx, "unable to fetch authz user", log.Err(err))
		return nil, errors.Wrap(err, "unable to fetch authz user")
	}
	if user == nil {
		log.Error(ctx, "fetched authz user is nil")
		return nil, errors.Wrap(err, "fetched authz user is nil")
	}
	return user, nil
}

// NOTE: This is a temporary function used until all telematics job supports both Fleet and Non-Fleet programs.
func getCommonEmailArgs(jCtx jtypes.Context, deps *Deps, app *application.Application) (*commonEmailArgs, error) {
	uwUser, err := getUser(jCtx, deps.AuthWrapper, app.UnderwriterID.String())
	if err != nil {
		log.Error(
			jCtx, "failed to get underwriter user", log.Err(err),
			log.String("underwriterID", app.UnderwriterID.String()),
		)
		return nil, errors.Wrap(err, "failed to get underwriter user")
	}
	err = validateUnderwriterUser(uwUser)
	if err != nil {
		log.Error(jCtx, "failed to validateUnderwriterUser", log.Err(err), log.Any("underwriterUser", uwUser))
		return nil, errors.Wrap(err, "failed to validateUnderwriterUser")
	}
	uwDetails, err := applicationutil.GetUnderwriterDetails(uwUser.Email)
	if err != nil {
		log.Error(
			jCtx, "failed to GetUnderwriterDetails", log.Err(err),
			log.String("underwriterEmail", uwUser.Email),
		)
		return nil, errors.Wrap(err, "failed to GetUnderwriterDetails")
	}
	err = validateUnderwriterDetails(uwDetails)
	if err != nil {
		log.Error(jCtx, "failed to validateUnderwriterDetails", log.Err(err), log.Any("uwDetails", uwDetails))
		return nil, errors.Wrap(err, "failed to validateUnderwriterDetails")
	}

	creator, err := getUser(jCtx, deps.AuthWrapper, app.MarketerID.String())
	if err != nil {
		log.Error(
			jCtx, "failed to get creator user", log.Err(err),
			log.String("appID", app.ID),
		)
		return nil, errors.Wrap(err, "failed to get creator user")
	}

	producer, err := getUser(jCtx, deps.AuthWrapper, *app.ProducerID)
	if err != nil {
		log.Error(
			jCtx, "failed to get producer user", log.Err(err),
			log.String("producerID", *app.ProducerID),
		)
		return nil, errors.Wrap(err, "failed to get producer user")
	}

	assignedBD, err := GetBD(jCtx, deps.AuthWrapper, app.AssignedBD)
	if err != nil {
		return nil, err
	}
	return &commonEmailArgs{
		underwriter:        uwUser,
		underwriterDetails: uwDetails,
		creator:            creator,
		producer:           producer,
		assignedBD:         assignedBD,
		applicationLink:    generateApplicationLink(app.ID),
	}, nil
}

func getEmailArgsForAppData(jCtx jtypes.Context, deps *Deps, appData *CommonAppData) (*commonEmailArgs, error) {
	uwUser, err := getUser(jCtx, deps.AuthWrapper, appData.UnderwriterID.String())
	if err != nil {
		log.Error(
			jCtx, "failed to get underwriter user", log.Err(err),
			log.String("underwriterID", appData.UnderwriterID.String()),
		)
		return nil, errors.Wrap(err, "failed to get underwriter user")
	}
	err = validateUnderwriterUser(uwUser)
	if err != nil {
		log.Error(jCtx, "failed to validateUnderwriterUser", log.Err(err), log.Any("underwriterUser", uwUser))
		return nil, errors.Wrap(err, "failed to validateUnderwriterUser")
	}

	uwDetails, err := applicationutil.GetUnderwriterDetails(uwUser.Email)
	if err != nil {
		log.Error(
			jCtx, "failed to GetUnderwriterDetails", log.Err(err),
			log.String("underwriterEmail", uwUser.Email),
		)
		return nil, errors.Wrap(err, "failed to GetUnderwriterDetails")
	}
	err = validateUnderwriterDetails(uwDetails)
	if err != nil {
		log.Error(jCtx, "failed to validateUnderwriterDetails", log.Err(err), log.Any("uwDetails", uwDetails))
		return nil, errors.Wrap(err, "failed to validateUnderwriterDetails")
	}

	creator, err := getUser(jCtx, deps.AuthWrapper, appData.MarketerID.String())
	if err != nil {
		log.Error(
			jCtx, "failed to get creator user", log.Err(err),
		)
		return nil, errors.Wrap(err, "failed to get creator user")
	}

	producer, err := getUser(jCtx, deps.AuthWrapper, appData.ProducerID.String())
	if err != nil {
		log.Error(
			jCtx, "failed to get producer user", log.Err(err),
			log.String("producerID", appData.ProducerID.String()),
		)
		return nil, errors.Wrap(err, "failed to get producer user")
	}

	assignedBD, err := GetBD(jCtx, deps.AuthWrapper, appData.AssignedBD)
	if err != nil {
		return nil, err
	}

	return &commonEmailArgs{
		underwriter:        uwUser,
		underwriterDetails: uwDetails,
		creator:            creator,
		producer:           producer,
		assignedBD:         assignedBD,
		applicationLink:    generateApplicationLink(appData.AppID.String()),
	}, nil
}

func GetBD(
	jCtx jtypes.Context,
	authWrapper auth.DataWrapper,
	bd *uuid.UUID,
) (*authz.User, error) {
	var assignedBD *authz.User
	var err error
	if bd != nil {
		assignedBD, err = getUser(jCtx, authWrapper, bd.String())
		if err != nil {
			log.Error(
				jCtx, "failed to get assigned BD", log.Err(err),
				log.String("assignedBD", bd.String()),
			)
			return nil, errors.Wrap(err, "failed to get assigned user")
		}
	} else {
		// If BD is not assigned,
		// add erin as default as per this https://nirvana-tech.slack.com/archives/C06FZ5JT9MZ/p1741878362846529.
		defaultBD, pErr := authWrapper.FetchAuthzUserByEmail(jCtx, defaultBDEmail)
		if pErr != nil {
			return nil, errors.Wrapf(pErr, "failed to fetch default BD with email %s", defaultBDEmail)
		}

		assignedBD = defaultBD
	}
	return assignedBD, nil
}

func getUser(jCtx jtypes.Context, authWrapper auth.DataWrapper, userID string) (*authz.User, error) {
	uw, err := GetUser(jCtx, userID, authWrapper)
	if err != nil {
		return nil, err
	}
	return uw, nil
}

func validateUnderwriterUser(uw *authz.User) error {
	if uw == nil {
		return errors.New("nil underwriter user fetched from authz")
	}
	if uw.Email == "" {
		return errors.New("empty underwriter email in authz underwriter user")
	}
	if uw.FirstName == "" {
		return errors.New("empty underwriter first name in authz underwriter user")
	}
	return nil
}

func validateUnderwriterDetails(uwDetails *applicationutil.UnderwriterDetails) error {
	if uwDetails == nil {
		return errors.New("nil underwriterDetails")
	}
	if uwDetails.Designation == "" {
		return errors.New("empty designation in underwriterDetails")
	}
	if uwDetails.PhoneNumber == "" {
		return errors.New("empty phoneNumber in underwriterDetails")
	}
	return nil
}

func validateCommonApplicationFields(app *application.Application) error {
	if app == nil {
		return errors.New("nil application")
	}
	if app.ProducerID == nil {
		return errors.New("nil producerID")
	}
	if app.CreatedBy == "" {
		return errors.New("empty creatorID")
	}
	if app.CompanyInfo == nil || app.CompanyInfo.Name == "" || app.CompanyInfo.DOTNumber == 0 {
		return errors.New("invalid CompanyInfo")
	}
	return nil
}

func getCCs(
	jCtx jtypes.Context, args *commonEmailArgs, app *application.Application, shouldAddUW bool,
) (ccs []models.Contact, err error) {
	creatorID := args.creator.ID.String()
	producerID := *app.ProducerID
	if producerID != creatorID {
		ccs = append(ccs, authutil.GenerateContactFromUserInfo(args.creator.UserInfo))
	} else {
		log.Warn(
			jCtx, "producerID and creatorID are the same", log.String("producerID", producerID),
			log.String("creatorID", creatorID),
		)
	}

	// We don't want to spam underwriters and assigned BDs with emails of test agencies.
	if !constants.TestAgencies()[app.AgencyID] {
		if shouldAddUW {
			underwriterID := args.underwriter.ID.String()
			switch underwriterID {
			case producerID:
				log.Warn(
					jCtx, "underwriterID and producerID are the same", log.String("producerID", producerID),
					log.String("underwriterID", underwriterID),
				)
			case creatorID:
				log.Warn(
					jCtx, "underwriterID and creatorID are the same", log.String("creatorID", creatorID),
					log.String("underwriterID", underwriterID),
				)
			default:
				ccs = append(ccs, authutil.GenerateContactFromUserInfo(args.underwriter.UserInfo))
			}
		}

		if !shouldAddUW && args.assignedBD != nil {
			ccs = append(ccs, authutil.GenerateContactFromUserInfo(args.assignedBD.UserInfo))
		}
	}

	return ccs, nil
}

func generateApplicationLink(appID string) string {
	return generateQuotingAppLink(fmt.Sprintf(getApplicationURLPath, appID))
}

func generateQuotingAppLink(urlPath string) string {
	return scheme + "://" + host + "/" + urlPath
}

func compiledIndicationOptionsCoveragesFromSubmission(
	sub application.SubmissionObject,
	indOpts []application.IndicationOption,
) []application.IndicationOption {
	for idx, option := range indOpts {
		opt := compileIndicationOptionCoveragesFromSubmission(sub, option)
		indOpts[idx] = opt
	}
	return indOpts
}

func compileIndicationOptionCoveragesFromSubmission(
	sub application.SubmissionObject,
	option application.IndicationOption,
) application.IndicationOption {
	for _, subCov := range sub.CoverageInfo.Coverages {
		found := false
		for i, cov := range option.Coverages {
			if cov.CoverageType == subCov.CoverageType {
				cov.Limit = subCov.Limit
				cov.Deductible = subCov.Deductible
				option.Coverages[i] = cov
				found = true
			}
		}
		if !found {
			option.Coverages = append(option.Coverages, subCov)
		}
	}
	return option
}

func emitMetric(jCtx jtypes.Context, metricsClient statsd.Statter, job string, jobDuration time.Duration, err error) {
	jobSucceeded := err == nil
	errTag := statsd.Tag{successTagKey, strconv.FormatBool(jobSucceeded)}
	metricName := getMetricNameFromJob(job)

	metricsClientErr := metricsClient.Inc(convertToCounter(metricName), 1, 1, errTag)
	if metricsClientErr != nil {
		log.Error(
			jCtx, "failed to emit counter metric",
			log.String("metricName", metricName), log.Err(metricsClientErr),
		)
	}

	metricsClientErr = metricsClient.TimingDuration(convertToLatency(metricName), jobDuration, 1, errTag)
	if metricsClientErr != nil {
		log.Error(
			jCtx, "failed to emit latency metric",
			log.String("metricName", metricName), log.Err(metricsClientErr),
		)
	}
}

func getMetricNameFromJob(job string) string {
	return quotingPrefix + "." + job
}

func convertToCounter(metricName string) string {
	return metricName + "." + counterSuffix
}

func convertToLatency(metricName string) string {
	return metricName + "." + latencySuffix
}

func validateDotDetails(dotDetails *fmcsa_models.DotDetails) error {
	var err error
	if dotDetails == nil {
		return errors.New("nil dotDetails")
	}
	if dotDetails.Census.PhysicalAddressStreet == nil {
		err = multierr.Append(err, errors.New("nil PhysicalAddressStreet"))
	}
	if dotDetails.Census.PhysicalAddressCity == nil {
		err = multierr.Append(err, errors.New("nil PhysicalAddressCity"))
	}
	if dotDetails.Census.PhysicalAddressState == nil {
		err = multierr.Append(err, errors.New("nil PhysicalAddressState"))
	}
	if dotDetails.Census.PhysicalAddressZipCode == nil {
		err = multierr.Append(err, errors.New("nil PhysicalAddressZipCode"))
	}
	return err
}

func isPibitTrialRunFeatureTurnedOn(
	ctx context.Context,
	featFlagClient feature_flag_lib.Client,
	user *authz.User,
) (bool, error) {
	isFeatureTurnedOn, err := featFlagClient.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(*user), feature_flag_lib.FeaturePibitTrialRun, false)
	if err != nil {
		log.Error(
			ctx, "failed to get feature flag value", log.Err(err),
			log.Stringer("featureFlag", feature_flag_lib.FeaturePibitTrialRun),
		)
		return false, errors.Wrapf(err, "failed to get feature flag value for %s",
			feature_flag_lib.FeaturePibitTrialRun)
	}
	return isFeatureTurnedOn, nil
}

// FetchOrCreateNonExpiredConsentLink wraps the functions FetchOrCreateActiveConsentLinkForApplicationId and parses the
// shareable link into a string link with scheme and host
func FetchOrCreateNonExpiredConsentLink(
	ctx context.Context,
	sharingWrapper sharing_wrapper.DataWrapper,
	applicationId uuid.UUID,
	applicationCreatedBy uuid.UUID,
	programType policy_enums.ProgramType,
) (string, error) {
	sharingLink, err := sharing.FetchOrCreateActiveConsentLinkForApplicationId(
		ctx,
		sharingWrapper,
		applicationId,
		applicationCreatedBy,
	)
	if err != nil {
		return "", errors.Wrapf(
			err,
			"failed to fetch or create non-expired shareable link for application %s",
			applicationId,
		)
	}
	consentURL, err := url_util.GenerateQuotingConsentURL(sharingLink.ID, programType)
	if err != nil {
		return "", errors.Wrapf(
			err,
			"failed to generate quoting consent link with host for application %s",
			applicationId,
		)
	}
	return consentURL, nil
}

// DispatchSalesforceUpdateEvent TODO (Tanzeem): Emit a failure count metric here
// due to unexpected jobber downtime, error in marshalling job message, etc
func DispatchSalesforceUpdateEvent(
	ctx jtypes.Context,
	eventClient event.Client,
	payload *message.UpdateSalesforceOpportunityArgs,
) error {
	producerId, err := event.UseJobRunIdAsProducerId[*message.UpdateSalesforceOpportunityArgs](ctx, nil)
	if err != nil {
		log.Error(ctx, "Failed to get producer id", log.Err(err))
		return err
	}

	// trigger event
	eventJobRunId, err := event.Dispatch(
		ctx, eventClient, event.UpdateSalesforceOpportunityEvent,
		event.EventHandlerJobCreatorUuid(ctx.GetStore().Id(), ctx.GetJobRunId(), producerId, event.UpdateSalesforceOpportunityEvent),
		payload, producerId,
	)
	if err != nil {
		log.Error(ctx, "Failed to dispatch event", log.Err(err))
		return err
	}
	log.Info(
		ctx,
		"Dispatched UpdateSalesforceOpportunityEvent",
		log.String("event", string(event.UpdateSalesforceOpportunityEvent)),
		log.String("eventJobRunId", eventJobRunId.String()))

	return nil
}

func getPackageName(packageType app_enums.IndicationOptionTag) string {
	return strings.TrimPrefix(packageType.String(), "IndicationOptionTag")
}

func getWritableStoreFirstInterceptorsConfigs(contextID uuid.UUID, lookupDepth int) []*clients_management.InterceptorConfig {
	return []*clients_management.InterceptorConfig{
		{
			ConcreteConfig: &clients_management.InterceptorConfig_ReadFromStoreInterceptorConfig{
				ReadFromStoreInterceptorConfig: read_from_store_interceptor.NewStoreFirstConfig(contextID, lookupDepth),
			},
		},
		{
			ConcreteConfig: &clients_management.InterceptorConfig_WriteToStoreInterceptorConfig{
				WriteToStoreInterceptorConfig: write_to_store_interceptor.NewEnabledConfig(contextID),
			},
		},
	}
}

func triggerProcessEntitiesJob(
	ctx context.Context,
	quotingJobber quoting_jobber.Client,
	fileKey string,
	persistHistory bool,
) error {
	addJobRunParams := jobber.NewAddJobRunParams(
		process_entities.ProcessEntities,
		&messages.ProcessEntitiesMessage{
			FileKey:        fileKey,
			PersistHistory: persistHistory,
		},
		jtypes.NewMetadata(jtypes.OneOff),
	)
	jobRunID, err := quotingJobber.AddJobRun(ctx, addJobRunParams)
	if err != nil {
		log.Error(
			ctx,
			fmt.Sprintf("failed to add %s job",
				process_entities.ProcessEntities,
			),
			log.Err(err),
			log.Any("addJobRunParams", addJobRunParams),
		)
		return errors.Wrapf(
			err,
			"failed to add %s job for fileKey: %s",
			process_entities.ProcessEntities,
			fileKey,
		)
	}
	log.Info(
		ctx,
		fmt.Sprintf("added %s job", process_entities.ProcessEntities),
		log.Stringer("JobRunId", jobRunID),
		log.String("fileKey", fileKey),
		log.Bool("persistHistory", persistHistory),
	)
	return nil
}

func GetCommonAppData(
	ctx context.Context,
	fleetAppWrapper application.DataWrapper,
	admittedWrapper nf_app.Wrapper[*admitted_app.AdmittedApp],
	appID uuid.UUID,
	programType policy_enums.ProgramType,
) (*CommonAppData, error) {
	if programType == policy_enums.ProgramTypeNonFleetAdmitted {
		appObj, err := admittedWrapper.GetAppById(ctx, appID)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get application by id %s", appID.String())
		}

		return &CommonAppData{
			AppID:           appID,
			EffectiveDate:   appObj.EffectiveDate,
			AgencyID:        appObj.AgencyID,
			UnderwriterID:   appObj.UnderwriterID,
			CreatedBy:       appObj.CreatedBy,
			MarketerID:      &appObj.MarketerId,
			ProducerID:      pointer_utils.ToPointer(appObj.ProducerID),
			AssignedBD:      appObj.AssignedBD,
			IsRenewal:       appObj.IsRenewal(),
			DOTNumber:       int64(appObj.Info.CompanyInfo.DOTNumber),
			CompanyName:     appObj.Info.CompanyInfo.Name,
			TSPConnHandleId: pointer_utils.ToPointer(appObj.Info.TSPInfo.TSPConnHandleId.String()),
			InsuredEmail:    appObj.Info.TSPInfo.InsuredInfo.Email,
			InsuredName:     appObj.Info.TSPInfo.InsuredInfo.Name,
		}, nil
	}

	appObj, err := fleetAppWrapper.GetAppById(ctx, appID.String())
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get application by id %s", appID.String())
	}

	var insuredEmail, insuredName string
	if appObj.AdditionalInsuredInfo != nil && appObj.AdditionalInsuredInfo.Email.Valid {
		insuredEmail = appObj.AdditionalInsuredInfo.Email.String
		insuredName = appObj.AdditionalInsuredInfo.Name
	}

	appData := &CommonAppData{
		AppID:           appID,
		EffectiveDate:   appObj.CoverageInfo.EffectiveDate,
		AgencyID:        appObj.AgencyID,
		UnderwriterID:   appObj.UnderwriterID,
		MarketerID:      appObj.MarketerID,
		AssignedBD:      appObj.AssignedBD,
		IsRenewal:       appObj.IsRenewal(),
		DOTNumber:       appObj.CompanyInfo.DOTNumber,
		CompanyName:     appObj.CompanyInfo.Name,
		TSPConnHandleId: appObj.TSPConnHandleId,
		InsuredEmail:    insuredEmail,
		InsuredName:     insuredName,
	}

	createdByUUID, err := uuid.Parse(appObj.CreatedBy)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse createdBy %s", appObj.CreatedBy)
	}
	appData.CreatedBy = createdByUUID

	if appObj.ProducerID != nil {
		producerUUID, err := uuid.Parse(*appObj.ProducerID)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to parse producer %s", *appObj.ProducerID)
		}
		appData.ProducerID = pointer_utils.ToPointer(producerUUID)
	}

	return appData, nil
}
