package impl

import (
	"context"
	"time"

	insurance_eng "nirvanatech.com/nirvana/insurance-core/monitoring"

	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	applicationutil "nirvanatech.com/nirvana/common-go/application-util"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/quoting/jobs"
)

const (
	ScheduleTelematicsConsentReminderEmailsTaskID = "ScheduleTelematicsConsentReminderEmailsTask"
	scheduleChangeThresholdDuration               = 30 * 24 * time.Hour
	sevenDays                                     = 7 * 24 * time.Hour
	threeDays                                     = 3 * 24 * time.Hour
	maxEmailsToBeSent                             = 2
)

type scheduleOptions struct {
	startTime time.Time
	interval  time.Duration
}

func NewScheduleTelematicsConsentReminderEmailsJob(deps *Deps) (*jtypes.Job[*jobs.ScheduleTelematicsConsentReminderEmailsArgs], error) {
	return jtypes.NewJob(
		jobs.ScheduleTelematicsReminderEmails,
		[]jtypes.TaskCreator[*jobs.ScheduleTelematicsConsentReminderEmailsArgs]{
			func() jtypes.Task[*jobs.ScheduleTelematicsConsentReminderEmailsArgs] {
				return &scheduleTelematicsConsentReminderEmailsTask{deps: deps}
			},
		},
		jobs.ScheduleTelematicsConsentReminderEmailsUnmarshalFn,
	)
}

type scheduleTelematicsConsentReminderEmailsTask struct {
	job_utils.NonRetryableTask[*jobs.ScheduleTelematicsConsentReminderEmailsArgs]
	job_utils.NoopUndoTask[*jobs.ScheduleTelematicsConsentReminderEmailsArgs]
	deps *Deps
}

func (t *scheduleTelematicsConsentReminderEmailsTask) ID() string {
	return ScheduleTelematicsConsentReminderEmailsTaskID
}

func (t *scheduleTelematicsConsentReminderEmailsTask) Run(
	jCtx jtypes.Context, msg *jobs.ScheduleTelematicsConsentReminderEmailsArgs,
) error {
	pdDetails := map[string]any{}
	var agencyID uuid.UUID
	if err := t.run(jCtx, pdDetails, msg, &agencyID); err != nil {
		pdErr := t.deps.InsuranceEngPDClient.TriggerAlertForJobFailure(
			jCtx, &insurance_eng.TriggerPagerDutyAlertForJobFailureRequest{
				PDDetails:   pdDetails,
				Err:         err,
				AgencyID:    agencyID,
				ProgramType: policyenums.ProgramTypeFleet,
			},
		)
		if pdErr != nil {
			return pdErr
		}
		return err
	}
	return nil
}

func (t *scheduleTelematicsConsentReminderEmailsTask) run(
	jCtx jtypes.Context, pdDetails map[string]any, msg *jobs.ScheduleTelematicsConsentReminderEmailsArgs,
	agencyID *uuid.UUID,
) error {
	pdDetails["applicationID"] = msg.ApplicationID

	err := validateScheduleTelematicsConsentReminderEmailsArgs(msg)
	if err != nil {
		log.Error(jCtx, "failed to validateScheduleTelematicsConsentReminderEmailsArgs", log.Err(err))
		return errors.Wrap(err, "failed to validateScheduleTelematicsConsentReminderEmailsArgs")
	}
	appID := msg.ApplicationID
	jCtx = jCtx.WithUpdatedBaseCtx(func(ctx context.Context) context.Context {
		return log.ContextWithFields(jCtx, log.String("applicationID", msg.ApplicationID))
	})

	app, err := t.deps.AppWrapper.GetAppById(jCtx, appID)
	if err != nil {
		log.Error(jCtx, "failed to GetAppById", log.Err(err))
		return errors.Wrap(err, "failed to GetAppById")
	}
	*agencyID = app.AgencyID

	err = validateApplicationForScheduleTelematicsConsentReminderEmailsJob(app)
	if err != nil {
		log.Error(jCtx, "failed to validate application", log.Err(err))
		return errors.Wrap(err, "failed to validate application")
	}

	creator, err := GetUser(jCtx, app.MarketerID.String(), t.deps.AuthWrapper)
	if err != nil {
		log.Error(jCtx, "failed to get creator", log.Err(err))
		return errors.Wrap(err, "failed to get creator")
	}

	shouldScheduleEmails, err := applicationutil.ShouldProcessEmail(
		jCtx, t.deps.FeatureFlagClient, app, creator, feature_flag_lib.FeatureTelematicsConsentReminderEmail,
	)
	if err != nil {
		log.Error(jCtx, "failed to determine if emails should be scheduled", log.Err(err))
		return errors.Wrap(err, "failed to determine if emails should be scheduled")
	}
	if !shouldScheduleEmails {
		log.Warn(jCtx, "skipping scheduling TelematicsConsentReminder emails")
		return nil
	}

	if err = t.scheduleTelematicsReminderEmails(jCtx, app, creator); err != nil {
		log.Error(jCtx, "failed to scheduleTelematicsReminderEmails", log.Err(err))
		return errors.Wrap(err, "failed to scheduleTelematicsReminderEmails")
	}

	return nil
}

func validateScheduleTelematicsConsentReminderEmailsArgs(msg *jobs.ScheduleTelematicsConsentReminderEmailsArgs) error {
	if msg == nil {
		return errors.New("nil message")
	}
	if msg.ApplicationID == "" {
		return errors.New("empty ApplicationID")
	}
	return nil
}

func validateApplicationForScheduleTelematicsConsentReminderEmailsJob(app *application.Application) error {
	if app == nil {
		return errors.New("nil application")
	}
	if app.CreatedBy == "" {
		return errors.New("invalid CreatedBy")
	}
	return nil
}

func (t *scheduleTelematicsConsentReminderEmailsTask) scheduleTelematicsReminderEmails(
	jCtx jtypes.Context, app *application.Application, creator *authz.User,
) error {
	jobRunParams, err := t.createJobRunParams(jCtx, app, creator)
	if err != nil {
		return errors.Wrap(err, "failed to create JobRunParams")
	}
	jCtx = jCtx.WithUpdatedBaseCtx(func(ctx context.Context) context.Context {
		return log.ContextWithFields(jCtx, log.Any("jobRunParams", jobRunParams))
	})

	jobRunID, err := jCtx.GetJobber().AddJobRun(jCtx, jobRunParams)
	alreadyExistsErr := jtypes.ErrJobRunAlreadyExists
	if err != nil {
		if errors.Is(err, alreadyExistsErr) {
			log.Warn(jCtx, "job already exists", log.Err(err), log.Stringer("existingJobRunID", jobRunID))
			return nil
		} else {
			log.Error(jCtx, "failed to AddJobRun", log.Err(err))
			return errors.Wrap(err, "failed to AddJobRun")
		}
	}

	err = t.writeTelematicsReminderEmailPreferenceToApp(jCtx, app)
	if err != nil {
		log.Error(jCtx, "failed to write TelematicsReminderEmailPreference to the Application table", log.Err(err))
		return errors.Wrap(err, "failed to write TelematicsReminderEmailPreference to the Application table")
	}

	log.Info(
		jCtx, "TelematicsConsentReminder emails scheduled successfully", log.Stringer("jobRunID", jobRunID),
	)
	return nil
}

func (t *scheduleTelematicsConsentReminderEmailsTask) createJobRunParams(
	jCtx jtypes.Context, app *application.Application, creator *authz.User,
) (jtypes.AddJobRunParams, error) {
	emailScheduleOptions, err := t.getEmailScheduleOptions(jCtx, app, creator)
	if err != nil {
		return jtypes.AddJobRunParams{}, errors.Wrap(err, "failed to getEmailScheduleOptions")
	}

	metadata := jtypes.NewMetadata(jtypes.Scheduled).WithRequestedStartTime(emailScheduleOptions.startTime)
	appUUID, err := uuid.Parse(app.ID)
	if err != nil {
		return jtypes.AddJobRunParams{}, errors.Wrap(err, "failed to parse appID")
	}
	creatorUUID := uuid.NewSHA1(appUUID, []byte(jobs.SendTelematicsConsentReminderEmail))
	schedule := jtypes.NewConstantDelaySchedule(emailScheduleOptions.interval, null.IntFrom(maxEmailsToBeSent-1))

	return jtypes.NewAddJobRunParams(
		jobs.SendTelematicsConsentReminderEmail,
		&jobs.SendTelematicsConsentReminderEmailArgs{
			ApplicationID: app.ID,
		},
		metadata,
	).WithCreatorUUID(creatorUUID).WithSchedule(schedule), nil
}

func (t *scheduleTelematicsConsentReminderEmailsTask) getEmailScheduleOptions(
	jCtx jtypes.Context, app *application.Application, creator *authz.User,
) (*scheduleOptions, error) {
	timeNow := time.Now()
	effectiveDate := app.ModelInput.CoverageInfo.EffectiveDate
	fallbackInterval := threeDays
	featureFlag := feature_flag_lib.FeatureTelematicsConsentReminderEmailsPreThresholdDateInterval
	if effectiveDate.Sub(timeNow) > scheduleChangeThresholdDuration {
		featureFlag = feature_flag_lib.FeatureTelematicsConsentReminderEmailsPostThresholdDateInterval
		fallbackInterval = sevenDays
	}

	var interval time.Duration
	intervalValInSeconds, err := t.deps.FeatureFlagClient.IntVariation(
		feature_flag_lib.BuildLookupAttributes(*creator),
		featureFlag,
		int(fallbackInterval.Seconds()))
	if err != nil {
		log.Error(
			jCtx, "failed to determine feature flag value, falling back to fallbackInterval", log.Err(err),
			log.Stringer("featureFlag", featureFlag), log.Duration("fallbackInterval", fallbackInterval),
		)
		interval = fallbackInterval
	} else {
		interval = time.Duration(intervalValInSeconds) * time.Second
	}

	return &scheduleOptions{
		startTime: timeNow.Add(interval),
		interval:  interval,
	}, nil
}

func (t *scheduleTelematicsConsentReminderEmailsTask) writeTelematicsReminderEmailPreferenceToApp(
	jCtx jtypes.Context, app *application.Application,
) error {
	err := t.deps.AppWrapper.UpdateApp(jCtx, app.ID,
		func(a application.Application) (application.Application, error) {
			if a.AdditionalEmailInfo == nil {
				a.AdditionalEmailInfo = &application.AdditionalEmailInfo{}
			}
			a.AdditionalEmailInfo.TelematicsConsentReminder.Preference = application.EmailPreference{
				Preference: app_enums.EmailPreferenceActive,
				UpdatedAt:  time.Now(),
			}
			return a, nil
		})
	if err != nil {
		log.Error(jCtx, "failed to update application", log.Err(err))
		return errors.Wrap(err, "failed to update application")
	}
	return nil
}
