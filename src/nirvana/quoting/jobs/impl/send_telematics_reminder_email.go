package impl

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	insurance_eng "nirvanatech.com/nirvana/insurance-core/monitoring"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	application_util "nirvanatech.com/nirvana/common-go/application-util"
	auth_util "nirvanatech.com/nirvana/common-go/auth-util"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	application_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	application_email "nirvanatech.com/nirvana/db-api/db_wrappers/emails/application"
	application_email_enums "nirvanatech.com/nirvana/db-api/db_wrappers/emails/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/emailer/models"
	"nirvanatech.com/nirvana/infra/constants"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/oauth"
	"nirvanatech.com/nirvana/quoting/jobs"
	"nirvanatech.com/nirvana/quoting/utils"
	"nirvanatech.com/nirvana/telematics"
)

const (
	SendTelematicsConsentReminderEmailTaskID = "SendTelematicsConsentReminderEmailTask"
	telematicsConnectionLinkKey              = "telematics_connection_link"
	uwFirstName                              = "underwriter_first_name"
	uwName                                   = "underwriter_name"
	uwEmail                                  = "underwriter_email"
	companyName                              = "company_name"
	dotNumber                                = "dot_number"
	uwIcon                                   = "underwriter_icon"
	uwDesignation                            = "underwriter_designation"
	uwPhoneNumber                            = "underwriter_phone_no"
	applicationLink                          = "application_link"
	reminderEmailSubject                     = "Action Required: Connect Telematics for %s - %s"
	isFollowUp1                              = "is_follow_up_1"
	isFollowUp2                              = "is_follow_up_2"
	isFollowUp3                              = "is_follow_up_3"
	tspLink                                  = "tsp_link"
	agentName                                = "agent_name"
	// count metric for notifying downstream services about the success of telematics reminder email
	consentReminderEmailSuccessNotifCountMetric = "consent_reminder_email_success_notification.count"
)

var followupNumberToVarMapping = map[int32]string{
	1: isFollowUp1,
	2: isFollowUp2,
	3: isFollowUp3,
}

func NewSendTelematicsConsentReminderEmailJob(deps *Deps) (*jtypes.Job[*jobs.SendTelematicsConsentReminderEmailArgs], error) {
	return jtypes.NewJob(
		jobs.SendTelematicsConsentReminderEmail,
		[]jtypes.TaskCreator[*jobs.SendTelematicsConsentReminderEmailArgs]{
			func() jtypes.Task[*jobs.SendTelematicsConsentReminderEmailArgs] {
				return &sendTelematicsConsentReminderEmailTask{deps: deps}
			},
		},
		jobs.SendTelematicsConsentReminderEmailUnmarshalFn,
	)
}

type sendTelematicsConsentReminderEmailTask struct {
	job_utils.NonRetryableTask[*jobs.SendTelematicsConsentReminderEmailArgs]
	job_utils.NoopUndoTask[*jobs.SendTelematicsConsentReminderEmailArgs]
	deps *Deps
}

var _ jtypes.Task[*jobs.SendTelematicsConsentReminderEmailArgs] = (*sendTelematicsConsentReminderEmailTask)(nil)

func (t *sendTelematicsConsentReminderEmailTask) ID() string {
	return SendTelematicsConsentReminderEmailTaskID
}

func (t *sendTelematicsConsentReminderEmailTask) Run(
	jCtx jtypes.Context, msg *jobs.SendTelematicsConsentReminderEmailArgs,
) error {
	pdDetails := map[string]any{}
	var agencyID uuid.UUID
	if err := t.run(jCtx, pdDetails, msg, &agencyID); err != nil {
		pdErr := t.deps.InsuranceEngPDClient.TriggerAlertForJobFailure(
			jCtx, &insurance_eng.TriggerPagerDutyAlertForJobFailureRequest{
				PDDetails:   pdDetails,
				Err:         err,
				AgencyID:    agencyID,
				ProgramType: policy_enums.ProgramTypeFleet,
			},
		)
		if pdErr != nil {
			return pdErr
		}
		return err
	}
	return nil
}

func (t *sendTelematicsConsentReminderEmailTask) run(
	jCtx jtypes.Context, pdDetails map[string]any, msg *jobs.SendTelematicsConsentReminderEmailArgs,
	agencyID *uuid.UUID,
) error {
	pdDetails["applicationID"] = msg.ApplicationID

	if err := validateSendTelematicsConsentReminderEmailArgs(msg); err != nil {
		return errors.Wrap(err, "failed to validate SendTelematicsConsentReminderEmailArgs")
	}
	jCtx = jCtx.WithUpdatedBaseCtx(func(ctx context.Context) context.Context {
		return log.ContextWithFields(jCtx, log.String("applicationID", msg.ApplicationID))
	})

	app, err := t.deps.AppWrapper.GetAppById(jCtx, msg.ApplicationID)
	if err != nil {
		if errors.Is(err, application.ErrApplicationArchived) {
			log.Info(jCtx, "application is archived, returning early")
			return nil
		}
		log.Error(jCtx, "failed to GetAppById", log.Err(err))
		return errors.Wrap(err, "failed to GetAppById")
	}
	*agencyID = app.AgencyID
	if err = validateApplicationForSendTelematicsReminderEmailJob(app); err != nil {
		log.Error(jCtx, "failed to validate application", log.Err(err))
		return errors.Wrap(err, "failed to validate application")
	}

	jobRun, err := t.getJobRun(jCtx)
	if err != nil {
		return err
	}
	currRunNumber, err := t.getJobRunNumber(jCtx, jobRun)
	if err != nil {
		log.Error(jCtx, "failed to getJobRunNumber", log.Err(err))
		return errors.Wrap(err, "failed to getJobRunNumber")
	}

	shouldSendEmail, err := t.shouldSendEmail(jCtx, app)
	if err != nil {
		log.Error(jCtx, "failed to determine if email should be sent", log.Err(err))
		return errors.Wrap(err, "failed to determine if email should be sent")
	}
	if !shouldSendEmail {
		err = t.cancelSchedule(jCtx)
		if err != nil {
			log.Error(jCtx, "failed to cancelSchedule", log.Err(err))
			return errors.Wrap(err, "failed to cancelSchedule")
		}

		err = t.updateApplicationWithCancelledJobs(jCtx, app.ID, jobRun, currRunNumber)
		if err != nil {
			log.Error(
				jCtx, "failed to updateApplicationWithCancelledJobs", log.Err(err),
				log.Int32("currRunNumber", currRunNumber),
			)
			return errors.Wrap(err, "failed to updateApplicationWithCancelledJobs")
		}
		log.Info(jCtx, "scheduled cancelled")
		return nil
	}

	sendRequest, err := t.createSendRequest(jCtx, app, currRunNumber)
	if err != nil {
		log.Error(jCtx, "failed to createSendRequest", log.Err(err))
		return errors.Wrap(err, "failed to createSendRequest")
	}

	_, err = t.deps.Emailer.Send(jCtx, sendRequest)
	if err != nil {
		log.Error(jCtx, "failed to send TelematicsConsentReminder email", log.Err(err),
			log.Any("sendRequest", sendRequest), log.Int32("emailNumber", currRunNumber),
		)
		return errors.Wrap(err, "failed to send TelematicsConsentReminder email")
	}
	log.Info(
		jCtx, "TelematicsConsentReminder email sent successfully", log.Any("sendRequest", sendRequest),
		log.Int32("emailNumber", currRunNumber),
	)
	emailLog := application_email.NewEmailLog(
		uuid.MustParse(msg.ApplicationID),
		policy_enums.ProgramTypeFleet,
		application_email_enums.EmailTypeConsentReminder,
	)
	if err := t.deps.AppEmailLogWrapper.InsertEmailLog(jCtx, emailLog); err != nil {
		log.Error(
			jCtx,
			"failed to insert application email log",
			log.AppID(msg.ApplicationID),
			log.Any("EmailType", emailLog.EmailType.String()),
			log.Err(err),
		)
		return errors.Wrapf(
			err,
			"failed to insert application %s log for email %s",
			msg.ApplicationID,
			emailLog.EmailType.String(),
		)
	}
	err = t.updateApplicationWithCompletedJob(jCtx, app.ID, jobRun.JobRunId.String(), currRunNumber)
	if err != nil {
		log.Error(jCtx, "failed to updateApplicationWithCompletedJob", log.Err(err))
		return errors.Wrap(err, "failed to updateApplicationWithCompletedJob")
	}

	err = t.notifyEmailSent(jCtx, app.ID)
	if err != nil {
		log.Error(jCtx, fmt.Sprintf("error while notifying consent reminder email sent successfully for app ID %s", app.ID), log.Err(err))

		// emit failure metric
		if metricError := t.deps.MetricsClient.Inc(consentReminderEmailSuccessNotifCountMetric, 1, 1, statsd.Tag{metricTagKeyStatus, metricTagValueFailed}); metricError != nil {
			log.Error(jCtx, fmt.Sprintf("error emitting metric %s", consentReminderEmailSuccessNotifCountMetric), log.Err(metricError))
		}
	}

	// emit success metric
	if metricError := t.deps.MetricsClient.Inc(consentReminderEmailSuccessNotifCountMetric, 1, 1, statsd.Tag{metricTagKeyStatus, metricTagValueSucceeded}); metricError != nil {
		log.Error(jCtx, fmt.Sprintf("error emitting metric %s", consentReminderEmailSuccessNotifCountMetric), log.Err(metricError))
	}

	return nil
}

func (t *sendTelematicsConsentReminderEmailTask) notifyEmailSent(ctx context.Context, appId string) error {
	app, err := t.deps.AppWrapper.GetAppById(ctx, appId)
	if err != nil {
		return errors.Wrapf(err, "failed to get app with ID %s", appId)
	}

	emailInfo, err := utils.GenerateTelematicsReminderEmailInfo(
		ctx, t.deps.Jobber, t.deps.AppReviewWrapper, app,
	)
	if err != nil {
		return errors.Wrapf(err, "failed to generate telematics reminder email info for app %s", app.ID)
	}

	// currently there is direct coupling with downstream consumers
	err = t.deps.AppReviewStateMachine.TelematicsReminderEmailSent(ctx, app.ID, *emailInfo)
	if err != nil {
		return errors.Wrapf(err, "failed to perform TelematicsReminderEmailSent action on app review state machine for app ID %s", app.ID)
	}
	return nil
}

func validateSendTelematicsConsentReminderEmailArgs(msg *jobs.SendTelematicsConsentReminderEmailArgs) error {
	if msg == nil {
		return errors.New("nil message")
	}
	if msg.ApplicationID == "" {
		return errors.New("empty ApplicationID")
	}
	return nil
}

func validateApplicationForSendTelematicsReminderEmailJob(app *application.Application) error {
	if err := validateCommonApplicationFields(app); err != nil {
		return err
	}

	if app.AdditionalEmailInfo == nil ||
		app.AdditionalEmailInfo.TelematicsConsentReminder.Preference.Preference == application_enums.EmailPreferenceInvalid ||
		!app.AdditionalEmailInfo.TelematicsConsentReminder.Preference.Preference.IsAEmailPreference() {
		return errors.New("invalid AdditionalEmailInfo")
	}

	return nil
}

func (t *sendTelematicsConsentReminderEmailTask) getJobRun(jCtx jtypes.Context) (*jtypes.JobRun, error) {
	jr, err := jCtx.GetJobber().GetJobRun(jCtx, jCtx.GetJobRunId())
	if err != nil {
		log.Error(jCtx, "failed to GetJobRun", log.Err(err))
		return nil, errors.Wrap(err, "failed to GetJobRun")
	}
	return jr, nil
}

func (t *sendTelematicsConsentReminderEmailTask) getJobRunNumber(
	jCtx jtypes.Context, jr *jtypes.JobRun,
) (count int32, err error) {
	for ; err == nil; jr, err = jCtx.GetJobber().GetJobRun(jCtx, jtypes.NewJobRunId(jr.JobId, jr.Schedule.PrevRunId)) {
		if jr.Schedule == nil {
			return 0, errors.Newf("%s is not a type of scheduled job", jr)
		}
		count++
		if jr.Schedule.PrevRunId == jtypes.InvalidRunId {
			break
		}
	}
	return
}

func (t *sendTelematicsConsentReminderEmailTask) shouldSendEmail(
	jCtx jtypes.Context, app *application.Application,
) (bool, error) {
	isEmailPreferenceActive := app.AdditionalEmailInfo.TelematicsConsentReminder.Preference.Preference == application_enums.EmailPreferenceActive
	if !isEmailPreferenceActive {
		log.Info(jCtx, "Not sending email as email preference is inactive")
		return false, nil
	}

	consentForDotExists, err := t.consentForDotExists(jCtx, strconv.FormatInt(app.CompanyInfo.DOTNumber, 10), app.CreatedAt)
	if err != nil {
		return false, errors.Wrap(err, "failed to determine if TelematicsConsent has been provided")
	}
	if consentForDotExists {
		log.Info(
			jCtx,
			fmt.Sprintf("Not sending email as active consent was found for DOT %d", app.CompanyInfo.DOTNumber),
			log.Int64("dotNumber", app.CompanyInfo.DOTNumber),
		)
		return false, nil
	}

	latestPendingReview, err := t.deps.AppReviewWrapper.GetLatestPendingReview(jCtx, app.ID)
	if err != nil && !errors.Is(err, uw.ErrAppReviewNotFound) {
		log.Error(jCtx, "failed to GetLatestPendingReview", log.Err(err))
		return false, errors.Wrap(err, "failed to GetLatestPendingReview")
	}
	isEffectiveDateInPast := false
	if latestPendingReview != nil {
		isEffectiveDateInPast = time.Now().After(latestPendingReview.EffectiveDate)
	}
	if isEffectiveDateInPast {
		log.Info(jCtx, "Not sending email as effective date has passed")
		return false, nil
	}

	isUnderUWReview := application_util.IsAppUnderUWReview(latestPendingReview, app.State)
	if !isUnderUWReview {
		log.Info(jCtx, "Not sending email as application is not under UW review")
		return false, nil
	}
	creator, err := GetUser(jCtx, app.MarketerID.String(), t.deps.AuthWrapper)
	if err != nil {
		log.Error(jCtx, "failed to get creator", log.Err(err))
		return false, errors.Wrap(err, "failed to get creator")
	}

	shouldProcessEmail, err := application_util.ShouldProcessEmail(
		jCtx, t.deps.FeatureFlagClient, app, creator, feature_flag_lib.FeatureTelematicsConsentReminderEmail,
	)
	if err != nil {
		log.Error(jCtx, "failed to determine if the email should be processed further", log.Err(err))
		return false, errors.Wrap(err, "failed to determine if the email should be processed further")
	}

	return shouldProcessEmail, nil
}

// consentForDotExists checks if an active consent is present for a DOT or if there was any consent activated at after
// the given appCreationDate even if it's not active anymore. This is to prevent sending reminder emails for users that have
// already connected, but we encountered an issue with the connection later.
func (t *sendTelematicsConsentReminderEmailTask) consentForDotExists(jCtx jtypes.Context, dotNumber string, appCreationDate time.Time) (bool, error) {
	fleet, err := t.deps.FleetWrapper.FetchFleetByDOT(jCtx, dotNumber)
	if err != nil && errors.Is(err, sql.ErrNoRows) {
		log.Info(jCtx, fmt.Sprintf("No db record found for fleet with DOT %s", dotNumber))
		return false, nil
	}
	if err != nil {
		return false, errors.Wrap(err, fmt.Sprintf("failed to get fleet for DOT %s", dotNumber))
	}

	consents, err := t.deps.FleetTelematicsWrapper.GetAllFleetTelematicsConsent(jCtx, fleet.ID)
	if err != nil {
		return false, errors.Wrap(err, fmt.Sprintf("failed to get consents for fleet %s", fleet.ID.String()))
	}

	var retErr error
	for _, consent := range consents {
		connInfo, err := t.deps.TSPConnManager.GetConnectionInfo(jCtx, consent.HandleID)
		if err != nil {
			if errors.Is(err, oauth.ErrUnexpectedProvider) {
				log.Info(jCtx, "Skipping error due to invalid provider for OAuth handle", log.String("handleID", consent.HandleID.String()))
				continue
			}
			retErr = errors.CombineErrors(retErr, err)
			continue
		}
		if connInfo.Status.IsOneOf(telematics.ConnectionStatusAuthorized, telematics.ConnectionStatusConnected) {
			return true, nil
		}

		if connInfo.ActivatedAt.Valid && connInfo.ActivatedAt.Time.After(appCreationDate) {
			return true, nil
		}
	}
	if retErr != nil {
		return false, errors.Wrap(retErr, "failed getting connection info for consents")
	}
	return false, nil
}

func (t *sendTelematicsConsentReminderEmailTask) cancelSchedule(jCtx jtypes.Context) error {
	cancelScheduleReq := &job_utils.CancelScheduleRequest{
		JobRunID: jCtx.GetJobRunId(),
		// Since we must not amend a job from within the job, we should not cancel it here and let it get completed.
		JobsToSkip: []jtypes.JobRunId{jCtx.GetJobRunId()},
	}
	if err := job_utils.CancelSchedule(jCtx, jCtx.GetJobber(), cancelScheduleReq); err != nil {
		return errors.Wrap(err, "failed to CancelSchedule")
	}
	return nil
}

func (t *sendTelematicsConsentReminderEmailTask) updateApplicationWithCancelledJobs(
	jCtx jtypes.Context, appID string, jr *jtypes.JobRun, jobRunNumber int32,
) error {
	err := t.deps.AppWrapper.UpdateApp(jCtx, appID,
		func(a application.Application) (application.Application, error) {
			var err error
			jobStatus := a.AdditionalEmailInfo.TelematicsConsentReminder.JobStatus
			for ; err == nil; jr, err = jCtx.GetJobber().GetJobRun(jCtx, jtypes.NewJobRunId(jr.JobId, jr.Schedule.NextRunId)) {
				if jr.Schedule == nil {
					return a, errors.Newf("%s is not a type of scheduled job", jr)
				}
				status := application_enums.EmailJobStatusCancelled
				if jr.JobRunId == jCtx.GetJobRunId() {
					status = application_enums.EmailJobStatusSkipped
				}
				jobStatus = append(jobStatus, application.EmailJobStatus{
					JobRunID:     jr.JobRunId.String(),
					JobRunNumber: jobRunNumber,
					Status:       status,
					UpdatedAt:    time.Now(),
				})
				if jr.Schedule.NextRunId == jtypes.InvalidRunId {
					break
				}
				jobRunNumber++
			}
			a.AdditionalEmailInfo.TelematicsConsentReminder.JobStatus = jobStatus
			return a, nil
		})
	if err != nil {
		log.Error(jCtx, "failed to update app", log.Err(err))
		return errors.Wrap(err, "failed to update app")
	}
	return nil
}

func (t *sendTelematicsConsentReminderEmailTask) createSendRequest(
	jCtx jtypes.Context, app *application.Application, currRunNumber int32,
) (*models.SendRequest, error) {
	appId, err := uuid.Parse(app.ID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse appID :%s", app.ID)
	}

	emailArgs, err := getCommonEmailArgs(jCtx, t.deps, app)
	if err != nil {
		log.Error(jCtx, "failed to getCommonEmailArgs", log.Err(err))
		return nil, errors.Wrap(err, "failed to getCommonEmailArgs")
	}

	ccs, err := getCCs(jCtx, emailArgs, app, false)
	if err != nil {
		log.Error(jCtx, "failed to getCCs", log.String("creatorID", app.CreatedBy), log.Err(err))
		return nil, errors.Wrap(err, "failed to getCCs")
	}

	consentLink, err := FetchOrCreateNonExpiredConsentLink(
		jCtx,
		t.deps.SharingWrapper,
		appId,
		emailArgs.creator.ID,
		enums.ProgramTypeFleet,
	)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to fetch or create non-expired consent link for ApplicationId: %s",
			appId,
		)
	}

	templateData := getTemplateDataForReminderEmail(app, emailArgs, currRunNumber)
	templateData[telematicsConnectionLinkKey] = consentLink

	return &models.SendRequest{
		EmailType:    models.TelematicsConsentReminderEmail,
		Sender:       auth_util.GenerateContactFromUserInfo(emailArgs.assignedBD.UserInfo),
		Recipients:   []models.Contact{auth_util.GenerateContactFromUserInfo(emailArgs.producer.UserInfo)},
		BCC:          []models.Contact{models.NirvanaAutomatedEmailContact},
		CC:           ccs,
		Subject:      fmt.Sprintf(reminderEmailSubject, templateData[companyName], templateData[dotNumber]),
		TemplateData: templateData,
		SubscriptionOptions: &models.SubscriptionOptions{
			IncludeUnsubscribeLink: true,
		},
		EventOptions: &models.EventOptions{
			EmitEvent: true,
			ExtraProperties: map[string]any{
				"ApplicationID":  app.ID,
				"IsTestApp":      constants.TestAgencies()[app.AgencyID],
				"ReminderNumber": currRunNumber,
			},
		},
		ReplyTo: &models.NirvanaSupportContact,
	}, nil
}

func getTemplateDataForReminderEmail(app *application.Application, args *commonEmailArgs, runNo int32) map[string]any {
	dotNo := strconv.FormatInt(app.CompanyInfo.DOTNumber, 10)
	data := map[string]any{
		uwFirstName:     args.underwriter.FirstName,
		uwName:          args.underwriter.FullName(),
		uwEmail:         args.underwriter.Email,
		companyName:     app.CompanyInfo.Name,
		dotNumber:       dotNo,
		uwIcon:          *application_util.GetHighResolutionUnderwriterIconURL(args.underwriter.Email),
		uwDesignation:   args.underwriterDetails.Designation,
		uwPhoneNumber:   args.underwriterDetails.PhoneNumber,
		applicationLink: args.applicationLink,
	}

	for i := int32(1); i <= 3; i++ {
		data[followupNumberToVarMapping[i]] = false
		if i == runNo {
			data[followupNumberToVarMapping[i]] = true
		}
	}

	return data
}

func (t *sendTelematicsConsentReminderEmailTask) updateApplicationWithCompletedJob(
	jCtx jtypes.Context, appID, jobRunID string, jobRunNumber int32,
) error {
	err := t.deps.AppWrapper.UpdateApp(jCtx, appID,
		func(a application.Application) (application.Application, error) {
			a.AdditionalEmailInfo.TelematicsConsentReminder.JobStatus = append(
				a.AdditionalEmailInfo.TelematicsConsentReminder.JobStatus,
				application.EmailJobStatus{
					JobRunID:     jobRunID,
					JobRunNumber: jobRunNumber,
					Status:       application_enums.EmailJobStatusCompleted,
					UpdatedAt:    time.Now(),
				})
			return a, nil
		})
	if err != nil {
		log.Error(jCtx, "failed to update app", log.Err(err))
		return errors.Wrap(err, "failed to update app")
	}
	return nil
}
