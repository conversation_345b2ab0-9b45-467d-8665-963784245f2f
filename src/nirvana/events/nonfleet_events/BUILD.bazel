load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "nonfleet_events",
    srcs = [
        "base_event.go",
        "nf_application_created.go",
        "nf_application_declined.go",
        "nf_application_producer_selected.go",
        "nf_application_submitted_for_review.go",
        "nf_forms_sig_packet_bound.go",
        "nf_forms_sig_packet_released.go",
        "nf_indication_generation_completed.go",
        "nf_indication_selected.go",
        "nf_uw_application_review_approved.go",
        "nf_uw_application_review_closed.go",
        "nf_uw_application_review_declined.go",
        "nf_uw_application_review_rollback.go",
        "nf_uw_effective_date_update.go",
        "nf_uw_reassigned_event.go",
    ],
    importpath = "nirvanatech.com/nirvana/events/nonfleet_events",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/str_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/nonfleet/rule_runs",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/events",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/infra/authz",
        "//nirvana/infra/constants",
        "//nirvana/nonfleet/state-machine/enums",
        "//nirvana/nonfleet/underwriting_panels/operations",
        "//nirvana/underwriting/app_review/actions/reasons",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_friendsofgo_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@in_gopkg_segmentio_analytics_go_v3//:analytics-go_v3",
    ],
)
