package nonfleet_events

import (
	"context"
	"strings"

	"github.com/cockroachdb/errors"
	"gopkg.in/segmentio/analytics-go.v3"

	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/rule_runs"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/nonfleet/state-machine/enums"
)

type NFApplicationDeclined struct {
	baseEvent
	Reason *rule_runs.Reason
}

func (N NFApplicationDeclined) Upload(
	ctx context.Context,
	deps events.EventDeps,
) error {
	return N.baseEvent.Upload(ctx, deps, N.Name(), N.GetProperties())
}

func (N NFApplicationDeclined) Name() string {
	return enums.NFApplicationDeclined.String()
}

func (N NFApplicationDeclined) UserVisibleName() string {
	return N.Name()
}

func (N NFApplicationDeclined) GetProperties() analytics.Properties {
	baseProperties := N.baseEvent.Properties()
	primaryReason, secondaryReason := "", ""
	if N.Reason.PrimaryReason != nil {
		primaryReason = *N.Reason.PrimaryReason
	}
	if N.Reason.SecondaryReason != nil {
		secondaryReason = *N.Reason.SecondaryReason
	}
	guidelineBased := strings.Join(N.Reason.GuidelineBasedReasons, ", ")
	nonGuidelineBased := strings.Join(N.Reason.NonGuidelineBasedReasons, ", ")
	return baseProperties.
		Set("Comments", N.Reason.Text).
		Set("PrimaryReason", primaryReason).
		Set("SecondaryReason", secondaryReason).
		Set("GuidelineBasedReasons", guidelineBased).
		Set("NonGuidelineBasedReasons", nonGuidelineBased)
}

func NewNFApplicationDeclined[T application.AppInfo](
	ctx context.Context,
	authWrapper auth.DataWrapper,
	app *application.Application[T],
) (events.Event, error) {
	baseEvent, err := createBaseEventFromApplication[T](ctx, app, authWrapper)
	if err != nil {
		return nil, errors.Wrapf(err, "Failed to create the baseEvent")
	}

	return &NFApplicationDeclined{
		baseEvent: *baseEvent,
		Reason: &rule_runs.Reason{
			Text:                     app.Info.GetClosureInfo().Comments,
			PrimaryReason:            app.Info.GetClosureInfo().PrimaryReason,
			SecondaryReason:          app.Info.GetClosureInfo().SecondaryReason,
			GuidelineBasedReasons:    app.Info.GetClosureInfo().GuidelineBasedReasons,
			NonGuidelineBasedReasons: app.Info.GetClosureInfo().NonGuidelineBasedReasons,
		},
	}, nil
}
