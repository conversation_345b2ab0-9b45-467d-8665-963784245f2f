// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: fleet/model/exposure.proto

package model

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Exposure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectedMileage int32 `protobuf:"varint,1,opt,name=projectedMileage,proto3" json:"projectedMileage,omitempty"`
	QuotedMileage    int32 `protobuf:"varint,2,opt,name=quotedMileage,proto3" json:"quotedMileage,omitempty"`
}

func (x *Exposure) Reset() {
	*x = Exposure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_exposure_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Exposure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Exposure) ProtoMessage() {}

func (x *Exposure) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_exposure_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Exposure.ProtoReflect.Descriptor instead.
func (*Exposure) Descriptor() ([]byte, []int) {
	return file_fleet_model_exposure_proto_rawDescGZIP(), []int{0}
}

func (x *Exposure) GetProjectedMileage() int32 {
	if x != nil {
		return x.ProjectedMileage
	}
	return 0
}

func (x *Exposure) GetQuotedMileage() int32 {
	if x != nil {
		return x.QuotedMileage
	}
	return 0
}

var File_fleet_model_exposure_proto protoreflect.FileDescriptor

var file_fleet_model_exposure_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x78,
	0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x5c, 0x0a, 0x08, 0x45, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x10, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x64, 0x4d, 0x69, 0x6c, 0x65, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x64,
	0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x42, 0x25, 0x5a, 0x23, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_fleet_model_exposure_proto_rawDescOnce sync.Once
	file_fleet_model_exposure_proto_rawDescData = file_fleet_model_exposure_proto_rawDesc
)

func file_fleet_model_exposure_proto_rawDescGZIP() []byte {
	file_fleet_model_exposure_proto_rawDescOnce.Do(func() {
		file_fleet_model_exposure_proto_rawDescData = protoimpl.X.CompressGZIP(file_fleet_model_exposure_proto_rawDescData)
	})
	return file_fleet_model_exposure_proto_rawDescData
}

var file_fleet_model_exposure_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_fleet_model_exposure_proto_goTypes = []interface{}{
	(*Exposure)(nil), // 0: fleet_model.Exposure
}
var file_fleet_model_exposure_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_fleet_model_exposure_proto_init() }
func file_fleet_model_exposure_proto_init() {
	if File_fleet_model_exposure_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_fleet_model_exposure_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Exposure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_fleet_model_exposure_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_fleet_model_exposure_proto_goTypes,
		DependencyIndexes: file_fleet_model_exposure_proto_depIdxs,
		MessageInfos:      file_fleet_model_exposure_proto_msgTypes,
	}.Build()
	File_fleet_model_exposure_proto = out.File
	file_fleet_model_exposure_proto_rawDesc = nil
	file_fleet_model_exposure_proto_goTypes = nil
	file_fleet_model_exposure_proto_depIdxs = nil
}
