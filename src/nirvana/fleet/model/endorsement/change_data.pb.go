// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: fleet/model/endorsement/change_data.proto

package endorsement

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	proto "nirvanatech.com/nirvana/common-go/proto"
	model "nirvanatech.com/nirvana/fleet/model"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FleetChangeType int32

const (
	FleetChangeType_FleetChangeType_Invalid         FleetChangeType = 0
	FleetChangeType_FleetChangeType_MailingAddress  FleetChangeType = 1
	FleetChangeType_FleetChangeType_TerminalAddress FleetChangeType = 2
)

// Enum value maps for FleetChangeType.
var (
	FleetChangeType_name = map[int32]string{
		0: "FleetChangeType_Invalid",
		1: "FleetChangeType_MailingAddress",
		2: "FleetChangeType_TerminalAddress",
	}
	FleetChangeType_value = map[string]int32{
		"FleetChangeType_Invalid":         0,
		"FleetChangeType_MailingAddress":  1,
		"FleetChangeType_TerminalAddress": 2,
	}
)

func (x FleetChangeType) Enum() *FleetChangeType {
	p := new(FleetChangeType)
	*p = x
	return p
}

func (x FleetChangeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FleetChangeType) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_endorsement_change_data_proto_enumTypes[0].Descriptor()
}

func (FleetChangeType) Type() protoreflect.EnumType {
	return &file_fleet_model_endorsement_change_data_proto_enumTypes[0]
}

func (x FleetChangeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FleetChangeType.Descriptor instead.
func (FleetChangeType) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{0}
}

type FleetChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChangeType FleetChangeType `protobuf:"varint,1,opt,name=changeType,proto3,enum=fleet_endorsement.FleetChangeType" json:"changeType,omitempty"`
	// Types that are assignable to Data:
	//
	//	*FleetChange_MailingAddressChange
	//	*FleetChange_TerminalAddressChange
	Data isFleetChange_Data `protobuf_oneof:"data"`
}

func (x *FleetChange) Reset() {
	*x = FleetChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_endorsement_change_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetChange) ProtoMessage() {}

func (x *FleetChange) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_endorsement_change_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetChange.ProtoReflect.Descriptor instead.
func (*FleetChange) Descriptor() ([]byte, []int) {
	return file_fleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{0}
}

func (x *FleetChange) GetChangeType() FleetChangeType {
	if x != nil {
		return x.ChangeType
	}
	return FleetChangeType_FleetChangeType_Invalid
}

func (m *FleetChange) GetData() isFleetChange_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *FleetChange) GetMailingAddressChange() *MailingAddressChange {
	if x, ok := x.GetData().(*FleetChange_MailingAddressChange); ok {
		return x.MailingAddressChange
	}
	return nil
}

func (x *FleetChange) GetTerminalAddressChange() *TerminalAddressChange {
	if x, ok := x.GetData().(*FleetChange_TerminalAddressChange); ok {
		return x.TerminalAddressChange
	}
	return nil
}

type isFleetChange_Data interface {
	isFleetChange_Data()
}

type FleetChange_MailingAddressChange struct {
	MailingAddressChange *MailingAddressChange `protobuf:"bytes,2,opt,name=mailingAddressChange,proto3,oneof"`
}

type FleetChange_TerminalAddressChange struct {
	TerminalAddressChange *TerminalAddressChange `protobuf:"bytes,3,opt,name=terminalAddressChange,proto3,oneof"`
}

func (*FleetChange_MailingAddressChange) isFleetChange_Data() {}

func (*FleetChange_TerminalAddressChange) isFleetChange_Data() {}

type MailingAddressChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewAddress *proto.Address `protobuf:"bytes,1,opt,name=newAddress,proto3" json:"newAddress,omitempty"`
}

func (x *MailingAddressChange) Reset() {
	*x = MailingAddressChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_endorsement_change_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MailingAddressChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MailingAddressChange) ProtoMessage() {}

func (x *MailingAddressChange) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_endorsement_change_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MailingAddressChange.ProtoReflect.Descriptor instead.
func (*MailingAddressChange) Descriptor() ([]byte, []int) {
	return file_fleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{1}
}

func (x *MailingAddressChange) GetNewAddress() *proto.Address {
	if x != nil {
		return x.NewAddress
	}
	return nil
}

type TerminalAddressChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Add    []*model.TerminalLocation `protobuf:"bytes,1,rep,name=add,proto3" json:"add,omitempty"`
	Remove []string                  `protobuf:"bytes,2,rep,name=remove,proto3" json:"remove,omitempty"`
	Update []*model.TerminalLocation `protobuf:"bytes,3,rep,name=update,proto3" json:"update,omitempty"`
}

func (x *TerminalAddressChange) Reset() {
	*x = TerminalAddressChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_endorsement_change_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TerminalAddressChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalAddressChange) ProtoMessage() {}

func (x *TerminalAddressChange) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_endorsement_change_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalAddressChange.ProtoReflect.Descriptor instead.
func (*TerminalAddressChange) Descriptor() ([]byte, []int) {
	return file_fleet_model_endorsement_change_data_proto_rawDescGZIP(), []int{2}
}

func (x *TerminalAddressChange) GetAdd() []*model.TerminalLocation {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *TerminalAddressChange) GetRemove() []string {
	if x != nil {
		return x.Remove
	}
	return nil
}

func (x *TerminalAddressChange) GetUpdate() []*model.TerminalLocation {
	if x != nil {
		return x.Update
	}
	return nil
}

var File_fleet_model_endorsement_change_data_proto protoreflect.FileDescriptor

var file_fleet_model_endorsement_change_data_proto_rawDesc = []byte{
	0x0a, 0x29, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x15,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9a, 0x02, 0x0a, 0x0b, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x5f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x6c,
	0x65, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5d, 0x0a, 0x14, 0x6d, 0x61, 0x69,
	0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x61, 0x69, 0x6c,
	0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x48, 0x00, 0x52, 0x14, 0x6d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x60, 0x0a, 0x15, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x48, 0x00, 0x52, 0x15, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x47, 0x0a, 0x14, 0x4d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x0a, 0x6e, 0x65,
	0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x0a, 0x6e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x15,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x03, 0x61, 0x64, 0x64, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x03, 0x61, 0x64, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x35,
	0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x2a, 0x77, 0x0a, 0x0f, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x6c, 0x65, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x6c, 0x65,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x10, 0x02, 0x42, 0x31,
	0x5a, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_fleet_model_endorsement_change_data_proto_rawDescOnce sync.Once
	file_fleet_model_endorsement_change_data_proto_rawDescData = file_fleet_model_endorsement_change_data_proto_rawDesc
)

func file_fleet_model_endorsement_change_data_proto_rawDescGZIP() []byte {
	file_fleet_model_endorsement_change_data_proto_rawDescOnce.Do(func() {
		file_fleet_model_endorsement_change_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_fleet_model_endorsement_change_data_proto_rawDescData)
	})
	return file_fleet_model_endorsement_change_data_proto_rawDescData
}

var file_fleet_model_endorsement_change_data_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_fleet_model_endorsement_change_data_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_fleet_model_endorsement_change_data_proto_goTypes = []interface{}{
	(FleetChangeType)(0),           // 0: fleet_endorsement.FleetChangeType
	(*FleetChange)(nil),            // 1: fleet_endorsement.FleetChange
	(*MailingAddressChange)(nil),   // 2: fleet_endorsement.MailingAddressChange
	(*TerminalAddressChange)(nil),  // 3: fleet_endorsement.TerminalAddressChange
	(*proto.Address)(nil),          // 4: common.Address
	(*model.TerminalLocation)(nil), // 5: fleet_model.TerminalLocation
}
var file_fleet_model_endorsement_change_data_proto_depIdxs = []int32{
	0, // 0: fleet_endorsement.FleetChange.changeType:type_name -> fleet_endorsement.FleetChangeType
	2, // 1: fleet_endorsement.FleetChange.mailingAddressChange:type_name -> fleet_endorsement.MailingAddressChange
	3, // 2: fleet_endorsement.FleetChange.terminalAddressChange:type_name -> fleet_endorsement.TerminalAddressChange
	4, // 3: fleet_endorsement.MailingAddressChange.newAddress:type_name -> common.Address
	5, // 4: fleet_endorsement.TerminalAddressChange.add:type_name -> fleet_model.TerminalLocation
	5, // 5: fleet_endorsement.TerminalAddressChange.update:type_name -> fleet_model.TerminalLocation
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_fleet_model_endorsement_change_data_proto_init() }
func file_fleet_model_endorsement_change_data_proto_init() {
	if File_fleet_model_endorsement_change_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_fleet_model_endorsement_change_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_endorsement_change_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MailingAddressChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_endorsement_change_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TerminalAddressChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_fleet_model_endorsement_change_data_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*FleetChange_MailingAddressChange)(nil),
		(*FleetChange_TerminalAddressChange)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_fleet_model_endorsement_change_data_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_fleet_model_endorsement_change_data_proto_goTypes,
		DependencyIndexes: file_fleet_model_endorsement_change_data_proto_depIdxs,
		EnumInfos:         file_fleet_model_endorsement_change_data_proto_enumTypes,
		MessageInfos:      file_fleet_model_endorsement_change_data_proto_msgTypes,
	}.Build()
	File_fleet_model_endorsement_change_data_proto = out.File
	file_fleet_model_endorsement_change_data_proto_rawDesc = nil
	file_fleet_model_endorsement_change_data_proto_goTypes = nil
	file_fleet_model_endorsement_change_data_proto_depIdxs = nil
}
