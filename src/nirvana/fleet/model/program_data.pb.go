// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: fleet/model/program_data.proto

package model

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	proto "nirvanatech.com/nirvana/common-go/proto"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TypeOfTerminal int32

const (
	TypeOfTerminal_TYPE_OF_TERMINAL_UNSPECIFIED TypeOfTerminal = 0
	TypeOfTerminal_TYPE_OF_TERMINAL_DOCK        TypeOfTerminal = 1
	TypeOfTerminal_TYPE_OF_TERMINAL_DROP_LOT    TypeOfTerminal = 2
	TypeOfTerminal_TYPE_OF_TERMINAL_OFFICE      TypeOfTerminal = 3
	TypeOfTerminal_TYPE_OF_TERMINAL_TERMINAL    TypeOfTerminal = 4
)

// Enum value maps for TypeOfTerminal.
var (
	TypeOfTerminal_name = map[int32]string{
		0: "TYPE_OF_TERMINAL_UNSPECIFIED",
		1: "TYPE_OF_TERMINAL_DOCK",
		2: "TYPE_OF_TERMINAL_DROP_LOT",
		3: "TYPE_OF_TERMINAL_OFFICE",
		4: "TYPE_OF_TERMINAL_TERMINAL",
	}
	TypeOfTerminal_value = map[string]int32{
		"TYPE_OF_TERMINAL_UNSPECIFIED": 0,
		"TYPE_OF_TERMINAL_DOCK":        1,
		"TYPE_OF_TERMINAL_DROP_LOT":    2,
		"TYPE_OF_TERMINAL_OFFICE":      3,
		"TYPE_OF_TERMINAL_TERMINAL":    4,
	}
)

func (x TypeOfTerminal) Enum() *TypeOfTerminal {
	p := new(TypeOfTerminal)
	*p = x
	return p
}

func (x TypeOfTerminal) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TypeOfTerminal) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[0].Descriptor()
}

func (TypeOfTerminal) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[0]
}

func (x TypeOfTerminal) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TypeOfTerminal.Descriptor instead.
func (TypeOfTerminal) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{0}
}

type CommodityCategory int32

const (
	CommodityCategory_COMMODITY_CATEGORY_UNSPECIFIED                      CommodityCategory = 0
	CommodityCategory_COMMODITY_CATEGORY_AUTO_PARTS_AND_ACCESSORIES       CommodityCategory = 1
	CommodityCategory_COMMODITY_CATEGORY_BEVERAGES_ALCOHOLIC              CommodityCategory = 2
	CommodityCategory_COMMODITY_CATEGORY_BEVERAGE_NONALCOHOLIC            CommodityCategory = 3
	CommodityCategory_COMMODITY_CATEGORY_BUILDING_MATERIALS_AND_LUMBER    CommodityCategory = 4
	CommodityCategory_COMMODITY_CATEGORY_ELECTRONICS_FINISHED             CommodityCategory = 5
	CommodityCategory_COMMODITY_CATEGORY_ELECTRONIC_PARTS                 CommodityCategory = 6
	CommodityCategory_COMMODITY_CATEGORY_FOODS_BAKED_DRY_PACKAGED_CANNED  CommodityCategory = 7
	CommodityCategory_COMMODITY_CATEGORY_FOODS_FRESH_PRODUCE_NON_DRY      CommodityCategory = 8
	CommodityCategory_COMMODITY_CATEGORY_REEFER_MEAT_AND_SEAFOOD          CommodityCategory = 9
	CommodityCategory_COMMODITY_CATEGORY_REEFER_NON_MEAT                  CommodityCategory = 10
	CommodityCategory_COMMODITY_CATEGORY_GENERAL_FREIGHT                  CommodityCategory = 11
	CommodityCategory_COMMODITY_CATEGORY_HOUSEHOLD_GOODS                  CommodityCategory = 12
	CommodityCategory_COMMODITY_CATEGORY_HOUSEHOLD_HARDWARE               CommodityCategory = 13
	CommodityCategory_COMMODITY_CATEGORY_INTERMODAL_CONTAINERIZED_FREIGHT CommodityCategory = 14
	CommodityCategory_COMMODITY_CATEGORY_MAIL_AND_PARCEL                  CommodityCategory = 15
	CommodityCategory_COMMODITY_CATEGORY_METALS_NON_PRECIOUS              CommodityCategory = 16
	CommodityCategory_COMMODITY_CATEGORY_METALS_PRECIOUS                  CommodityCategory = 17
	CommodityCategory_COMMODITY_CATEGORY_PAPER_AND_PLASTIC_PRODUCTS       CommodityCategory = 18
	CommodityCategory_COMMODITY_CATEGORY_TEXTILES                         CommodityCategory = 19
	CommodityCategory_COMMODITY_CATEGORY_OTHER                            CommodityCategory = 20
	CommodityCategory_COMMODITY_CATEGORY_AGRICULTURAL_PRODUCTS            CommodityCategory = 21
	CommodityCategory_COMMODITY_CATEGORY_CHEMICAL_PETROLEUM               CommodityCategory = 22
	CommodityCategory_COMMODITY_CATEGORY_CONSTRUCTION_AGGREGATE           CommodityCategory = 23
	CommodityCategory_COMMODITY_CATEGORY_GARBAGE                          CommodityCategory = 24
	CommodityCategory_COMMODITY_CATEGORY_GENERATORS_AC_UNITS              CommodityCategory = 25
	CommodityCategory_COMMODITY_CATEGORY_GRAIN_FEED_HAY_COTTON            CommodityCategory = 26
	CommodityCategory_COMMODITY_CATEGORY_HAZARDOUS_MATERIALS              CommodityCategory = 27
	CommodityCategory_COMMODITY_CATEGORY_LIVESTOCK                        CommodityCategory = 28
	CommodityCategory_COMMODITY_CATEGORY_MOBILE_EQUIPMENT                 CommodityCategory = 29
	CommodityCategory_COMMODITY_CATEGORY_MOBILE_HOMES                     CommodityCategory = 30
	CommodityCategory_COMMODITY_CATEGORY_MOTORIZED_VEHICLES               CommodityCategory = 31
	CommodityCategory_COMMODITY_CATEGORY_NURSERY_STOCK                    CommodityCategory = 32
	CommodityCategory_COMMODITY_CATEGORY_PHARMACEUTICALS                  CommodityCategory = 33
	CommodityCategory_COMMODITY_CATEGORY_SCRAP_METAL                      CommodityCategory = 34
	CommodityCategory_COMMODITY_CATEGORY_WOOD                             CommodityCategory = 35
)

// Enum value maps for CommodityCategory.
var (
	CommodityCategory_name = map[int32]string{
		0:  "COMMODITY_CATEGORY_UNSPECIFIED",
		1:  "COMMODITY_CATEGORY_AUTO_PARTS_AND_ACCESSORIES",
		2:  "COMMODITY_CATEGORY_BEVERAGES_ALCOHOLIC",
		3:  "COMMODITY_CATEGORY_BEVERAGE_NONALCOHOLIC",
		4:  "COMMODITY_CATEGORY_BUILDING_MATERIALS_AND_LUMBER",
		5:  "COMMODITY_CATEGORY_ELECTRONICS_FINISHED",
		6:  "COMMODITY_CATEGORY_ELECTRONIC_PARTS",
		7:  "COMMODITY_CATEGORY_FOODS_BAKED_DRY_PACKAGED_CANNED",
		8:  "COMMODITY_CATEGORY_FOODS_FRESH_PRODUCE_NON_DRY",
		9:  "COMMODITY_CATEGORY_REEFER_MEAT_AND_SEAFOOD",
		10: "COMMODITY_CATEGORY_REEFER_NON_MEAT",
		11: "COMMODITY_CATEGORY_GENERAL_FREIGHT",
		12: "COMMODITY_CATEGORY_HOUSEHOLD_GOODS",
		13: "COMMODITY_CATEGORY_HOUSEHOLD_HARDWARE",
		14: "COMMODITY_CATEGORY_INTERMODAL_CONTAINERIZED_FREIGHT",
		15: "COMMODITY_CATEGORY_MAIL_AND_PARCEL",
		16: "COMMODITY_CATEGORY_METALS_NON_PRECIOUS",
		17: "COMMODITY_CATEGORY_METALS_PRECIOUS",
		18: "COMMODITY_CATEGORY_PAPER_AND_PLASTIC_PRODUCTS",
		19: "COMMODITY_CATEGORY_TEXTILES",
		20: "COMMODITY_CATEGORY_OTHER",
		21: "COMMODITY_CATEGORY_AGRICULTURAL_PRODUCTS",
		22: "COMMODITY_CATEGORY_CHEMICAL_PETROLEUM",
		23: "COMMODITY_CATEGORY_CONSTRUCTION_AGGREGATE",
		24: "COMMODITY_CATEGORY_GARBAGE",
		25: "COMMODITY_CATEGORY_GENERATORS_AC_UNITS",
		26: "COMMODITY_CATEGORY_GRAIN_FEED_HAY_COTTON",
		27: "COMMODITY_CATEGORY_HAZARDOUS_MATERIALS",
		28: "COMMODITY_CATEGORY_LIVESTOCK",
		29: "COMMODITY_CATEGORY_MOBILE_EQUIPMENT",
		30: "COMMODITY_CATEGORY_MOBILE_HOMES",
		31: "COMMODITY_CATEGORY_MOTORIZED_VEHICLES",
		32: "COMMODITY_CATEGORY_NURSERY_STOCK",
		33: "COMMODITY_CATEGORY_PHARMACEUTICALS",
		34: "COMMODITY_CATEGORY_SCRAP_METAL",
		35: "COMMODITY_CATEGORY_WOOD",
	}
	CommodityCategory_value = map[string]int32{
		"COMMODITY_CATEGORY_UNSPECIFIED":                      0,
		"COMMODITY_CATEGORY_AUTO_PARTS_AND_ACCESSORIES":       1,
		"COMMODITY_CATEGORY_BEVERAGES_ALCOHOLIC":              2,
		"COMMODITY_CATEGORY_BEVERAGE_NONALCOHOLIC":            3,
		"COMMODITY_CATEGORY_BUILDING_MATERIALS_AND_LUMBER":    4,
		"COMMODITY_CATEGORY_ELECTRONICS_FINISHED":             5,
		"COMMODITY_CATEGORY_ELECTRONIC_PARTS":                 6,
		"COMMODITY_CATEGORY_FOODS_BAKED_DRY_PACKAGED_CANNED":  7,
		"COMMODITY_CATEGORY_FOODS_FRESH_PRODUCE_NON_DRY":      8,
		"COMMODITY_CATEGORY_REEFER_MEAT_AND_SEAFOOD":          9,
		"COMMODITY_CATEGORY_REEFER_NON_MEAT":                  10,
		"COMMODITY_CATEGORY_GENERAL_FREIGHT":                  11,
		"COMMODITY_CATEGORY_HOUSEHOLD_GOODS":                  12,
		"COMMODITY_CATEGORY_HOUSEHOLD_HARDWARE":               13,
		"COMMODITY_CATEGORY_INTERMODAL_CONTAINERIZED_FREIGHT": 14,
		"COMMODITY_CATEGORY_MAIL_AND_PARCEL":                  15,
		"COMMODITY_CATEGORY_METALS_NON_PRECIOUS":              16,
		"COMMODITY_CATEGORY_METALS_PRECIOUS":                  17,
		"COMMODITY_CATEGORY_PAPER_AND_PLASTIC_PRODUCTS":       18,
		"COMMODITY_CATEGORY_TEXTILES":                         19,
		"COMMODITY_CATEGORY_OTHER":                            20,
		"COMMODITY_CATEGORY_AGRICULTURAL_PRODUCTS":            21,
		"COMMODITY_CATEGORY_CHEMICAL_PETROLEUM":               22,
		"COMMODITY_CATEGORY_CONSTRUCTION_AGGREGATE":           23,
		"COMMODITY_CATEGORY_GARBAGE":                          24,
		"COMMODITY_CATEGORY_GENERATORS_AC_UNITS":              25,
		"COMMODITY_CATEGORY_GRAIN_FEED_HAY_COTTON":            26,
		"COMMODITY_CATEGORY_HAZARDOUS_MATERIALS":              27,
		"COMMODITY_CATEGORY_LIVESTOCK":                        28,
		"COMMODITY_CATEGORY_MOBILE_EQUIPMENT":                 29,
		"COMMODITY_CATEGORY_MOBILE_HOMES":                     30,
		"COMMODITY_CATEGORY_MOTORIZED_VEHICLES":               31,
		"COMMODITY_CATEGORY_NURSERY_STOCK":                    32,
		"COMMODITY_CATEGORY_PHARMACEUTICALS":                  33,
		"COMMODITY_CATEGORY_SCRAP_METAL":                      34,
		"COMMODITY_CATEGORY_WOOD":                             35,
	}
)

func (x CommodityCategory) Enum() *CommodityCategory {
	p := new(CommodityCategory)
	*p = x
	return p
}

func (x CommodityCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommodityCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[1].Descriptor()
}

func (CommodityCategory) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[1]
}

func (x CommodityCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommodityCategory.Descriptor instead.
func (CommodityCategory) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{1}
}

type CommodityName int32

const (
	CommodityName_COMMODITY_NAME_UNSPECIFIED                                                                      CommodityName = 0
	CommodityName_COMMODITY_HAULED_AGGREGATES_ROCK_OR_SAND_OR_GRAVEL_OR_STONE_OR_DIRT                             CommodityName = 1
	CommodityName_COMMODITY_HAULED_APPLIANCES_OR_HARDWARE                                                         CommodityName = 2
	CommodityName_COMMODITY_HAULED_ASH                                                                            CommodityName = 3
	CommodityName_COMMODITY_HAULED_ASPHALT_LIQUID                                                                 CommodityName = 4
	CommodityName_COMMODITY_HAULED_AUTO_PARTS_ACCESSORIES                                                         CommodityName = 5
	CommodityName_COMMODITY_HAULED_BEVERAGES_ALCOHOLIC                                                            CommodityName = 6
	CommodityName_COMMODITY_HAULED_BEVERAGES_NON_ALCOHOLIC                                                        CommodityName = 7
	CommodityName_COMMODITY_HAULED_BOATS                                                                          CommodityName = 8
	CommodityName_COMMODITY_HAULED_BUILDING_MATERIALS_FINISHED                                                    CommodityName = 9
	CommodityName_COMMODITY_HAULED_BUILDING_MATERIALS_RAW                                                         CommodityName = 10
	CommodityName_COMMODITY_HAULED_CHEMICALS_BULK_LIQUID                                                          CommodityName = 11
	CommodityName_COMMODITY_HAULED_CLASS1_EXPLOSIVES                                                              CommodityName = 12
	CommodityName_COMMODITY_HAULED_CLASS2_GASES                                                                   CommodityName = 13
	CommodityName_COMMODITY_HAULED_CLASS3_FLAMMABLE_OR_COMBUSTIBLE_LIQUIDS                                        CommodityName = 14
	CommodityName_COMMODITY_HAULED_CLASS4_FLAMMABLE_SOLIDS                                                        CommodityName = 15
	CommodityName_COMMODITY_HAULED_CLASS5_OXIDIZING_SUBSTANCES_OR_ORGANIC_PEROXIDES                               CommodityName = 16
	CommodityName_COMMODITY_HAULED_CLASS6_TOXINS_OR_INFECTIOUS_SUBSTANCES                                         CommodityName = 17
	CommodityName_COMMODITY_HAULED_CLASS7_RADIOACTIVE_MATERIALS                                                   CommodityName = 18
	CommodityName_COMMODITY_HAULED_CLASS8_CORROSIVES                                                              CommodityName = 19
	CommodityName_COMMODITY_HAULED_CLASS9_MISCELLANEOUS_OR_NOT_OTHERWISE_SPECIFIED                                CommodityName = 20
	CommodityName_COMMODITY_HAULED_CONSTRUCTION_EQUIPMENT                                                         CommodityName = 21
	CommodityName_COMMODITY_HAULED_COSMETICS                                                                      CommodityName = 22
	CommodityName_COMMODITY_HAULED_DRY_FOODS                                                                      CommodityName = 23
	CommodityName_COMMODITY_HAULED_ELECTRONICS                                                                    CommodityName = 24
	CommodityName_COMMODITY_HAULED_EMPTY_CONTAINERS                                                               CommodityName = 25
	CommodityName_COMMODITY_HAULED_ENGINES_OR_MACHINERY                                                           CommodityName = 26
	CommodityName_COMMODITY_HAULED_FERTILIZERS                                                                    CommodityName = 27
	CommodityName_COMMODITY_HAULED_FLOWERS_OR_PLANTS                                                              CommodityName = 28
	CommodityName_COMMODITY_HAULED_GENERAL_DRY_FREIGHT                                                            CommodityName = 29
	CommodityName_COMMODITY_HAULED_GRAIN_OR_SEED_OR_FEED                                                          CommodityName = 30
	CommodityName_COMMODITY_HAULED_HAZARDOUS_MATERIALS_OR_BATTERIES                                               CommodityName = 31
	CommodityName_COMMODITY_HAULED_LARGE_MACHINERY                                                                CommodityName = 32
	CommodityName_COMMODITY_HAULED_LIQUIDS_FUEL                                                                   CommodityName = 33
	CommodityName_COMMODITY_HAULED_LIQUIDS_MILK                                                                   CommodityName = 34
	CommodityName_COMMODITY_HAULED_LIQUIDS_OTHER_NON_FLAMMABLE                                                    CommodityName = 35
	CommodityName_COMMODITY_HAULED_LUMBER_OR_LOGS                                                                 CommodityName = 36
	CommodityName_COMMODITY_HAULED_MAIL_PARCELS_OR_AMAZON                                                         CommodityName = 37
	CommodityName_COMMODITY_HAULED_MAIL_USPS                                                                      CommodityName = 38
	CommodityName_COMMODITY_HAULED_METALS_COPPER                                                                  CommodityName = 39
	CommodityName_COMMODITY_HAULED_METALS_ROLLED_OR_COILED_STEELS                                                 CommodityName = 40
	CommodityName_COMMODITY_HAULED_METALS_BARS_OR_BEAMS                                                           CommodityName = 41
	CommodityName_COMMODITY_HAULED_OVER_SIZED_OR_OVERWEIGHT                                                       CommodityName = 42
	CommodityName_COMMODITY_HAULED_PAPER_OR_PLASTIC_PRODUCTS                                                      CommodityName = 43
	CommodityName_COMMODITY_HAULED_PHARMACEUTICALS                                                                CommodityName = 44
	CommodityName_COMMODITY_HAULED_REFRIGERATED_FOODS_MEAT                                                        CommodityName = 45
	CommodityName_COMMODITY_HAULED_REFRIGERATED_FOODS_NON_MEAT                                                    CommodityName = 46
	CommodityName_COMMODITY_HAULED_REFRIGERATED_GOODS                                                             CommodityName = 47
	CommodityName_COMMODITY_HAULED_RETAIL_PRODUCTS                                                                CommodityName = 48
	CommodityName_COMMODITY_HAULED_SAND_OR_SILICATE                                                               CommodityName = 49
	CommodityName_COMMODITY_HAULED_SCRAP_METAL                                                                    CommodityName = 50
	CommodityName_COMMODITY_HAULED_WOOD_CHIPS                                                                     CommodityName = 51
	CommodityName_COMMODITY_HAULED_ADIPIC_PELLETS                                                                 CommodityName = 52
	CommodityName_COMMODITY_HAULED_AGRICULTURAL_EQUIPMENT                                                         CommodityName = 53
	CommodityName_COMMODITY_HAULED_AIRCRAFT_OR_JET_ENGINES                                                        CommodityName = 54
	CommodityName_COMMODITY_HAULED_ALL_TERRAIN_VEHICLES                                                           CommodityName = 55
	CommodityName_COMMODITY_HAULED_ALUMINUM_CHLORIDE                                                              CommodityName = 56
	CommodityName_COMMODITY_HAULED_AMMONIUM_NITRATE_OR_FERTILIZER                                                 CommodityName = 57
	CommodityName_COMMODITY_HAULED_AMUSEMENT_DEVICES                                                              CommodityName = 58
	CommodityName_COMMODITY_HAULED_ANHYDROUS_AMMONIA_UN1005_CLASS203                                              CommodityName = 59
	CommodityName_COMMODITY_HAULED_APPLIANCES                                                                     CommodityName = 60
	CommodityName_COMMODITY_HAULED_ASBESTOS                                                                       CommodityName = 61
	CommodityName_COMMODITY_HAULED_ASPHALT_AND_BLACKTOP                                                           CommodityName = 62
	CommodityName_COMMODITY_HAULED_AUTOMOBILE_PARTS_AND_ACCESSORIES                                               CommodityName = 63
	CommodityName_COMMODITY_HAULED_AUTOMOBILES_USED_OR_CLASSIC_OR_ANTIQUE_OR_AUCTION_AND_SNOWBIRD_TYPE_OPERATIONS CommodityName = 64
	CommodityName_COMMODITY_HAULED_AUTOMOBILES_CRUSHED_OR_JUNK                                                    CommodityName = 65
	CommodityName_COMMODITY_HAULED_AUTOMOBILES_NEW                                                                CommodityName = 66
	CommodityName_COMMODITY_HAULED_AUTOS_PERSONAL_OR_PRIVATE_PASSENGER_OR_MOTOR_HOMES                             CommodityName = 67
	CommodityName_COMMODITY_HAULED_AUTOS_RACE_CARS                                                                CommodityName = 68
	CommodityName_COMMODITY_HAULED_BAKERY_GOODS_BREAD_OR_PIE_OR_PASTRIES_OR_COOKIE_OR_CAKES                       CommodityName = 69
	CommodityName_COMMODITY_HAULED_BANANAS                                                                        CommodityName = 70
	CommodityName_COMMODITY_HAULED_BATTERIES_DRY_CAR_OR_MARINE_OR_CAMPERS_OR_ETC                                  CommodityName = 71
	CommodityName_COMMODITY_HAULED_BATTERIES_WET_CAR_OR_MARINE_OR_CAMPER_OR_ETC                                   CommodityName = 72
	CommodityName_COMMODITY_HAULED_BATTERIES_HOUSEHOLD                                                            CommodityName = 73
	CommodityName_COMMODITY_HAULED_BEANS_SOYBEANS_OR_KIDNEY_OR_PEAS_OR_LENTILS_OR_CHICKPEAS                       CommodityName = 74
	CommodityName_COMMODITY_HAULED_BEER_OR_WINE_OR_BRANDY                                                         CommodityName = 75
	CommodityName_COMMODITY_HAULED_BEES                                                                           CommodityName = 76
	CommodityName_COMMODITY_HAULED_BOATS25FEET_AND_OVER_IN_LENGTH_NEW_OR_USED                                     CommodityName = 77
	CommodityName_COMMODITY_HAULED_BOATS_UNDER25FEET_LENGTH_NEW_OR_USED                                           CommodityName = 78
	CommodityName_COMMODITY_HAULED_BOTTLED_AND_CANNED_SOFT_DRINKS                                                 CommodityName = 79
	CommodityName_COMMODITY_HAULED_BOTTLED_WATER                                                                  CommodityName = 80
	CommodityName_COMMODITY_HAULED_BRICKS                                                                         CommodityName = 81
	CommodityName_COMMODITY_HAULED_BULLDOZER                                                                      CommodityName = 82
	CommodityName_COMMODITY_HAULED_CADMIUM                                                                        CommodityName = 83
	CommodityName_COMMODITY_HAULED_CALCIUM_CARBIDE                                                                CommodityName = 84
	CommodityName_COMMODITY_HAULED_CALCIUM_CHLORIDE_SOLUTION                                                      CommodityName = 85
	CommodityName_COMMODITY_HAULED_CAMERAS_AND_PHOTOGRAPHY                                                        CommodityName = 86
	CommodityName_COMMODITY_HAULED_CAMPERS_AND_RECREATIONAL_VEHICLES                                              CommodityName = 87
	CommodityName_COMMODITY_HAULED_CANNED_GOODS                                                                   CommodityName = 88
	CommodityName_COMMODITY_HAULED_CARBON_BLACK                                                                   CommodityName = 89
	CommodityName_COMMODITY_HAULED_CARNIVAL_OR_CIRCUS_EQUIPMENT_OR_RIDES                                          CommodityName = 90
	CommodityName_COMMODITY_HAULED_CARPETING                                                                      CommodityName = 91
	CommodityName_COMMODITY_HAULED_CEILING_OR_FLOORING_AND_WALL_COVERING                                          CommodityName = 92
	CommodityName_COMMODITY_HAULED_CEMENT_AND_CONCRETE_BULK                                                       CommodityName = 93
	CommodityName_COMMODITY_HAULED_CERAMIC_TILE_OR_QUARRY_TILE_OR_PAVERS_OR_PROSAIC                               CommodityName = 94
	CommodityName_COMMODITY_HAULED_CEREALS                                                                        CommodityName = 95
	CommodityName_COMMODITY_HAULED_CHARCOAL                                                                       CommodityName = 96
	CommodityName_COMMODITY_HAULED_CHICKEN_SLUDGE_OR_GUTS_HOT_OFFAL_OR_CHICKEN_FAT                                CommodityName = 97
	CommodityName_COMMODITY_HAULED_CHINA_AND_CERAMICS                                                             CommodityName = 98
	CommodityName_COMMODITY_HAULED_CHIPS_OR_CANDY_AND_OTHER_SNACK_FOODS                                           CommodityName = 99
	CommodityName_COMMODITY_HAULED_CHLORINE                                                                       CommodityName = 100
	CommodityName_COMMODITY_HAULED_CLASS2_1_FLAMMABLE_GAS                                                         CommodityName = 101
	CommodityName_COMMODITY_HAULED_CLASS2_2_NON_FLAMMABLE_COMPRESSED_GAS                                          CommodityName = 102
	CommodityName_COMMODITY_HAULED_CLASS2_3_POISONOUS_GAS                                                         CommodityName = 103
	CommodityName_COMMODITY_HAULED_CLASS3_FLAMMABLE_PACKAGING_GROUP_I                                             CommodityName = 104
	CommodityName_COMMODITY_HAULED_CLASS3_FLAMMABLE_PACKAGING_GROUP_II                                            CommodityName = 105
	CommodityName_COMMODITY_HAULED_CLASS3_FLAMMABLE_PACKAGING_GROUP_III                                           CommodityName = 106
	CommodityName_COMMODITY_HAULED_CLASS4_1_FLAMMABLE_SOLID_PACKAGING_GROUP_I                                     CommodityName = 107
	CommodityName_COMMODITY_HAULED_CLASS4_1_FLAMMABLE_SOLID_PACKAGING_GROUP_II                                    CommodityName = 108
	CommodityName_COMMODITY_HAULED_CLASS4_1_FLAMMABLE_SOLID_PACKAGING_GROUP_III                                   CommodityName = 109
	CommodityName_COMMODITY_HAULED_CLASS4_2_SPONTANEOUSLY_COMBUSTIBLE_MATERIAL                                    CommodityName = 110
	CommodityName_COMMODITY_HAULED_CLASS4_3_DANGEROUS_WHEN_WET_MATERIAL                                           CommodityName = 111
	CommodityName_COMMODITY_HAULED_CLASS5_1_OXIDIZER_PACKAGING_GROUP_I                                            CommodityName = 112
	CommodityName_COMMODITY_HAULED_CLASS5_1_OXIDIZER_PACKAGING_GROUP_II                                           CommodityName = 113
	CommodityName_COMMODITY_HAULED_CLASS5_1_OXIDIZER_PACKAGING_GROUP_III                                          CommodityName = 114
	CommodityName_COMMODITY_HAULED_CLASS5_2_ORGANIC_PEROXIDE                                                      CommodityName = 115
	CommodityName_COMMODITY_HAULED_CLASS6_1_POISONOUS_MATERIALS_INHALATION_HAZARD                                 CommodityName = 116
	CommodityName_COMMODITY_HAULED_CLASS6_1_POISONOUS_MATERIALS_OTHER_THAN_INHALATION_HAZARD                      CommodityName = 117
	CommodityName_COMMODITY_HAULED_CLASS6_2_INFECTIOUS_SUBSTANCES_PACKAGING_GROUP_I                               CommodityName = 118
	CommodityName_COMMODITY_HAULED_CLASS6_2_INFECTIOUS_SUBSTANCES_PACKAGING_GROUP_II                              CommodityName = 119
	CommodityName_COMMODITY_HAULED_CLASS6_2_INFECTIOUS_SUBSTANCES_PACKAGING_GROUP_III                             CommodityName = 120
	CommodityName_COMMODITY_HAULED_CLASS7_RADIOACTIVE                                                             CommodityName = 121
	CommodityName_COMMODITY_HAULED_CLASS8_CORROSIVES_PACKAGING_GROUP_I                                            CommodityName = 122
	CommodityName_COMMODITY_HAULED_CLASS8_CORROSIVES_PACKAGING_GROUP_II                                           CommodityName = 123
	CommodityName_COMMODITY_HAULED_CLASS8_CORROSIVES_PACKAGING_GROUP_III                                          CommodityName = 124
	CommodityName_COMMODITY_HAULED_CLASS9_MISCELLANEOUS_HAZARDOUS_MATERIALS                                       CommodityName = 125
	CommodityName_COMMODITY_HAULED_CLAY                                                                           CommodityName = 126
	CommodityName_COMMODITY_HAULED_CLOTHING                                                                       CommodityName = 127
	CommodityName_COMMODITY_HAULED_COAL                                                                           CommodityName = 128
	CommodityName_COMMODITY_HAULED_COAL_DUST_POWDER_TITANIUM_DIOXIDE                                              CommodityName = 129
	CommodityName_COMMODITY_HAULED_COMMUNICATION_EQUIPMENT                                                        CommodityName = 130
	CommodityName_COMMODITY_HAULED_COMMUNICATIONS_CELL_PHONES                                                     CommodityName = 131
	CommodityName_COMMODITY_HAULED_COMPRESSED_GASES_AND_HEATING_OIL                                               CommodityName = 132
	CommodityName_COMMODITY_HAULED_COMPUTER_ACCESSORIES                                                           CommodityName = 133
	CommodityName_COMMODITY_HAULED_COMPUTER_COMPONENTS                                                            CommodityName = 134
	CommodityName_COMMODITY_HAULED_COMPUTER_HARDWARE                                                              CommodityName = 135
	CommodityName_COMMODITY_HAULED_CONCRETE_PRODUCTS                                                              CommodityName = 136
	CommodityName_COMMODITY_HAULED_CONFECTIONARY_PRODUCTS                                                         CommodityName = 137
	CommodityName_COMMODITY_HAULED_CONSTRUCTION_DEBRIS_INCLUDES_DEMOLITION_DEBRIS                                 CommodityName = 138
	CommodityName_COMMODITY_HAULED_CONTAMINATED_DIRT_OR_SOIL                                                      CommodityName = 139
	CommodityName_COMMODITY_HAULED_COPPER_AND_COPPER_PRODUCTS                                                     CommodityName = 140
	CommodityName_COMMODITY_HAULED_CORROSIVE_SOLIDS_SALT                                                          CommodityName = 141
	CommodityName_COMMODITY_HAULED_COTTON_GINNED                                                                  CommodityName = 142
	CommodityName_COMMODITY_HAULED_COWHIDES_RAW_HIDES                                                             CommodityName = 143
	CommodityName_COMMODITY_HAULED_CRANES_BOOMS                                                                   CommodityName = 144
	CommodityName_COMMODITY_HAULED_DAIRY_PRODUCTS                                                                 CommodityName = 145
	CommodityName_COMMODITY_HAULED_DIET_FOODS                                                                     CommodityName = 146
	CommodityName_COMMODITY_HAULED_DIMENSION_STONE_STONE_SLABS_GRANITE_MARBLE_LIMESTONE_SLATE                     CommodityName = 147
	CommodityName_COMMODITY_HAULED_DRIVE_TOW_AWAY_TRAILERS_TRACTORS                                               CommodityName = 148
	CommodityName_COMMODITY_HAULED_DRUGS_OVER_THE_COUNTER_DRUGS_MEDICATIONS                                       CommodityName = 149
	CommodityName_COMMODITY_HAULED_DRUGS_PRESCRIPTIONS_AND_PHARMACEUTICALS                                        CommodityName = 150
	CommodityName_COMMODITY_HAULED_DRY_CHEMICALS                                                                  CommodityName = 151
	CommodityName_COMMODITY_HAULED_DRY_ICE                                                                        CommodityName = 152
	CommodityName_COMMODITY_HAULED_EDIBLE_OILS                                                                    CommodityName = 153
	CommodityName_COMMODITY_HAULED_EGGS                                                                           CommodityName = 154
	CommodityName_COMMODITY_HAULED_ELECTRICAL_EQUIPMENT                                                           CommodityName = 155
	CommodityName_COMMODITY_HAULED_ELECTRICAL_PARTS_SUPPLIES_OR_FIXTURES_SUPPLIES                                 CommodityName = 156
	CommodityName_COMMODITY_HAULED_ELECTRICAL_SYSTEMS_AND_EQUIPMENT                                               CommodityName = 157
	CommodityName_COMMODITY_HAULED_ELECTRONICS_AND_COMPUTERS_NOT_OTHERWISE_CLASSIFIED                             CommodityName = 158
	CommodityName_COMMODITY_HAULED_EXOTIC_CIRCUS_ZOO_OR_WILD_ANIMALS_LIVE                                         CommodityName = 159
	CommodityName_COMMODITY_HAULED_EXPLOSIVES_DETONATORS                                                          CommodityName = 160
	CommodityName_COMMODITY_HAULED_FEED_SEEDS                                                                     CommodityName = 161
	CommodityName_COMMODITY_HAULED_FERTILIZER_DRY_BAGGED                                                          CommodityName = 162
	CommodityName_COMMODITY_HAULED_FERTILIZER_LIQUID                                                              CommodityName = 163
	CommodityName_COMMODITY_HAULED_FILM_CELLULOID_SCRAP                                                           CommodityName = 164
	CommodityName_COMMODITY_HAULED_FIREARMS_AND_SUPPLIES_AMMUNITION_BLACK_POWDER_ETC                              CommodityName = 165
	CommodityName_COMMODITY_HAULED_FIREWOOD                                                                       CommodityName = 166
	CommodityName_COMMODITY_HAULED_FIREWORKS                                                                      CommodityName = 167
	CommodityName_COMMODITY_HAULED_FISH_LIVE                                                                      CommodityName = 168
	CommodityName_COMMODITY_HAULED_FISH_AND_SEAFOOD_FRESH_OR_FROZEN                                               CommodityName = 169
	CommodityName_COMMODITY_HAULED_FLAMMABLES                                                                     CommodityName = 170
	CommodityName_COMMODITY_HAULED_FLOUR                                                                          CommodityName = 171
	CommodityName_COMMODITY_HAULED_FLY_ASH                                                                        CommodityName = 172
	CommodityName_COMMODITY_HAULED_FRESH_FOODS_PRODUCE_VEGETABLES_FRUIT                                           CommodityName = 173
	CommodityName_COMMODITY_HAULED_FROZEN_FOODS_VEGETABLES_FRUIT_EXCLUDING_FISH_AND_SEAFOOD                       CommodityName = 174
	CommodityName_COMMODITY_HAULED_FROZEN_READY_TO_BAKE_COOK_FOODS                                                CommodityName = 175
	CommodityName_COMMODITY_HAULED_FUR_FUR_SKIN_PELTS                                                             CommodityName = 176
	CommodityName_COMMODITY_HAULED_FURNITURE                                                                      CommodityName = 177
	CommodityName_COMMODITY_HAULED_GAME_OR_WILD_BIRDS_LIVE_PHEASANTS_QUAIL_DUCK_GEESE                             CommodityName = 178
	CommodityName_COMMODITY_HAULED_GARBAGE_REFUSE_TRASH                                                           CommodityName = 179
	CommodityName_COMMODITY_HAULED_GASOLINE                                                                       CommodityName = 180
	CommodityName_COMMODITY_HAULED_GLASS_FLAT                                                                     CommodityName = 181
	CommodityName_COMMODITY_HAULED_GLASS_DUST_POWDER                                                              CommodityName = 182
	CommodityName_COMMODITY_HAULED_GLASS_PRODUCTS                                                                 CommodityName = 183
	CommodityName_COMMODITY_HAULED_GOLF_CARTS                                                                     CommodityName = 184
	CommodityName_COMMODITY_HAULED_GRAINS_CORN_WHEAT_BARLEY_RICE_OATS_RYE_PEANUTS_SORGHUM                         CommodityName = 185
	CommodityName_COMMODITY_HAULED_GYPSUM                                                                         CommodityName = 186
	CommodityName_COMMODITY_HAULED_HAND_TOOLS_POWER_TOOLS                                                         CommodityName = 187
	CommodityName_COMMODITY_HAULED_HANDHELD_DEVICES_GPS_PDAS_IPODS_HEADSETS_HEADPHONES_MP3                        CommodityName = 188
	CommodityName_COMMODITY_HAULED_HAY                                                                            CommodityName = 189
	CommodityName_COMMODITY_HAULED_HAZARDOUS_WASTE                                                                CommodityName = 190
	CommodityName_COMMODITY_HAULED_HEALTH_AIDS                                                                    CommodityName = 191
	CommodityName_COMMODITY_HAULED_HEATING_VENTILATION_AND_AIR_CONDITIONING                                       CommodityName = 192
	CommodityName_COMMODITY_HAULED_HOME_AUDIO                                                                     CommodityName = 193
	CommodityName_COMMODITY_HAULED_HOME_FURNISHINGS_AND_ACCESSORIES_DRAPES                                        CommodityName = 194
	CommodityName_COMMODITY_HAULED_HOUSE_AND_BUILDING_MOVERS                                                      CommodityName = 195
	CommodityName_COMMODITY_HAULED_HOUSEHOLD_CLEANING_PRODUCTS                                                    CommodityName = 196
	CommodityName_COMMODITY_HAULED_HOUSEHOLD_HARDWARE_FASTENERS_KEYS_LOCKS_HINGES_CHAINS                          CommodityName = 197
	CommodityName_COMMODITY_HAULED_HOUSEHOLD_HARDWARE_PLUMBING_SUPPLIES_PAINT_CABINET_BATH                        CommodityName = 198
	CommodityName_COMMODITY_HAULED_HOUSEWARES_COOKING_UTENSILS_DISHES_ETC                                         CommodityName = 199
	CommodityName_COMMODITY_HAULED_ICE_CREAM_ICE                                                                  CommodityName = 200
	CommodityName_COMMODITY_HAULED_INDUSTRIAL_EQUIPMENT                                                           CommodityName = 201
	CommodityName_COMMODITY_HAULED_INK_WATER_SOLUBLE                                                              CommodityName = 202
	CommodityName_COMMODITY_HAULED_INPUT_DEVICES                                                                  CommodityName = 203
	CommodityName_COMMODITY_HAULED_INTEGRATED_CIRCUIT_COMPUTER_CHIP_MICROPROCESSORS_SEMICONDUCTOR_HARD_DRIVE      CommodityName = 204
	CommodityName_COMMODITY_HAULED_INTERMODAL_CONTAINER_CONTAINERIZED_FREIGHT                                     CommodityName = 205
	CommodityName_COMMODITY_HAULED_JEWELRY                                                                        CommodityName = 206
	CommodityName_COMMODITY_HAULED_JUNK                                                                           CommodityName = 207
	CommodityName_COMMODITY_HAULED_LAWN_AND_GARDEN                                                                CommodityName = 208
	CommodityName_COMMODITY_HAULED_LEAD_POWDER_DUST                                                               CommodityName = 209
	CommodityName_COMMODITY_HAULED_LEATHER                                                                        CommodityName = 210
	CommodityName_COMMODITY_HAULED_LIME_SLACKED_AND_UNSLACKED                                                     CommodityName = 211
	CommodityName_COMMODITY_HAULED_LIMESTONE                                                                      CommodityName = 212
	CommodityName_COMMODITY_HAULED_LIQUID_LATEX                                                                   CommodityName = 213
	CommodityName_COMMODITY_HAULED_LIQUOR_EXCLUDING_BEER_AND_WINE                                                 CommodityName = 214
	CommodityName_COMMODITY_HAULED_LIVESTOCK_LIVE_PIGS_CATTLE_SHEEP_HORSES                                        CommodityName = 215
	CommodityName_COMMODITY_HAULED_LOGS_LOGGERS_WOOD_HARVESTING_PULP                                              CommodityName = 216
	CommodityName_COMMODITY_HAULED_LUMBER                                                                         CommodityName = 217
	CommodityName_COMMODITY_HAULED_MACHINERY_AND_HEAVY_EQUIPMENT                                                  CommodityName = 218
	CommodityName_COMMODITY_HAULED_MAIL                                                                           CommodityName = 219
	CommodityName_COMMODITY_HAULED_MANURE_AND_FERTILIZER_BULK                                                     CommodityName = 220
	CommodityName_COMMODITY_HAULED_MEAT_FROZEN_FRESH_PACKAGED_BOXED_EXCLUDING_SWINGING_MEAT                       CommodityName = 221
	CommodityName_COMMODITY_HAULED_MEDICAL_INSTRUMENTS                                                            CommodityName = 222
	CommodityName_COMMODITY_HAULED_METAL_PRODUCTS                                                                 CommodityName = 223
	CommodityName_COMMODITY_HAULED_METAL_SHEETS_COILS_ROLLS                                                       CommodityName = 224
	CommodityName_COMMODITY_HAULED_METHYL_BROMIDE                                                                 CommodityName = 225
	CommodityName_COMMODITY_HAULED_METHYL_METHANOL_ALCOHOL                                                        CommodityName = 226
	CommodityName_COMMODITY_HAULED_MILK                                                                           CommodityName = 227
	CommodityName_COMMODITY_HAULED_MOBILE_HOMES                                                                   CommodityName = 228
	CommodityName_COMMODITY_HAULED_MOBILE_MODULAR_HOMES                                                           CommodityName = 229
	CommodityName_COMMODITY_HAULED_MOLASSES                                                                       CommodityName = 230
	CommodityName_COMMODITY_HAULED_MOTORCYCLES                                                                    CommodityName = 231
	CommodityName_COMMODITY_HAULED_MULCH_TOP_SOIL_OR_FILL                                                         CommodityName = 232
	CommodityName_COMMODITY_HAULED_MULTIMEDIA_PROJECTORS                                                          CommodityName = 233
	CommodityName_COMMODITY_HAULED_MUSICAL_INSTRUMENTS                                                            CommodityName = 234
	CommodityName_COMMODITY_HAULED_MUSLIN                                                                         CommodityName = 235
	CommodityName_COMMODITY_HAULED_NETWORKING                                                                     CommodityName = 236
	CommodityName_COMMODITY_HAULED_NUTRITION                                                                      CommodityName = 237
	CommodityName_COMMODITY_HAULED_NUTS_AND_SEEDS                                                                 CommodityName = 238
	CommodityName_COMMODITY_HAULED_OFFICE_EQUIPMENT_MACHINES_AND_SUPPLIES                                         CommodityName = 239
	CommodityName_COMMODITY_HAULED_OIL_LUBRICATING_IN_TANKERS                                                     CommodityName = 240
	CommodityName_COMMODITY_HAULED_OIL_FIELD_EQUIPMENT                                                            CommodityName = 241
	CommodityName_COMMODITY_HAULED_OIL_IN_BARRELS_OR_SMALL_CANS                                                   CommodityName = 242
	CommodityName_COMMODITY_HAULED_ORES                                                                           CommodityName = 243
	CommodityName_COMMODITY_HAULED_OTHER_BUILDING_MATERIALS_NOT_OTHERWISE_CLASSIFIED                              CommodityName = 244
	CommodityName_COMMODITY_HAULED_OTHER_PAPER_PLASTIC_GLASS                                                      CommodityName = 245
	CommodityName_COMMODITY_HAULED_OXIDIZERS                                                                      CommodityName = 246
	CommodityName_COMMODITY_HAULED_PACKING_MATERIAL_AND_SUPPLIES                                                  CommodityName = 247
	CommodityName_COMMODITY_HAULED_PAINT_AND_PAINT_THINNERS_CANNED_OR_BULK                                        CommodityName = 248
	CommodityName_COMMODITY_HAULED_PAPER_AND_PAPER_PRODUCTS_INCLUDING_PACKAGING_MATERIALS                         CommodityName = 249
	CommodityName_COMMODITY_HAULED_PAPER_SHREDDED                                                                 CommodityName = 250
	CommodityName_COMMODITY_HAULED_PERFUMES_AND_COLOGNES                                                          CommodityName = 251
	CommodityName_COMMODITY_HAULED_PERSONAL_CARE                                                                  CommodityName = 252
	CommodityName_COMMODITY_HAULED_PET_SUPPLIES_FOOD_ETC                                                          CommodityName = 253
	CommodityName_COMMODITY_HAULED_PETROLEUM_PRODUCTS_BULK_OR_IN_TANKERS                                          CommodityName = 254
	CommodityName_COMMODITY_HAULED_PIPE_EXCEPT_OIL_FIELD_PIPE                                                     CommodityName = 255
	CommodityName_COMMODITY_HAULED_PLANTS_AND_NURSERY_STOCK                                                       CommodityName = 256
	CommodityName_COMMODITY_HAULED_PLASTER_DRYWALL_GYPSUM_BOARD                                                   CommodityName = 257
	CommodityName_COMMODITY_HAULED_PLASTIC_PELLETS                                                                CommodityName = 258
	CommodityName_COMMODITY_HAULED_PLASTIC_PRODUCTS                                                               CommodityName = 259
	CommodityName_COMMODITY_HAULED_PLUMBING_FIXTURES_AND_EQUIPMENT                                                CommodityName = 260
	CommodityName_COMMODITY_HAULED_POTASH                                                                         CommodityName = 261
	CommodityName_COMMODITY_HAULED_POTASSIUM_CHLORIDE                                                             CommodityName = 262
	CommodityName_COMMODITY_HAULED_POULTRY_FROZEN_FRESH_PACKAGED_BOXED_PROCESSED                                  CommodityName = 263
	CommodityName_COMMODITY_HAULED_POULTRY_LIVE_DUCKS_CHICKENS_GEESE                                              CommodityName = 264
	CommodityName_COMMODITY_HAULED_PREPACKAGED_FOODS                                                              CommodityName = 265
	CommodityName_COMMODITY_HAULED_PRINTED_MATERIAL                                                               CommodityName = 266
	CommodityName_COMMODITY_HAULED_PRINTERS                                                                       CommodityName = 267
	CommodityName_COMMODITY_HAULED_RADIOACTIVE_MATERIAL                                                           CommodityName = 268
	CommodityName_COMMODITY_HAULED_RAW_SILK                                                                       CommodityName = 269
	CommodityName_COMMODITY_HAULED_REFRIGERATION_MEAT                                                             CommodityName = 270
	CommodityName_COMMODITY_HAULED_REFRIGERATION_NON_MEAT                                                         CommodityName = 271
	CommodityName_COMMODITY_HAULED_RENDERING                                                                      CommodityName = 272
	CommodityName_COMMODITY_HAULED_RIGGING_CRANES                                                                 CommodityName = 273
	CommodityName_COMMODITY_HAULED_ROCK                                                                           CommodityName = 274
	CommodityName_COMMODITY_HAULED_RUBBER_PRODUCTS                                                                CommodityName = 275
	CommodityName_COMMODITY_HAULED_SALINE_SOLUTION                                                                CommodityName = 276
	CommodityName_COMMODITY_HAULED_SALT                                                                           CommodityName = 277
	CommodityName_COMMODITY_HAULED_SAND_AND_GRAVEL_CRUSHED_STONE_SLAG_AGGREGATE_SILICA                            CommodityName = 278
	CommodityName_COMMODITY_HAULED_SATIN                                                                          CommodityName = 279
	CommodityName_COMMODITY_HAULED_SAWDUST                                                                        CommodityName = 280
	CommodityName_COMMODITY_HAULED_SCIENTIFIC_INSTRUMENTS_AND_EQUIPMENT                                           CommodityName = 281
	CommodityName_COMMODITY_HAULED_SCRAP_METAL_IRON_OR_SALVAGE_OPERATIONS                                         CommodityName = 282
	CommodityName_COMMODITY_HAULED_SHAVERS                                                                        CommodityName = 283
	CommodityName_COMMODITY_HAULED_SHELLFISH_MOLLUSKS_CRUSTACEANS_FRESH_OR_FROZEN                                 CommodityName = 284
	CommodityName_COMMODITY_HAULED_SHINGLES                                                                       CommodityName = 285
	CommodityName_COMMODITY_HAULED_SILK                                                                           CommodityName = 286
	CommodityName_COMMODITY_HAULED_SLUDGE_CLASS_AOR_CLASS_B                                                       CommodityName = 287
	CommodityName_COMMODITY_HAULED_SOAP_LIQUID                                                                    CommodityName = 288
	CommodityName_COMMODITY_HAULED_SOD                                                                            CommodityName = 289
	CommodityName_COMMODITY_HAULED_SODIUM_CHLORIDE                                                                CommodityName = 290
	CommodityName_COMMODITY_HAULED_SODIUM_SULFATE                                                                 CommodityName = 291
	CommodityName_COMMODITY_HAULED_SOFTWARE                                                                       CommodityName = 292
	CommodityName_COMMODITY_HAULED_SOLAR_PANELS_CELLS                                                             CommodityName = 293
	CommodityName_COMMODITY_HAULED_SPORTING_GOODS                                                                 CommodityName = 294
	CommodityName_COMMODITY_HAULED_STEEL_AND_CONSTRUCTION_METALS                                                  CommodityName = 295
	CommodityName_COMMODITY_HAULED_STORAGE_AND_MEDIA                                                              CommodityName = 296
	CommodityName_COMMODITY_HAULED_SUEDE                                                                          CommodityName = 297
	CommodityName_COMMODITY_HAULED_SUGAR                                                                          CommodityName = 298
	CommodityName_COMMODITY_HAULED_SUGAR_LIQUID                                                                   CommodityName = 299
	CommodityName_COMMODITY_HAULED_SWINGING_MEAT                                                                  CommodityName = 300
	CommodityName_COMMODITY_HAULED_TELEPHONE_AND_OR_UTILITY_POLES                                                 CommodityName = 301
	CommodityName_COMMODITY_HAULED_TEXTILES                                                                       CommodityName = 302
	CommodityName_COMMODITY_HAULED_TIRES_NEW_ONLY                                                                 CommodityName = 303
	CommodityName_COMMODITY_HAULED_TIRES_SCRAP                                                                    CommodityName = 304
	CommodityName_COMMODITY_HAULED_TOBACCO_FINISHED_PRODUCTS                                                      CommodityName = 305
	CommodityName_COMMODITY_HAULED_TOBACCO_RAW                                                                    CommodityName = 306
	CommodityName_COMMODITY_HAULED_TRAILERS_AS_CARGO_ON_FLATBED                                                   CommodityName = 307
	CommodityName_COMMODITY_HAULED_TRAILERS_AS_CARGO_PULLED_BY_POWER_UNIT                                         CommodityName = 308
	CommodityName_COMMODITY_HAULED_TV_AND_VIDEO                                                                   CommodityName = 309
	CommodityName_COMMODITY_HAULED_UNKNOWN_COMMODITY_A                                                            CommodityName = 310
	CommodityName_COMMODITY_HAULED_UNKNOWN_COMMODITY_B                                                            CommodityName = 311
	CommodityName_COMMODITY_HAULED_UNKNOWN_COMMODITY_C                                                            CommodityName = 312
	CommodityName_COMMODITY_HAULED_UNKNOWN_COMMODITY_D                                                            CommodityName = 313
	CommodityName_COMMODITY_HAULED_UNKNOWN_COMMODITY_E                                                            CommodityName = 314
	CommodityName_COMMODITY_HAULED_UNKNOWN_COMMODITY_F                                                            CommodityName = 315
	CommodityName_COMMODITY_HAULED_URANIUM_ORE                                                                    CommodityName = 316
	CommodityName_COMMODITY_HAULED_URANIUM_OXIDE                                                                  CommodityName = 317
	CommodityName_COMMODITY_HAULED_VANS_CUSTOMIZED                                                                CommodityName = 318
	CommodityName_COMMODITY_HAULED_VEGETABLE_OIL                                                                  CommodityName = 319
	CommodityName_COMMODITY_HAULED_VELVET                                                                         CommodityName = 320
	CommodityName_COMMODITY_HAULED_VIDEO_GAMES                                                                    CommodityName = 321
	CommodityName_COMMODITY_HAULED_WASTE_WATER_LANDFILL                                                           CommodityName = 322
	CommodityName_COMMODITY_HAULED_WATER_WELL                                                                     CommodityName = 323
	CommodityName_COMMODITY_HAULED_WOOL                                                                           CommodityName = 324
)

// Enum value maps for CommodityName.
var (
	CommodityName_name = map[int32]string{
		0:   "COMMODITY_NAME_UNSPECIFIED",
		1:   "COMMODITY_HAULED_AGGREGATES_ROCK_OR_SAND_OR_GRAVEL_OR_STONE_OR_DIRT",
		2:   "COMMODITY_HAULED_APPLIANCES_OR_HARDWARE",
		3:   "COMMODITY_HAULED_ASH",
		4:   "COMMODITY_HAULED_ASPHALT_LIQUID",
		5:   "COMMODITY_HAULED_AUTO_PARTS_ACCESSORIES",
		6:   "COMMODITY_HAULED_BEVERAGES_ALCOHOLIC",
		7:   "COMMODITY_HAULED_BEVERAGES_NON_ALCOHOLIC",
		8:   "COMMODITY_HAULED_BOATS",
		9:   "COMMODITY_HAULED_BUILDING_MATERIALS_FINISHED",
		10:  "COMMODITY_HAULED_BUILDING_MATERIALS_RAW",
		11:  "COMMODITY_HAULED_CHEMICALS_BULK_LIQUID",
		12:  "COMMODITY_HAULED_CLASS1_EXPLOSIVES",
		13:  "COMMODITY_HAULED_CLASS2_GASES",
		14:  "COMMODITY_HAULED_CLASS3_FLAMMABLE_OR_COMBUSTIBLE_LIQUIDS",
		15:  "COMMODITY_HAULED_CLASS4_FLAMMABLE_SOLIDS",
		16:  "COMMODITY_HAULED_CLASS5_OXIDIZING_SUBSTANCES_OR_ORGANIC_PEROXIDES",
		17:  "COMMODITY_HAULED_CLASS6_TOXINS_OR_INFECTIOUS_SUBSTANCES",
		18:  "COMMODITY_HAULED_CLASS7_RADIOACTIVE_MATERIALS",
		19:  "COMMODITY_HAULED_CLASS8_CORROSIVES",
		20:  "COMMODITY_HAULED_CLASS9_MISCELLANEOUS_OR_NOT_OTHERWISE_SPECIFIED",
		21:  "COMMODITY_HAULED_CONSTRUCTION_EQUIPMENT",
		22:  "COMMODITY_HAULED_COSMETICS",
		23:  "COMMODITY_HAULED_DRY_FOODS",
		24:  "COMMODITY_HAULED_ELECTRONICS",
		25:  "COMMODITY_HAULED_EMPTY_CONTAINERS",
		26:  "COMMODITY_HAULED_ENGINES_OR_MACHINERY",
		27:  "COMMODITY_HAULED_FERTILIZERS",
		28:  "COMMODITY_HAULED_FLOWERS_OR_PLANTS",
		29:  "COMMODITY_HAULED_GENERAL_DRY_FREIGHT",
		30:  "COMMODITY_HAULED_GRAIN_OR_SEED_OR_FEED",
		31:  "COMMODITY_HAULED_HAZARDOUS_MATERIALS_OR_BATTERIES",
		32:  "COMMODITY_HAULED_LARGE_MACHINERY",
		33:  "COMMODITY_HAULED_LIQUIDS_FUEL",
		34:  "COMMODITY_HAULED_LIQUIDS_MILK",
		35:  "COMMODITY_HAULED_LIQUIDS_OTHER_NON_FLAMMABLE",
		36:  "COMMODITY_HAULED_LUMBER_OR_LOGS",
		37:  "COMMODITY_HAULED_MAIL_PARCELS_OR_AMAZON",
		38:  "COMMODITY_HAULED_MAIL_USPS",
		39:  "COMMODITY_HAULED_METALS_COPPER",
		40:  "COMMODITY_HAULED_METALS_ROLLED_OR_COILED_STEELS",
		41:  "COMMODITY_HAULED_METALS_BARS_OR_BEAMS",
		42:  "COMMODITY_HAULED_OVER_SIZED_OR_OVERWEIGHT",
		43:  "COMMODITY_HAULED_PAPER_OR_PLASTIC_PRODUCTS",
		44:  "COMMODITY_HAULED_PHARMACEUTICALS",
		45:  "COMMODITY_HAULED_REFRIGERATED_FOODS_MEAT",
		46:  "COMMODITY_HAULED_REFRIGERATED_FOODS_NON_MEAT",
		47:  "COMMODITY_HAULED_REFRIGERATED_GOODS",
		48:  "COMMODITY_HAULED_RETAIL_PRODUCTS",
		49:  "COMMODITY_HAULED_SAND_OR_SILICATE",
		50:  "COMMODITY_HAULED_SCRAP_METAL",
		51:  "COMMODITY_HAULED_WOOD_CHIPS",
		52:  "COMMODITY_HAULED_ADIPIC_PELLETS",
		53:  "COMMODITY_HAULED_AGRICULTURAL_EQUIPMENT",
		54:  "COMMODITY_HAULED_AIRCRAFT_OR_JET_ENGINES",
		55:  "COMMODITY_HAULED_ALL_TERRAIN_VEHICLES",
		56:  "COMMODITY_HAULED_ALUMINUM_CHLORIDE",
		57:  "COMMODITY_HAULED_AMMONIUM_NITRATE_OR_FERTILIZER",
		58:  "COMMODITY_HAULED_AMUSEMENT_DEVICES",
		59:  "COMMODITY_HAULED_ANHYDROUS_AMMONIA_UN1005_CLASS203",
		60:  "COMMODITY_HAULED_APPLIANCES",
		61:  "COMMODITY_HAULED_ASBESTOS",
		62:  "COMMODITY_HAULED_ASPHALT_AND_BLACKTOP",
		63:  "COMMODITY_HAULED_AUTOMOBILE_PARTS_AND_ACCESSORIES",
		64:  "COMMODITY_HAULED_AUTOMOBILES_USED_OR_CLASSIC_OR_ANTIQUE_OR_AUCTION_AND_SNOWBIRD_TYPE_OPERATIONS",
		65:  "COMMODITY_HAULED_AUTOMOBILES_CRUSHED_OR_JUNK",
		66:  "COMMODITY_HAULED_AUTOMOBILES_NEW",
		67:  "COMMODITY_HAULED_AUTOS_PERSONAL_OR_PRIVATE_PASSENGER_OR_MOTOR_HOMES",
		68:  "COMMODITY_HAULED_AUTOS_RACE_CARS",
		69:  "COMMODITY_HAULED_BAKERY_GOODS_BREAD_OR_PIE_OR_PASTRIES_OR_COOKIE_OR_CAKES",
		70:  "COMMODITY_HAULED_BANANAS",
		71:  "COMMODITY_HAULED_BATTERIES_DRY_CAR_OR_MARINE_OR_CAMPERS_OR_ETC",
		72:  "COMMODITY_HAULED_BATTERIES_WET_CAR_OR_MARINE_OR_CAMPER_OR_ETC",
		73:  "COMMODITY_HAULED_BATTERIES_HOUSEHOLD",
		74:  "COMMODITY_HAULED_BEANS_SOYBEANS_OR_KIDNEY_OR_PEAS_OR_LENTILS_OR_CHICKPEAS",
		75:  "COMMODITY_HAULED_BEER_OR_WINE_OR_BRANDY",
		76:  "COMMODITY_HAULED_BEES",
		77:  "COMMODITY_HAULED_BOATS25FEET_AND_OVER_IN_LENGTH_NEW_OR_USED",
		78:  "COMMODITY_HAULED_BOATS_UNDER25FEET_LENGTH_NEW_OR_USED",
		79:  "COMMODITY_HAULED_BOTTLED_AND_CANNED_SOFT_DRINKS",
		80:  "COMMODITY_HAULED_BOTTLED_WATER",
		81:  "COMMODITY_HAULED_BRICKS",
		82:  "COMMODITY_HAULED_BULLDOZER",
		83:  "COMMODITY_HAULED_CADMIUM",
		84:  "COMMODITY_HAULED_CALCIUM_CARBIDE",
		85:  "COMMODITY_HAULED_CALCIUM_CHLORIDE_SOLUTION",
		86:  "COMMODITY_HAULED_CAMERAS_AND_PHOTOGRAPHY",
		87:  "COMMODITY_HAULED_CAMPERS_AND_RECREATIONAL_VEHICLES",
		88:  "COMMODITY_HAULED_CANNED_GOODS",
		89:  "COMMODITY_HAULED_CARBON_BLACK",
		90:  "COMMODITY_HAULED_CARNIVAL_OR_CIRCUS_EQUIPMENT_OR_RIDES",
		91:  "COMMODITY_HAULED_CARPETING",
		92:  "COMMODITY_HAULED_CEILING_OR_FLOORING_AND_WALL_COVERING",
		93:  "COMMODITY_HAULED_CEMENT_AND_CONCRETE_BULK",
		94:  "COMMODITY_HAULED_CERAMIC_TILE_OR_QUARRY_TILE_OR_PAVERS_OR_PROSAIC",
		95:  "COMMODITY_HAULED_CEREALS",
		96:  "COMMODITY_HAULED_CHARCOAL",
		97:  "COMMODITY_HAULED_CHICKEN_SLUDGE_OR_GUTS_HOT_OFFAL_OR_CHICKEN_FAT",
		98:  "COMMODITY_HAULED_CHINA_AND_CERAMICS",
		99:  "COMMODITY_HAULED_CHIPS_OR_CANDY_AND_OTHER_SNACK_FOODS",
		100: "COMMODITY_HAULED_CHLORINE",
		101: "COMMODITY_HAULED_CLASS2_1_FLAMMABLE_GAS",
		102: "COMMODITY_HAULED_CLASS2_2_NON_FLAMMABLE_COMPRESSED_GAS",
		103: "COMMODITY_HAULED_CLASS2_3_POISONOUS_GAS",
		104: "COMMODITY_HAULED_CLASS3_FLAMMABLE_PACKAGING_GROUP_I",
		105: "COMMODITY_HAULED_CLASS3_FLAMMABLE_PACKAGING_GROUP_II",
		106: "COMMODITY_HAULED_CLASS3_FLAMMABLE_PACKAGING_GROUP_III",
		107: "COMMODITY_HAULED_CLASS4_1_FLAMMABLE_SOLID_PACKAGING_GROUP_I",
		108: "COMMODITY_HAULED_CLASS4_1_FLAMMABLE_SOLID_PACKAGING_GROUP_II",
		109: "COMMODITY_HAULED_CLASS4_1_FLAMMABLE_SOLID_PACKAGING_GROUP_III",
		110: "COMMODITY_HAULED_CLASS4_2_SPONTANEOUSLY_COMBUSTIBLE_MATERIAL",
		111: "COMMODITY_HAULED_CLASS4_3_DANGEROUS_WHEN_WET_MATERIAL",
		112: "COMMODITY_HAULED_CLASS5_1_OXIDIZER_PACKAGING_GROUP_I",
		113: "COMMODITY_HAULED_CLASS5_1_OXIDIZER_PACKAGING_GROUP_II",
		114: "COMMODITY_HAULED_CLASS5_1_OXIDIZER_PACKAGING_GROUP_III",
		115: "COMMODITY_HAULED_CLASS5_2_ORGANIC_PEROXIDE",
		116: "COMMODITY_HAULED_CLASS6_1_POISONOUS_MATERIALS_INHALATION_HAZARD",
		117: "COMMODITY_HAULED_CLASS6_1_POISONOUS_MATERIALS_OTHER_THAN_INHALATION_HAZARD",
		118: "COMMODITY_HAULED_CLASS6_2_INFECTIOUS_SUBSTANCES_PACKAGING_GROUP_I",
		119: "COMMODITY_HAULED_CLASS6_2_INFECTIOUS_SUBSTANCES_PACKAGING_GROUP_II",
		120: "COMMODITY_HAULED_CLASS6_2_INFECTIOUS_SUBSTANCES_PACKAGING_GROUP_III",
		121: "COMMODITY_HAULED_CLASS7_RADIOACTIVE",
		122: "COMMODITY_HAULED_CLASS8_CORROSIVES_PACKAGING_GROUP_I",
		123: "COMMODITY_HAULED_CLASS8_CORROSIVES_PACKAGING_GROUP_II",
		124: "COMMODITY_HAULED_CLASS8_CORROSIVES_PACKAGING_GROUP_III",
		125: "COMMODITY_HAULED_CLASS9_MISCELLANEOUS_HAZARDOUS_MATERIALS",
		126: "COMMODITY_HAULED_CLAY",
		127: "COMMODITY_HAULED_CLOTHING",
		128: "COMMODITY_HAULED_COAL",
		129: "COMMODITY_HAULED_COAL_DUST_POWDER_TITANIUM_DIOXIDE",
		130: "COMMODITY_HAULED_COMMUNICATION_EQUIPMENT",
		131: "COMMODITY_HAULED_COMMUNICATIONS_CELL_PHONES",
		132: "COMMODITY_HAULED_COMPRESSED_GASES_AND_HEATING_OIL",
		133: "COMMODITY_HAULED_COMPUTER_ACCESSORIES",
		134: "COMMODITY_HAULED_COMPUTER_COMPONENTS",
		135: "COMMODITY_HAULED_COMPUTER_HARDWARE",
		136: "COMMODITY_HAULED_CONCRETE_PRODUCTS",
		137: "COMMODITY_HAULED_CONFECTIONARY_PRODUCTS",
		138: "COMMODITY_HAULED_CONSTRUCTION_DEBRIS_INCLUDES_DEMOLITION_DEBRIS",
		139: "COMMODITY_HAULED_CONTAMINATED_DIRT_OR_SOIL",
		140: "COMMODITY_HAULED_COPPER_AND_COPPER_PRODUCTS",
		141: "COMMODITY_HAULED_CORROSIVE_SOLIDS_SALT",
		142: "COMMODITY_HAULED_COTTON_GINNED",
		143: "COMMODITY_HAULED_COWHIDES_RAW_HIDES",
		144: "COMMODITY_HAULED_CRANES_BOOMS",
		145: "COMMODITY_HAULED_DAIRY_PRODUCTS",
		146: "COMMODITY_HAULED_DIET_FOODS",
		147: "COMMODITY_HAULED_DIMENSION_STONE_STONE_SLABS_GRANITE_MARBLE_LIMESTONE_SLATE",
		148: "COMMODITY_HAULED_DRIVE_TOW_AWAY_TRAILERS_TRACTORS",
		149: "COMMODITY_HAULED_DRUGS_OVER_THE_COUNTER_DRUGS_MEDICATIONS",
		150: "COMMODITY_HAULED_DRUGS_PRESCRIPTIONS_AND_PHARMACEUTICALS",
		151: "COMMODITY_HAULED_DRY_CHEMICALS",
		152: "COMMODITY_HAULED_DRY_ICE",
		153: "COMMODITY_HAULED_EDIBLE_OILS",
		154: "COMMODITY_HAULED_EGGS",
		155: "COMMODITY_HAULED_ELECTRICAL_EQUIPMENT",
		156: "COMMODITY_HAULED_ELECTRICAL_PARTS_SUPPLIES_OR_FIXTURES_SUPPLIES",
		157: "COMMODITY_HAULED_ELECTRICAL_SYSTEMS_AND_EQUIPMENT",
		158: "COMMODITY_HAULED_ELECTRONICS_AND_COMPUTERS_NOT_OTHERWISE_CLASSIFIED",
		159: "COMMODITY_HAULED_EXOTIC_CIRCUS_ZOO_OR_WILD_ANIMALS_LIVE",
		160: "COMMODITY_HAULED_EXPLOSIVES_DETONATORS",
		161: "COMMODITY_HAULED_FEED_SEEDS",
		162: "COMMODITY_HAULED_FERTILIZER_DRY_BAGGED",
		163: "COMMODITY_HAULED_FERTILIZER_LIQUID",
		164: "COMMODITY_HAULED_FILM_CELLULOID_SCRAP",
		165: "COMMODITY_HAULED_FIREARMS_AND_SUPPLIES_AMMUNITION_BLACK_POWDER_ETC",
		166: "COMMODITY_HAULED_FIREWOOD",
		167: "COMMODITY_HAULED_FIREWORKS",
		168: "COMMODITY_HAULED_FISH_LIVE",
		169: "COMMODITY_HAULED_FISH_AND_SEAFOOD_FRESH_OR_FROZEN",
		170: "COMMODITY_HAULED_FLAMMABLES",
		171: "COMMODITY_HAULED_FLOUR",
		172: "COMMODITY_HAULED_FLY_ASH",
		173: "COMMODITY_HAULED_FRESH_FOODS_PRODUCE_VEGETABLES_FRUIT",
		174: "COMMODITY_HAULED_FROZEN_FOODS_VEGETABLES_FRUIT_EXCLUDING_FISH_AND_SEAFOOD",
		175: "COMMODITY_HAULED_FROZEN_READY_TO_BAKE_COOK_FOODS",
		176: "COMMODITY_HAULED_FUR_FUR_SKIN_PELTS",
		177: "COMMODITY_HAULED_FURNITURE",
		178: "COMMODITY_HAULED_GAME_OR_WILD_BIRDS_LIVE_PHEASANTS_QUAIL_DUCK_GEESE",
		179: "COMMODITY_HAULED_GARBAGE_REFUSE_TRASH",
		180: "COMMODITY_HAULED_GASOLINE",
		181: "COMMODITY_HAULED_GLASS_FLAT",
		182: "COMMODITY_HAULED_GLASS_DUST_POWDER",
		183: "COMMODITY_HAULED_GLASS_PRODUCTS",
		184: "COMMODITY_HAULED_GOLF_CARTS",
		185: "COMMODITY_HAULED_GRAINS_CORN_WHEAT_BARLEY_RICE_OATS_RYE_PEANUTS_SORGHUM",
		186: "COMMODITY_HAULED_GYPSUM",
		187: "COMMODITY_HAULED_HAND_TOOLS_POWER_TOOLS",
		188: "COMMODITY_HAULED_HANDHELD_DEVICES_GPS_PDAS_IPODS_HEADSETS_HEADPHONES_MP3",
		189: "COMMODITY_HAULED_HAY",
		190: "COMMODITY_HAULED_HAZARDOUS_WASTE",
		191: "COMMODITY_HAULED_HEALTH_AIDS",
		192: "COMMODITY_HAULED_HEATING_VENTILATION_AND_AIR_CONDITIONING",
		193: "COMMODITY_HAULED_HOME_AUDIO",
		194: "COMMODITY_HAULED_HOME_FURNISHINGS_AND_ACCESSORIES_DRAPES",
		195: "COMMODITY_HAULED_HOUSE_AND_BUILDING_MOVERS",
		196: "COMMODITY_HAULED_HOUSEHOLD_CLEANING_PRODUCTS",
		197: "COMMODITY_HAULED_HOUSEHOLD_HARDWARE_FASTENERS_KEYS_LOCKS_HINGES_CHAINS",
		198: "COMMODITY_HAULED_HOUSEHOLD_HARDWARE_PLUMBING_SUPPLIES_PAINT_CABINET_BATH",
		199: "COMMODITY_HAULED_HOUSEWARES_COOKING_UTENSILS_DISHES_ETC",
		200: "COMMODITY_HAULED_ICE_CREAM_ICE",
		201: "COMMODITY_HAULED_INDUSTRIAL_EQUIPMENT",
		202: "COMMODITY_HAULED_INK_WATER_SOLUBLE",
		203: "COMMODITY_HAULED_INPUT_DEVICES",
		204: "COMMODITY_HAULED_INTEGRATED_CIRCUIT_COMPUTER_CHIP_MICROPROCESSORS_SEMICONDUCTOR_HARD_DRIVE",
		205: "COMMODITY_HAULED_INTERMODAL_CONTAINER_CONTAINERIZED_FREIGHT",
		206: "COMMODITY_HAULED_JEWELRY",
		207: "COMMODITY_HAULED_JUNK",
		208: "COMMODITY_HAULED_LAWN_AND_GARDEN",
		209: "COMMODITY_HAULED_LEAD_POWDER_DUST",
		210: "COMMODITY_HAULED_LEATHER",
		211: "COMMODITY_HAULED_LIME_SLACKED_AND_UNSLACKED",
		212: "COMMODITY_HAULED_LIMESTONE",
		213: "COMMODITY_HAULED_LIQUID_LATEX",
		214: "COMMODITY_HAULED_LIQUOR_EXCLUDING_BEER_AND_WINE",
		215: "COMMODITY_HAULED_LIVESTOCK_LIVE_PIGS_CATTLE_SHEEP_HORSES",
		216: "COMMODITY_HAULED_LOGS_LOGGERS_WOOD_HARVESTING_PULP",
		217: "COMMODITY_HAULED_LUMBER",
		218: "COMMODITY_HAULED_MACHINERY_AND_HEAVY_EQUIPMENT",
		219: "COMMODITY_HAULED_MAIL",
		220: "COMMODITY_HAULED_MANURE_AND_FERTILIZER_BULK",
		221: "COMMODITY_HAULED_MEAT_FROZEN_FRESH_PACKAGED_BOXED_EXCLUDING_SWINGING_MEAT",
		222: "COMMODITY_HAULED_MEDICAL_INSTRUMENTS",
		223: "COMMODITY_HAULED_METAL_PRODUCTS",
		224: "COMMODITY_HAULED_METAL_SHEETS_COILS_ROLLS",
		225: "COMMODITY_HAULED_METHYL_BROMIDE",
		226: "COMMODITY_HAULED_METHYL_METHANOL_ALCOHOL",
		227: "COMMODITY_HAULED_MILK",
		228: "COMMODITY_HAULED_MOBILE_HOMES",
		229: "COMMODITY_HAULED_MOBILE_MODULAR_HOMES",
		230: "COMMODITY_HAULED_MOLASSES",
		231: "COMMODITY_HAULED_MOTORCYCLES",
		232: "COMMODITY_HAULED_MULCH_TOP_SOIL_OR_FILL",
		233: "COMMODITY_HAULED_MULTIMEDIA_PROJECTORS",
		234: "COMMODITY_HAULED_MUSICAL_INSTRUMENTS",
		235: "COMMODITY_HAULED_MUSLIN",
		236: "COMMODITY_HAULED_NETWORKING",
		237: "COMMODITY_HAULED_NUTRITION",
		238: "COMMODITY_HAULED_NUTS_AND_SEEDS",
		239: "COMMODITY_HAULED_OFFICE_EQUIPMENT_MACHINES_AND_SUPPLIES",
		240: "COMMODITY_HAULED_OIL_LUBRICATING_IN_TANKERS",
		241: "COMMODITY_HAULED_OIL_FIELD_EQUIPMENT",
		242: "COMMODITY_HAULED_OIL_IN_BARRELS_OR_SMALL_CANS",
		243: "COMMODITY_HAULED_ORES",
		244: "COMMODITY_HAULED_OTHER_BUILDING_MATERIALS_NOT_OTHERWISE_CLASSIFIED",
		245: "COMMODITY_HAULED_OTHER_PAPER_PLASTIC_GLASS",
		246: "COMMODITY_HAULED_OXIDIZERS",
		247: "COMMODITY_HAULED_PACKING_MATERIAL_AND_SUPPLIES",
		248: "COMMODITY_HAULED_PAINT_AND_PAINT_THINNERS_CANNED_OR_BULK",
		249: "COMMODITY_HAULED_PAPER_AND_PAPER_PRODUCTS_INCLUDING_PACKAGING_MATERIALS",
		250: "COMMODITY_HAULED_PAPER_SHREDDED",
		251: "COMMODITY_HAULED_PERFUMES_AND_COLOGNES",
		252: "COMMODITY_HAULED_PERSONAL_CARE",
		253: "COMMODITY_HAULED_PET_SUPPLIES_FOOD_ETC",
		254: "COMMODITY_HAULED_PETROLEUM_PRODUCTS_BULK_OR_IN_TANKERS",
		255: "COMMODITY_HAULED_PIPE_EXCEPT_OIL_FIELD_PIPE",
		256: "COMMODITY_HAULED_PLANTS_AND_NURSERY_STOCK",
		257: "COMMODITY_HAULED_PLASTER_DRYWALL_GYPSUM_BOARD",
		258: "COMMODITY_HAULED_PLASTIC_PELLETS",
		259: "COMMODITY_HAULED_PLASTIC_PRODUCTS",
		260: "COMMODITY_HAULED_PLUMBING_FIXTURES_AND_EQUIPMENT",
		261: "COMMODITY_HAULED_POTASH",
		262: "COMMODITY_HAULED_POTASSIUM_CHLORIDE",
		263: "COMMODITY_HAULED_POULTRY_FROZEN_FRESH_PACKAGED_BOXED_PROCESSED",
		264: "COMMODITY_HAULED_POULTRY_LIVE_DUCKS_CHICKENS_GEESE",
		265: "COMMODITY_HAULED_PREPACKAGED_FOODS",
		266: "COMMODITY_HAULED_PRINTED_MATERIAL",
		267: "COMMODITY_HAULED_PRINTERS",
		268: "COMMODITY_HAULED_RADIOACTIVE_MATERIAL",
		269: "COMMODITY_HAULED_RAW_SILK",
		270: "COMMODITY_HAULED_REFRIGERATION_MEAT",
		271: "COMMODITY_HAULED_REFRIGERATION_NON_MEAT",
		272: "COMMODITY_HAULED_RENDERING",
		273: "COMMODITY_HAULED_RIGGING_CRANES",
		274: "COMMODITY_HAULED_ROCK",
		275: "COMMODITY_HAULED_RUBBER_PRODUCTS",
		276: "COMMODITY_HAULED_SALINE_SOLUTION",
		277: "COMMODITY_HAULED_SALT",
		278: "COMMODITY_HAULED_SAND_AND_GRAVEL_CRUSHED_STONE_SLAG_AGGREGATE_SILICA",
		279: "COMMODITY_HAULED_SATIN",
		280: "COMMODITY_HAULED_SAWDUST",
		281: "COMMODITY_HAULED_SCIENTIFIC_INSTRUMENTS_AND_EQUIPMENT",
		282: "COMMODITY_HAULED_SCRAP_METAL_IRON_OR_SALVAGE_OPERATIONS",
		283: "COMMODITY_HAULED_SHAVERS",
		284: "COMMODITY_HAULED_SHELLFISH_MOLLUSKS_CRUSTACEANS_FRESH_OR_FROZEN",
		285: "COMMODITY_HAULED_SHINGLES",
		286: "COMMODITY_HAULED_SILK",
		287: "COMMODITY_HAULED_SLUDGE_CLASS_AOR_CLASS_B",
		288: "COMMODITY_HAULED_SOAP_LIQUID",
		289: "COMMODITY_HAULED_SOD",
		290: "COMMODITY_HAULED_SODIUM_CHLORIDE",
		291: "COMMODITY_HAULED_SODIUM_SULFATE",
		292: "COMMODITY_HAULED_SOFTWARE",
		293: "COMMODITY_HAULED_SOLAR_PANELS_CELLS",
		294: "COMMODITY_HAULED_SPORTING_GOODS",
		295: "COMMODITY_HAULED_STEEL_AND_CONSTRUCTION_METALS",
		296: "COMMODITY_HAULED_STORAGE_AND_MEDIA",
		297: "COMMODITY_HAULED_SUEDE",
		298: "COMMODITY_HAULED_SUGAR",
		299: "COMMODITY_HAULED_SUGAR_LIQUID",
		300: "COMMODITY_HAULED_SWINGING_MEAT",
		301: "COMMODITY_HAULED_TELEPHONE_AND_OR_UTILITY_POLES",
		302: "COMMODITY_HAULED_TEXTILES",
		303: "COMMODITY_HAULED_TIRES_NEW_ONLY",
		304: "COMMODITY_HAULED_TIRES_SCRAP",
		305: "COMMODITY_HAULED_TOBACCO_FINISHED_PRODUCTS",
		306: "COMMODITY_HAULED_TOBACCO_RAW",
		307: "COMMODITY_HAULED_TRAILERS_AS_CARGO_ON_FLATBED",
		308: "COMMODITY_HAULED_TRAILERS_AS_CARGO_PULLED_BY_POWER_UNIT",
		309: "COMMODITY_HAULED_TV_AND_VIDEO",
		310: "COMMODITY_HAULED_UNKNOWN_COMMODITY_A",
		311: "COMMODITY_HAULED_UNKNOWN_COMMODITY_B",
		312: "COMMODITY_HAULED_UNKNOWN_COMMODITY_C",
		313: "COMMODITY_HAULED_UNKNOWN_COMMODITY_D",
		314: "COMMODITY_HAULED_UNKNOWN_COMMODITY_E",
		315: "COMMODITY_HAULED_UNKNOWN_COMMODITY_F",
		316: "COMMODITY_HAULED_URANIUM_ORE",
		317: "COMMODITY_HAULED_URANIUM_OXIDE",
		318: "COMMODITY_HAULED_VANS_CUSTOMIZED",
		319: "COMMODITY_HAULED_VEGETABLE_OIL",
		320: "COMMODITY_HAULED_VELVET",
		321: "COMMODITY_HAULED_VIDEO_GAMES",
		322: "COMMODITY_HAULED_WASTE_WATER_LANDFILL",
		323: "COMMODITY_HAULED_WATER_WELL",
		324: "COMMODITY_HAULED_WOOL",
	}
	CommodityName_value = map[string]int32{
		"COMMODITY_NAME_UNSPECIFIED":                                          0,
		"COMMODITY_HAULED_AGGREGATES_ROCK_OR_SAND_OR_GRAVEL_OR_STONE_OR_DIRT": 1,
		"COMMODITY_HAULED_APPLIANCES_OR_HARDWARE":                             2,
		"COMMODITY_HAULED_ASH":                                                3,
		"COMMODITY_HAULED_ASPHALT_LIQUID":                                     4,
		"COMMODITY_HAULED_AUTO_PARTS_ACCESSORIES":                             5,
		"COMMODITY_HAULED_BEVERAGES_ALCOHOLIC":                                6,
		"COMMODITY_HAULED_BEVERAGES_NON_ALCOHOLIC":                            7,
		"COMMODITY_HAULED_BOATS":                                              8,
		"COMMODITY_HAULED_BUILDING_MATERIALS_FINISHED":                        9,
		"COMMODITY_HAULED_BUILDING_MATERIALS_RAW":                             10,
		"COMMODITY_HAULED_CHEMICALS_BULK_LIQUID":                              11,
		"COMMODITY_HAULED_CLASS1_EXPLOSIVES":                                  12,
		"COMMODITY_HAULED_CLASS2_GASES":                                       13,
		"COMMODITY_HAULED_CLASS3_FLAMMABLE_OR_COMBUSTIBLE_LIQUIDS":            14,
		"COMMODITY_HAULED_CLASS4_FLAMMABLE_SOLIDS":                            15,
		"COMMODITY_HAULED_CLASS5_OXIDIZING_SUBSTANCES_OR_ORGANIC_PEROXIDES":   16,
		"COMMODITY_HAULED_CLASS6_TOXINS_OR_INFECTIOUS_SUBSTANCES":             17,
		"COMMODITY_HAULED_CLASS7_RADIOACTIVE_MATERIALS":                       18,
		"COMMODITY_HAULED_CLASS8_CORROSIVES":                                  19,
		"COMMODITY_HAULED_CLASS9_MISCELLANEOUS_OR_NOT_OTHERWISE_SPECIFIED":    20,
		"COMMODITY_HAULED_CONSTRUCTION_EQUIPMENT":                             21,
		"COMMODITY_HAULED_COSMETICS":                                          22,
		"COMMODITY_HAULED_DRY_FOODS":                                          23,
		"COMMODITY_HAULED_ELECTRONICS":                                        24,
		"COMMODITY_HAULED_EMPTY_CONTAINERS":                                   25,
		"COMMODITY_HAULED_ENGINES_OR_MACHINERY":                               26,
		"COMMODITY_HAULED_FERTILIZERS":                                        27,
		"COMMODITY_HAULED_FLOWERS_OR_PLANTS":                                  28,
		"COMMODITY_HAULED_GENERAL_DRY_FREIGHT":                                29,
		"COMMODITY_HAULED_GRAIN_OR_SEED_OR_FEED":                              30,
		"COMMODITY_HAULED_HAZARDOUS_MATERIALS_OR_BATTERIES":                   31,
		"COMMODITY_HAULED_LARGE_MACHINERY":                                    32,
		"COMMODITY_HAULED_LIQUIDS_FUEL":                                       33,
		"COMMODITY_HAULED_LIQUIDS_MILK":                                       34,
		"COMMODITY_HAULED_LIQUIDS_OTHER_NON_FLAMMABLE":                        35,
		"COMMODITY_HAULED_LUMBER_OR_LOGS":                                     36,
		"COMMODITY_HAULED_MAIL_PARCELS_OR_AMAZON":                             37,
		"COMMODITY_HAULED_MAIL_USPS":                                          38,
		"COMMODITY_HAULED_METALS_COPPER":                                      39,
		"COMMODITY_HAULED_METALS_ROLLED_OR_COILED_STEELS":                     40,
		"COMMODITY_HAULED_METALS_BARS_OR_BEAMS":                               41,
		"COMMODITY_HAULED_OVER_SIZED_OR_OVERWEIGHT":                           42,
		"COMMODITY_HAULED_PAPER_OR_PLASTIC_PRODUCTS":                          43,
		"COMMODITY_HAULED_PHARMACEUTICALS":                                    44,
		"COMMODITY_HAULED_REFRIGERATED_FOODS_MEAT":                            45,
		"COMMODITY_HAULED_REFRIGERATED_FOODS_NON_MEAT":                        46,
		"COMMODITY_HAULED_REFRIGERATED_GOODS":                                 47,
		"COMMODITY_HAULED_RETAIL_PRODUCTS":                                    48,
		"COMMODITY_HAULED_SAND_OR_SILICATE":                                   49,
		"COMMODITY_HAULED_SCRAP_METAL":                                        50,
		"COMMODITY_HAULED_WOOD_CHIPS":                                         51,
		"COMMODITY_HAULED_ADIPIC_PELLETS":                                     52,
		"COMMODITY_HAULED_AGRICULTURAL_EQUIPMENT":                             53,
		"COMMODITY_HAULED_AIRCRAFT_OR_JET_ENGINES":                            54,
		"COMMODITY_HAULED_ALL_TERRAIN_VEHICLES":                               55,
		"COMMODITY_HAULED_ALUMINUM_CHLORIDE":                                  56,
		"COMMODITY_HAULED_AMMONIUM_NITRATE_OR_FERTILIZER":                     57,
		"COMMODITY_HAULED_AMUSEMENT_DEVICES":                                  58,
		"COMMODITY_HAULED_ANHYDROUS_AMMONIA_UN1005_CLASS203":                  59,
		"COMMODITY_HAULED_APPLIANCES":                                         60,
		"COMMODITY_HAULED_ASBESTOS":                                           61,
		"COMMODITY_HAULED_ASPHALT_AND_BLACKTOP":                               62,
		"COMMODITY_HAULED_AUTOMOBILE_PARTS_AND_ACCESSORIES":                   63,
		"COMMODITY_HAULED_AUTOMOBILES_USED_OR_CLASSIC_OR_ANTIQUE_OR_AUCTION_AND_SNOWBIRD_TYPE_OPERATIONS": 64,
		"COMMODITY_HAULED_AUTOMOBILES_CRUSHED_OR_JUNK":                                                    65,
		"COMMODITY_HAULED_AUTOMOBILES_NEW":                                                                66,
		"COMMODITY_HAULED_AUTOS_PERSONAL_OR_PRIVATE_PASSENGER_OR_MOTOR_HOMES":                             67,
		"COMMODITY_HAULED_AUTOS_RACE_CARS":                                                                68,
		"COMMODITY_HAULED_BAKERY_GOODS_BREAD_OR_PIE_OR_PASTRIES_OR_COOKIE_OR_CAKES":                       69,
		"COMMODITY_HAULED_BANANAS":                                                                        70,
		"COMMODITY_HAULED_BATTERIES_DRY_CAR_OR_MARINE_OR_CAMPERS_OR_ETC":                                  71,
		"COMMODITY_HAULED_BATTERIES_WET_CAR_OR_MARINE_OR_CAMPER_OR_ETC":                                   72,
		"COMMODITY_HAULED_BATTERIES_HOUSEHOLD":                                                            73,
		"COMMODITY_HAULED_BEANS_SOYBEANS_OR_KIDNEY_OR_PEAS_OR_LENTILS_OR_CHICKPEAS":                       74,
		"COMMODITY_HAULED_BEER_OR_WINE_OR_BRANDY":                                                         75,
		"COMMODITY_HAULED_BEES":                                                                           76,
		"COMMODITY_HAULED_BOATS25FEET_AND_OVER_IN_LENGTH_NEW_OR_USED":                                     77,
		"COMMODITY_HAULED_BOATS_UNDER25FEET_LENGTH_NEW_OR_USED":                                           78,
		"COMMODITY_HAULED_BOTTLED_AND_CANNED_SOFT_DRINKS":                                                 79,
		"COMMODITY_HAULED_BOTTLED_WATER":                                                                  80,
		"COMMODITY_HAULED_BRICKS":                                                                         81,
		"COMMODITY_HAULED_BULLDOZER":                                                                      82,
		"COMMODITY_HAULED_CADMIUM":                                                                        83,
		"COMMODITY_HAULED_CALCIUM_CARBIDE":                                                                84,
		"COMMODITY_HAULED_CALCIUM_CHLORIDE_SOLUTION":                                                      85,
		"COMMODITY_HAULED_CAMERAS_AND_PHOTOGRAPHY":                                                        86,
		"COMMODITY_HAULED_CAMPERS_AND_RECREATIONAL_VEHICLES":                                              87,
		"COMMODITY_HAULED_CANNED_GOODS":                                                                   88,
		"COMMODITY_HAULED_CARBON_BLACK":                                                                   89,
		"COMMODITY_HAULED_CARNIVAL_OR_CIRCUS_EQUIPMENT_OR_RIDES":                                          90,
		"COMMODITY_HAULED_CARPETING":                                                                      91,
		"COMMODITY_HAULED_CEILING_OR_FLOORING_AND_WALL_COVERING":                                          92,
		"COMMODITY_HAULED_CEMENT_AND_CONCRETE_BULK":                                                       93,
		"COMMODITY_HAULED_CERAMIC_TILE_OR_QUARRY_TILE_OR_PAVERS_OR_PROSAIC":                               94,
		"COMMODITY_HAULED_CEREALS":                                                                        95,
		"COMMODITY_HAULED_CHARCOAL":                                                                       96,
		"COMMODITY_HAULED_CHICKEN_SLUDGE_OR_GUTS_HOT_OFFAL_OR_CHICKEN_FAT":                                97,
		"COMMODITY_HAULED_CHINA_AND_CERAMICS":                                                             98,
		"COMMODITY_HAULED_CHIPS_OR_CANDY_AND_OTHER_SNACK_FOODS":                                           99,
		"COMMODITY_HAULED_CHLORINE":                                                                       100,
		"COMMODITY_HAULED_CLASS2_1_FLAMMABLE_GAS":                                                         101,
		"COMMODITY_HAULED_CLASS2_2_NON_FLAMMABLE_COMPRESSED_GAS":                                          102,
		"COMMODITY_HAULED_CLASS2_3_POISONOUS_GAS":                                                         103,
		"COMMODITY_HAULED_CLASS3_FLAMMABLE_PACKAGING_GROUP_I":                                             104,
		"COMMODITY_HAULED_CLASS3_FLAMMABLE_PACKAGING_GROUP_II":                                            105,
		"COMMODITY_HAULED_CLASS3_FLAMMABLE_PACKAGING_GROUP_III":                                           106,
		"COMMODITY_HAULED_CLASS4_1_FLAMMABLE_SOLID_PACKAGING_GROUP_I":                                     107,
		"COMMODITY_HAULED_CLASS4_1_FLAMMABLE_SOLID_PACKAGING_GROUP_II":                                    108,
		"COMMODITY_HAULED_CLASS4_1_FLAMMABLE_SOLID_PACKAGING_GROUP_III":                                   109,
		"COMMODITY_HAULED_CLASS4_2_SPONTANEOUSLY_COMBUSTIBLE_MATERIAL":                                    110,
		"COMMODITY_HAULED_CLASS4_3_DANGEROUS_WHEN_WET_MATERIAL":                                           111,
		"COMMODITY_HAULED_CLASS5_1_OXIDIZER_PACKAGING_GROUP_I":                                            112,
		"COMMODITY_HAULED_CLASS5_1_OXIDIZER_PACKAGING_GROUP_II":                                           113,
		"COMMODITY_HAULED_CLASS5_1_OXIDIZER_PACKAGING_GROUP_III":                                          114,
		"COMMODITY_HAULED_CLASS5_2_ORGANIC_PEROXIDE":                                                      115,
		"COMMODITY_HAULED_CLASS6_1_POISONOUS_MATERIALS_INHALATION_HAZARD":                                 116,
		"COMMODITY_HAULED_CLASS6_1_POISONOUS_MATERIALS_OTHER_THAN_INHALATION_HAZARD":                      117,
		"COMMODITY_HAULED_CLASS6_2_INFECTIOUS_SUBSTANCES_PACKAGING_GROUP_I":                               118,
		"COMMODITY_HAULED_CLASS6_2_INFECTIOUS_SUBSTANCES_PACKAGING_GROUP_II":                              119,
		"COMMODITY_HAULED_CLASS6_2_INFECTIOUS_SUBSTANCES_PACKAGING_GROUP_III":                             120,
		"COMMODITY_HAULED_CLASS7_RADIOACTIVE":                                                             121,
		"COMMODITY_HAULED_CLASS8_CORROSIVES_PACKAGING_GROUP_I":                                            122,
		"COMMODITY_HAULED_CLASS8_CORROSIVES_PACKAGING_GROUP_II":                                           123,
		"COMMODITY_HAULED_CLASS8_CORROSIVES_PACKAGING_GROUP_III":                                          124,
		"COMMODITY_HAULED_CLASS9_MISCELLANEOUS_HAZARDOUS_MATERIALS":                                       125,
		"COMMODITY_HAULED_CLAY":                                                                           126,
		"COMMODITY_HAULED_CLOTHING":                                                                       127,
		"COMMODITY_HAULED_COAL":                                                                           128,
		"COMMODITY_HAULED_COAL_DUST_POWDER_TITANIUM_DIOXIDE":                                              129,
		"COMMODITY_HAULED_COMMUNICATION_EQUIPMENT":                                                        130,
		"COMMODITY_HAULED_COMMUNICATIONS_CELL_PHONES":                                                     131,
		"COMMODITY_HAULED_COMPRESSED_GASES_AND_HEATING_OIL":                                               132,
		"COMMODITY_HAULED_COMPUTER_ACCESSORIES":                                                           133,
		"COMMODITY_HAULED_COMPUTER_COMPONENTS":                                                            134,
		"COMMODITY_HAULED_COMPUTER_HARDWARE":                                                              135,
		"COMMODITY_HAULED_CONCRETE_PRODUCTS":                                                              136,
		"COMMODITY_HAULED_CONFECTIONARY_PRODUCTS":                                                         137,
		"COMMODITY_HAULED_CONSTRUCTION_DEBRIS_INCLUDES_DEMOLITION_DEBRIS":                                 138,
		"COMMODITY_HAULED_CONTAMINATED_DIRT_OR_SOIL":                                                      139,
		"COMMODITY_HAULED_COPPER_AND_COPPER_PRODUCTS":                                                     140,
		"COMMODITY_HAULED_CORROSIVE_SOLIDS_SALT":                                                          141,
		"COMMODITY_HAULED_COTTON_GINNED":                                                                  142,
		"COMMODITY_HAULED_COWHIDES_RAW_HIDES":                                                             143,
		"COMMODITY_HAULED_CRANES_BOOMS":                                                                   144,
		"COMMODITY_HAULED_DAIRY_PRODUCTS":                                                                 145,
		"COMMODITY_HAULED_DIET_FOODS":                                                                     146,
		"COMMODITY_HAULED_DIMENSION_STONE_STONE_SLABS_GRANITE_MARBLE_LIMESTONE_SLATE":                     147,
		"COMMODITY_HAULED_DRIVE_TOW_AWAY_TRAILERS_TRACTORS":                                               148,
		"COMMODITY_HAULED_DRUGS_OVER_THE_COUNTER_DRUGS_MEDICATIONS":                                       149,
		"COMMODITY_HAULED_DRUGS_PRESCRIPTIONS_AND_PHARMACEUTICALS":                                        150,
		"COMMODITY_HAULED_DRY_CHEMICALS":                                                                  151,
		"COMMODITY_HAULED_DRY_ICE":                                                                        152,
		"COMMODITY_HAULED_EDIBLE_OILS":                                                                    153,
		"COMMODITY_HAULED_EGGS":                                                                           154,
		"COMMODITY_HAULED_ELECTRICAL_EQUIPMENT":                                                           155,
		"COMMODITY_HAULED_ELECTRICAL_PARTS_SUPPLIES_OR_FIXTURES_SUPPLIES":                                 156,
		"COMMODITY_HAULED_ELECTRICAL_SYSTEMS_AND_EQUIPMENT":                                               157,
		"COMMODITY_HAULED_ELECTRONICS_AND_COMPUTERS_NOT_OTHERWISE_CLASSIFIED":                             158,
		"COMMODITY_HAULED_EXOTIC_CIRCUS_ZOO_OR_WILD_ANIMALS_LIVE":                                         159,
		"COMMODITY_HAULED_EXPLOSIVES_DETONATORS":                                                          160,
		"COMMODITY_HAULED_FEED_SEEDS":                                                                     161,
		"COMMODITY_HAULED_FERTILIZER_DRY_BAGGED":                                                          162,
		"COMMODITY_HAULED_FERTILIZER_LIQUID":                                                              163,
		"COMMODITY_HAULED_FILM_CELLULOID_SCRAP":                                                           164,
		"COMMODITY_HAULED_FIREARMS_AND_SUPPLIES_AMMUNITION_BLACK_POWDER_ETC":                              165,
		"COMMODITY_HAULED_FIREWOOD":                                                                       166,
		"COMMODITY_HAULED_FIREWORKS":                                                                      167,
		"COMMODITY_HAULED_FISH_LIVE":                                                                      168,
		"COMMODITY_HAULED_FISH_AND_SEAFOOD_FRESH_OR_FROZEN":                                               169,
		"COMMODITY_HAULED_FLAMMABLES":                                                                     170,
		"COMMODITY_HAULED_FLOUR":                                                                          171,
		"COMMODITY_HAULED_FLY_ASH":                                                                        172,
		"COMMODITY_HAULED_FRESH_FOODS_PRODUCE_VEGETABLES_FRUIT":                                           173,
		"COMMODITY_HAULED_FROZEN_FOODS_VEGETABLES_FRUIT_EXCLUDING_FISH_AND_SEAFOOD":                       174,
		"COMMODITY_HAULED_FROZEN_READY_TO_BAKE_COOK_FOODS":                                                175,
		"COMMODITY_HAULED_FUR_FUR_SKIN_PELTS":                                                             176,
		"COMMODITY_HAULED_FURNITURE":                                                                      177,
		"COMMODITY_HAULED_GAME_OR_WILD_BIRDS_LIVE_PHEASANTS_QUAIL_DUCK_GEESE":                             178,
		"COMMODITY_HAULED_GARBAGE_REFUSE_TRASH":                                                           179,
		"COMMODITY_HAULED_GASOLINE":                                                                       180,
		"COMMODITY_HAULED_GLASS_FLAT":                                                                     181,
		"COMMODITY_HAULED_GLASS_DUST_POWDER":                                                              182,
		"COMMODITY_HAULED_GLASS_PRODUCTS":                                                                 183,
		"COMMODITY_HAULED_GOLF_CARTS":                                                                     184,
		"COMMODITY_HAULED_GRAINS_CORN_WHEAT_BARLEY_RICE_OATS_RYE_PEANUTS_SORGHUM":                         185,
		"COMMODITY_HAULED_GYPSUM":                                                                         186,
		"COMMODITY_HAULED_HAND_TOOLS_POWER_TOOLS":                                                         187,
		"COMMODITY_HAULED_HANDHELD_DEVICES_GPS_PDAS_IPODS_HEADSETS_HEADPHONES_MP3":                        188,
		"COMMODITY_HAULED_HAY":                                                                            189,
		"COMMODITY_HAULED_HAZARDOUS_WASTE":                                                                190,
		"COMMODITY_HAULED_HEALTH_AIDS":                                                                    191,
		"COMMODITY_HAULED_HEATING_VENTILATION_AND_AIR_CONDITIONING":                                       192,
		"COMMODITY_HAULED_HOME_AUDIO":                                                                     193,
		"COMMODITY_HAULED_HOME_FURNISHINGS_AND_ACCESSORIES_DRAPES":                                        194,
		"COMMODITY_HAULED_HOUSE_AND_BUILDING_MOVERS":                                                      195,
		"COMMODITY_HAULED_HOUSEHOLD_CLEANING_PRODUCTS":                                                    196,
		"COMMODITY_HAULED_HOUSEHOLD_HARDWARE_FASTENERS_KEYS_LOCKS_HINGES_CHAINS":                          197,
		"COMMODITY_HAULED_HOUSEHOLD_HARDWARE_PLUMBING_SUPPLIES_PAINT_CABINET_BATH":                        198,
		"COMMODITY_HAULED_HOUSEWARES_COOKING_UTENSILS_DISHES_ETC":                                         199,
		"COMMODITY_HAULED_ICE_CREAM_ICE":                                                                  200,
		"COMMODITY_HAULED_INDUSTRIAL_EQUIPMENT":                                                           201,
		"COMMODITY_HAULED_INK_WATER_SOLUBLE":                                                              202,
		"COMMODITY_HAULED_INPUT_DEVICES":                                                                  203,
		"COMMODITY_HAULED_INTEGRATED_CIRCUIT_COMPUTER_CHIP_MICROPROCESSORS_SEMICONDUCTOR_HARD_DRIVE":      204,
		"COMMODITY_HAULED_INTERMODAL_CONTAINER_CONTAINERIZED_FREIGHT":                                     205,
		"COMMODITY_HAULED_JEWELRY":                                                                        206,
		"COMMODITY_HAULED_JUNK":                                                                           207,
		"COMMODITY_HAULED_LAWN_AND_GARDEN":                                                                208,
		"COMMODITY_HAULED_LEAD_POWDER_DUST":                                                               209,
		"COMMODITY_HAULED_LEATHER":                                                                        210,
		"COMMODITY_HAULED_LIME_SLACKED_AND_UNSLACKED":                                                     211,
		"COMMODITY_HAULED_LIMESTONE":                                                                      212,
		"COMMODITY_HAULED_LIQUID_LATEX":                                                                   213,
		"COMMODITY_HAULED_LIQUOR_EXCLUDING_BEER_AND_WINE":                                                 214,
		"COMMODITY_HAULED_LIVESTOCK_LIVE_PIGS_CATTLE_SHEEP_HORSES":                                        215,
		"COMMODITY_HAULED_LOGS_LOGGERS_WOOD_HARVESTING_PULP":                                              216,
		"COMMODITY_HAULED_LUMBER":                                                                         217,
		"COMMODITY_HAULED_MACHINERY_AND_HEAVY_EQUIPMENT":                                                  218,
		"COMMODITY_HAULED_MAIL":                                                                           219,
		"COMMODITY_HAULED_MANURE_AND_FERTILIZER_BULK":                                                     220,
		"COMMODITY_HAULED_MEAT_FROZEN_FRESH_PACKAGED_BOXED_EXCLUDING_SWINGING_MEAT":                       221,
		"COMMODITY_HAULED_MEDICAL_INSTRUMENTS":                                                            222,
		"COMMODITY_HAULED_METAL_PRODUCTS":                                                                 223,
		"COMMODITY_HAULED_METAL_SHEETS_COILS_ROLLS":                                                       224,
		"COMMODITY_HAULED_METHYL_BROMIDE":                                                                 225,
		"COMMODITY_HAULED_METHYL_METHANOL_ALCOHOL":                                                        226,
		"COMMODITY_HAULED_MILK":                                                                           227,
		"COMMODITY_HAULED_MOBILE_HOMES":                                                                   228,
		"COMMODITY_HAULED_MOBILE_MODULAR_HOMES":                                                           229,
		"COMMODITY_HAULED_MOLASSES":                                                                       230,
		"COMMODITY_HAULED_MOTORCYCLES":                                                                    231,
		"COMMODITY_HAULED_MULCH_TOP_SOIL_OR_FILL":                                                         232,
		"COMMODITY_HAULED_MULTIMEDIA_PROJECTORS":                                                          233,
		"COMMODITY_HAULED_MUSICAL_INSTRUMENTS":                                                            234,
		"COMMODITY_HAULED_MUSLIN":                                                                         235,
		"COMMODITY_HAULED_NETWORKING":                                                                     236,
		"COMMODITY_HAULED_NUTRITION":                                                                      237,
		"COMMODITY_HAULED_NUTS_AND_SEEDS":                                                                 238,
		"COMMODITY_HAULED_OFFICE_EQUIPMENT_MACHINES_AND_SUPPLIES":                                         239,
		"COMMODITY_HAULED_OIL_LUBRICATING_IN_TANKERS":                                                     240,
		"COMMODITY_HAULED_OIL_FIELD_EQUIPMENT":                                                            241,
		"COMMODITY_HAULED_OIL_IN_BARRELS_OR_SMALL_CANS":                                                   242,
		"COMMODITY_HAULED_ORES":                                                                           243,
		"COMMODITY_HAULED_OTHER_BUILDING_MATERIALS_NOT_OTHERWISE_CLASSIFIED":                              244,
		"COMMODITY_HAULED_OTHER_PAPER_PLASTIC_GLASS":                                                      245,
		"COMMODITY_HAULED_OXIDIZERS":                                                                      246,
		"COMMODITY_HAULED_PACKING_MATERIAL_AND_SUPPLIES":                                                  247,
		"COMMODITY_HAULED_PAINT_AND_PAINT_THINNERS_CANNED_OR_BULK":                                        248,
		"COMMODITY_HAULED_PAPER_AND_PAPER_PRODUCTS_INCLUDING_PACKAGING_MATERIALS":                         249,
		"COMMODITY_HAULED_PAPER_SHREDDED":                                                                 250,
		"COMMODITY_HAULED_PERFUMES_AND_COLOGNES":                                                          251,
		"COMMODITY_HAULED_PERSONAL_CARE":                                                                  252,
		"COMMODITY_HAULED_PET_SUPPLIES_FOOD_ETC":                                                          253,
		"COMMODITY_HAULED_PETROLEUM_PRODUCTS_BULK_OR_IN_TANKERS":                                          254,
		"COMMODITY_HAULED_PIPE_EXCEPT_OIL_FIELD_PIPE":                                                     255,
		"COMMODITY_HAULED_PLANTS_AND_NURSERY_STOCK":                                                       256,
		"COMMODITY_HAULED_PLASTER_DRYWALL_GYPSUM_BOARD":                                                   257,
		"COMMODITY_HAULED_PLASTIC_PELLETS":                                                                258,
		"COMMODITY_HAULED_PLASTIC_PRODUCTS":                                                               259,
		"COMMODITY_HAULED_PLUMBING_FIXTURES_AND_EQUIPMENT":                                                260,
		"COMMODITY_HAULED_POTASH":                                                                         261,
		"COMMODITY_HAULED_POTASSIUM_CHLORIDE":                                                             262,
		"COMMODITY_HAULED_POULTRY_FROZEN_FRESH_PACKAGED_BOXED_PROCESSED":                                  263,
		"COMMODITY_HAULED_POULTRY_LIVE_DUCKS_CHICKENS_GEESE":                                              264,
		"COMMODITY_HAULED_PREPACKAGED_FOODS":                                                              265,
		"COMMODITY_HAULED_PRINTED_MATERIAL":                                                               266,
		"COMMODITY_HAULED_PRINTERS":                                                                       267,
		"COMMODITY_HAULED_RADIOACTIVE_MATERIAL":                                                           268,
		"COMMODITY_HAULED_RAW_SILK":                                                                       269,
		"COMMODITY_HAULED_REFRIGERATION_MEAT":                                                             270,
		"COMMODITY_HAULED_REFRIGERATION_NON_MEAT":                                                         271,
		"COMMODITY_HAULED_RENDERING":                                                                      272,
		"COMMODITY_HAULED_RIGGING_CRANES":                                                                 273,
		"COMMODITY_HAULED_ROCK":                                                                           274,
		"COMMODITY_HAULED_RUBBER_PRODUCTS":                                                                275,
		"COMMODITY_HAULED_SALINE_SOLUTION":                                                                276,
		"COMMODITY_HAULED_SALT":                                                                           277,
		"COMMODITY_HAULED_SAND_AND_GRAVEL_CRUSHED_STONE_SLAG_AGGREGATE_SILICA":                            278,
		"COMMODITY_HAULED_SATIN":                                                                          279,
		"COMMODITY_HAULED_SAWDUST":                                                                        280,
		"COMMODITY_HAULED_SCIENTIFIC_INSTRUMENTS_AND_EQUIPMENT":                                           281,
		"COMMODITY_HAULED_SCRAP_METAL_IRON_OR_SALVAGE_OPERATIONS":                                         282,
		"COMMODITY_HAULED_SHAVERS":                                                                        283,
		"COMMODITY_HAULED_SHELLFISH_MOLLUSKS_CRUSTACEANS_FRESH_OR_FROZEN":                                 284,
		"COMMODITY_HAULED_SHINGLES":                                                                       285,
		"COMMODITY_HAULED_SILK":                                                                           286,
		"COMMODITY_HAULED_SLUDGE_CLASS_AOR_CLASS_B":                                                       287,
		"COMMODITY_HAULED_SOAP_LIQUID":                                                                    288,
		"COMMODITY_HAULED_SOD":                                                                            289,
		"COMMODITY_HAULED_SODIUM_CHLORIDE":                                                                290,
		"COMMODITY_HAULED_SODIUM_SULFATE":                                                                 291,
		"COMMODITY_HAULED_SOFTWARE":                                                                       292,
		"COMMODITY_HAULED_SOLAR_PANELS_CELLS":                                                             293,
		"COMMODITY_HAULED_SPORTING_GOODS":                                                                 294,
		"COMMODITY_HAULED_STEEL_AND_CONSTRUCTION_METALS":                                                  295,
		"COMMODITY_HAULED_STORAGE_AND_MEDIA":                                                              296,
		"COMMODITY_HAULED_SUEDE":                                                                          297,
		"COMMODITY_HAULED_SUGAR":                                                                          298,
		"COMMODITY_HAULED_SUGAR_LIQUID":                                                                   299,
		"COMMODITY_HAULED_SWINGING_MEAT":                                                                  300,
		"COMMODITY_HAULED_TELEPHONE_AND_OR_UTILITY_POLES":                                                 301,
		"COMMODITY_HAULED_TEXTILES":                                                                       302,
		"COMMODITY_HAULED_TIRES_NEW_ONLY":                                                                 303,
		"COMMODITY_HAULED_TIRES_SCRAP":                                                                    304,
		"COMMODITY_HAULED_TOBACCO_FINISHED_PRODUCTS":                                                      305,
		"COMMODITY_HAULED_TOBACCO_RAW":                                                                    306,
		"COMMODITY_HAULED_TRAILERS_AS_CARGO_ON_FLATBED":                                                   307,
		"COMMODITY_HAULED_TRAILERS_AS_CARGO_PULLED_BY_POWER_UNIT":                                         308,
		"COMMODITY_HAULED_TV_AND_VIDEO":                                                                   309,
		"COMMODITY_HAULED_UNKNOWN_COMMODITY_A":                                                            310,
		"COMMODITY_HAULED_UNKNOWN_COMMODITY_B":                                                            311,
		"COMMODITY_HAULED_UNKNOWN_COMMODITY_C":                                                            312,
		"COMMODITY_HAULED_UNKNOWN_COMMODITY_D":                                                            313,
		"COMMODITY_HAULED_UNKNOWN_COMMODITY_E":                                                            314,
		"COMMODITY_HAULED_UNKNOWN_COMMODITY_F":                                                            315,
		"COMMODITY_HAULED_URANIUM_ORE":                                                                    316,
		"COMMODITY_HAULED_URANIUM_OXIDE":                                                                  317,
		"COMMODITY_HAULED_VANS_CUSTOMIZED":                                                                318,
		"COMMODITY_HAULED_VEGETABLE_OIL":                                                                  319,
		"COMMODITY_HAULED_VELVET":                                                                         320,
		"COMMODITY_HAULED_VIDEO_GAMES":                                                                    321,
		"COMMODITY_HAULED_WASTE_WATER_LANDFILL":                                                           322,
		"COMMODITY_HAULED_WATER_WELL":                                                                     323,
		"COMMODITY_HAULED_WOOL":                                                                           324,
	}
)

func (x CommodityName) Enum() *CommodityName {
	p := new(CommodityName)
	*p = x
	return p
}

func (x CommodityName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommodityName) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[2].Descriptor()
}

func (CommodityName) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[2]
}

func (x CommodityName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommodityName.Descriptor instead.
func (CommodityName) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{2}
}

type RadiusOfOperationRange int32

const (
	RadiusOfOperationRange_RADIUS_OF_OPERATION_RANGE_UNSPECIFIED                 RadiusOfOperationRange = 0
	RadiusOfOperationRange_RADIUS_OF_OPERATION_RANGE_ZERO_TO_FIFTY               RadiusOfOperationRange = 1
	RadiusOfOperationRange_RADIUS_OF_OPERATION_RANGE_FIFTY_TO_TWO_HUNDRED        RadiusOfOperationRange = 2
	RadiusOfOperationRange_RADIUS_OF_OPERATION_RANGE_TWO_HUNDRED_TO_FIVE_HUNDRED RadiusOfOperationRange = 3
	RadiusOfOperationRange_RADIUS_OF_OPERATION_RANGE_FIVE_HUNDRED_PLUS           RadiusOfOperationRange = 4
)

// Enum value maps for RadiusOfOperationRange.
var (
	RadiusOfOperationRange_name = map[int32]string{
		0: "RADIUS_OF_OPERATION_RANGE_UNSPECIFIED",
		1: "RADIUS_OF_OPERATION_RANGE_ZERO_TO_FIFTY",
		2: "RADIUS_OF_OPERATION_RANGE_FIFTY_TO_TWO_HUNDRED",
		3: "RADIUS_OF_OPERATION_RANGE_TWO_HUNDRED_TO_FIVE_HUNDRED",
		4: "RADIUS_OF_OPERATION_RANGE_FIVE_HUNDRED_PLUS",
	}
	RadiusOfOperationRange_value = map[string]int32{
		"RADIUS_OF_OPERATION_RANGE_UNSPECIFIED":                 0,
		"RADIUS_OF_OPERATION_RANGE_ZERO_TO_FIFTY":               1,
		"RADIUS_OF_OPERATION_RANGE_FIFTY_TO_TWO_HUNDRED":        2,
		"RADIUS_OF_OPERATION_RANGE_TWO_HUNDRED_TO_FIVE_HUNDRED": 3,
		"RADIUS_OF_OPERATION_RANGE_FIVE_HUNDRED_PLUS":           4,
	}
)

func (x RadiusOfOperationRange) Enum() *RadiusOfOperationRange {
	p := new(RadiusOfOperationRange)
	*p = x
	return p
}

func (x RadiusOfOperationRange) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RadiusOfOperationRange) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[3].Descriptor()
}

func (RadiusOfOperationRange) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[3]
}

func (x RadiusOfOperationRange) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RadiusOfOperationRange.Descriptor instead.
func (RadiusOfOperationRange) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{3}
}

type VehicleType int32

const (
	VehicleType_VEHICLE_TYPE_UNSPECIFIED            VehicleType = 0
	VehicleType_VEHICLE_TYPE_TRACTOR                VehicleType = 1
	VehicleType_VEHICLE_TYPE_TRUCK                  VehicleType = 2
	VehicleType_VEHICLE_TYPE_TRAILER                VehicleType = 3
	VehicleType_VEHICLE_TYPE_SPARE_TRAILER          VehicleType = 4
	VehicleType_VEHICLE_TYPE_NON_OWNED_SEMI_TRAILER VehicleType = 5
)

// Enum value maps for VehicleType.
var (
	VehicleType_name = map[int32]string{
		0: "VEHICLE_TYPE_UNSPECIFIED",
		1: "VEHICLE_TYPE_TRACTOR",
		2: "VEHICLE_TYPE_TRUCK",
		3: "VEHICLE_TYPE_TRAILER",
		4: "VEHICLE_TYPE_SPARE_TRAILER",
		5: "VEHICLE_TYPE_NON_OWNED_SEMI_TRAILER",
	}
	VehicleType_value = map[string]int32{
		"VEHICLE_TYPE_UNSPECIFIED":            0,
		"VEHICLE_TYPE_TRACTOR":                1,
		"VEHICLE_TYPE_TRUCK":                  2,
		"VEHICLE_TYPE_TRAILER":                3,
		"VEHICLE_TYPE_SPARE_TRAILER":          4,
		"VEHICLE_TYPE_NON_OWNED_SEMI_TRAILER": 5,
	}
)

func (x VehicleType) Enum() *VehicleType {
	p := new(VehicleType)
	*p = x
	return p
}

func (x VehicleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleType) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[4].Descriptor()
}

func (VehicleType) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[4]
}

func (x VehicleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleType.Descriptor instead.
func (VehicleType) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{4}
}

type VehicleWeightClass int32

const (
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_UNSPECIFIED VehicleWeightClass = 0
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_LIGHT       VehicleWeightClass = 1
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_MEDIUM      VehicleWeightClass = 2
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_HEAVY       VehicleWeightClass = 3
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_EXTRA_HEAVY VehicleWeightClass = 4
	VehicleWeightClass_VEHICLE_WEIGHT_CLASS_TRAILER     VehicleWeightClass = 5
)

// Enum value maps for VehicleWeightClass.
var (
	VehicleWeightClass_name = map[int32]string{
		0: "VEHICLE_WEIGHT_CLASS_UNSPECIFIED",
		1: "VEHICLE_WEIGHT_CLASS_LIGHT",
		2: "VEHICLE_WEIGHT_CLASS_MEDIUM",
		3: "VEHICLE_WEIGHT_CLASS_HEAVY",
		4: "VEHICLE_WEIGHT_CLASS_EXTRA_HEAVY",
		5: "VEHICLE_WEIGHT_CLASS_TRAILER",
	}
	VehicleWeightClass_value = map[string]int32{
		"VEHICLE_WEIGHT_CLASS_UNSPECIFIED": 0,
		"VEHICLE_WEIGHT_CLASS_LIGHT":       1,
		"VEHICLE_WEIGHT_CLASS_MEDIUM":      2,
		"VEHICLE_WEIGHT_CLASS_HEAVY":       3,
		"VEHICLE_WEIGHT_CLASS_EXTRA_HEAVY": 4,
		"VEHICLE_WEIGHT_CLASS_TRAILER":     5,
	}
)

func (x VehicleWeightClass) Enum() *VehicleWeightClass {
	p := new(VehicleWeightClass)
	*p = x
	return p
}

func (x VehicleWeightClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleWeightClass) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[5].Descriptor()
}

func (VehicleWeightClass) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[5]
}

func (x VehicleWeightClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleWeightClass.Descriptor instead.
func (VehicleWeightClass) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{5}
}

type OperationClass int32

const (
	OperationClass_OPERATION_CLASS_UNSPECIFIED  OperationClass = 0
	OperationClass_OPERATION_CLASS_DRY_VAN      OperationClass = 1
	OperationClass_OPERATION_CLASS_REFRIGERATED OperationClass = 2
	OperationClass_OPERATION_CLASS_FLATBED      OperationClass = 3
	OperationClass_OPERATION_CLASS_INTERMODAL   OperationClass = 4
	OperationClass_OPERATION_CLASS_TANKER       OperationClass = 5
	OperationClass_OPERATION_CLASS_HAZMAT       OperationClass = 6
	OperationClass_OPERATION_CLASS_HEAVY_HAUL   OperationClass = 7
	OperationClass_OPERATION_CLASS_DUMP         OperationClass = 8
)

// Enum value maps for OperationClass.
var (
	OperationClass_name = map[int32]string{
		0: "OPERATION_CLASS_UNSPECIFIED",
		1: "OPERATION_CLASS_DRY_VAN",
		2: "OPERATION_CLASS_REFRIGERATED",
		3: "OPERATION_CLASS_FLATBED",
		4: "OPERATION_CLASS_INTERMODAL",
		5: "OPERATION_CLASS_TANKER",
		6: "OPERATION_CLASS_HAZMAT",
		7: "OPERATION_CLASS_HEAVY_HAUL",
		8: "OPERATION_CLASS_DUMP",
	}
	OperationClass_value = map[string]int32{
		"OPERATION_CLASS_UNSPECIFIED":  0,
		"OPERATION_CLASS_DRY_VAN":      1,
		"OPERATION_CLASS_REFRIGERATED": 2,
		"OPERATION_CLASS_FLATBED":      3,
		"OPERATION_CLASS_INTERMODAL":   4,
		"OPERATION_CLASS_TANKER":       5,
		"OPERATION_CLASS_HAZMAT":       6,
		"OPERATION_CLASS_HEAVY_HAUL":   7,
		"OPERATION_CLASS_DUMP":         8,
	}
)

func (x OperationClass) Enum() *OperationClass {
	p := new(OperationClass)
	*p = x
	return p
}

func (x OperationClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperationClass) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[6].Descriptor()
}

func (OperationClass) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[6]
}

func (x OperationClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperationClass.Descriptor instead.
func (OperationClass) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{6}
}

type TerminalPrivateTheftProtectionSystem int32

const (
	TerminalPrivateTheftProtectionSystem_TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_UNSPECIFIED                                            TerminalPrivateTheftProtectionSystem = 0
	TerminalPrivateTheftProtectionSystem_TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_NO_ALARM                                               TerminalPrivateTheftProtectionSystem = 1
	TerminalPrivateTheftProtectionSystem_TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_LOCAL_ALARM                                            TerminalPrivateTheftProtectionSystem = 2
	TerminalPrivateTheftProtectionSystem_TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_CENTRAL_STATION_ALARM                                  TerminalPrivateTheftProtectionSystem = 3
	TerminalPrivateTheftProtectionSystem_TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_CENTRAL_STATION_ALARM_AND_ADDITIONAL_THEFT_PROTECTIONS TerminalPrivateTheftProtectionSystem = 4
)

// Enum value maps for TerminalPrivateTheftProtectionSystem.
var (
	TerminalPrivateTheftProtectionSystem_name = map[int32]string{
		0: "TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_UNSPECIFIED",
		1: "TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_NO_ALARM",
		2: "TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_LOCAL_ALARM",
		3: "TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_CENTRAL_STATION_ALARM",
		4: "TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_CENTRAL_STATION_ALARM_AND_ADDITIONAL_THEFT_PROTECTIONS",
	}
	TerminalPrivateTheftProtectionSystem_value = map[string]int32{
		"TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_UNSPECIFIED":                                            0,
		"TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_NO_ALARM":                                               1,
		"TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_LOCAL_ALARM":                                            2,
		"TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_CENTRAL_STATION_ALARM":                                  3,
		"TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_CENTRAL_STATION_ALARM_AND_ADDITIONAL_THEFT_PROTECTIONS": 4,
	}
)

func (x TerminalPrivateTheftProtectionSystem) Enum() *TerminalPrivateTheftProtectionSystem {
	p := new(TerminalPrivateTheftProtectionSystem)
	*p = x
	return p
}

func (x TerminalPrivateTheftProtectionSystem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TerminalPrivateTheftProtectionSystem) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[7].Descriptor()
}

func (TerminalPrivateTheftProtectionSystem) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[7]
}

func (x TerminalPrivateTheftProtectionSystem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TerminalPrivateTheftProtectionSystem.Descriptor instead.
func (TerminalPrivateTheftProtectionSystem) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{7}
}

type TerminalPrivateFireProtectionSystem int32

const (
	TerminalPrivateFireProtectionSystem_TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_UNSPECIFIED                                      TerminalPrivateFireProtectionSystem = 0
	TerminalPrivateFireProtectionSystem_TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_NO_AUTOMATIC_FIRE_SUPPRESSION                    TerminalPrivateFireProtectionSystem = 1
	TerminalPrivateFireProtectionSystem_TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_INCOMPLETE_SUPPRESSION                           TerminalPrivateFireProtectionSystem = 2
	TerminalPrivateFireProtectionSystem_TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_COMPLETE_SUPPRESSION                             TerminalPrivateFireProtectionSystem = 3
	TerminalPrivateFireProtectionSystem_TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_AUTOMATIC_SUPPRESSION_AND_ADDITIONAL_PROTECTIONS TerminalPrivateFireProtectionSystem = 4
)

// Enum value maps for TerminalPrivateFireProtectionSystem.
var (
	TerminalPrivateFireProtectionSystem_name = map[int32]string{
		0: "TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_UNSPECIFIED",
		1: "TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_NO_AUTOMATIC_FIRE_SUPPRESSION",
		2: "TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_INCOMPLETE_SUPPRESSION",
		3: "TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_COMPLETE_SUPPRESSION",
		4: "TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_AUTOMATIC_SUPPRESSION_AND_ADDITIONAL_PROTECTIONS",
	}
	TerminalPrivateFireProtectionSystem_value = map[string]int32{
		"TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_UNSPECIFIED":                                      0,
		"TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_NO_AUTOMATIC_FIRE_SUPPRESSION":                    1,
		"TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_INCOMPLETE_SUPPRESSION":                           2,
		"TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_COMPLETE_SUPPRESSION":                             3,
		"TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_AUTOMATIC_SUPPRESSION_AND_ADDITIONAL_PROTECTIONS": 4,
	}
)

func (x TerminalPrivateFireProtectionSystem) Enum() *TerminalPrivateFireProtectionSystem {
	p := new(TerminalPrivateFireProtectionSystem)
	*p = x
	return p
}

func (x TerminalPrivateFireProtectionSystem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TerminalPrivateFireProtectionSystem) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[8].Descriptor()
}

func (TerminalPrivateFireProtectionSystem) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[8]
}

func (x TerminalPrivateFireProtectionSystem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TerminalPrivateFireProtectionSystem.Descriptor instead.
func (TerminalPrivateFireProtectionSystem) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{8}
}

type TerminalConstructionClass int32

const (
	TerminalConstructionClass_TERMINAL_CONSTRUCTION_CLASS_UNSPECIFIED              TerminalConstructionClass = 0
	TerminalConstructionClass_TERMINAL_CONSTRUCTION_CLASS_FRAME                    TerminalConstructionClass = 1
	TerminalConstructionClass_TERMINAL_CONSTRUCTION_CLASS_JOISTED_MASONRY          TerminalConstructionClass = 2
	TerminalConstructionClass_TERMINAL_CONSTRUCTION_CLASS_NON_COMBUSTIBLE          TerminalConstructionClass = 3
	TerminalConstructionClass_TERMINAL_CONSTRUCTION_CLASS_MASONRY_NON_COMBUSTIBLE  TerminalConstructionClass = 4
	TerminalConstructionClass_TERMINAL_CONSTRUCTION_CLASS_MODIFIED_FIRE_RESISTIVE  TerminalConstructionClass = 5
	TerminalConstructionClass_TERMINAL_CONSTRUCTION_CLASS_FIRE_RESISTIVE           TerminalConstructionClass = 6
	TerminalConstructionClass_TERMINAL_CONSTRUCTION_CLASS_NONE_OF_ABOVE_OR_UNKNOWN TerminalConstructionClass = 7
)

// Enum value maps for TerminalConstructionClass.
var (
	TerminalConstructionClass_name = map[int32]string{
		0: "TERMINAL_CONSTRUCTION_CLASS_UNSPECIFIED",
		1: "TERMINAL_CONSTRUCTION_CLASS_FRAME",
		2: "TERMINAL_CONSTRUCTION_CLASS_JOISTED_MASONRY",
		3: "TERMINAL_CONSTRUCTION_CLASS_NON_COMBUSTIBLE",
		4: "TERMINAL_CONSTRUCTION_CLASS_MASONRY_NON_COMBUSTIBLE",
		5: "TERMINAL_CONSTRUCTION_CLASS_MODIFIED_FIRE_RESISTIVE",
		6: "TERMINAL_CONSTRUCTION_CLASS_FIRE_RESISTIVE",
		7: "TERMINAL_CONSTRUCTION_CLASS_NONE_OF_ABOVE_OR_UNKNOWN",
	}
	TerminalConstructionClass_value = map[string]int32{
		"TERMINAL_CONSTRUCTION_CLASS_UNSPECIFIED":              0,
		"TERMINAL_CONSTRUCTION_CLASS_FRAME":                    1,
		"TERMINAL_CONSTRUCTION_CLASS_JOISTED_MASONRY":          2,
		"TERMINAL_CONSTRUCTION_CLASS_NON_COMBUSTIBLE":          3,
		"TERMINAL_CONSTRUCTION_CLASS_MASONRY_NON_COMBUSTIBLE":  4,
		"TERMINAL_CONSTRUCTION_CLASS_MODIFIED_FIRE_RESISTIVE":  5,
		"TERMINAL_CONSTRUCTION_CLASS_FIRE_RESISTIVE":           6,
		"TERMINAL_CONSTRUCTION_CLASS_NONE_OF_ABOVE_OR_UNKNOWN": 7,
	}
)

func (x TerminalConstructionClass) Enum() *TerminalConstructionClass {
	p := new(TerminalConstructionClass)
	*p = x
	return p
}

func (x TerminalConstructionClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TerminalConstructionClass) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[9].Descriptor()
}

func (TerminalConstructionClass) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[9]
}

func (x TerminalConstructionClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TerminalConstructionClass.Descriptor instead.
func (TerminalConstructionClass) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{9}
}

type TerminalPublicProtectionClass int32

const (
	TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_UNSPECIFIED TerminalPublicProtectionClass = 0
	TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_1           TerminalPublicProtectionClass = 1
	TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_2           TerminalPublicProtectionClass = 2
	TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_3           TerminalPublicProtectionClass = 3
	TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_4           TerminalPublicProtectionClass = 4
	TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_5           TerminalPublicProtectionClass = 5
	TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_6           TerminalPublicProtectionClass = 6
	TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_7           TerminalPublicProtectionClass = 7
	TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_8           TerminalPublicProtectionClass = 8
	TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_9           TerminalPublicProtectionClass = 9
	TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_10          TerminalPublicProtectionClass = 10
)

// Enum value maps for TerminalPublicProtectionClass.
var (
	TerminalPublicProtectionClass_name = map[int32]string{
		0:  "TERMINAL_PUBLIC_PROTECTION_CLASS_UNSPECIFIED",
		1:  "TERMINAL_PUBLIC_PROTECTION_CLASS_1",
		2:  "TERMINAL_PUBLIC_PROTECTION_CLASS_2",
		3:  "TERMINAL_PUBLIC_PROTECTION_CLASS_3",
		4:  "TERMINAL_PUBLIC_PROTECTION_CLASS_4",
		5:  "TERMINAL_PUBLIC_PROTECTION_CLASS_5",
		6:  "TERMINAL_PUBLIC_PROTECTION_CLASS_6",
		7:  "TERMINAL_PUBLIC_PROTECTION_CLASS_7",
		8:  "TERMINAL_PUBLIC_PROTECTION_CLASS_8",
		9:  "TERMINAL_PUBLIC_PROTECTION_CLASS_9",
		10: "TERMINAL_PUBLIC_PROTECTION_CLASS_10",
	}
	TerminalPublicProtectionClass_value = map[string]int32{
		"TERMINAL_PUBLIC_PROTECTION_CLASS_UNSPECIFIED": 0,
		"TERMINAL_PUBLIC_PROTECTION_CLASS_1":           1,
		"TERMINAL_PUBLIC_PROTECTION_CLASS_2":           2,
		"TERMINAL_PUBLIC_PROTECTION_CLASS_3":           3,
		"TERMINAL_PUBLIC_PROTECTION_CLASS_4":           4,
		"TERMINAL_PUBLIC_PROTECTION_CLASS_5":           5,
		"TERMINAL_PUBLIC_PROTECTION_CLASS_6":           6,
		"TERMINAL_PUBLIC_PROTECTION_CLASS_7":           7,
		"TERMINAL_PUBLIC_PROTECTION_CLASS_8":           8,
		"TERMINAL_PUBLIC_PROTECTION_CLASS_9":           9,
		"TERMINAL_PUBLIC_PROTECTION_CLASS_10":          10,
	}
)

func (x TerminalPublicProtectionClass) Enum() *TerminalPublicProtectionClass {
	p := new(TerminalPublicProtectionClass)
	*p = x
	return p
}

func (x TerminalPublicProtectionClass) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TerminalPublicProtectionClass) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[10].Descriptor()
}

func (TerminalPublicProtectionClass) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[10]
}

func (x TerminalPublicProtectionClass) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TerminalPublicProtectionClass.Descriptor instead.
func (TerminalPublicProtectionClass) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{10}
}

type NegotiatedRatesExemption int32

const (
	NegotiatedRatesExemption_NEGOTIATED_RATES_EXEMPTION_UNSPECIFIED     NegotiatedRatesExemption = 0
	NegotiatedRatesExemption_NEGOTIATED_RATES_EXEMPTION_FIFTEEN         NegotiatedRatesExemption = 1
	NegotiatedRatesExemption_NEGOTIATED_RATES_EXEMPTION_LARGE_FLEET     NegotiatedRatesExemption = 2
	NegotiatedRatesExemption_NEGOTIATED_RATES_EXEMPTION_CONSENT_TO_RATE NegotiatedRatesExemption = 3
)

// Enum value maps for NegotiatedRatesExemption.
var (
	NegotiatedRatesExemption_name = map[int32]string{
		0: "NEGOTIATED_RATES_EXEMPTION_UNSPECIFIED",
		1: "NEGOTIATED_RATES_EXEMPTION_FIFTEEN",
		2: "NEGOTIATED_RATES_EXEMPTION_LARGE_FLEET",
		3: "NEGOTIATED_RATES_EXEMPTION_CONSENT_TO_RATE",
	}
	NegotiatedRatesExemption_value = map[string]int32{
		"NEGOTIATED_RATES_EXEMPTION_UNSPECIFIED":     0,
		"NEGOTIATED_RATES_EXEMPTION_FIFTEEN":         1,
		"NEGOTIATED_RATES_EXEMPTION_LARGE_FLEET":     2,
		"NEGOTIATED_RATES_EXEMPTION_CONSENT_TO_RATE": 3,
	}
)

func (x NegotiatedRatesExemption) Enum() *NegotiatedRatesExemption {
	p := new(NegotiatedRatesExemption)
	*p = x
	return p
}

func (x NegotiatedRatesExemption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NegotiatedRatesExemption) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[11].Descriptor()
}

func (NegotiatedRatesExemption) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[11]
}

func (x NegotiatedRatesExemption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NegotiatedRatesExemption.Descriptor instead.
func (NegotiatedRatesExemption) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{11}
}

type LicenseHolderType int32

const (
	LicenseHolderType_LICENSE_HOLDER_TYPE_UNSPECIFIED LicenseHolderType = 0
	LicenseHolderType_LICENSE_HOLDER_TYPE_AGENCY      LicenseHolderType = 1
	LicenseHolderType_LICENSE_HOLDER_TYPE_AGENT       LicenseHolderType = 2
)

// Enum value maps for LicenseHolderType.
var (
	LicenseHolderType_name = map[int32]string{
		0: "LICENSE_HOLDER_TYPE_UNSPECIFIED",
		1: "LICENSE_HOLDER_TYPE_AGENCY",
		2: "LICENSE_HOLDER_TYPE_AGENT",
	}
	LicenseHolderType_value = map[string]int32{
		"LICENSE_HOLDER_TYPE_UNSPECIFIED": 0,
		"LICENSE_HOLDER_TYPE_AGENCY":      1,
		"LICENSE_HOLDER_TYPE_AGENT":       2,
	}
)

func (x LicenseHolderType) Enum() *LicenseHolderType {
	p := new(LicenseHolderType)
	*p = x
	return p
}

func (x LicenseHolderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LicenseHolderType) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[12].Descriptor()
}

func (LicenseHolderType) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[12]
}

func (x LicenseHolderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LicenseHolderType.Descriptor instead.
func (LicenseHolderType) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{12}
}

type PaymentFrequency int32

const (
	PaymentFrequency_PAYMENT_FREQUENCY_UNSPECIFIED      PaymentFrequency = 0
	PaymentFrequency_PAYMENT_FREQUENCY_MONTHLY_REPORTER PaymentFrequency = 1
	PaymentFrequency_PAYMENT_FREQUENCY_PAID_IN_FULL     PaymentFrequency = 2
)

// Enum value maps for PaymentFrequency.
var (
	PaymentFrequency_name = map[int32]string{
		0: "PAYMENT_FREQUENCY_UNSPECIFIED",
		1: "PAYMENT_FREQUENCY_MONTHLY_REPORTER",
		2: "PAYMENT_FREQUENCY_PAID_IN_FULL",
	}
	PaymentFrequency_value = map[string]int32{
		"PAYMENT_FREQUENCY_UNSPECIFIED":      0,
		"PAYMENT_FREQUENCY_MONTHLY_REPORTER": 1,
		"PAYMENT_FREQUENCY_PAID_IN_FULL":     2,
	}
)

func (x PaymentFrequency) Enum() *PaymentFrequency {
	p := new(PaymentFrequency)
	*p = x
	return p
}

func (x PaymentFrequency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentFrequency) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[13].Descriptor()
}

func (PaymentFrequency) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[13]
}

func (x PaymentFrequency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentFrequency.Descriptor instead.
func (PaymentFrequency) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{13}
}

type DepositPaymentMethod int32

const (
	DepositPaymentMethod_DEPOSIT_PAYMENT_METHOD_UNSPECIFIED      DepositPaymentMethod = 0
	DepositPaymentMethod_DEPOSIT_PAYMENT_METHOD_INVOICE_CASH     DepositPaymentMethod = 1
	DepositPaymentMethod_DEPOSIT_PAYMENT_METHOD_LETTER_OF_CREDIT DepositPaymentMethod = 2
)

// Enum value maps for DepositPaymentMethod.
var (
	DepositPaymentMethod_name = map[int32]string{
		0: "DEPOSIT_PAYMENT_METHOD_UNSPECIFIED",
		1: "DEPOSIT_PAYMENT_METHOD_INVOICE_CASH",
		2: "DEPOSIT_PAYMENT_METHOD_LETTER_OF_CREDIT",
	}
	DepositPaymentMethod_value = map[string]int32{
		"DEPOSIT_PAYMENT_METHOD_UNSPECIFIED":      0,
		"DEPOSIT_PAYMENT_METHOD_INVOICE_CASH":     1,
		"DEPOSIT_PAYMENT_METHOD_LETTER_OF_CREDIT": 2,
	}
)

func (x DepositPaymentMethod) Enum() *DepositPaymentMethod {
	p := new(DepositPaymentMethod)
	*p = x
	return p
}

func (x DepositPaymentMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DepositPaymentMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[14].Descriptor()
}

func (DepositPaymentMethod) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[14]
}

func (x DepositPaymentMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DepositPaymentMethod.Descriptor instead.
func (DepositPaymentMethod) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{14}
}

type AdditionalCommodityType int32

const (
	AdditionalCommodityType_ADDITIONAL_COMMODITY_TYPE_UNSPECIFIED                      AdditionalCommodityType = 0
	AdditionalCommodityType_ADDITIONAL_COMMODITY_TYPE_HAZARDOUS_MATERIALS_INCL_CLASS_9 AdditionalCommodityType = 1
	AdditionalCommodityType_ADDITIONAL_COMMODITY_TYPE_LIFT_GATE_OR_WHITE_GLOVE_SERVICE AdditionalCommodityType = 2
	AdditionalCommodityType_ADDITIONAL_COMMODITY_TYPE_RESIDENTIAL_DELIVERY             AdditionalCommodityType = 3
	AdditionalCommodityType_ADDITIONAL_COMMODITY_TYPE_DOUBLE_OR_TRIPLE_TRAILERS        AdditionalCommodityType = 4
	AdditionalCommodityType_ADDITIONAL_COMMODITY_TYPE_MEAT_ON_HOOK                     AdditionalCommodityType = 5
)

// Enum value maps for AdditionalCommodityType.
var (
	AdditionalCommodityType_name = map[int32]string{
		0: "ADDITIONAL_COMMODITY_TYPE_UNSPECIFIED",
		1: "ADDITIONAL_COMMODITY_TYPE_HAZARDOUS_MATERIALS_INCL_CLASS_9",
		2: "ADDITIONAL_COMMODITY_TYPE_LIFT_GATE_OR_WHITE_GLOVE_SERVICE",
		3: "ADDITIONAL_COMMODITY_TYPE_RESIDENTIAL_DELIVERY",
		4: "ADDITIONAL_COMMODITY_TYPE_DOUBLE_OR_TRIPLE_TRAILERS",
		5: "ADDITIONAL_COMMODITY_TYPE_MEAT_ON_HOOK",
	}
	AdditionalCommodityType_value = map[string]int32{
		"ADDITIONAL_COMMODITY_TYPE_UNSPECIFIED":                      0,
		"ADDITIONAL_COMMODITY_TYPE_HAZARDOUS_MATERIALS_INCL_CLASS_9": 1,
		"ADDITIONAL_COMMODITY_TYPE_LIFT_GATE_OR_WHITE_GLOVE_SERVICE": 2,
		"ADDITIONAL_COMMODITY_TYPE_RESIDENTIAL_DELIVERY":             3,
		"ADDITIONAL_COMMODITY_TYPE_DOUBLE_OR_TRIPLE_TRAILERS":        4,
		"ADDITIONAL_COMMODITY_TYPE_MEAT_ON_HOOK":                     5,
	}
)

func (x AdditionalCommodityType) Enum() *AdditionalCommodityType {
	p := new(AdditionalCommodityType)
	*p = x
	return p
}

func (x AdditionalCommodityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdditionalCommodityType) Descriptor() protoreflect.EnumDescriptor {
	return file_fleet_model_program_data_proto_enumTypes[15].Descriptor()
}

func (AdditionalCommodityType) Type() protoreflect.EnumType {
	return &file_fleet_model_program_data_proto_enumTypes[15]
}

func (x AdditionalCommodityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdditionalCommodityType.Descriptor instead.
func (AdditionalCommodityType) EnumDescriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{15}
}

type FleetProgramData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Company                             *Company              `protobuf:"bytes,1,opt,name=company,proto3" json:"company,omitempty"`
	Operation                           *Operation            `protobuf:"bytes,2,opt,name=operation,proto3" json:"operation,omitempty"`
	Drivers                             []*Driver             `protobuf:"bytes,3,rep,name=drivers,proto3" json:"drivers,omitempty"`
	Equipment                           *Equipment            `protobuf:"bytes,4,opt,name=equipment,proto3" json:"equipment,omitempty"`
	LossHistory                         *LossHistory          `protobuf:"bytes,5,opt,name=lossHistory,proto3" json:"lossHistory,omitempty"`
	NegotiatedRates                     *NegotiatedRates      `protobuf:"bytes,6,opt,name=negotiatedRates,proto3" json:"negotiatedRates,omitempty"`
	BrokerLicense                       *BrokerLicense        `protobuf:"bytes,7,opt,name=brokerLicense,proto3" json:"brokerLicense,omitempty"`
	CameraSubsidy                       *CameraSubsidy        `protobuf:"bytes,8,opt,name=cameraSubsidy,proto3" json:"cameraSubsidy,omitempty"`
	Billing                             *Billing              `protobuf:"bytes,9,opt,name=billing,proto3" json:"billing,omitempty"`
	IsMinimumMileageGuaranteeApplicable bool                  `protobuf:"varint,10,opt,name=isMinimumMileageGuaranteeApplicable,proto3" json:"isMinimumMileageGuaranteeApplicable,omitempty"`
	ScheduleModification                *ScheduleModification `protobuf:"bytes,11,opt,name=scheduleModification,proto3" json:"scheduleModification,omitempty"`
	CommodityDetails                    *CommodityDetails     `protobuf:"bytes,12,opt,name=commodityDetails,proto3" json:"commodityDetails,omitempty"`
}

func (x *FleetProgramData) Reset() {
	*x = FleetProgramData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetProgramData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetProgramData) ProtoMessage() {}

func (x *FleetProgramData) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetProgramData.ProtoReflect.Descriptor instead.
func (*FleetProgramData) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{0}
}

func (x *FleetProgramData) GetCompany() *Company {
	if x != nil {
		return x.Company
	}
	return nil
}

func (x *FleetProgramData) GetOperation() *Operation {
	if x != nil {
		return x.Operation
	}
	return nil
}

func (x *FleetProgramData) GetDrivers() []*Driver {
	if x != nil {
		return x.Drivers
	}
	return nil
}

func (x *FleetProgramData) GetEquipment() *Equipment {
	if x != nil {
		return x.Equipment
	}
	return nil
}

func (x *FleetProgramData) GetLossHistory() *LossHistory {
	if x != nil {
		return x.LossHistory
	}
	return nil
}

func (x *FleetProgramData) GetNegotiatedRates() *NegotiatedRates {
	if x != nil {
		return x.NegotiatedRates
	}
	return nil
}

func (x *FleetProgramData) GetBrokerLicense() *BrokerLicense {
	if x != nil {
		return x.BrokerLicense
	}
	return nil
}

func (x *FleetProgramData) GetCameraSubsidy() *CameraSubsidy {
	if x != nil {
		return x.CameraSubsidy
	}
	return nil
}

func (x *FleetProgramData) GetBilling() *Billing {
	if x != nil {
		return x.Billing
	}
	return nil
}

func (x *FleetProgramData) GetIsMinimumMileageGuaranteeApplicable() bool {
	if x != nil {
		return x.IsMinimumMileageGuaranteeApplicable
	}
	return false
}

func (x *FleetProgramData) GetScheduleModification() *ScheduleModification {
	if x != nil {
		return x.ScheduleModification
	}
	return nil
}

func (x *FleetProgramData) GetCommodityDetails() *CommodityDetails {
	if x != nil {
		return x.CommodityDetails
	}
	return nil
}

type Company struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DoingBusinessAs   *string             `protobuf:"bytes,1,opt,name=doingBusinessAs,proto3,oneof" json:"doingBusinessAs,omitempty"`
	UsState           string              `protobuf:"bytes,2,opt,name=usState,proto3" json:"usState,omitempty"`
	TerminalLocations []*TerminalLocation `protobuf:"bytes,3,rep,name=terminalLocations,proto3" json:"terminalLocations,omitempty"`
	MailingAddress    *proto.Address      `protobuf:"bytes,4,opt,name=mailingAddress,proto3" json:"mailingAddress,omitempty"`
	TaxRecords        []*TaxRecord        `protobuf:"bytes,5,rep,name=taxRecords,proto3" json:"taxRecords,omitempty"`
}

func (x *Company) Reset() {
	*x = Company{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Company) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Company) ProtoMessage() {}

func (x *Company) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Company.ProtoReflect.Descriptor instead.
func (*Company) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{1}
}

func (x *Company) GetDoingBusinessAs() string {
	if x != nil && x.DoingBusinessAs != nil {
		return *x.DoingBusinessAs
	}
	return ""
}

func (x *Company) GetUsState() string {
	if x != nil {
		return x.UsState
	}
	return ""
}

func (x *Company) GetTerminalLocations() []*TerminalLocation {
	if x != nil {
		return x.TerminalLocations
	}
	return nil
}

func (x *Company) GetMailingAddress() *proto.Address {
	if x != nil {
		return x.MailingAddress
	}
	return nil
}

func (x *Company) GetTaxRecords() []*TaxRecord {
	if x != nil {
		return x.TaxRecords
	}
	return nil
}

type TerminalLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address                *proto.Address                        `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	IsGated                bool                                  `protobuf:"varint,2,opt,name=isGated,proto3" json:"isGated,omitempty"`
	IsGuarded              bool                                  `protobuf:"varint,3,opt,name=isGuarded,proto3" json:"isGuarded,omitempty"`
	TypeOfTerminal         TypeOfTerminal                        `protobuf:"varint,4,opt,name=typeOfTerminal,proto3,enum=fleet_model.TypeOfTerminal" json:"typeOfTerminal,omitempty"`
	IsRatingAddress        bool                                  `protobuf:"varint,5,opt,name=isRatingAddress,proto3" json:"isRatingAddress,omitempty"`
	ConstructionClass      *TerminalConstructionClass            `protobuf:"varint,6,opt,name=constructionClass,proto3,enum=fleet_model.TerminalConstructionClass,oneof" json:"constructionClass,omitempty"`
	PublicProtectionClass  *TerminalPublicProtectionClass        `protobuf:"varint,7,opt,name=publicProtectionClass,proto3,enum=fleet_model.TerminalPublicProtectionClass,oneof" json:"publicProtectionClass,omitempty"`
	PrivateTheftProtection *TerminalPrivateTheftProtectionSystem `protobuf:"varint,8,opt,name=privateTheftProtection,proto3,enum=fleet_model.TerminalPrivateTheftProtectionSystem,oneof" json:"privateTheftProtection,omitempty"`
	PrivateFireProtection  *TerminalPrivateFireProtectionSystem  `protobuf:"varint,9,opt,name=privateFireProtection,proto3,enum=fleet_model.TerminalPrivateFireProtectionSystem,oneof" json:"privateFireProtection,omitempty"`
}

func (x *TerminalLocation) Reset() {
	*x = TerminalLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TerminalLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalLocation) ProtoMessage() {}

func (x *TerminalLocation) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalLocation.ProtoReflect.Descriptor instead.
func (*TerminalLocation) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{2}
}

func (x *TerminalLocation) GetAddress() *proto.Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *TerminalLocation) GetIsGated() bool {
	if x != nil {
		return x.IsGated
	}
	return false
}

func (x *TerminalLocation) GetIsGuarded() bool {
	if x != nil {
		return x.IsGuarded
	}
	return false
}

func (x *TerminalLocation) GetTypeOfTerminal() TypeOfTerminal {
	if x != nil {
		return x.TypeOfTerminal
	}
	return TypeOfTerminal_TYPE_OF_TERMINAL_UNSPECIFIED
}

func (x *TerminalLocation) GetIsRatingAddress() bool {
	if x != nil {
		return x.IsRatingAddress
	}
	return false
}

func (x *TerminalLocation) GetConstructionClass() TerminalConstructionClass {
	if x != nil && x.ConstructionClass != nil {
		return *x.ConstructionClass
	}
	return TerminalConstructionClass_TERMINAL_CONSTRUCTION_CLASS_UNSPECIFIED
}

func (x *TerminalLocation) GetPublicProtectionClass() TerminalPublicProtectionClass {
	if x != nil && x.PublicProtectionClass != nil {
		return *x.PublicProtectionClass
	}
	return TerminalPublicProtectionClass_TERMINAL_PUBLIC_PROTECTION_CLASS_UNSPECIFIED
}

func (x *TerminalLocation) GetPrivateTheftProtection() TerminalPrivateTheftProtectionSystem {
	if x != nil && x.PrivateTheftProtection != nil {
		return *x.PrivateTheftProtection
	}
	return TerminalPrivateTheftProtectionSystem_TERMINAL_PRIVATE_THEFT_PROTECTION_SYSTEM_UNSPECIFIED
}

func (x *TerminalLocation) GetPrivateFireProtection() TerminalPrivateFireProtectionSystem {
	if x != nil && x.PrivateFireProtection != nil {
		return *x.PrivateFireProtection
	}
	return TerminalPrivateFireProtectionSystem_TERMINAL_PRIVATE_FIRE_PROTECTION_SYSTEM_UNSPECIFIED
}

type Operation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrimaryOperationClass    OperationClass             `protobuf:"varint,1,opt,name=primaryOperationClass,proto3,enum=fleet_model.OperationClass" json:"primaryOperationClass,omitempty"`
	OperationClassRecords    []*OperationClassRecord    `protobuf:"bytes,2,rep,name=operationClassRecords,proto3" json:"operationClassRecords,omitempty"`
	RadiusOfOperationRecords []*RadiusOfOperationRecord `protobuf:"bytes,3,rep,name=radiusOfOperationRecords,proto3" json:"radiusOfOperationRecords,omitempty"`
	VehicleZoneRecords       []*VehicleZoneRecord       `protobuf:"bytes,4,rep,name=vehicleZoneRecords,proto3" json:"vehicleZoneRecords,omitempty"`
	PercentageOfSubHaul      *float64                   `protobuf:"fixed64,5,opt,name=percentageOfSubHaul,proto3,oneof" json:"percentageOfSubHaul,omitempty"`
}

func (x *Operation) Reset() {
	*x = Operation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Operation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operation) ProtoMessage() {}

func (x *Operation) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operation.ProtoReflect.Descriptor instead.
func (*Operation) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{3}
}

func (x *Operation) GetPrimaryOperationClass() OperationClass {
	if x != nil {
		return x.PrimaryOperationClass
	}
	return OperationClass_OPERATION_CLASS_UNSPECIFIED
}

func (x *Operation) GetOperationClassRecords() []*OperationClassRecord {
	if x != nil {
		return x.OperationClassRecords
	}
	return nil
}

func (x *Operation) GetRadiusOfOperationRecords() []*RadiusOfOperationRecord {
	if x != nil {
		return x.RadiusOfOperationRecords
	}
	return nil
}

func (x *Operation) GetVehicleZoneRecords() []*VehicleZoneRecord {
	if x != nil {
		return x.VehicleZoneRecords
	}
	return nil
}

func (x *Operation) GetPercentageOfSubHaul() float64 {
	if x != nil && x.PercentageOfSubHaul != nil {
		return *x.PercentageOfSubHaul
	}
	return 0
}

type OperationClassRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OperationClass OperationClass `protobuf:"varint,1,opt,name=operationClass,proto3,enum=fleet_model.OperationClass" json:"operationClass,omitempty"`
	Percentage     int32          `protobuf:"varint,2,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *OperationClassRecord) Reset() {
	*x = OperationClassRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationClassRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationClassRecord) ProtoMessage() {}

func (x *OperationClassRecord) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationClassRecord.ProtoReflect.Descriptor instead.
func (*OperationClassRecord) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{4}
}

func (x *OperationClassRecord) GetOperationClass() OperationClass {
	if x != nil {
		return x.OperationClass
	}
	return OperationClass_OPERATION_CLASS_UNSPECIFIED
}

func (x *OperationClassRecord) GetPercentage() int32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type RadiusOfOperationRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RadiusOfOperationRange RadiusOfOperationRange `protobuf:"varint,1,opt,name=radiusOfOperationRange,proto3,enum=fleet_model.RadiusOfOperationRange" json:"radiusOfOperationRange,omitempty"`
	Percentage             int32                  `protobuf:"varint,2,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *RadiusOfOperationRecord) Reset() {
	*x = RadiusOfOperationRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RadiusOfOperationRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RadiusOfOperationRecord) ProtoMessage() {}

func (x *RadiusOfOperationRecord) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RadiusOfOperationRecord.ProtoReflect.Descriptor instead.
func (*RadiusOfOperationRecord) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{5}
}

func (x *RadiusOfOperationRecord) GetRadiusOfOperationRange() RadiusOfOperationRange {
	if x != nil {
		return x.RadiusOfOperationRange
	}
	return RadiusOfOperationRange_RADIUS_OF_OPERATION_RANGE_UNSPECIFIED
}

func (x *RadiusOfOperationRecord) GetPercentage() int32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type VehicleZoneRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartZone  int32 `protobuf:"varint,1,opt,name=startZone,proto3" json:"startZone,omitempty"`
	EndZone    int32 `protobuf:"varint,2,opt,name=endZone,proto3" json:"endZone,omitempty"`
	Percentage int32 `protobuf:"varint,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
}

func (x *VehicleZoneRecord) Reset() {
	*x = VehicleZoneRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleZoneRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleZoneRecord) ProtoMessage() {}

func (x *VehicleZoneRecord) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleZoneRecord.ProtoReflect.Descriptor instead.
func (*VehicleZoneRecord) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{6}
}

func (x *VehicleZoneRecord) GetStartZone() int32 {
	if x != nil {
		return x.StartZone
	}
	return 0
}

func (x *VehicleZoneRecord) GetEndZone() int32 {
	if x != nil {
		return x.EndZone
	}
	return 0
}

func (x *VehicleZoneRecord) GetPercentage() int32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type TaxRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JurisdictionType string                     `protobuf:"bytes,1,opt,name=jurisdictionType,proto3" json:"jurisdictionType,omitempty"`
	JurisdictionName string                     `protobuf:"bytes,2,opt,name=jurisdictionName,proto3" json:"jurisdictionName,omitempty"`
	TaxCode          string                     `protobuf:"bytes,3,opt,name=taxCode,proto3" json:"taxCode,omitempty"`
	LinesOfBusiness  []*TaxRecordLineOfBusiness `protobuf:"bytes,4,rep,name=linesOfBusiness,proto3" json:"linesOfBusiness,omitempty"`
}

func (x *TaxRecord) Reset() {
	*x = TaxRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaxRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaxRecord) ProtoMessage() {}

func (x *TaxRecord) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaxRecord.ProtoReflect.Descriptor instead.
func (*TaxRecord) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{7}
}

func (x *TaxRecord) GetJurisdictionType() string {
	if x != nil {
		return x.JurisdictionType
	}
	return ""
}

func (x *TaxRecord) GetJurisdictionName() string {
	if x != nil {
		return x.JurisdictionName
	}
	return ""
}

func (x *TaxRecord) GetTaxCode() string {
	if x != nil {
		return x.TaxCode
	}
	return ""
}

func (x *TaxRecord) GetLinesOfBusiness() []*TaxRecordLineOfBusiness {
	if x != nil {
		return x.LinesOfBusiness
	}
	return nil
}

type TaxRecordLineOfBusiness struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Coverage string `protobuf:"bytes,1,opt,name=coverage,proto3" json:"coverage,omitempty"`
	TaxValue string `protobuf:"bytes,2,opt,name=taxValue,proto3" json:"taxValue,omitempty"`
}

func (x *TaxRecordLineOfBusiness) Reset() {
	*x = TaxRecordLineOfBusiness{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaxRecordLineOfBusiness) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaxRecordLineOfBusiness) ProtoMessage() {}

func (x *TaxRecordLineOfBusiness) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaxRecordLineOfBusiness.ProtoReflect.Descriptor instead.
func (*TaxRecordLineOfBusiness) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{8}
}

func (x *TaxRecordLineOfBusiness) GetCoverage() string {
	if x != nil {
		return x.Coverage
	}
	return ""
}

func (x *TaxRecordLineOfBusiness) GetTaxValue() string {
	if x != nil {
		return x.TaxValue
	}
	return ""
}

type BrokerLicense struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                       string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	LicenseHolderType        LicenseHolderType `protobuf:"varint,2,opt,name=licenseHolderType,proto3,enum=fleet_model.LicenseHolderType" json:"licenseHolderType,omitempty"`
	LicenseNumber            string            `protobuf:"bytes,3,opt,name=licenseNumber,proto3" json:"licenseNumber,omitempty"`
	Address                  *proto.Address    `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	LicenseEffectiveInterval *proto.Interval   `protobuf:"bytes,5,opt,name=licenseEffectiveInterval,proto3" json:"licenseEffectiveInterval,omitempty"`
}

func (x *BrokerLicense) Reset() {
	*x = BrokerLicense{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrokerLicense) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrokerLicense) ProtoMessage() {}

func (x *BrokerLicense) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrokerLicense.ProtoReflect.Descriptor instead.
func (*BrokerLicense) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{9}
}

func (x *BrokerLicense) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BrokerLicense) GetLicenseHolderType() LicenseHolderType {
	if x != nil {
		return x.LicenseHolderType
	}
	return LicenseHolderType_LICENSE_HOLDER_TYPE_UNSPECIFIED
}

func (x *BrokerLicense) GetLicenseNumber() string {
	if x != nil {
		return x.LicenseNumber
	}
	return ""
}

func (x *BrokerLicense) GetAddress() *proto.Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *BrokerLicense) GetLicenseEffectiveInterval() *proto.Interval {
	if x != nil {
		return x.LicenseEffectiveInterval
	}
	return nil
}

type Billing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepositAmount           *float64             `protobuf:"fixed64,1,opt,name=depositAmount,proto3,oneof" json:"depositAmount,omitempty"`
	AgencyCommissionRate    *float64             `protobuf:"fixed64,2,opt,name=agencyCommissionRate,proto3,oneof" json:"agencyCommissionRate,omitempty"`
	PaymentFrequency        PaymentFrequency     `protobuf:"varint,3,opt,name=paymentFrequency,proto3,enum=fleet_model.PaymentFrequency" json:"paymentFrequency,omitempty"`
	DepositPaymentMethod    DepositPaymentMethod `protobuf:"varint,4,opt,name=depositPaymentMethod,proto3,enum=fleet_model.DepositPaymentMethod" json:"depositPaymentMethod,omitempty"`
	AllowedCollateralAmount *float64             `protobuf:"fixed64,5,opt,name=allowedCollateralAmount,proto3,oneof" json:"allowedCollateralAmount,omitempty"`
}

func (x *Billing) Reset() {
	*x = Billing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Billing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Billing) ProtoMessage() {}

func (x *Billing) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Billing.ProtoReflect.Descriptor instead.
func (*Billing) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{10}
}

func (x *Billing) GetDepositAmount() float64 {
	if x != nil && x.DepositAmount != nil {
		return *x.DepositAmount
	}
	return 0
}

func (x *Billing) GetAgencyCommissionRate() float64 {
	if x != nil && x.AgencyCommissionRate != nil {
		return *x.AgencyCommissionRate
	}
	return 0
}

func (x *Billing) GetPaymentFrequency() PaymentFrequency {
	if x != nil {
		return x.PaymentFrequency
	}
	return PaymentFrequency_PAYMENT_FREQUENCY_UNSPECIFIED
}

func (x *Billing) GetDepositPaymentMethod() DepositPaymentMethod {
	if x != nil {
		return x.DepositPaymentMethod
	}
	return DepositPaymentMethod_DEPOSIT_PAYMENT_METHOD_UNSPECIFIED
}

func (x *Billing) GetAllowedCollateralAmount() float64 {
	if x != nil && x.AllowedCollateralAmount != nil {
		return *x.AllowedCollateralAmount
	}
	return 0
}

type CameraSubsidy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumberOfCameras int32    `protobuf:"varint,1,opt,name=numberOfCameras,proto3" json:"numberOfCameras,omitempty"`
	SubsidyAmount   *float64 `protobuf:"fixed64,2,opt,name=subsidyAmount,proto3,oneof" json:"subsidyAmount,omitempty"`
	CameraProvider  string   `protobuf:"bytes,3,opt,name=cameraProvider,proto3" json:"cameraProvider,omitempty"`
}

func (x *CameraSubsidy) Reset() {
	*x = CameraSubsidy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraSubsidy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraSubsidy) ProtoMessage() {}

func (x *CameraSubsidy) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraSubsidy.ProtoReflect.Descriptor instead.
func (*CameraSubsidy) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{11}
}

func (x *CameraSubsidy) GetNumberOfCameras() int32 {
	if x != nil {
		return x.NumberOfCameras
	}
	return 0
}

func (x *CameraSubsidy) GetSubsidyAmount() float64 {
	if x != nil && x.SubsidyAmount != nil {
		return *x.SubsidyAmount
	}
	return 0
}

func (x *CameraSubsidy) GetCameraProvider() string {
	if x != nil {
		return x.CameraProvider
	}
	return ""
}

type ScheduleModification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SafetyModAllCoverages float64 `protobuf:"fixed64,1,opt,name=safetyModAllCoverages,proto3" json:"safetyModAllCoverages,omitempty"`
	AlCredit              float64 `protobuf:"fixed64,2,opt,name=alCredit,proto3" json:"alCredit,omitempty"`
	ApdCredit             float64 `protobuf:"fixed64,3,opt,name=apdCredit,proto3" json:"apdCredit,omitempty"`
	MtcCredit             float64 `protobuf:"fixed64,4,opt,name=mtcCredit,proto3" json:"mtcCredit,omitempty"`
}

func (x *ScheduleModification) Reset() {
	*x = ScheduleModification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleModification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleModification) ProtoMessage() {}

func (x *ScheduleModification) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleModification.ProtoReflect.Descriptor instead.
func (*ScheduleModification) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{12}
}

func (x *ScheduleModification) GetSafetyModAllCoverages() float64 {
	if x != nil {
		return x.SafetyModAllCoverages
	}
	return 0
}

func (x *ScheduleModification) GetAlCredit() float64 {
	if x != nil {
		return x.AlCredit
	}
	return 0
}

func (x *ScheduleModification) GetApdCredit() float64 {
	if x != nil {
		return x.ApdCredit
	}
	return 0
}

func (x *ScheduleModification) GetMtcCredit() float64 {
	if x != nil {
		return x.MtcCredit
	}
	return 0
}

type Driver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LicenseNumber     string                 `protobuf:"bytes,1,opt,name=licenseNumber,proto3" json:"licenseNumber,omitempty"`
	LicenseState      string                 `protobuf:"bytes,2,opt,name=licenseState,proto3" json:"licenseState,omitempty"`
	DateOfHire        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=dateOfHire,proto3" json:"dateOfHire,omitempty"`
	DateOfBirth       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=dateOfBirth,proto3,oneof" json:"dateOfBirth,omitempty"`
	FirstName         *string                `protobuf:"bytes,5,opt,name=firstName,proto3,oneof" json:"firstName,omitempty"`
	LastName          *string                `protobuf:"bytes,6,opt,name=lastName,proto3,oneof" json:"lastName,omitempty"`
	YearsOfExperience *float64               `protobuf:"fixed64,7,opt,name=yearsOfExperience,proto3,oneof" json:"yearsOfExperience,omitempty"`
}

func (x *Driver) Reset() {
	*x = Driver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Driver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Driver) ProtoMessage() {}

func (x *Driver) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Driver.ProtoReflect.Descriptor instead.
func (*Driver) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{13}
}

func (x *Driver) GetLicenseNumber() string {
	if x != nil {
		return x.LicenseNumber
	}
	return ""
}

func (x *Driver) GetLicenseState() string {
	if x != nil {
		return x.LicenseState
	}
	return ""
}

func (x *Driver) GetDateOfHire() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfHire
	}
	return nil
}

func (x *Driver) GetDateOfBirth() *timestamppb.Timestamp {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *Driver) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *Driver) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *Driver) GetYearsOfExperience() float64 {
	if x != nil && x.YearsOfExperience != nil {
		return *x.YearsOfExperience
	}
	return 0
}

type Equipment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OwnerOperatorUnits *int32     `protobuf:"varint,1,opt,name=ownerOperatorUnits,proto3,oneof" json:"ownerOperatorUnits,omitempty"`
	Vehicles           []*Vehicle `protobuf:"bytes,2,rep,name=vehicles,proto3" json:"vehicles,omitempty"`
}

func (x *Equipment) Reset() {
	*x = Equipment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Equipment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Equipment) ProtoMessage() {}

func (x *Equipment) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Equipment.ProtoReflect.Descriptor instead.
func (*Equipment) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{14}
}

func (x *Equipment) GetOwnerOperatorUnits() int32 {
	if x != nil && x.OwnerOperatorUnits != nil {
		return *x.OwnerOperatorUnits
	}
	return 0
}

func (x *Equipment) GetVehicles() []*Vehicle {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

type Vehicle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin         string `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	StatedValue *int32 `protobuf:"varint,2,opt,name=statedValue,proto3,oneof" json:"statedValue,omitempty"`
}

func (x *Vehicle) Reset() {
	*x = Vehicle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Vehicle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vehicle) ProtoMessage() {}

func (x *Vehicle) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vehicle.ProtoReflect.Descriptor instead.
func (*Vehicle) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{15}
}

func (x *Vehicle) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *Vehicle) GetStatedValue() int32 {
	if x != nil && x.StatedValue != nil {
		return *x.StatedValue
	}
	return 0
}

type LossHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LossSummaries map[string]*LossSummaryRecords `protobuf:"bytes,1,rep,name=lossSummaries,proto3" json:"lossSummaries,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	LargeLosses   []*LargeLoss                   `protobuf:"bytes,2,rep,name=largeLosses,proto3" json:"largeLosses,omitempty"`
}

func (x *LossHistory) Reset() {
	*x = LossHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LossHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LossHistory) ProtoMessage() {}

func (x *LossHistory) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LossHistory.ProtoReflect.Descriptor instead.
func (*LossHistory) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{16}
}

func (x *LossHistory) GetLossSummaries() map[string]*LossSummaryRecords {
	if x != nil {
		return x.LossSummaries
	}
	return nil
}

func (x *LossHistory) GetLargeLosses() []*LargeLoss {
	if x != nil {
		return x.LargeLosses
	}
	return nil
}

type LossSummaryRecords struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LossSummaries []*LossSummary `protobuf:"bytes,1,rep,name=lossSummaries,proto3" json:"lossSummaries,omitempty"`
}

func (x *LossSummaryRecords) Reset() {
	*x = LossSummaryRecords{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LossSummaryRecords) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LossSummaryRecords) ProtoMessage() {}

func (x *LossSummaryRecords) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LossSummaryRecords.ProtoReflect.Descriptor instead.
func (*LossSummaryRecords) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{17}
}

func (x *LossSummaryRecords) GetLossSummaries() []*LossSummary {
	if x != nil {
		return x.LossSummaries
	}
	return nil
}

type LossSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PolicyInterval     *proto.Interval `protobuf:"bytes,1,opt,name=policyInterval,proto3" json:"policyInterval,omitempty"`
	NumberOfPowerUnits int32           `protobuf:"varint,2,opt,name=numberOfPowerUnits,proto3" json:"numberOfPowerUnits,omitempty"`
	LossIncurred       int32           `protobuf:"varint,3,opt,name=lossIncurred,proto3" json:"lossIncurred,omitempty"`
	NumberOfClaims     int32           `protobuf:"varint,4,opt,name=numberOfClaims,proto3" json:"numberOfClaims,omitempty"`
	IsNirvanaPeriod    *bool           `protobuf:"varint,5,opt,name=isNirvanaPeriod,proto3,oneof" json:"isNirvanaPeriod,omitempty"`
}

func (x *LossSummary) Reset() {
	*x = LossSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LossSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LossSummary) ProtoMessage() {}

func (x *LossSummary) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LossSummary.ProtoReflect.Descriptor instead.
func (*LossSummary) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{18}
}

func (x *LossSummary) GetPolicyInterval() *proto.Interval {
	if x != nil {
		return x.PolicyInterval
	}
	return nil
}

func (x *LossSummary) GetNumberOfPowerUnits() int32 {
	if x != nil {
		return x.NumberOfPowerUnits
	}
	return 0
}

func (x *LossSummary) GetLossIncurred() int32 {
	if x != nil {
		return x.LossIncurred
	}
	return 0
}

func (x *LossSummary) GetNumberOfClaims() int32 {
	if x != nil {
		return x.NumberOfClaims
	}
	return 0
}

func (x *LossSummary) GetIsNirvanaPeriod() bool {
	if x != nil && x.IsNirvanaPeriod != nil {
		return *x.IsNirvanaPeriod
	}
	return false
}

type LargeLoss struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LossDate     *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=lossDate,proto3" json:"lossDate,omitempty"`
	CoverageID   string                 `protobuf:"bytes,2,opt,name=coverageID,proto3" json:"coverageID,omitempty"`
	LossIncurred int32                  `protobuf:"varint,3,opt,name=lossIncurred,proto3" json:"lossIncurred,omitempty"`
	Description  string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *LargeLoss) Reset() {
	*x = LargeLoss{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LargeLoss) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LargeLoss) ProtoMessage() {}

func (x *LargeLoss) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LargeLoss.ProtoReflect.Descriptor instead.
func (*LargeLoss) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{19}
}

func (x *LargeLoss) GetLossDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LossDate
	}
	return nil
}

func (x *LargeLoss) GetCoverageID() string {
	if x != nil {
		return x.CoverageID
	}
	return ""
}

func (x *LargeLoss) GetLossIncurred() int32 {
	if x != nil {
		return x.LossIncurred
	}
	return 0
}

func (x *LargeLoss) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type NegotiatedRates struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsApplicable       bool                     `protobuf:"varint,1,opt,name=isApplicable,proto3" json:"isApplicable,omitempty"`
	IsApplied          bool                     `protobuf:"varint,2,opt,name=isApplied,proto3" json:"isApplied,omitempty"`
	BaseLimitPremium   *int64                   `protobuf:"varint,3,opt,name=baseLimitPremium,proto3,oneof" json:"baseLimitPremium,omitempty"`
	ThresholdPremium   *int64                   `protobuf:"varint,4,opt,name=thresholdPremium,proto3,oneof" json:"thresholdPremium,omitempty"`
	CoverageIDs        []string                 `protobuf:"bytes,5,rep,name=coverageIDs,proto3" json:"coverageIDs,omitempty"`
	Rules              []*NegotiatedRatesRule   `protobuf:"bytes,6,rep,name=rules,proto3" json:"rules,omitempty"`
	AlNegotiatedRate   int64                    `protobuf:"varint,7,opt,name=alNegotiatedRate,proto3" json:"alNegotiatedRate,omitempty"`
	AlTraditionalRate  *int64                   `protobuf:"varint,8,opt,name=alTraditionalRate,proto3,oneof" json:"alTraditionalRate,omitempty"`
	ApdNegotiatedRate  *int64                   `protobuf:"varint,9,opt,name=apdNegotiatedRate,proto3,oneof" json:"apdNegotiatedRate,omitempty"`
	ApdTraditionalRate *int64                   `protobuf:"varint,10,opt,name=apdTraditionalRate,proto3,oneof" json:"apdTraditionalRate,omitempty"`
	Exemption          NegotiatedRatesExemption `protobuf:"varint,11,opt,name=exemption,proto3,enum=fleet_model.NegotiatedRatesExemption" json:"exemption,omitempty"`
}

func (x *NegotiatedRates) Reset() {
	*x = NegotiatedRates{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NegotiatedRates) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NegotiatedRates) ProtoMessage() {}

func (x *NegotiatedRates) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NegotiatedRates.ProtoReflect.Descriptor instead.
func (*NegotiatedRates) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{20}
}

func (x *NegotiatedRates) GetIsApplicable() bool {
	if x != nil {
		return x.IsApplicable
	}
	return false
}

func (x *NegotiatedRates) GetIsApplied() bool {
	if x != nil {
		return x.IsApplied
	}
	return false
}

func (x *NegotiatedRates) GetBaseLimitPremium() int64 {
	if x != nil && x.BaseLimitPremium != nil {
		return *x.BaseLimitPremium
	}
	return 0
}

func (x *NegotiatedRates) GetThresholdPremium() int64 {
	if x != nil && x.ThresholdPremium != nil {
		return *x.ThresholdPremium
	}
	return 0
}

func (x *NegotiatedRates) GetCoverageIDs() []string {
	if x != nil {
		return x.CoverageIDs
	}
	return nil
}

func (x *NegotiatedRates) GetRules() []*NegotiatedRatesRule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *NegotiatedRates) GetAlNegotiatedRate() int64 {
	if x != nil {
		return x.AlNegotiatedRate
	}
	return 0
}

func (x *NegotiatedRates) GetAlTraditionalRate() int64 {
	if x != nil && x.AlTraditionalRate != nil {
		return *x.AlTraditionalRate
	}
	return 0
}

func (x *NegotiatedRates) GetApdNegotiatedRate() int64 {
	if x != nil && x.ApdNegotiatedRate != nil {
		return *x.ApdNegotiatedRate
	}
	return 0
}

func (x *NegotiatedRates) GetApdTraditionalRate() int64 {
	if x != nil && x.ApdTraditionalRate != nil {
		return *x.ApdTraditionalRate
	}
	return 0
}

func (x *NegotiatedRates) GetExemption() NegotiatedRatesExemption {
	if x != nil {
		return x.Exemption
	}
	return NegotiatedRatesExemption_NEGOTIATED_RATES_EXEMPTION_UNSPECIFIED
}

type NegotiatedRatesRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleType     NegotiatedRatesExemption `protobuf:"varint,1,opt,name=ruleType,proto3,enum=fleet_model.NegotiatedRatesExemption" json:"ruleType,omitempty"`
	IsApplicable bool                     `protobuf:"varint,2,opt,name=isApplicable,proto3" json:"isApplicable,omitempty"`
}

func (x *NegotiatedRatesRule) Reset() {
	*x = NegotiatedRatesRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NegotiatedRatesRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NegotiatedRatesRule) ProtoMessage() {}

func (x *NegotiatedRatesRule) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NegotiatedRatesRule.ProtoReflect.Descriptor instead.
func (*NegotiatedRatesRule) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{21}
}

func (x *NegotiatedRatesRule) GetRuleType() NegotiatedRatesExemption {
	if x != nil {
		return x.RuleType
	}
	return NegotiatedRatesExemption_NEGOTIATED_RATES_EXEMPTION_UNSPECIFIED
}

func (x *NegotiatedRatesRule) GetIsApplicable() bool {
	if x != nil {
		return x.IsApplicable
	}
	return false
}

type CommodityDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdditionalCommodityType  AdditionalCommodityType `protobuf:"varint,1,opt,name=additionalCommodityType,proto3,enum=fleet_model.AdditionalCommodityType" json:"additionalCommodityType,omitempty"`
	PrimaryCommodity         *CommodityName          `protobuf:"varint,2,opt,name=primaryCommodity,proto3,enum=fleet_model.CommodityName,oneof" json:"primaryCommodity,omitempty"`
	PrimaryCommodityCategory *CommodityCategory      `protobuf:"varint,3,opt,name=primaryCommodityCategory,proto3,enum=fleet_model.CommodityCategory,oneof" json:"primaryCommodityCategory,omitempty"`
	CommodityDistribution    *CommodityDistribution  `protobuf:"bytes,4,opt,name=commodityDistribution,proto3" json:"commodityDistribution,omitempty"`
}

func (x *CommodityDetails) Reset() {
	*x = CommodityDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommodityDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommodityDetails) ProtoMessage() {}

func (x *CommodityDetails) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommodityDetails.ProtoReflect.Descriptor instead.
func (*CommodityDetails) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{22}
}

func (x *CommodityDetails) GetAdditionalCommodityType() AdditionalCommodityType {
	if x != nil {
		return x.AdditionalCommodityType
	}
	return AdditionalCommodityType_ADDITIONAL_COMMODITY_TYPE_UNSPECIFIED
}

func (x *CommodityDetails) GetPrimaryCommodity() CommodityName {
	if x != nil && x.PrimaryCommodity != nil {
		return *x.PrimaryCommodity
	}
	return CommodityName_COMMODITY_NAME_UNSPECIFIED
}

func (x *CommodityDetails) GetPrimaryCommodityCategory() CommodityCategory {
	if x != nil && x.PrimaryCommodityCategory != nil {
		return *x.PrimaryCommodityCategory
	}
	return CommodityCategory_COMMODITY_CATEGORY_UNSPECIFIED
}

func (x *CommodityDetails) GetCommodityDistribution() *CommodityDistribution {
	if x != nil {
		return x.CommodityDistribution
	}
	return nil
}

type CommodityDistribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeightedCommodityRecords []*WeightedCommodityRecord `protobuf:"bytes,1,rep,name=weightedCommodityRecords,proto3" json:"weightedCommodityRecords,omitempty"`
	AdditionalCommodities    *AdditionalCommodities     `protobuf:"bytes,2,opt,name=additionalCommodities,proto3" json:"additionalCommodities,omitempty"`
}

func (x *CommodityDistribution) Reset() {
	*x = CommodityDistribution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommodityDistribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommodityDistribution) ProtoMessage() {}

func (x *CommodityDistribution) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommodityDistribution.ProtoReflect.Descriptor instead.
func (*CommodityDistribution) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{23}
}

func (x *CommodityDistribution) GetWeightedCommodityRecords() []*WeightedCommodityRecord {
	if x != nil {
		return x.WeightedCommodityRecords
	}
	return nil
}

func (x *CommodityDistribution) GetAdditionalCommodities() *AdditionalCommodities {
	if x != nil {
		return x.AdditionalCommodities
	}
	return nil
}

type WeightedCommodityRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommodityCategory         CommodityCategory `protobuf:"varint,1,opt,name=commodityCategory,proto3,enum=fleet_model.CommodityCategory" json:"commodityCategory,omitempty"`
	Commodity                 *Commodity        `protobuf:"bytes,2,opt,name=commodity,proto3" json:"commodity,omitempty"`
	PercentageOfHauls         int32             `protobuf:"varint,3,opt,name=percentageOfHauls,proto3" json:"percentageOfHauls,omitempty"`
	AverageDollarValuePerHaul int64             `protobuf:"varint,4,opt,name=averageDollarValuePerHaul,proto3" json:"averageDollarValuePerHaul,omitempty"`
	MaximumDollarValuePerHaul int64             `protobuf:"varint,5,opt,name=maximumDollarValuePerHaul,proto3" json:"maximumDollarValuePerHaul,omitempty"`
}

func (x *WeightedCommodityRecord) Reset() {
	*x = WeightedCommodityRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeightedCommodityRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeightedCommodityRecord) ProtoMessage() {}

func (x *WeightedCommodityRecord) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeightedCommodityRecord.ProtoReflect.Descriptor instead.
func (*WeightedCommodityRecord) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{24}
}

func (x *WeightedCommodityRecord) GetCommodityCategory() CommodityCategory {
	if x != nil {
		return x.CommodityCategory
	}
	return CommodityCategory_COMMODITY_CATEGORY_UNSPECIFIED
}

func (x *WeightedCommodityRecord) GetCommodity() *Commodity {
	if x != nil {
		return x.Commodity
	}
	return nil
}

func (x *WeightedCommodityRecord) GetPercentageOfHauls() int32 {
	if x != nil {
		return x.PercentageOfHauls
	}
	return 0
}

func (x *WeightedCommodityRecord) GetAverageDollarValuePerHaul() int64 {
	if x != nil {
		return x.AverageDollarValuePerHaul
	}
	return 0
}

func (x *WeightedCommodityRecord) GetMaximumDollarValuePerHaul() int64 {
	if x != nil {
		return x.MaximumDollarValuePerHaul
	}
	return 0
}

type Commodity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommodityName *CommodityName `protobuf:"varint,1,opt,name=commodityName,proto3,enum=fleet_model.CommodityName,oneof" json:"commodityName,omitempty"`
	Label         string         `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
}

func (x *Commodity) Reset() {
	*x = Commodity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Commodity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Commodity) ProtoMessage() {}

func (x *Commodity) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Commodity.ProtoReflect.Descriptor instead.
func (*Commodity) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{25}
}

func (x *Commodity) GetCommodityName() CommodityName {
	if x != nil && x.CommodityName != nil {
		return *x.CommodityName
	}
	return CommodityName_COMMODITY_NAME_UNSPECIFIED
}

func (x *Commodity) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

type AdditionalCommodities struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Commodities       string `protobuf:"bytes,1,opt,name=Commodities,proto3" json:"Commodities,omitempty"`
	PercentageOfHauls int32  `protobuf:"varint,2,opt,name=PercentageOfHauls,proto3" json:"PercentageOfHauls,omitempty"`
}

func (x *AdditionalCommodities) Reset() {
	*x = AdditionalCommodities{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fleet_model_program_data_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalCommodities) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalCommodities) ProtoMessage() {}

func (x *AdditionalCommodities) ProtoReflect() protoreflect.Message {
	mi := &file_fleet_model_program_data_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalCommodities.ProtoReflect.Descriptor instead.
func (*AdditionalCommodities) Descriptor() ([]byte, []int) {
	return file_fleet_model_program_data_proto_rawDescGZIP(), []int{26}
}

func (x *AdditionalCommodities) GetCommodities() string {
	if x != nil {
		return x.Commodities
	}
	return ""
}

func (x *AdditionalCommodities) GetPercentageOfHauls() int32 {
	if x != nil {
		return x.PercentageOfHauls
	}
	return 0
}

var File_fleet_model_program_data_proto protoreflect.FileDescriptor

var file_fleet_model_program_data_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0b, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x15, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x89, 0x06, 0x0a, 0x10, 0x46, 0x6c, 0x65,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x0a,
	0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x34, 0x0a,
	0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x07, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x07, 0x64, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x65, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x65,
	0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x0b, 0x6c, 0x6f, 0x73, 0x73,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x6f, 0x73, 0x73,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x0b, 0x6c, 0x6f, 0x73, 0x73, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x12, 0x46, 0x0a, 0x0f, 0x6e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74,
	0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x67, 0x6f,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x73, 0x52, 0x0f, 0x6e, 0x65, 0x67,
	0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x0d,
	0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x42, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x52,
	0x0d, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x12, 0x40,
	0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x75, 0x62, 0x73, 0x69, 0x64, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x75, 0x62, 0x73, 0x69, 0x64,
	0x79, 0x52, 0x0d, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x75, 0x62, 0x73, 0x69, 0x64, 0x79,
	0x12, 0x2e, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x12, 0x50, 0x0a, 0x23, 0x69, 0x73, 0x4d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x4d, 0x69, 0x6c,
	0x65, 0x61, 0x67, 0x65, 0x47, 0x75, 0x61, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x23, 0x69,
	0x73, 0x4d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x47,
	0x75, 0x61, 0x72, 0x61, 0x6e, 0x74, 0x65, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x55, 0x0a, 0x14, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x14, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x10, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x22, 0xa4, 0x02, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x12, 0x2d, 0x0a, 0x0f, 0x64, 0x6f, 0x69, 0x6e, 0x67, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x41, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0f, 0x64, 0x6f, 0x69,
	0x6e, 0x67, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x18, 0x0a, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x4b, 0x0a, 0x11, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x11, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x37, 0x0a, 0x0e, 0x6d, 0x61, 0x69, 0x6c, 0x69, 0x6e,
	0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x0e, 0x6d, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x36, 0x0a, 0x0a, 0x74, 0x61, 0x78, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x54, 0x61, 0x78, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0a, 0x74, 0x61, 0x78,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x64, 0x6f, 0x69, 0x6e,
	0x67, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x41, 0x73, 0x22, 0xe8, 0x05, 0x0a, 0x10,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x29, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x69,
	0x73, 0x47, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73,
	0x47, 0x61, 0x74, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x47, 0x75, 0x61, 0x72, 0x64,
	0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x47, 0x75, 0x61, 0x72,
	0x64, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x0e, 0x74, 0x79, 0x70, 0x65, 0x4f, 0x66, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x66,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x0e, 0x74, 0x79, 0x70, 0x65, 0x4f, 0x66,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x28, 0x0a, 0x0f, 0x69, 0x73, 0x52, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0f, 0x69, 0x73, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x59, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x48, 0x00, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a,
	0x15, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x48, 0x01, 0x52, 0x15, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x6e, 0x0a, 0x16, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x54,
	0x68, 0x65, 0x66, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x54, 0x68, 0x65, 0x66, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x48, 0x02, 0x52, 0x16, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x54, 0x68, 0x65, 0x66, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x6b, 0x0a, 0x15, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x46,
	0x69, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x46, 0x69, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x48, 0x03, 0x52, 0x15, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x46, 0x69, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x54, 0x68, 0x65,
	0x66, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x18, 0x0a, 0x16,
	0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb8, 0x03, 0x0a, 0x09, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x51, 0x0a, 0x15, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x52, 0x15, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x57, 0x0a, 0x15, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x15, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x12, 0x60, 0x0a, 0x18, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x18, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73,
	0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x12, 0x4e, 0x0a, 0x12, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5a, 0x6f, 0x6e,
	0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x12,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x12, 0x35, 0x0a, 0x13, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x4f, 0x66, 0x53, 0x75, 0x62, 0x48, 0x61, 0x75, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x48,
	0x00, 0x52, 0x13, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x4f, 0x66, 0x53,
	0x75, 0x62, 0x48, 0x61, 0x75, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x48, 0x61, 0x75,
	0x6c, 0x22, 0x7b, 0x0a, 0x14, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x43, 0x0a, 0x0e, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1b, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0e,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x1e,
	0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22, 0x96,
	0x01, 0x0a, 0x17, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x5b, 0x0a, 0x16, 0x72, 0x61,
	0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f,
	0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x16, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22, 0x6b, 0x0a, 0x11, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e,
	0x64, 0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x22, 0xcd, 0x01, 0x0a, 0x09, 0x54, 0x61, 0x78, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x6a, 0x75, 0x72, 0x69, 0x73, 0x64, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6a, 0x75,
	0x72, 0x69, 0x73, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a,
	0x0a, 0x10, 0x6a, 0x75, 0x72, 0x69, 0x73, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6a, 0x75, 0x72, 0x69, 0x73, 0x64,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61,
	0x78, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x78,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x4e, 0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x4f, 0x66, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x61, 0x78, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x4f, 0x66, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x52, 0x0f, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x4f, 0x66, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x22, 0x51, 0x0a, 0x17, 0x54, 0x61, 0x78, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x4c, 0x69, 0x6e, 0x65, 0x4f, 0x66, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74,
	0x61, 0x78, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x61, 0x78, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x8c, 0x02, 0x0a, 0x0d, 0x42, 0x72, 0x6f, 0x6b,
	0x65, 0x72, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4c, 0x0a, 0x11, 0x6c, 0x69, 0x63,
	0x65, 0x6e, 0x73, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x48, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x69, 0x63, 0x65, 0x6e,
	0x73, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x29, 0x0a,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x4c, 0x0a, 0x18, 0x6c, 0x69, 0x63, 0x65,
	0x6e, 0x73, 0x65, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x18, 0x6c, 0x69,
	0x63, 0x65, 0x6e, 0x73, 0x65, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x22, 0x95, 0x03, 0x0a, 0x07, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x12, 0x29, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0d, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a,
	0x14, 0x61, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x48, 0x01, 0x52, 0x14, 0x61,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x49, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52,
	0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x12, 0x55, 0x0a, 0x14, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x21, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x52, 0x14, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x3d, 0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x48, 0x02, 0x52, 0x17, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x64, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x67,
	0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61,
	0x74, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x43, 0x6f,
	0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x9e,
	0x01, 0x0a, 0x0d, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x75, 0x62, 0x73, 0x69, 0x64, 0x79,
	0x12, 0x28, 0x0a, 0x0f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x4f, 0x66, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x73, 0x12, 0x29, 0x0a, 0x0d, 0x73, 0x75,
	0x62, 0x73, 0x69, 0x64, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x73, 0x69, 0x64, 0x79, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x50,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x42, 0x10, 0x0a,
	0x0e, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x69, 0x64, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0xa4, 0x01, 0x0a, 0x14, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x15, 0x73, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x4d, 0x6f, 0x64, 0x41, 0x6c, 0x6c, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x4d,
	0x6f, 0x64, 0x41, 0x6c, 0x6c, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x08, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x70,
	0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x61,
	0x70, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x74, 0x63, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6d, 0x74, 0x63,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x22, 0x89, 0x03, 0x0a, 0x06, 0x44, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x69, 0x63, 0x65, 0x6e,
	0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c,
	0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x64, 0x61, 0x74,
	0x65, 0x4f, 0x66, 0x48, 0x69, 0x72, 0x65, 0x12, 0x41, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f,
	0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52,
	0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a,
	0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x02, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31,
	0x0a, 0x11, 0x79, 0x65, 0x61, 0x72, 0x73, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x48, 0x03, 0x52, 0x11, 0x79, 0x65, 0x61,
	0x72, 0x73, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74,
	0x68, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x14, 0x0a, 0x12,
	0x5f, 0x79, 0x65, 0x61, 0x72, 0x73, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x22, 0x89, 0x01, 0x0a, 0x09, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x33, 0x0a, 0x12, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x12,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x6e, 0x69,
	0x74, 0x73, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x08, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x22, 0x52,
	0x0a, 0x07, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e, 0x12, 0x25, 0x0a, 0x0b, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0xfd, 0x01, 0x0a, 0x0b, 0x4c, 0x6f, 0x73, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x12, 0x51, 0x0a, 0x0d, 0x6c, 0x6f, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x6f, 0x73, 0x73, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x2e, 0x4c, 0x6f, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6c, 0x6f, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x69, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x0b, 0x6c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f,
	0x73, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f,
	0x73, 0x73, 0x52, 0x0b, 0x6c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x1a,
	0x61, 0x0a, 0x12, 0x4c, 0x6f, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x6f, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x54, 0x0a, 0x12, 0x4c, 0x6f, 0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x6c, 0x6f, 0x73, 0x73,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x6f,
	0x73, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x0d, 0x6c, 0x6f, 0x73, 0x73, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x22, 0x86, 0x02, 0x0a, 0x0b, 0x4c, 0x6f, 0x73,
	0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x38, 0x0a, 0x0e, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x52, 0x0e, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x12, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x55, 0x6e, 0x69,
	0x74, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x6f, 0x73, 0x73, 0x49, 0x6e, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6c, 0x6f, 0x73, 0x73, 0x49, 0x6e,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x4f, 0x66, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x73, 0x12, 0x2d,
	0x0a, 0x0f, 0x69, 0x73, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0f, 0x69, 0x73, 0x4e, 0x69, 0x72,
	0x76, 0x61, 0x6e, 0x61, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x69, 0x73, 0x4e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x22, 0xa9, 0x01, 0x0a, 0x09, 0x4c, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x6f, 0x73, 0x73, 0x12,
	0x36, 0x0a, 0x08, 0x6c, 0x6f, 0x73, 0x73, 0x44, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x6c,
	0x6f, 0x73, 0x73, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x6f, 0x73, 0x73, 0x49,
	0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6c,
	0x6f, 0x73, 0x73, 0x49, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x88, 0x05,
	0x0a, 0x0f, 0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x65, 0x64, 0x12, 0x2f, 0x0a, 0x10, 0x62, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52,
	0x10, 0x62, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75,
	0x6d, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x10, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01,
	0x52, 0x10, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x50, 0x72, 0x65, 0x6d, 0x69,
	0x75, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x49, 0x44, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x49, 0x44, 0x73, 0x12, 0x36, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52,
	0x61, 0x74, 0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12,
	0x2a, 0x0a, 0x10, 0x61, 0x6c, 0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52,
	0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x61, 0x6c, 0x4e, 0x65, 0x67,
	0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12, 0x31, 0x0a, 0x11, 0x61,
	0x6c, 0x54, 0x72, 0x61, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x52, 0x61, 0x74, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x11, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31,
	0x0a, 0x11, 0x61, 0x70, 0x64, 0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52,
	0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x11, 0x61, 0x70, 0x64,
	0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x33, 0x0a, 0x12, 0x61, 0x70, 0x64, 0x54, 0x72, 0x61, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x48, 0x04, 0x52,
	0x12, 0x61, 0x70, 0x64, 0x54, 0x72, 0x61, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x52,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x43, 0x0a, 0x09, 0x65, 0x78, 0x65, 0x6d, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74,
	0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x73, 0x45, 0x78, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x09, 0x65, 0x78, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x13, 0x0a, 0x11, 0x5f,
	0x62, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d,
	0x42, 0x13, 0x0a, 0x11, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x50, 0x72,
	0x65, 0x6d, 0x69, 0x75, 0x6d, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f,
	0x61, 0x70, 0x64, 0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74,
	0x65, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x61, 0x70, 0x64, 0x54, 0x72, 0x61, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x22, 0x7c, 0x0a, 0x13, 0x4e, 0x65, 0x67, 0x6f,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x41, 0x0a, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x73, 0x45,
	0x78, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xac, 0x03, 0x0a, 0x10, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x64, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5e, 0x0a, 0x17, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x17, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x10, 0x70,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x48, 0x00, 0x52, 0x10, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x64, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x5f, 0x0a, 0x18, 0x70, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69,
	0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x48, 0x01, 0x52, 0x18, 0x70, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x58, 0x0a, 0x15, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0xd3, 0x01, 0x0a, 0x15, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64,
	0x69, 0x74, 0x79, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x60, 0x0a, 0x18, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x64, 0x69, 0x74, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x18, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65,
	0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x12, 0x58, 0x0a, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x52, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x22, 0xc7, 0x02, 0x0a, 0x17,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x4c, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x34, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69,
	0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x12, 0x2c, 0x0a, 0x11, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x4f, 0x66, 0x48, 0x61, 0x75, 0x6c, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x4f, 0x66, 0x48, 0x61, 0x75, 0x6c, 0x73, 0x12, 0x3c, 0x0a, 0x19, 0x61, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x44, 0x6f, 0x6c, 0x6c, 0x61, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50,
	0x65, 0x72, 0x48, 0x61, 0x75, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x19, 0x61, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x44, 0x6f, 0x6c, 0x6c, 0x61, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x50, 0x65, 0x72, 0x48, 0x61, 0x75, 0x6c, 0x12, 0x3c, 0x0a, 0x19, 0x6d, 0x61, 0x78, 0x69, 0x6d,
	0x75, 0x6d, 0x44, 0x6f, 0x6c, 0x6c, 0x61, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x65, 0x72,
	0x48, 0x61, 0x75, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x19, 0x6d, 0x61, 0x78, 0x69,
	0x6d, 0x75, 0x6d, 0x44, 0x6f, 0x6c, 0x6c, 0x61, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x65,
	0x72, 0x48, 0x61, 0x75, 0x6c, 0x22, 0x7a, 0x0a, 0x09, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69,
	0x74, 0x79, 0x12, 0x45, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69,
	0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x67, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x11,
	0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x4f, 0x66, 0x48, 0x61, 0x75, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x4f, 0x66, 0x48, 0x61, 0x75, 0x6c, 0x73, 0x2a, 0xa8, 0x01, 0x0a, 0x0e, 0x54,
	0x79, 0x70, 0x65, 0x4f, 0x66, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x20, 0x0a,
	0x1c, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x46, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41,
	0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x19, 0x0a, 0x15, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x46, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49,
	0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x4f, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4f, 0x46, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x44,
	0x52, 0x4f, 0x50, 0x5f, 0x4c, 0x4f, 0x54, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4f, 0x46, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x4f, 0x46,
	0x46, 0x49, 0x43, 0x45, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f,
	0x46, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49,
	0x4e, 0x41, 0x4c, 0x10, 0x04, 0x2a, 0x8e, 0x0c, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64,
	0x69, 0x74, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x22, 0x0a, 0x1e, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x31, 0x0a, 0x2d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x53,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52, 0x49, 0x45, 0x53,
	0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x42, 0x45, 0x56, 0x45, 0x52, 0x41, 0x47,
	0x45, 0x53, 0x5f, 0x41, 0x4c, 0x43, 0x4f, 0x48, 0x4f, 0x4c, 0x49, 0x43, 0x10, 0x02, 0x12, 0x2c,
	0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x42, 0x45, 0x56, 0x45, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x4f,
	0x4e, 0x41, 0x4c, 0x43, 0x4f, 0x48, 0x4f, 0x4c, 0x49, 0x43, 0x10, 0x03, 0x12, 0x34, 0x0a, 0x30,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x54, 0x45,
	0x52, 0x49, 0x41, 0x4c, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4c, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x10, 0x04, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x52, 0x4f,
	0x4e, 0x49, 0x43, 0x53, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x05, 0x12,
	0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x52, 0x4f, 0x4e, 0x49, 0x43,
	0x5f, 0x50, 0x41, 0x52, 0x54, 0x53, 0x10, 0x06, 0x12, 0x36, 0x0a, 0x32, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46,
	0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x42, 0x41, 0x4b, 0x45, 0x44, 0x5f, 0x44, 0x52, 0x59, 0x5f, 0x50,
	0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x45, 0x44, 0x10, 0x07,
	0x12, 0x32, 0x0a, 0x2e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x46, 0x52, 0x45,
	0x53, 0x48, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x44,
	0x52, 0x59, 0x10, 0x08, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x45, 0x46, 0x45,
	0x52, 0x5f, 0x4d, 0x45, 0x41, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x41, 0x46, 0x4f,
	0x4f, 0x44, 0x10, 0x09, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x45, 0x46, 0x45,
	0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x4d, 0x45, 0x41, 0x54, 0x10, 0x0a, 0x12, 0x26, 0x0a, 0x22,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x46, 0x52, 0x45, 0x49, 0x47,
	0x48, 0x54, 0x10, 0x0b, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45,
	0x48, 0x4f, 0x4c, 0x44, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x10, 0x0c, 0x12, 0x29, 0x0a, 0x25,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x48, 0x4f, 0x4c, 0x44, 0x5f, 0x48, 0x41, 0x52,
	0x44, 0x57, 0x41, 0x52, 0x45, 0x10, 0x0d, 0x12, 0x37, 0x0a, 0x33, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4d, 0x4f, 0x44, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e,
	0x45, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x46, 0x52, 0x45, 0x49, 0x47, 0x48, 0x54, 0x10, 0x0e,
	0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x4e, 0x44, 0x5f,
	0x50, 0x41, 0x52, 0x43, 0x45, 0x4c, 0x10, 0x0f, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d,
	0x45, 0x54, 0x41, 0x4c, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x49, 0x4f,
	0x55, 0x53, 0x10, 0x10, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c,
	0x53, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x49, 0x4f, 0x55, 0x53, 0x10, 0x11, 0x12, 0x31, 0x0a, 0x2d,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x50, 0x41, 0x50, 0x45, 0x52, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x4c, 0x41,
	0x53, 0x54, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x12, 0x12,
	0x1f, 0x0a, 0x1b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x49, 0x4c, 0x45, 0x53, 0x10, 0x13,
	0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x14, 0x12, 0x2c,
	0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x47, 0x52, 0x49, 0x43, 0x55, 0x4c, 0x54, 0x55, 0x52, 0x41,
	0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x15, 0x12, 0x29, 0x0a, 0x25,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x43, 0x48, 0x45, 0x4d, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x50, 0x45, 0x54, 0x52,
	0x4f, 0x4c, 0x45, 0x55, 0x4d, 0x10, 0x16, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45,
	0x47, 0x41, 0x54, 0x45, 0x10, 0x17, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x47, 0x41, 0x52,
	0x42, 0x41, 0x47, 0x45, 0x10, 0x18, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x47, 0x45, 0x4e,
	0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x53, 0x5f, 0x41, 0x43, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x53,
	0x10, 0x19, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x47, 0x52, 0x41, 0x49, 0x4e, 0x5f, 0x46,
	0x45, 0x45, 0x44, 0x5f, 0x48, 0x41, 0x59, 0x5f, 0x43, 0x4f, 0x54, 0x54, 0x4f, 0x4e, 0x10, 0x1a,
	0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x48, 0x41, 0x5a, 0x41, 0x52, 0x44, 0x4f, 0x55, 0x53,
	0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x53, 0x10, 0x1b, 0x12, 0x20, 0x0a, 0x1c,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x10, 0x1c, 0x12, 0x27,
	0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x51, 0x55, 0x49,
	0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x1d, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4d, 0x4f,
	0x42, 0x49, 0x4c, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x53, 0x10, 0x1e, 0x12, 0x29, 0x0a, 0x25,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x54, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x53, 0x10, 0x1f, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4e, 0x55,
	0x52, 0x53, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x10, 0x20, 0x12, 0x26, 0x0a,
	0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x50, 0x48, 0x41, 0x52, 0x4d, 0x41, 0x43, 0x45, 0x55, 0x54, 0x49, 0x43,
	0x41, 0x4c, 0x53, 0x10, 0x21, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x43, 0x52, 0x41,
	0x50, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x10, 0x22, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x57, 0x4f, 0x4f, 0x44, 0x10, 0x23, 0x2a, 0xb2, 0x75, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x64, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x47, 0x0a, 0x43, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x47, 0x47,
	0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x53, 0x5f, 0x52, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x52, 0x5f,
	0x53, 0x41, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x5f, 0x47, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x4f,
	0x52, 0x5f, 0x53, 0x54, 0x4f, 0x4e, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x44, 0x49, 0x52, 0x54, 0x10,
	0x01, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45, 0x53,
	0x5f, 0x4f, 0x52, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x57, 0x41, 0x52, 0x45, 0x10, 0x02, 0x12, 0x18,
	0x0a, 0x14, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x41, 0x53, 0x48, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x53, 0x50,
	0x48, 0x41, 0x4c, 0x54, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x10, 0x04, 0x12, 0x2b, 0x0a,
	0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x53, 0x5f, 0x41, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x4f, 0x52, 0x49, 0x45, 0x53, 0x10, 0x05, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42,
	0x45, 0x56, 0x45, 0x52, 0x41, 0x47, 0x45, 0x53, 0x5f, 0x41, 0x4c, 0x43, 0x4f, 0x48, 0x4f, 0x4c,
	0x49, 0x43, 0x10, 0x06, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x45, 0x56, 0x45, 0x52, 0x41, 0x47,
	0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x41, 0x4c, 0x43, 0x4f, 0x48, 0x4f, 0x4c, 0x49, 0x43,
	0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x4f, 0x41, 0x54, 0x53, 0x10, 0x08, 0x12, 0x30,
	0x0a, 0x2c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x54, 0x45,
	0x52, 0x49, 0x41, 0x4c, 0x53, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x09,
	0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41,
	0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x53, 0x5f, 0x52, 0x41, 0x57, 0x10, 0x0a, 0x12, 0x2a, 0x0a,
	0x26, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x43, 0x48, 0x45, 0x4d, 0x49, 0x43, 0x41, 0x4c, 0x53, 0x5f, 0x42, 0x55, 0x4c, 0x4b,
	0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x10, 0x0b, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x31, 0x5f, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x53, 0x49, 0x56, 0x45, 0x53, 0x10,
	0x0c, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x32, 0x5f, 0x47, 0x41, 0x53,
	0x45, 0x53, 0x10, 0x0d, 0x12, 0x3c, 0x0a, 0x38, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x33, 0x5f,
	0x46, 0x4c, 0x41, 0x4d, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x4d,
	0x42, 0x55, 0x53, 0x54, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x53,
	0x10, 0x0e, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x34, 0x5f, 0x46, 0x4c,
	0x41, 0x4d, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x4f, 0x4c, 0x49, 0x44, 0x53, 0x10, 0x0f,
	0x12, 0x45, 0x0a, 0x41, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x35, 0x5f, 0x4f, 0x58, 0x49, 0x44,
	0x49, 0x5a, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x53,
	0x5f, 0x4f, 0x52, 0x5f, 0x4f, 0x52, 0x47, 0x41, 0x4e, 0x49, 0x43, 0x5f, 0x50, 0x45, 0x52, 0x4f,
	0x58, 0x49, 0x44, 0x45, 0x53, 0x10, 0x10, 0x12, 0x3b, 0x0a, 0x37, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x36, 0x5f, 0x54, 0x4f, 0x58, 0x49, 0x4e, 0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x46,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x54, 0x41, 0x4e, 0x43,
	0x45, 0x53, 0x10, 0x11, 0x12, 0x31, 0x0a, 0x2d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x37, 0x5f,
	0x52, 0x41, 0x44, 0x49, 0x4f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x45,
	0x52, 0x49, 0x41, 0x4c, 0x53, 0x10, 0x12, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x38, 0x5f, 0x43, 0x4f, 0x52, 0x52, 0x4f, 0x53, 0x49, 0x56, 0x45, 0x53, 0x10, 0x13, 0x12,
	0x44, 0x0a, 0x40, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x39, 0x5f, 0x4d, 0x49, 0x53, 0x43, 0x45,
	0x4c, 0x4c, 0x41, 0x4e, 0x45, 0x4f, 0x55, 0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x4f, 0x54, 0x48, 0x45, 0x52, 0x57, 0x49, 0x53, 0x45, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x14, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52,
	0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x15, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x53, 0x4d, 0x45, 0x54, 0x49, 0x43, 0x53,
	0x10, 0x16, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x44, 0x52, 0x59, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x53,
	0x10, 0x17, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x52, 0x4f, 0x4e, 0x49,
	0x43, 0x53, 0x10, 0x18, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x53, 0x10, 0x19, 0x12, 0x29, 0x0a, 0x25, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x4d, 0x41, 0x43, 0x48, 0x49,
	0x4e, 0x45, 0x52, 0x59, 0x10, 0x1a, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x45, 0x52, 0x54, 0x49,
	0x4c, 0x49, 0x5a, 0x45, 0x52, 0x53, 0x10, 0x1b, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x45, 0x52, 0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x54, 0x53, 0x10, 0x1c,
	0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x44, 0x52, 0x59,
	0x5f, 0x46, 0x52, 0x45, 0x49, 0x47, 0x48, 0x54, 0x10, 0x1d, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x47,
	0x52, 0x41, 0x49, 0x4e, 0x5f, 0x4f, 0x52, 0x5f, 0x53, 0x45, 0x45, 0x44, 0x5f, 0x4f, 0x52, 0x5f,
	0x46, 0x45, 0x45, 0x44, 0x10, 0x1e, 0x12, 0x35, 0x0a, 0x31, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x41, 0x5a, 0x41, 0x52,
	0x44, 0x4f, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x53, 0x5f, 0x4f,
	0x52, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x45, 0x52, 0x49, 0x45, 0x53, 0x10, 0x1f, 0x12, 0x24, 0x0a,
	0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x4c, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x4d, 0x41, 0x43, 0x48, 0x49, 0x4e, 0x45, 0x52,
	0x59, 0x10, 0x20, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x53, 0x5f,
	0x46, 0x55, 0x45, 0x4c, 0x10, 0x21, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49,
	0x44, 0x53, 0x5f, 0x4d, 0x49, 0x4c, 0x4b, 0x10, 0x22, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4c, 0x49,
	0x51, 0x55, 0x49, 0x44, 0x53, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f,
	0x46, 0x4c, 0x41, 0x4d, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x23, 0x12, 0x23, 0x0a, 0x1f, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x4c, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x47, 0x53, 0x10, 0x24,
	0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x43, 0x45, 0x4c,
	0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x41, 0x4d, 0x41, 0x5a, 0x4f, 0x4e, 0x10, 0x25, 0x12, 0x1e, 0x0a,
	0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x55, 0x53, 0x50, 0x53, 0x10, 0x26, 0x12, 0x22, 0x0a,
	0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x53, 0x5f, 0x43, 0x4f, 0x50, 0x50, 0x45, 0x52, 0x10,
	0x27, 0x12, 0x33, 0x0a, 0x2f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x53, 0x5f, 0x52, 0x4f, 0x4c,
	0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x54,
	0x45, 0x45, 0x4c, 0x53, 0x10, 0x28, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c,
	0x53, 0x5f, 0x42, 0x41, 0x52, 0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x42, 0x45, 0x41, 0x4d, 0x53, 0x10,
	0x29, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x44,
	0x5f, 0x4f, 0x52, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x10, 0x2a,
	0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x50, 0x45, 0x52, 0x5f, 0x4f, 0x52, 0x5f, 0x50, 0x4c,
	0x41, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x2b,
	0x12, 0x24, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x48, 0x41, 0x52, 0x4d, 0x41, 0x43, 0x45, 0x55, 0x54, 0x49,
	0x43, 0x41, 0x4c, 0x53, 0x10, 0x2c, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x49,
	0x47, 0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x4d, 0x45,
	0x41, 0x54, 0x10, 0x2d, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x49, 0x47, 0x45,
	0x52, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f,
	0x4d, 0x45, 0x41, 0x54, 0x10, 0x2e, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x49,
	0x47, 0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x10, 0x2f, 0x12,
	0x24, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x53, 0x10, 0x30, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x41, 0x4e, 0x44, 0x5f, 0x4f,
	0x52, 0x5f, 0x53, 0x49, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x10, 0x31, 0x12, 0x20, 0x0a, 0x1c,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x53, 0x43, 0x52, 0x41, 0x50, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x10, 0x32, 0x12, 0x1f,
	0x0a, 0x1b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x57, 0x4f, 0x4f, 0x44, 0x5f, 0x43, 0x48, 0x49, 0x50, 0x53, 0x10, 0x33, 0x12,
	0x23, 0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x41, 0x44, 0x49, 0x50, 0x49, 0x43, 0x5f, 0x50, 0x45, 0x4c, 0x4c, 0x45,
	0x54, 0x53, 0x10, 0x34, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x47, 0x52, 0x49, 0x43, 0x55, 0x4c,
	0x54, 0x55, 0x52, 0x41, 0x4c, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x35, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x49, 0x52, 0x43, 0x52, 0x41, 0x46, 0x54, 0x5f, 0x4f,
	0x52, 0x5f, 0x4a, 0x45, 0x54, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x53, 0x10, 0x36, 0x12,
	0x29, 0x0a, 0x25, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x45, 0x52, 0x52, 0x41, 0x49, 0x4e, 0x5f,
	0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x53, 0x10, 0x37, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41,
	0x4c, 0x55, 0x4d, 0x49, 0x4e, 0x55, 0x4d, 0x5f, 0x43, 0x48, 0x4c, 0x4f, 0x52, 0x49, 0x44, 0x45,
	0x10, 0x38, 0x12, 0x33, 0x0a, 0x2f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x4d, 0x4d, 0x4f, 0x4e, 0x49, 0x55, 0x4d, 0x5f,
	0x4e, 0x49, 0x54, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x46, 0x45, 0x52, 0x54, 0x49,
	0x4c, 0x49, 0x5a, 0x45, 0x52, 0x10, 0x39, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x4d, 0x55, 0x53,
	0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x53, 0x10, 0x3a, 0x12,
	0x36, 0x0a, 0x32, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x41, 0x4e, 0x48, 0x59, 0x44, 0x52, 0x4f, 0x55, 0x53, 0x5f, 0x41, 0x4d,
	0x4d, 0x4f, 0x4e, 0x49, 0x41, 0x5f, 0x55, 0x4e, 0x31, 0x30, 0x30, 0x35, 0x5f, 0x43, 0x4c, 0x41,
	0x53, 0x53, 0x32, 0x30, 0x33, 0x10, 0x3b, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x50, 0x50, 0x4c,
	0x49, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x3c, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x53, 0x42,
	0x45, 0x53, 0x54, 0x4f, 0x53, 0x10, 0x3d, 0x12, 0x29, 0x0a, 0x25, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x53, 0x50, 0x48,
	0x41, 0x4c, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x54, 0x4f, 0x50,
	0x10, 0x3e, 0x12, 0x35, 0x0a, 0x31, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x4d, 0x4f, 0x42, 0x49, 0x4c,
	0x45, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x4f, 0x52, 0x49, 0x45, 0x53, 0x10, 0x3f, 0x12, 0x63, 0x0a, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x55,
	0x54, 0x4f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x44, 0x5f, 0x4f,
	0x52, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x49, 0x43, 0x5f, 0x4f, 0x52, 0x5f, 0x41, 0x4e, 0x54,
	0x49, 0x51, 0x55, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x41, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x53, 0x4e, 0x4f, 0x57, 0x42, 0x49, 0x52, 0x44, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x40, 0x12, 0x30,
	0x0a, 0x2c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x53, 0x5f, 0x43,
	0x52, 0x55, 0x53, 0x48, 0x45, 0x44, 0x5f, 0x4f, 0x52, 0x5f, 0x4a, 0x55, 0x4e, 0x4b, 0x10, 0x41,
	0x12, 0x24, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x53,
	0x5f, 0x4e, 0x45, 0x57, 0x10, 0x42, 0x12, 0x47, 0x0a, 0x43, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x53,
	0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x4f, 0x52, 0x5f, 0x50, 0x52, 0x49,
	0x56, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x45, 0x4e, 0x47, 0x45, 0x52, 0x5f, 0x4f,
	0x52, 0x5f, 0x4d, 0x4f, 0x54, 0x4f, 0x52, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x53, 0x10, 0x43, 0x12,
	0x24, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x53, 0x5f, 0x52, 0x41, 0x43, 0x45, 0x5f, 0x43,
	0x41, 0x52, 0x53, 0x10, 0x44, 0x12, 0x4d, 0x0a, 0x49, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x41, 0x4b, 0x45, 0x52, 0x59,
	0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x42, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x4f, 0x52, 0x5f,
	0x50, 0x49, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x50, 0x41, 0x53, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f,
	0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x4f, 0x4b, 0x49, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4b,
	0x45, 0x53, 0x10, 0x45, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x41, 0x4e, 0x41, 0x4e, 0x41, 0x53,
	0x10, 0x46, 0x12, 0x42, 0x0a, 0x3e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x45, 0x52, 0x49, 0x45, 0x53,
	0x5f, 0x44, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x5f, 0x4f, 0x52, 0x5f, 0x4d, 0x41, 0x52, 0x49,
	0x4e, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x45, 0x52, 0x53, 0x5f, 0x4f, 0x52,
	0x5f, 0x45, 0x54, 0x43, 0x10, 0x47, 0x12, 0x41, 0x0a, 0x3d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x45,
	0x52, 0x49, 0x45, 0x53, 0x5f, 0x57, 0x45, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x5f, 0x4f, 0x52, 0x5f,
	0x4d, 0x41, 0x52, 0x49, 0x4e, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x45, 0x52,
	0x5f, 0x4f, 0x52, 0x5f, 0x45, 0x54, 0x43, 0x10, 0x48, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x41,
	0x54, 0x54, 0x45, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x48, 0x4f, 0x4c,
	0x44, 0x10, 0x49, 0x12, 0x4d, 0x0a, 0x49, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x45, 0x41, 0x4e, 0x53, 0x5f, 0x53, 0x4f,
	0x59, 0x42, 0x45, 0x41, 0x4e, 0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x4b, 0x49, 0x44, 0x4e, 0x45, 0x59,
	0x5f, 0x4f, 0x52, 0x5f, 0x50, 0x45, 0x41, 0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x4c, 0x45, 0x4e, 0x54,
	0x49, 0x4c, 0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x48, 0x49, 0x43, 0x4b, 0x50, 0x45, 0x41, 0x53,
	0x10, 0x4a, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x45, 0x45, 0x52, 0x5f, 0x4f, 0x52, 0x5f, 0x57,
	0x49, 0x4e, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x42, 0x52, 0x41, 0x4e, 0x44, 0x59, 0x10, 0x4b, 0x12,
	0x19, 0x0a, 0x15, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x42, 0x45, 0x45, 0x53, 0x10, 0x4c, 0x12, 0x3f, 0x0a, 0x3b, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42,
	0x4f, 0x41, 0x54, 0x53, 0x32, 0x35, 0x46, 0x45, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4f,
	0x56, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x5f, 0x4c, 0x45, 0x4e, 0x47, 0x54, 0x48, 0x5f, 0x4e, 0x45,
	0x57, 0x5f, 0x4f, 0x52, 0x5f, 0x55, 0x53, 0x45, 0x44, 0x10, 0x4d, 0x12, 0x39, 0x0a, 0x35, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x42, 0x4f, 0x41, 0x54, 0x53, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x52, 0x32, 0x35, 0x46, 0x45, 0x45,
	0x54, 0x5f, 0x4c, 0x45, 0x4e, 0x47, 0x54, 0x48, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x4f, 0x52, 0x5f,
	0x55, 0x53, 0x45, 0x44, 0x10, 0x4e, 0x12, 0x33, 0x0a, 0x2f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4c,
	0x45, 0x44, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x45, 0x44, 0x5f, 0x53, 0x4f,
	0x46, 0x54, 0x5f, 0x44, 0x52, 0x49, 0x4e, 0x4b, 0x53, 0x10, 0x4f, 0x12, 0x22, 0x0a, 0x1e, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x42, 0x4f, 0x54, 0x54, 0x4c, 0x45, 0x44, 0x5f, 0x57, 0x41, 0x54, 0x45, 0x52, 0x10, 0x50, 0x12,
	0x1b, 0x0a, 0x17, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x42, 0x52, 0x49, 0x43, 0x4b, 0x53, 0x10, 0x51, 0x12, 0x1e, 0x0a, 0x1a,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x42, 0x55, 0x4c, 0x4c, 0x44, 0x4f, 0x5a, 0x45, 0x52, 0x10, 0x52, 0x12, 0x1c, 0x0a, 0x18,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x43, 0x41, 0x44, 0x4d, 0x49, 0x55, 0x4d, 0x10, 0x53, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43,
	0x41, 0x4c, 0x43, 0x49, 0x55, 0x4d, 0x5f, 0x43, 0x41, 0x52, 0x42, 0x49, 0x44, 0x45, 0x10, 0x54,
	0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x4c, 0x43, 0x49, 0x55, 0x4d, 0x5f, 0x43, 0x48, 0x4c,
	0x4f, 0x52, 0x49, 0x44, 0x45, 0x5f, 0x53, 0x4f, 0x4c, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x55,
	0x12, 0x2c, 0x0a, 0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x4d, 0x45, 0x52, 0x41, 0x53, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x50, 0x48, 0x4f, 0x54, 0x4f, 0x47, 0x52, 0x41, 0x50, 0x48, 0x59, 0x10, 0x56, 0x12, 0x36,
	0x0a, 0x32, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x45, 0x52, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x52,
	0x45, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x53, 0x10, 0x57, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x45,
	0x44, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x10, 0x58, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x41,
	0x52, 0x42, 0x4f, 0x4e, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x10, 0x59, 0x12, 0x3a, 0x0a, 0x36,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x43, 0x41, 0x52, 0x4e, 0x49, 0x56, 0x41, 0x4c, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x49, 0x52,
	0x43, 0x55, 0x53, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x52,
	0x5f, 0x52, 0x49, 0x44, 0x45, 0x53, 0x10, 0x5a, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x52,
	0x50, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x5b, 0x12, 0x3a, 0x0a, 0x36, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x45, 0x49,
	0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x52, 0x5f, 0x46, 0x4c, 0x4f, 0x4f, 0x52, 0x49, 0x4e, 0x47,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x49,
	0x4e, 0x47, 0x10, 0x5c, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x43, 0x52, 0x45, 0x54, 0x45, 0x5f, 0x42, 0x55, 0x4c,
	0x4b, 0x10, 0x5d, 0x12, 0x45, 0x0a, 0x41, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x45, 0x52, 0x41, 0x4d, 0x49, 0x43, 0x5f,
	0x54, 0x49, 0x4c, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x51, 0x55, 0x41, 0x52, 0x52, 0x59, 0x5f, 0x54,
	0x49, 0x4c, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x50, 0x41, 0x56, 0x45, 0x52, 0x53, 0x5f, 0x4f, 0x52,
	0x5f, 0x50, 0x52, 0x4f, 0x53, 0x41, 0x49, 0x43, 0x10, 0x5e, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43,
	0x45, 0x52, 0x45, 0x41, 0x4c, 0x53, 0x10, 0x5f, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x41,
	0x52, 0x43, 0x4f, 0x41, 0x4c, 0x10, 0x60, 0x12, 0x44, 0x0a, 0x40, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x49, 0x43,
	0x4b, 0x45, 0x4e, 0x5f, 0x53, 0x4c, 0x55, 0x44, 0x47, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x47, 0x55,
	0x54, 0x53, 0x5f, 0x48, 0x4f, 0x54, 0x5f, 0x4f, 0x46, 0x46, 0x41, 0x4c, 0x5f, 0x4f, 0x52, 0x5f,
	0x43, 0x48, 0x49, 0x43, 0x4b, 0x45, 0x4e, 0x5f, 0x46, 0x41, 0x54, 0x10, 0x61, 0x12, 0x27, 0x0a,
	0x23, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x43, 0x48, 0x49, 0x4e, 0x41, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x45, 0x52, 0x41,
	0x4d, 0x49, 0x43, 0x53, 0x10, 0x62, 0x12, 0x39, 0x0a, 0x35, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x49, 0x50, 0x53,
	0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4e, 0x44, 0x59, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x5f, 0x53, 0x4e, 0x41, 0x43, 0x4b, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x53, 0x10,
	0x63, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x4c, 0x4f, 0x52, 0x49, 0x4e, 0x45, 0x10, 0x64,
	0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x32, 0x5f, 0x31, 0x5f, 0x46, 0x4c,
	0x41, 0x4d, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x47, 0x41, 0x53, 0x10, 0x65, 0x12, 0x3a, 0x0a,
	0x36, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x32, 0x5f, 0x32, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x46,
	0x4c, 0x41, 0x4d, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x52, 0x45, 0x53,
	0x53, 0x45, 0x44, 0x5f, 0x47, 0x41, 0x53, 0x10, 0x66, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x32, 0x5f, 0x33, 0x5f, 0x50, 0x4f, 0x49, 0x53, 0x4f, 0x4e, 0x4f, 0x55, 0x53,
	0x5f, 0x47, 0x41, 0x53, 0x10, 0x67, 0x12, 0x37, 0x0a, 0x33, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53,
	0x33, 0x5f, 0x46, 0x4c, 0x41, 0x4d, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x43, 0x4b,
	0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49, 0x10, 0x68, 0x12,
	0x38, 0x0a, 0x34, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x33, 0x5f, 0x46, 0x4c, 0x41, 0x4d, 0x4d,
	0x41, 0x42, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49, 0x49, 0x10, 0x69, 0x12, 0x39, 0x0a, 0x35, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x33, 0x5f, 0x46, 0x4c, 0x41, 0x4d, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x50,
	0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49,
	0x49, 0x49, 0x10, 0x6a, 0x12, 0x3f, 0x0a, 0x3b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x34, 0x5f,
	0x31, 0x5f, 0x46, 0x4c, 0x41, 0x4d, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x4f, 0x4c, 0x49,
	0x44, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x4f, 0x55,
	0x50, 0x5f, 0x49, 0x10, 0x6b, 0x12, 0x40, 0x0a, 0x3c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x34,
	0x5f, 0x31, 0x5f, 0x46, 0x4c, 0x41, 0x4d, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x4f, 0x4c,
	0x49, 0x44, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x4f,
	0x55, 0x50, 0x5f, 0x49, 0x49, 0x10, 0x6c, 0x12, 0x41, 0x0a, 0x3d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x34, 0x5f, 0x31, 0x5f, 0x46, 0x4c, 0x41, 0x4d, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53,
	0x4f, 0x4c, 0x49, 0x44, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49, 0x49, 0x49, 0x10, 0x6d, 0x12, 0x40, 0x0a, 0x3c, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43,
	0x4c, 0x41, 0x53, 0x53, 0x34, 0x5f, 0x32, 0x5f, 0x53, 0x50, 0x4f, 0x4e, 0x54, 0x41, 0x4e, 0x45,
	0x4f, 0x55, 0x53, 0x4c, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x42, 0x55, 0x53, 0x54, 0x49, 0x42, 0x4c,
	0x45, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x10, 0x6e, 0x12, 0x39, 0x0a, 0x35,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x34, 0x5f, 0x33, 0x5f, 0x44, 0x41, 0x4e, 0x47, 0x45, 0x52,
	0x4f, 0x55, 0x53, 0x5f, 0x57, 0x48, 0x45, 0x4e, 0x5f, 0x57, 0x45, 0x54, 0x5f, 0x4d, 0x41, 0x54,
	0x45, 0x52, 0x49, 0x41, 0x4c, 0x10, 0x6f, 0x12, 0x38, 0x0a, 0x34, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x35, 0x5f, 0x31, 0x5f, 0x4f, 0x58, 0x49, 0x44, 0x49, 0x5a, 0x45, 0x52, 0x5f, 0x50, 0x41,
	0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49, 0x10,
	0x70, 0x12, 0x39, 0x0a, 0x35, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x35, 0x5f, 0x31, 0x5f, 0x4f,
	0x58, 0x49, 0x44, 0x49, 0x5a, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e,
	0x47, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49, 0x49, 0x10, 0x71, 0x12, 0x3a, 0x0a, 0x36,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x35, 0x5f, 0x31, 0x5f, 0x4f, 0x58, 0x49, 0x44, 0x49, 0x5a,
	0x45, 0x52, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x4f,
	0x55, 0x50, 0x5f, 0x49, 0x49, 0x49, 0x10, 0x72, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41,
	0x53, 0x53, 0x35, 0x5f, 0x32, 0x5f, 0x4f, 0x52, 0x47, 0x41, 0x4e, 0x49, 0x43, 0x5f, 0x50, 0x45,
	0x52, 0x4f, 0x58, 0x49, 0x44, 0x45, 0x10, 0x73, 0x12, 0x43, 0x0a, 0x3f, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41,
	0x53, 0x53, 0x36, 0x5f, 0x31, 0x5f, 0x50, 0x4f, 0x49, 0x53, 0x4f, 0x4e, 0x4f, 0x55, 0x53, 0x5f,
	0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x53, 0x5f, 0x49, 0x4e, 0x48, 0x41, 0x4c, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x48, 0x41, 0x5a, 0x41, 0x52, 0x44, 0x10, 0x74, 0x12, 0x4e, 0x0a,
	0x4a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x36, 0x5f, 0x31, 0x5f, 0x50, 0x4f, 0x49, 0x53, 0x4f,
	0x4e, 0x4f, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x53, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x48, 0x41, 0x4c, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x48, 0x41, 0x5a, 0x41, 0x52, 0x44, 0x10, 0x75, 0x12, 0x45, 0x0a,
	0x41, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x36, 0x5f, 0x32, 0x5f, 0x49, 0x4e, 0x46, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x53,
	0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50,
	0x5f, 0x49, 0x10, 0x76, 0x12, 0x46, 0x0a, 0x42, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x36, 0x5f,
	0x32, 0x5f, 0x49, 0x4e, 0x46, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x42,
	0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e,
	0x47, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49, 0x49, 0x10, 0x77, 0x12, 0x47, 0x0a, 0x43,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x36, 0x5f, 0x32, 0x5f, 0x49, 0x4e, 0x46, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x5f,
	0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f,
	0x49, 0x49, 0x49, 0x10, 0x78, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x37,
	0x5f, 0x52, 0x41, 0x44, 0x49, 0x4f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x79, 0x12, 0x38,
	0x0a, 0x34, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x38, 0x5f, 0x43, 0x4f, 0x52, 0x52, 0x4f, 0x53,
	0x49, 0x56, 0x45, 0x53, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49, 0x10, 0x7a, 0x12, 0x39, 0x0a, 0x35, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41,
	0x53, 0x53, 0x38, 0x5f, 0x43, 0x4f, 0x52, 0x52, 0x4f, 0x53, 0x49, 0x56, 0x45, 0x53, 0x5f, 0x50,
	0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49,
	0x49, 0x10, 0x7b, 0x12, 0x3a, 0x0a, 0x36, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x38, 0x5f, 0x43,
	0x4f, 0x52, 0x52, 0x4f, 0x53, 0x49, 0x56, 0x45, 0x53, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47,
	0x49, 0x4e, 0x47, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49, 0x49, 0x49, 0x10, 0x7c, 0x12,
	0x3d, 0x0a, 0x39, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x39, 0x5f, 0x4d, 0x49, 0x53, 0x43, 0x45,
	0x4c, 0x4c, 0x41, 0x4e, 0x45, 0x4f, 0x55, 0x53, 0x5f, 0x48, 0x41, 0x5a, 0x41, 0x52, 0x44, 0x4f,
	0x55, 0x53, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x53, 0x10, 0x7d, 0x12, 0x19,
	0x0a, 0x15, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x59, 0x10, 0x7e, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4c,
	0x4f, 0x54, 0x48, 0x49, 0x4e, 0x47, 0x10, 0x7f, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x41,
	0x4c, 0x10, 0x80, 0x01, 0x12, 0x37, 0x0a, 0x32, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x41, 0x4c, 0x5f, 0x44, 0x55,
	0x53, 0x54, 0x5f, 0x50, 0x4f, 0x57, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x49, 0x54, 0x41, 0x4e, 0x49,
	0x55, 0x4d, 0x5f, 0x44, 0x49, 0x4f, 0x58, 0x49, 0x44, 0x45, 0x10, 0x81, 0x01, 0x12, 0x2d, 0x0a,
	0x28, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x55, 0x4e, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x82, 0x01, 0x12, 0x30, 0x0a, 0x2b,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x55, 0x4e, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f,
	0x43, 0x45, 0x4c, 0x4c, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x53, 0x10, 0x83, 0x01, 0x12, 0x36,
	0x0a, 0x31, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x52, 0x45, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x47, 0x41,
	0x53, 0x45, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x48, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f,
	0x4f, 0x49, 0x4c, 0x10, 0x84, 0x01, 0x12, 0x2a, 0x0a, 0x25, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55,
	0x54, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52, 0x49, 0x45, 0x53, 0x10,
	0x85, 0x01, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x52, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x86, 0x01, 0x12, 0x27, 0x0a,
	0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x52, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x57,
	0x41, 0x52, 0x45, 0x10, 0x87, 0x01, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x43, 0x52,
	0x45, 0x54, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x88, 0x01, 0x12,
	0x2c, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x52,
	0x59, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x89, 0x01, 0x12, 0x44, 0x0a,
	0x3f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44,
	0x45, 0x42, 0x52, 0x49, 0x53, 0x5f, 0x49, 0x4e, 0x43, 0x4c, 0x55, 0x44, 0x45, 0x53, 0x5f, 0x44,
	0x45, 0x4d, 0x4f, 0x4c, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x42, 0x52, 0x49, 0x53,
	0x10, 0x8a, 0x01, 0x12, 0x2f, 0x0a, 0x2a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x4d, 0x49, 0x4e,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x44, 0x49, 0x52, 0x54, 0x5f, 0x4f, 0x52, 0x5f, 0x53, 0x4f, 0x49,
	0x4c, 0x10, 0x8b, 0x01, 0x12, 0x30, 0x0a, 0x2b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x50, 0x50, 0x45, 0x52, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x50, 0x50, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x53, 0x10, 0x8c, 0x01, 0x12, 0x2b, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x52, 0x52, 0x4f,
	0x53, 0x49, 0x56, 0x45, 0x5f, 0x53, 0x4f, 0x4c, 0x49, 0x44, 0x53, 0x5f, 0x53, 0x41, 0x4c, 0x54,
	0x10, 0x8d, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x54, 0x54, 0x4f, 0x4e, 0x5f, 0x47,
	0x49, 0x4e, 0x4e, 0x45, 0x44, 0x10, 0x8e, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x57,
	0x48, 0x49, 0x44, 0x45, 0x53, 0x5f, 0x52, 0x41, 0x57, 0x5f, 0x48, 0x49, 0x44, 0x45, 0x53, 0x10,
	0x8f, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x43, 0x52, 0x41, 0x4e, 0x45, 0x53, 0x5f, 0x42, 0x4f,
	0x4f, 0x4d, 0x53, 0x10, 0x90, 0x01, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x44, 0x41, 0x49, 0x52, 0x59,
	0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x91, 0x01, 0x12, 0x20, 0x0a, 0x1b,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x44, 0x49, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x53, 0x10, 0x92, 0x01, 0x12, 0x50,
	0x0a, 0x4b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x44, 0x49, 0x4d, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x4f,
	0x4e, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x4e, 0x45, 0x5f, 0x53, 0x4c, 0x41, 0x42, 0x53, 0x5f, 0x47,
	0x52, 0x41, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x4d, 0x41, 0x52, 0x42, 0x4c, 0x45, 0x5f, 0x4c, 0x49,
	0x4d, 0x45, 0x53, 0x54, 0x4f, 0x4e, 0x45, 0x5f, 0x53, 0x4c, 0x41, 0x54, 0x45, 0x10, 0x93, 0x01,
	0x12, 0x36, 0x0a, 0x31, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x5f, 0x54, 0x4f, 0x57, 0x5f, 0x41,
	0x57, 0x41, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x53, 0x5f, 0x54, 0x52, 0x41,
	0x43, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x94, 0x01, 0x12, 0x3e, 0x0a, 0x39, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x44, 0x52, 0x55,
	0x47, 0x53, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x54, 0x48, 0x45, 0x5f, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x45, 0x52, 0x5f, 0x44, 0x52, 0x55, 0x47, 0x53, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x95, 0x01, 0x12, 0x3d, 0x0a, 0x38, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x44, 0x52, 0x55,
	0x47, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x48, 0x41, 0x52, 0x4d, 0x41, 0x43, 0x45, 0x55, 0x54, 0x49,
	0x43, 0x41, 0x4c, 0x53, 0x10, 0x96, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x44, 0x52, 0x59, 0x5f,
	0x43, 0x48, 0x45, 0x4d, 0x49, 0x43, 0x41, 0x4c, 0x53, 0x10, 0x97, 0x01, 0x12, 0x1d, 0x0a, 0x18,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x44, 0x52, 0x59, 0x5f, 0x49, 0x43, 0x45, 0x10, 0x98, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x45, 0x44, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x4f, 0x49, 0x4c, 0x53, 0x10, 0x99, 0x01, 0x12, 0x1a,
	0x0a, 0x15, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x45, 0x47, 0x47, 0x53, 0x10, 0x9a, 0x01, 0x12, 0x2a, 0x0a, 0x25, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x45,
	0x4c, 0x45, 0x43, 0x54, 0x52, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x9b, 0x01, 0x12, 0x44, 0x0a, 0x3f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x52, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x53, 0x5f, 0x53, 0x55, 0x50, 0x50,
	0x4c, 0x49, 0x45, 0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x46, 0x49, 0x58, 0x54, 0x55, 0x52, 0x45, 0x53,
	0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x53, 0x10, 0x9c, 0x01, 0x12, 0x36, 0x0a, 0x31,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x52, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x53, 0x59, 0x53, 0x54,
	0x45, 0x4d, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x9d, 0x01, 0x12, 0x48, 0x0a, 0x43, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x52, 0x4f,
	0x4e, 0x49, 0x43, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45,
	0x52, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x57, 0x49, 0x53, 0x45,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x9e, 0x01, 0x12, 0x3c,
	0x0a, 0x37, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x45, 0x58, 0x4f, 0x54, 0x49, 0x43, 0x5f, 0x43, 0x49, 0x52, 0x43, 0x55, 0x53,
	0x5f, 0x5a, 0x4f, 0x4f, 0x5f, 0x4f, 0x52, 0x5f, 0x57, 0x49, 0x4c, 0x44, 0x5f, 0x41, 0x4e, 0x49,
	0x4d, 0x41, 0x4c, 0x53, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x10, 0x9f, 0x01, 0x12, 0x2b, 0x0a, 0x26,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x53, 0x49, 0x56, 0x45, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x4f,
	0x4e, 0x41, 0x54, 0x4f, 0x52, 0x53, 0x10, 0xa0, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x45,
	0x45, 0x44, 0x5f, 0x53, 0x45, 0x45, 0x44, 0x53, 0x10, 0xa1, 0x01, 0x12, 0x2b, 0x0a, 0x26, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x46, 0x45, 0x52, 0x54, 0x49, 0x4c, 0x49, 0x5a, 0x45, 0x52, 0x5f, 0x44, 0x52, 0x59, 0x5f, 0x42,
	0x41, 0x47, 0x47, 0x45, 0x44, 0x10, 0xa2, 0x01, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x45, 0x52,
	0x54, 0x49, 0x4c, 0x49, 0x5a, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x10, 0xa3,
	0x01, 0x12, 0x2a, 0x0a, 0x25, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x4d, 0x5f, 0x43, 0x45, 0x4c, 0x4c, 0x55,
	0x4c, 0x4f, 0x49, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x41, 0x50, 0x10, 0xa4, 0x01, 0x12, 0x47, 0x0a,
	0x42, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x46, 0x49, 0x52, 0x45, 0x41, 0x52, 0x4d, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53,
	0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x53, 0x5f, 0x41, 0x4d, 0x4d, 0x55, 0x4e, 0x49, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x5f, 0x50, 0x4f, 0x57, 0x44, 0x45, 0x52, 0x5f,
	0x45, 0x54, 0x43, 0x10, 0xa5, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x49, 0x52, 0x45, 0x57,
	0x4f, 0x4f, 0x44, 0x10, 0xa6, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x49, 0x52, 0x45, 0x57,
	0x4f, 0x52, 0x4b, 0x53, 0x10, 0xa7, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x49, 0x53, 0x48,
	0x5f, 0x4c, 0x49, 0x56, 0x45, 0x10, 0xa8, 0x01, 0x12, 0x36, 0x0a, 0x31, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x49, 0x53,
	0x48, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x41, 0x46, 0x4f, 0x4f, 0x44, 0x5f, 0x46, 0x52,
	0x45, 0x53, 0x48, 0x5f, 0x4f, 0x52, 0x5f, 0x46, 0x52, 0x4f, 0x5a, 0x45, 0x4e, 0x10, 0xa9, 0x01,
	0x12, 0x20, 0x0a, 0x1b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x4c, 0x41, 0x4d, 0x4d, 0x41, 0x42, 0x4c, 0x45, 0x53, 0x10,
	0xaa, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x4c, 0x4f, 0x55, 0x52, 0x10, 0xab, 0x01, 0x12,
	0x1d, 0x0a, 0x18, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x46, 0x4c, 0x59, 0x5f, 0x41, 0x53, 0x48, 0x10, 0xac, 0x01, 0x12, 0x3a,
	0x0a, 0x35, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x45, 0x5f, 0x56, 0x45, 0x47, 0x45, 0x54, 0x41, 0x42, 0x4c, 0x45,
	0x53, 0x5f, 0x46, 0x52, 0x55, 0x49, 0x54, 0x10, 0xad, 0x01, 0x12, 0x4e, 0x0a, 0x49, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46,
	0x52, 0x4f, 0x5a, 0x45, 0x4e, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x56, 0x45, 0x47, 0x45,
	0x54, 0x41, 0x42, 0x4c, 0x45, 0x53, 0x5f, 0x46, 0x52, 0x55, 0x49, 0x54, 0x5f, 0x45, 0x58, 0x43,
	0x4c, 0x55, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x41, 0x4e, 0x44, 0x5f,
	0x53, 0x45, 0x41, 0x46, 0x4f, 0x4f, 0x44, 0x10, 0xae, 0x01, 0x12, 0x35, 0x0a, 0x30, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46,
	0x52, 0x4f, 0x5a, 0x45, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x54, 0x4f, 0x5f, 0x42,
	0x41, 0x4b, 0x45, 0x5f, 0x43, 0x4f, 0x4f, 0x4b, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x53, 0x10, 0xaf,
	0x01, 0x12, 0x28, 0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x46, 0x55, 0x52, 0x5f, 0x46, 0x55, 0x52, 0x5f, 0x53, 0x4b,
	0x49, 0x4e, 0x5f, 0x50, 0x45, 0x4c, 0x54, 0x53, 0x10, 0xb0, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x46, 0x55, 0x52, 0x4e, 0x49, 0x54, 0x55, 0x52, 0x45, 0x10, 0xb1, 0x01, 0x12, 0x48, 0x0a, 0x43,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x57, 0x49, 0x4c, 0x44, 0x5f, 0x42, 0x49,
	0x52, 0x44, 0x53, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x50, 0x48, 0x45, 0x41, 0x53, 0x41, 0x4e,
	0x54, 0x53, 0x5f, 0x51, 0x55, 0x41, 0x49, 0x4c, 0x5f, 0x44, 0x55, 0x43, 0x4b, 0x5f, 0x47, 0x45,
	0x45, 0x53, 0x45, 0x10, 0xb2, 0x01, 0x12, 0x2a, 0x0a, 0x25, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x47, 0x41, 0x52, 0x42, 0x41,
	0x47, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x53, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x53, 0x48, 0x10,
	0xb3, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x47, 0x41, 0x53, 0x4f, 0x4c, 0x49, 0x4e, 0x45, 0x10,
	0xb4, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x47, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x46, 0x4c, 0x41,
	0x54, 0x10, 0xb5, 0x01, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x47, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x44,
	0x55, 0x53, 0x54, 0x5f, 0x50, 0x4f, 0x57, 0x44, 0x45, 0x52, 0x10, 0xb6, 0x01, 0x12, 0x24, 0x0a,
	0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x47, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53,
	0x10, 0xb7, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x47, 0x4f, 0x4c, 0x46, 0x5f, 0x43, 0x41, 0x52,
	0x54, 0x53, 0x10, 0xb8, 0x01, 0x12, 0x4c, 0x0a, 0x47, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x47, 0x52, 0x41, 0x49, 0x4e, 0x53,
	0x5f, 0x43, 0x4f, 0x52, 0x4e, 0x5f, 0x57, 0x48, 0x45, 0x41, 0x54, 0x5f, 0x42, 0x41, 0x52, 0x4c,
	0x45, 0x59, 0x5f, 0x52, 0x49, 0x43, 0x45, 0x5f, 0x4f, 0x41, 0x54, 0x53, 0x5f, 0x52, 0x59, 0x45,
	0x5f, 0x50, 0x45, 0x41, 0x4e, 0x55, 0x54, 0x53, 0x5f, 0x53, 0x4f, 0x52, 0x47, 0x48, 0x55, 0x4d,
	0x10, 0xb9, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x47, 0x59, 0x50, 0x53, 0x55, 0x4d, 0x10, 0xba,
	0x01, 0x12, 0x2c, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x53,
	0x5f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x53, 0x10, 0xbb, 0x01, 0x12,
	0x4d, 0x0a, 0x48, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x48, 0x45, 0x4c, 0x44, 0x5f, 0x44, 0x45, 0x56,
	0x49, 0x43, 0x45, 0x53, 0x5f, 0x47, 0x50, 0x53, 0x5f, 0x50, 0x44, 0x41, 0x53, 0x5f, 0x49, 0x50,
	0x4f, 0x44, 0x53, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x53, 0x45, 0x54, 0x53, 0x5f, 0x48, 0x45, 0x41,
	0x44, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x53, 0x5f, 0x4d, 0x50, 0x33, 0x10, 0xbc, 0x01, 0x12, 0x19,
	0x0a, 0x14, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x48, 0x41, 0x59, 0x10, 0xbd, 0x01, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x41,
	0x5a, 0x41, 0x52, 0x44, 0x4f, 0x55, 0x53, 0x5f, 0x57, 0x41, 0x53, 0x54, 0x45, 0x10, 0xbe, 0x01,
	0x12, 0x21, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x41, 0x49, 0x44, 0x53,
	0x10, 0xbf, 0x01, 0x12, 0x3e, 0x0a, 0x39, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f,
	0x56, 0x45, 0x4e, 0x54, 0x49, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4e, 0x44, 0x5f,
	0x41, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x49, 0x4e, 0x47,
	0x10, 0xc0, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x41, 0x55, 0x44,
	0x49, 0x4f, 0x10, 0xc1, 0x01, 0x12, 0x3d, 0x0a, 0x38, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x46,
	0x55, 0x52, 0x4e, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x41,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x4f, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x44, 0x52, 0x41, 0x50, 0x45,
	0x53, 0x10, 0xc2, 0x01, 0x12, 0x2f, 0x0a, 0x2a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x41,
	0x4e, 0x44, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x56, 0x45,
	0x52, 0x53, 0x10, 0xc3, 0x01, 0x12, 0x31, 0x0a, 0x2c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x48,
	0x4f, 0x4c, 0x44, 0x5f, 0x43, 0x4c, 0x45, 0x41, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x52, 0x4f,
	0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0xc4, 0x01, 0x12, 0x4b, 0x0a, 0x46, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x4f, 0x55,
	0x53, 0x45, 0x48, 0x4f, 0x4c, 0x44, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x57, 0x41, 0x52, 0x45, 0x5f,
	0x46, 0x41, 0x53, 0x54, 0x45, 0x4e, 0x45, 0x52, 0x53, 0x5f, 0x4b, 0x45, 0x59, 0x53, 0x5f, 0x4c,
	0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x48, 0x49, 0x4e, 0x47, 0x45, 0x53, 0x5f, 0x43, 0x48, 0x41, 0x49,
	0x4e, 0x53, 0x10, 0xc5, 0x01, 0x12, 0x4d, 0x0a, 0x48, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x48,
	0x4f, 0x4c, 0x44, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x57, 0x41, 0x52, 0x45, 0x5f, 0x50, 0x4c, 0x55,
	0x4d, 0x42, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x53, 0x5f, 0x50,
	0x41, 0x49, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x42, 0x49, 0x4e, 0x45, 0x54, 0x5f, 0x42, 0x41, 0x54,
	0x48, 0x10, 0xc6, 0x01, 0x12, 0x3c, 0x0a, 0x37, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x57, 0x41,
	0x52, 0x45, 0x53, 0x5f, 0x43, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x54, 0x45, 0x4e,
	0x53, 0x49, 0x4c, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x48, 0x45, 0x53, 0x5f, 0x45, 0x54, 0x43, 0x10,
	0xc7, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x49, 0x43, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x4d,
	0x5f, 0x49, 0x43, 0x45, 0x10, 0xc8, 0x01, 0x12, 0x2a, 0x0a, 0x25, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x44, 0x55,
	0x53, 0x54, 0x52, 0x49, 0x41, 0x4c, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0xc9, 0x01, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x4b, 0x5f, 0x57, 0x41, 0x54, 0x45,
	0x52, 0x5f, 0x53, 0x4f, 0x4c, 0x55, 0x42, 0x4c, 0x45, 0x10, 0xca, 0x01, 0x12, 0x23, 0x0a, 0x1e,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x53, 0x10, 0xcb,
	0x01, 0x12, 0x5f, 0x0a, 0x5a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x41, 0x54, 0x45, 0x44,
	0x5f, 0x43, 0x49, 0x52, 0x43, 0x55, 0x49, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45,
	0x52, 0x5f, 0x43, 0x48, 0x49, 0x50, 0x5f, 0x4d, 0x49, 0x43, 0x52, 0x4f, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x4f, 0x52, 0x53, 0x5f, 0x53, 0x45, 0x4d, 0x49, 0x43, 0x4f, 0x4e, 0x44, 0x55,
	0x43, 0x54, 0x4f, 0x52, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x10,
	0xcc, 0x01, 0x12, 0x40, 0x0a, 0x3b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4d, 0x4f, 0x44, 0x41,
	0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x54,
	0x41, 0x49, 0x4e, 0x45, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x46, 0x52, 0x45, 0x49, 0x47, 0x48,
	0x54, 0x10, 0xcd, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4a, 0x45, 0x57, 0x45, 0x4c, 0x52, 0x59,
	0x10, 0xce, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4a, 0x55, 0x4e, 0x4b, 0x10, 0xcf, 0x01, 0x12,
	0x25, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x4c, 0x41, 0x57, 0x4e, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x47, 0x41, 0x52,
	0x44, 0x45, 0x4e, 0x10, 0xd0, 0x01, 0x12, 0x26, 0x0a, 0x21, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x44, 0x5f,
	0x50, 0x4f, 0x57, 0x44, 0x45, 0x52, 0x5f, 0x44, 0x55, 0x53, 0x54, 0x10, 0xd1, 0x01, 0x12, 0x1d,
	0x0a, 0x18, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x4c, 0x45, 0x41, 0x54, 0x48, 0x45, 0x52, 0x10, 0xd2, 0x01, 0x12, 0x30, 0x0a,
	0x2b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x45, 0x5f, 0x53, 0x4c, 0x41, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x41,
	0x4e, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x4c, 0x41, 0x43, 0x4b, 0x45, 0x44, 0x10, 0xd3, 0x01, 0x12,
	0x1f, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x45, 0x53, 0x54, 0x4f, 0x4e, 0x45, 0x10, 0xd4, 0x01,
	0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x5f, 0x4c, 0x41, 0x54, 0x45,
	0x58, 0x10, 0xd5, 0x01, 0x12, 0x34, 0x0a, 0x2f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x4f, 0x52, 0x5f,
	0x45, 0x58, 0x43, 0x4c, 0x55, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x45, 0x45, 0x52, 0x5f, 0x41,
	0x4e, 0x44, 0x5f, 0x57, 0x49, 0x4e, 0x45, 0x10, 0xd6, 0x01, 0x12, 0x3d, 0x0a, 0x38, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4c,
	0x49, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x50, 0x49,
	0x47, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x50, 0x5f,
	0x48, 0x4f, 0x52, 0x53, 0x45, 0x53, 0x10, 0xd7, 0x01, 0x12, 0x37, 0x0a, 0x32, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4c, 0x4f,
	0x47, 0x53, 0x5f, 0x4c, 0x4f, 0x47, 0x47, 0x45, 0x52, 0x53, 0x5f, 0x57, 0x4f, 0x4f, 0x44, 0x5f,
	0x48, 0x41, 0x52, 0x56, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x55, 0x4c, 0x50, 0x10,
	0xd8, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4c, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0xd9, 0x01,
	0x12, 0x33, 0x0a, 0x2e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x43, 0x48, 0x49, 0x4e, 0x45, 0x52, 0x59, 0x5f, 0x41,
	0x4e, 0x44, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0xda, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0xdb,
	0x01, 0x12, 0x30, 0x0a, 0x2b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x52, 0x45, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x46, 0x45, 0x52, 0x54, 0x49, 0x4c, 0x49, 0x5a, 0x45, 0x52, 0x5f, 0x42, 0x55, 0x4c, 0x4b,
	0x10, 0xdc, 0x01, 0x12, 0x4e, 0x0a, 0x49, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x45, 0x41, 0x54, 0x5f, 0x46, 0x52, 0x4f,
	0x5a, 0x45, 0x4e, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47,
	0x45, 0x44, 0x5f, 0x42, 0x4f, 0x58, 0x45, 0x44, 0x5f, 0x45, 0x58, 0x43, 0x4c, 0x55, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x53, 0x57, 0x49, 0x4e, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x45, 0x41, 0x54,
	0x10, 0xdd, 0x01, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x4c, 0x5f,
	0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0xde, 0x01, 0x12, 0x24,
	0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54,
	0x53, 0x10, 0xdf, 0x01, 0x12, 0x2e, 0x0a, 0x29, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x5f, 0x53,
	0x48, 0x45, 0x45, 0x54, 0x53, 0x5f, 0x43, 0x4f, 0x49, 0x4c, 0x53, 0x5f, 0x52, 0x4f, 0x4c, 0x4c,
	0x53, 0x10, 0xe0, 0x01, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x59, 0x4c, 0x5f,
	0x42, 0x52, 0x4f, 0x4d, 0x49, 0x44, 0x45, 0x10, 0xe1, 0x01, 0x12, 0x2d, 0x0a, 0x28, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d,
	0x45, 0x54, 0x48, 0x59, 0x4c, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x41, 0x4e, 0x4f, 0x4c, 0x5f, 0x41,
	0x4c, 0x43, 0x4f, 0x48, 0x4f, 0x4c, 0x10, 0xe2, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x49,
	0x4c, 0x4b, 0x10, 0xe3, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45,
	0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x53, 0x10, 0xe4, 0x01, 0x12, 0x2a, 0x0a, 0x25, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x4f,
	0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x41, 0x52, 0x5f, 0x48, 0x4f, 0x4d,
	0x45, 0x53, 0x10, 0xe5, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x4f, 0x4c, 0x41, 0x53, 0x53,
	0x45, 0x53, 0x10, 0xe6, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x4f, 0x54, 0x4f, 0x52, 0x43,
	0x59, 0x43, 0x4c, 0x45, 0x53, 0x10, 0xe7, 0x01, 0x12, 0x2c, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x55, 0x4c,
	0x43, 0x48, 0x5f, 0x54, 0x4f, 0x50, 0x5f, 0x53, 0x4f, 0x49, 0x4c, 0x5f, 0x4f, 0x52, 0x5f, 0x46,
	0x49, 0x4c, 0x4c, 0x10, 0xe8, 0x01, 0x12, 0x2b, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49,
	0x4d, 0x45, 0x44, 0x49, 0x41, 0x5f, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x53,
	0x10, 0xe9, 0x01, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4d, 0x55, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f,
	0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0xea, 0x01, 0x12, 0x1c,
	0x0a, 0x17, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x4d, 0x55, 0x53, 0x4c, 0x49, 0x4e, 0x10, 0xeb, 0x01, 0x12, 0x20, 0x0a, 0x1b,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0xec, 0x01, 0x12, 0x1f,
	0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x4e, 0x55, 0x54, 0x52, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xed, 0x01, 0x12,
	0x24, 0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x4e, 0x55, 0x54, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x45,
	0x44, 0x53, 0x10, 0xee, 0x01, 0x12, 0x3c, 0x0a, 0x37, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x49, 0x43, 0x45,
	0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x43, 0x48, 0x49,
	0x4e, 0x45, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x53,
	0x10, 0xef, 0x01, 0x12, 0x30, 0x0a, 0x2b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x4c, 0x55, 0x42, 0x52,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x5f, 0x54, 0x41, 0x4e, 0x4b, 0x45,
	0x52, 0x53, 0x10, 0xf0, 0x01, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xf1, 0x01,
	0x12, 0x32, 0x0a, 0x2d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x49, 0x4c, 0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x41, 0x52, 0x52,
	0x45, 0x4c, 0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x41, 0x4e,
	0x53, 0x10, 0xf2, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x52, 0x45, 0x53, 0x10, 0xf3, 0x01,
	0x12, 0x47, 0x0a, 0x42, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x53, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x57, 0x49, 0x53, 0x45, 0x5f, 0x43, 0x4c, 0x41, 0x53,
	0x53, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0xf4, 0x01, 0x12, 0x2f, 0x0a, 0x2a, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x54,
	0x48, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x50, 0x45, 0x52, 0x5f, 0x50, 0x4c, 0x41, 0x53, 0x54, 0x49,
	0x43, 0x5f, 0x47, 0x4c, 0x41, 0x53, 0x53, 0x10, 0xf5, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x4f,
	0x58, 0x49, 0x44, 0x49, 0x5a, 0x45, 0x52, 0x53, 0x10, 0xf6, 0x01, 0x12, 0x33, 0x0a, 0x2e, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x50, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x53, 0x10, 0xf7, 0x01,
	0x12, 0x3d, 0x0a, 0x38, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50,
	0x41, 0x49, 0x4e, 0x54, 0x5f, 0x54, 0x48, 0x49, 0x4e, 0x4e, 0x45, 0x52, 0x53, 0x5f, 0x43, 0x41,
	0x4e, 0x4e, 0x45, 0x44, 0x5f, 0x4f, 0x52, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x10, 0xf8, 0x01, 0x12,
	0x4c, 0x0a, 0x47, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x50, 0x45, 0x52, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x50, 0x41,
	0x50, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x5f, 0x49, 0x4e, 0x43,
	0x4c, 0x55, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x49, 0x4e, 0x47,
	0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x53, 0x10, 0xf9, 0x01, 0x12, 0x24, 0x0a,
	0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x50, 0x41, 0x50, 0x45, 0x52, 0x5f, 0x53, 0x48, 0x52, 0x45, 0x44, 0x44, 0x45, 0x44,
	0x10, 0xfa, 0x01, 0x12, 0x2b, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x55, 0x4d, 0x45, 0x53,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x47, 0x4e, 0x45, 0x53, 0x10, 0xfb, 0x01,
	0x12, 0x23, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x41,
	0x52, 0x45, 0x10, 0xfc, 0x01, 0x12, 0x2b, 0x0a, 0x26, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x53, 0x55,
	0x50, 0x50, 0x4c, 0x49, 0x45, 0x53, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x5f, 0x45, 0x54, 0x43, 0x10,
	0xfd, 0x01, 0x12, 0x3b, 0x0a, 0x36, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x52, 0x4f, 0x4c, 0x45, 0x55, 0x4d,
	0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x5f, 0x4f,
	0x52, 0x5f, 0x49, 0x4e, 0x5f, 0x54, 0x41, 0x4e, 0x4b, 0x45, 0x52, 0x53, 0x10, 0xfe, 0x01, 0x12,
	0x30, 0x0a, 0x2b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x50, 0x49, 0x50, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x5f,
	0x4f, 0x49, 0x4c, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x50, 0x49, 0x50, 0x45, 0x10, 0xff,
	0x01, 0x12, 0x2e, 0x0a, 0x29, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x54, 0x53, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x4e, 0x55, 0x52, 0x53, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x10, 0x80,
	0x02, 0x12, 0x32, 0x0a, 0x2d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x4c, 0x41, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x44, 0x52,
	0x59, 0x57, 0x41, 0x4c, 0x4c, 0x5f, 0x47, 0x59, 0x50, 0x53, 0x55, 0x4d, 0x5f, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x10, 0x81, 0x02, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x4c, 0x41, 0x53, 0x54, 0x49,
	0x43, 0x5f, 0x50, 0x45, 0x4c, 0x4c, 0x45, 0x54, 0x53, 0x10, 0x82, 0x02, 0x12, 0x26, 0x0a, 0x21,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x50, 0x4c, 0x41, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54,
	0x53, 0x10, 0x83, 0x02, 0x12, 0x35, 0x0a, 0x30, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x4c, 0x55, 0x4d, 0x42, 0x49, 0x4e,
	0x47, 0x5f, 0x46, 0x49, 0x58, 0x54, 0x55, 0x52, 0x45, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x45,
	0x51, 0x55, 0x49, 0x50, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x84, 0x02, 0x12, 0x1c, 0x0a, 0x17, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x50, 0x4f, 0x54, 0x41, 0x53, 0x48, 0x10, 0x85, 0x02, 0x12, 0x28, 0x0a, 0x23, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x4f,
	0x54, 0x41, 0x53, 0x53, 0x49, 0x55, 0x4d, 0x5f, 0x43, 0x48, 0x4c, 0x4f, 0x52, 0x49, 0x44, 0x45,
	0x10, 0x86, 0x02, 0x12, 0x43, 0x0a, 0x3e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x4f, 0x55, 0x4c, 0x54, 0x52, 0x59, 0x5f,
	0x46, 0x52, 0x4f, 0x5a, 0x45, 0x4e, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x50, 0x41, 0x43,
	0x4b, 0x41, 0x47, 0x45, 0x44, 0x5f, 0x42, 0x4f, 0x58, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x87, 0x02, 0x12, 0x37, 0x0a, 0x32, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x4f, 0x55,
	0x4c, 0x54, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x5f, 0x44, 0x55, 0x43, 0x4b, 0x53, 0x5f,
	0x43, 0x48, 0x49, 0x43, 0x4b, 0x45, 0x4e, 0x53, 0x5f, 0x47, 0x45, 0x45, 0x53, 0x45, 0x10, 0x88,
	0x02, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45,
	0x44, 0x5f, 0x46, 0x4f, 0x4f, 0x44, 0x53, 0x10, 0x89, 0x02, 0x12, 0x26, 0x0a, 0x21, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50,
	0x52, 0x49, 0x4e, 0x54, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x10,
	0x8a, 0x02, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x53, 0x10,
	0x8b, 0x02, 0x12, 0x2a, 0x0a, 0x25, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x52, 0x41, 0x44, 0x49, 0x4f, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x10, 0x8c, 0x02, 0x12, 0x1e,
	0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x52, 0x41, 0x57, 0x5f, 0x53, 0x49, 0x4c, 0x4b, 0x10, 0x8d, 0x02, 0x12, 0x28,
	0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x49, 0x47, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x4d, 0x45, 0x41, 0x54, 0x10, 0x8e, 0x02, 0x12, 0x2c, 0x0a, 0x27, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x46,
	0x52, 0x49, 0x47, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x4d,
	0x45, 0x41, 0x54, 0x10, 0x8f, 0x02, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x4e, 0x44, 0x45,
	0x52, 0x49, 0x4e, 0x47, 0x10, 0x90, 0x02, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x52, 0x49, 0x47, 0x47,
	0x49, 0x4e, 0x47, 0x5f, 0x43, 0x52, 0x41, 0x4e, 0x45, 0x53, 0x10, 0x91, 0x02, 0x12, 0x1a, 0x0a,
	0x15, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x52, 0x4f, 0x43, 0x4b, 0x10, 0x92, 0x02, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x52, 0x55,
	0x42, 0x42, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0x93, 0x02,
	0x12, 0x25, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x41, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x53, 0x4f, 0x4c, 0x55,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x94, 0x02, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x41, 0x4c, 0x54,
	0x10, 0x95, 0x02, 0x12, 0x49, 0x0a, 0x44, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x41, 0x4e, 0x44, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x47, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x43, 0x52, 0x55, 0x53, 0x48, 0x45, 0x44, 0x5f,
	0x53, 0x54, 0x4f, 0x4e, 0x45, 0x5f, 0x53, 0x4c, 0x41, 0x47, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45,
	0x47, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x49, 0x4c, 0x49, 0x43, 0x41, 0x10, 0x96, 0x02, 0x12, 0x1b,
	0x0a, 0x16, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x53, 0x41, 0x54, 0x49, 0x4e, 0x10, 0x97, 0x02, 0x12, 0x1d, 0x0a, 0x18, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x53, 0x41, 0x57, 0x44, 0x55, 0x53, 0x54, 0x10, 0x98, 0x02, 0x12, 0x3a, 0x0a, 0x35, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53,
	0x43, 0x49, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x43, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55,
	0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x99, 0x02, 0x12, 0x3c, 0x0a, 0x37, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x41, 0x50,
	0x5f, 0x4d, 0x45, 0x54, 0x41, 0x4c, 0x5f, 0x49, 0x52, 0x4f, 0x4e, 0x5f, 0x4f, 0x52, 0x5f, 0x53,
	0x41, 0x4c, 0x56, 0x41, 0x47, 0x45, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x53, 0x10, 0x9a, 0x02, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x48, 0x41, 0x56, 0x45, 0x52, 0x53,
	0x10, 0x9b, 0x02, 0x12, 0x44, 0x0a, 0x3f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x46, 0x49, 0x53,
	0x48, 0x5f, 0x4d, 0x4f, 0x4c, 0x4c, 0x55, 0x53, 0x4b, 0x53, 0x5f, 0x43, 0x52, 0x55, 0x53, 0x54,
	0x41, 0x43, 0x45, 0x41, 0x4e, 0x53, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x4f, 0x52, 0x5f,
	0x46, 0x52, 0x4f, 0x5a, 0x45, 0x4e, 0x10, 0x9c, 0x02, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x48,
	0x49, 0x4e, 0x47, 0x4c, 0x45, 0x53, 0x10, 0x9d, 0x02, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x49,
	0x4c, 0x4b, 0x10, 0x9e, 0x02, 0x12, 0x2e, 0x0a, 0x29, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x4c, 0x55, 0x44, 0x47, 0x45,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x41, 0x4f, 0x52, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53,
	0x5f, 0x42, 0x10, 0x9f, 0x02, 0x12, 0x21, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x4f, 0x41, 0x50, 0x5f, 0x4c,
	0x49, 0x51, 0x55, 0x49, 0x44, 0x10, 0xa0, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x4f, 0x44,
	0x10, 0xa1, 0x02, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x4f, 0x44, 0x49, 0x55, 0x4d, 0x5f, 0x43,
	0x48, 0x4c, 0x4f, 0x52, 0x49, 0x44, 0x45, 0x10, 0xa2, 0x02, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53,
	0x4f, 0x44, 0x49, 0x55, 0x4d, 0x5f, 0x53, 0x55, 0x4c, 0x46, 0x41, 0x54, 0x45, 0x10, 0xa3, 0x02,
	0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x4f, 0x46, 0x54, 0x57, 0x41, 0x52, 0x45, 0x10, 0xa4, 0x02,
	0x12, 0x28, 0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x4f, 0x4c, 0x41, 0x52, 0x5f, 0x50, 0x41, 0x4e, 0x45, 0x4c,
	0x53, 0x5f, 0x43, 0x45, 0x4c, 0x4c, 0x53, 0x10, 0xa5, 0x02, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53,
	0x50, 0x4f, 0x52, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x10, 0xa6, 0x02,
	0x12, 0x33, 0x0a, 0x2e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x45, 0x45, 0x4c, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x43,
	0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x45, 0x54, 0x41,
	0x4c, 0x53, 0x10, 0xa7, 0x02, 0x12, 0x27, 0x0a, 0x22, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47,
	0x45, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x10, 0xa8, 0x02, 0x12, 0x1b,
	0x0a, 0x16, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x53, 0x55, 0x45, 0x44, 0x45, 0x10, 0xa9, 0x02, 0x12, 0x1b, 0x0a, 0x16, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x53, 0x55, 0x47, 0x41, 0x52, 0x10, 0xaa, 0x02, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x55, 0x47,
	0x41, 0x52, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x44, 0x10, 0xab, 0x02, 0x12, 0x23, 0x0a, 0x1e,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x53, 0x57, 0x49, 0x4e, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x45, 0x41, 0x54, 0x10, 0xac,
	0x02, 0x12, 0x34, 0x0a, 0x2f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x45, 0x4c, 0x45, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x50,
	0x4f, 0x4c, 0x45, 0x53, 0x10, 0xad, 0x02, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x45, 0x58, 0x54,
	0x49, 0x4c, 0x45, 0x53, 0x10, 0xae, 0x02, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f,
	0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x49, 0x52, 0x45,
	0x53, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0xaf, 0x02, 0x12, 0x21, 0x0a,
	0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x54, 0x49, 0x52, 0x45, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x41, 0x50, 0x10, 0xb0, 0x02,
	0x12, 0x2f, 0x0a, 0x2a, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x42, 0x41, 0x43, 0x43, 0x4f, 0x5f, 0x46, 0x49, 0x4e,
	0x49, 0x53, 0x48, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x53, 0x10, 0xb1,
	0x02, 0x12, 0x21, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x42, 0x41, 0x43, 0x43, 0x4f, 0x5f, 0x52, 0x41,
	0x57, 0x10, 0xb2, 0x02, 0x12, 0x32, 0x0a, 0x2d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52,
	0x53, 0x5f, 0x41, 0x53, 0x5f, 0x43, 0x41, 0x52, 0x47, 0x4f, 0x5f, 0x4f, 0x4e, 0x5f, 0x46, 0x4c,
	0x41, 0x54, 0x42, 0x45, 0x44, 0x10, 0xb3, 0x02, 0x12, 0x3c, 0x0a, 0x37, 0x43, 0x4f, 0x4d, 0x4d,
	0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x52, 0x41,
	0x49, 0x4c, 0x45, 0x52, 0x53, 0x5f, 0x41, 0x53, 0x5f, 0x43, 0x41, 0x52, 0x47, 0x4f, 0x5f, 0x50,
	0x55, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x55,
	0x4e, 0x49, 0x54, 0x10, 0xb4, 0x02, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x56, 0x5f, 0x41, 0x4e,
	0x44, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x10, 0xb5, 0x02, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x41, 0x10, 0xb6, 0x02, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x42, 0x10, 0xb7, 0x02,
	0x12, 0x29, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x10, 0xb8, 0x02, 0x12, 0x29, 0x0a, 0x24, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x44, 0x10, 0xb9, 0x02, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44,
	0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x45, 0x10, 0xba,
	0x02, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x43, 0x4f,
	0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x10, 0xbb, 0x02, 0x12, 0x21, 0x0a, 0x1c,
	0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44,
	0x5f, 0x55, 0x52, 0x41, 0x4e, 0x49, 0x55, 0x4d, 0x5f, 0x4f, 0x52, 0x45, 0x10, 0xbc, 0x02, 0x12,
	0x23, 0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x55, 0x52, 0x41, 0x4e, 0x49, 0x55, 0x4d, 0x5f, 0x4f, 0x58, 0x49, 0x44,
	0x45, 0x10, 0xbd, 0x02, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54,
	0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x56, 0x41, 0x4e, 0x53, 0x5f, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45, 0x44, 0x10, 0xbe, 0x02, 0x12, 0x23, 0x0a, 0x1e, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f,
	0x56, 0x45, 0x47, 0x45, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4f, 0x49, 0x4c, 0x10, 0xbf, 0x02,
	0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41,
	0x55, 0x4c, 0x45, 0x44, 0x5f, 0x56, 0x45, 0x4c, 0x56, 0x45, 0x54, 0x10, 0xc0, 0x02, 0x12, 0x21,
	0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c,
	0x45, 0x44, 0x5f, 0x56, 0x49, 0x44, 0x45, 0x4f, 0x5f, 0x47, 0x41, 0x4d, 0x45, 0x53, 0x10, 0xc1,
	0x02, 0x12, 0x2a, 0x0a, 0x25, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48,
	0x41, 0x55, 0x4c, 0x45, 0x44, 0x5f, 0x57, 0x41, 0x53, 0x54, 0x45, 0x5f, 0x57, 0x41, 0x54, 0x45,
	0x52, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x46, 0x49, 0x4c, 0x4c, 0x10, 0xc2, 0x02, 0x12, 0x20, 0x0a,
	0x1b, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x45,
	0x44, 0x5f, 0x57, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x57, 0x45, 0x4c, 0x4c, 0x10, 0xc3, 0x02, 0x12,
	0x1a, 0x0a, 0x15, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x41, 0x55,
	0x4c, 0x45, 0x44, 0x5f, 0x57, 0x4f, 0x4f, 0x4c, 0x10, 0xc4, 0x02, 0x2a, 0x90, 0x02, 0x0a, 0x16,
	0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x29, 0x0a, 0x25, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53,
	0x5f, 0x4f, 0x46, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41,
	0x4e, 0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x2b, 0x0a, 0x27, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x5a,
	0x45, 0x52, 0x4f, 0x5f, 0x54, 0x4f, 0x5f, 0x46, 0x49, 0x46, 0x54, 0x59, 0x10, 0x01, 0x12, 0x32,
	0x0a, 0x2e, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f, 0x50, 0x45, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x46, 0x54,
	0x59, 0x5f, 0x54, 0x4f, 0x5f, 0x54, 0x57, 0x4f, 0x5f, 0x48, 0x55, 0x4e, 0x44, 0x52, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x39, 0x0a, 0x35, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f,
	0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f,
	0x54, 0x57, 0x4f, 0x5f, 0x48, 0x55, 0x4e, 0x44, 0x52, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x46,
	0x49, 0x56, 0x45, 0x5f, 0x48, 0x55, 0x4e, 0x44, 0x52, 0x45, 0x44, 0x10, 0x03, 0x12, 0x2f, 0x0a,
	0x2b, 0x52, 0x41, 0x44, 0x49, 0x55, 0x53, 0x5f, 0x4f, 0x46, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x56, 0x45, 0x5f,
	0x48, 0x55, 0x4e, 0x44, 0x52, 0x45, 0x44, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x10, 0x04, 0x2a, 0xc0,
	0x01, 0x0a, 0x0b, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c,
	0x0a, 0x18, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41,
	0x43, 0x54, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x55, 0x43, 0x4b, 0x10, 0x02, 0x12, 0x18,
	0x0a, 0x14, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54,
	0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x50, 0x41, 0x52, 0x45, 0x5f, 0x54,
	0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x10, 0x04, 0x12, 0x27, 0x0a, 0x23, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x4f, 0x57, 0x4e,
	0x45, 0x44, 0x5f, 0x53, 0x45, 0x4d, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x10,
	0x05, 0x2a, 0xe3, 0x01, 0x0a, 0x12, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x57, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x20, 0x56, 0x45, 0x48, 0x49,
	0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e,
	0x0a, 0x1a, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x10, 0x01, 0x12, 0x1f,
	0x0a, 0x1b, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d, 0x10, 0x02, 0x12,
	0x1e, 0x0a, 0x1a, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48,
	0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x10, 0x03, 0x12,
	0x24, 0x0a, 0x20, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48,
	0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x5f, 0x48, 0x45,
	0x41, 0x56, 0x59, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45,
	0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x54, 0x52,
	0x41, 0x49, 0x4c, 0x45, 0x52, 0x10, 0x05, 0x2a, 0x9f, 0x02, 0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x1b, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x44,
	0x52, 0x59, 0x5f, 0x56, 0x41, 0x4e, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x50, 0x45, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x52, 0x45, 0x46, 0x52,
	0x49, 0x47, 0x45, 0x52, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x46, 0x4c,
	0x41, 0x54, 0x42, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4d, 0x4f, 0x44, 0x41, 0x4c, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x54, 0x41, 0x4e, 0x4b, 0x45,
	0x52, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x48, 0x41, 0x5a, 0x4d, 0x41, 0x54, 0x10, 0x06, 0x12,
	0x1e, 0x0a, 0x1a, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41,
	0x53, 0x53, 0x5f, 0x48, 0x45, 0x41, 0x56, 0x59, 0x5f, 0x48, 0x41, 0x55, 0x4c, 0x10, 0x07, 0x12,
	0x18, 0x0a, 0x14, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41,
	0x53, 0x53, 0x5f, 0x44, 0x55, 0x4d, 0x50, 0x10, 0x08, 0x2a, 0xfa, 0x02, 0x0a, 0x24, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x54, 0x68, 0x65,
	0x66, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x12, 0x38, 0x0a, 0x34, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50,
	0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x48, 0x45, 0x46, 0x54, 0x5f, 0x50, 0x52, 0x4f,
	0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x35, 0x0a, 0x31,
	0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45,
	0x5f, 0x54, 0x48, 0x45, 0x46, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x4e, 0x4f, 0x5f, 0x41, 0x4c, 0x41, 0x52,
	0x4d, 0x10, 0x01, 0x12, 0x38, 0x0a, 0x34, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f,
	0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x48, 0x45, 0x46, 0x54, 0x5f, 0x50, 0x52,
	0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f,
	0x4c, 0x4f, 0x43, 0x41, 0x4c, 0x5f, 0x41, 0x4c, 0x41, 0x52, 0x4d, 0x10, 0x02, 0x12, 0x42, 0x0a,
	0x3e, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54,
	0x45, 0x5f, 0x54, 0x48, 0x45, 0x46, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x43, 0x45, 0x4e, 0x54, 0x52, 0x41,
	0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4c, 0x41, 0x52, 0x4d, 0x10,
	0x03, 0x12, 0x63, 0x0a, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x52,
	0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x48, 0x45, 0x46, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x54,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x43, 0x45,
	0x4e, 0x54, 0x52, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4c,
	0x41, 0x52, 0x4d, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e,
	0x41, 0x4c, 0x5f, 0x54, 0x48, 0x45, 0x46, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x53, 0x10, 0x04, 0x2a, 0x8d, 0x03, 0x0a, 0x23, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x46, 0x69, 0x72, 0x65, 0x50, 0x72,
	0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x37,
	0x0a, 0x33, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41,
	0x54, 0x45, 0x5f, 0x46, 0x49, 0x52, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x49, 0x0a, 0x45, 0x54, 0x45, 0x52, 0x4d, 0x49,
	0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x49, 0x52, 0x45,
	0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x53, 0x54,
	0x45, 0x4d, 0x5f, 0x4e, 0x4f, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x4d, 0x41, 0x54, 0x49, 0x43, 0x5f,
	0x46, 0x49, 0x52, 0x45, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x52, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e,
	0x10, 0x01, 0x12, 0x42, 0x0a, 0x3e, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50,
	0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x49, 0x52, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x54,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x49, 0x4e,
	0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x52, 0x45, 0x53,
	0x53, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x40, 0x0a, 0x3c, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e,
	0x41, 0x4c, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x49, 0x52, 0x45, 0x5f,
	0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45,
	0x4d, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x52,
	0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x5c, 0x0a, 0x58, 0x54, 0x45, 0x52, 0x4d,
	0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x49, 0x52,
	0x45, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x53,
	0x54, 0x45, 0x4d, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x4d, 0x41, 0x54, 0x49, 0x43, 0x5f, 0x53, 0x55,
	0x50, 0x50, 0x52, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x41, 0x44,
	0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x53, 0x10, 0x04, 0x2a, 0xad, 0x03, 0x0a, 0x19, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x12, 0x2b, 0x0a, 0x27, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c,
	0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c,
	0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x25, 0x0a, 0x21, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53,
	0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x2f, 0x0a, 0x2b, 0x54, 0x45, 0x52, 0x4d,
	0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x4a, 0x4f, 0x49, 0x53, 0x54, 0x45, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4f, 0x4e, 0x52, 0x59, 0x10, 0x02, 0x12, 0x2f, 0x0a, 0x2b, 0x54, 0x45, 0x52,
	0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4d,
	0x42, 0x55, 0x53, 0x54, 0x49, 0x42, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x37, 0x0a, 0x33, 0x54, 0x45,
	0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x4d, 0x41, 0x53, 0x4f, 0x4e, 0x52,
	0x59, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x42, 0x55, 0x53, 0x54, 0x49, 0x42, 0x4c,
	0x45, 0x10, 0x04, 0x12, 0x37, 0x0a, 0x33, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f,
	0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41,
	0x53, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x46, 0x49, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x53, 0x49, 0x53, 0x54, 0x49, 0x56, 0x45, 0x10, 0x05, 0x12, 0x2e, 0x0a, 0x2a,
	0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x46, 0x49, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x53, 0x49, 0x53, 0x54, 0x49, 0x56, 0x45, 0x10, 0x06, 0x12, 0x38, 0x0a, 0x34,
	0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x45,
	0x5f, 0x4f, 0x46, 0x5f, 0x41, 0x42, 0x4f, 0x56, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x07, 0x2a, 0xe2, 0x03, 0x0a, 0x1d, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x30, 0x0a, 0x2c, 0x54, 0x45, 0x52, 0x4d,
	0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x54,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x45,
	0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52,
	0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x31,
	0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50,
	0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x32, 0x10, 0x02, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x45,
	0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52,
	0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x33,
	0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50,
	0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x34, 0x10, 0x04, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x45,
	0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52,
	0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x35,
	0x10, 0x05, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50,
	0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x36, 0x10, 0x06, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x45,
	0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52,
	0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x37,
	0x10, 0x07, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50,
	0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x38, 0x10, 0x08, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x45,
	0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52,
	0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x39,
	0x10, 0x09, 0x12, 0x27, 0x0a, 0x23, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x50,
	0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x31, 0x30, 0x10, 0x0a, 0x2a, 0xca, 0x01, 0x0a, 0x18,
	0x4e, 0x65, 0x67, 0x6f, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x52, 0x61, 0x74, 0x65, 0x73, 0x45,
	0x78, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x26, 0x4e, 0x45, 0x47, 0x4f,
	0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x45,
	0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x4e, 0x45, 0x47, 0x4f, 0x54, 0x49, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x45, 0x4d, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x46, 0x54, 0x45, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26,
	0x4e, 0x45, 0x47, 0x4f, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x53,
	0x5f, 0x45, 0x58, 0x45, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x41, 0x52, 0x47, 0x45,
	0x5f, 0x46, 0x4c, 0x45, 0x45, 0x54, 0x10, 0x02, 0x12, 0x2e, 0x0a, 0x2a, 0x4e, 0x45, 0x47, 0x4f,
	0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x45,
	0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x4f, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10, 0x03, 0x2a, 0x77, 0x0a, 0x11, 0x4c, 0x69, 0x63, 0x65,
	0x6e, 0x73, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a,
	0x1f, 0x4c, 0x49, 0x43, 0x45, 0x4e, 0x53, 0x45, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x49, 0x43, 0x45, 0x4e, 0x53, 0x45, 0x5f, 0x48, 0x4f,
	0x4c, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x43, 0x59,
	0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x49, 0x43, 0x45, 0x4e, 0x53, 0x45, 0x5f, 0x48, 0x4f,
	0x4c, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x10,
	0x02, 0x2a, 0x81, 0x01, 0x0a, 0x10, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x4d,
	0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x52, 0x10,
	0x01, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x46,
	0x55, 0x4c, 0x4c, 0x10, 0x02, 0x2a, 0x94, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x26,
	0x0a, 0x22, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27, 0x0a, 0x23, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49,
	0x54, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44,
	0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x10, 0x01, 0x12,
	0x2b, 0x0a, 0x27, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x4c, 0x45, 0x54, 0x54, 0x45, 0x52,
	0x5f, 0x4f, 0x46, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x02, 0x2a, 0xdd, 0x02, 0x0a,
	0x17, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x64, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x44, 0x44, 0x49,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x3e, 0x0a, 0x3a, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41,
	0x4c, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x48, 0x41, 0x5a, 0x41, 0x52, 0x44, 0x4f, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x54, 0x45, 0x52,
	0x49, 0x41, 0x4c, 0x53, 0x5f, 0x49, 0x4e, 0x43, 0x4c, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f,
	0x39, 0x10, 0x01, 0x12, 0x3e, 0x0a, 0x3a, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41,
	0x4c, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4c, 0x49, 0x46, 0x54, 0x5f, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x57, 0x48,
	0x49, 0x54, 0x45, 0x5f, 0x47, 0x4c, 0x4f, 0x56, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43,
	0x45, 0x10, 0x02, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41,
	0x4c, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x4c,
	0x49, 0x56, 0x45, 0x52, 0x59, 0x10, 0x03, 0x12, 0x37, 0x0a, 0x33, 0x41, 0x44, 0x44, 0x49, 0x54,
	0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4f, 0x55, 0x42, 0x4c, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x54,
	0x52, 0x49, 0x50, 0x4c, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4c, 0x45, 0x52, 0x53, 0x10, 0x04,
	0x12, 0x2a, 0x0a, 0x26, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x43,
	0x4f, 0x4d, 0x4d, 0x4f, 0x44, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x45,
	0x41, 0x54, 0x5f, 0x4f, 0x4e, 0x5f, 0x48, 0x4f, 0x4f, 0x4b, 0x10, 0x05, 0x42, 0x25, 0x5a, 0x23,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_fleet_model_program_data_proto_rawDescOnce sync.Once
	file_fleet_model_program_data_proto_rawDescData = file_fleet_model_program_data_proto_rawDesc
)

func file_fleet_model_program_data_proto_rawDescGZIP() []byte {
	file_fleet_model_program_data_proto_rawDescOnce.Do(func() {
		file_fleet_model_program_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_fleet_model_program_data_proto_rawDescData)
	})
	return file_fleet_model_program_data_proto_rawDescData
}

var file_fleet_model_program_data_proto_enumTypes = make([]protoimpl.EnumInfo, 16)
var file_fleet_model_program_data_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_fleet_model_program_data_proto_goTypes = []interface{}{
	(TypeOfTerminal)(0),                       // 0: fleet_model.TypeOfTerminal
	(CommodityCategory)(0),                    // 1: fleet_model.CommodityCategory
	(CommodityName)(0),                        // 2: fleet_model.CommodityName
	(RadiusOfOperationRange)(0),               // 3: fleet_model.RadiusOfOperationRange
	(VehicleType)(0),                          // 4: fleet_model.VehicleType
	(VehicleWeightClass)(0),                   // 5: fleet_model.VehicleWeightClass
	(OperationClass)(0),                       // 6: fleet_model.OperationClass
	(TerminalPrivateTheftProtectionSystem)(0), // 7: fleet_model.TerminalPrivateTheftProtectionSystem
	(TerminalPrivateFireProtectionSystem)(0),  // 8: fleet_model.TerminalPrivateFireProtectionSystem
	(TerminalConstructionClass)(0),            // 9: fleet_model.TerminalConstructionClass
	(TerminalPublicProtectionClass)(0),        // 10: fleet_model.TerminalPublicProtectionClass
	(NegotiatedRatesExemption)(0),             // 11: fleet_model.NegotiatedRatesExemption
	(LicenseHolderType)(0),                    // 12: fleet_model.LicenseHolderType
	(PaymentFrequency)(0),                     // 13: fleet_model.PaymentFrequency
	(DepositPaymentMethod)(0),                 // 14: fleet_model.DepositPaymentMethod
	(AdditionalCommodityType)(0),              // 15: fleet_model.AdditionalCommodityType
	(*FleetProgramData)(nil),                  // 16: fleet_model.FleetProgramData
	(*Company)(nil),                           // 17: fleet_model.Company
	(*TerminalLocation)(nil),                  // 18: fleet_model.TerminalLocation
	(*Operation)(nil),                         // 19: fleet_model.Operation
	(*OperationClassRecord)(nil),              // 20: fleet_model.OperationClassRecord
	(*RadiusOfOperationRecord)(nil),           // 21: fleet_model.RadiusOfOperationRecord
	(*VehicleZoneRecord)(nil),                 // 22: fleet_model.VehicleZoneRecord
	(*TaxRecord)(nil),                         // 23: fleet_model.TaxRecord
	(*TaxRecordLineOfBusiness)(nil),           // 24: fleet_model.TaxRecordLineOfBusiness
	(*BrokerLicense)(nil),                     // 25: fleet_model.BrokerLicense
	(*Billing)(nil),                           // 26: fleet_model.Billing
	(*CameraSubsidy)(nil),                     // 27: fleet_model.CameraSubsidy
	(*ScheduleModification)(nil),              // 28: fleet_model.ScheduleModification
	(*Driver)(nil),                            // 29: fleet_model.Driver
	(*Equipment)(nil),                         // 30: fleet_model.Equipment
	(*Vehicle)(nil),                           // 31: fleet_model.Vehicle
	(*LossHistory)(nil),                       // 32: fleet_model.LossHistory
	(*LossSummaryRecords)(nil),                // 33: fleet_model.LossSummaryRecords
	(*LossSummary)(nil),                       // 34: fleet_model.LossSummary
	(*LargeLoss)(nil),                         // 35: fleet_model.LargeLoss
	(*NegotiatedRates)(nil),                   // 36: fleet_model.NegotiatedRates
	(*NegotiatedRatesRule)(nil),               // 37: fleet_model.NegotiatedRatesRule
	(*CommodityDetails)(nil),                  // 38: fleet_model.CommodityDetails
	(*CommodityDistribution)(nil),             // 39: fleet_model.CommodityDistribution
	(*WeightedCommodityRecord)(nil),           // 40: fleet_model.WeightedCommodityRecord
	(*Commodity)(nil),                         // 41: fleet_model.Commodity
	(*AdditionalCommodities)(nil),             // 42: fleet_model.AdditionalCommodities
	nil,                                       // 43: fleet_model.LossHistory.LossSummariesEntry
	(*proto.Address)(nil),                     // 44: common.Address
	(*proto.Interval)(nil),                    // 45: common.Interval
	(*timestamppb.Timestamp)(nil),             // 46: google.protobuf.Timestamp
}
var file_fleet_model_program_data_proto_depIdxs = []int32{
	17, // 0: fleet_model.FleetProgramData.company:type_name -> fleet_model.Company
	19, // 1: fleet_model.FleetProgramData.operation:type_name -> fleet_model.Operation
	29, // 2: fleet_model.FleetProgramData.drivers:type_name -> fleet_model.Driver
	30, // 3: fleet_model.FleetProgramData.equipment:type_name -> fleet_model.Equipment
	32, // 4: fleet_model.FleetProgramData.lossHistory:type_name -> fleet_model.LossHistory
	36, // 5: fleet_model.FleetProgramData.negotiatedRates:type_name -> fleet_model.NegotiatedRates
	25, // 6: fleet_model.FleetProgramData.brokerLicense:type_name -> fleet_model.BrokerLicense
	27, // 7: fleet_model.FleetProgramData.cameraSubsidy:type_name -> fleet_model.CameraSubsidy
	26, // 8: fleet_model.FleetProgramData.billing:type_name -> fleet_model.Billing
	28, // 9: fleet_model.FleetProgramData.scheduleModification:type_name -> fleet_model.ScheduleModification
	38, // 10: fleet_model.FleetProgramData.commodityDetails:type_name -> fleet_model.CommodityDetails
	18, // 11: fleet_model.Company.terminalLocations:type_name -> fleet_model.TerminalLocation
	44, // 12: fleet_model.Company.mailingAddress:type_name -> common.Address
	23, // 13: fleet_model.Company.taxRecords:type_name -> fleet_model.TaxRecord
	44, // 14: fleet_model.TerminalLocation.address:type_name -> common.Address
	0,  // 15: fleet_model.TerminalLocation.typeOfTerminal:type_name -> fleet_model.TypeOfTerminal
	9,  // 16: fleet_model.TerminalLocation.constructionClass:type_name -> fleet_model.TerminalConstructionClass
	10, // 17: fleet_model.TerminalLocation.publicProtectionClass:type_name -> fleet_model.TerminalPublicProtectionClass
	7,  // 18: fleet_model.TerminalLocation.privateTheftProtection:type_name -> fleet_model.TerminalPrivateTheftProtectionSystem
	8,  // 19: fleet_model.TerminalLocation.privateFireProtection:type_name -> fleet_model.TerminalPrivateFireProtectionSystem
	6,  // 20: fleet_model.Operation.primaryOperationClass:type_name -> fleet_model.OperationClass
	20, // 21: fleet_model.Operation.operationClassRecords:type_name -> fleet_model.OperationClassRecord
	21, // 22: fleet_model.Operation.radiusOfOperationRecords:type_name -> fleet_model.RadiusOfOperationRecord
	22, // 23: fleet_model.Operation.vehicleZoneRecords:type_name -> fleet_model.VehicleZoneRecord
	6,  // 24: fleet_model.OperationClassRecord.operationClass:type_name -> fleet_model.OperationClass
	3,  // 25: fleet_model.RadiusOfOperationRecord.radiusOfOperationRange:type_name -> fleet_model.RadiusOfOperationRange
	24, // 26: fleet_model.TaxRecord.linesOfBusiness:type_name -> fleet_model.TaxRecordLineOfBusiness
	12, // 27: fleet_model.BrokerLicense.licenseHolderType:type_name -> fleet_model.LicenseHolderType
	44, // 28: fleet_model.BrokerLicense.address:type_name -> common.Address
	45, // 29: fleet_model.BrokerLicense.licenseEffectiveInterval:type_name -> common.Interval
	13, // 30: fleet_model.Billing.paymentFrequency:type_name -> fleet_model.PaymentFrequency
	14, // 31: fleet_model.Billing.depositPaymentMethod:type_name -> fleet_model.DepositPaymentMethod
	46, // 32: fleet_model.Driver.dateOfHire:type_name -> google.protobuf.Timestamp
	46, // 33: fleet_model.Driver.dateOfBirth:type_name -> google.protobuf.Timestamp
	31, // 34: fleet_model.Equipment.vehicles:type_name -> fleet_model.Vehicle
	43, // 35: fleet_model.LossHistory.lossSummaries:type_name -> fleet_model.LossHistory.LossSummariesEntry
	35, // 36: fleet_model.LossHistory.largeLosses:type_name -> fleet_model.LargeLoss
	34, // 37: fleet_model.LossSummaryRecords.lossSummaries:type_name -> fleet_model.LossSummary
	45, // 38: fleet_model.LossSummary.policyInterval:type_name -> common.Interval
	46, // 39: fleet_model.LargeLoss.lossDate:type_name -> google.protobuf.Timestamp
	37, // 40: fleet_model.NegotiatedRates.rules:type_name -> fleet_model.NegotiatedRatesRule
	11, // 41: fleet_model.NegotiatedRates.exemption:type_name -> fleet_model.NegotiatedRatesExemption
	11, // 42: fleet_model.NegotiatedRatesRule.ruleType:type_name -> fleet_model.NegotiatedRatesExemption
	15, // 43: fleet_model.CommodityDetails.additionalCommodityType:type_name -> fleet_model.AdditionalCommodityType
	2,  // 44: fleet_model.CommodityDetails.primaryCommodity:type_name -> fleet_model.CommodityName
	1,  // 45: fleet_model.CommodityDetails.primaryCommodityCategory:type_name -> fleet_model.CommodityCategory
	39, // 46: fleet_model.CommodityDetails.commodityDistribution:type_name -> fleet_model.CommodityDistribution
	40, // 47: fleet_model.CommodityDistribution.weightedCommodityRecords:type_name -> fleet_model.WeightedCommodityRecord
	42, // 48: fleet_model.CommodityDistribution.additionalCommodities:type_name -> fleet_model.AdditionalCommodities
	1,  // 49: fleet_model.WeightedCommodityRecord.commodityCategory:type_name -> fleet_model.CommodityCategory
	41, // 50: fleet_model.WeightedCommodityRecord.commodity:type_name -> fleet_model.Commodity
	2,  // 51: fleet_model.Commodity.commodityName:type_name -> fleet_model.CommodityName
	33, // 52: fleet_model.LossHistory.LossSummariesEntry.value:type_name -> fleet_model.LossSummaryRecords
	53, // [53:53] is the sub-list for method output_type
	53, // [53:53] is the sub-list for method input_type
	53, // [53:53] is the sub-list for extension type_name
	53, // [53:53] is the sub-list for extension extendee
	0,  // [0:53] is the sub-list for field type_name
}

func init() { file_fleet_model_program_data_proto_init() }
func file_fleet_model_program_data_proto_init() {
	if File_fleet_model_program_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_fleet_model_program_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetProgramData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Company); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TerminalLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Operation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationClassRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RadiusOfOperationRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleZoneRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaxRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaxRecordLineOfBusiness); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrokerLicense); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Billing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraSubsidy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScheduleModification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Driver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Equipment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Vehicle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LossHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LossSummaryRecords); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LossSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LargeLoss); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NegotiatedRates); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NegotiatedRatesRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommodityDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommodityDistribution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeightedCommodityRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Commodity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fleet_model_program_data_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalCommodities); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_fleet_model_program_data_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_fleet_model_program_data_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_fleet_model_program_data_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_fleet_model_program_data_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_fleet_model_program_data_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_fleet_model_program_data_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_fleet_model_program_data_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_fleet_model_program_data_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_fleet_model_program_data_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_fleet_model_program_data_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_fleet_model_program_data_proto_msgTypes[22].OneofWrappers = []interface{}{}
	file_fleet_model_program_data_proto_msgTypes[25].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_fleet_model_program_data_proto_rawDesc,
			NumEnums:      16,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_fleet_model_program_data_proto_goTypes,
		DependencyIndexes: file_fleet_model_program_data_proto_depIdxs,
		EnumInfos:         file_fleet_model_program_data_proto_enumTypes,
		MessageInfos:      file_fleet_model_program_data_proto_msgTypes,
	}.Build()
	File_fleet_model_program_data_proto = out.File
	file_fleet_model_program_data_proto_rawDesc = nil
	file_fleet_model_program_data_proto_goTypes = nil
	file_fleet_model_program_data_proto_depIdxs = nil
}
