package backfill_forms_v2_db

import (
	"nirvanatech.com/nirvana/forms/model"
)

var PolicyNumberFieldAL = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.PolicyNumbers.CoverageAutoLiability",
	},
	Validation: model.Validation{
		Required: true,
	},
}

var PolicyNumberFieldMTC = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.PolicyNumbers.CoverageMotorTruckCargo",
	},
	Validation: model.Validation{
		Required: false,
	},
}

var InsuranceCarrierOnFormFooterField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.InsuranceCarrierOnFormFooter",
	},
}

var CheckboxCheckedField = model.Field{
	Source: model.Source{
		Key: "Constants.CheckboxChecked",
	},
}

var CheckboxUncheckedField = model.Field{
	Source: model.Source{
		Key: "Constants.CheckboxUnchecked",
	},
}

var SingleLimitBIAndPDField = model.Field{
	Source: model.Source{
		Key: "Constants.SingleLimitBIAndPD",
	},
}

var InsuranceCarrierField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.InsuranceCarrier",
	},
}

var InsuranceCarrierMailingAddressField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.InsuranceCarrierMailingAddress",
	},
}

var AcceptanceOfTerrorismCoverageField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.AcceptanceOfTerrorismCoverage",
	},
	Validation: model.Validation{
		Required: true,
	},
}

var DeclineOfTerrorismCoverageField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.DeclineOfTerrorismCoverage",
	},
	Validation: model.Validation{
		Required: true,
	},
}

var CoverageEarnedFreightLimitField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageEarnedFreight.Limit",
	},
}

var PolicyEffectiveDateField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.PolicyPeriod.From.Date",
	},
}

var AgentNameField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.AgentName",
	},
}

var AgentNumberField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.AgentNumber",
	},
}

var PolicyExpirationDateField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.PolicyPeriod.To.Date",
	},
}

var InsuredNameField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CompanyInfo.Name",
	},
}

var InsuredNamePlusDBAField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CompanyInfo.CompanyNamePlusDBA",
	},
}

var PhysicalAddressField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CompanyInfo.Address.PhysicalAddress",
	},
}

var CarrierNameField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.InsuranceCarrier",
	},
}

var PolicyNumberFieldGL = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.PolicyNumbers.CoverageGeneralLiability",
	},
}

var CoveragePollutantCleanupAndRemovalLimitField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoveragePollutantCleanupAndRemoval.Limit",
	},
}

var CoverageMiscellaneousEquipmentLimitField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageMiscellaneousEquipment.Limit",
	},
}

var CoverageDebrisRemovalLimitField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageDebrisRemoval.Limit",
	},
}

var MTCDeductibleField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.Deductible",
	},
}

var MTCLimitField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.Limit",
	},
}

var MTCPremiumPerHundredMilesField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.PremiumPerHundredMiles",
	},
}

var MTCPremiumField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.Premium",
	},
}

var BlanketAppliesField = model.Field{
	Source: model.Source{
		Key: "Constants.BlanketApplies",
	},
}

var NumberOfDaysNoticeNonpaymentField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.NumberOfDaysNoticeNonpayment",
	},
}

var NumberOfDaysNoticeCancellationField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.NumberOfDaysNoticeCancellation",
	},
}

var MTCSurplusLinesTax = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.SurplusLinesTax",
	},
}

var MTCStampingFee = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageMotorTruckCargo.StampingFee",
	},
}

var Date = model.Field{
	Source: model.Source{
		Key: "ComputedFields.Date",
	},
}

var NumberOfCamerasField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.CameraSubsidyDetails.NumberOfCameras",
	},
}

var CameraSubsidyAmountField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.CameraSubsidyDetails.CameraSubsidyAmount",
	},
}

var CameraProviderMotiveField = model.Field{
	Source: model.Source{
		Key: "Constants.CameraProviderMotive",
	},
}

var CameraProviderSamsaraField = model.Field{
	Source: model.Source{
		Key: "Constants.CameraProviderSamsara",
	},
}

var CargoTILimitOfInsuranceField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageCargoTrailerInterchange.Limit",
	},
}

var CargoTIDeductibleField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageCargoTrailerInterchange.Deductible",
	},
}

var ComputedReferToTerminalScheduleField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.ReferToTerminalSchedule",
	},
}

var ReeferBreakdownField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoverageReeferWithoutHumanErrorPresent",
	},
}

var ReeferBreakdownWithHumanErrorField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoverageReeferWithHumanErrorPresent",
	},
}

var ReeferLimitPerVehicleField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.ReeferLimitPerVehicle",
	},
}

var ReeferBreakdownDeductibleField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.ReeferBreakdownDeductible",
	},
}

var InsuranceProducerField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.InsuranceProducer",
	},
}

var InsuranceProducerMailingAddressField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.InsuranceProducerMailingAddress",
	},
}

var UMUIMCheckboxField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.UMUIMCheckbox",
	},
}

var LimitOfInsuranceUMField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.LimitOfInsuranceUM",
	},
}

var BrokerField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.Broker",
	},
}

var BrokerMailingAddressField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.BrokerMailingAddress",
	},
}

var CoverageLossMitigationExpensesLimitField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageLossMitigationExpenses.Limit",
	},
}

var FormsOfBusinessCorporationField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CompanyInfo.FormOfBusinessCorporation",
	},
}

var FormsOfBusinessLLCField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CompanyInfo.FormsOfBusinessLLC",
	},
}

var TotalPremium = model.Field{
	Source: model.Source{
		Key: "ComputedFields.PolicyInfo.TotalPremium",
	},
}

var LimitOfInsuranceUMPDField = model.Field{
	Source: model.Source{
		Key: "ComputedFields.CoveragesInfo.CoverageUninsuredMotoristPropertyDamage.Limit",
	},
}
