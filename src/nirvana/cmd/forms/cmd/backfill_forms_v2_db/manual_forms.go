package backfill_forms_v2_db

import (
	"context"
	"encoding/json"
	"reflect"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/hyperjumptech/grule-rule-engine/pkg"
	"github.com/spf13/cobra"

	"nirvanatech.com/nirvana/api-server/handlers/forms"
	"nirvanatech.com/nirvana/api-server/handlers/forms/transformer"
	"nirvanatech.com/nirvana/api-server/interceptors/forms/deps"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	formenums "nirvanatech.com/nirvana/db-api/db_wrappers/forms/enums"
	policyEnums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/forms/util"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
	oapi_forms "nirvanatech.com/nirvana/openapi-specs/components/forms"
	"nirvanatech.com/nirvana/policy_common/forms_generator/cmd/model"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
)

type LogicalOperator string

const (
	LogicalOperatorAnd LogicalOperator = "and"
	LogicalOperatorOr  LogicalOperator = "or"
)

type conditionalRuleInput struct {
	when map[string]interface{}
	// logicalOperator is optional and used to determine how the `when` conditions are combined
	// with the existing conditions in the form schedule.
	logicalOperator           *LogicalOperator
	scheduleTypeToAdd         *compilation.ScheduleType // Added only to CompilationTypePolicy
	applicableCoveragePackage map[string]model.PackageType
	programType               *policyEnums.ProgramType
	statesIncluded            []string
	formTemplateType          *formenums.FormTemplateType
}

const manualFormsV2DB = "manualFormsV2Db"

var ManualFormsV2DBCmd = cobrafx.NewCLI(
	&cobra.Command{
		Use:   manualFormsV2DB,
		Short: "Manual forms v2 db",
	},
	runManualFormsBackfill,
)

func runManualFormsBackfill(cmd *cobra.Command, _ []string, e env) error {
	ctx := cmd.Context()
	log.Info(ctx, "Starting CLI job")
	updatedFormCodes, alreadyAdditionalRuleExistFormCodes := make([]string, 0), make([]string, 0)
	for fullFormCode, additionalRule := range commonAdditionalRules {
		updateAdditionalRule(ctx, e.Deps, fullFormCode, additionalRule, &alreadyAdditionalRuleExistFormCodes)
		updatedFormCodes = append(updatedFormCodes, util.GetStandardFormCodeTrimmed(fullFormCode))
	}
	log.Info(ctx, "Updated form codes", log.String("formCodes", strings.Join(updatedFormCodes, ",")))
	log.Info(
		ctx,
		"Already Additional Rule exists for form codes",
		log.String("formCodes", strings.Join(alreadyAdditionalRuleExistFormCodes, ",")),
	)
	log.Info(ctx, "Stopping CLI job")
	return nil
}

func updateAdditionalRule(
	ctx context.Context,
	deps deps.Deps,
	code string,
	update conditionalRuleInput,
	alreadyAdditionalRuleExistFormCodes *[]string,
) {
	// Right now when and schedule type is getting updated in one go
	if update.scheduleTypeToAdd == nil && len(update.when) == 0 &&
		len(update.applicableCoveragePackage) == 0 && update.formTemplateType == nil && update.statesIncluded == nil {
		log.Error(ctx, "Update is empty", log.String("formCode", code))
		return
	}

	fs, err := deps.FormScheduleManager.GetFormScheduleByFullFormCode(ctx, code)
	if err != nil {
		log.Error(ctx, "Failed to get form schedule by full form code",
			log.Err(err),
			log.String("formCode", code))
		return
	}
	if fs == nil {
		log.Error(ctx, "Form schedule is nil",
			log.String("formCode", code))
		return
	}
	ruleIdToBeReset := false
	for i := 0; i < len(fs.ApplicabilityRules); i++ {
		if update.programType != nil &&
			!slice_utils.Contains(fs.ApplicabilityRules[i].CoreRule.ProgramTypes, *update.programType) {
			continue
		}
		if update.scheduleTypeToAdd != nil &&
			!slice_utils.Contains(fs.ApplicabilityRules[i].ScheduleType, *update.scheduleTypeToAdd) {
			if len(fs.ApplicabilityRules[i].CoreRule.FormCompilationTypes) == 1 {
				if fs.ApplicabilityRules[i].CoreRule.FormCompilationTypes[0] == compilation.CompilationTypePolicy &&
					compilation.ScheduleTypeSignedQuoteForms != *update.scheduleTypeToAdd {
					fs.ApplicabilityRules[i].ScheduleType = append(fs.ApplicabilityRules[i].ScheduleType,
						*update.scheduleTypeToAdd)
				}
			} else {
				// No addition of schedule types other than ScheduleTypeSignedQuoteForms to signature packet forms
				fs.ApplicabilityRules[i].CoreRule.FormCompilationTypes = []compilation.CompilationType{compilation.CompilationTypeSignaturePacket}
				clonedApplicabilityRule, err := deepCopy(ctx, fs.ApplicabilityRules[i])
				if err != nil {
					log.Error(ctx, "Failed to deep copy applicability rule",
						log.Err(err),
						log.String("formCode", code),
						log.Int("ruleIndex", i))
					return
				}
				if compilation.ScheduleTypeSignedQuoteForms == *update.scheduleTypeToAdd {
					fs.ApplicabilityRules[i].ScheduleType = append(fs.ApplicabilityRules[i].ScheduleType,
						*update.scheduleTypeToAdd)
				}
				clonedApplicabilityRule.CoreRule.FormCompilationTypes = []compilation.CompilationType{compilation.CompilationTypePolicy}
				fs.ApplicabilityRules = append(fs.ApplicabilityRules, clonedApplicabilityRule)
				ruleIdToBeReset = true
			}
		}

		if len(update.when) != 0 {
			if fs.ApplicabilityRules[i].AdditionalRule == nil {
				fs.ApplicabilityRules[i].AdditionalRule = &pkg.GruleJSON{}
			}

			code = util.GetStandardFormCodeTrimmed(code)
			if fs.ApplicabilityRules[i].AdditionalRule.When != nil {
				existingRule := fs.ApplicabilityRules[i].AdditionalRule.When

				if reflect.DeepEqual(existingRule, update.when) {
					if !slice_utils.Contains(*alreadyAdditionalRuleExistFormCodes, code) {
						log.Info(ctx, "Rule already exists for form, skipping duplicate when condition", log.String("formCode", code))
						*alreadyAdditionalRuleExistFormCodes = append(*alreadyAdditionalRuleExistFormCodes, code)
					}
				} else {
					combinedRule, err := combineRules(ctx, existingRule, update.when, update.logicalOperator)
					if err != nil {
						log.Error(ctx, "Failed to combine rules",
							log.Err(err),
							log.String("formCode", code),
							log.Any("existingRule", existingRule),
							log.Any("newRule", update.when))
						return
					}
					fs.ApplicabilityRules[i].AdditionalRule.When = combinedRule

					if !slice_utils.Contains(*alreadyAdditionalRuleExistFormCodes, code) {
						*alreadyAdditionalRuleExistFormCodes = append(*alreadyAdditionalRuleExistFormCodes, code)
					}
				}
			} else {
				fs.ApplicabilityRules[i].AdditionalRule.When = update.when
			}
			ruleIdToBeReset = true
		}
		if len(update.applicableCoveragePackage) > 0 {
			fs.ApplicabilityRules[i].CoreRule.ApplicableCoveragePackages = update.applicableCoveragePackage
		}
		if update.statesIncluded != nil {
			fs.ApplicabilityRules[i].CoreRule.IncludedStates = update.statesIncluded
		}
	}

	if update.formTemplateType != nil {
		fs.FormTemplateType = *update.formTemplateType
	}

	if ruleIdToBeReset {
		for i := range fs.ApplicabilityRules {
			fs.ApplicabilityRules[i].RuleId = ""
		}
	}

	fsUpdateRequest := oapi_forms.FormScheduleUpdateRequest{}
	oapiRules, err := transformer.GetOAPIApplicabilityRules(ctx, fs.GetApplicabilityRules())
	if err != nil {
		log.Error(ctx, "Failed to get oapi applicability rules",
			log.Err(err),
			log.String("formCode", code),
			log.String("fullFormCode", fs.FullFormCode))
		return
	}
	fsUpdateRequest.ApplicabilityRules = &oapiRules
	fsUpdateRequest.FullFormCode = fs.FullFormCode

	resp := forms.HandlePutFormSchedule(ctx, deps, forms.PutFormScheduleRequest{
		FormScheduleUpdateRequest: fsUpdateRequest,
	})

	if resp.Error != nil {
		log.Error(ctx, "Failed to update form schedule",
			log.Err(resp.Error),
			log.String("formCode", code),
			log.String("fullFormCode", fs.FullFormCode))
		return
	}
}

// extractConditionsForOperator extracts conditions from a rule based on the target operator
// Flattens structures that match the target operator, treats other structures as single conditions
func extractConditionsForOperator(rule any, targetOperator LogicalOperator) []any {
	ruleMap, ok := rule.(map[string]interface{})
	if !ok {
		return []any{rule}
	}

	// Use the operator string directly as the map key
	conditions, hasOperator := ruleMap[string(targetOperator)]
	if !hasOperator {
		return []any{rule}
	}

	conditionsSlice, isSlice := conditions.([]any)
	if !isSlice {
		return []any{rule}
	}

	return conditionsSlice
}

func combineRules(ctx context.Context, existingRule, newRule any, operator *LogicalOperator) (any, error) {
	// Require explicit operator - no default behavior
	if operator == nil {
		return nil, errors.New("logical operator is required for rule combination")
	}

	logicalOp := *operator

	// Extract conditions from both rules using the specified operator
	var conditions []any
	conditions = append(conditions, extractConditionsForOperator(existingRule, logicalOp)...)
	conditions = append(conditions, extractConditionsForOperator(newRule, logicalOp)...)

	// Remove duplicates using deterministic JSON comparison
	uniqueConditions := make([]any, 0, len(conditions))
	seen := make(map[string]bool)

	for _, condition := range conditions {
		conditionBytes, err := json.Marshal(condition)
		if err != nil {
			log.Error(ctx, "Failed to marshal condition", log.Err(err), log.Any("condition", condition))
			return nil, errors.Wrap(err, "failed to marshal condition")
		}

		conditionStr := string(conditionBytes)
		if !seen[conditionStr] {
			seen[conditionStr] = true
			uniqueConditions = append(uniqueConditions, condition)
		}
	}

	// If only one condition, return it directly without wrapping
	if len(uniqueConditions) == 1 {
		return uniqueConditions[0], nil
	}

	// Create structure with the specified operator
	return map[string]interface{}{
		string(logicalOp): uniqueConditions,
	}, nil
}

var IsMTCRatingModelV2 = map[string]interface{}{
	"eq": []interface{}{
		"F.IsMTCRatingModelV2",
		true,
	},
}

var IsNotMTCRatingModelV2 = map[string]interface{}{
	"eq": []interface{}{
		"F.IsMTCRatingModelV2",
		false,
	},
}

var cameraSubsidyConditionKeepTruckin = map[string]interface{}{
	"and": []interface{}{
		map[string]interface{}{
			"gt": []interface{}{
				"F.Camera.Count",
				map[string]interface{}{
					"const": 0.0,
				},
			},
		},
		map[string]interface{}{
			"gt": []interface{}{
				"F.Camera.SubsidyAmount",
				map[string]interface{}{
					"const": 0.0,
				},
			},
		},
		map[string]interface{}{
			"eq": []interface{}{
				map[string]interface{}{
					"obj": "F.TSPProvider",
				},
				map[string]interface{}{
					"const": "TSPKeepTruckin",
				},
			},
		},
	},
}

var cameraSubsidyConditionNotKeepTruckin = map[string]interface{}{
	"and": []interface{}{
		map[string]interface{}{
			"gt": []interface{}{
				"F.Camera.Count",
				map[string]interface{}{
					"const": 0.0,
				},
			},
		},
		map[string]interface{}{
			"gt": []interface{}{
				"F.Camera.SubsidyAmount",
				map[string]interface{}{
					"const": 0.0,
				},
			},
		},
		map[string]interface{}{
			"not": []interface{}{
				map[string]interface{}{
					"obj": "F.TSPProvider",
				},
				map[string]interface{}{
					"const": "",
				},
			},
		},
		map[string]interface{}{
			"not": []interface{}{
				map[string]interface{}{
					"obj": "F.TSPProvider",
				},
				map[string]interface{}{
					"const": "TSPKeepTruckin",
				},
			},
		},
	},
}

var commonAdditionalRules = map[string]conditionalRuleInput{
	// All new MTC forms
	"NIS IM 020 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM DS 001 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM DS 002 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 022 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 023 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeManualForms),
	},
	"NIS IM 024 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeManualForms),
	},
	"NIS IM 025 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 027 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 028 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeManualForms),
	},
	"NIS IM 029 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 030 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 031 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 032 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 033 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 034 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 035 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeManualForms),
	},
	"NIS IM 036 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeManualForms),
	},
	"NIS IM 037 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 038 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 021 AZ 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 021 KY 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 021 MI 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 021 MO 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 021 NE 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 021 NM 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 021 OR 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 021 WA 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 021 WI 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM DS 001 CA 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 035 WA 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeManualForms),
	},
	"NIS IM 036 WA 05 25": {
		when:              IsMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeManualForms),
	},
	// All old MTC forms
	"NIS IM DS 001 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM DS 001 CA 07 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 001 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 001 10 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 003 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 004 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 006 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 016 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"CM 01 03 02 14": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"CM 01 11 04 21": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"CM 01 42 01 20": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"IL 02 49 12 21": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"IL 02 62 02 15": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"IL 02 72 11 21": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"IL 02 79 09 08": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"IL 02 71 01 15": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 005 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 009 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 010 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 007 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 008 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 008 IN 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 008 KY 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 008 MO 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 008 PA 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 008 NC 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 008 WA 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 011 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 012 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 012 GA 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 013 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 013 10 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 014 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 014 10 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 008 AR 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM N 001 05 25": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"F.IsExpiringPolicyMTCRatingModelV1",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	},
	// Fleet
	// Rule Check for Agency Cottingham & Butler and not attach form
	// Rule Check for MTCRatingModelV2
	"NIS IM 026 05 25": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"not": []interface{}{
						map[string]interface{}{
							"obj": "F.AgencyId",
						},
						map[string]interface{}{
							"const": "088ca6fd-ac21-4939-a8dc-b131315f37cb",
						},
					},
				},
				IsMTCRatingModelV2,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"IL 09 52 01 15": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.AllCoverages, \"CoverageTerrorism\")",
						true,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"F.AllCoverages.CoverageTerrorism",
						true,
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.Metadata.AttachIL0952
	"CA 22 64 10 13": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.AllCoverages, \"CoveragePersonalInjuryProtection\")",
						true,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"F.AllCoverages.CoveragePersonalInjuryProtection",
						true,
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.Metadata.ALDeductible > 0 || input.Metadata.AttachCA2264
	"CIM 70 62 00 07 23": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.AllCoverages, \"CoverageReeferWithHumanError\")",
						true,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"F.AllCoverages.CoverageReeferWithHumanError",
						true,
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.Metadata.AttachCIM7062
	"CIM 70 58 00 08 22": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.AllCoverages, \"CoverageReeferWithoutHumanError\")",
						true,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"F.AllCoverages.CoverageReeferWithoutHumanError",
						true,
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.Metadata.AttachCIM7058
	"CSA 12 23": {
		when: map[string]interface{}{
			"gt": []interface{}{
				"F.ALDeductible",
				map[string]interface{}{
					"const": 25000.0,
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // Condition: input.Metadata.ALDeductible > 25000
	"NDCPO B 06 24": {
		when:              cameraSubsidyConditionNotKeepTruckin,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	},
	"NDCPO A 06 24": {
		when:              cameraSubsidyConditionKeepTruckin,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	},
	"NDCPO B 06 25": {
		when:              cameraSubsidyConditionNotKeepTruckin,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	},
	"NDCPO A 06 25": {
		when:              cameraSubsidyConditionKeepTruckin,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	},
	"CTRF 385 01 17": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"F.IsNegotiatedPremiumGTTraditionalPremium",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // Condition: input.Metadata.IsNegotiatedPremiumGTTraditionalPremium
	"MG_09_24": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"F.IsMinimumMileageGuaranteed",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // Condition: input.Metadata.IsMinimumMileageGuaranteed
	// THe below 5 contains the following condition - appObj.IsRenewal()
	"CG P 023 05 23": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"IsNil(F.ExpiringPolicyForms)",
						false,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.ExpiringPolicyForms, \"CoverageGeneralLiability\")",
						true,
					},
				},
				map[string]interface{}{
					"or": []interface{}{
						map[string]interface{}{
							"eq": []interface{}{
								"common.MapContains(F.ExpiringPolicyForms.CoverageGeneralLiability, \"CG 40 32 05 23\")",
								false,
							},
						},
						map[string]interface{}{
							"not": []interface{}{
								"F.ExpiringPolicyForms.CoverageGeneralLiability[\"CG 40 32 05 23\"]",
								true,
							},
						},
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // Condition: input.Metadata.AttachCGP023
	"CG P 024 12 23": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"IsNil(F.ExpiringPolicyForms)",
						false,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.ExpiringPolicyForms, \"CoverageGeneralLiability\")",
						true,
					},
				},
				map[string]interface{}{
					"not": []interface{}{
						map[string]interface{}{
							"or": []interface{}{
								map[string]interface{}{
									"and": []interface{}{
										map[string]interface{}{
											"eq": []interface{}{
												"common.MapContains(F.ExpiringPolicyForms.CoverageGeneralLiability, \"CG 00 69 12 23\")",
												true,
											},
										},
										map[string]interface{}{
											"eq": []interface{}{
												"F.ExpiringPolicyForms.CoverageGeneralLiability[\"CG 00 69 12 23\"]",
												true,
											},
										},
									},
								},
								map[string]interface{}{
									"and": []interface{}{
										map[string]interface{}{
											"eq": []interface{}{
												"common.MapContains(F.ExpiringPolicyForms.CoverageGeneralLiability, \"CG 21 06 12 23\")",
												true,
											},
										},
										map[string]interface{}{
											"eq": []interface{}{
												"F.ExpiringPolicyForms.CoverageGeneralLiability[\"CG 21 06 12 23\"]",
												true,
											},
										},
									},
								},
								map[string]interface{}{
									"and": []interface{}{
										map[string]interface{}{
											"eq": []interface{}{
												"common.MapContains(F.ExpiringPolicyForms.CoverageGeneralLiability, \"CG 40 35 12 23\")",
												true,
											},
										},
										map[string]interface{}{
											"eq": []interface{}{
												"F.ExpiringPolicyForms.CoverageGeneralLiability[\"CG 40 35 12 23\"]",
												true,
											},
										},
									},
								},
							},
						},
						true,
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // attach if expiring policy form comp does not contain any of CG 00 69 12 23, CG 21 06 12 23 and CG 40 35 12 23
	"CM P 004 12 19": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"IsNil(F.ExpiringPolicyForms)",
						false,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.ExpiringPolicyForms, \"CoverageMotorTruckCargo\")",
						true,
					},
				},
				map[string]interface{}{
					"or": []interface{}{
						map[string]interface{}{
							"eq": []interface{}{
								"common.MapContains(F.ExpiringPolicyForms.CoverageMotorTruckCargo, \"CM 99 05 12 19\")",
								false,
							},
						},
						map[string]interface{}{
							"not": []interface{}{
								"F.ExpiringPolicyForms.CoverageMotorTruckCargo[\"CM 99 05 12 19\"]",
								true,
							},
						},
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // Condition: input.Metadata.AttachCMP004
	"CM P 008 12 23": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"IsNil(F.ExpiringPolicyForms)",
						false,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.ExpiringPolicyForms, \"CoverageMotorTruckCargo\")",
						true,
					},
				},
				map[string]interface{}{
					"or": []interface{}{
						map[string]interface{}{
							"eq": []interface{}{
								"common.MapContains(F.ExpiringPolicyForms.CoverageMotorTruckCargo, \"CM 99 12 12 23\")",
								false,
							},
						},
						map[string]interface{}{
							"not": []interface{}{
								"F.ExpiringPolicyForms.CoverageMotorTruckCargo[\"CM 99 12 12 23\"]",
								true,
							},
						},
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // Condition: input.Metadata.AttachCMP008
	"CA P 014 12 23": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"IsNil(F.ExpiringPolicyForms)",
						false,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.ExpiringPolicyForms, \"CoverageAutoLiability\")",
						true,
					},
				},
				map[string]interface{}{
					"and": []interface{}{
						map[string]interface{}{
							"eq": []interface{}{
								"common.MapContains(F.ExpiringPolicyForms.CoverageAutoLiability, \"CA 00 20 11 20\")",
								true,
							},
						},
						map[string]interface{}{
							"eq": []interface{}{
								"F.ExpiringPolicyForms.CoverageAutoLiability[\"CA 00 20 11 20\"]",
								true,
							},
						},
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // attach if expiring policy form comp contains CA 00 20 11 20
	"CA 03 01 10 13": {
		when: map[string]interface{}{
			"gt": []interface{}{
				"F.ALDeductible",
				map[string]interface{}{
					"const": 0.0,
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.Metadata.ALDeductible > 0
	"CA 03 02 10 13": {
		when: map[string]interface{}{
			"gt": []interface{}{
				"F.ALDeductible",
				map[string]interface{}{
					"const": 0.0,
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.Metadata.ALDeductible > 0
	"CG 03 00 01 96": {
		when: map[string]interface{}{
			"gt": []interface{}{
				"F.GLDeductible",
				map[string]interface{}{
					"const": 0.0,
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.Metadata.GLDeductible > 0
	"CIM 70 53 00 07 22": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"F.IsAPDMTCDeductibleCombined",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.Metadata.CoveragesWithCombinedDeductibles != nil
	// Non Fleet Admitted
	// Not needed as the package type rule is already present in the core rule, but schedule type core needs to be added
	"CA 20 48 10 13": {
		programType:       pointer_utils.ToPointer(policyEnums.ProgramTypeNonFleetAdmitted),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition input.PackageType == app_enums.IndicationOptionTagStandard || input.PackageType == app_enums.IndicationOptionTagComplete
	"CA 04 43 11 20": {
		applicableCoveragePackage: map[string]model.PackageType{
			enums.CoverageAutoLiability.String(): model.StandardAndComplete,
		},
		programType:       pointer_utils.ToPointer(policyEnums.ProgramTypeNonFleetAdmitted),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.PackageType == app_enums.IndicationOptionTagStandard || input.PackageType == app_enums.IndicationOptionTagComplete
	"CA 04 43 12 23": {
		applicableCoveragePackage: map[string]model.PackageType{
			enums.CoverageAutoLiability.String(): model.StandardAndComplete,
		},
		programType:       pointer_utils.ToPointer(policyEnums.ProgramTypeNonFleetAdmitted),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.PackageType == app_enums.IndicationOptionTagStandard || input.PackageType == app_enums.IndicationOptionTagComplete
	"CAP 04 01 00 NF 11 23": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.AllCoverages, \"CoverageNonOwnedTrailer\")",
						true,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"F.AllCoverages.CoverageNonOwnedTrailer",
						true,
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.Metadata.AttachCAP040100NF1123
	// Not needed as the package type rule is already present in the core rule, but schedule type core needs to be added
	"CG 20 26 12 19": {
		programType:       pointer_utils.ToPointer(policyEnums.ProgramTypeNonFleetAdmitted),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.PackageType == app_enums.IndicationOptionTagStandard || input.PackageType == app_enums.IndicationOptionTagComplete
	// Not needed as the package type rule is already present in the core rule, but schedule type core needs to be added
	"CG 24 53 12 19": {
		programType:       pointer_utils.ToPointer(policyEnums.ProgramTypeNonFleetAdmitted),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.PackageType == app_enums.IndicationOptionTagStandard || input.PackageType == app_enums.IndicationOptionTagComplete
	"CIM 70 51 00 NF 07 22": {
		applicableCoveragePackage: map[string]model.PackageType{
			enums.CoverageMotorTruckCargo.String(): model.StandardAndComplete,
		},
		programType:       pointer_utils.ToPointer(policyEnums.ProgramTypeNonFleetAdmitted),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.PackageType == app_enums.IndicationOptionTagStandard || input.PackageType == app_enums.IndicationOptionTagComplete
	"CIM 70 53 00 NF 07 22": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"F.IsAPDMTCDeductibleCombined",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	}, // Condition: input.Metadata.IsAPDMTCDeductibleCombined
	"IL U 005 02 12": {
		when: map[string]interface{}{
			"lt": []interface{}{
				"F.NumberOfPowerUnits",
				map[string]interface{}{
					"const": 5.0,
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		programType:       pointer_utils.ToPointer(policyEnums.ProgramTypeNonFleetAdmitted),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // Condition: input.Metadata.NumberOfPowerUnits < 5
	"IL U 006 02 12": {
		when: map[string]interface{}{
			"lt": []interface{}{
				"F.NumberOfPowerUnits",
				map[string]interface{}{
					"const": 5.0,
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		programType:       pointer_utils.ToPointer(policyEnums.ProgramTypeNonFleetAdmitted),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // Condition: input.Metadata.NumberOfPowerUnits < 5
	"CA U 010 11 13": {
		when: map[string]interface{}{
			"gte": []interface{}{
				"F.NumberOfPowerUnits",
				map[string]interface{}{
					"const": 5.0,
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		programType:       pointer_utils.ToPointer(policyEnums.ProgramTypeNonFleetAdmitted),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // Condition: input.Metadata.NumberOfPowerUnits >= 5
	"CA U 011 11 13": {
		when: map[string]interface{}{
			"gte": []interface{}{
				"F.NumberOfPowerUnits",
				map[string]interface{}{
					"const": 5.0,
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		programType:       pointer_utils.ToPointer(policyEnums.ProgramTypeNonFleetAdmitted),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // Condition: input.Metadata.NumberOfPowerUnits >= 5
	"IL N 088 09 03": {
		when: map[string]interface{}{
			"or": []interface{}{
				map[string]interface{}{
					"and": []interface{}{
						map[string]interface{}{
							"eq": []interface{}{
								"common.MapContains(F.AllCoverages, \"CoverageGeneralLiability\")",
								true,
							},
						},
						map[string]interface{}{
							"eq": []interface{}{
								"F.AllCoverages.CoverageGeneralLiability",
								true,
							},
						},
					},
				},
				map[string]interface{}{
					"and": []interface{}{
						map[string]interface{}{
							"eq": []interface{}{
								"common.MapContains(F.AllCoverages, \"CoverageMotorTruckCargo\")",
								true,
							},
						},
						map[string]interface{}{
							"eq": []interface{}{
								"F.AllCoverages.CoverageMotorTruckCargo",
								true,
							},
						},
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		programType:       pointer_utils.ToPointer(policyEnums.ProgramTypeNonFleetAdmitted),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	}, // Condition: input.Metadata.AttachILN08809
	// Manual change after the migration
	"CAP 70 03 00 NF 07 23": {
		formTemplateType: pointer_utils.ToPointer(formenums.FormTemplateTypeComposedPDF),
	},
	"NIS IL N 001 01 24": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"F.IsRenewal",
						true,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						map[string]interface{}{
							"obj": "F.ExistingCarrierForRenewalApp",
						},
						map[string]interface{}{
							"const": "Falls Lake National Insurance Company",
						},
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						map[string]interface{}{
							"obj": "F.InsuranceCarrier",
						},
						map[string]interface{}{
							"const": "MS Transverse Insurance Company",
						},
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeSignedQuoteForms),
	},
	"CIM 70 58 00 NF 10 24": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.AllCoverages, \"CoverageReefer\")",
						true,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"F.AllCoverages.CoverageReefer",
						true,
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"CIM 70 62 00 NF 10 24": {
		when: map[string]interface{}{
			"and": []interface{}{
				map[string]interface{}{
					"eq": []interface{}{
						"common.MapContains(F.AllCoverages, \"CoverageReeferWithHumanError\")",
						true,
					},
				},
				map[string]interface{}{
					"eq": []interface{}{
						"F.AllCoverages.CoverageReeferWithHumanError",
						true,
					},
				},
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"IL 00 30 01 06": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"common.MapNotContains(F.AllCoverages, \"CoverageTerrorism\")",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"CG 21 90 01 06": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"common.MapNotContains(F.AllCoverages, \"CoverageTerrorism\")",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"CA 23 85 10 13": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"common.MapNotContains(F.AllCoverages, \"CoverageTerrorism\")",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"CA 23 92 10 13": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"common.MapNotContains(F.AllCoverages, \"CoverageTerrorism\")",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"CA 23 86 10 13": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"common.MapNotContains(F.AllCoverages, \"CoverageTerrorism\")",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"CA 23 84 10 13": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"common.MapNotContains(F.AllCoverages, \"CoverageTerrorism\")",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"CG 32 22 01 06": {
		when: map[string]interface{}{
			"eq": []interface{}{
				"common.MapNotContains(F.AllCoverages, \"CoverageTerrorism\")",
				true,
			},
		},
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 002 AZ 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 002 KY 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 002 MI 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 002 NE 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 002 NM 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 002 OR 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 002 WA 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 002 WI 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
	"NIS IM 015 MO 01 24": {
		when:              IsNotMTCRatingModelV2,
		logicalOperator:   pointer_utils.ToPointer(LogicalOperatorAnd),
		scheduleTypeToAdd: pointer_utils.ToPointer(compilation.ScheduleTypeCore),
	},
}
