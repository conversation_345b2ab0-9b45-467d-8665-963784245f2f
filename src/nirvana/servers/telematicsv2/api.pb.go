// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: telematicsv2/api.proto

package telematicsv2

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConnectionStatus int32

const (
	ConnectionStatus_ConnectionStatusUnknown            ConnectionStatus = 0
	ConnectionStatus_ConnectionStatusInitiated          ConnectionStatus = 1
	ConnectionStatus_ConnectionStatusAuthorized         ConnectionStatus = 2
	ConnectionStatus_ConnectionStatusRejected           ConnectionStatus = 3
	ConnectionStatus_ConnectionStatusConnected          ConnectionStatus = 4
	ConnectionStatus_ConnectionStatusDisconnected       ConnectionStatus = 5
	ConnectionStatus_ConnectionStatusInternallyDisabled ConnectionStatus = 6
	ConnectionStatus_ConnectionStatusPermanentlyLost    ConnectionStatus = 7
	ConnectionStatus_ConnectionStatusPermanentlyDeleted ConnectionStatus = 8
)

// Enum value maps for ConnectionStatus.
var (
	ConnectionStatus_name = map[int32]string{
		0: "ConnectionStatusUnknown",
		1: "ConnectionStatusInitiated",
		2: "ConnectionStatusAuthorized",
		3: "ConnectionStatusRejected",
		4: "ConnectionStatusConnected",
		5: "ConnectionStatusDisconnected",
		6: "ConnectionStatusInternallyDisabled",
		7: "ConnectionStatusPermanentlyLost",
		8: "ConnectionStatusPermanentlyDeleted",
	}
	ConnectionStatus_value = map[string]int32{
		"ConnectionStatusUnknown":            0,
		"ConnectionStatusInitiated":          1,
		"ConnectionStatusAuthorized":         2,
		"ConnectionStatusRejected":           3,
		"ConnectionStatusConnected":          4,
		"ConnectionStatusDisconnected":       5,
		"ConnectionStatusInternallyDisabled": 6,
		"ConnectionStatusPermanentlyLost":    7,
		"ConnectionStatusPermanentlyDeleted": 8,
	}
)

func (x ConnectionStatus) Enum() *ConnectionStatus {
	p := new(ConnectionStatus)
	*p = x
	return p
}

func (x ConnectionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConnectionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_telematicsv2_api_proto_enumTypes[0].Descriptor()
}

func (ConnectionStatus) Type() protoreflect.EnumType {
	return &file_telematicsv2_api_proto_enumTypes[0]
}

func (x ConnectionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConnectionStatus.Descriptor instead.
func (ConnectionStatus) EnumDescriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{0}
}

type Month int32

const (
	Month_MONTH_UNSPECIFIED Month = 0
	Month_JANUARY           Month = 1
	Month_FEBRUARY          Month = 2
	Month_MARCH             Month = 3
	Month_APRIL             Month = 4
	Month_MAY               Month = 5
	Month_JUNE              Month = 6
	Month_JULY              Month = 7
	Month_AUGUST            Month = 8
	Month_SEPTEMBER         Month = 9
	Month_OCTOBER           Month = 10
	Month_NOVEMBER          Month = 11
	Month_DECEMBER          Month = 12
)

// Enum value maps for Month.
var (
	Month_name = map[int32]string{
		0:  "MONTH_UNSPECIFIED",
		1:  "JANUARY",
		2:  "FEBRUARY",
		3:  "MARCH",
		4:  "APRIL",
		5:  "MAY",
		6:  "JUNE",
		7:  "JULY",
		8:  "AUGUST",
		9:  "SEPTEMBER",
		10: "OCTOBER",
		11: "NOVEMBER",
		12: "DECEMBER",
	}
	Month_value = map[string]int32{
		"MONTH_UNSPECIFIED": 0,
		"JANUARY":           1,
		"FEBRUARY":          2,
		"MARCH":             3,
		"APRIL":             4,
		"MAY":               5,
		"JUNE":              6,
		"JULY":              7,
		"AUGUST":            8,
		"SEPTEMBER":         9,
		"OCTOBER":           10,
		"NOVEMBER":          11,
		"DECEMBER":          12,
	}
)

func (x Month) Enum() *Month {
	p := new(Month)
	*p = x
	return p
}

func (x Month) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Month) Descriptor() protoreflect.EnumDescriptor {
	return file_telematicsv2_api_proto_enumTypes[1].Descriptor()
}

func (Month) Type() protoreflect.EnumType {
	return &file_telematicsv2_api_proto_enumTypes[1]
}

func (x Month) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Month.Descriptor instead.
func (Month) EnumDescriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{1}
}

type ResourceType int32

const (
	ResourceType_GPSLog                     ResourceType = 0
	ResourceType_EngineStateLog             ResourceType = 1
	ResourceType_OdometerLog                ResourceType = 2
	ResourceType_HarshBrakingEventLog       ResourceType = 3
	ResourceType_HarshAccelerationEventLog  ResourceType = 4
	ResourceType_HarshTurnEventLog          ResourceType = 5
	ResourceType_SpeedingEventLog           ResourceType = 6
	ResourceType_RoadSegmentsLog            ResourceType = 7
	ResourceType_TspTripLog                 ResourceType = 8
	ResourceType_VehicleDriverAssignmentLog ResourceType = 9
	ResourceType_SpeedingSeverityEventLog   ResourceType = 10
	ResourceType_GPSLogWithSpeedLimits      ResourceType = 11
	ResourceType_GPSLogWithTrafficDensity   ResourceType = 12
	ResourceType_GPSLogWithMaps             ResourceType = 13
	ResourceType_GPSLogWithGeography        ResourceType = 14
	ResourceType_GPSLogWithWeather          ResourceType = 15
	ResourceType_SafetyEvents               ResourceType = 16
)

// Enum value maps for ResourceType.
var (
	ResourceType_name = map[int32]string{
		0:  "GPSLog",
		1:  "EngineStateLog",
		2:  "OdometerLog",
		3:  "HarshBrakingEventLog",
		4:  "HarshAccelerationEventLog",
		5:  "HarshTurnEventLog",
		6:  "SpeedingEventLog",
		7:  "RoadSegmentsLog",
		8:  "TspTripLog",
		9:  "VehicleDriverAssignmentLog",
		10: "SpeedingSeverityEventLog",
		11: "GPSLogWithSpeedLimits",
		12: "GPSLogWithTrafficDensity",
		13: "GPSLogWithMaps",
		14: "GPSLogWithGeography",
		15: "GPSLogWithWeather",
		16: "SafetyEvents",
	}
	ResourceType_value = map[string]int32{
		"GPSLog":                     0,
		"EngineStateLog":             1,
		"OdometerLog":                2,
		"HarshBrakingEventLog":       3,
		"HarshAccelerationEventLog":  4,
		"HarshTurnEventLog":          5,
		"SpeedingEventLog":           6,
		"RoadSegmentsLog":            7,
		"TspTripLog":                 8,
		"VehicleDriverAssignmentLog": 9,
		"SpeedingSeverityEventLog":   10,
		"GPSLogWithSpeedLimits":      11,
		"GPSLogWithTrafficDensity":   12,
		"GPSLogWithMaps":             13,
		"GPSLogWithGeography":        14,
		"GPSLogWithWeather":          15,
		"SafetyEvents":               16,
	}
)

func (x ResourceType) Enum() *ResourceType {
	p := new(ResourceType)
	*p = x
	return p
}

func (x ResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_telematicsv2_api_proto_enumTypes[2].Descriptor()
}

func (ResourceType) Type() protoreflect.EnumType {
	return &file_telematicsv2_api_proto_enumTypes[2]
}

func (x ResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResourceType.Descriptor instead.
func (ResourceType) EnumDescriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{2}
}

type PipelineKind int32

const (
	PipelineKind_MillimanScoringPipeline PipelineKind = 0
	// Deprecated: Marked as deprecated in telematicsv2/api.proto.
	PipelineKind_BillingPipeline PipelineKind = 1
	// Deprecated: Marked as deprecated in telematicsv2/api.proto.
	PipelineKind_ExpNormalisationOnlyPipeline PipelineKind = 2
	// Deprecated: Marked as deprecated in telematicsv2/api.proto.
	PipelineKind_LinearSafetyScorePipeline PipelineKind = 3
	PipelineKind_ConfigurablePipeline      PipelineKind = 4
)

// Enum value maps for PipelineKind.
var (
	PipelineKind_name = map[int32]string{
		0: "MillimanScoringPipeline",
		1: "BillingPipeline",
		2: "ExpNormalisationOnlyPipeline",
		3: "LinearSafetyScorePipeline",
		4: "ConfigurablePipeline",
	}
	PipelineKind_value = map[string]int32{
		"MillimanScoringPipeline":      0,
		"BillingPipeline":              1,
		"ExpNormalisationOnlyPipeline": 2,
		"LinearSafetyScorePipeline":    3,
		"ConfigurablePipeline":         4,
	}
)

func (x PipelineKind) Enum() *PipelineKind {
	p := new(PipelineKind)
	*p = x
	return p
}

func (x PipelineKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PipelineKind) Descriptor() protoreflect.EnumDescriptor {
	return file_telematicsv2_api_proto_enumTypes[3].Descriptor()
}

func (PipelineKind) Type() protoreflect.EnumType {
	return &file_telematicsv2_api_proto_enumTypes[3]
}

func (x PipelineKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PipelineKind.Descriptor instead.
func (PipelineKind) EnumDescriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{3}
}

type IdentifierType int32

const (
	IdentifierType_UNDEFINED           IdentifierType = 0
	IdentifierType_VIN                 IdentifierType = 1
	IdentifierType_PROVIDER_VEHICLE_ID IdentifierType = 2
	IdentifierType_PROVIDER_DRIVER_ID  IdentifierType = 3
)

// Enum value maps for IdentifierType.
var (
	IdentifierType_name = map[int32]string{
		0: "UNDEFINED",
		1: "VIN",
		2: "PROVIDER_VEHICLE_ID",
		3: "PROVIDER_DRIVER_ID",
	}
	IdentifierType_value = map[string]int32{
		"UNDEFINED":           0,
		"VIN":                 1,
		"PROVIDER_VEHICLE_ID": 2,
		"PROVIDER_DRIVER_ID":  3,
	}
)

func (x IdentifierType) Enum() *IdentifierType {
	p := new(IdentifierType)
	*p = x
	return p
}

func (x IdentifierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentifierType) Descriptor() protoreflect.EnumDescriptor {
	return file_telematicsv2_api_proto_enumTypes[4].Descriptor()
}

func (IdentifierType) Type() protoreflect.EnumType {
	return &file_telematicsv2_api_proto_enumTypes[4]
}

func (x IdentifierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdentifierType.Descriptor instead.
func (IdentifierType) EnumDescriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{4}
}

type ExecutionStatus int32

const (
	ExecutionStatus_Unknown   ExecutionStatus = 0
	ExecutionStatus_Running   ExecutionStatus = 1
	ExecutionStatus_Failed    ExecutionStatus = 2
	ExecutionStatus_Succeeded ExecutionStatus = 3
)

// Enum value maps for ExecutionStatus.
var (
	ExecutionStatus_name = map[int32]string{
		0: "Unknown",
		1: "Running",
		2: "Failed",
		3: "Succeeded",
	}
	ExecutionStatus_value = map[string]int32{
		"Unknown":   0,
		"Running":   1,
		"Failed":    2,
		"Succeeded": 3,
	}
)

func (x ExecutionStatus) Enum() *ExecutionStatus {
	p := new(ExecutionStatus)
	*p = x
	return p
}

func (x ExecutionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExecutionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_telematicsv2_api_proto_enumTypes[5].Descriptor()
}

func (ExecutionStatus) Type() protoreflect.EnumType {
	return &file_telematicsv2_api_proto_enumTypes[5]
}

func (x ExecutionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExecutionStatus.Descriptor instead.
func (ExecutionStatus) EnumDescriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{5}
}

type PipelineDetailsRequest_Richness int32

const (
	PipelineDetailsRequest_OPTIMIZE_FOR_PERFORMANCE PipelineDetailsRequest_Richness = 0
	PipelineDetailsRequest_VIN_LEVEL_VISIBILITY     PipelineDetailsRequest_Richness = 1
	PipelineDetailsRequest_WEEK_LEVEL_VISIBILITY    PipelineDetailsRequest_Richness = 2
)

// Enum value maps for PipelineDetailsRequest_Richness.
var (
	PipelineDetailsRequest_Richness_name = map[int32]string{
		0: "OPTIMIZE_FOR_PERFORMANCE",
		1: "VIN_LEVEL_VISIBILITY",
		2: "WEEK_LEVEL_VISIBILITY",
	}
	PipelineDetailsRequest_Richness_value = map[string]int32{
		"OPTIMIZE_FOR_PERFORMANCE": 0,
		"VIN_LEVEL_VISIBILITY":     1,
		"WEEK_LEVEL_VISIBILITY":    2,
	}
)

func (x PipelineDetailsRequest_Richness) Enum() *PipelineDetailsRequest_Richness {
	p := new(PipelineDetailsRequest_Richness)
	*p = x
	return p
}

func (x PipelineDetailsRequest_Richness) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PipelineDetailsRequest_Richness) Descriptor() protoreflect.EnumDescriptor {
	return file_telematicsv2_api_proto_enumTypes[6].Descriptor()
}

func (PipelineDetailsRequest_Richness) Type() protoreflect.EnumType {
	return &file_telematicsv2_api_proto_enumTypes[6]
}

func (x PipelineDetailsRequest_Richness) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PipelineDetailsRequest_Richness.Descriptor instead.
func (PipelineDetailsRequest_Richness) EnumDescriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{19, 0}
}

type DataFileListRequest_OperationMode int32

const (
	DataFileListRequest_Standard DataFileListRequest_OperationMode = 0
	DataFileListRequest_Legacy   DataFileListRequest_OperationMode = 1
)

// Enum value maps for DataFileListRequest_OperationMode.
var (
	DataFileListRequest_OperationMode_name = map[int32]string{
		0: "Standard",
		1: "Legacy",
	}
	DataFileListRequest_OperationMode_value = map[string]int32{
		"Standard": 0,
		"Legacy":   1,
	}
)

func (x DataFileListRequest_OperationMode) Enum() *DataFileListRequest_OperationMode {
	p := new(DataFileListRequest_OperationMode)
	*p = x
	return p
}

func (x DataFileListRequest_OperationMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataFileListRequest_OperationMode) Descriptor() protoreflect.EnumDescriptor {
	return file_telematicsv2_api_proto_enumTypes[7].Descriptor()
}

func (DataFileListRequest_OperationMode) Type() protoreflect.EnumType {
	return &file_telematicsv2_api_proto_enumTypes[7]
}

func (x DataFileListRequest_OperationMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataFileListRequest_OperationMode.Descriptor instead.
func (DataFileListRequest_OperationMode) EnumDescriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{36, 0}
}

type ConnectionHandleId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ConnectionHandleId) Reset() {
	*x = ConnectionHandleId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectionHandleId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionHandleId) ProtoMessage() {}

func (x *ConnectionHandleId) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionHandleId.ProtoReflect.Descriptor instead.
func (*ConnectionHandleId) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{0}
}

func (x *ConnectionHandleId) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type TelematicsConnectionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TspName                       string                 `protobuf:"bytes,1,opt,name=tsp_name,json=tspName,proto3" json:"tsp_name,omitempty"`
	ProviderName                  string                 `protobuf:"bytes,2,opt,name=provider_name,json=providerName,proto3" json:"provider_name,omitempty"`
	ConnectionStatus              ConnectionStatus       `protobuf:"varint,3,opt,name=connection_status,json=connectionStatus,proto3,enum=telematicsv2.ConnectionStatus" json:"connection_status,omitempty"`
	ActivatedAt                   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=activated_at,json=activatedAt,proto3,oneof" json:"activated_at,omitempty"`
	DataPullLegalLimit            *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=data_pull_legal_limit,json=dataPullLegalLimit,proto3,oneof" json:"data_pull_legal_limit,omitempty"`
	ConsentKind                   string                 `protobuf:"bytes,6,opt,name=consent_kind,json=consentKind,proto3" json:"consent_kind,omitempty"`
	CreatedAt                     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	DataProviderHealthStatus      *bool                  `protobuf:"varint,8,opt,name=data_provider_health_status,json=dataProviderHealthStatus,proto3,oneof" json:"data_provider_health_status,omitempty"`
	LastHealthCheckAt             *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=last_health_check_at,json=lastHealthCheckAt,proto3,oneof" json:"last_health_check_at,omitempty"`
	LastSuccessfulHealthCheckAt   *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=last_successful_health_check_at,json=lastSuccessfulHealthCheckAt,proto3,oneof" json:"last_successful_health_check_at,omitempty"`
	ConsecutiveFailedHealthChecks int32                  `protobuf:"varint,11,opt,name=consecutive_failed_health_checks,json=consecutiveFailedHealthChecks,proto3" json:"consecutive_failed_health_checks,omitempty"`
}

func (x *TelematicsConnectionInfo) Reset() {
	*x = TelematicsConnectionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsConnectionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsConnectionInfo) ProtoMessage() {}

func (x *TelematicsConnectionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsConnectionInfo.ProtoReflect.Descriptor instead.
func (*TelematicsConnectionInfo) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{1}
}

func (x *TelematicsConnectionInfo) GetTspName() string {
	if x != nil {
		return x.TspName
	}
	return ""
}

func (x *TelematicsConnectionInfo) GetProviderName() string {
	if x != nil {
		return x.ProviderName
	}
	return ""
}

func (x *TelematicsConnectionInfo) GetConnectionStatus() ConnectionStatus {
	if x != nil {
		return x.ConnectionStatus
	}
	return ConnectionStatus_ConnectionStatusUnknown
}

func (x *TelematicsConnectionInfo) GetActivatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ActivatedAt
	}
	return nil
}

func (x *TelematicsConnectionInfo) GetDataPullLegalLimit() *timestamppb.Timestamp {
	if x != nil {
		return x.DataPullLegalLimit
	}
	return nil
}

func (x *TelematicsConnectionInfo) GetConsentKind() string {
	if x != nil {
		return x.ConsentKind
	}
	return ""
}

func (x *TelematicsConnectionInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TelematicsConnectionInfo) GetDataProviderHealthStatus() bool {
	if x != nil && x.DataProviderHealthStatus != nil {
		return *x.DataProviderHealthStatus
	}
	return false
}

func (x *TelematicsConnectionInfo) GetLastHealthCheckAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastHealthCheckAt
	}
	return nil
}

func (x *TelematicsConnectionInfo) GetLastSuccessfulHealthCheckAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSuccessfulHealthCheckAt
	}
	return nil
}

func (x *TelematicsConnectionInfo) GetConsecutiveFailedHealthChecks() int32 {
	if x != nil {
		return x.ConsecutiveFailedHealthChecks
	}
	return 0
}

type Vehicle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin                 string                 `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	IsVinFake           bool                   `protobuf:"varint,2,opt,name=is_vin_fake,json=isVinFake,proto3" json:"is_vin_fake,omitempty"`
	TspId               string                 `protobuf:"bytes,4,opt,name=tsp_id,json=tspId,proto3" json:"tsp_id,omitempty"`
	CameraSerial        string                 `protobuf:"bytes,13,opt,name=camera_serial,json=cameraSerial,proto3" json:"camera_serial,omitempty"`
	TagIds              []string               `protobuf:"bytes,14,rep,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	AliasName           *string                `protobuf:"bytes,15,opt,name=alias_name,json=aliasName,proto3,oneof" json:"alias_name,omitempty"`
	TspActivationStatus *bool                  `protobuf:"varint,16,opt,name=tsp_activation_status,json=tspActivationStatus,proto3,oneof" json:"tsp_activation_status,omitempty"`
	CreatedAt           *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	LastUpdatedAt       *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=last_updated_at,json=lastUpdatedAt,proto3" json:"last_updated_at,omitempty"`
	TspCreatedAt        *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=tsp_created_at,json=tspCreatedAt,proto3,oneof" json:"tsp_created_at,omitempty"`
	TspUpdatedAt        *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=tsp_updated_at,json=tspUpdatedAt,proto3,oneof" json:"tsp_updated_at,omitempty"`
}

func (x *Vehicle) Reset() {
	*x = Vehicle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Vehicle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vehicle) ProtoMessage() {}

func (x *Vehicle) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vehicle.ProtoReflect.Descriptor instead.
func (*Vehicle) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{2}
}

func (x *Vehicle) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *Vehicle) GetIsVinFake() bool {
	if x != nil {
		return x.IsVinFake
	}
	return false
}

func (x *Vehicle) GetTspId() string {
	if x != nil {
		return x.TspId
	}
	return ""
}

func (x *Vehicle) GetCameraSerial() string {
	if x != nil {
		return x.CameraSerial
	}
	return ""
}

func (x *Vehicle) GetTagIds() []string {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *Vehicle) GetAliasName() string {
	if x != nil && x.AliasName != nil {
		return *x.AliasName
	}
	return ""
}

func (x *Vehicle) GetTspActivationStatus() bool {
	if x != nil && x.TspActivationStatus != nil {
		return *x.TspActivationStatus
	}
	return false
}

func (x *Vehicle) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Vehicle) GetLastUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdatedAt
	}
	return nil
}

func (x *Vehicle) GetTspCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TspCreatedAt
	}
	return nil
}

func (x *Vehicle) GetTspUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TspUpdatedAt
	}
	return nil
}

type Driver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceId            string                 `protobuf:"bytes,1,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	FullName            *string                `protobuf:"bytes,2,opt,name=full_name,json=fullName,proto3,oneof" json:"full_name,omitempty"`
	AliasName           *string                `protobuf:"bytes,3,opt,name=alias_name,json=aliasName,proto3,oneof" json:"alias_name,omitempty"`
	LicenseNumber       *string                `protobuf:"bytes,4,opt,name=license_number,json=licenseNumber,proto3,oneof" json:"license_number,omitempty"`
	LicenseState        *string                `protobuf:"bytes,5,opt,name=license_state,json=licenseState,proto3,oneof" json:"license_state,omitempty"`
	GroupIds            []string               `protobuf:"bytes,6,rep,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
	TspActivationStatus *bool                  `protobuf:"varint,12,opt,name=tsp_activation_status,json=tspActivationStatus,proto3,oneof" json:"tsp_activation_status,omitempty"`
	IsTracked           bool                   `protobuf:"varint,7,opt,name=is_tracked,json=isTracked,proto3" json:"is_tracked,omitempty"`
	CreatedAt           *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	LastUpdatedAt       *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=last_updated_at,json=lastUpdatedAt,proto3" json:"last_updated_at,omitempty"`
	TspCreatedAt        *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=tsp_created_at,json=tspCreatedAt,proto3,oneof" json:"tsp_created_at,omitempty"`
	TspUpdatedAt        *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=tsp_updated_at,json=tspUpdatedAt,proto3,oneof" json:"tsp_updated_at,omitempty"`
}

func (x *Driver) Reset() {
	*x = Driver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Driver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Driver) ProtoMessage() {}

func (x *Driver) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Driver.ProtoReflect.Descriptor instead.
func (*Driver) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{3}
}

func (x *Driver) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *Driver) GetFullName() string {
	if x != nil && x.FullName != nil {
		return *x.FullName
	}
	return ""
}

func (x *Driver) GetAliasName() string {
	if x != nil && x.AliasName != nil {
		return *x.AliasName
	}
	return ""
}

func (x *Driver) GetLicenseNumber() string {
	if x != nil && x.LicenseNumber != nil {
		return *x.LicenseNumber
	}
	return ""
}

func (x *Driver) GetLicenseState() string {
	if x != nil && x.LicenseState != nil {
		return *x.LicenseState
	}
	return ""
}

func (x *Driver) GetGroupIds() []string {
	if x != nil {
		return x.GroupIds
	}
	return nil
}

func (x *Driver) GetTspActivationStatus() bool {
	if x != nil && x.TspActivationStatus != nil {
		return *x.TspActivationStatus
	}
	return false
}

func (x *Driver) GetIsTracked() bool {
	if x != nil {
		return x.IsTracked
	}
	return false
}

func (x *Driver) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Driver) GetLastUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdatedAt
	}
	return nil
}

func (x *Driver) GetTspCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TspCreatedAt
	}
	return nil
}

func (x *Driver) GetTspUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TspUpdatedAt
	}
	return nil
}

type VehicleGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceId      string                 `protobuf:"bytes,1,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Name          *string                `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	IsTracked     bool                   `protobuf:"varint,3,opt,name=is_tracked,json=isTracked,proto3" json:"is_tracked,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	LastUpdatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=last_updated_at,json=lastUpdatedAt,proto3" json:"last_updated_at,omitempty"`
	TspCreatedAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=tsp_created_at,json=tspCreatedAt,proto3,oneof" json:"tsp_created_at,omitempty"`
	TspUpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=tsp_updated_at,json=tspUpdatedAt,proto3,oneof" json:"tsp_updated_at,omitempty"`
}

func (x *VehicleGroup) Reset() {
	*x = VehicleGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleGroup) ProtoMessage() {}

func (x *VehicleGroup) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleGroup.ProtoReflect.Descriptor instead.
func (*VehicleGroup) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{4}
}

func (x *VehicleGroup) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *VehicleGroup) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *VehicleGroup) GetIsTracked() bool {
	if x != nil {
		return x.IsTracked
	}
	return false
}

func (x *VehicleGroup) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VehicleGroup) GetLastUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdatedAt
	}
	return nil
}

func (x *VehicleGroup) GetTspCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TspCreatedAt
	}
	return nil
}

func (x *VehicleGroup) GetTspUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.TspUpdatedAt
	}
	return nil
}

type VehicleList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vehicles []*Vehicle `protobuf:"bytes,1,rep,name=vehicles,proto3" json:"vehicles,omitempty"`
}

func (x *VehicleList) Reset() {
	*x = VehicleList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleList) ProtoMessage() {}

func (x *VehicleList) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleList.ProtoReflect.Descriptor instead.
func (*VehicleList) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{5}
}

func (x *VehicleList) GetVehicles() []*Vehicle {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

type DriverList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Drivers []*Driver `protobuf:"bytes,1,rep,name=drivers,proto3" json:"drivers,omitempty"`
}

func (x *DriverList) Reset() {
	*x = DriverList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DriverList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverList) ProtoMessage() {}

func (x *DriverList) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverList.ProtoReflect.Descriptor instead.
func (*DriverList) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{6}
}

func (x *DriverList) GetDrivers() []*Driver {
	if x != nil {
		return x.Drivers
	}
	return nil
}

type VehicleGroupList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Groups []*VehicleGroup `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"`
}

func (x *VehicleGroupList) Reset() {
	*x = VehicleGroupList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleGroupList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleGroupList) ProtoMessage() {}

func (x *VehicleGroupList) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleGroupList.ProtoReflect.Descriptor instead.
func (*VehicleGroupList) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{7}
}

func (x *VehicleGroupList) GetGroups() []*VehicleGroup {
	if x != nil {
		return x.Groups
	}
	return nil
}

type PingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PingStatus bool `protobuf:"varint,1,opt,name=ping_status,json=pingStatus,proto3" json:"ping_status,omitempty"`
}

func (x *PingResponse) Reset() {
	*x = PingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingResponse) ProtoMessage() {}

func (x *PingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingResponse.ProtoReflect.Descriptor instead.
func (*PingResponse) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{8}
}

func (x *PingResponse) GetPingStatus() bool {
	if x != nil {
		return x.PingStatus
	}
	return false
}

type YearMonth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  int32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Month Month `protobuf:"varint,2,opt,name=month,proto3,enum=telematicsv2.Month" json:"month,omitempty"`
}

func (x *YearMonth) Reset() {
	*x = YearMonth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YearMonth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YearMonth) ProtoMessage() {}

func (x *YearMonth) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YearMonth.ProtoReflect.Descriptor instead.
func (*YearMonth) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{9}
}

func (x *YearMonth) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *YearMonth) GetMonth() Month {
	if x != nil {
		return x.Month
	}
	return Month_MONTH_UNSPECIFIED
}

type IFTAMonthlyMileage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin         string                 `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	MonthOfYear *YearMonth             `protobuf:"bytes,2,opt,name=monthOfYear,proto3" json:"monthOfYear,omitempty"`
	Miles       float64                `protobuf:"fixed64,3,opt,name=miles,proto3" json:"miles,omitempty"`
	FetchedAt   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=fetchedAt,proto3" json:"fetchedAt,omitempty"`
	Kilometers  *float64               `protobuf:"fixed64,5,opt,name=kilometers,proto3,oneof" json:"kilometers,omitempty"`
}

func (x *IFTAMonthlyMileage) Reset() {
	*x = IFTAMonthlyMileage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IFTAMonthlyMileage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IFTAMonthlyMileage) ProtoMessage() {}

func (x *IFTAMonthlyMileage) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IFTAMonthlyMileage.ProtoReflect.Descriptor instead.
func (*IFTAMonthlyMileage) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{10}
}

func (x *IFTAMonthlyMileage) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *IFTAMonthlyMileage) GetMonthOfYear() *YearMonth {
	if x != nil {
		return x.MonthOfYear
	}
	return nil
}

func (x *IFTAMonthlyMileage) GetMiles() float64 {
	if x != nil {
		return x.Miles
	}
	return 0
}

func (x *IFTAMonthlyMileage) GetFetchedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FetchedAt
	}
	return nil
}

func (x *IFTAMonthlyMileage) GetKilometers() float64 {
	if x != nil && x.Kilometers != nil {
		return *x.Kilometers
	}
	return 0
}

type IFTAMonthlyMileages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mileages []*IFTAMonthlyMileage `protobuf:"bytes,1,rep,name=mileages,proto3" json:"mileages,omitempty"`
}

func (x *IFTAMonthlyMileages) Reset() {
	*x = IFTAMonthlyMileages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IFTAMonthlyMileages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IFTAMonthlyMileages) ProtoMessage() {}

func (x *IFTAMonthlyMileages) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IFTAMonthlyMileages.ProtoReflect.Descriptor instead.
func (*IFTAMonthlyMileages) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{11}
}

func (x *IFTAMonthlyMileages) GetMileages() []*IFTAMonthlyMileage {
	if x != nil {
		return x.Mileages
	}
	return nil
}

type DataScienceDailyMileage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vin              string                 `protobuf:"bytes,1,opt,name=vin,proto3" json:"vin,omitempty"`
	Day              *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=day,proto3" json:"day,omitempty"`
	GpsMiles         *float64               `protobuf:"fixed64,3,opt,name=gpsMiles,proto3,oneof" json:"gpsMiles,omitempty"`
	ModelOutputMiles *float64               `protobuf:"fixed64,4,opt,name=modelOutputMiles,proto3,oneof" json:"modelOutputMiles,omitempty"`
	OdometerMiles    *float64               `protobuf:"fixed64,5,opt,name=odometerMiles,proto3,oneof" json:"odometerMiles,omitempty"`
	FetchedAt        *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=fetchedAt,proto3" json:"fetchedAt,omitempty"`
}

func (x *DataScienceDailyMileage) Reset() {
	*x = DataScienceDailyMileage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataScienceDailyMileage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataScienceDailyMileage) ProtoMessage() {}

func (x *DataScienceDailyMileage) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataScienceDailyMileage.ProtoReflect.Descriptor instead.
func (*DataScienceDailyMileage) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{12}
}

func (x *DataScienceDailyMileage) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *DataScienceDailyMileage) GetDay() *timestamppb.Timestamp {
	if x != nil {
		return x.Day
	}
	return nil
}

func (x *DataScienceDailyMileage) GetGpsMiles() float64 {
	if x != nil && x.GpsMiles != nil {
		return *x.GpsMiles
	}
	return 0
}

func (x *DataScienceDailyMileage) GetModelOutputMiles() float64 {
	if x != nil && x.ModelOutputMiles != nil {
		return *x.ModelOutputMiles
	}
	return 0
}

func (x *DataScienceDailyMileage) GetOdometerMiles() float64 {
	if x != nil && x.OdometerMiles != nil {
		return *x.OdometerMiles
	}
	return 0
}

func (x *DataScienceDailyMileage) GetFetchedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FetchedAt
	}
	return nil
}

type DataScienceDailyMileages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mileages []*DataScienceDailyMileage `protobuf:"bytes,1,rep,name=mileages,proto3" json:"mileages,omitempty"`
}

func (x *DataScienceDailyMileages) Reset() {
	*x = DataScienceDailyMileages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataScienceDailyMileages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataScienceDailyMileages) ProtoMessage() {}

func (x *DataScienceDailyMileages) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataScienceDailyMileages.ProtoReflect.Descriptor instead.
func (*DataScienceDailyMileages) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{13}
}

func (x *DataScienceDailyMileages) GetMileages() []*DataScienceDailyMileage {
	if x != nil {
		return x.Mileages
	}
	return nil
}

type VehicleDriverAssignmentLogItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SerializerVersion *int32                 `protobuf:"varint,1,opt,name=serializer_version,json=serializerVersion,proto3,oneof" json:"serializer_version,omitempty"`
	AssignedAtTime    *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=assigned_at_time,json=assignedAtTime,proto3" json:"assigned_at_time,omitempty"`
	StartTime         *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime           *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	TspDriverId       *string                `protobuf:"bytes,5,opt,name=tsp_driver_id,json=tspDriverId,proto3,oneof" json:"tsp_driver_id,omitempty"`
	TspVehicleId      string                 `protobuf:"bytes,6,opt,name=tsp_vehicle_id,json=tspVehicleId,proto3" json:"tsp_vehicle_id,omitempty"`
	IsPassenger       bool                   `protobuf:"varint,7,opt,name=is_passenger,json=isPassenger,proto3" json:"is_passenger,omitempty"`
}

func (x *VehicleDriverAssignmentLogItem) Reset() {
	*x = VehicleDriverAssignmentLogItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleDriverAssignmentLogItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleDriverAssignmentLogItem) ProtoMessage() {}

func (x *VehicleDriverAssignmentLogItem) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleDriverAssignmentLogItem.ProtoReflect.Descriptor instead.
func (*VehicleDriverAssignmentLogItem) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{14}
}

func (x *VehicleDriverAssignmentLogItem) GetSerializerVersion() int32 {
	if x != nil && x.SerializerVersion != nil {
		return *x.SerializerVersion
	}
	return 0
}

func (x *VehicleDriverAssignmentLogItem) GetAssignedAtTime() *timestamppb.Timestamp {
	if x != nil {
		return x.AssignedAtTime
	}
	return nil
}

func (x *VehicleDriverAssignmentLogItem) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *VehicleDriverAssignmentLogItem) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *VehicleDriverAssignmentLogItem) GetTspDriverId() string {
	if x != nil && x.TspDriverId != nil {
		return *x.TspDriverId
	}
	return ""
}

func (x *VehicleDriverAssignmentLogItem) GetTspVehicleId() string {
	if x != nil {
		return x.TspVehicleId
	}
	return ""
}

func (x *VehicleDriverAssignmentLogItem) GetIsPassenger() bool {
	if x != nil {
		return x.IsPassenger
	}
	return false
}

type ISOWeek struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year int32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	Week int32 `protobuf:"varint,2,opt,name=week,proto3" json:"week,omitempty"`
}

func (x *ISOWeek) Reset() {
	*x = ISOWeek{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ISOWeek) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ISOWeek) ProtoMessage() {}

func (x *ISOWeek) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ISOWeek.ProtoReflect.Descriptor instead.
func (*ISOWeek) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{15}
}

func (x *ISOWeek) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *ISOWeek) GetWeek() int32 {
	if x != nil {
		return x.Week
	}
	return 0
}

type PipelineSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConnectionHandleId string       `protobuf:"bytes,1,opt,name=connection_handle_id,json=connectionHandleId,proto3" json:"connection_handle_id,omitempty"`
	Kind               PipelineKind `protobuf:"varint,2,opt,name=kind,proto3,enum=telematicsv2.PipelineKind" json:"kind,omitempty"`
	OnlySuccessful     bool         `protobuf:"varint,3,opt,name=only_successful,json=onlySuccessful,proto3" json:"only_successful,omitempty"`
}

func (x *PipelineSummaryRequest) Reset() {
	*x = PipelineSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineSummaryRequest) ProtoMessage() {}

func (x *PipelineSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineSummaryRequest.ProtoReflect.Descriptor instead.
func (*PipelineSummaryRequest) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{16}
}

func (x *PipelineSummaryRequest) GetConnectionHandleId() string {
	if x != nil {
		return x.ConnectionHandleId
	}
	return ""
}

func (x *PipelineSummaryRequest) GetKind() PipelineKind {
	if x != nil {
		return x.Kind
	}
	return PipelineKind_MillimanScoringPipeline
}

func (x *PipelineSummaryRequest) GetOnlySuccessful() bool {
	if x != nil {
		return x.OnlySuccessful
	}
	return false
}

type Interval struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	End   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *Interval) Reset() {
	*x = Interval{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Interval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Interval) ProtoMessage() {}

func (x *Interval) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Interval.ProtoReflect.Descriptor instead.
func (*Interval) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{17}
}

func (x *Interval) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *Interval) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

type AllPipelineSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConnectionHandleId string       `protobuf:"bytes,1,opt,name=connection_handle_id,json=connectionHandleId,proto3" json:"connection_handle_id,omitempty"`
	Kind               PipelineKind `protobuf:"varint,2,opt,name=kind,proto3,enum=telematicsv2.PipelineKind" json:"kind,omitempty"`
	OverlapInterval    *Interval    `protobuf:"bytes,3,opt,name=overlap_interval,json=overlapInterval,proto3,oneof" json:"overlap_interval,omitempty"`
}

func (x *AllPipelineSummaryRequest) Reset() {
	*x = AllPipelineSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllPipelineSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllPipelineSummaryRequest) ProtoMessage() {}

func (x *AllPipelineSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllPipelineSummaryRequest.ProtoReflect.Descriptor instead.
func (*AllPipelineSummaryRequest) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{18}
}

func (x *AllPipelineSummaryRequest) GetConnectionHandleId() string {
	if x != nil {
		return x.ConnectionHandleId
	}
	return ""
}

func (x *AllPipelineSummaryRequest) GetKind() PipelineKind {
	if x != nil {
		return x.Kind
	}
	return PipelineKind_MillimanScoringPipeline
}

func (x *AllPipelineSummaryRequest) GetOverlapInterval() *Interval {
	if x != nil {
		return x.OverlapInterval
	}
	return nil
}

type PipelineDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PipelineId string                          `protobuf:"bytes,1,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	Richness   PipelineDetailsRequest_Richness `protobuf:"varint,2,opt,name=richness,proto3,enum=telematicsv2.PipelineDetailsRequest_Richness" json:"richness,omitempty"`
}

func (x *PipelineDetailsRequest) Reset() {
	*x = PipelineDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineDetailsRequest) ProtoMessage() {}

func (x *PipelineDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineDetailsRequest.ProtoReflect.Descriptor instead.
func (*PipelineDetailsRequest) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{19}
}

func (x *PipelineDetailsRequest) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *PipelineDetailsRequest) GetRichness() PipelineDetailsRequest_Richness {
	if x != nil {
		return x.Richness
	}
	return PipelineDetailsRequest_OPTIMIZE_FOR_PERFORMANCE
}

type IFTAMonthlyMileageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConnectionHandleId string                 `protobuf:"bytes,1,opt,name=connectionHandleId,proto3" json:"connectionHandleId,omitempty"`
	MonthOfYear        *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=monthOfYear,proto3" json:"monthOfYear,omitempty"`
}

func (x *IFTAMonthlyMileageRequest) Reset() {
	*x = IFTAMonthlyMileageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IFTAMonthlyMileageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IFTAMonthlyMileageRequest) ProtoMessage() {}

func (x *IFTAMonthlyMileageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IFTAMonthlyMileageRequest.ProtoReflect.Descriptor instead.
func (*IFTAMonthlyMileageRequest) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{20}
}

func (x *IFTAMonthlyMileageRequest) GetConnectionHandleId() string {
	if x != nil {
		return x.ConnectionHandleId
	}
	return ""
}

func (x *IFTAMonthlyMileageRequest) GetMonthOfYear() *timestamppb.Timestamp {
	if x != nil {
		return x.MonthOfYear
	}
	return nil
}

type DataScienceDailyMileageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConnectionHandleId string                 `protobuf:"bytes,1,opt,name=connectionHandleId,proto3" json:"connectionHandleId,omitempty"`
	From               *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=from,proto3" json:"from,omitempty"`
	To                 *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=to,proto3" json:"to,omitempty"`
}

func (x *DataScienceDailyMileageRequest) Reset() {
	*x = DataScienceDailyMileageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataScienceDailyMileageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataScienceDailyMileageRequest) ProtoMessage() {}

func (x *DataScienceDailyMileageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataScienceDailyMileageRequest.ProtoReflect.Descriptor instead.
func (*DataScienceDailyMileageRequest) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{21}
}

func (x *DataScienceDailyMileageRequest) GetConnectionHandleId() string {
	if x != nil {
		return x.ConnectionHandleId
	}
	return ""
}

func (x *DataScienceDailyMileageRequest) GetFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *DataScienceDailyMileageRequest) GetTo() *timestamppb.Timestamp {
	if x != nil {
		return x.To
	}
	return nil
}

type VehicleDriverAssignmentLogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId                          string                 `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	RequestInterval                   *Interval              `protobuf:"bytes,2,opt,name=request_interval,json=requestInterval,proto3" json:"request_interval,omitempty"`
	IdentifierType                    IdentifierType         `protobuf:"varint,3,opt,name=identifier_type,json=identifierType,proto3,enum=telematicsv2.IdentifierType" json:"identifier_type,omitempty"`
	IdentifierValue                   string                 `protobuf:"bytes,4,opt,name=identifier_value,json=identifierValue,proto3" json:"identifier_value,omitempty"`
	NormalizerVersion                 *int32                 `protobuf:"varint,5,opt,name=normalizer_version,json=normalizerVersion,proto3,oneof" json:"normalizer_version,omitempty"`
	OverrideCachingBehaviorOnInterval *bool                  `protobuf:"varint,6,opt,name=override_caching_behavior_on_interval,json=overrideCachingBehaviorOnInterval,proto3,oneof" json:"override_caching_behavior_on_interval,omitempty"`
	IgnoreProviderCache               *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=ignore_provider_cache,json=ignoreProviderCache,proto3,oneof" json:"ignore_provider_cache,omitempty"`
}

func (x *VehicleDriverAssignmentLogRequest) Reset() {
	*x = VehicleDriverAssignmentLogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleDriverAssignmentLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleDriverAssignmentLogRequest) ProtoMessage() {}

func (x *VehicleDriverAssignmentLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleDriverAssignmentLogRequest.ProtoReflect.Descriptor instead.
func (*VehicleDriverAssignmentLogRequest) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{22}
}

func (x *VehicleDriverAssignmentLogRequest) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *VehicleDriverAssignmentLogRequest) GetRequestInterval() *Interval {
	if x != nil {
		return x.RequestInterval
	}
	return nil
}

func (x *VehicleDriverAssignmentLogRequest) GetIdentifierType() IdentifierType {
	if x != nil {
		return x.IdentifierType
	}
	return IdentifierType_UNDEFINED
}

func (x *VehicleDriverAssignmentLogRequest) GetIdentifierValue() string {
	if x != nil {
		return x.IdentifierValue
	}
	return ""
}

func (x *VehicleDriverAssignmentLogRequest) GetNormalizerVersion() int32 {
	if x != nil && x.NormalizerVersion != nil {
		return *x.NormalizerVersion
	}
	return 0
}

func (x *VehicleDriverAssignmentLogRequest) GetOverrideCachingBehaviorOnInterval() bool {
	if x != nil && x.OverrideCachingBehaviorOnInterval != nil {
		return *x.OverrideCachingBehaviorOnInterval
	}
	return false
}

func (x *VehicleDriverAssignmentLogRequest) GetIgnoreProviderCache() *timestamppb.Timestamp {
	if x != nil {
		return x.IgnoreProviderCache
	}
	return nil
}

type TriggerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind    PipelineKind `protobuf:"varint,1,opt,name=kind,proto3,enum=telematicsv2.PipelineKind" json:"kind,omitempty"`
	Version int32        `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	Input   []byte       `protobuf:"bytes,3,opt,name=input,proto3" json:"input,omitempty"`
}

func (x *TriggerRequest) Reset() {
	*x = TriggerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerRequest) ProtoMessage() {}

func (x *TriggerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerRequest.ProtoReflect.Descriptor instead.
func (*TriggerRequest) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{23}
}

func (x *TriggerRequest) GetKind() PipelineKind {
	if x != nil {
		return x.Kind
	}
	return PipelineKind_MillimanScoringPipeline
}

func (x *TriggerRequest) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *TriggerRequest) GetInput() []byte {
	if x != nil {
		return x.Input
	}
	return nil
}

type JobRunId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId string `protobuf:"bytes,1,opt,name=JobId,proto3" json:"JobId,omitempty"`
	RunId int32  `protobuf:"varint,2,opt,name=runId,proto3" json:"runId,omitempty"`
}

func (x *JobRunId) Reset() {
	*x = JobRunId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobRunId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobRunId) ProtoMessage() {}

func (x *JobRunId) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobRunId.ProtoReflect.Descriptor instead.
func (*JobRunId) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{24}
}

func (x *JobRunId) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *JobRunId) GetRunId() int32 {
	if x != nil {
		return x.RunId
	}
	return 0
}

type PipelineSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PipelineId         string                 `protobuf:"bytes,1,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	ConnectionHandleId string                 `protobuf:"bytes,2,opt,name=connection_handle_id,json=connectionHandleId,proto3" json:"connection_handle_id,omitempty"`
	Kind               PipelineKind           `protobuf:"varint,3,opt,name=kind,proto3,enum=telematicsv2.PipelineKind" json:"kind,omitempty"`
	Status             ExecutionStatus        `protobuf:"varint,5,opt,name=status,proto3,enum=telematicsv2.ExecutionStatus" json:"status,omitempty"`
	StartedAt          *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	FinishedAt         *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=finished_at,json=finishedAt,proto3,oneof" json:"finished_at,omitempty"`
	CurrentStep        string                 `protobuf:"bytes,8,opt,name=current_step,json=currentStep,proto3" json:"current_step,omitempty"`
	JobrunId           *JobRunId              `protobuf:"bytes,9,opt,name=jobrun_id,json=jobrunId,proto3" json:"jobrun_id,omitempty"`
}

func (x *PipelineSummary) Reset() {
	*x = PipelineSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineSummary) ProtoMessage() {}

func (x *PipelineSummary) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineSummary.ProtoReflect.Descriptor instead.
func (*PipelineSummary) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{25}
}

func (x *PipelineSummary) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *PipelineSummary) GetConnectionHandleId() string {
	if x != nil {
		return x.ConnectionHandleId
	}
	return ""
}

func (x *PipelineSummary) GetKind() PipelineKind {
	if x != nil {
		return x.Kind
	}
	return PipelineKind_MillimanScoringPipeline
}

func (x *PipelineSummary) GetStatus() ExecutionStatus {
	if x != nil {
		return x.Status
	}
	return ExecutionStatus_Unknown
}

func (x *PipelineSummary) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *PipelineSummary) GetFinishedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FinishedAt
	}
	return nil
}

func (x *PipelineSummary) GetCurrentStep() string {
	if x != nil {
		return x.CurrentStep
	}
	return ""
}

func (x *PipelineSummary) GetJobrunId() *JobRunId {
	if x != nil {
		return x.JobrunId
	}
	return nil
}

type ManyPipelineSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Summaries []*PipelineSummary `protobuf:"bytes,1,rep,name=summaries,proto3" json:"summaries,omitempty"`
}

func (x *ManyPipelineSummary) Reset() {
	*x = ManyPipelineSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManyPipelineSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManyPipelineSummary) ProtoMessage() {}

func (x *ManyPipelineSummary) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManyPipelineSummary.ProtoReflect.Descriptor instead.
func (*ManyPipelineSummary) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{26}
}

func (x *ManyPipelineSummary) GetSummaries() []*PipelineSummary {
	if x != nil {
		return x.Summaries
	}
	return nil
}

type PipelineId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *PipelineId) Reset() {
	*x = PipelineId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineId) ProtoMessage() {}

func (x *PipelineId) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineId.ProtoReflect.Descriptor instead.
func (*PipelineId) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{27}
}

func (x *PipelineId) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type PipelineDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Job *Job `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *PipelineDetails) Reset() {
	*x = PipelineDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineDetails) ProtoMessage() {}

func (x *PipelineDetails) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineDetails.ProtoReflect.Descriptor instead.
func (*PipelineDetails) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{28}
}

func (x *PipelineDetails) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

type Job struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	JobrunId   *JobRunId              `protobuf:"bytes,2,opt,name=jobrun_id,json=jobrunId,proto3" json:"jobrun_id,omitempty"`
	Status     ExecutionStatus        `protobuf:"varint,3,opt,name=status,proto3,enum=telematicsv2.ExecutionStatus" json:"status,omitempty"`
	Tasks      []*Task                `protobuf:"bytes,4,rep,name=tasks,proto3" json:"tasks,omitempty"`
	Details    *anypb.Any             `protobuf:"bytes,5,opt,name=details,proto3" json:"details,omitempty"`
	StartedAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=startedAt,proto3" json:"startedAt,omitempty"`
	FinishedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=finishedAt,proto3,oneof" json:"finishedAt,omitempty"`
	Error      string                 `protobuf:"bytes,8,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *Job) Reset() {
	*x = Job{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job) ProtoMessage() {}

func (x *Job) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job.ProtoReflect.Descriptor instead.
func (*Job) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{29}
}

func (x *Job) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Job) GetJobrunId() *JobRunId {
	if x != nil {
		return x.JobrunId
	}
	return nil
}

func (x *Job) GetStatus() ExecutionStatus {
	if x != nil {
		return x.Status
	}
	return ExecutionStatus_Unknown
}

func (x *Job) GetTasks() []*Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *Job) GetDetails() *anypb.Any {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *Job) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *Job) GetFinishedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FinishedAt
	}
	return nil
}

func (x *Job) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Status  ExecutionStatus `protobuf:"varint,2,opt,name=status,proto3,enum=telematicsv2.ExecutionStatus" json:"status,omitempty"`
	SubJobs []*Job          `protobuf:"bytes,3,rep,name=sub_jobs,json=subJobs,proto3" json:"sub_jobs,omitempty"`
}

func (x *Task) Reset() {
	*x = Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{30}
}

func (x *Task) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Task) GetStatus() ExecutionStatus {
	if x != nil {
		return x.Status
	}
	return ExecutionStatus_Unknown
}

func (x *Task) GetSubJobs() []*Job {
	if x != nil {
		return x.SubJobs
	}
	return nil
}

type MillimanScoringPipelineDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DsArtifacts *DataScienceArtifacts `protobuf:"bytes,2,opt,name=ds_artifacts,json=dsArtifacts,proto3" json:"ds_artifacts,omitempty"`
}

func (x *MillimanScoringPipelineDetails) Reset() {
	*x = MillimanScoringPipelineDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MillimanScoringPipelineDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MillimanScoringPipelineDetails) ProtoMessage() {}

func (x *MillimanScoringPipelineDetails) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MillimanScoringPipelineDetails.ProtoReflect.Descriptor instead.
func (*MillimanScoringPipelineDetails) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{31}
}

func (x *MillimanScoringPipelineDetails) GetDsArtifacts() *DataScienceArtifacts {
	if x != nil {
		return x.DsArtifacts
	}
	return nil
}

type MetaflowDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunId string `protobuf:"bytes,1,opt,name=run_id,json=runId,proto3" json:"run_id,omitempty"`
	State string `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *MetaflowDetails) Reset() {
	*x = MetaflowDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetaflowDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaflowDetails) ProtoMessage() {}

func (x *MetaflowDetails) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaflowDetails.ProtoReflect.Descriptor instead.
func (*MetaflowDetails) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{32}
}

func (x *MetaflowDetails) GetRunId() string {
	if x != nil {
		return x.RunId
	}
	return ""
}

func (x *MetaflowDetails) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

type DataScienceArtifacts struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DsRuns map[string]*MetaflowDetails `protobuf:"bytes,1,rep,name=ds_runs,json=dsRuns,proto3" json:"ds_runs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DataScienceArtifacts) Reset() {
	*x = DataScienceArtifacts{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataScienceArtifacts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataScienceArtifacts) ProtoMessage() {}

func (x *DataScienceArtifacts) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataScienceArtifacts.ProtoReflect.Descriptor instead.
func (*DataScienceArtifacts) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{33}
}

func (x *DataScienceArtifacts) GetDsRuns() map[string]*MetaflowDetails {
	if x != nil {
		return x.DsRuns
	}
	return nil
}

type EntityId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  IdentifierType `protobuf:"varint,1,opt,name=type,proto3,enum=telematicsv2.IdentifierType" json:"type,omitempty"`
	Value string         `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *EntityId) Reset() {
	*x = EntityId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntityId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityId) ProtoMessage() {}

func (x *EntityId) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityId.ProtoReflect.Descriptor instead.
func (*EntityId) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{34}
}

func (x *EntityId) GetType() IdentifierType {
	if x != nil {
		return x.Type
	}
	return IdentifierType_UNDEFINED
}

func (x *EntityId) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type NormalisedStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind    ResourceType `protobuf:"varint,1,opt,name=kind,proto3,enum=telematicsv2.ResourceType" json:"kind,omitempty"`
	Version int32        `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *NormalisedStat) Reset() {
	*x = NormalisedStat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NormalisedStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalisedStat) ProtoMessage() {}

func (x *NormalisedStat) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalisedStat.ProtoReflect.Descriptor instead.
func (*NormalisedStat) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{35}
}

func (x *NormalisedStat) GetKind() ResourceType {
	if x != nil {
		return x.Kind
	}
	return ResourceType_GPSLog
}

func (x *NormalisedStat) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type DataFileListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleId          string                            `protobuf:"bytes,1,opt,name=handle_id,json=handleId,proto3" json:"handle_id,omitempty"`
	DataInterval      *Interval                         `protobuf:"bytes,2,opt,name=data_interval,json=dataInterval,proto3" json:"data_interval,omitempty"`
	DataType          *NormalisedStat                   `protobuf:"bytes,3,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"`
	Entities          []*EntityId                       `protobuf:"bytes,4,rep,name=entities,proto3" json:"entities,omitempty"`
	AggregateBy       IdentifierType                    `protobuf:"varint,5,opt,name=aggregate_by,json=aggregateBy,proto3,enum=telematicsv2.IdentifierType" json:"aggregate_by,omitempty"`
	AcceptPartialData bool                              `protobuf:"varint,6,opt,name=accept_partial_data,json=acceptPartialData,proto3" json:"accept_partial_data,omitempty"`
	Mode              DataFileListRequest_OperationMode `protobuf:"varint,7,opt,name=mode,proto3,enum=telematicsv2.DataFileListRequest_OperationMode" json:"mode,omitempty"`
}

func (x *DataFileListRequest) Reset() {
	*x = DataFileListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataFileListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataFileListRequest) ProtoMessage() {}

func (x *DataFileListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataFileListRequest.ProtoReflect.Descriptor instead.
func (*DataFileListRequest) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{36}
}

func (x *DataFileListRequest) GetHandleId() string {
	if x != nil {
		return x.HandleId
	}
	return ""
}

func (x *DataFileListRequest) GetDataInterval() *Interval {
	if x != nil {
		return x.DataInterval
	}
	return nil
}

func (x *DataFileListRequest) GetDataType() *NormalisedStat {
	if x != nil {
		return x.DataType
	}
	return nil
}

func (x *DataFileListRequest) GetEntities() []*EntityId {
	if x != nil {
		return x.Entities
	}
	return nil
}

func (x *DataFileListRequest) GetAggregateBy() IdentifierType {
	if x != nil {
		return x.AggregateBy
	}
	return IdentifierType_UNDEFINED
}

func (x *DataFileListRequest) GetAcceptPartialData() bool {
	if x != nil {
		return x.AcceptPartialData
	}
	return false
}

func (x *DataFileListRequest) GetMode() DataFileListRequest_OperationMode {
	if x != nil {
		return x.Mode
	}
	return DataFileListRequest_Standard
}

type DataFileListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AggFileLists     map[string]*DataFiles `protobuf:"bytes,1,rep,name=agg_file_lists,json=aggFileLists,proto3" json:"agg_file_lists,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	UnknownFileLists map[string]*DataFiles `protobuf:"bytes,2,rep,name=unknown_file_lists,json=unknownFileLists,proto3" json:"unknown_file_lists,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DataFileListResponse) Reset() {
	*x = DataFileListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataFileListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataFileListResponse) ProtoMessage() {}

func (x *DataFileListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataFileListResponse.ProtoReflect.Descriptor instead.
func (*DataFileListResponse) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{37}
}

func (x *DataFileListResponse) GetAggFileLists() map[string]*DataFiles {
	if x != nil {
		return x.AggFileLists
	}
	return nil
}

func (x *DataFileListResponse) GetUnknownFileLists() map[string]*DataFiles {
	if x != nil {
		return x.UnknownFileLists
	}
	return nil
}

type DataFiles struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Span          *Interval       `protobuf:"bytes,1,opt,name=span,proto3" json:"span,omitempty"`
	NumDataPoints int32           `protobuf:"varint,2,opt,name=num_data_points,json=numDataPoints,proto3" json:"num_data_points,omitempty"`
	Files         []*FileReadTask `protobuf:"bytes,3,rep,name=files,proto3" json:"files,omitempty"`
}

func (x *DataFiles) Reset() {
	*x = DataFiles{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataFiles) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataFiles) ProtoMessage() {}

func (x *DataFiles) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataFiles.ProtoReflect.Descriptor instead.
func (*DataFiles) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{38}
}

func (x *DataFiles) GetSpan() *Interval {
	if x != nil {
		return x.Span
	}
	return nil
}

func (x *DataFiles) GetNumDataPoints() int32 {
	if x != nil {
		return x.NumDataPoints
	}
	return 0
}

func (x *DataFiles) GetFiles() []*FileReadTask {
	if x != nil {
		return x.Files
	}
	return nil
}

type FileReadTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Span           *Interval `protobuf:"bytes,1,opt,name=span,proto3" json:"span,omitempty"`
	NumDataPoints  int32     `protobuf:"varint,2,opt,name=num_data_points,json=numDataPoints,proto3" json:"num_data_points,omitempty"`
	S3FilePath     string    `protobuf:"bytes,3,opt,name=s3_file_path,json=s3FilePath,proto3" json:"s3_file_path,omitempty"`
	IntervalFilter *Interval `protobuf:"bytes,4,opt,name=interval_filter,json=intervalFilter,proto3,oneof" json:"interval_filter,omitempty"`
}

func (x *FileReadTask) Reset() {
	*x = FileReadTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileReadTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileReadTask) ProtoMessage() {}

func (x *FileReadTask) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileReadTask.ProtoReflect.Descriptor instead.
func (*FileReadTask) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{39}
}

func (x *FileReadTask) GetSpan() *Interval {
	if x != nil {
		return x.Span
	}
	return nil
}

func (x *FileReadTask) GetNumDataPoints() int32 {
	if x != nil {
		return x.NumDataPoints
	}
	return 0
}

func (x *FileReadTask) GetS3FilePath() string {
	if x != nil {
		return x.S3FilePath
	}
	return ""
}

func (x *FileReadTask) GetIntervalFilter() *Interval {
	if x != nil {
		return x.IntervalFilter
	}
	return nil
}

type CarfaxVinsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vins      []string `protobuf:"bytes,1,rep,name=vins,proto3" json:"vins,omitempty"`
	Requester string   `protobuf:"bytes,2,opt,name=requester,proto3" json:"requester,omitempty"`
}

func (x *CarfaxVinsRequest) Reset() {
	*x = CarfaxVinsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarfaxVinsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarfaxVinsRequest) ProtoMessage() {}

func (x *CarfaxVinsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarfaxVinsRequest.ProtoReflect.Descriptor instead.
func (*CarfaxVinsRequest) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{40}
}

func (x *CarfaxVinsRequest) GetVins() []string {
	if x != nil {
		return x.Vins
	}
	return nil
}

func (x *CarfaxVinsRequest) GetRequester() string {
	if x != nil {
		return x.Requester
	}
	return ""
}

type CarfaxVinsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Created     int32    `protobuf:"varint,1,opt,name=created,proto3" json:"created,omitempty"`
	Renewed     int32    `protobuf:"varint,2,opt,name=renewed,proto3" json:"renewed,omitempty"`
	InvalidVins []string `protobuf:"bytes,3,rep,name=invalid_vins,json=invalidVins,proto3" json:"invalid_vins,omitempty"`
}

func (x *CarfaxVinsResponse) Reset() {
	*x = CarfaxVinsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarfaxVinsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarfaxVinsResponse) ProtoMessage() {}

func (x *CarfaxVinsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarfaxVinsResponse.ProtoReflect.Descriptor instead.
func (*CarfaxVinsResponse) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{41}
}

func (x *CarfaxVinsResponse) GetCreated() int32 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *CarfaxVinsResponse) GetRenewed() int32 {
	if x != nil {
		return x.Renewed
	}
	return 0
}

func (x *CarfaxVinsResponse) GetInvalidVins() []string {
	if x != nil {
		return x.InvalidVins
	}
	return nil
}

type OAuthTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessToken string `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
}

func (x *OAuthTokenResponse) Reset() {
	*x = OAuthTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OAuthTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OAuthTokenResponse) ProtoMessage() {}

func (x *OAuthTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OAuthTokenResponse.ProtoReflect.Descriptor instead.
func (*OAuthTokenResponse) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{42}
}

func (x *OAuthTokenResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

type SamsaraSafetySettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawJson string `protobuf:"bytes,1,opt,name=raw_json,json=rawJson,proto3" json:"raw_json,omitempty"`
}

func (x *SamsaraSafetySettingsResponse) Reset() {
	*x = SamsaraSafetySettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_telematicsv2_api_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SamsaraSafetySettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SamsaraSafetySettingsResponse) ProtoMessage() {}

func (x *SamsaraSafetySettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_telematicsv2_api_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SamsaraSafetySettingsResponse.ProtoReflect.Descriptor instead.
func (*SamsaraSafetySettingsResponse) Descriptor() ([]byte, []int) {
	return file_telematicsv2_api_proto_rawDescGZIP(), []int{43}
}

func (x *SamsaraSafetySettingsResponse) GetRawJson() string {
	if x != nil {
		return x.RawJson
	}
	return ""
}

var File_telematicsv2_api_proto protoreflect.FileDescriptor

var file_telematicsv2_api_proto_rawDesc = []byte{
	0x0a, 0x16, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2f, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x2a, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xeb,
	0x06, 0x0a, 0x18, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x74,
	0x73, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74,
	0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x11, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x42, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x52, 0x0a, 0x15,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x01, 0x52, 0x12, 0x64, 0x61, 0x74, 0x61, 0x50,
	0x75, 0x6c, 0x6c, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x6b, 0x69, 0x6e, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x4b,
	0x69, 0x6e, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x42,
	0x0a, 0x1b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x18, 0x64, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88,
	0x01, 0x01, 0x12, 0x50, 0x0a, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x03, 0x52, 0x11,
	0x6c, 0x61, 0x73, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a, 0x1f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x04, 0x52, 0x1b, 0x6c, 0x61, 0x73,
	0x74, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x47, 0x0a, 0x20, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1d, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69,
	0x76, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70,
	0x75, 0x6c, 0x6c, 0x5f, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42,
	0x1e, 0x0a, 0x1c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x17, 0x0a, 0x15, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x61, 0x74, 0x42, 0x22, 0x0a, 0x20, 0x5f, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x5f, 0x68, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x61, 0x74, 0x22, 0x92, 0x05, 0x0a,
	0x07, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73,
	0x5f, 0x76, 0x69, 0x6e, 0x5f, 0x66, 0x61, 0x6b, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x56, 0x69, 0x6e, 0x46, 0x61, 0x6b, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x73,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x73, 0x70, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61,
	0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12,
	0x22, 0x0a, 0x0a, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x4e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x15, 0x74, 0x73, 0x70, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x01, 0x52, 0x13, 0x74, 0x73, 0x70, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x61,
	0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x45, 0x0a, 0x0e, 0x74,
	0x73, 0x70, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48,
	0x02, 0x52, 0x0c, 0x74, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x45, 0x0a, 0x0e, 0x74, 0x73, 0x70, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x03, 0x52, 0x0c, 0x74, 0x73, 0x70, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x6c,
	0x69, 0x61, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x74, 0x73, 0x70,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x74, 0x73, 0x70, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x74, 0x73, 0x70, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x4a, 0x04,
	0x08, 0x05, 0x10, 0x06, 0x4a, 0x04, 0x08, 0x06, 0x10, 0x07, 0x4a, 0x04, 0x08, 0x07, 0x10, 0x08,
	0x4a, 0x04, 0x08, 0x0c, 0x10, 0x0d, 0x52, 0x08, 0x74, 0x73, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x52, 0x04, 0x6d, 0x61, 0x6b, 0x65, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x79,
	0x65, 0x61, 0x72, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x22, 0xc5, 0x05, 0x0a, 0x06, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x66, 0x75, 0x6c,
	0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08,
	0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x61,
	0x6c, 0x69, 0x61, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x01, 0x52, 0x09, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x2a, 0x0a, 0x0e, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0d, 0x6c, 0x69, 0x63, 0x65, 0x6e,
	0x73, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x6c,
	0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x03, 0x52, 0x0c, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x64, 0x73, 0x12, 0x37, 0x0a, 0x15, 0x74, 0x73, 0x70, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x04, 0x52, 0x13, 0x74, 0x73, 0x70, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x45, 0x0a, 0x0e, 0x74, 0x73, 0x70,
	0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x05, 0x52,
	0x0c, 0x74, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x45, 0x0a, 0x0e, 0x74, 0x73, 0x70, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x48, 0x06, 0x52, 0x0c, 0x74, 0x73, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x66, 0x75, 0x6c, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6c, 0x69, 0x63, 0x65,
	0x6e, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x74, 0x73,
	0x70, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x74, 0x73, 0x70, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x74, 0x73, 0x70, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0x9f, 0x03, 0x0a, 0x0c, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0d, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x45,
	0x0a, 0x0e, 0x74, 0x73, 0x70, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x48, 0x01, 0x52, 0x0c, 0x74, 0x73, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x0e, 0x74, 0x73, 0x70, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x02, 0x52, 0x0c, 0x74, 0x73, 0x70,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x74, 0x73, 0x70, 0x5f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x74, 0x73, 0x70,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0x40, 0x0a, 0x0b, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x08, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x52, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x22, 0x3c, 0x0a,
	0x0a, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x07, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x72, 0x69, 0x76,
	0x65, 0x72, 0x52, 0x07, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x22, 0x46, 0x0a, 0x10, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x32, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x06, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x22, 0x2f, 0x0a, 0x0c, 0x50, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x70, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x4a, 0x0a, 0x09, 0x59, 0x65, 0x61, 0x72, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x29, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x22, 0xe5, 0x01, 0x0a, 0x12, 0x49, 0x46, 0x54, 0x41, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79,
	0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e, 0x12, 0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x6e,
	0x74, 0x68, 0x4f, 0x66, 0x59, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x59, 0x65,
	0x61, 0x72, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x0b, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x4f, 0x66,
	0x59, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x05, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x65, 0x64, 0x41, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x23, 0x0a, 0x0a, 0x6b, 0x69, 0x6c, 0x6f, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x6b, 0x69, 0x6c, 0x6f,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6b, 0x69,
	0x6c, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x22, 0x53, 0x0a, 0x13, 0x49, 0x46, 0x54, 0x41,
	0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x73, 0x12,
	0x3c, 0x0a, 0x08, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32,
	0x2e, 0x49, 0x46, 0x54, 0x41, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65,
	0x61, 0x67, 0x65, 0x52, 0x08, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x73, 0x22, 0xc4, 0x02,
	0x0a, 0x17, 0x44, 0x61, 0x74, 0x61, 0x53, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e, 0x12, 0x2c, 0x0a, 0x03, 0x64,
	0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x64, 0x61, 0x79, 0x12, 0x1f, 0x0a, 0x08, 0x67, 0x70, 0x73,
	0x4d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x08, 0x67,
	0x70, 0x73, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x10, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x01, 0x52, 0x10, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0d, 0x6f,
	0x64, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x48, 0x02, 0x52, 0x0d, 0x6f, 0x64, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4d, 0x69,
	0x6c, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a, 0x09, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65,
	0x64, 0x41, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x66, 0x65, 0x74, 0x63, 0x68, 0x65, 0x64, 0x41, 0x74,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x67, 0x70, 0x73, 0x4d, 0x69, 0x6c, 0x65, 0x73, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4d, 0x69, 0x6c,
	0x65, 0x73, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6f, 0x64, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4d,
	0x69, 0x6c, 0x65, 0x73, 0x22, 0x5d, 0x0a, 0x18, 0x44, 0x61, 0x74, 0x61, 0x53, 0x63, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x73,
	0x12, 0x41, 0x0a, 0x08, 0x6d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76,
	0x32, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x53, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x08, 0x6d, 0x69, 0x6c, 0x65, 0x61,
	0x67, 0x65, 0x73, 0x22, 0xa7, 0x03, 0x0a, 0x1e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x4c,
	0x6f, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x32, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x69, 0x7a, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x48, 0x00, 0x52, 0x11, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x72,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x44, 0x0a, 0x10, 0x61, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0e, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0d, 0x74, 0x73, 0x70, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0b, 0x74, 0x73, 0x70,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0e, 0x74,
	0x73, 0x70, 0x5f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x73, 0x70, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65,
	0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x50, 0x61, 0x73, 0x73, 0x65,
	0x6e, 0x67, 0x65, 0x72, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x69,
	0x7a, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x74, 0x73, 0x70, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0x31, 0x0a,
	0x07, 0x49, 0x53, 0x4f, 0x57, 0x65, 0x65, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x77, 0x65, 0x65, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x77, 0x65, 0x65, 0x6b,
	0x22, 0xa3, 0x01, 0x0a, 0x16, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a,
	0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x6f, 0x6e, 0x6c, 0x79, 0x53, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x22, 0x6a, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x12, 0x2c, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x65,
	0x6e, 0x64, 0x22, 0xda, 0x01, 0x0a, 0x19, 0x41, 0x6c, 0x6c, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x30, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x49, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69,
	0x6e, 0x64, 0x12, 0x46, 0x0a, 0x10, 0x6f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x0f, 0x6f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6f,
	0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x22,
	0xe3, 0x01, 0x0a, 0x16, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x08, 0x72,
	0x69, 0x63, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e,
	0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x52, 0x69, 0x63, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x08, 0x72, 0x69,
	0x63, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x22, 0x5d, 0x0a, 0x08, 0x52, 0x69, 0x63, 0x68, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x50, 0x54, 0x49, 0x4d, 0x49, 0x5a, 0x45, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x00,
	0x12, 0x18, 0x0a, 0x14, 0x56, 0x49, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x56, 0x49,
	0x53, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x57, 0x45,
	0x45, 0x4b, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x56, 0x49, 0x53, 0x49, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x10, 0x02, 0x22, 0x89, 0x01, 0x0a, 0x19, 0x49, 0x46, 0x54, 0x41, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0b, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x4f, 0x66, 0x59, 0x65,
	0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x4f, 0x66, 0x59, 0x65, 0x61,
	0x72, 0x22, 0xac, 0x01, 0x0a, 0x1e, 0x44, 0x61, 0x74, 0x61, 0x53, 0x63, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04,
	0x66, 0x72, 0x6f, 0x6d, 0x12, 0x2a, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x6f,
	0x22, 0xb0, 0x04, 0x0a, 0x21, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x72, 0x69, 0x76,
	0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x45, 0x0a, 0x0f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1c, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x32, 0x0a, 0x12, 0x6e, 0x6f, 0x72, 0x6d,
	0x61, 0x6c, 0x69, 0x7a, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x11, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x55, 0x0a, 0x25,
	0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x67,
	0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x5f, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x21, 0x6f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x43, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x42, 0x65,
	0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x4f, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x88, 0x01, 0x01, 0x12, 0x53, 0x0a, 0x15, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x02,
	0x52, 0x13, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x43, 0x61, 0x63, 0x68, 0x65, 0x88, 0x01, 0x01, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x6e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42,
	0x28, 0x0a, 0x26, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x63, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x5f, 0x6f, 0x6e,
	0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x69, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x61,
	0x63, 0x68, 0x65, 0x22, 0x70, 0x0a, 0x0e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x76, 0x32, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52,
	0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05,
	0x69, 0x6e, 0x70, 0x75, 0x74, 0x22, 0x36, 0x0a, 0x08, 0x4a, 0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x75, 0x6e, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x22, 0xb0, 0x03,
	0x0a, 0x0f, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76,
	0x32, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04,
	0x6b, 0x69, 0x6e, 0x64, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x40, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x65, 0x70, 0x12, 0x33, 0x0a, 0x09, 0x6a,
	0x6f, 0x62, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x4a, 0x6f,
	0x62, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x52, 0x08, 0x6a, 0x6f, 0x62, 0x72, 0x75, 0x6e, 0x49, 0x64,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x22, 0x52, 0x0a, 0x13, 0x4d, 0x61, 0x6e, 0x79, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x3b, 0x0a, 0x09, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x09, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x69, 0x65, 0x73, 0x22, 0x22, 0x0a, 0x0a, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x36, 0x0a, 0x0f, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x23, 0x0a, 0x03, 0x6a,
	0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x03, 0x6a, 0x6f, 0x62,
	0x22, 0xff, 0x02, 0x0a, 0x03, 0x4a, 0x6f, 0x62, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x09,
	0x6a, 0x6f, 0x62, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x4a,
	0x6f, 0x62, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x52, 0x08, 0x6a, 0x6f, 0x62, 0x72, 0x75, 0x6e, 0x49,
	0x64, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1d, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32,
	0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x74, 0x61, 0x73,
	0x6b, 0x73, 0x12, 0x2e, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3f, 0x0a, 0x0a,
	0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x0a,
	0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x41, 0x74, 0x22, 0x7f, 0x0a, 0x04, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d,
	0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x6a, 0x6f, 0x62,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x4a, 0x6f, 0x62, 0x52, 0x07, 0x73, 0x75, 0x62, 0x4a,
	0x6f, 0x62, 0x73, 0x22, 0x7b, 0x0a, 0x1e, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x6e, 0x53,
	0x63, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x45, 0x0a, 0x0c, 0x64, 0x73, 0x5f, 0x61, 0x72, 0x74, 0x69,
	0x66, 0x61, 0x63, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x53,
	0x63, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x52,
	0x0b, 0x64, 0x73, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x4a, 0x04, 0x08, 0x01,
	0x10, 0x02, 0x52, 0x0c, 0x64, 0x70, 0x5f, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73,
	0x22, 0x3e, 0x0a, 0x0f, 0x4d, 0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x22, 0xb9, 0x01, 0x0a, 0x14, 0x44, 0x61, 0x74, 0x61, 0x53, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x65,
	0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x12, 0x47, 0x0a, 0x07, 0x64, 0x73, 0x5f,
	0x72, 0x75, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x53, 0x63,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x44,
	0x73, 0x52, 0x75, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x64, 0x73, 0x52, 0x75,
	0x6e, 0x73, 0x1a, 0x58, 0x0a, 0x0b, 0x44, 0x73, 0x52, 0x75, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76,
	0x32, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x52, 0x0a, 0x08,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0x5a, 0x0a, 0x0e, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x69, 0x73, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x6b, 0x69,
	0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xbf, 0x03, 0x0a,
	0x13, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x3b, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x52, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x39,
	0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32,
	0x2e, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x69, 0x73, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x3f, 0x0a,
	0x0c, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x76, 0x32, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x2e,
	0x0a, 0x13, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x43,
	0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x74,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d,
	0x6f, 0x64, 0x65, 0x22, 0x29, 0x0a, 0x0d, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x10, 0x01, 0x22, 0x92,
	0x03, 0x0a, 0x14, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x0e, 0x61, 0x67, 0x67, 0x5f, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x41, 0x67, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x61, 0x67, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x73, 0x12, 0x66, 0x0a, 0x12, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x5f, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x75, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x73, 0x1a, 0x58, 0x0a, 0x11, 0x41,
	0x67, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5c, 0x0a, 0x15, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x91, 0x01, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65,
	0x73, 0x12, 0x2a, 0x0a, 0x04, 0x73, 0x70, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x04, 0x73, 0x70, 0x61, 0x6e, 0x12, 0x26, 0x0a,
	0x0f, 0x6e, 0x75, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x30, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x61, 0x64, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0xde, 0x01, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x61, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2a, 0x0a, 0x04, 0x73, 0x70, 0x61, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x04,
	0x73, 0x70, 0x61, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x75, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6e,
	0x75, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x0c,
	0x73, 0x33, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x33, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x44,
	0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x48,
	0x00, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x45, 0x0a, 0x11, 0x43, 0x61, 0x72, 0x66,
	0x61, 0x78, 0x56, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x76, 0x69, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x76, 0x69, 0x6e,
	0x73, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x72, 0x22,
	0x6b, 0x0a, 0x12, 0x43, 0x61, 0x72, 0x66, 0x61, 0x78, 0x56, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x5f, 0x76, 0x69, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0b, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x56, 0x69, 0x6e, 0x73, 0x22, 0x37, 0x0a, 0x12,
	0x4f, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x3a, 0x0a, 0x1d, 0x53, 0x61, 0x6d, 0x73, 0x61, 0x72, 0x61,
	0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x6a, 0x73,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x61, 0x77, 0x4a, 0x73, 0x6f,
	0x6e, 0x2a, 0xc2, 0x02, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64,
	0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64,
	0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x10, 0x03,
	0x12, 0x1d, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x10, 0x04, 0x12,
	0x20, 0x0a, 0x1c, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x10,
	0x05, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6c, 0x79, 0x44,
	0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x06, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x65, 0x72,
	0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x6c, 0x79, 0x4c, 0x6f, 0x73, 0x74, 0x10, 0x07, 0x12, 0x26,
	0x0a, 0x22, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x50, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x6c, 0x79, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x10, 0x08, 0x2a, 0xb0, 0x01, 0x0a, 0x05, 0x4d, 0x6f, 0x6e, 0x74, 0x68,
	0x12, 0x15, 0x0a, 0x11, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4a, 0x41, 0x4e, 0x55, 0x41,
	0x52, 0x59, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x45, 0x42, 0x52, 0x55, 0x41, 0x52, 0x59,
	0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x41, 0x52, 0x43, 0x48, 0x10, 0x03, 0x12, 0x09, 0x0a,
	0x05, 0x41, 0x50, 0x52, 0x49, 0x4c, 0x10, 0x04, 0x12, 0x07, 0x0a, 0x03, 0x4d, 0x41, 0x59, 0x10,
	0x05, 0x12, 0x08, 0x0a, 0x04, 0x4a, 0x55, 0x4e, 0x45, 0x10, 0x06, 0x12, 0x08, 0x0a, 0x04, 0x4a,
	0x55, 0x4c, 0x59, 0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x55, 0x47, 0x55, 0x53, 0x54, 0x10,
	0x08, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x45, 0x50, 0x54, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x09,
	0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x43, 0x54, 0x4f, 0x42, 0x45, 0x52, 0x10, 0x0a, 0x12, 0x0c, 0x0a,
	0x08, 0x4e, 0x4f, 0x56, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x0b, 0x12, 0x0c, 0x0a, 0x08, 0x44,
	0x45, 0x43, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x0c, 0x2a, 0x97, 0x03, 0x0a, 0x0c, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x47, 0x50,
	0x53, 0x4c, 0x6f, 0x67, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4f, 0x64,
	0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x48,
	0x61, 0x72, 0x73, 0x68, 0x42, 0x72, 0x61, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x4c, 0x6f, 0x67, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x48, 0x61, 0x72, 0x73, 0x68, 0x41, 0x63,
	0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c,
	0x6f, 0x67, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x48, 0x61, 0x72, 0x73, 0x68, 0x54, 0x75, 0x72,
	0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x53,
	0x70, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x10,
	0x06, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x6f, 0x61, 0x64, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x4c, 0x6f, 0x67, 0x10, 0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x73, 0x70, 0x54, 0x72, 0x69,
	0x70, 0x4c, 0x6f, 0x67, 0x10, 0x08, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x4c, 0x6f, 0x67, 0x10, 0x09, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x70, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c,
	0x6f, 0x67, 0x10, 0x0a, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x50, 0x53, 0x4c, 0x6f, 0x67, 0x57, 0x69,
	0x74, 0x68, 0x53, 0x70, 0x65, 0x65, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x10, 0x0b, 0x12,
	0x1c, 0x0a, 0x18, 0x47, 0x50, 0x53, 0x4c, 0x6f, 0x67, 0x57, 0x69, 0x74, 0x68, 0x54, 0x72, 0x61,
	0x66, 0x66, 0x69, 0x63, 0x44, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x79, 0x10, 0x0c, 0x12, 0x12, 0x0a,
	0x0e, 0x47, 0x50, 0x53, 0x4c, 0x6f, 0x67, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x61, 0x70, 0x73, 0x10,
	0x0d, 0x12, 0x17, 0x0a, 0x13, 0x47, 0x50, 0x53, 0x4c, 0x6f, 0x67, 0x57, 0x69, 0x74, 0x68, 0x47,
	0x65, 0x6f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x79, 0x10, 0x0e, 0x12, 0x15, 0x0a, 0x11, 0x47, 0x50,
	0x53, 0x4c, 0x6f, 0x67, 0x57, 0x69, 0x74, 0x68, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x10,
	0x0f, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x73, 0x10, 0x10, 0x2a, 0xa7, 0x01, 0x0a, 0x0c, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x4b, 0x69, 0x6e, 0x64, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x6d, 0x61, 0x6e,
	0x53, 0x63, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x10,
	0x00, 0x12, 0x17, 0x0a, 0x0f, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x70, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x10, 0x01, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x24, 0x0a, 0x1c, 0x45, 0x78,
	0x70, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x6e,
	0x6c, 0x79, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x10, 0x02, 0x1a, 0x02, 0x08, 0x01,
	0x12, 0x21, 0x0a, 0x19, 0x4c, 0x69, 0x6e, 0x65, 0x61, 0x72, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x10, 0x03, 0x1a,
	0x02, 0x08, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x62, 0x6c, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x10, 0x04, 0x2a, 0x59, 0x0a,
	0x0e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0d, 0x0a, 0x09, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07,
	0x0a, 0x03, 0x56, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x52, 0x4f, 0x56, 0x49,
	0x44, 0x45, 0x52, 0x5f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x49, 0x44, 0x10, 0x02,
	0x12, 0x16, 0x0a, 0x12, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x5f, 0x44, 0x52, 0x49,
	0x56, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x2a, 0x46, 0x0a, 0x0f, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10,
	0x02, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x10, 0x03,
	0x32, 0x86, 0x0d, 0x0a, 0x19, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x67,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x2e,
	0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x1a,
	0x26, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x2e, 0x74, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x1a, 0x19, 0x2e, 0x74, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x1a, 0x18, 0x2e, 0x74, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x57, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x2e, 0x74, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x1a, 0x1e, 0x2e, 0x74,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x44, 0x0a, 0x04,
	0x50, 0x69, 0x6e, 0x67, 0x12, 0x20, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x1a, 0x1a, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x58, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x6c,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x6c, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x24, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d,
	0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x63, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x27, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x6c, 0x6c, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x4d,
	0x61, 0x6e, 0x79, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x59, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x24, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d,
	0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x41, 0x0a,
	0x07, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x1c, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64,
	0x12, 0x3f, 0x0a, 0x09, 0x52, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x18, 0x2e,
	0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x1a, 0x18, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49,
	0x64, 0x12, 0x3c, 0x0a, 0x06, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x18, 0x2e, 0x74, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x49, 0x64, 0x1a, 0x18, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12,
	0x63, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x49, 0x46, 0x54, 0x41, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c,
	0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x12, 0x27, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x46, 0x54, 0x41, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x21, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32,
	0x2e, 0x49, 0x46, 0x54, 0x41, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65,
	0x61, 0x67, 0x65, 0x73, 0x12, 0x72, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x53,
	0x63, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61,
	0x67, 0x65, 0x12, 0x2c, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76,
	0x32, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x53, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x26, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x53, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x4d, 0x69, 0x6c, 0x65, 0x61, 0x67, 0x65, 0x73, 0x12, 0x90, 0x01, 0x0a, 0x2d, 0x47, 0x65, 0x74,
	0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x4c, 0x6f, 0x67, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x2f, 0x2e, 0x74, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x74, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x30, 0x01, 0x12, 0x5c, 0x0a, 0x17, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x43, 0x61, 0x72, 0x66,
	0x61, 0x78, 0x56, 0x69, 0x6e, 0x73, 0x12, 0x1f, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x72, 0x66, 0x61, 0x78, 0x56, 0x69, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x72, 0x66, 0x61, 0x78, 0x56, 0x69, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x4f, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x20, 0x2e, 0x74, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x1a, 0x20, 0x2e, 0x74,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x4f, 0x41, 0x75, 0x74,
	0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6d, 0x73, 0x61, 0x72, 0x61, 0x53, 0x61, 0x66, 0x65,
	0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x20, 0x2e, 0x74, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x1a, 0x2b, 0x2e, 0x74,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x76, 0x32, 0x2e, 0x53, 0x61, 0x6d, 0x73,
	0x61, 0x72, 0x61, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_telematicsv2_api_proto_rawDescOnce sync.Once
	file_telematicsv2_api_proto_rawDescData = file_telematicsv2_api_proto_rawDesc
)

func file_telematicsv2_api_proto_rawDescGZIP() []byte {
	file_telematicsv2_api_proto_rawDescOnce.Do(func() {
		file_telematicsv2_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_telematicsv2_api_proto_rawDescData)
	})
	return file_telematicsv2_api_proto_rawDescData
}

var file_telematicsv2_api_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_telematicsv2_api_proto_msgTypes = make([]protoimpl.MessageInfo, 47)
var file_telematicsv2_api_proto_goTypes = []interface{}{
	(ConnectionStatus)(0),                     // 0: telematicsv2.ConnectionStatus
	(Month)(0),                                // 1: telematicsv2.Month
	(ResourceType)(0),                         // 2: telematicsv2.ResourceType
	(PipelineKind)(0),                         // 3: telematicsv2.PipelineKind
	(IdentifierType)(0),                       // 4: telematicsv2.IdentifierType
	(ExecutionStatus)(0),                      // 5: telematicsv2.ExecutionStatus
	(PipelineDetailsRequest_Richness)(0),      // 6: telematicsv2.PipelineDetailsRequest.Richness
	(DataFileListRequest_OperationMode)(0),    // 7: telematicsv2.DataFileListRequest.OperationMode
	(*ConnectionHandleId)(nil),                // 8: telematicsv2.ConnectionHandleId
	(*TelematicsConnectionInfo)(nil),          // 9: telematicsv2.TelematicsConnectionInfo
	(*Vehicle)(nil),                           // 10: telematicsv2.Vehicle
	(*Driver)(nil),                            // 11: telematicsv2.Driver
	(*VehicleGroup)(nil),                      // 12: telematicsv2.VehicleGroup
	(*VehicleList)(nil),                       // 13: telematicsv2.VehicleList
	(*DriverList)(nil),                        // 14: telematicsv2.DriverList
	(*VehicleGroupList)(nil),                  // 15: telematicsv2.VehicleGroupList
	(*PingResponse)(nil),                      // 16: telematicsv2.PingResponse
	(*YearMonth)(nil),                         // 17: telematicsv2.YearMonth
	(*IFTAMonthlyMileage)(nil),                // 18: telematicsv2.IFTAMonthlyMileage
	(*IFTAMonthlyMileages)(nil),               // 19: telematicsv2.IFTAMonthlyMileages
	(*DataScienceDailyMileage)(nil),           // 20: telematicsv2.DataScienceDailyMileage
	(*DataScienceDailyMileages)(nil),          // 21: telematicsv2.DataScienceDailyMileages
	(*VehicleDriverAssignmentLogItem)(nil),    // 22: telematicsv2.VehicleDriverAssignmentLogItem
	(*ISOWeek)(nil),                           // 23: telematicsv2.ISOWeek
	(*PipelineSummaryRequest)(nil),            // 24: telematicsv2.PipelineSummaryRequest
	(*Interval)(nil),                          // 25: telematicsv2.Interval
	(*AllPipelineSummaryRequest)(nil),         // 26: telematicsv2.AllPipelineSummaryRequest
	(*PipelineDetailsRequest)(nil),            // 27: telematicsv2.PipelineDetailsRequest
	(*IFTAMonthlyMileageRequest)(nil),         // 28: telematicsv2.IFTAMonthlyMileageRequest
	(*DataScienceDailyMileageRequest)(nil),    // 29: telematicsv2.DataScienceDailyMileageRequest
	(*VehicleDriverAssignmentLogRequest)(nil), // 30: telematicsv2.VehicleDriverAssignmentLogRequest
	(*TriggerRequest)(nil),                    // 31: telematicsv2.TriggerRequest
	(*JobRunId)(nil),                          // 32: telematicsv2.JobRunId
	(*PipelineSummary)(nil),                   // 33: telematicsv2.PipelineSummary
	(*ManyPipelineSummary)(nil),               // 34: telematicsv2.ManyPipelineSummary
	(*PipelineId)(nil),                        // 35: telematicsv2.PipelineId
	(*PipelineDetails)(nil),                   // 36: telematicsv2.PipelineDetails
	(*Job)(nil),                               // 37: telematicsv2.Job
	(*Task)(nil),                              // 38: telematicsv2.Task
	(*MillimanScoringPipelineDetails)(nil),    // 39: telematicsv2.MillimanScoringPipelineDetails
	(*MetaflowDetails)(nil),                   // 40: telematicsv2.MetaflowDetails
	(*DataScienceArtifacts)(nil),              // 41: telematicsv2.DataScienceArtifacts
	(*EntityId)(nil),                          // 42: telematicsv2.EntityId
	(*NormalisedStat)(nil),                    // 43: telematicsv2.NormalisedStat
	(*DataFileListRequest)(nil),               // 44: telematicsv2.DataFileListRequest
	(*DataFileListResponse)(nil),              // 45: telematicsv2.DataFileListResponse
	(*DataFiles)(nil),                         // 46: telematicsv2.DataFiles
	(*FileReadTask)(nil),                      // 47: telematicsv2.FileReadTask
	(*CarfaxVinsRequest)(nil),                 // 48: telematicsv2.CarfaxVinsRequest
	(*CarfaxVinsResponse)(nil),                // 49: telematicsv2.CarfaxVinsResponse
	(*OAuthTokenResponse)(nil),                // 50: telematicsv2.OAuthTokenResponse
	(*SamsaraSafetySettingsResponse)(nil),     // 51: telematicsv2.SamsaraSafetySettingsResponse
	nil,                                       // 52: telematicsv2.DataScienceArtifacts.DsRunsEntry
	nil,                                       // 53: telematicsv2.DataFileListResponse.AggFileListsEntry
	nil,                                       // 54: telematicsv2.DataFileListResponse.UnknownFileListsEntry
	(*timestamppb.Timestamp)(nil),             // 55: google.protobuf.Timestamp
	(*anypb.Any)(nil),                         // 56: google.protobuf.Any
}
var file_telematicsv2_api_proto_depIdxs = []int32{
	0,  // 0: telematicsv2.TelematicsConnectionInfo.connection_status:type_name -> telematicsv2.ConnectionStatus
	55, // 1: telematicsv2.TelematicsConnectionInfo.activated_at:type_name -> google.protobuf.Timestamp
	55, // 2: telematicsv2.TelematicsConnectionInfo.data_pull_legal_limit:type_name -> google.protobuf.Timestamp
	55, // 3: telematicsv2.TelematicsConnectionInfo.created_at:type_name -> google.protobuf.Timestamp
	55, // 4: telematicsv2.TelematicsConnectionInfo.last_health_check_at:type_name -> google.protobuf.Timestamp
	55, // 5: telematicsv2.TelematicsConnectionInfo.last_successful_health_check_at:type_name -> google.protobuf.Timestamp
	55, // 6: telematicsv2.Vehicle.created_at:type_name -> google.protobuf.Timestamp
	55, // 7: telematicsv2.Vehicle.last_updated_at:type_name -> google.protobuf.Timestamp
	55, // 8: telematicsv2.Vehicle.tsp_created_at:type_name -> google.protobuf.Timestamp
	55, // 9: telematicsv2.Vehicle.tsp_updated_at:type_name -> google.protobuf.Timestamp
	55, // 10: telematicsv2.Driver.created_at:type_name -> google.protobuf.Timestamp
	55, // 11: telematicsv2.Driver.last_updated_at:type_name -> google.protobuf.Timestamp
	55, // 12: telematicsv2.Driver.tsp_created_at:type_name -> google.protobuf.Timestamp
	55, // 13: telematicsv2.Driver.tsp_updated_at:type_name -> google.protobuf.Timestamp
	55, // 14: telematicsv2.VehicleGroup.created_at:type_name -> google.protobuf.Timestamp
	55, // 15: telematicsv2.VehicleGroup.last_updated_at:type_name -> google.protobuf.Timestamp
	55, // 16: telematicsv2.VehicleGroup.tsp_created_at:type_name -> google.protobuf.Timestamp
	55, // 17: telematicsv2.VehicleGroup.tsp_updated_at:type_name -> google.protobuf.Timestamp
	10, // 18: telematicsv2.VehicleList.vehicles:type_name -> telematicsv2.Vehicle
	11, // 19: telematicsv2.DriverList.drivers:type_name -> telematicsv2.Driver
	12, // 20: telematicsv2.VehicleGroupList.groups:type_name -> telematicsv2.VehicleGroup
	1,  // 21: telematicsv2.YearMonth.month:type_name -> telematicsv2.Month
	17, // 22: telematicsv2.IFTAMonthlyMileage.monthOfYear:type_name -> telematicsv2.YearMonth
	55, // 23: telematicsv2.IFTAMonthlyMileage.fetchedAt:type_name -> google.protobuf.Timestamp
	18, // 24: telematicsv2.IFTAMonthlyMileages.mileages:type_name -> telematicsv2.IFTAMonthlyMileage
	55, // 25: telematicsv2.DataScienceDailyMileage.day:type_name -> google.protobuf.Timestamp
	55, // 26: telematicsv2.DataScienceDailyMileage.fetchedAt:type_name -> google.protobuf.Timestamp
	20, // 27: telematicsv2.DataScienceDailyMileages.mileages:type_name -> telematicsv2.DataScienceDailyMileage
	55, // 28: telematicsv2.VehicleDriverAssignmentLogItem.assigned_at_time:type_name -> google.protobuf.Timestamp
	55, // 29: telematicsv2.VehicleDriverAssignmentLogItem.start_time:type_name -> google.protobuf.Timestamp
	55, // 30: telematicsv2.VehicleDriverAssignmentLogItem.end_time:type_name -> google.protobuf.Timestamp
	3,  // 31: telematicsv2.PipelineSummaryRequest.kind:type_name -> telematicsv2.PipelineKind
	55, // 32: telematicsv2.Interval.start:type_name -> google.protobuf.Timestamp
	55, // 33: telematicsv2.Interval.end:type_name -> google.protobuf.Timestamp
	3,  // 34: telematicsv2.AllPipelineSummaryRequest.kind:type_name -> telematicsv2.PipelineKind
	25, // 35: telematicsv2.AllPipelineSummaryRequest.overlap_interval:type_name -> telematicsv2.Interval
	6,  // 36: telematicsv2.PipelineDetailsRequest.richness:type_name -> telematicsv2.PipelineDetailsRequest.Richness
	55, // 37: telematicsv2.IFTAMonthlyMileageRequest.monthOfYear:type_name -> google.protobuf.Timestamp
	55, // 38: telematicsv2.DataScienceDailyMileageRequest.from:type_name -> google.protobuf.Timestamp
	55, // 39: telematicsv2.DataScienceDailyMileageRequest.to:type_name -> google.protobuf.Timestamp
	25, // 40: telematicsv2.VehicleDriverAssignmentLogRequest.request_interval:type_name -> telematicsv2.Interval
	4,  // 41: telematicsv2.VehicleDriverAssignmentLogRequest.identifier_type:type_name -> telematicsv2.IdentifierType
	55, // 42: telematicsv2.VehicleDriverAssignmentLogRequest.ignore_provider_cache:type_name -> google.protobuf.Timestamp
	3,  // 43: telematicsv2.TriggerRequest.kind:type_name -> telematicsv2.PipelineKind
	3,  // 44: telematicsv2.PipelineSummary.kind:type_name -> telematicsv2.PipelineKind
	5,  // 45: telematicsv2.PipelineSummary.status:type_name -> telematicsv2.ExecutionStatus
	55, // 46: telematicsv2.PipelineSummary.started_at:type_name -> google.protobuf.Timestamp
	55, // 47: telematicsv2.PipelineSummary.finished_at:type_name -> google.protobuf.Timestamp
	32, // 48: telematicsv2.PipelineSummary.jobrun_id:type_name -> telematicsv2.JobRunId
	33, // 49: telematicsv2.ManyPipelineSummary.summaries:type_name -> telematicsv2.PipelineSummary
	37, // 50: telematicsv2.PipelineDetails.job:type_name -> telematicsv2.Job
	32, // 51: telematicsv2.Job.jobrun_id:type_name -> telematicsv2.JobRunId
	5,  // 52: telematicsv2.Job.status:type_name -> telematicsv2.ExecutionStatus
	38, // 53: telematicsv2.Job.tasks:type_name -> telematicsv2.Task
	56, // 54: telematicsv2.Job.details:type_name -> google.protobuf.Any
	55, // 55: telematicsv2.Job.startedAt:type_name -> google.protobuf.Timestamp
	55, // 56: telematicsv2.Job.finishedAt:type_name -> google.protobuf.Timestamp
	5,  // 57: telematicsv2.Task.status:type_name -> telematicsv2.ExecutionStatus
	37, // 58: telematicsv2.Task.sub_jobs:type_name -> telematicsv2.Job
	41, // 59: telematicsv2.MillimanScoringPipelineDetails.ds_artifacts:type_name -> telematicsv2.DataScienceArtifacts
	52, // 60: telematicsv2.DataScienceArtifacts.ds_runs:type_name -> telematicsv2.DataScienceArtifacts.DsRunsEntry
	4,  // 61: telematicsv2.EntityId.type:type_name -> telematicsv2.IdentifierType
	2,  // 62: telematicsv2.NormalisedStat.kind:type_name -> telematicsv2.ResourceType
	25, // 63: telematicsv2.DataFileListRequest.data_interval:type_name -> telematicsv2.Interval
	43, // 64: telematicsv2.DataFileListRequest.data_type:type_name -> telematicsv2.NormalisedStat
	42, // 65: telematicsv2.DataFileListRequest.entities:type_name -> telematicsv2.EntityId
	4,  // 66: telematicsv2.DataFileListRequest.aggregate_by:type_name -> telematicsv2.IdentifierType
	7,  // 67: telematicsv2.DataFileListRequest.mode:type_name -> telematicsv2.DataFileListRequest.OperationMode
	53, // 68: telematicsv2.DataFileListResponse.agg_file_lists:type_name -> telematicsv2.DataFileListResponse.AggFileListsEntry
	54, // 69: telematicsv2.DataFileListResponse.unknown_file_lists:type_name -> telematicsv2.DataFileListResponse.UnknownFileListsEntry
	25, // 70: telematicsv2.DataFiles.span:type_name -> telematicsv2.Interval
	47, // 71: telematicsv2.DataFiles.files:type_name -> telematicsv2.FileReadTask
	25, // 72: telematicsv2.FileReadTask.span:type_name -> telematicsv2.Interval
	25, // 73: telematicsv2.FileReadTask.interval_filter:type_name -> telematicsv2.Interval
	40, // 74: telematicsv2.DataScienceArtifacts.DsRunsEntry.value:type_name -> telematicsv2.MetaflowDetails
	46, // 75: telematicsv2.DataFileListResponse.AggFileListsEntry.value:type_name -> telematicsv2.DataFiles
	46, // 76: telematicsv2.DataFileListResponse.UnknownFileListsEntry.value:type_name -> telematicsv2.DataFiles
	8,  // 77: telematicsv2.TelematicsPipelineManager.GetTelematicsConnectionInfo:input_type -> telematicsv2.ConnectionHandleId
	8,  // 78: telematicsv2.TelematicsPipelineManager.GetVehicleList:input_type -> telematicsv2.ConnectionHandleId
	8,  // 79: telematicsv2.TelematicsPipelineManager.GetDriverList:input_type -> telematicsv2.ConnectionHandleId
	8,  // 80: telematicsv2.TelematicsPipelineManager.GetVehicleGroupList:input_type -> telematicsv2.ConnectionHandleId
	8,  // 81: telematicsv2.TelematicsPipelineManager.Ping:input_type -> telematicsv2.ConnectionHandleId
	44, // 82: telematicsv2.TelematicsPipelineManager.GetDataFileList:input_type -> telematicsv2.DataFileListRequest
	24, // 83: telematicsv2.TelematicsPipelineManager.GetLatestPipelineSummary:input_type -> telematicsv2.PipelineSummaryRequest
	26, // 84: telematicsv2.TelematicsPipelineManager.GetAllPipelineSummary:input_type -> telematicsv2.AllPipelineSummaryRequest
	27, // 85: telematicsv2.TelematicsPipelineManager.GetPipelineDetails:input_type -> telematicsv2.PipelineDetailsRequest
	31, // 86: telematicsv2.TelematicsPipelineManager.Trigger:input_type -> telematicsv2.TriggerRequest
	35, // 87: telematicsv2.TelematicsPipelineManager.ReTrigger:input_type -> telematicsv2.PipelineId
	35, // 88: telematicsv2.TelematicsPipelineManager.Cancel:input_type -> telematicsv2.PipelineId
	28, // 89: telematicsv2.TelematicsPipelineManager.GetIFTAMonthlyMileage:input_type -> telematicsv2.IFTAMonthlyMileageRequest
	29, // 90: telematicsv2.TelematicsPipelineManager.GetDataScienceDailyMileage:input_type -> telematicsv2.DataScienceDailyMileageRequest
	30, // 91: telematicsv2.TelematicsPipelineManager.GetNormalizedVehicleDriverAssignmentLogStream:input_type -> telematicsv2.VehicleDriverAssignmentLogRequest
	48, // 92: telematicsv2.TelematicsPipelineManager.CreateOrRenewCarfaxVins:input_type -> telematicsv2.CarfaxVinsRequest
	8,  // 93: telematicsv2.TelematicsPipelineManager.GetOAuthToken:input_type -> telematicsv2.ConnectionHandleId
	8,  // 94: telematicsv2.TelematicsPipelineManager.GetSamsaraSafetySettings:input_type -> telematicsv2.ConnectionHandleId
	9,  // 95: telematicsv2.TelematicsPipelineManager.GetTelematicsConnectionInfo:output_type -> telematicsv2.TelematicsConnectionInfo
	13, // 96: telematicsv2.TelematicsPipelineManager.GetVehicleList:output_type -> telematicsv2.VehicleList
	14, // 97: telematicsv2.TelematicsPipelineManager.GetDriverList:output_type -> telematicsv2.DriverList
	15, // 98: telematicsv2.TelematicsPipelineManager.GetVehicleGroupList:output_type -> telematicsv2.VehicleGroupList
	16, // 99: telematicsv2.TelematicsPipelineManager.Ping:output_type -> telematicsv2.PingResponse
	45, // 100: telematicsv2.TelematicsPipelineManager.GetDataFileList:output_type -> telematicsv2.DataFileListResponse
	33, // 101: telematicsv2.TelematicsPipelineManager.GetLatestPipelineSummary:output_type -> telematicsv2.PipelineSummary
	34, // 102: telematicsv2.TelematicsPipelineManager.GetAllPipelineSummary:output_type -> telematicsv2.ManyPipelineSummary
	36, // 103: telematicsv2.TelematicsPipelineManager.GetPipelineDetails:output_type -> telematicsv2.PipelineDetails
	35, // 104: telematicsv2.TelematicsPipelineManager.Trigger:output_type -> telematicsv2.PipelineId
	35, // 105: telematicsv2.TelematicsPipelineManager.ReTrigger:output_type -> telematicsv2.PipelineId
	35, // 106: telematicsv2.TelematicsPipelineManager.Cancel:output_type -> telematicsv2.PipelineId
	19, // 107: telematicsv2.TelematicsPipelineManager.GetIFTAMonthlyMileage:output_type -> telematicsv2.IFTAMonthlyMileages
	21, // 108: telematicsv2.TelematicsPipelineManager.GetDataScienceDailyMileage:output_type -> telematicsv2.DataScienceDailyMileages
	22, // 109: telematicsv2.TelematicsPipelineManager.GetNormalizedVehicleDriverAssignmentLogStream:output_type -> telematicsv2.VehicleDriverAssignmentLogItem
	49, // 110: telematicsv2.TelematicsPipelineManager.CreateOrRenewCarfaxVins:output_type -> telematicsv2.CarfaxVinsResponse
	50, // 111: telematicsv2.TelematicsPipelineManager.GetOAuthToken:output_type -> telematicsv2.OAuthTokenResponse
	51, // 112: telematicsv2.TelematicsPipelineManager.GetSamsaraSafetySettings:output_type -> telematicsv2.SamsaraSafetySettingsResponse
	95, // [95:113] is the sub-list for method output_type
	77, // [77:95] is the sub-list for method input_type
	77, // [77:77] is the sub-list for extension type_name
	77, // [77:77] is the sub-list for extension extendee
	0,  // [0:77] is the sub-list for field type_name
}

func init() { file_telematicsv2_api_proto_init() }
func file_telematicsv2_api_proto_init() {
	if File_telematicsv2_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_telematicsv2_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectionHandleId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsConnectionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Vehicle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Driver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DriverList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleGroupList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YearMonth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IFTAMonthlyMileage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IFTAMonthlyMileages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataScienceDailyMileage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataScienceDailyMileages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleDriverAssignmentLogItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ISOWeek); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Interval); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllPipelineSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IFTAMonthlyMileageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataScienceDailyMileageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleDriverAssignmentLogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobRunId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManyPipelineSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Job); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MillimanScoringPipelineDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetaflowDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataScienceArtifacts); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntityId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NormalisedStat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataFileListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataFileListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataFiles); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileReadTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarfaxVinsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarfaxVinsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OAuthTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_telematicsv2_api_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SamsaraSafetySettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_telematicsv2_api_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_telematicsv2_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_telematicsv2_api_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_telematicsv2_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_telematicsv2_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_telematicsv2_api_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_telematicsv2_api_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_telematicsv2_api_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_telematicsv2_api_proto_msgTypes[22].OneofWrappers = []interface{}{}
	file_telematicsv2_api_proto_msgTypes[25].OneofWrappers = []interface{}{}
	file_telematicsv2_api_proto_msgTypes[29].OneofWrappers = []interface{}{}
	file_telematicsv2_api_proto_msgTypes[39].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_telematicsv2_api_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   47,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_telematicsv2_api_proto_goTypes,
		DependencyIndexes: file_telematicsv2_api_proto_depIdxs,
		EnumInfos:         file_telematicsv2_api_proto_enumTypes,
		MessageInfos:      file_telematicsv2_api_proto_msgTypes,
	}.Build()
	File_telematicsv2_api_proto = out.File
	file_telematicsv2_api_proto_rawDesc = nil
	file_telematicsv2_api_proto_goTypes = nil
	file_telematicsv2_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// TelematicsPipelineManagerClient is the client API for TelematicsPipelineManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TelematicsPipelineManagerClient interface {
	GetTelematicsConnectionInfo(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*TelematicsConnectionInfo, error)
	GetVehicleList(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*VehicleList, error)
	GetDriverList(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*DriverList, error)
	GetVehicleGroupList(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*VehicleGroupList, error)
	Ping(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*PingResponse, error)
	GetDataFileList(ctx context.Context, in *DataFileListRequest, opts ...grpc.CallOption) (*DataFileListResponse, error)
	GetLatestPipelineSummary(ctx context.Context, in *PipelineSummaryRequest, opts ...grpc.CallOption) (*PipelineSummary, error)
	GetAllPipelineSummary(ctx context.Context, in *AllPipelineSummaryRequest, opts ...grpc.CallOption) (*ManyPipelineSummary, error)
	GetPipelineDetails(ctx context.Context, in *PipelineDetailsRequest, opts ...grpc.CallOption) (*PipelineDetails, error)
	Trigger(ctx context.Context, in *TriggerRequest, opts ...grpc.CallOption) (*PipelineId, error)
	ReTrigger(ctx context.Context, in *PipelineId, opts ...grpc.CallOption) (*PipelineId, error)
	Cancel(ctx context.Context, in *PipelineId, opts ...grpc.CallOption) (*PipelineId, error)
	GetIFTAMonthlyMileage(ctx context.Context, in *IFTAMonthlyMileageRequest, opts ...grpc.CallOption) (*IFTAMonthlyMileages, error)
	GetDataScienceDailyMileage(ctx context.Context, in *DataScienceDailyMileageRequest, opts ...grpc.CallOption) (*DataScienceDailyMileages, error)
	GetNormalizedVehicleDriverAssignmentLogStream(ctx context.Context, in *VehicleDriverAssignmentLogRequest, opts ...grpc.CallOption) (TelematicsPipelineManager_GetNormalizedVehicleDriverAssignmentLogStreamClient, error)
	CreateOrRenewCarfaxVins(ctx context.Context, in *CarfaxVinsRequest, opts ...grpc.CallOption) (*CarfaxVinsResponse, error)
	GetOAuthToken(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*OAuthTokenResponse, error)
	GetSamsaraSafetySettings(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*SamsaraSafetySettingsResponse, error)
}

type telematicsPipelineManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewTelematicsPipelineManagerClient(cc grpc.ClientConnInterface) TelematicsPipelineManagerClient {
	return &telematicsPipelineManagerClient{cc}
}

func (c *telematicsPipelineManagerClient) GetTelematicsConnectionInfo(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*TelematicsConnectionInfo, error) {
	out := new(TelematicsConnectionInfo)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetTelematicsConnectionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetVehicleList(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*VehicleList, error) {
	out := new(VehicleList)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetVehicleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetDriverList(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*DriverList, error) {
	out := new(DriverList)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetDriverList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetVehicleGroupList(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*VehicleGroupList, error) {
	out := new(VehicleGroupList)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetVehicleGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) Ping(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*PingResponse, error) {
	out := new(PingResponse)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/Ping", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetDataFileList(ctx context.Context, in *DataFileListRequest, opts ...grpc.CallOption) (*DataFileListResponse, error) {
	out := new(DataFileListResponse)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetDataFileList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetLatestPipelineSummary(ctx context.Context, in *PipelineSummaryRequest, opts ...grpc.CallOption) (*PipelineSummary, error) {
	out := new(PipelineSummary)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetLatestPipelineSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetAllPipelineSummary(ctx context.Context, in *AllPipelineSummaryRequest, opts ...grpc.CallOption) (*ManyPipelineSummary, error) {
	out := new(ManyPipelineSummary)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetAllPipelineSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetPipelineDetails(ctx context.Context, in *PipelineDetailsRequest, opts ...grpc.CallOption) (*PipelineDetails, error) {
	out := new(PipelineDetails)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetPipelineDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) Trigger(ctx context.Context, in *TriggerRequest, opts ...grpc.CallOption) (*PipelineId, error) {
	out := new(PipelineId)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/Trigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) ReTrigger(ctx context.Context, in *PipelineId, opts ...grpc.CallOption) (*PipelineId, error) {
	out := new(PipelineId)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/ReTrigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) Cancel(ctx context.Context, in *PipelineId, opts ...grpc.CallOption) (*PipelineId, error) {
	out := new(PipelineId)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/Cancel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetIFTAMonthlyMileage(ctx context.Context, in *IFTAMonthlyMileageRequest, opts ...grpc.CallOption) (*IFTAMonthlyMileages, error) {
	out := new(IFTAMonthlyMileages)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetIFTAMonthlyMileage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetDataScienceDailyMileage(ctx context.Context, in *DataScienceDailyMileageRequest, opts ...grpc.CallOption) (*DataScienceDailyMileages, error) {
	out := new(DataScienceDailyMileages)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetDataScienceDailyMileage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetNormalizedVehicleDriverAssignmentLogStream(ctx context.Context, in *VehicleDriverAssignmentLogRequest, opts ...grpc.CallOption) (TelematicsPipelineManager_GetNormalizedVehicleDriverAssignmentLogStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &_TelematicsPipelineManager_serviceDesc.Streams[0], "/telematicsv2.TelematicsPipelineManager/GetNormalizedVehicleDriverAssignmentLogStream", opts...)
	if err != nil {
		return nil, err
	}
	x := &telematicsPipelineManagerGetNormalizedVehicleDriverAssignmentLogStreamClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type TelematicsPipelineManager_GetNormalizedVehicleDriverAssignmentLogStreamClient interface {
	Recv() (*VehicleDriverAssignmentLogItem, error)
	grpc.ClientStream
}

type telematicsPipelineManagerGetNormalizedVehicleDriverAssignmentLogStreamClient struct {
	grpc.ClientStream
}

func (x *telematicsPipelineManagerGetNormalizedVehicleDriverAssignmentLogStreamClient) Recv() (*VehicleDriverAssignmentLogItem, error) {
	m := new(VehicleDriverAssignmentLogItem)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *telematicsPipelineManagerClient) CreateOrRenewCarfaxVins(ctx context.Context, in *CarfaxVinsRequest, opts ...grpc.CallOption) (*CarfaxVinsResponse, error) {
	out := new(CarfaxVinsResponse)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/CreateOrRenewCarfaxVins", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetOAuthToken(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*OAuthTokenResponse, error) {
	out := new(OAuthTokenResponse)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetOAuthToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telematicsPipelineManagerClient) GetSamsaraSafetySettings(ctx context.Context, in *ConnectionHandleId, opts ...grpc.CallOption) (*SamsaraSafetySettingsResponse, error) {
	out := new(SamsaraSafetySettingsResponse)
	err := c.cc.Invoke(ctx, "/telematicsv2.TelematicsPipelineManager/GetSamsaraSafetySettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TelematicsPipelineManagerServer is the server API for TelematicsPipelineManager service.
type TelematicsPipelineManagerServer interface {
	GetTelematicsConnectionInfo(context.Context, *ConnectionHandleId) (*TelematicsConnectionInfo, error)
	GetVehicleList(context.Context, *ConnectionHandleId) (*VehicleList, error)
	GetDriverList(context.Context, *ConnectionHandleId) (*DriverList, error)
	GetVehicleGroupList(context.Context, *ConnectionHandleId) (*VehicleGroupList, error)
	Ping(context.Context, *ConnectionHandleId) (*PingResponse, error)
	GetDataFileList(context.Context, *DataFileListRequest) (*DataFileListResponse, error)
	GetLatestPipelineSummary(context.Context, *PipelineSummaryRequest) (*PipelineSummary, error)
	GetAllPipelineSummary(context.Context, *AllPipelineSummaryRequest) (*ManyPipelineSummary, error)
	GetPipelineDetails(context.Context, *PipelineDetailsRequest) (*PipelineDetails, error)
	Trigger(context.Context, *TriggerRequest) (*PipelineId, error)
	ReTrigger(context.Context, *PipelineId) (*PipelineId, error)
	Cancel(context.Context, *PipelineId) (*PipelineId, error)
	GetIFTAMonthlyMileage(context.Context, *IFTAMonthlyMileageRequest) (*IFTAMonthlyMileages, error)
	GetDataScienceDailyMileage(context.Context, *DataScienceDailyMileageRequest) (*DataScienceDailyMileages, error)
	GetNormalizedVehicleDriverAssignmentLogStream(*VehicleDriverAssignmentLogRequest, TelematicsPipelineManager_GetNormalizedVehicleDriverAssignmentLogStreamServer) error
	CreateOrRenewCarfaxVins(context.Context, *CarfaxVinsRequest) (*CarfaxVinsResponse, error)
	GetOAuthToken(context.Context, *ConnectionHandleId) (*OAuthTokenResponse, error)
	GetSamsaraSafetySettings(context.Context, *ConnectionHandleId) (*SamsaraSafetySettingsResponse, error)
}

// UnimplementedTelematicsPipelineManagerServer can be embedded to have forward compatible implementations.
type UnimplementedTelematicsPipelineManagerServer struct {
}

func (*UnimplementedTelematicsPipelineManagerServer) GetTelematicsConnectionInfo(context.Context, *ConnectionHandleId) (*TelematicsConnectionInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTelematicsConnectionInfo not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetVehicleList(context.Context, *ConnectionHandleId) (*VehicleList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVehicleList not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetDriverList(context.Context, *ConnectionHandleId) (*DriverList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDriverList not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetVehicleGroupList(context.Context, *ConnectionHandleId) (*VehicleGroupList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVehicleGroupList not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) Ping(context.Context, *ConnectionHandleId) (*PingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetDataFileList(context.Context, *DataFileListRequest) (*DataFileListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDataFileList not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetLatestPipelineSummary(context.Context, *PipelineSummaryRequest) (*PipelineSummary, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestPipelineSummary not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetAllPipelineSummary(context.Context, *AllPipelineSummaryRequest) (*ManyPipelineSummary, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllPipelineSummary not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetPipelineDetails(context.Context, *PipelineDetailsRequest) (*PipelineDetails, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPipelineDetails not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) Trigger(context.Context, *TriggerRequest) (*PipelineId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Trigger not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) ReTrigger(context.Context, *PipelineId) (*PipelineId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReTrigger not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) Cancel(context.Context, *PipelineId) (*PipelineId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cancel not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetIFTAMonthlyMileage(context.Context, *IFTAMonthlyMileageRequest) (*IFTAMonthlyMileages, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIFTAMonthlyMileage not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetDataScienceDailyMileage(context.Context, *DataScienceDailyMileageRequest) (*DataScienceDailyMileages, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDataScienceDailyMileage not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetNormalizedVehicleDriverAssignmentLogStream(*VehicleDriverAssignmentLogRequest, TelematicsPipelineManager_GetNormalizedVehicleDriverAssignmentLogStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method GetNormalizedVehicleDriverAssignmentLogStream not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) CreateOrRenewCarfaxVins(context.Context, *CarfaxVinsRequest) (*CarfaxVinsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrRenewCarfaxVins not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetOAuthToken(context.Context, *ConnectionHandleId) (*OAuthTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOAuthToken not implemented")
}
func (*UnimplementedTelematicsPipelineManagerServer) GetSamsaraSafetySettings(context.Context, *ConnectionHandleId) (*SamsaraSafetySettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSamsaraSafetySettings not implemented")
}

func RegisterTelematicsPipelineManagerServer(s *grpc.Server, srv TelematicsPipelineManagerServer) {
	s.RegisterService(&_TelematicsPipelineManager_serviceDesc, srv)
}

func _TelematicsPipelineManager_GetTelematicsConnectionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectionHandleId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetTelematicsConnectionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetTelematicsConnectionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetTelematicsConnectionInfo(ctx, req.(*ConnectionHandleId))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetVehicleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectionHandleId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetVehicleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetVehicleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetVehicleList(ctx, req.(*ConnectionHandleId))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetDriverList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectionHandleId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetDriverList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetDriverList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetDriverList(ctx, req.(*ConnectionHandleId))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetVehicleGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectionHandleId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetVehicleGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetVehicleGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetVehicleGroupList(ctx, req.(*ConnectionHandleId))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectionHandleId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/Ping",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).Ping(ctx, req.(*ConnectionHandleId))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetDataFileList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataFileListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetDataFileList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetDataFileList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetDataFileList(ctx, req.(*DataFileListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetLatestPipelineSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PipelineSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetLatestPipelineSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetLatestPipelineSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetLatestPipelineSummary(ctx, req.(*PipelineSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetAllPipelineSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllPipelineSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetAllPipelineSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetAllPipelineSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetAllPipelineSummary(ctx, req.(*AllPipelineSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetPipelineDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PipelineDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetPipelineDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetPipelineDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetPipelineDetails(ctx, req.(*PipelineDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_Trigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).Trigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/Trigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).Trigger(ctx, req.(*TriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_ReTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PipelineId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).ReTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/ReTrigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).ReTrigger(ctx, req.(*PipelineId))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_Cancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PipelineId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).Cancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/Cancel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).Cancel(ctx, req.(*PipelineId))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetIFTAMonthlyMileage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IFTAMonthlyMileageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetIFTAMonthlyMileage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetIFTAMonthlyMileage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetIFTAMonthlyMileage(ctx, req.(*IFTAMonthlyMileageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetDataScienceDailyMileage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataScienceDailyMileageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetDataScienceDailyMileage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetDataScienceDailyMileage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetDataScienceDailyMileage(ctx, req.(*DataScienceDailyMileageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetNormalizedVehicleDriverAssignmentLogStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(VehicleDriverAssignmentLogRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TelematicsPipelineManagerServer).GetNormalizedVehicleDriverAssignmentLogStream(m, &telematicsPipelineManagerGetNormalizedVehicleDriverAssignmentLogStreamServer{stream})
}

type TelematicsPipelineManager_GetNormalizedVehicleDriverAssignmentLogStreamServer interface {
	Send(*VehicleDriverAssignmentLogItem) error
	grpc.ServerStream
}

type telematicsPipelineManagerGetNormalizedVehicleDriverAssignmentLogStreamServer struct {
	grpc.ServerStream
}

func (x *telematicsPipelineManagerGetNormalizedVehicleDriverAssignmentLogStreamServer) Send(m *VehicleDriverAssignmentLogItem) error {
	return x.ServerStream.SendMsg(m)
}

func _TelematicsPipelineManager_CreateOrRenewCarfaxVins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CarfaxVinsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).CreateOrRenewCarfaxVins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/CreateOrRenewCarfaxVins",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).CreateOrRenewCarfaxVins(ctx, req.(*CarfaxVinsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetOAuthToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectionHandleId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetOAuthToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetOAuthToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetOAuthToken(ctx, req.(*ConnectionHandleId))
	}
	return interceptor(ctx, in, info, handler)
}

func _TelematicsPipelineManager_GetSamsaraSafetySettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectionHandleId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TelematicsPipelineManagerServer).GetSamsaraSafetySettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/telematicsv2.TelematicsPipelineManager/GetSamsaraSafetySettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TelematicsPipelineManagerServer).GetSamsaraSafetySettings(ctx, req.(*ConnectionHandleId))
	}
	return interceptor(ctx, in, info, handler)
}

var _TelematicsPipelineManager_serviceDesc = grpc.ServiceDesc{
	ServiceName: "telematicsv2.TelematicsPipelineManager",
	HandlerType: (*TelematicsPipelineManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTelematicsConnectionInfo",
			Handler:    _TelematicsPipelineManager_GetTelematicsConnectionInfo_Handler,
		},
		{
			MethodName: "GetVehicleList",
			Handler:    _TelematicsPipelineManager_GetVehicleList_Handler,
		},
		{
			MethodName: "GetDriverList",
			Handler:    _TelematicsPipelineManager_GetDriverList_Handler,
		},
		{
			MethodName: "GetVehicleGroupList",
			Handler:    _TelematicsPipelineManager_GetVehicleGroupList_Handler,
		},
		{
			MethodName: "Ping",
			Handler:    _TelematicsPipelineManager_Ping_Handler,
		},
		{
			MethodName: "GetDataFileList",
			Handler:    _TelematicsPipelineManager_GetDataFileList_Handler,
		},
		{
			MethodName: "GetLatestPipelineSummary",
			Handler:    _TelematicsPipelineManager_GetLatestPipelineSummary_Handler,
		},
		{
			MethodName: "GetAllPipelineSummary",
			Handler:    _TelematicsPipelineManager_GetAllPipelineSummary_Handler,
		},
		{
			MethodName: "GetPipelineDetails",
			Handler:    _TelematicsPipelineManager_GetPipelineDetails_Handler,
		},
		{
			MethodName: "Trigger",
			Handler:    _TelematicsPipelineManager_Trigger_Handler,
		},
		{
			MethodName: "ReTrigger",
			Handler:    _TelematicsPipelineManager_ReTrigger_Handler,
		},
		{
			MethodName: "Cancel",
			Handler:    _TelematicsPipelineManager_Cancel_Handler,
		},
		{
			MethodName: "GetIFTAMonthlyMileage",
			Handler:    _TelematicsPipelineManager_GetIFTAMonthlyMileage_Handler,
		},
		{
			MethodName: "GetDataScienceDailyMileage",
			Handler:    _TelematicsPipelineManager_GetDataScienceDailyMileage_Handler,
		},
		{
			MethodName: "CreateOrRenewCarfaxVins",
			Handler:    _TelematicsPipelineManager_CreateOrRenewCarfaxVins_Handler,
		},
		{
			MethodName: "GetOAuthToken",
			Handler:    _TelematicsPipelineManager_GetOAuthToken_Handler,
		},
		{
			MethodName: "GetSamsaraSafetySettings",
			Handler:    _TelematicsPipelineManager_GetSamsaraSafetySettings_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetNormalizedVehicleDriverAssignmentLogStream",
			Handler:       _TelematicsPipelineManager_GetNormalizedVehicleDriverAssignmentLogStream_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "telematicsv2/api.proto",
}
