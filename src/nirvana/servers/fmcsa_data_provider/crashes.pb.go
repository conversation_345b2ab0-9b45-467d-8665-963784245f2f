// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: fmcsa_data_provider/crashes.proto

package fmcsa_data_provider

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Crash struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key            *CrashKey              `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	ReportDate     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=report_date,json=reportDate,proto3" json:"report_date,omitempty"`
	UsState        string                 `protobuf:"bytes,3,opt,name=us_state,json=usState,proto3" json:"us_state,omitempty"`
	Location       *string                `protobuf:"bytes,4,opt,name=location,proto3,oneof" json:"location,omitempty"`
	NumInjuries    int32                  `protobuf:"varint,5,opt,name=num_injuries,json=numInjuries,proto3" json:"num_injuries,omitempty"`
	NumFatalities  int32                  `protobuf:"varint,6,opt,name=num_fatalities,json=numFatalities,proto3" json:"num_fatalities,omitempty"`
	WasTowedAway   bool                   `protobuf:"varint,7,opt,name=was_towed_away,json=wasTowedAway,proto3" json:"was_towed_away,omitempty"`
	NotPreventable bool                   `protobuf:"varint,8,opt,name=not_preventable,json=notPreventable,proto3" json:"not_preventable,omitempty"`
	HazmatReleased *bool                  `protobuf:"varint,9,opt,name=hazmat_released,json=hazmatReleased,proto3,oneof" json:"hazmat_released,omitempty"`
}

func (x *Crash) Reset() {
	*x = Crash{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_crashes_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Crash) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Crash) ProtoMessage() {}

func (x *Crash) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_crashes_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Crash.ProtoReflect.Descriptor instead.
func (*Crash) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_crashes_proto_rawDescGZIP(), []int{0}
}

func (x *Crash) GetKey() *CrashKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *Crash) GetReportDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ReportDate
	}
	return nil
}

func (x *Crash) GetUsState() string {
	if x != nil {
		return x.UsState
	}
	return ""
}

func (x *Crash) GetLocation() string {
	if x != nil && x.Location != nil {
		return *x.Location
	}
	return ""
}

func (x *Crash) GetNumInjuries() int32 {
	if x != nil {
		return x.NumInjuries
	}
	return 0
}

func (x *Crash) GetNumFatalities() int32 {
	if x != nil {
		return x.NumFatalities
	}
	return 0
}

func (x *Crash) GetWasTowedAway() bool {
	if x != nil {
		return x.WasTowedAway
	}
	return false
}

func (x *Crash) GetNotPreventable() bool {
	if x != nil {
		return x.NotPreventable
	}
	return false
}

func (x *Crash) GetHazmatReleased() bool {
	if x != nil && x.HazmatReleased != nil {
		return *x.HazmatReleased
	}
	return false
}

type CrashKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber            int64  `protobuf:"varint,1,opt,name=dot_number,json=dotNumber,proto3" json:"dot_number,omitempty"`
	ReportNumber         string `protobuf:"bytes,2,opt,name=report_number,json=reportNumber,proto3" json:"report_number,omitempty"`
	ReportSequenceNumber int32  `protobuf:"varint,3,opt,name=report_sequence_number,json=reportSequenceNumber,proto3" json:"report_sequence_number,omitempty"`
}

func (x *CrashKey) Reset() {
	*x = CrashKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_crashes_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrashKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrashKey) ProtoMessage() {}

func (x *CrashKey) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_crashes_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrashKey.ProtoReflect.Descriptor instead.
func (*CrashKey) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_crashes_proto_rawDescGZIP(), []int{1}
}

func (x *CrashKey) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *CrashKey) GetReportNumber() string {
	if x != nil {
		return x.ReportNumber
	}
	return ""
}

func (x *CrashKey) GetReportSequenceNumber() int32 {
	if x != nil {
		return x.ReportSequenceNumber
	}
	return 0
}

var File_fmcsa_data_provider_crashes_proto protoreflect.FileDescriptor

var file_fmcsa_data_provider_crashes_proto_rawDesc = []byte{
	0x0a, 0x21, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x63, 0x72, 0x61, 0x73, 0x68, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x13, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x03, 0x0a, 0x05, 0x43, 0x72,
	0x61, 0x73, 0x68, 0x12, 0x2f, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x61, 0x73, 0x68, 0x4b, 0x65, 0x79, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a,
	0x0c, 0x6e, 0x75, 0x6d, 0x5f, 0x69, 0x6e, 0x6a, 0x75, 0x72, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x49, 0x6e, 0x6a, 0x75, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x6e, 0x75, 0x6d, 0x5f, 0x66, 0x61, 0x74, 0x61, 0x6c, 0x69, 0x74, 0x69,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x46, 0x61, 0x74,
	0x61, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x77, 0x61, 0x73, 0x5f, 0x74,
	0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x77, 0x61, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x77, 0x61, 0x73, 0x54, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x77, 0x61, 0x79, 0x12, 0x27, 0x0a,
	0x0f, 0x6e, 0x6f, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x50, 0x72, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x0f, 0x68, 0x61, 0x7a, 0x6d, 0x61, 0x74,
	0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x01, 0x52, 0x0e, 0x68, 0x61, 0x7a, 0x6d, 0x61, 0x74, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x68, 0x61, 0x7a, 0x6d, 0x61, 0x74, 0x5f, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x64, 0x22, 0x84, 0x01, 0x0a, 0x08, 0x43, 0x72, 0x61, 0x73, 0x68, 0x4b,
	0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_fmcsa_data_provider_crashes_proto_rawDescOnce sync.Once
	file_fmcsa_data_provider_crashes_proto_rawDescData = file_fmcsa_data_provider_crashes_proto_rawDesc
)

func file_fmcsa_data_provider_crashes_proto_rawDescGZIP() []byte {
	file_fmcsa_data_provider_crashes_proto_rawDescOnce.Do(func() {
		file_fmcsa_data_provider_crashes_proto_rawDescData = protoimpl.X.CompressGZIP(file_fmcsa_data_provider_crashes_proto_rawDescData)
	})
	return file_fmcsa_data_provider_crashes_proto_rawDescData
}

var file_fmcsa_data_provider_crashes_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_fmcsa_data_provider_crashes_proto_goTypes = []interface{}{
	(*Crash)(nil),                 // 0: fmcsa_data_provider.Crash
	(*CrashKey)(nil),              // 1: fmcsa_data_provider.CrashKey
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_fmcsa_data_provider_crashes_proto_depIdxs = []int32{
	1, // 0: fmcsa_data_provider.Crash.key:type_name -> fmcsa_data_provider.CrashKey
	2, // 1: fmcsa_data_provider.Crash.report_date:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_fmcsa_data_provider_crashes_proto_init() }
func file_fmcsa_data_provider_crashes_proto_init() {
	if File_fmcsa_data_provider_crashes_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_fmcsa_data_provider_crashes_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Crash); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_crashes_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrashKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_fmcsa_data_provider_crashes_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_fmcsa_data_provider_crashes_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_fmcsa_data_provider_crashes_proto_goTypes,
		DependencyIndexes: file_fmcsa_data_provider_crashes_proto_depIdxs,
		MessageInfos:      file_fmcsa_data_provider_crashes_proto_msgTypes,
	}.Build()
	File_fmcsa_data_provider_crashes_proto = out.File
	file_fmcsa_data_provider_crashes_proto_rawDesc = nil
	file_fmcsa_data_provider_crashes_proto_goTypes = nil
	file_fmcsa_data_provider_crashes_proto_depIdxs = nil
}
