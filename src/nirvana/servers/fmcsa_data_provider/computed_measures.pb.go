// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: fmcsa_data_provider/computed_measures.proto

package fmcsa_data_provider

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BasicMeasure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InspectionsWithViolations int32   `protobuf:"varint,1,opt,name=inspections_with_violations,json=inspectionsWithViolations,proto3" json:"inspections_with_violations,omitempty"`
	Measure                   float64 `protobuf:"fixed64,2,opt,name=measure,proto3" json:"measure,omitempty"`
	Group                     string  `protobuf:"bytes,3,opt,name=group,proto3" json:"group,omitempty"`
	AccuteCriticalIndicator   bool    `protobuf:"varint,4,opt,name=accute_critical_indicator,json=accuteCriticalIndicator,proto3" json:"accute_critical_indicator,omitempty"`
	Conclusive                bool    `protobuf:"varint,5,opt,name=conclusive,proto3" json:"conclusive,omitempty"`
}

func (x *BasicMeasure) Reset() {
	*x = BasicMeasure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_computed_measures_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BasicMeasure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasicMeasure) ProtoMessage() {}

func (x *BasicMeasure) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_computed_measures_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasicMeasure.ProtoReflect.Descriptor instead.
func (*BasicMeasure) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_computed_measures_proto_rawDescGZIP(), []int{0}
}

func (x *BasicMeasure) GetInspectionsWithViolations() int32 {
	if x != nil {
		return x.InspectionsWithViolations
	}
	return 0
}

func (x *BasicMeasure) GetMeasure() float64 {
	if x != nil {
		return x.Measure
	}
	return 0
}

func (x *BasicMeasure) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *BasicMeasure) GetAccuteCriticalIndicator() bool {
	if x != nil {
		return x.AccuteCriticalIndicator
	}
	return false
}

func (x *BasicMeasure) GetConclusive() bool {
	if x != nil {
		return x.Conclusive
	}
	return false
}

type CrashIndicatorMeasure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Measure    float64 `protobuf:"fixed64,1,opt,name=measure,proto3" json:"measure,omitempty"`
	Group      string  `protobuf:"bytes,2,opt,name=group,proto3" json:"group,omitempty"`
	Conclusive bool    `protobuf:"varint,3,opt,name=conclusive,proto3" json:"conclusive,omitempty"`
}

func (x *CrashIndicatorMeasure) Reset() {
	*x = CrashIndicatorMeasure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_computed_measures_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrashIndicatorMeasure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrashIndicatorMeasure) ProtoMessage() {}

func (x *CrashIndicatorMeasure) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_computed_measures_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrashIndicatorMeasure.ProtoReflect.Descriptor instead.
func (*CrashIndicatorMeasure) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_computed_measures_proto_rawDescGZIP(), []int{1}
}

func (x *CrashIndicatorMeasure) GetMeasure() float64 {
	if x != nil {
		return x.Measure
	}
	return 0
}

func (x *CrashIndicatorMeasure) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *CrashIndicatorMeasure) GetConclusive() bool {
	if x != nil {
		return x.Conclusive
	}
	return false
}

type ComputedMeasure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid                       string                 `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Date                       *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	DotNumber                  int64                  `protobuf:"varint,3,opt,name=dot_number,json=dotNumber,proto3" json:"dot_number,omitempty"`
	CarrierType                string                 `protobuf:"bytes,4,opt,name=carrier_type,json=carrierType,proto3" json:"carrier_type,omitempty"`
	PhyCountry                 string                 `protobuf:"bytes,5,opt,name=phy_country,json=phyCountry,proto3" json:"phy_country,omitempty"`
	InspTotal                  int32                  `protobuf:"varint,6,opt,name=insp_total,json=inspTotal,proto3" json:"insp_total,omitempty"`
	DriverInspTotal            int32                  `protobuf:"varint,7,opt,name=driver_insp_total,json=driverInspTotal,proto3" json:"driver_insp_total,omitempty"`
	DriverOosInspTotal         int32                  `protobuf:"varint,8,opt,name=driver_oos_insp_total,json=driverOosInspTotal,proto3" json:"driver_oos_insp_total,omitempty"`
	VehicleInspTotal           int32                  `protobuf:"varint,9,opt,name=vehicle_insp_total,json=vehicleInspTotal,proto3" json:"vehicle_insp_total,omitempty"`
	VehicleOosInspTotal        int32                  `protobuf:"varint,10,opt,name=vehicle_oos_insp_total,json=vehicleOosInspTotal,proto3" json:"vehicle_oos_insp_total,omitempty"`
	UnsafeDrivingMeasure       *BasicMeasure          `protobuf:"bytes,11,opt,name=unsafe_driving_measure,json=unsafeDrivingMeasure,proto3" json:"unsafe_driving_measure,omitempty"`
	HoursOfServiceMeasure      *BasicMeasure          `protobuf:"bytes,12,opt,name=hours_of_service_measure,json=hoursOfServiceMeasure,proto3" json:"hours_of_service_measure,omitempty"`
	DriverFitnessMeasure       *BasicMeasure          `protobuf:"bytes,13,opt,name=driver_fitness_measure,json=driverFitnessMeasure,proto3" json:"driver_fitness_measure,omitempty"`
	ControlledSubstanceMeasure *BasicMeasure          `protobuf:"bytes,14,opt,name=controlled_substance_measure,json=controlledSubstanceMeasure,proto3" json:"controlled_substance_measure,omitempty"`
	VehicleMaintenanceMeasure  *BasicMeasure          `protobuf:"bytes,15,opt,name=vehicle_maintenance_measure,json=vehicleMaintenanceMeasure,proto3" json:"vehicle_maintenance_measure,omitempty"`
	HazardousMaterialsMeasure  *BasicMeasure          `protobuf:"bytes,16,opt,name=hazardous_materials_measure,json=hazardousMaterialsMeasure,proto3" json:"hazardous_materials_measure,omitempty"`
	CrashIndicatorMeasure      *CrashIndicatorMeasure `protobuf:"bytes,17,opt,name=crash_indicator_measure,json=crashIndicatorMeasure,proto3" json:"crash_indicator_measure,omitempty"`
}

func (x *ComputedMeasure) Reset() {
	*x = ComputedMeasure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_computed_measures_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComputedMeasure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComputedMeasure) ProtoMessage() {}

func (x *ComputedMeasure) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_computed_measures_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComputedMeasure.ProtoReflect.Descriptor instead.
func (*ComputedMeasure) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_computed_measures_proto_rawDescGZIP(), []int{2}
}

func (x *ComputedMeasure) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *ComputedMeasure) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *ComputedMeasure) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *ComputedMeasure) GetCarrierType() string {
	if x != nil {
		return x.CarrierType
	}
	return ""
}

func (x *ComputedMeasure) GetPhyCountry() string {
	if x != nil {
		return x.PhyCountry
	}
	return ""
}

func (x *ComputedMeasure) GetInspTotal() int32 {
	if x != nil {
		return x.InspTotal
	}
	return 0
}

func (x *ComputedMeasure) GetDriverInspTotal() int32 {
	if x != nil {
		return x.DriverInspTotal
	}
	return 0
}

func (x *ComputedMeasure) GetDriverOosInspTotal() int32 {
	if x != nil {
		return x.DriverOosInspTotal
	}
	return 0
}

func (x *ComputedMeasure) GetVehicleInspTotal() int32 {
	if x != nil {
		return x.VehicleInspTotal
	}
	return 0
}

func (x *ComputedMeasure) GetVehicleOosInspTotal() int32 {
	if x != nil {
		return x.VehicleOosInspTotal
	}
	return 0
}

func (x *ComputedMeasure) GetUnsafeDrivingMeasure() *BasicMeasure {
	if x != nil {
		return x.UnsafeDrivingMeasure
	}
	return nil
}

func (x *ComputedMeasure) GetHoursOfServiceMeasure() *BasicMeasure {
	if x != nil {
		return x.HoursOfServiceMeasure
	}
	return nil
}

func (x *ComputedMeasure) GetDriverFitnessMeasure() *BasicMeasure {
	if x != nil {
		return x.DriverFitnessMeasure
	}
	return nil
}

func (x *ComputedMeasure) GetControlledSubstanceMeasure() *BasicMeasure {
	if x != nil {
		return x.ControlledSubstanceMeasure
	}
	return nil
}

func (x *ComputedMeasure) GetVehicleMaintenanceMeasure() *BasicMeasure {
	if x != nil {
		return x.VehicleMaintenanceMeasure
	}
	return nil
}

func (x *ComputedMeasure) GetHazardousMaterialsMeasure() *BasicMeasure {
	if x != nil {
		return x.HazardousMaterialsMeasure
	}
	return nil
}

func (x *ComputedMeasure) GetCrashIndicatorMeasure() *CrashIndicatorMeasure {
	if x != nil {
		return x.CrashIndicatorMeasure
	}
	return nil
}

var File_fmcsa_data_provider_computed_measures_proto protoreflect.FileDescriptor

var file_fmcsa_data_provider_computed_measures_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x6d,
	0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x66,
	0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xda, 0x01, 0x0a, 0x0c, 0x42, 0x61, 0x73, 0x69, 0x63, 0x4d, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x12, 0x3e, 0x0a, 0x1b, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x69, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x57, 0x69, 0x74, 0x68, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x3a, 0x0a, 0x19, 0x61, 0x63, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x63,
	0x72, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x61, 0x63, 0x63, 0x75, 0x74, 0x65, 0x43,
	0x72, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65,
	0x22, 0x67, 0x0a, 0x15, 0x43, 0x72, 0x61, 0x73, 0x68, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x6f, 0x72, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e,
	0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x63,
	0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65, 0x22, 0xb6, 0x08, 0x0a, 0x0f, 0x43, 0x6f,
	0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x68, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x68, 0x79, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x73, 0x70, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x6e, 0x73, 0x70, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x6e,
	0x73, 0x70, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x70, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x31, 0x0a, 0x15, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x6f, 0x6f, 0x73, 0x5f, 0x69, 0x6e,
	0x73, 0x70, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x6f, 0x73, 0x49, 0x6e, 0x73, 0x70, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x6e,
	0x73, 0x70, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x73, 0x70, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x33, 0x0a, 0x16, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x6f, 0x6f, 0x73, 0x5f,
	0x69, 0x6e, 0x73, 0x70, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x13, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4f, 0x6f, 0x73, 0x49, 0x6e, 0x73, 0x70,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x57, 0x0a, 0x16, 0x75, 0x6e, 0x73, 0x61, 0x66, 0x65, 0x5f,
	0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x73, 0x69,
	0x63, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x52, 0x14, 0x75, 0x6e, 0x73, 0x61, 0x66, 0x65,
	0x44, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x12, 0x5a,
	0x0a, 0x18, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x4d, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x52, 0x15, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x4f, 0x66, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x12, 0x57, 0x0a, 0x16, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x6d, 0x63,
	0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x52, 0x14, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x46, 0x69, 0x74, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x12, 0x63, 0x0a, 0x1c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65,
	0x64, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x6d, 0x63, 0x73,
	0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e,
	0x42, 0x61, 0x73, 0x69, 0x63, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x52, 0x1a, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x64, 0x53, 0x75, 0x62, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x12, 0x61, 0x0a, 0x1b, 0x76, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x19, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x12, 0x61, 0x0a, 0x1b, 0x68,
	0x61, 0x7a, 0x61, 0x72, 0x64, 0x6f, 0x75, 0x73, 0x5f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x73, 0x5f, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x4d, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x52, 0x19, 0x68, 0x61, 0x7a, 0x61, 0x72, 0x64, 0x6f, 0x75, 0x73, 0x4d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x12, 0x62,
	0x0a, 0x17, 0x63, 0x72, 0x61, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x61, 0x73, 0x68, 0x49, 0x6e, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x6f, 0x72, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x52, 0x15, 0x63, 0x72, 0x61,
	0x73, 0x68, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x4d, 0x65, 0x61, 0x73, 0x75,
	0x72, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_fmcsa_data_provider_computed_measures_proto_rawDescOnce sync.Once
	file_fmcsa_data_provider_computed_measures_proto_rawDescData = file_fmcsa_data_provider_computed_measures_proto_rawDesc
)

func file_fmcsa_data_provider_computed_measures_proto_rawDescGZIP() []byte {
	file_fmcsa_data_provider_computed_measures_proto_rawDescOnce.Do(func() {
		file_fmcsa_data_provider_computed_measures_proto_rawDescData = protoimpl.X.CompressGZIP(file_fmcsa_data_provider_computed_measures_proto_rawDescData)
	})
	return file_fmcsa_data_provider_computed_measures_proto_rawDescData
}

var file_fmcsa_data_provider_computed_measures_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_fmcsa_data_provider_computed_measures_proto_goTypes = []interface{}{
	(*BasicMeasure)(nil),          // 0: fmcsa_data_provider.BasicMeasure
	(*CrashIndicatorMeasure)(nil), // 1: fmcsa_data_provider.CrashIndicatorMeasure
	(*ComputedMeasure)(nil),       // 2: fmcsa_data_provider.ComputedMeasure
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_fmcsa_data_provider_computed_measures_proto_depIdxs = []int32{
	3, // 0: fmcsa_data_provider.ComputedMeasure.date:type_name -> google.protobuf.Timestamp
	0, // 1: fmcsa_data_provider.ComputedMeasure.unsafe_driving_measure:type_name -> fmcsa_data_provider.BasicMeasure
	0, // 2: fmcsa_data_provider.ComputedMeasure.hours_of_service_measure:type_name -> fmcsa_data_provider.BasicMeasure
	0, // 3: fmcsa_data_provider.ComputedMeasure.driver_fitness_measure:type_name -> fmcsa_data_provider.BasicMeasure
	0, // 4: fmcsa_data_provider.ComputedMeasure.controlled_substance_measure:type_name -> fmcsa_data_provider.BasicMeasure
	0, // 5: fmcsa_data_provider.ComputedMeasure.vehicle_maintenance_measure:type_name -> fmcsa_data_provider.BasicMeasure
	0, // 6: fmcsa_data_provider.ComputedMeasure.hazardous_materials_measure:type_name -> fmcsa_data_provider.BasicMeasure
	1, // 7: fmcsa_data_provider.ComputedMeasure.crash_indicator_measure:type_name -> fmcsa_data_provider.CrashIndicatorMeasure
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_fmcsa_data_provider_computed_measures_proto_init() }
func file_fmcsa_data_provider_computed_measures_proto_init() {
	if File_fmcsa_data_provider_computed_measures_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_fmcsa_data_provider_computed_measures_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BasicMeasure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_computed_measures_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrashIndicatorMeasure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_computed_measures_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComputedMeasure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_fmcsa_data_provider_computed_measures_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_fmcsa_data_provider_computed_measures_proto_goTypes,
		DependencyIndexes: file_fmcsa_data_provider_computed_measures_proto_depIdxs,
		MessageInfos:      file_fmcsa_data_provider_computed_measures_proto_msgTypes,
	}.Build()
	File_fmcsa_data_provider_computed_measures_proto = out.File
	file_fmcsa_data_provider_computed_measures_proto_rawDesc = nil
	file_fmcsa_data_provider_computed_measures_proto_goTypes = nil
	file_fmcsa_data_provider_computed_measures_proto_depIdxs = nil
}
