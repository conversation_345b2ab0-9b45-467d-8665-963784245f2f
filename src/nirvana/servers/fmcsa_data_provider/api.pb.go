// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: fmcsa_data_provider/api.proto

package fmcsa_data_provider

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	proto "nirvanatech.com/nirvana/common-go/proto"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetComputedMeasuresRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber int64           `protobuf:"varint,1,opt,name=dot_number,json=dotNumber,proto3" json:"dot_number,omitempty"`
	Interval  *proto.Interval `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
}

func (x *GetComputedMeasuresRequest) Reset() {
	*x = GetComputedMeasuresRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetComputedMeasuresRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetComputedMeasuresRequest) ProtoMessage() {}

func (x *GetComputedMeasuresRequest) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetComputedMeasuresRequest.ProtoReflect.Descriptor instead.
func (*GetComputedMeasuresRequest) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetComputedMeasuresRequest) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *GetComputedMeasuresRequest) GetInterval() *proto.Interval {
	if x != nil {
		return x.Interval
	}
	return nil
}

type GetComputedMeasuresResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComputedMeasures []*ComputedMeasure `protobuf:"bytes,1,rep,name=computed_measures,json=computedMeasures,proto3" json:"computed_measures,omitempty"`
}

func (x *GetComputedMeasuresResponse) Reset() {
	*x = GetComputedMeasuresResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetComputedMeasuresResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetComputedMeasuresResponse) ProtoMessage() {}

func (x *GetComputedMeasuresResponse) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetComputedMeasuresResponse.ProtoReflect.Descriptor instead.
func (*GetComputedMeasuresResponse) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetComputedMeasuresResponse) GetComputedMeasures() []*ComputedMeasure {
	if x != nil {
		return x.ComputedMeasures
	}
	return nil
}

type GetInspectionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber int64           `protobuf:"varint,1,opt,name=dot_number,json=dotNumber,proto3" json:"dot_number,omitempty"`
	Interval  *proto.Interval `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
}

func (x *GetInspectionsRequest) Reset() {
	*x = GetInspectionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInspectionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInspectionsRequest) ProtoMessage() {}

func (x *GetInspectionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInspectionsRequest.ProtoReflect.Descriptor instead.
func (*GetInspectionsRequest) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetInspectionsRequest) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *GetInspectionsRequest) GetInterval() *proto.Interval {
	if x != nil {
		return x.Interval
	}
	return nil
}

type GetInspectionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Inspections []*Inspection `protobuf:"bytes,1,rep,name=inspections,proto3" json:"inspections,omitempty"`
}

func (x *GetInspectionsResponse) Reset() {
	*x = GetInspectionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInspectionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInspectionsResponse) ProtoMessage() {}

func (x *GetInspectionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInspectionsResponse.ProtoReflect.Descriptor instead.
func (*GetInspectionsResponse) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetInspectionsResponse) GetInspections() []*Inspection {
	if x != nil {
		return x.Inspections
	}
	return nil
}

type GetCrashesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber int64           `protobuf:"varint,1,opt,name=dot_number,json=dotNumber,proto3" json:"dot_number,omitempty"`
	Interval  *proto.Interval `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
}

func (x *GetCrashesRequest) Reset() {
	*x = GetCrashesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCrashesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCrashesRequest) ProtoMessage() {}

func (x *GetCrashesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCrashesRequest.ProtoReflect.Descriptor instead.
func (*GetCrashesRequest) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetCrashesRequest) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *GetCrashesRequest) GetInterval() *proto.Interval {
	if x != nil {
		return x.Interval
	}
	return nil
}

type GetCrashesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Crashes []*Crash `protobuf:"bytes,1,rep,name=crashes,proto3" json:"crashes,omitempty"`
}

func (x *GetCrashesResponse) Reset() {
	*x = GetCrashesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCrashesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCrashesResponse) ProtoMessage() {}

func (x *GetCrashesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCrashesResponse.ProtoReflect.Descriptor instead.
func (*GetCrashesResponse) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetCrashesResponse) GetCrashes() []*Crash {
	if x != nil {
		return x.Crashes
	}
	return nil
}

var File_fmcsa_data_provider_api_proto protoreflect.FileDescriptor

var file_fmcsa_data_provider_api_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x13, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x69, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x21, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x2f, 0x63, 0x72, 0x61, 0x73, 0x68, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2b, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64,
	0x5f, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x69, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x4d, 0x65,
	0x61, 0x73, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x64, 0x6f, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x08,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x22, 0x70, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x11, 0x63, 0x6f, 0x6d,
	0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70,
	0x75, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x73, 0x22, 0x64, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x22, 0x5b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x0b,
	0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x60, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x72, 0x61, 0x73, 0x68, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x22, 0x4a, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x72, 0x61, 0x73, 0x68, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x63, 0x72, 0x61, 0x73, 0x68,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x43,
	0x72, 0x61, 0x73, 0x68, 0x52, 0x07, 0x63, 0x72, 0x61, 0x73, 0x68, 0x65, 0x73, 0x32, 0xdd, 0x02,
	0x0a, 0x11, 0x46, 0x6d, 0x63, 0x73, 0x61, 0x44, 0x61, 0x74, 0x61, 0x50, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x12, 0x7a, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x66, 0x6d, 0x63,
	0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x66, 0x6d,
	0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x6b, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x2a, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e,
	0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x43, 0x72, 0x61, 0x73, 0x68, 0x65, 0x73, 0x12, 0x26, 0x2e, 0x66, 0x6d, 0x63,
	0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x61, 0x73, 0x68, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x27, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x61, 0x73,
	0x68, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_fmcsa_data_provider_api_proto_rawDescOnce sync.Once
	file_fmcsa_data_provider_api_proto_rawDescData = file_fmcsa_data_provider_api_proto_rawDesc
)

func file_fmcsa_data_provider_api_proto_rawDescGZIP() []byte {
	file_fmcsa_data_provider_api_proto_rawDescOnce.Do(func() {
		file_fmcsa_data_provider_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_fmcsa_data_provider_api_proto_rawDescData)
	})
	return file_fmcsa_data_provider_api_proto_rawDescData
}

var file_fmcsa_data_provider_api_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_fmcsa_data_provider_api_proto_goTypes = []interface{}{
	(*GetComputedMeasuresRequest)(nil),  // 0: fmcsa_data_provider.GetComputedMeasuresRequest
	(*GetComputedMeasuresResponse)(nil), // 1: fmcsa_data_provider.GetComputedMeasuresResponse
	(*GetInspectionsRequest)(nil),       // 2: fmcsa_data_provider.GetInspectionsRequest
	(*GetInspectionsResponse)(nil),      // 3: fmcsa_data_provider.GetInspectionsResponse
	(*GetCrashesRequest)(nil),           // 4: fmcsa_data_provider.GetCrashesRequest
	(*GetCrashesResponse)(nil),          // 5: fmcsa_data_provider.GetCrashesResponse
	(*proto.Interval)(nil),              // 6: common.Interval
	(*ComputedMeasure)(nil),             // 7: fmcsa_data_provider.ComputedMeasure
	(*Inspection)(nil),                  // 8: fmcsa_data_provider.Inspection
	(*Crash)(nil),                       // 9: fmcsa_data_provider.Crash
}
var file_fmcsa_data_provider_api_proto_depIdxs = []int32{
	6, // 0: fmcsa_data_provider.GetComputedMeasuresRequest.interval:type_name -> common.Interval
	7, // 1: fmcsa_data_provider.GetComputedMeasuresResponse.computed_measures:type_name -> fmcsa_data_provider.ComputedMeasure
	6, // 2: fmcsa_data_provider.GetInspectionsRequest.interval:type_name -> common.Interval
	8, // 3: fmcsa_data_provider.GetInspectionsResponse.inspections:type_name -> fmcsa_data_provider.Inspection
	6, // 4: fmcsa_data_provider.GetCrashesRequest.interval:type_name -> common.Interval
	9, // 5: fmcsa_data_provider.GetCrashesResponse.crashes:type_name -> fmcsa_data_provider.Crash
	0, // 6: fmcsa_data_provider.FmcsaDataProvider.GetComputedMeasures:input_type -> fmcsa_data_provider.GetComputedMeasuresRequest
	2, // 7: fmcsa_data_provider.FmcsaDataProvider.GetInspections:input_type -> fmcsa_data_provider.GetInspectionsRequest
	4, // 8: fmcsa_data_provider.FmcsaDataProvider.GetCrashes:input_type -> fmcsa_data_provider.GetCrashesRequest
	1, // 9: fmcsa_data_provider.FmcsaDataProvider.GetComputedMeasures:output_type -> fmcsa_data_provider.GetComputedMeasuresResponse
	3, // 10: fmcsa_data_provider.FmcsaDataProvider.GetInspections:output_type -> fmcsa_data_provider.GetInspectionsResponse
	5, // 11: fmcsa_data_provider.FmcsaDataProvider.GetCrashes:output_type -> fmcsa_data_provider.GetCrashesResponse
	9, // [9:12] is the sub-list for method output_type
	6, // [6:9] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_fmcsa_data_provider_api_proto_init() }
func file_fmcsa_data_provider_api_proto_init() {
	if File_fmcsa_data_provider_api_proto != nil {
		return
	}
	file_fmcsa_data_provider_inspections_proto_init()
	file_fmcsa_data_provider_crashes_proto_init()
	file_fmcsa_data_provider_computed_measures_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_fmcsa_data_provider_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetComputedMeasuresRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetComputedMeasuresResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInspectionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInspectionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCrashesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCrashesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_fmcsa_data_provider_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_fmcsa_data_provider_api_proto_goTypes,
		DependencyIndexes: file_fmcsa_data_provider_api_proto_depIdxs,
		MessageInfos:      file_fmcsa_data_provider_api_proto_msgTypes,
	}.Build()
	File_fmcsa_data_provider_api_proto = out.File
	file_fmcsa_data_provider_api_proto_rawDesc = nil
	file_fmcsa_data_provider_api_proto_goTypes = nil
	file_fmcsa_data_provider_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// FmcsaDataProviderClient is the client API for FmcsaDataProvider service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FmcsaDataProviderClient interface {
	GetComputedMeasures(ctx context.Context, in *GetComputedMeasuresRequest, opts ...grpc.CallOption) (*GetComputedMeasuresResponse, error)
	GetInspections(ctx context.Context, in *GetInspectionsRequest, opts ...grpc.CallOption) (*GetInspectionsResponse, error)
	GetCrashes(ctx context.Context, in *GetCrashesRequest, opts ...grpc.CallOption) (*GetCrashesResponse, error)
}

type fmcsaDataProviderClient struct {
	cc grpc.ClientConnInterface
}

func NewFmcsaDataProviderClient(cc grpc.ClientConnInterface) FmcsaDataProviderClient {
	return &fmcsaDataProviderClient{cc}
}

func (c *fmcsaDataProviderClient) GetComputedMeasures(ctx context.Context, in *GetComputedMeasuresRequest, opts ...grpc.CallOption) (*GetComputedMeasuresResponse, error) {
	out := new(GetComputedMeasuresResponse)
	err := c.cc.Invoke(ctx, "/fmcsa_data_provider.FmcsaDataProvider/GetComputedMeasures", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fmcsaDataProviderClient) GetInspections(ctx context.Context, in *GetInspectionsRequest, opts ...grpc.CallOption) (*GetInspectionsResponse, error) {
	out := new(GetInspectionsResponse)
	err := c.cc.Invoke(ctx, "/fmcsa_data_provider.FmcsaDataProvider/GetInspections", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fmcsaDataProviderClient) GetCrashes(ctx context.Context, in *GetCrashesRequest, opts ...grpc.CallOption) (*GetCrashesResponse, error) {
	out := new(GetCrashesResponse)
	err := c.cc.Invoke(ctx, "/fmcsa_data_provider.FmcsaDataProvider/GetCrashes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FmcsaDataProviderServer is the server API for FmcsaDataProvider service.
type FmcsaDataProviderServer interface {
	GetComputedMeasures(context.Context, *GetComputedMeasuresRequest) (*GetComputedMeasuresResponse, error)
	GetInspections(context.Context, *GetInspectionsRequest) (*GetInspectionsResponse, error)
	GetCrashes(context.Context, *GetCrashesRequest) (*GetCrashesResponse, error)
}

// UnimplementedFmcsaDataProviderServer can be embedded to have forward compatible implementations.
type UnimplementedFmcsaDataProviderServer struct {
}

func (*UnimplementedFmcsaDataProviderServer) GetComputedMeasures(context.Context, *GetComputedMeasuresRequest) (*GetComputedMeasuresResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetComputedMeasures not implemented")
}
func (*UnimplementedFmcsaDataProviderServer) GetInspections(context.Context, *GetInspectionsRequest) (*GetInspectionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInspections not implemented")
}
func (*UnimplementedFmcsaDataProviderServer) GetCrashes(context.Context, *GetCrashesRequest) (*GetCrashesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCrashes not implemented")
}

func RegisterFmcsaDataProviderServer(s *grpc.Server, srv FmcsaDataProviderServer) {
	s.RegisterService(&_FmcsaDataProvider_serviceDesc, srv)
}

func _FmcsaDataProvider_GetComputedMeasures_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetComputedMeasuresRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FmcsaDataProviderServer).GetComputedMeasures(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fmcsa_data_provider.FmcsaDataProvider/GetComputedMeasures",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FmcsaDataProviderServer).GetComputedMeasures(ctx, req.(*GetComputedMeasuresRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FmcsaDataProvider_GetInspections_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInspectionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FmcsaDataProviderServer).GetInspections(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fmcsa_data_provider.FmcsaDataProvider/GetInspections",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FmcsaDataProviderServer).GetInspections(ctx, req.(*GetInspectionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FmcsaDataProvider_GetCrashes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCrashesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FmcsaDataProviderServer).GetCrashes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fmcsa_data_provider.FmcsaDataProvider/GetCrashes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FmcsaDataProviderServer).GetCrashes(ctx, req.(*GetCrashesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _FmcsaDataProvider_serviceDesc = grpc.ServiceDesc{
	ServiceName: "fmcsa_data_provider.FmcsaDataProvider",
	HandlerType: (*FmcsaDataProviderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetComputedMeasures",
			Handler:    _FmcsaDataProvider_GetComputedMeasures_Handler,
		},
		{
			MethodName: "GetInspections",
			Handler:    _FmcsaDataProvider_GetInspections_Handler,
		},
		{
			MethodName: "GetCrashes",
			Handler:    _FmcsaDataProvider_GetCrashes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "fmcsa_data_provider/api.proto",
}
