// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: fmcsa_data_provider/inspections.proto

package fmcsa_data_provider

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InspectionLevelEnum int32

const (
	InspectionLevelEnum_INSPECTION_LEVEL_UNSPECIFIED  InspectionLevelEnum = 0
	InspectionLevelEnum_INSPECTION_LEVEL_GENERAL      InspectionLevelEnum = 1
	InspectionLevelEnum_INSPECTION_LEVEL_WALK_AROUND  InspectionLevelEnum = 2
	InspectionLevelEnum_INSPECTION_LEVEL_DRIVER_ONLY  InspectionLevelEnum = 3
	InspectionLevelEnum_INSPECTION_LEVEL_SPECIAL      InspectionLevelEnum = 4
	InspectionLevelEnum_INSPECTION_LEVEL_VEHICLE_ONLY InspectionLevelEnum = 5
	InspectionLevelEnum_INSPECTION_LEVEL_RADIOACTIVE  InspectionLevelEnum = 6
	InspectionLevelEnum_INSPECTION_LEVEL_INVALID      InspectionLevelEnum = 99
)

// Enum value maps for InspectionLevelEnum.
var (
	InspectionLevelEnum_name = map[int32]string{
		0:  "INSPECTION_LEVEL_UNSPECIFIED",
		1:  "INSPECTION_LEVEL_GENERAL",
		2:  "INSPECTION_LEVEL_WALK_AROUND",
		3:  "INSPECTION_LEVEL_DRIVER_ONLY",
		4:  "INSPECTION_LEVEL_SPECIAL",
		5:  "INSPECTION_LEVEL_VEHICLE_ONLY",
		6:  "INSPECTION_LEVEL_RADIOACTIVE",
		99: "INSPECTION_LEVEL_INVALID",
	}
	InspectionLevelEnum_value = map[string]int32{
		"INSPECTION_LEVEL_UNSPECIFIED":  0,
		"INSPECTION_LEVEL_GENERAL":      1,
		"INSPECTION_LEVEL_WALK_AROUND":  2,
		"INSPECTION_LEVEL_DRIVER_ONLY":  3,
		"INSPECTION_LEVEL_SPECIAL":      4,
		"INSPECTION_LEVEL_VEHICLE_ONLY": 5,
		"INSPECTION_LEVEL_RADIOACTIVE":  6,
		"INSPECTION_LEVEL_INVALID":      99,
	}
)

func (x InspectionLevelEnum) Enum() *InspectionLevelEnum {
	p := new(InspectionLevelEnum)
	*p = x
	return p
}

func (x InspectionLevelEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InspectionLevelEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_fmcsa_data_provider_inspections_proto_enumTypes[0].Descriptor()
}

func (InspectionLevelEnum) Type() protoreflect.EnumType {
	return &file_fmcsa_data_provider_inspections_proto_enumTypes[0]
}

func (x InspectionLevelEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InspectionLevelEnum.Descriptor instead.
func (InspectionLevelEnum) EnumDescriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_inspections_proto_rawDescGZIP(), []int{0}
}

type BasicCategory int32

const (
	BasicCategory_BASIC_CATEGORY_UNSPECIFIED           BasicCategory = 0
	BasicCategory_BASIC_CATEGORY_UNSAFE_DRIVING        BasicCategory = 1
	BasicCategory_BASIC_CATEGORY_HOS_COMPLIANCE        BasicCategory = 2
	BasicCategory_BASIC_CATEGORY_VEHICLE_MAINTENANCE   BasicCategory = 3
	BasicCategory_BASIC_CATEGORY_CONTROLLED_SUBSTANCES BasicCategory = 4
	BasicCategory_BASIC_CATEGORY_HM_COMPLIANCE         BasicCategory = 5
	BasicCategory_BASIC_CATEGORY_DRIVER_FITNESS        BasicCategory = 6
	BasicCategory_BASIC_CATEGORY_CRASH_INDICATOR       BasicCategory = 7
	BasicCategory_BASIC_CATEGORY_INSURANCE_OTHER       BasicCategory = 8
)

// Enum value maps for BasicCategory.
var (
	BasicCategory_name = map[int32]string{
		0: "BASIC_CATEGORY_UNSPECIFIED",
		1: "BASIC_CATEGORY_UNSAFE_DRIVING",
		2: "BASIC_CATEGORY_HOS_COMPLIANCE",
		3: "BASIC_CATEGORY_VEHICLE_MAINTENANCE",
		4: "BASIC_CATEGORY_CONTROLLED_SUBSTANCES",
		5: "BASIC_CATEGORY_HM_COMPLIANCE",
		6: "BASIC_CATEGORY_DRIVER_FITNESS",
		7: "BASIC_CATEGORY_CRASH_INDICATOR",
		8: "BASIC_CATEGORY_INSURANCE_OTHER",
	}
	BasicCategory_value = map[string]int32{
		"BASIC_CATEGORY_UNSPECIFIED":           0,
		"BASIC_CATEGORY_UNSAFE_DRIVING":        1,
		"BASIC_CATEGORY_HOS_COMPLIANCE":        2,
		"BASIC_CATEGORY_VEHICLE_MAINTENANCE":   3,
		"BASIC_CATEGORY_CONTROLLED_SUBSTANCES": 4,
		"BASIC_CATEGORY_HM_COMPLIANCE":         5,
		"BASIC_CATEGORY_DRIVER_FITNESS":        6,
		"BASIC_CATEGORY_CRASH_INDICATOR":       7,
		"BASIC_CATEGORY_INSURANCE_OTHER":       8,
	}
)

func (x BasicCategory) Enum() *BasicCategory {
	p := new(BasicCategory)
	*p = x
	return p
}

func (x BasicCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BasicCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_fmcsa_data_provider_inspections_proto_enumTypes[1].Descriptor()
}

func (BasicCategory) Type() protoreflect.EnumType {
	return &file_fmcsa_data_provider_inspections_proto_enumTypes[1]
}

func (x BasicCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BasicCategory.Descriptor instead.
func (BasicCategory) EnumDescriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_inspections_proto_rawDescGZIP(), []int{1}
}

type InspectionIdentification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InspectionId int64  `protobuf:"varint,1,opt,name=inspection_id,json=inspectionId,proto3" json:"inspection_id,omitempty"`
	ReportState  string `protobuf:"bytes,2,opt,name=report_state,json=reportState,proto3" json:"report_state,omitempty"`
	ReportNumber string `protobuf:"bytes,3,opt,name=report_number,json=reportNumber,proto3" json:"report_number,omitempty"`
}

func (x *InspectionIdentification) Reset() {
	*x = InspectionIdentification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InspectionIdentification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InspectionIdentification) ProtoMessage() {}

func (x *InspectionIdentification) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InspectionIdentification.ProtoReflect.Descriptor instead.
func (*InspectionIdentification) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_inspections_proto_rawDescGZIP(), []int{0}
}

func (x *InspectionIdentification) GetInspectionId() int64 {
	if x != nil {
		return x.InspectionId
	}
	return 0
}

func (x *InspectionIdentification) GetReportState() string {
	if x != nil {
		return x.ReportState
	}
	return ""
}

func (x *InspectionIdentification) GetReportNumber() string {
	if x != nil {
		return x.ReportNumber
	}
	return ""
}

type InspectionTimestamps struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InspectionDate     *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=inspection_date,json=inspectionDate,proto3" json:"inspection_date,omitempty"`
	InspStartTime      *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=insp_start_time,json=inspStartTime,proto3" json:"insp_start_time,omitempty"`
	InspEndTime        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=insp_end_time,json=inspEndTime,proto3" json:"insp_end_time,omitempty"`
	FmcsaPublishedDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=fmcsa_published_date,json=fmcsaPublishedDate,proto3" json:"fmcsa_published_date,omitempty"`
	McmisAddDate       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=mcmis_add_date,json=mcmisAddDate,proto3" json:"mcmis_add_date,omitempty"`
	DatagovCreatedAt   *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=datagov_created_at,json=datagovCreatedAt,proto3" json:"datagov_created_at,omitempty"`
	DatagovDeletedAt   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=datagov_deleted_at,json=datagovDeletedAt,proto3" json:"datagov_deleted_at,omitempty"`
}

func (x *InspectionTimestamps) Reset() {
	*x = InspectionTimestamps{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InspectionTimestamps) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InspectionTimestamps) ProtoMessage() {}

func (x *InspectionTimestamps) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InspectionTimestamps.ProtoReflect.Descriptor instead.
func (*InspectionTimestamps) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_inspections_proto_rawDescGZIP(), []int{1}
}

func (x *InspectionTimestamps) GetInspectionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.InspectionDate
	}
	return nil
}

func (x *InspectionTimestamps) GetInspStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.InspStartTime
	}
	return nil
}

func (x *InspectionTimestamps) GetInspEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.InspEndTime
	}
	return nil
}

func (x *InspectionTimestamps) GetFmcsaPublishedDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FmcsaPublishedDate
	}
	return nil
}

func (x *InspectionTimestamps) GetMcmisAddDate() *timestamppb.Timestamp {
	if x != nil {
		return x.McmisAddDate
	}
	return nil
}

func (x *InspectionTimestamps) GetDatagovCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DatagovCreatedAt
	}
	return nil
}

func (x *InspectionTimestamps) GetDatagovDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DatagovDeletedAt
	}
	return nil
}

type LocationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region          string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Location        string `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
	LocationDesc    string `protobuf:"bytes,3,opt,name=location_desc,json=locationDesc,proto3" json:"location_desc,omitempty"`
	CountyCodeState string `protobuf:"bytes,4,opt,name=county_code_state,json=countyCodeState,proto3" json:"county_code_state,omitempty"`
	CountyCode      int32  `protobuf:"varint,5,opt,name=county_code,json=countyCode,proto3" json:"county_code,omitempty"`
}

func (x *LocationInfo) Reset() {
	*x = LocationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationInfo) ProtoMessage() {}

func (x *LocationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationInfo.ProtoReflect.Descriptor instead.
func (*LocationInfo) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_inspections_proto_rawDescGZIP(), []int{2}
}

func (x *LocationInfo) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *LocationInfo) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *LocationInfo) GetLocationDesc() string {
	if x != nil {
		return x.LocationDesc
	}
	return ""
}

func (x *LocationInfo) GetCountyCodeState() string {
	if x != nil {
		return x.CountyCodeState
	}
	return ""
}

func (x *LocationInfo) GetCountyCode() int32 {
	if x != nil {
		return x.CountyCode
	}
	return 0
}

type EnforcementInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSizeWeightEnforcement  bool                `protobuf:"varint,1,opt,name=is_size_weight_enforcement,json=isSizeWeightEnforcement,proto3" json:"is_size_weight_enforcement,omitempty"`
	IsTrafficEnforcement     bool                `protobuf:"varint,2,opt,name=is_traffic_enforcement,json=isTrafficEnforcement,proto3" json:"is_traffic_enforcement,omitempty"`
	IsLocalEnforcement       bool                `protobuf:"varint,3,opt,name=is_local_enforcement,json=isLocalEnforcement,proto3" json:"is_local_enforcement,omitempty"`
	IsPostAccident           bool                `protobuf:"varint,4,opt,name=is_post_accident,json=isPostAccident,proto3" json:"is_post_accident,omitempty"`
	IsAlcoholControlSub      bool                `protobuf:"varint,5,opt,name=is_alcohol_control_sub,json=isAlcoholControlSub,proto3" json:"is_alcohol_control_sub,omitempty"`
	IsDrugInterdictionSearch bool                `protobuf:"varint,6,opt,name=is_drug_interdiction_search,json=isDrugInterdictionSearch,proto3" json:"is_drug_interdiction_search,omitempty"`
	DrugInterdictionArrests  int32               `protobuf:"varint,7,opt,name=drug_interdiction_arrests,json=drugInterdictionArrests,proto3" json:"drug_interdiction_arrests,omitempty"`
	InspectionFacility       string              `protobuf:"bytes,8,opt,name=inspection_facility,json=inspectionFacility,proto3" json:"inspection_facility,omitempty"`
	InspectionLevel          InspectionLevelEnum `protobuf:"varint,9,opt,name=inspection_level,json=inspectionLevel,proto3,enum=fmcsa_data_provider.InspectionLevelEnum" json:"inspection_level,omitempty"`
}

func (x *EnforcementInfo) Reset() {
	*x = EnforcementInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnforcementInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnforcementInfo) ProtoMessage() {}

func (x *EnforcementInfo) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnforcementInfo.ProtoReflect.Descriptor instead.
func (*EnforcementInfo) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_inspections_proto_rawDescGZIP(), []int{3}
}

func (x *EnforcementInfo) GetIsSizeWeightEnforcement() bool {
	if x != nil {
		return x.IsSizeWeightEnforcement
	}
	return false
}

func (x *EnforcementInfo) GetIsTrafficEnforcement() bool {
	if x != nil {
		return x.IsTrafficEnforcement
	}
	return false
}

func (x *EnforcementInfo) GetIsLocalEnforcement() bool {
	if x != nil {
		return x.IsLocalEnforcement
	}
	return false
}

func (x *EnforcementInfo) GetIsPostAccident() bool {
	if x != nil {
		return x.IsPostAccident
	}
	return false
}

func (x *EnforcementInfo) GetIsAlcoholControlSub() bool {
	if x != nil {
		return x.IsAlcoholControlSub
	}
	return false
}

func (x *EnforcementInfo) GetIsDrugInterdictionSearch() bool {
	if x != nil {
		return x.IsDrugInterdictionSearch
	}
	return false
}

func (x *EnforcementInfo) GetDrugInterdictionArrests() int32 {
	if x != nil {
		return x.DrugInterdictionArrests
	}
	return 0
}

func (x *EnforcementInfo) GetInspectionFacility() string {
	if x != nil {
		return x.InspectionFacility
	}
	return ""
}

func (x *EnforcementInfo) GetInspectionLevel() InspectionLevelEnum {
	if x != nil {
		return x.InspectionLevel
	}
	return InspectionLevelEnum_INSPECTION_LEVEL_UNSPECIFIED
}

type ViolationAggregates struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalBasicViolations int32 `protobuf:"varint,1,opt,name=total_basic_violations,json=totalBasicViolations,proto3" json:"total_basic_violations,omitempty"`
	TotalViolations      int32 `protobuf:"varint,2,opt,name=total_violations,json=totalViolations,proto3" json:"total_violations,omitempty"`
}

func (x *ViolationAggregates) Reset() {
	*x = ViolationAggregates{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ViolationAggregates) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ViolationAggregates) ProtoMessage() {}

func (x *ViolationAggregates) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ViolationAggregates.ProtoReflect.Descriptor instead.
func (*ViolationAggregates) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_inspections_proto_rawDescGZIP(), []int{4}
}

func (x *ViolationAggregates) GetTotalBasicViolations() int32 {
	if x != nil {
		return x.TotalBasicViolations
	}
	return 0
}

func (x *ViolationAggregates) GetTotalViolations() int32 {
	if x != nil {
		return x.TotalViolations
	}
	return 0
}

type CarrierInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DotNumber                     int64  `protobuf:"varint,1,opt,name=dot_number,json=dotNumber,proto3" json:"dot_number,omitempty"`
	ShipperName                   string `protobuf:"bytes,2,opt,name=shipper_name,json=shipperName,proto3" json:"shipper_name,omitempty"`
	CombinationVehicleGrossWeight int32  `protobuf:"varint,3,opt,name=combination_vehicle_gross_weight,json=combinationVehicleGrossWeight,proto3" json:"combination_vehicle_gross_weight,omitempty"`
	DocketNumber                  int64  `protobuf:"varint,4,opt,name=docket_number,json=docketNumber,proto3" json:"docket_number,omitempty"`
	IsInterstateInspection        bool   `protobuf:"varint,5,opt,name=is_interstate_inspection,json=isInterstateInspection,proto3" json:"is_interstate_inspection,omitempty"`
	ShippingPaperNumber           string `protobuf:"bytes,6,opt,name=shipping_paper_number,json=shippingPaperNumber,proto3" json:"shipping_paper_number,omitempty"`
}

func (x *CarrierInfo) Reset() {
	*x = CarrierInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarrierInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarrierInfo) ProtoMessage() {}

func (x *CarrierInfo) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarrierInfo.ProtoReflect.Descriptor instead.
func (*CarrierInfo) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_inspections_proto_rawDescGZIP(), []int{5}
}

func (x *CarrierInfo) GetDotNumber() int64 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *CarrierInfo) GetShipperName() string {
	if x != nil {
		return x.ShipperName
	}
	return ""
}

func (x *CarrierInfo) GetCombinationVehicleGrossWeight() int32 {
	if x != nil {
		return x.CombinationVehicleGrossWeight
	}
	return 0
}

func (x *CarrierInfo) GetDocketNumber() int64 {
	if x != nil {
		return x.DocketNumber
	}
	return 0
}

func (x *CarrierInfo) GetIsInterstateInspection() bool {
	if x != nil {
		return x.IsInterstateInspection
	}
	return false
}

func (x *CarrierInfo) GetShippingPaperNumber() string {
	if x != nil {
		return x.ShippingPaperNumber
	}
	return ""
}

type Inspection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identification       *InspectionIdentification `protobuf:"bytes,1,opt,name=identification,proto3" json:"identification,omitempty"`
	InspectionTimestamps *InspectionTimestamps     `protobuf:"bytes,2,opt,name=inspection_timestamps,json=inspectionTimestamps,proto3" json:"inspection_timestamps,omitempty"`
	LocationInfo         *LocationInfo             `protobuf:"bytes,3,opt,name=location_info,json=locationInfo,proto3" json:"location_info,omitempty"`
	EnforcementInfo      *EnforcementInfo          `protobuf:"bytes,4,opt,name=enforcement_info,json=enforcementInfo,proto3" json:"enforcement_info,omitempty"`
	ViolationAggregates  *ViolationAggregates      `protobuf:"bytes,5,opt,name=violation_aggregates,json=violationAggregates,proto3" json:"violation_aggregates,omitempty"`
	CarrierInfo          *CarrierInfo              `protobuf:"bytes,6,opt,name=carrier_info,json=carrierInfo,proto3" json:"carrier_info,omitempty"`
	StatusCode           string                    `protobuf:"bytes,7,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
}

func (x *Inspection) Reset() {
	*x = Inspection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Inspection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Inspection) ProtoMessage() {}

func (x *Inspection) ProtoReflect() protoreflect.Message {
	mi := &file_fmcsa_data_provider_inspections_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Inspection.ProtoReflect.Descriptor instead.
func (*Inspection) Descriptor() ([]byte, []int) {
	return file_fmcsa_data_provider_inspections_proto_rawDescGZIP(), []int{6}
}

func (x *Inspection) GetIdentification() *InspectionIdentification {
	if x != nil {
		return x.Identification
	}
	return nil
}

func (x *Inspection) GetInspectionTimestamps() *InspectionTimestamps {
	if x != nil {
		return x.InspectionTimestamps
	}
	return nil
}

func (x *Inspection) GetLocationInfo() *LocationInfo {
	if x != nil {
		return x.LocationInfo
	}
	return nil
}

func (x *Inspection) GetEnforcementInfo() *EnforcementInfo {
	if x != nil {
		return x.EnforcementInfo
	}
	return nil
}

func (x *Inspection) GetViolationAggregates() *ViolationAggregates {
	if x != nil {
		return x.ViolationAggregates
	}
	return nil
}

func (x *Inspection) GetCarrierInfo() *CarrierInfo {
	if x != nil {
		return x.CarrierInfo
	}
	return nil
}

func (x *Inspection) GetStatusCode() string {
	if x != nil {
		return x.StatusCode
	}
	return ""
}

var File_fmcsa_data_provider_inspections_proto protoreflect.FileDescriptor

var file_fmcsa_data_provider_inspections_proto_rawDesc = []byte{
	0x0a, 0x25, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x01,
	0x0a, 0x18, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x83, 0x04, 0x0a, 0x14, 0x49, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x73,
	0x12, 0x43, 0x0a, 0x0f, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x0f, 0x69, 0x6e, 0x73, 0x70, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x70,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x69, 0x6e, 0x73,
	0x70, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x69, 0x6e,
	0x73, 0x70, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x14, 0x66, 0x6d, 0x63,
	0x73, 0x61, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x12, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x0e, 0x6d, 0x63, 0x6d, 0x69, 0x73,
	0x5f, 0x61, 0x64, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6d, 0x63, 0x6d,
	0x69, 0x73, 0x41, 0x64, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x48, 0x0a, 0x12, 0x64, 0x61, 0x74,
	0x61, 0x67, 0x6f, 0x76, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x10, 0x64, 0x61, 0x74, 0x61, 0x67, 0x6f, 0x76, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x48, 0x0a, 0x12, 0x64, 0x61, 0x74, 0x61, 0x67, 0x6f, 0x76, 0x5f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x64, 0x61, 0x74,
	0x61, 0x67, 0x6f, 0x76, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xb4, 0x01,
	0x0a, 0x0c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x22, 0x98, 0x04, 0x0a, 0x0f, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x65, 0x6e, 0x66, 0x6f, 0x72,
	0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x69, 0x73,
	0x53, 0x69, 0x7a, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x66,
	0x66, 0x69, 0x63, 0x5f, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x69, 0x73, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63,
	0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x69,
	0x73, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x4c, 0x6f, 0x63,
	0x61, 0x6c, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x28, 0x0a,
	0x10, 0x69, 0x73, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x50, 0x6f, 0x73, 0x74, 0x41,
	0x63, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x16, 0x69, 0x73, 0x5f, 0x61, 0x6c,
	0x63, 0x6f, 0x68, 0x6f, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x73, 0x75,
	0x62, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x41, 0x6c, 0x63, 0x6f, 0x68,
	0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x53, 0x75, 0x62, 0x12, 0x3d, 0x0a, 0x1b,
	0x69, 0x73, 0x5f, 0x64, 0x72, 0x75, 0x67, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x18, 0x69, 0x73, 0x44, 0x72, 0x75, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x64, 0x69,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x3a, 0x0a, 0x19, 0x64,
	0x72, 0x75, 0x67, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x72, 0x72, 0x65, 0x73, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x17,
	0x64, 0x72, 0x75, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x72, 0x72, 0x65, 0x73, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x13, 0x69, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x61, 0x63, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x46, 0x61, 0x63, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x55, 0x0a, 0x10, 0x69, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x52, 0x0f,
	0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22,
	0x76, 0x0a, 0x13, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x61, 0x73,
	0x69, 0x63, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x29, 0x0a, 0x10,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x56, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xab, 0x02, 0x0a, 0x0b, 0x43, 0x61, 0x72, 0x72,
	0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x6f, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x68, 0x69, 0x70, 0x70, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x68,
	0x69, 0x70, 0x70, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x20, 0x63, 0x6f, 0x6d,
	0x62, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x73, 0x73, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x1d, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x73, 0x73, 0x57, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x6b, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x69, 0x73, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x73, 0x74, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61,
	0x70, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x70, 0x65, 0x72, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x9f, 0x04, 0x0a, 0x0a, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66,
	0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x2e, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5e, 0x0a, 0x15, 0x69,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x6d, 0x63,
	0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x2e, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x73, 0x52, 0x14, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x73, 0x12, 0x46, 0x0a, 0x0d, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x4f, 0x0a, 0x10, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b, 0x0a, 0x14, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x13, 0x76, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x43, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x66, 0x6d, 0x63, 0x73, 0x61, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x43, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x2a, 0x9c, 0x02, 0x0a, 0x15, 0x69, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x12, 0x20, 0x0a, 0x1c, 0x49, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x4c, 0x10,
	0x01, 0x12, 0x20, 0x0a, 0x1c, 0x49, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x57, 0x41, 0x4c, 0x4b, 0x5f, 0x41, 0x52, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x49, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x4f,
	0x4e, 0x4c, 0x59, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x53, 0x50, 0x45, 0x43, 0x49, 0x41,
	0x4c, 0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f,
	0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x49, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x52, 0x41, 0x44, 0x49, 0x4f,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x06, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x10, 0x63, 0x2a, 0xd4, 0x02, 0x0a, 0x0d, 0x42, 0x61, 0x73, 0x69, 0x63,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x1a, 0x42, 0x41, 0x53, 0x49,
	0x43, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x42, 0x41, 0x53, 0x49,
	0x43, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x41, 0x46,
	0x45, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x42,
	0x41, 0x53, 0x49, 0x43, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x48, 0x4f,
	0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x02, 0x12, 0x26,
	0x0a, 0x22, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x45, 0x4e,
	0x41, 0x4e, 0x43, 0x45, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c,
	0x4c, 0x45, 0x44, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x04,
	0x12, 0x20, 0x0a, 0x1c, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x48, 0x4d, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x49, 0x41, 0x4e, 0x43, 0x45,
	0x10, 0x05, 0x12, 0x21, 0x0a, 0x1d, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x54, 0x4e,
	0x45, 0x53, 0x53, 0x10, 0x06, 0x12, 0x22, 0x0a, 0x1e, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x52, 0x41, 0x53, 0x48, 0x5f, 0x49, 0x4e,
	0x44, 0x49, 0x43, 0x41, 0x54, 0x4f, 0x52, 0x10, 0x07, 0x12, 0x22, 0x0a, 0x1e, 0x42, 0x41, 0x53,
	0x49, 0x43, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x49, 0x4e, 0x53, 0x55,
	0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x08, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_fmcsa_data_provider_inspections_proto_rawDescOnce sync.Once
	file_fmcsa_data_provider_inspections_proto_rawDescData = file_fmcsa_data_provider_inspections_proto_rawDesc
)

func file_fmcsa_data_provider_inspections_proto_rawDescGZIP() []byte {
	file_fmcsa_data_provider_inspections_proto_rawDescOnce.Do(func() {
		file_fmcsa_data_provider_inspections_proto_rawDescData = protoimpl.X.CompressGZIP(file_fmcsa_data_provider_inspections_proto_rawDescData)
	})
	return file_fmcsa_data_provider_inspections_proto_rawDescData
}

var file_fmcsa_data_provider_inspections_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_fmcsa_data_provider_inspections_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_fmcsa_data_provider_inspections_proto_goTypes = []interface{}{
	(InspectionLevelEnum)(0),         // 0: fmcsa_data_provider.inspection_level_enum
	(BasicCategory)(0),               // 1: fmcsa_data_provider.BasicCategory
	(*InspectionIdentification)(nil), // 2: fmcsa_data_provider.InspectionIdentification
	(*InspectionTimestamps)(nil),     // 3: fmcsa_data_provider.InspectionTimestamps
	(*LocationInfo)(nil),             // 4: fmcsa_data_provider.LocationInfo
	(*EnforcementInfo)(nil),          // 5: fmcsa_data_provider.EnforcementInfo
	(*ViolationAggregates)(nil),      // 6: fmcsa_data_provider.ViolationAggregates
	(*CarrierInfo)(nil),              // 7: fmcsa_data_provider.CarrierInfo
	(*Inspection)(nil),               // 8: fmcsa_data_provider.Inspection
	(*timestamppb.Timestamp)(nil),    // 9: google.protobuf.Timestamp
}
var file_fmcsa_data_provider_inspections_proto_depIdxs = []int32{
	9,  // 0: fmcsa_data_provider.InspectionTimestamps.inspection_date:type_name -> google.protobuf.Timestamp
	9,  // 1: fmcsa_data_provider.InspectionTimestamps.insp_start_time:type_name -> google.protobuf.Timestamp
	9,  // 2: fmcsa_data_provider.InspectionTimestamps.insp_end_time:type_name -> google.protobuf.Timestamp
	9,  // 3: fmcsa_data_provider.InspectionTimestamps.fmcsa_published_date:type_name -> google.protobuf.Timestamp
	9,  // 4: fmcsa_data_provider.InspectionTimestamps.mcmis_add_date:type_name -> google.protobuf.Timestamp
	9,  // 5: fmcsa_data_provider.InspectionTimestamps.datagov_created_at:type_name -> google.protobuf.Timestamp
	9,  // 6: fmcsa_data_provider.InspectionTimestamps.datagov_deleted_at:type_name -> google.protobuf.Timestamp
	0,  // 7: fmcsa_data_provider.EnforcementInfo.inspection_level:type_name -> fmcsa_data_provider.inspection_level_enum
	2,  // 8: fmcsa_data_provider.Inspection.identification:type_name -> fmcsa_data_provider.InspectionIdentification
	3,  // 9: fmcsa_data_provider.Inspection.inspection_timestamps:type_name -> fmcsa_data_provider.InspectionTimestamps
	4,  // 10: fmcsa_data_provider.Inspection.location_info:type_name -> fmcsa_data_provider.LocationInfo
	5,  // 11: fmcsa_data_provider.Inspection.enforcement_info:type_name -> fmcsa_data_provider.EnforcementInfo
	6,  // 12: fmcsa_data_provider.Inspection.violation_aggregates:type_name -> fmcsa_data_provider.ViolationAggregates
	7,  // 13: fmcsa_data_provider.Inspection.carrier_info:type_name -> fmcsa_data_provider.CarrierInfo
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_fmcsa_data_provider_inspections_proto_init() }
func file_fmcsa_data_provider_inspections_proto_init() {
	if File_fmcsa_data_provider_inspections_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_fmcsa_data_provider_inspections_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InspectionIdentification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_inspections_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InspectionTimestamps); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_inspections_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_inspections_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnforcementInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_inspections_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ViolationAggregates); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_inspections_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarrierInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_fmcsa_data_provider_inspections_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Inspection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_fmcsa_data_provider_inspections_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_fmcsa_data_provider_inspections_proto_goTypes,
		DependencyIndexes: file_fmcsa_data_provider_inspections_proto_depIdxs,
		EnumInfos:         file_fmcsa_data_provider_inspections_proto_enumTypes,
		MessageInfos:      file_fmcsa_data_provider_inspections_proto_msgTypes,
	}.Build()
	File_fmcsa_data_provider_inspections_proto = out.File
	file_fmcsa_data_provider_inspections_proto_rawDesc = nil
	file_fmcsa_data_provider_inspections_proto_goTypes = nil
	file_fmcsa_data_provider_inspections_proto_depIdxs = nil
}
