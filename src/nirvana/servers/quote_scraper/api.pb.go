// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: quote_scraper/api.proto

package quote_scraper

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ScrapeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Submission *Submission `protobuf:"bytes,1,opt,name=submission,proto3" json:"submission,omitempty"`
}

func (x *ScrapeRequest) Reset() {
	*x = ScrapeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScrapeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScrapeRequest) ProtoMessage() {}

func (x *ScrapeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScrapeRequest.ProtoReflect.Descriptor instead.
func (*ScrapeRequest) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{0}
}

func (x *ScrapeRequest) GetSubmission() *Submission {
	if x != nil {
		return x.Submission
	}
	return nil
}

type ScrapeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *ScrapeResponse) Reset() {
	*x = ScrapeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScrapeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScrapeResponse) ProtoMessage() {}

func (x *ScrapeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScrapeResponse.ProtoReflect.Descriptor instead.
func (*ScrapeResponse) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{1}
}

func (x *ScrapeResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type Submission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationId string `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	SubmissionId  string `protobuf:"bytes,2,opt,name=submission_id,json=submissionId,proto3" json:"submission_id,omitempty"`
	// Types that are assignable to Selected:
	//
	//	*Submission_Progressive
	Selected   isSubmission_Selected `protobuf_oneof:"selected"`
	ScrapeType string                `protobuf:"bytes,4,opt,name=scrape_type,json=scrapeType,proto3" json:"scrape_type,omitempty"`
}

func (x *Submission) Reset() {
	*x = Submission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Submission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Submission) ProtoMessage() {}

func (x *Submission) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Submission.ProtoReflect.Descriptor instead.
func (*Submission) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{2}
}

func (x *Submission) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *Submission) GetSubmissionId() string {
	if x != nil {
		return x.SubmissionId
	}
	return ""
}

func (m *Submission) GetSelected() isSubmission_Selected {
	if m != nil {
		return m.Selected
	}
	return nil
}

func (x *Submission) GetProgressive() *ProgressiveSubmission {
	if x, ok := x.GetSelected().(*Submission_Progressive); ok {
		return x.Progressive
	}
	return nil
}

func (x *Submission) GetScrapeType() string {
	if x != nil {
		return x.ScrapeType
	}
	return ""
}

type isSubmission_Selected interface {
	isSubmission_Selected()
}

type Submission_Progressive struct {
	Progressive *ProgressiveSubmission `protobuf:"bytes,3,opt,name=progressive,proto3,oneof"`
}

func (*Submission_Progressive) isSubmission_Selected() {}

type ProgressiveSubmission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start    *Start           `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	Drivers  []*DriverDetails `protobuf:"bytes,2,rep,name=drivers,proto3" json:"drivers,omitempty"`
	Vehicles []*Vehicle       `protobuf:"bytes,3,rep,name=vehicles,proto3" json:"vehicles,omitempty"`
	Business *Business        `protobuf:"bytes,4,opt,name=business,proto3" json:"business,omitempty"`
	Rates    *Coverages       `protobuf:"bytes,5,opt,name=rates,proto3" json:"rates,omitempty"`
}

func (x *ProgressiveSubmission) Reset() {
	*x = ProgressiveSubmission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressiveSubmission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressiveSubmission) ProtoMessage() {}

func (x *ProgressiveSubmission) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressiveSubmission.ProtoReflect.Descriptor instead.
func (*ProgressiveSubmission) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{3}
}

func (x *ProgressiveSubmission) GetStart() *Start {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *ProgressiveSubmission) GetDrivers() []*DriverDetails {
	if x != nil {
		return x.Drivers
	}
	return nil
}

func (x *ProgressiveSubmission) GetVehicles() []*Vehicle {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

func (x *ProgressiveSubmission) GetBusiness() *Business {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *ProgressiveSubmission) GetRates() *Coverages {
	if x != nil {
		return x.Rates
	}
	return nil
}

type Business struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmailAddress                    string `protobuf:"bytes,1,opt,name=email_address,json=emailAddress,proto3" json:"email_address,omitempty"`
	HaveAutoInsurancePolicy         int32  `protobuf:"varint,2,opt,name=have_auto_insurance_policy,json=haveAutoInsurancePolicy,proto3" json:"have_auto_insurance_policy,omitempty"`
	OtherBusinessInsurance          string `protobuf:"bytes,3,opt,name=other_business_insurance,json=otherBusinessInsurance,proto3" json:"other_business_insurance,omitempty"`
	BodilyInjuryCoverage            string `protobuf:"bytes,4,opt,name=bodily_injury_coverage,json=bodilyInjuryCoverage,proto3" json:"bodily_injury_coverage,omitempty"`
	NumberNamedAdditionalInsured    int32  `protobuf:"varint,5,opt,name=number_named_additional_insured,json=numberNamedAdditionalInsured,proto3" json:"number_named_additional_insured,omitempty"`
	NumberNamedWaiverOfSubro        int32  `protobuf:"varint,6,opt,name=number_named_waiver_of_subro,json=numberNamedWaiverOfSubro,proto3" json:"number_named_waiver_of_subro,omitempty"`
	RequireBlanketAdditionalInsured bool   `protobuf:"varint,7,opt,name=require_blanket_additional_insured,json=requireBlanketAdditionalInsured,proto3" json:"require_blanket_additional_insured,omitempty"`
	RequireBlanketWaiverOfSubro     bool   `protobuf:"varint,8,opt,name=require_blanket_waiver_of_subro,json=requireBlanketWaiverOfSubro,proto3" json:"require_blanket_waiver_of_subro,omitempty"`
	FilingProofOfInsurance          bool   `protobuf:"varint,9,opt,name=filing_proof_of_insurance,json=filingProofOfInsurance,proto3" json:"filing_proof_of_insurance,omitempty"`
	ApplySnapshotPreview            bool   `protobuf:"varint,10,opt,name=apply_snapshot_preview,json=applySnapshotPreview,proto3" json:"apply_snapshot_preview,omitempty"`
	QuoteIncludeAllVehicles         bool   `protobuf:"varint,11,opt,name=quote_include_all_vehicles,json=quoteIncludeAllVehicles,proto3" json:"quote_include_all_vehicles,omitempty"`
	RequiresFederalLiabilityFiling  bool   `protobuf:"varint,12,opt,name=requires_federal_liability_filing,json=requiresFederalLiabilityFiling,proto3" json:"requires_federal_liability_filing,omitempty"`
	RequiresMcs_90                  bool   `protobuf:"varint,13,opt,name=requires_mcs_90,json=requiresMcs90,proto3" json:"requires_mcs_90,omitempty"`
}

func (x *Business) Reset() {
	*x = Business{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Business) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Business) ProtoMessage() {}

func (x *Business) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Business.ProtoReflect.Descriptor instead.
func (*Business) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{4}
}

func (x *Business) GetEmailAddress() string {
	if x != nil {
		return x.EmailAddress
	}
	return ""
}

func (x *Business) GetHaveAutoInsurancePolicy() int32 {
	if x != nil {
		return x.HaveAutoInsurancePolicy
	}
	return 0
}

func (x *Business) GetOtherBusinessInsurance() string {
	if x != nil {
		return x.OtherBusinessInsurance
	}
	return ""
}

func (x *Business) GetBodilyInjuryCoverage() string {
	if x != nil {
		return x.BodilyInjuryCoverage
	}
	return ""
}

func (x *Business) GetNumberNamedAdditionalInsured() int32 {
	if x != nil {
		return x.NumberNamedAdditionalInsured
	}
	return 0
}

func (x *Business) GetNumberNamedWaiverOfSubro() int32 {
	if x != nil {
		return x.NumberNamedWaiverOfSubro
	}
	return 0
}

func (x *Business) GetRequireBlanketAdditionalInsured() bool {
	if x != nil {
		return x.RequireBlanketAdditionalInsured
	}
	return false
}

func (x *Business) GetRequireBlanketWaiverOfSubro() bool {
	if x != nil {
		return x.RequireBlanketWaiverOfSubro
	}
	return false
}

func (x *Business) GetFilingProofOfInsurance() bool {
	if x != nil {
		return x.FilingProofOfInsurance
	}
	return false
}

func (x *Business) GetApplySnapshotPreview() bool {
	if x != nil {
		return x.ApplySnapshotPreview
	}
	return false
}

func (x *Business) GetQuoteIncludeAllVehicles() bool {
	if x != nil {
		return x.QuoteIncludeAllVehicles
	}
	return false
}

func (x *Business) GetRequiresFederalLiabilityFiling() bool {
	if x != nil {
		return x.RequiresFederalLiabilityFiling
	}
	return false
}

func (x *Business) GetRequiresMcs_90() bool {
	if x != nil {
		return x.RequiresMcs_90
	}
	return false
}

type Start struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Zip                    string                 `protobuf:"bytes,1,opt,name=zip,proto3" json:"zip,omitempty"`
	PolicyIncumbent        bool                   `protobuf:"varint,2,opt,name=policy_incumbent,json=policyIncumbent,proto3" json:"policy_incumbent,omitempty"`
	PolicyEffective        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=policy_effective,json=policyEffective,proto3" json:"policy_effective,omitempty"`
	DotNumber              int32                  `protobuf:"varint,4,opt,name=dot_number,json=dotNumber,proto3" json:"dot_number,omitempty"`
	DotBelongToBusiness    bool                   `protobuf:"varint,5,opt,name=dot_belong_to_business,json=dotBelongToBusiness,proto3" json:"dot_belong_to_business,omitempty"`
	BusinessStructure      string                 `protobuf:"bytes,6,opt,name=business_structure,json=businessStructure,proto3" json:"business_structure,omitempty"`
	BusinessType           string                 `protobuf:"bytes,7,opt,name=business_type,json=businessType,proto3" json:"business_type,omitempty"`
	TypeOfTrucker          string                 `protobuf:"bytes,8,opt,name=type_of_trucker,json=typeOfTrucker,proto3" json:"type_of_trucker,omitempty"`
	BusinessName           string                 `protobuf:"bytes,9,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	OwnerFirstName         string                 `protobuf:"bytes,10,opt,name=owner_first_name,json=ownerFirstName,proto3" json:"owner_first_name,omitempty"`
	OwnerLastName          string                 `protobuf:"bytes,11,opt,name=owner_last_name,json=ownerLastName,proto3" json:"owner_last_name,omitempty"`
	UseHomeAddressForOwner bool                   `protobuf:"varint,12,opt,name=use_home_address_for_owner,json=useHomeAddressForOwner,proto3" json:"use_home_address_for_owner,omitempty"`
	OwnerAddressCity       string                 `protobuf:"bytes,13,opt,name=owner_address_city,json=ownerAddressCity,proto3" json:"owner_address_city,omitempty"`
	OwnerAddressStreet     string                 `protobuf:"bytes,14,opt,name=owner_address_street,json=ownerAddressStreet,proto3" json:"owner_address_street,omitempty"`
	OwnerAddressZip        string                 `protobuf:"bytes,15,opt,name=owner_address_zip,json=ownerAddressZip,proto3" json:"owner_address_zip,omitempty"`
	OwnerDob               *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=owner_dob,json=ownerDob,proto3" json:"owner_dob,omitempty"`
	OwnerPhoneNumber       string                 `protobuf:"bytes,17,opt,name=owner_phone_number,json=ownerPhoneNumber,proto3" json:"owner_phone_number,omitempty"`
	HasHazMaterial         bool                   `protobuf:"varint,18,opt,name=has_haz_material,json=hasHazMaterial,proto3" json:"has_haz_material,omitempty"`
}

func (x *Start) Reset() {
	*x = Start{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Start) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Start) ProtoMessage() {}

func (x *Start) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Start.ProtoReflect.Descriptor instead.
func (*Start) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{5}
}

func (x *Start) GetZip() string {
	if x != nil {
		return x.Zip
	}
	return ""
}

func (x *Start) GetPolicyIncumbent() bool {
	if x != nil {
		return x.PolicyIncumbent
	}
	return false
}

func (x *Start) GetPolicyEffective() *timestamppb.Timestamp {
	if x != nil {
		return x.PolicyEffective
	}
	return nil
}

func (x *Start) GetDotNumber() int32 {
	if x != nil {
		return x.DotNumber
	}
	return 0
}

func (x *Start) GetDotBelongToBusiness() bool {
	if x != nil {
		return x.DotBelongToBusiness
	}
	return false
}

func (x *Start) GetBusinessStructure() string {
	if x != nil {
		return x.BusinessStructure
	}
	return ""
}

func (x *Start) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *Start) GetTypeOfTrucker() string {
	if x != nil {
		return x.TypeOfTrucker
	}
	return ""
}

func (x *Start) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *Start) GetOwnerFirstName() string {
	if x != nil {
		return x.OwnerFirstName
	}
	return ""
}

func (x *Start) GetOwnerLastName() string {
	if x != nil {
		return x.OwnerLastName
	}
	return ""
}

func (x *Start) GetUseHomeAddressForOwner() bool {
	if x != nil {
		return x.UseHomeAddressForOwner
	}
	return false
}

func (x *Start) GetOwnerAddressCity() string {
	if x != nil {
		return x.OwnerAddressCity
	}
	return ""
}

func (x *Start) GetOwnerAddressStreet() string {
	if x != nil {
		return x.OwnerAddressStreet
	}
	return ""
}

func (x *Start) GetOwnerAddressZip() string {
	if x != nil {
		return x.OwnerAddressZip
	}
	return ""
}

func (x *Start) GetOwnerDob() *timestamppb.Timestamp {
	if x != nil {
		return x.OwnerDob
	}
	return nil
}

func (x *Start) GetOwnerPhoneNumber() string {
	if x != nil {
		return x.OwnerPhoneNumber
	}
	return ""
}

func (x *Start) GetHasHazMaterial() bool {
	if x != nil {
		return x.HasHazMaterial
	}
	return false
}

type Vehicle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                          string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Class                         string `protobuf:"bytes,2,opt,name=class,proto3" json:"class,omitempty"`
	AddVehicleBy                  string `protobuf:"bytes,3,opt,name=add_vehicle_by,json=addVehicleBy,proto3" json:"add_vehicle_by,omitempty"`
	Vin                           string `protobuf:"bytes,4,opt,name=vin,proto3" json:"vin,omitempty"`
	Zip                           string `protobuf:"bytes,5,opt,name=zip,proto3" json:"zip,omitempty"`
	FarthestRadiusOfOperation     int32  `protobuf:"varint,6,opt,name=farthest_radius_of_operation,json=farthestRadiusOfOperation,proto3" json:"farthest_radius_of_operation,omitempty"`
	PercentageVehicleRepossession int32  `protobuf:"varint,7,opt,name=percentage_vehicle_repossession,json=percentageVehicleRepossession,proto3" json:"percentage_vehicle_repossession,omitempty"`
	VehicleGrossWeight            string `protobuf:"bytes,8,opt,name=vehicle_gross_weight,json=vehicleGrossWeight,proto3" json:"vehicle_gross_weight,omitempty"`
	HasLoan                       bool   `protobuf:"varint,9,opt,name=has_loan,json=hasLoan,proto3" json:"has_loan,omitempty"`
	RequiredApd                   bool   `protobuf:"varint,10,opt,name=required_apd,json=requiredApd,proto3" json:"required_apd,omitempty"`
	VehicleHasNoEquipment         bool   `protobuf:"varint,11,opt,name=vehicle_has_no_equipment,json=vehicleHasNoEquipment,proto3" json:"vehicle_has_no_equipment,omitempty"`
	StatedValue                   int32  `protobuf:"varint,12,opt,name=stated_value,json=statedValue,proto3" json:"stated_value,omitempty"`
}

func (x *Vehicle) Reset() {
	*x = Vehicle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Vehicle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vehicle) ProtoMessage() {}

func (x *Vehicle) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vehicle.ProtoReflect.Descriptor instead.
func (*Vehicle) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{6}
}

func (x *Vehicle) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Vehicle) GetClass() string {
	if x != nil {
		return x.Class
	}
	return ""
}

func (x *Vehicle) GetAddVehicleBy() string {
	if x != nil {
		return x.AddVehicleBy
	}
	return ""
}

func (x *Vehicle) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *Vehicle) GetZip() string {
	if x != nil {
		return x.Zip
	}
	return ""
}

func (x *Vehicle) GetFarthestRadiusOfOperation() int32 {
	if x != nil {
		return x.FarthestRadiusOfOperation
	}
	return 0
}

func (x *Vehicle) GetPercentageVehicleRepossession() int32 {
	if x != nil {
		return x.PercentageVehicleRepossession
	}
	return 0
}

func (x *Vehicle) GetVehicleGrossWeight() string {
	if x != nil {
		return x.VehicleGrossWeight
	}
	return ""
}

func (x *Vehicle) GetHasLoan() bool {
	if x != nil {
		return x.HasLoan
	}
	return false
}

func (x *Vehicle) GetRequiredApd() bool {
	if x != nil {
		return x.RequiredApd
	}
	return false
}

func (x *Vehicle) GetVehicleHasNoEquipment() bool {
	if x != nil {
		return x.VehicleHasNoEquipment
	}
	return false
}

func (x *Vehicle) GetStatedValue() int32 {
	if x != nil {
		return x.StatedValue
	}
	return 0
}

type DriverDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstName         string                 `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName          string                 `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Dob               *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=dob,proto3" json:"dob,omitempty"`
	Cdl               string                 `protobuf:"bytes,4,opt,name=cdl,proto3" json:"cdl,omitempty"`
	ExcludeFromPolicy bool                   `protobuf:"varint,5,opt,name=exclude_from_policy,json=excludeFromPolicy,proto3" json:"exclude_from_policy,omitempty"`
	LicenseState      string                 `protobuf:"bytes,6,opt,name=license_state,json=licenseState,proto3" json:"license_state,omitempty"`
	Violations        []*Violation           `protobuf:"bytes,7,rep,name=violations,proto3" json:"violations,omitempty"`
	HasViolations     bool                   `protobuf:"varint,8,opt,name=has_violations,json=hasViolations,proto3" json:"has_violations,omitempty"`
}

func (x *DriverDetails) Reset() {
	*x = DriverDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DriverDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverDetails) ProtoMessage() {}

func (x *DriverDetails) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverDetails.ProtoReflect.Descriptor instead.
func (*DriverDetails) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{7}
}

func (x *DriverDetails) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *DriverDetails) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *DriverDetails) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *DriverDetails) GetCdl() string {
	if x != nil {
		return x.Cdl
	}
	return ""
}

func (x *DriverDetails) GetExcludeFromPolicy() bool {
	if x != nil {
		return x.ExcludeFromPolicy
	}
	return false
}

func (x *DriverDetails) GetLicenseState() string {
	if x != nil {
		return x.LicenseState
	}
	return ""
}

func (x *DriverDetails) GetViolations() []*Violation {
	if x != nil {
		return x.Violations
	}
	return nil
}

func (x *DriverDetails) GetHasViolations() bool {
	if x != nil {
		return x.HasViolations
	}
	return false
}

type Coverages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AutoLiabilityInfo *AutoLiability `protobuf:"bytes,1,opt,name=auto_liability_info,json=autoLiabilityInfo,proto3" json:"auto_liability_info,omitempty"`
}

func (x *Coverages) Reset() {
	*x = Coverages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Coverages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Coverages) ProtoMessage() {}

func (x *Coverages) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Coverages.ProtoReflect.Descriptor instead.
func (*Coverages) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{8}
}

func (x *Coverages) GetAutoLiabilityInfo() *AutoLiability {
	if x != nil {
		return x.AutoLiabilityInfo
	}
	return nil
}

type AutoLiability struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BiAndPdLimit    int32 `protobuf:"varint,1,opt,name=bi_and_pd_limit,json=biAndPdLimit,proto3" json:"bi_and_pd_limit,omitempty"`
	UmAndUimBiLimit int32 `protobuf:"varint,2,opt,name=um_and_uim_bi_limit,json=umAndUimBiLimit,proto3" json:"um_and_uim_bi_limit,omitempty"`
	PipDeductible   int32 `protobuf:"varint,3,opt,name=pip_deductible,json=pipDeductible,proto3" json:"pip_deductible,omitempty"`
}

func (x *AutoLiability) Reset() {
	*x = AutoLiability{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoLiability) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoLiability) ProtoMessage() {}

func (x *AutoLiability) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoLiability.ProtoReflect.Descriptor instead.
func (*AutoLiability) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{9}
}

func (x *AutoLiability) GetBiAndPdLimit() int32 {
	if x != nil {
		return x.BiAndPdLimit
	}
	return 0
}

func (x *AutoLiability) GetUmAndUimBiLimit() int32 {
	if x != nil {
		return x.UmAndUimBiLimit
	}
	return 0
}

func (x *AutoLiability) GetPipDeductible() int32 {
	if x != nil {
		return x.PipDeductible
	}
	return 0
}

type Violation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Points      int32  `protobuf:"varint,1,opt,name=points,proto3" json:"points,omitempty"`
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *Violation) Reset() {
	*x = Violation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Violation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Violation) ProtoMessage() {}

func (x *Violation) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Violation.ProtoReflect.Descriptor instead.
func (*Violation) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{10}
}

func (x *Violation) GetPoints() int32 {
	if x != nil {
		return x.Points
	}
	return 0
}

func (x *Violation) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type PollScrapeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *PollScrapeRequest) Reset() {
	*x = PollScrapeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollScrapeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollScrapeRequest) ProtoMessage() {}

func (x *PollScrapeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollScrapeRequest.ProtoReflect.Descriptor instead.
func (*PollScrapeRequest) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{11}
}

func (x *PollScrapeRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type PollScrapeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	S3Link      string `protobuf:"bytes,1,opt,name=s3_link,json=s3Link,proto3" json:"s3_link,omitempty"`
	Quote       []byte `protobuf:"bytes,2,opt,name=quote,proto3" json:"quote,omitempty"`
	Status      string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	RetryCount  int32  `protobuf:"varint,4,opt,name=retry_count,json=retryCount,proto3" json:"retry_count,omitempty"`
	ScrapeError *int32 `protobuf:"varint,5,opt,name=scrape_error,json=scrapeError,proto3,oneof" json:"scrape_error,omitempty"`
}

func (x *PollScrapeResponse) Reset() {
	*x = PollScrapeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_quote_scraper_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollScrapeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollScrapeResponse) ProtoMessage() {}

func (x *PollScrapeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_quote_scraper_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollScrapeResponse.ProtoReflect.Descriptor instead.
func (*PollScrapeResponse) Descriptor() ([]byte, []int) {
	return file_quote_scraper_api_proto_rawDescGZIP(), []int{12}
}

func (x *PollScrapeResponse) GetS3Link() string {
	if x != nil {
		return x.S3Link
	}
	return ""
}

func (x *PollScrapeResponse) GetQuote() []byte {
	if x != nil {
		return x.Quote
	}
	return nil
}

func (x *PollScrapeResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PollScrapeResponse) GetRetryCount() int32 {
	if x != nil {
		return x.RetryCount
	}
	return 0
}

func (x *PollScrapeResponse) GetScrapeError() int32 {
	if x != nil && x.ScrapeError != nil {
		return *x.ScrapeError
	}
	return 0
}

var File_quote_scraper_api_proto protoreflect.FileDescriptor

var file_quote_scraper_api_proto_rawDesc = []byte{
	0x0a, 0x17, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x2f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x71, 0x75, 0x6f, 0x74, 0x65,
	0x5f, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4a, 0x0a, 0x0d, 0x53, 0x63, 0x72,
	0x61, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x75,
	0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x2e, 0x53,
	0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x2f, 0x0a, 0x0e, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xcf, 0x01, 0x0a, 0x0a, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x48, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x76, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x73,
	0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x76, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0b,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x76, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x63, 0x72, 0x61, 0x70, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0x0a, 0x08,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0x94, 0x02, 0x0a, 0x15, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x76, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65,
	0x72, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x36,
	0x0a, 0x07, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x2e,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x73, 0x12, 0x32, 0x0a, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x71, 0x75, 0x6f, 0x74, 0x65,
	0x5f, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x52, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x08, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x71,
	0x75, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12,
	0x2e, 0x0a, 0x05, 0x72, 0x61, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x2e, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x52, 0x05, 0x72, 0x61, 0x74, 0x65, 0x73, 0x22,
	0x97, 0x06, 0x0a, 0x08, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x68, 0x61, 0x76, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x17, 0x68, 0x61, 0x76, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x49,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x38,
	0x0a, 0x18, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x16, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x62, 0x6f, 0x64, 0x69,
	0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x6a, 0x75, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x62, 0x6f, 0x64, 0x69, 0x6c, 0x79,
	0x49, 0x6e, 0x6a, 0x75, 0x72, 0x79, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x12, 0x45,
	0x0a, 0x1f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x5f, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x73, 0x75, 0x72, 0x65, 0x64, 0x12, 0x3e, 0x0a, 0x1c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x64, 0x5f, 0x77, 0x61, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f,
	0x73, 0x75, 0x62, 0x72, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x57, 0x61, 0x69, 0x76, 0x65, 0x72, 0x4f, 0x66,
	0x53, 0x75, 0x62, 0x72, 0x6f, 0x12, 0x4b, 0x0a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x5f, 0x62, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x1f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x65,
	0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x65, 0x64, 0x12, 0x44, 0x0a, 0x1f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x62, 0x6c,
	0x61, 0x6e, 0x6b, 0x65, 0x74, 0x5f, 0x77, 0x61, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f,
	0x73, 0x75, 0x62, 0x72, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x65, 0x74, 0x57, 0x61, 0x69, 0x76, 0x65,
	0x72, 0x4f, 0x66, 0x53, 0x75, 0x62, 0x72, 0x6f, 0x12, 0x39, 0x0a, 0x19, 0x66, 0x69, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x5f, 0x6f, 0x66, 0x5f, 0x69, 0x6e, 0x73, 0x75,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x66, 0x69, 0x6c,
	0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x4f, 0x66, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x73, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x3b, 0x0a, 0x1a, 0x71, 0x75, 0x6f,
	0x74, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x71,
	0x75, 0x6f, 0x74, 0x65, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x41, 0x6c, 0x6c, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x21, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x73, 0x5f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x66, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x1e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x46, 0x65, 0x64, 0x65, 0x72,
	0x61, 0x6c, 0x4c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x69, 0x6e,
	0x67, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x6d, 0x63,
	0x73, 0x5f, 0x39, 0x30, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x73, 0x4d, 0x63, 0x73, 0x39, 0x30, 0x22, 0xab, 0x06, 0x0a, 0x05, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x7a, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x7a, 0x69, 0x70, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f,
	0x69, 0x6e, 0x63, 0x75, 0x6d, 0x62, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x63, 0x75, 0x6d, 0x62, 0x65, 0x6e, 0x74,
	0x12, 0x45, 0x0a, 0x10, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x65, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x45, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64, 0x6f, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x16, 0x64, 0x6f, 0x74, 0x5f, 0x62, 0x65,
	0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x74, 0x6f, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x64, 0x6f, 0x74, 0x42, 0x65, 0x6c, 0x6f, 0x6e,
	0x67, 0x54, 0x6f, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x2d, 0x0a, 0x12, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x26, 0x0a, 0x0f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x74, 0x72, 0x75, 0x63, 0x6b,
	0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x79, 0x70, 0x65, 0x4f, 0x66,
	0x54, 0x72, 0x75, 0x63, 0x6b, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x46, 0x69, 0x72,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a,
	0x0a, 0x1a, 0x75, 0x73, 0x65, 0x5f, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x16, 0x75, 0x73, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x46, 0x6f, 0x72, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x69, 0x74, 0x79,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x43, 0x69, 0x74, 0x79, 0x12, 0x30, 0x0a, 0x14, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x65, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x72, 0x65, 0x65, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x7a, 0x69, 0x70, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5a, 0x69, 0x70, 0x12, 0x37, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x64, 0x6f, 0x62, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x44, 0x6f, 0x62, 0x12,
	0x2c, 0x0a, 0x12, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x0a,
	0x10, 0x68, 0x61, 0x73, 0x5f, 0x68, 0x61, 0x7a, 0x5f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x68, 0x61, 0x73, 0x48, 0x61, 0x7a, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x22, 0xd2, 0x03, 0x0a, 0x07, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x24, 0x0a,
	0x0e, 0x61, 0x64, 0x64, 0x5f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x62, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x42, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x76, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x7a, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x7a, 0x69, 0x70, 0x12, 0x3f, 0x0a, 0x1c, 0x66, 0x61, 0x72, 0x74, 0x68,
	0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x66,
	0x61, 0x72, 0x74, 0x68, 0x65, 0x73, 0x74, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x4f, 0x66, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x1f, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x72,
	0x65, 0x70, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x1d, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x30, 0x0a, 0x14, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x73,
	0x73, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x73, 0x73, 0x57, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4c, 0x6f, 0x61, 0x6e, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x41, 0x70, 0x64,
	0x12, 0x37, 0x0a, 0x18, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x68, 0x61, 0x73, 0x5f,
	0x6e, 0x6f, 0x5f, 0x65, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x15, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x48, 0x61, 0x73, 0x4e, 0x6f,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xc1, 0x02, 0x0a,
	0x0d, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x03, 0x64, 0x6f,
	0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x64, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x64, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x46, 0x72, 0x6f, 0x6d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69,
	0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x38, 0x0a, 0x0a, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x61,
	0x70, 0x65, 0x72, 0x2e, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x76,
	0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x68, 0x61, 0x73,
	0x5f, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x68, 0x61, 0x73, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x59, 0x0a, 0x09, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x12, 0x4c, 0x0a,
	0x13, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x6f,
	0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x4c,
	0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x4c, 0x69,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x8b, 0x01, 0x0a, 0x0d,
	0x41, 0x75, 0x74, 0x6f, 0x4c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x25, 0x0a,
	0x0f, 0x62, 0x69, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x70, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x62, 0x69, 0x41, 0x6e, 0x64, 0x50, 0x64, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x2c, 0x0a, 0x13, 0x75, 0x6d, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x75,
	0x69, 0x6d, 0x5f, 0x62, 0x69, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0f, 0x75, 0x6d, 0x41, 0x6e, 0x64, 0x55, 0x69, 0x6d, 0x42, 0x69, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x69, 0x70, 0x5f, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74,
	0x69, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x69, 0x70, 0x44,
	0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x22, 0x45, 0x0a, 0x09, 0x56, 0x69, 0x6f,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x32, 0x0a, 0x11, 0x50, 0x6f, 0x6c, 0x6c, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x22, 0xb5, 0x01, 0x0a, 0x12, 0x50, 0x6f, 0x6c, 0x6c, 0x53, 0x63, 0x72,
	0x61, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73,
	0x33, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x33,
	0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x63, 0x72,
	0x61, 0x70, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f,
	0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x32, 0xbf, 0x01, 0x0a,
	0x0c, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x12, 0x52, 0x0a,
	0x13, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x2e, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72,
	0x61, 0x70, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x61, 0x70,
	0x65, 0x72, 0x2e, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x5b, 0x0a, 0x14, 0x50, 0x6f, 0x6c, 0x6c, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x53,
	0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x2e, 0x71, 0x75, 0x6f, 0x74,
	0x65, 0x5f, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6c, 0x6c, 0x53, 0x63,
	0x72, 0x61, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x71, 0x75,
	0x6f, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x6c, 0x6c,
	0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_quote_scraper_api_proto_rawDescOnce sync.Once
	file_quote_scraper_api_proto_rawDescData = file_quote_scraper_api_proto_rawDesc
)

func file_quote_scraper_api_proto_rawDescGZIP() []byte {
	file_quote_scraper_api_proto_rawDescOnce.Do(func() {
		file_quote_scraper_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_quote_scraper_api_proto_rawDescData)
	})
	return file_quote_scraper_api_proto_rawDescData
}

var file_quote_scraper_api_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_quote_scraper_api_proto_goTypes = []interface{}{
	(*ScrapeRequest)(nil),         // 0: quote_scraper.ScrapeRequest
	(*ScrapeResponse)(nil),        // 1: quote_scraper.ScrapeResponse
	(*Submission)(nil),            // 2: quote_scraper.Submission
	(*ProgressiveSubmission)(nil), // 3: quote_scraper.ProgressiveSubmission
	(*Business)(nil),              // 4: quote_scraper.Business
	(*Start)(nil),                 // 5: quote_scraper.Start
	(*Vehicle)(nil),               // 6: quote_scraper.Vehicle
	(*DriverDetails)(nil),         // 7: quote_scraper.DriverDetails
	(*Coverages)(nil),             // 8: quote_scraper.Coverages
	(*AutoLiability)(nil),         // 9: quote_scraper.AutoLiability
	(*Violation)(nil),             // 10: quote_scraper.Violation
	(*PollScrapeRequest)(nil),     // 11: quote_scraper.PollScrapeRequest
	(*PollScrapeResponse)(nil),    // 12: quote_scraper.PollScrapeResponse
	(*timestamppb.Timestamp)(nil), // 13: google.protobuf.Timestamp
}
var file_quote_scraper_api_proto_depIdxs = []int32{
	2,  // 0: quote_scraper.ScrapeRequest.submission:type_name -> quote_scraper.Submission
	3,  // 1: quote_scraper.Submission.progressive:type_name -> quote_scraper.ProgressiveSubmission
	5,  // 2: quote_scraper.ProgressiveSubmission.start:type_name -> quote_scraper.Start
	7,  // 3: quote_scraper.ProgressiveSubmission.drivers:type_name -> quote_scraper.DriverDetails
	6,  // 4: quote_scraper.ProgressiveSubmission.vehicles:type_name -> quote_scraper.Vehicle
	4,  // 5: quote_scraper.ProgressiveSubmission.business:type_name -> quote_scraper.Business
	8,  // 6: quote_scraper.ProgressiveSubmission.rates:type_name -> quote_scraper.Coverages
	13, // 7: quote_scraper.Start.policy_effective:type_name -> google.protobuf.Timestamp
	13, // 8: quote_scraper.Start.owner_dob:type_name -> google.protobuf.Timestamp
	13, // 9: quote_scraper.DriverDetails.dob:type_name -> google.protobuf.Timestamp
	10, // 10: quote_scraper.DriverDetails.violations:type_name -> quote_scraper.Violation
	9,  // 11: quote_scraper.Coverages.auto_liability_info:type_name -> quote_scraper.AutoLiability
	0,  // 12: quote_scraper.QuoteScraper.SubmitScrapeRequest:input_type -> quote_scraper.ScrapeRequest
	11, // 13: quote_scraper.QuoteScraper.PollScrapeSubmission:input_type -> quote_scraper.PollScrapeRequest
	1,  // 14: quote_scraper.QuoteScraper.SubmitScrapeRequest:output_type -> quote_scraper.ScrapeResponse
	12, // 15: quote_scraper.QuoteScraper.PollScrapeSubmission:output_type -> quote_scraper.PollScrapeResponse
	14, // [14:16] is the sub-list for method output_type
	12, // [12:14] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_quote_scraper_api_proto_init() }
func file_quote_scraper_api_proto_init() {
	if File_quote_scraper_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_quote_scraper_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScrapeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScrapeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Submission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgressiveSubmission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Business); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Start); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Vehicle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DriverDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Coverages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoLiability); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Violation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollScrapeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_quote_scraper_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollScrapeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_quote_scraper_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*Submission_Progressive)(nil),
	}
	file_quote_scraper_api_proto_msgTypes[12].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_quote_scraper_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_quote_scraper_api_proto_goTypes,
		DependencyIndexes: file_quote_scraper_api_proto_depIdxs,
		MessageInfos:      file_quote_scraper_api_proto_msgTypes,
	}.Build()
	File_quote_scraper_api_proto = out.File
	file_quote_scraper_api_proto_rawDesc = nil
	file_quote_scraper_api_proto_goTypes = nil
	file_quote_scraper_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// QuoteScraperClient is the client API for QuoteScraper service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type QuoteScraperClient interface {
	SubmitScrapeRequest(ctx context.Context, in *ScrapeRequest, opts ...grpc.CallOption) (*ScrapeResponse, error)
	PollScrapeSubmission(ctx context.Context, in *PollScrapeRequest, opts ...grpc.CallOption) (*PollScrapeResponse, error)
}

type quoteScraperClient struct {
	cc grpc.ClientConnInterface
}

func NewQuoteScraperClient(cc grpc.ClientConnInterface) QuoteScraperClient {
	return &quoteScraperClient{cc}
}

func (c *quoteScraperClient) SubmitScrapeRequest(ctx context.Context, in *ScrapeRequest, opts ...grpc.CallOption) (*ScrapeResponse, error) {
	out := new(ScrapeResponse)
	err := c.cc.Invoke(ctx, "/quote_scraper.QuoteScraper/SubmitScrapeRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *quoteScraperClient) PollScrapeSubmission(ctx context.Context, in *PollScrapeRequest, opts ...grpc.CallOption) (*PollScrapeResponse, error) {
	out := new(PollScrapeResponse)
	err := c.cc.Invoke(ctx, "/quote_scraper.QuoteScraper/PollScrapeSubmission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// QuoteScraperServer is the server API for QuoteScraper service.
type QuoteScraperServer interface {
	SubmitScrapeRequest(context.Context, *ScrapeRequest) (*ScrapeResponse, error)
	PollScrapeSubmission(context.Context, *PollScrapeRequest) (*PollScrapeResponse, error)
}

// UnimplementedQuoteScraperServer can be embedded to have forward compatible implementations.
type UnimplementedQuoteScraperServer struct {
}

func (*UnimplementedQuoteScraperServer) SubmitScrapeRequest(context.Context, *ScrapeRequest) (*ScrapeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitScrapeRequest not implemented")
}
func (*UnimplementedQuoteScraperServer) PollScrapeSubmission(context.Context, *PollScrapeRequest) (*PollScrapeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PollScrapeSubmission not implemented")
}

func RegisterQuoteScraperServer(s *grpc.Server, srv QuoteScraperServer) {
	s.RegisterService(&_QuoteScraper_serviceDesc, srv)
}

func _QuoteScraper_SubmitScrapeRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScrapeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuoteScraperServer).SubmitScrapeRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/quote_scraper.QuoteScraper/SubmitScrapeRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuoteScraperServer).SubmitScrapeRequest(ctx, req.(*ScrapeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _QuoteScraper_PollScrapeSubmission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PollScrapeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuoteScraperServer).PollScrapeSubmission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/quote_scraper.QuoteScraper/PollScrapeSubmission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuoteScraperServer).PollScrapeSubmission(ctx, req.(*PollScrapeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _QuoteScraper_serviceDesc = grpc.ServiceDesc{
	ServiceName: "quote_scraper.QuoteScraper",
	HandlerType: (*QuoteScraperServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubmitScrapeRequest",
			Handler:    _QuoteScraper_SubmitScrapeRequest_Handler,
		},
		{
			MethodName: "PollScrapeSubmission",
			Handler:    _QuoteScraper_PollScrapeSubmission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "quote_scraper/api.proto",
}
