// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: vehicles_service/proto_generated.proto

package vehicles

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AgencyNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key  *AgencyNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	Name *string                `protobuf:"bytes,2,opt,name=Name,proto3,oneof" json:"Name,omitempty"`
}

func (x *AgencyNode) Reset() {
	*x = AgencyNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgencyNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgencyNode) ProtoMessage() {}

func (x *AgencyNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgencyNode.ProtoReflect.Descriptor instead.
func (*AgencyNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{0}
}

func (x *AgencyNode) GetKey() *AgencyNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *AgencyNode) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type FleetNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key       *FleetNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	Name      *string               `protobuf:"bytes,2,opt,name=Name,proto3,oneof" json:"Name,omitempty"`
	DOTNumber int64                 `protobuf:"varint,3,opt,name=DOTNumber,proto3" json:"DOTNumber,omitempty"`
}

func (x *FleetNode) Reset() {
	*x = FleetNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetNode) ProtoMessage() {}

func (x *FleetNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetNode.ProtoReflect.Descriptor instead.
func (*FleetNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{1}
}

func (x *FleetNode) GetKey() *FleetNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *FleetNode) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *FleetNode) GetDOTNumber() int64 {
	if x != nil {
		return x.DOTNumber
	}
	return 0
}

type ApplicationNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key      *ApplicationNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	AgencyID string                      `protobuf:"bytes,2,opt,name=AgencyID,proto3" json:"AgencyID,omitempty"`
	FleetID  string                      `protobuf:"bytes,3,opt,name=FleetID,proto3" json:"FleetID,omitempty"`
}

func (x *ApplicationNode) Reset() {
	*x = ApplicationNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicationNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicationNode) ProtoMessage() {}

func (x *ApplicationNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicationNode.ProtoReflect.Descriptor instead.
func (*ApplicationNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{2}
}

func (x *ApplicationNode) GetKey() *ApplicationNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *ApplicationNode) GetAgencyID() string {
	if x != nil {
		return x.AgencyID
	}
	return ""
}

func (x *ApplicationNode) GetFleetID() string {
	if x != nil {
		return x.FleetID
	}
	return ""
}

type EquipmentListNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key           *EquipmentListNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	ApplicationID string                        `protobuf:"bytes,2,opt,name=ApplicationID,proto3" json:"ApplicationID,omitempty"`
}

func (x *EquipmentListNode) Reset() {
	*x = EquipmentListNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentListNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentListNode) ProtoMessage() {}

func (x *EquipmentListNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentListNode.ProtoReflect.Descriptor instead.
func (*EquipmentListNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{3}
}

func (x *EquipmentListNode) GetKey() *EquipmentListNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *EquipmentListNode) GetApplicationID() string {
	if x != nil {
		return x.ApplicationID
	}
	return ""
}

type EquipmentListTimeseriesNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *EquipmentListTimeseriesNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *EquipmentListTimeseriesNode) Reset() {
	*x = EquipmentListTimeseriesNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentListTimeseriesNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentListTimeseriesNode) ProtoMessage() {}

func (x *EquipmentListTimeseriesNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentListTimeseriesNode.ProtoReflect.Descriptor instead.
func (*EquipmentListTimeseriesNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{4}
}

func (x *EquipmentListTimeseriesNode) GetKey() *EquipmentListTimeseriesNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type EquipmentVehicleNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *EquipmentVehicleNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *EquipmentVehicleNode) Reset() {
	*x = EquipmentVehicleNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentVehicleNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentVehicleNode) ProtoMessage() {}

func (x *EquipmentVehicleNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentVehicleNode.ProtoReflect.Descriptor instead.
func (*EquipmentVehicleNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{5}
}

func (x *EquipmentVehicleNode) GetKey() *EquipmentVehicleNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type EquipmentVehicleTimeseriesNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key          *EquipmentVehicleTimeseriesNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	StatedValue  *float64                                   `protobuf:"fixed64,2,opt,name=StatedValue,proto3,oneof" json:"StatedValue,omitempty"`
	Make         *string                                    `protobuf:"bytes,3,opt,name=Make,proto3,oneof" json:"Make,omitempty"`
	Model        *string                                    `protobuf:"bytes,4,opt,name=Model,proto3,oneof" json:"Model,omitempty"`
	Year         *int64                                     `protobuf:"varint,5,opt,name=Year,proto3,oneof" json:"Year,omitempty"`
	Manufacturer *string                                    `protobuf:"bytes,6,opt,name=Manufacturer,proto3,oneof" json:"Manufacturer,omitempty"`
	Trim         *string                                    `protobuf:"bytes,7,opt,name=Trim,proto3,oneof" json:"Trim,omitempty"`
	BodyClass    *string                                    `protobuf:"bytes,8,opt,name=BodyClass,proto3,oneof" json:"BodyClass,omitempty"`
	VehicleType  *string                                    `protobuf:"bytes,9,opt,name=VehicleType,proto3,oneof" json:"VehicleType,omitempty"`
	WeightClass  *string                                    `protobuf:"bytes,10,opt,name=WeightClass,proto3,oneof" json:"WeightClass,omitempty"`
	ShouldIgnore *bool                                      `protobuf:"varint,11,opt,name=ShouldIgnore,proto3,oneof" json:"ShouldIgnore,omitempty"`
}

func (x *EquipmentVehicleTimeseriesNode) Reset() {
	*x = EquipmentVehicleTimeseriesNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentVehicleTimeseriesNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentVehicleTimeseriesNode) ProtoMessage() {}

func (x *EquipmentVehicleTimeseriesNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentVehicleTimeseriesNode.ProtoReflect.Descriptor instead.
func (*EquipmentVehicleTimeseriesNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{6}
}

func (x *EquipmentVehicleTimeseriesNode) GetKey() *EquipmentVehicleTimeseriesNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *EquipmentVehicleTimeseriesNode) GetStatedValue() float64 {
	if x != nil && x.StatedValue != nil {
		return *x.StatedValue
	}
	return 0
}

func (x *EquipmentVehicleTimeseriesNode) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *EquipmentVehicleTimeseriesNode) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *EquipmentVehicleTimeseriesNode) GetYear() int64 {
	if x != nil && x.Year != nil {
		return *x.Year
	}
	return 0
}

func (x *EquipmentVehicleTimeseriesNode) GetManufacturer() string {
	if x != nil && x.Manufacturer != nil {
		return *x.Manufacturer
	}
	return ""
}

func (x *EquipmentVehicleTimeseriesNode) GetTrim() string {
	if x != nil && x.Trim != nil {
		return *x.Trim
	}
	return ""
}

func (x *EquipmentVehicleTimeseriesNode) GetBodyClass() string {
	if x != nil && x.BodyClass != nil {
		return *x.BodyClass
	}
	return ""
}

func (x *EquipmentVehicleTimeseriesNode) GetVehicleType() string {
	if x != nil && x.VehicleType != nil {
		return *x.VehicleType
	}
	return ""
}

func (x *EquipmentVehicleTimeseriesNode) GetWeightClass() string {
	if x != nil && x.WeightClass != nil {
		return *x.WeightClass
	}
	return ""
}

func (x *EquipmentVehicleTimeseriesNode) GetShouldIgnore() bool {
	if x != nil && x.ShouldIgnore != nil {
		return *x.ShouldIgnore
	}
	return false
}

type VINVehicleNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key            *VINVehicleNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	Make           *string                    `protobuf:"bytes,2,opt,name=Make,proto3,oneof" json:"Make,omitempty"`
	Model          *string                    `protobuf:"bytes,3,opt,name=Model,proto3,oneof" json:"Model,omitempty"`
	Year           *int64                     `protobuf:"varint,4,opt,name=Year,proto3,oneof" json:"Year,omitempty"`
	Trim           *string                    `protobuf:"bytes,5,opt,name=Trim,proto3,oneof" json:"Trim,omitempty"`
	RawVehicleType *string                    `protobuf:"bytes,6,opt,name=RawVehicleType,proto3,oneof" json:"RawVehicleType,omitempty"`
	Manufacturer   *string                    `protobuf:"bytes,7,opt,name=Manufacturer,proto3,oneof" json:"Manufacturer,omitempty"`
	BodyClass      *string                    `protobuf:"bytes,8,opt,name=BodyClass,proto3,oneof" json:"BodyClass,omitempty"`
	VehicleType    *string                    `protobuf:"bytes,9,opt,name=VehicleType,proto3,oneof" json:"VehicleType,omitempty"`
	WeightClass    *string                    `protobuf:"bytes,10,opt,name=WeightClass,proto3,oneof" json:"WeightClass,omitempty"`
}

func (x *VINVehicleNode) Reset() {
	*x = VINVehicleNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINVehicleNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINVehicleNode) ProtoMessage() {}

func (x *VINVehicleNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINVehicleNode.ProtoReflect.Descriptor instead.
func (*VINVehicleNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{7}
}

func (x *VINVehicleNode) GetKey() *VINVehicleNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *VINVehicleNode) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *VINVehicleNode) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *VINVehicleNode) GetYear() int64 {
	if x != nil && x.Year != nil {
		return *x.Year
	}
	return 0
}

func (x *VINVehicleNode) GetTrim() string {
	if x != nil && x.Trim != nil {
		return *x.Trim
	}
	return ""
}

func (x *VINVehicleNode) GetRawVehicleType() string {
	if x != nil && x.RawVehicleType != nil {
		return *x.RawVehicleType
	}
	return ""
}

func (x *VINVehicleNode) GetManufacturer() string {
	if x != nil && x.Manufacturer != nil {
		return *x.Manufacturer
	}
	return ""
}

func (x *VINVehicleNode) GetBodyClass() string {
	if x != nil && x.BodyClass != nil {
		return *x.BodyClass
	}
	return ""
}

func (x *VINVehicleNode) GetVehicleType() string {
	if x != nil && x.VehicleType != nil {
		return *x.VehicleType
	}
	return ""
}

func (x *VINVehicleNode) GetWeightClass() string {
	if x != nil && x.WeightClass != nil {
		return *x.WeightClass
	}
	return ""
}

type VINDecoderProblemNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key              *VINDecoderProblemNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	NHTSADecodeError *string                           `protobuf:"bytes,2,opt,name=NHTSADecodeError,proto3,oneof" json:"NHTSADecodeError,omitempty"`
	Errors           *string                           `protobuf:"bytes,3,opt,name=Errors,proto3,oneof" json:"Errors,omitempty"`
}

func (x *VINDecoderProblemNode) Reset() {
	*x = VINDecoderProblemNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDecoderProblemNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDecoderProblemNode) ProtoMessage() {}

func (x *VINDecoderProblemNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDecoderProblemNode.ProtoReflect.Descriptor instead.
func (*VINDecoderProblemNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{8}
}

func (x *VINDecoderProblemNode) GetKey() *VINDecoderProblemNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *VINDecoderProblemNode) GetNHTSADecodeError() string {
	if x != nil && x.NHTSADecodeError != nil {
		return *x.NHTSADecodeError
	}
	return ""
}

func (x *VINDecoderProblemNode) GetErrors() string {
	if x != nil && x.Errors != nil {
		return *x.Errors
	}
	return ""
}

type VINDecoderSolutionNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key                    *VINDecoderSolutionNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	ShouldSkip             *bool                              `protobuf:"varint,2,opt,name=ShouldSkip,proto3,oneof" json:"ShouldSkip,omitempty"`
	IsReviewed             *bool                              `protobuf:"varint,3,opt,name=IsReviewed,proto3,oneof" json:"IsReviewed,omitempty"`
	IsResolved             *bool                              `protobuf:"varint,4,opt,name=IsResolved,proto3,oneof" json:"IsResolved,omitempty"`
	FixedVIN               *string                            `protobuf:"bytes,5,opt,name=FixedVIN,proto3,oneof" json:"FixedVIN,omitempty"`
	Make                   *string                            `protobuf:"bytes,6,opt,name=Make,proto3,oneof" json:"Make,omitempty"`
	Model                  *string                            `protobuf:"bytes,7,opt,name=Model,proto3,oneof" json:"Model,omitempty"`
	Year                   *int64                             `protobuf:"varint,8,opt,name=Year,proto3,oneof" json:"Year,omitempty"`
	BodyClass              *string                            `protobuf:"bytes,9,opt,name=BodyClass,proto3,oneof" json:"BodyClass,omitempty"`
	VehicleType            *string                            `protobuf:"bytes,10,opt,name=VehicleType,proto3,oneof" json:"VehicleType,omitempty"`
	WeightClass            *string                            `protobuf:"bytes,11,opt,name=WeightClass,proto3,oneof" json:"WeightClass,omitempty"`
	ISOVehicleTypeOverride *string                            `protobuf:"bytes,12,opt,name=ISOVehicleTypeOverride,proto3,oneof" json:"ISOVehicleTypeOverride,omitempty"`
	ISOWeightGroupOverride *string                            `protobuf:"bytes,13,opt,name=ISOWeightGroupOverride,proto3,oneof" json:"ISOWeightGroupOverride,omitempty"`
	VehicleStartZone       *string                            `protobuf:"bytes,14,opt,name=VehicleStartZone,proto3,oneof" json:"VehicleStartZone,omitempty"`
	VehicleEndZone         *string                            `protobuf:"bytes,15,opt,name=VehicleEndZone,proto3,oneof" json:"VehicleEndZone,omitempty"`
}

func (x *VINDecoderSolutionNode) Reset() {
	*x = VINDecoderSolutionNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDecoderSolutionNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDecoderSolutionNode) ProtoMessage() {}

func (x *VINDecoderSolutionNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDecoderSolutionNode.ProtoReflect.Descriptor instead.
func (*VINDecoderSolutionNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{9}
}

func (x *VINDecoderSolutionNode) GetKey() *VINDecoderSolutionNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *VINDecoderSolutionNode) GetShouldSkip() bool {
	if x != nil && x.ShouldSkip != nil {
		return *x.ShouldSkip
	}
	return false
}

func (x *VINDecoderSolutionNode) GetIsReviewed() bool {
	if x != nil && x.IsReviewed != nil {
		return *x.IsReviewed
	}
	return false
}

func (x *VINDecoderSolutionNode) GetIsResolved() bool {
	if x != nil && x.IsResolved != nil {
		return *x.IsResolved
	}
	return false
}

func (x *VINDecoderSolutionNode) GetFixedVIN() string {
	if x != nil && x.FixedVIN != nil {
		return *x.FixedVIN
	}
	return ""
}

func (x *VINDecoderSolutionNode) GetMake() string {
	if x != nil && x.Make != nil {
		return *x.Make
	}
	return ""
}

func (x *VINDecoderSolutionNode) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}
	return ""
}

func (x *VINDecoderSolutionNode) GetYear() int64 {
	if x != nil && x.Year != nil {
		return *x.Year
	}
	return 0
}

func (x *VINDecoderSolutionNode) GetBodyClass() string {
	if x != nil && x.BodyClass != nil {
		return *x.BodyClass
	}
	return ""
}

func (x *VINDecoderSolutionNode) GetVehicleType() string {
	if x != nil && x.VehicleType != nil {
		return *x.VehicleType
	}
	return ""
}

func (x *VINDecoderSolutionNode) GetWeightClass() string {
	if x != nil && x.WeightClass != nil {
		return *x.WeightClass
	}
	return ""
}

func (x *VINDecoderSolutionNode) GetISOVehicleTypeOverride() string {
	if x != nil && x.ISOVehicleTypeOverride != nil {
		return *x.ISOVehicleTypeOverride
	}
	return ""
}

func (x *VINDecoderSolutionNode) GetISOWeightGroupOverride() string {
	if x != nil && x.ISOWeightGroupOverride != nil {
		return *x.ISOWeightGroupOverride
	}
	return ""
}

func (x *VINDecoderSolutionNode) GetVehicleStartZone() string {
	if x != nil && x.VehicleStartZone != nil {
		return *x.VehicleStartZone
	}
	return ""
}

func (x *VINDecoderSolutionNode) GetVehicleEndZone() string {
	if x != nil && x.VehicleEndZone != nil {
		return *x.VehicleEndZone
	}
	return ""
}

type TelematicsConnectionNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key            *TelematicsConnectionNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	CreatedAt      *timestamppb.Timestamp               `protobuf:"bytes,2,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty"`
	DeletedAt      *timestamppb.Timestamp               `protobuf:"bytes,3,opt,name=DeletedAt,proto3,oneof" json:"DeletedAt,omitempty"`
	FleetID        string                               `protobuf:"bytes,4,opt,name=FleetID,proto3" json:"FleetID,omitempty"`
	ProviderName   string                               `protobuf:"bytes,5,opt,name=ProviderName,proto3" json:"ProviderName,omitempty"`
	TSPName        string                               `protobuf:"bytes,6,opt,name=TSPName,proto3" json:"TSPName,omitempty"`
	OrganizationID string                               `protobuf:"bytes,7,opt,name=OrganizationID,proto3" json:"OrganizationID,omitempty"`
}

func (x *TelematicsConnectionNode) Reset() {
	*x = TelematicsConnectionNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsConnectionNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsConnectionNode) ProtoMessage() {}

func (x *TelematicsConnectionNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsConnectionNode.ProtoReflect.Descriptor instead.
func (*TelematicsConnectionNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{10}
}

func (x *TelematicsConnectionNode) GetKey() *TelematicsConnectionNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *TelematicsConnectionNode) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TelematicsConnectionNode) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *TelematicsConnectionNode) GetFleetID() string {
	if x != nil {
		return x.FleetID
	}
	return ""
}

func (x *TelematicsConnectionNode) GetProviderName() string {
	if x != nil {
		return x.ProviderName
	}
	return ""
}

func (x *TelematicsConnectionNode) GetTSPName() string {
	if x != nil {
		return x.TSPName
	}
	return ""
}

func (x *TelematicsConnectionNode) GetOrganizationID() string {
	if x != nil {
		return x.OrganizationID
	}
	return ""
}

type TelematicsConnectionSnapshotNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *TelematicsConnectionSnapshotNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *TelematicsConnectionSnapshotNode) Reset() {
	*x = TelematicsConnectionSnapshotNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsConnectionSnapshotNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsConnectionSnapshotNode) ProtoMessage() {}

func (x *TelematicsConnectionSnapshotNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsConnectionSnapshotNode.ProtoReflect.Descriptor instead.
func (*TelematicsConnectionSnapshotNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{11}
}

func (x *TelematicsConnectionSnapshotNode) GetKey() *TelematicsConnectionSnapshotNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type TelematicsTagNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *TelematicsTagNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *TelematicsTagNode) Reset() {
	*x = TelematicsTagNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsTagNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsTagNode) ProtoMessage() {}

func (x *TelematicsTagNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsTagNode.ProtoReflect.Descriptor instead.
func (*TelematicsTagNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{12}
}

func (x *TelematicsTagNode) GetKey() *TelematicsTagNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type TelematicsTagSnapshotNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key  *TelematicsTagSnapshotNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	Name *string                               `protobuf:"bytes,2,opt,name=Name,proto3,oneof" json:"Name,omitempty"`
}

func (x *TelematicsTagSnapshotNode) Reset() {
	*x = TelematicsTagSnapshotNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsTagSnapshotNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsTagSnapshotNode) ProtoMessage() {}

func (x *TelematicsTagSnapshotNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsTagSnapshotNode.ProtoReflect.Descriptor instead.
func (*TelematicsTagSnapshotNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{13}
}

func (x *TelematicsTagSnapshotNode) GetKey() *TelematicsTagSnapshotNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *TelematicsTagSnapshotNode) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type TelematicsVehicleNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *TelematicsVehicleNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *TelematicsVehicleNode) Reset() {
	*x = TelematicsVehicleNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleNode) ProtoMessage() {}

func (x *TelematicsVehicleNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleNode.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{14}
}

func (x *TelematicsVehicleNode) GetKey() *TelematicsVehicleNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type TelematicsVehicleSnapshotNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key          *TelematicsVehicleSnapshotNode_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	VIN          *string                                   `protobuf:"bytes,2,opt,name=VIN,proto3,oneof" json:"VIN,omitempty"`
	CameraSerial *string                                   `protobuf:"bytes,3,opt,name=CameraSerial,proto3,oneof" json:"CameraSerial,omitempty"`
	OdometerGPS  *float64                                  `protobuf:"fixed64,4,opt,name=OdometerGPS,proto3,oneof" json:"OdometerGPS,omitempty"`
	OdometerOBD  *float64                                  `protobuf:"fixed64,5,opt,name=OdometerOBD,proto3,oneof" json:"OdometerOBD,omitempty"`
	DeviceID     *string                                   `protobuf:"bytes,6,opt,name=DeviceID,proto3,oneof" json:"DeviceID,omitempty"`
	IsFakeVIN    *bool                                     `protobuf:"varint,7,opt,name=IsFakeVIN,proto3,oneof" json:"IsFakeVIN,omitempty"`
}

func (x *TelematicsVehicleSnapshotNode) Reset() {
	*x = TelematicsVehicleSnapshotNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleSnapshotNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleSnapshotNode) ProtoMessage() {}

func (x *TelematicsVehicleSnapshotNode) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleSnapshotNode.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleSnapshotNode) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{15}
}

func (x *TelematicsVehicleSnapshotNode) GetKey() *TelematicsVehicleSnapshotNode_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *TelematicsVehicleSnapshotNode) GetVIN() string {
	if x != nil && x.VIN != nil {
		return *x.VIN
	}
	return ""
}

func (x *TelematicsVehicleSnapshotNode) GetCameraSerial() string {
	if x != nil && x.CameraSerial != nil {
		return *x.CameraSerial
	}
	return ""
}

func (x *TelematicsVehicleSnapshotNode) GetOdometerGPS() float64 {
	if x != nil && x.OdometerGPS != nil {
		return *x.OdometerGPS
	}
	return 0
}

func (x *TelematicsVehicleSnapshotNode) GetOdometerOBD() float64 {
	if x != nil && x.OdometerOBD != nil {
		return *x.OdometerOBD
	}
	return 0
}

func (x *TelematicsVehicleSnapshotNode) GetDeviceID() string {
	if x != nil && x.DeviceID != nil {
		return *x.DeviceID
	}
	return ""
}

func (x *TelematicsVehicleSnapshotNode) GetIsFakeVIN() bool {
	if x != nil && x.IsFakeVIN != nil {
		return *x.IsFakeVIN
	}
	return false
}

type Node struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Selected:
	//
	//	*Node_Agency
	//	*Node_Fleet
	//	*Node_Application
	//	*Node_EquipmentList
	//	*Node_EquipmentListTimeseries
	//	*Node_EquipmentVehicle
	//	*Node_EquipmentVehicleTimeseries
	//	*Node_VINVehicle
	//	*Node_VINDecoderProblem
	//	*Node_VINDecoderSolution
	//	*Node_TelematicsConnection
	//	*Node_TelematicsConnectionSnapshot
	//	*Node_TelematicsTag
	//	*Node_TelematicsTagSnapshot
	//	*Node_TelematicsVehicle
	//	*Node_TelematicsVehicleSnapshot
	Selected isNode_Selected `protobuf_oneof:"Selected"`
}

func (x *Node) Reset() {
	*x = Node{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{16}
}

func (m *Node) GetSelected() isNode_Selected {
	if m != nil {
		return m.Selected
	}
	return nil
}

func (x *Node) GetAgency() *AgencyNode {
	if x, ok := x.GetSelected().(*Node_Agency); ok {
		return x.Agency
	}
	return nil
}

func (x *Node) GetFleet() *FleetNode {
	if x, ok := x.GetSelected().(*Node_Fleet); ok {
		return x.Fleet
	}
	return nil
}

func (x *Node) GetApplication() *ApplicationNode {
	if x, ok := x.GetSelected().(*Node_Application); ok {
		return x.Application
	}
	return nil
}

func (x *Node) GetEquipmentList() *EquipmentListNode {
	if x, ok := x.GetSelected().(*Node_EquipmentList); ok {
		return x.EquipmentList
	}
	return nil
}

func (x *Node) GetEquipmentListTimeseries() *EquipmentListTimeseriesNode {
	if x, ok := x.GetSelected().(*Node_EquipmentListTimeseries); ok {
		return x.EquipmentListTimeseries
	}
	return nil
}

func (x *Node) GetEquipmentVehicle() *EquipmentVehicleNode {
	if x, ok := x.GetSelected().(*Node_EquipmentVehicle); ok {
		return x.EquipmentVehicle
	}
	return nil
}

func (x *Node) GetEquipmentVehicleTimeseries() *EquipmentVehicleTimeseriesNode {
	if x, ok := x.GetSelected().(*Node_EquipmentVehicleTimeseries); ok {
		return x.EquipmentVehicleTimeseries
	}
	return nil
}

func (x *Node) GetVINVehicle() *VINVehicleNode {
	if x, ok := x.GetSelected().(*Node_VINVehicle); ok {
		return x.VINVehicle
	}
	return nil
}

func (x *Node) GetVINDecoderProblem() *VINDecoderProblemNode {
	if x, ok := x.GetSelected().(*Node_VINDecoderProblem); ok {
		return x.VINDecoderProblem
	}
	return nil
}

func (x *Node) GetVINDecoderSolution() *VINDecoderSolutionNode {
	if x, ok := x.GetSelected().(*Node_VINDecoderSolution); ok {
		return x.VINDecoderSolution
	}
	return nil
}

func (x *Node) GetTelematicsConnection() *TelematicsConnectionNode {
	if x, ok := x.GetSelected().(*Node_TelematicsConnection); ok {
		return x.TelematicsConnection
	}
	return nil
}

func (x *Node) GetTelematicsConnectionSnapshot() *TelematicsConnectionSnapshotNode {
	if x, ok := x.GetSelected().(*Node_TelematicsConnectionSnapshot); ok {
		return x.TelematicsConnectionSnapshot
	}
	return nil
}

func (x *Node) GetTelematicsTag() *TelematicsTagNode {
	if x, ok := x.GetSelected().(*Node_TelematicsTag); ok {
		return x.TelematicsTag
	}
	return nil
}

func (x *Node) GetTelematicsTagSnapshot() *TelematicsTagSnapshotNode {
	if x, ok := x.GetSelected().(*Node_TelematicsTagSnapshot); ok {
		return x.TelematicsTagSnapshot
	}
	return nil
}

func (x *Node) GetTelematicsVehicle() *TelematicsVehicleNode {
	if x, ok := x.GetSelected().(*Node_TelematicsVehicle); ok {
		return x.TelematicsVehicle
	}
	return nil
}

func (x *Node) GetTelematicsVehicleSnapshot() *TelematicsVehicleSnapshotNode {
	if x, ok := x.GetSelected().(*Node_TelematicsVehicleSnapshot); ok {
		return x.TelematicsVehicleSnapshot
	}
	return nil
}

type isNode_Selected interface {
	isNode_Selected()
}

type Node_Agency struct {
	Agency *AgencyNode `protobuf:"bytes,1,opt,name=Agency,proto3,oneof"`
}

type Node_Fleet struct {
	Fleet *FleetNode `protobuf:"bytes,2,opt,name=Fleet,proto3,oneof"`
}

type Node_Application struct {
	Application *ApplicationNode `protobuf:"bytes,3,opt,name=Application,proto3,oneof"`
}

type Node_EquipmentList struct {
	EquipmentList *EquipmentListNode `protobuf:"bytes,4,opt,name=EquipmentList,proto3,oneof"`
}

type Node_EquipmentListTimeseries struct {
	EquipmentListTimeseries *EquipmentListTimeseriesNode `protobuf:"bytes,5,opt,name=EquipmentListTimeseries,proto3,oneof"`
}

type Node_EquipmentVehicle struct {
	EquipmentVehicle *EquipmentVehicleNode `protobuf:"bytes,6,opt,name=EquipmentVehicle,proto3,oneof"`
}

type Node_EquipmentVehicleTimeseries struct {
	EquipmentVehicleTimeseries *EquipmentVehicleTimeseriesNode `protobuf:"bytes,7,opt,name=EquipmentVehicleTimeseries,proto3,oneof"`
}

type Node_VINVehicle struct {
	VINVehicle *VINVehicleNode `protobuf:"bytes,8,opt,name=VINVehicle,proto3,oneof"`
}

type Node_VINDecoderProblem struct {
	VINDecoderProblem *VINDecoderProblemNode `protobuf:"bytes,9,opt,name=VINDecoderProblem,proto3,oneof"`
}

type Node_VINDecoderSolution struct {
	VINDecoderSolution *VINDecoderSolutionNode `protobuf:"bytes,10,opt,name=VINDecoderSolution,proto3,oneof"`
}

type Node_TelematicsConnection struct {
	TelematicsConnection *TelematicsConnectionNode `protobuf:"bytes,11,opt,name=TelematicsConnection,proto3,oneof"`
}

type Node_TelematicsConnectionSnapshot struct {
	TelematicsConnectionSnapshot *TelematicsConnectionSnapshotNode `protobuf:"bytes,12,opt,name=TelematicsConnectionSnapshot,proto3,oneof"`
}

type Node_TelematicsTag struct {
	TelematicsTag *TelematicsTagNode `protobuf:"bytes,13,opt,name=TelematicsTag,proto3,oneof"`
}

type Node_TelematicsTagSnapshot struct {
	TelematicsTagSnapshot *TelematicsTagSnapshotNode `protobuf:"bytes,14,opt,name=TelematicsTagSnapshot,proto3,oneof"`
}

type Node_TelematicsVehicle struct {
	TelematicsVehicle *TelematicsVehicleNode `protobuf:"bytes,15,opt,name=TelematicsVehicle,proto3,oneof"`
}

type Node_TelematicsVehicleSnapshot struct {
	TelematicsVehicleSnapshot *TelematicsVehicleSnapshotNode `protobuf:"bytes,16,opt,name=TelematicsVehicleSnapshot,proto3,oneof"`
}

func (*Node_Agency) isNode_Selected() {}

func (*Node_Fleet) isNode_Selected() {}

func (*Node_Application) isNode_Selected() {}

func (*Node_EquipmentList) isNode_Selected() {}

func (*Node_EquipmentListTimeseries) isNode_Selected() {}

func (*Node_EquipmentVehicle) isNode_Selected() {}

func (*Node_EquipmentVehicleTimeseries) isNode_Selected() {}

func (*Node_VINVehicle) isNode_Selected() {}

func (*Node_VINDecoderProblem) isNode_Selected() {}

func (*Node_VINDecoderSolution) isNode_Selected() {}

func (*Node_TelematicsConnection) isNode_Selected() {}

func (*Node_TelematicsConnectionSnapshot) isNode_Selected() {}

func (*Node_TelematicsTag) isNode_Selected() {}

func (*Node_TelematicsTagSnapshot) isNode_Selected() {}

func (*Node_TelematicsVehicle) isNode_Selected() {}

func (*Node_TelematicsVehicleSnapshot) isNode_Selected() {}

type VINDecoderSolutionToProblemEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *VINDecoderSolutionToProblemEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *VINDecoderSolutionToProblemEdge) Reset() {
	*x = VINDecoderSolutionToProblemEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDecoderSolutionToProblemEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDecoderSolutionToProblemEdge) ProtoMessage() {}

func (x *VINDecoderSolutionToProblemEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDecoderSolutionToProblemEdge.ProtoReflect.Descriptor instead.
func (*VINDecoderSolutionToProblemEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{17}
}

func (x *VINDecoderSolutionToProblemEdge) GetKey() *VINDecoderSolutionToProblemEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type VINDecoderProblemToVINVehicleEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *VINDecoderProblemToVINVehicleEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *VINDecoderProblemToVINVehicleEdge) Reset() {
	*x = VINDecoderProblemToVINVehicleEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDecoderProblemToVINVehicleEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDecoderProblemToVINVehicleEdge) ProtoMessage() {}

func (x *VINDecoderProblemToVINVehicleEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDecoderProblemToVINVehicleEdge.ProtoReflect.Descriptor instead.
func (*VINDecoderProblemToVINVehicleEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{18}
}

func (x *VINDecoderProblemToVINVehicleEdge) GetKey() *VINDecoderProblemToVINVehicleEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type ApplicationToAgencyEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *ApplicationToAgencyEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *ApplicationToAgencyEdge) Reset() {
	*x = ApplicationToAgencyEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicationToAgencyEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicationToAgencyEdge) ProtoMessage() {}

func (x *ApplicationToAgencyEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicationToAgencyEdge.ProtoReflect.Descriptor instead.
func (*ApplicationToAgencyEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{19}
}

func (x *ApplicationToAgencyEdge) GetKey() *ApplicationToAgencyEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type ApplicationToFleetEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *ApplicationToFleetEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *ApplicationToFleetEdge) Reset() {
	*x = ApplicationToFleetEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicationToFleetEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicationToFleetEdge) ProtoMessage() {}

func (x *ApplicationToFleetEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicationToFleetEdge.ProtoReflect.Descriptor instead.
func (*ApplicationToFleetEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{20}
}

func (x *ApplicationToFleetEdge) GetKey() *ApplicationToFleetEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type EquipmentListToApplicationEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *EquipmentListToApplicationEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *EquipmentListToApplicationEdge) Reset() {
	*x = EquipmentListToApplicationEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentListToApplicationEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentListToApplicationEdge) ProtoMessage() {}

func (x *EquipmentListToApplicationEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentListToApplicationEdge.ProtoReflect.Descriptor instead.
func (*EquipmentListToApplicationEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{21}
}

func (x *EquipmentListToApplicationEdge) GetKey() *EquipmentListToApplicationEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type EquipmentVehicleToVINEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *EquipmentVehicleToVINEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *EquipmentVehicleToVINEdge) Reset() {
	*x = EquipmentVehicleToVINEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentVehicleToVINEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentVehicleToVINEdge) ProtoMessage() {}

func (x *EquipmentVehicleToVINEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentVehicleToVINEdge.ProtoReflect.Descriptor instead.
func (*EquipmentVehicleToVINEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{22}
}

func (x *EquipmentVehicleToVINEdge) GetKey() *EquipmentVehicleToVINEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type EquipmentTimeseriesToListEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *EquipmentTimeseriesToListEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *EquipmentTimeseriesToListEdge) Reset() {
	*x = EquipmentTimeseriesToListEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentTimeseriesToListEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentTimeseriesToListEdge) ProtoMessage() {}

func (x *EquipmentTimeseriesToListEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentTimeseriesToListEdge.ProtoReflect.Descriptor instead.
func (*EquipmentTimeseriesToListEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{23}
}

func (x *EquipmentTimeseriesToListEdge) GetKey() *EquipmentTimeseriesToListEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type EquipmentVehicleTimeseriesToListEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *EquipmentVehicleTimeseriesToListEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *EquipmentVehicleTimeseriesToListEdge) Reset() {
	*x = EquipmentVehicleTimeseriesToListEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentVehicleTimeseriesToListEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentVehicleTimeseriesToListEdge) ProtoMessage() {}

func (x *EquipmentVehicleTimeseriesToListEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentVehicleTimeseriesToListEdge.ProtoReflect.Descriptor instead.
func (*EquipmentVehicleTimeseriesToListEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{24}
}

func (x *EquipmentVehicleTimeseriesToListEdge) GetKey() *EquipmentVehicleTimeseriesToListEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type EquipmentVehicleTimeseriesToVehicleEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *EquipmentVehicleTimeseriesToVehicleEdge) Reset() {
	*x = EquipmentVehicleTimeseriesToVehicleEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentVehicleTimeseriesToVehicleEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentVehicleTimeseriesToVehicleEdge) ProtoMessage() {}

func (x *EquipmentVehicleTimeseriesToVehicleEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentVehicleTimeseriesToVehicleEdge.ProtoReflect.Descriptor instead.
func (*EquipmentVehicleTimeseriesToVehicleEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{25}
}

func (x *EquipmentVehicleTimeseriesToVehicleEdge) GetKey() *EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type TelematicsConnectionToFleetEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *TelematicsConnectionToFleetEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *TelematicsConnectionToFleetEdge) Reset() {
	*x = TelematicsConnectionToFleetEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsConnectionToFleetEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsConnectionToFleetEdge) ProtoMessage() {}

func (x *TelematicsConnectionToFleetEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsConnectionToFleetEdge.ProtoReflect.Descriptor instead.
func (*TelematicsConnectionToFleetEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{26}
}

func (x *TelematicsConnectionToFleetEdge) GetKey() *TelematicsConnectionToFleetEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type TelematicsConnectionSnapshotToConnectionEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *TelematicsConnectionSnapshotToConnectionEdge) Reset() {
	*x = TelematicsConnectionSnapshotToConnectionEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsConnectionSnapshotToConnectionEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsConnectionSnapshotToConnectionEdge) ProtoMessage() {}

func (x *TelematicsConnectionSnapshotToConnectionEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsConnectionSnapshotToConnectionEdge.ProtoReflect.Descriptor instead.
func (*TelematicsConnectionSnapshotToConnectionEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{27}
}

func (x *TelematicsConnectionSnapshotToConnectionEdge) GetKey() *TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type TelematicsTagSnapshotToConnectionSnapshotEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *TelematicsTagSnapshotToConnectionSnapshotEdge) Reset() {
	*x = TelematicsTagSnapshotToConnectionSnapshotEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsTagSnapshotToConnectionSnapshotEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsTagSnapshotToConnectionSnapshotEdge) ProtoMessage() {}

func (x *TelematicsTagSnapshotToConnectionSnapshotEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsTagSnapshotToConnectionSnapshotEdge.ProtoReflect.Descriptor instead.
func (*TelematicsTagSnapshotToConnectionSnapshotEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{28}
}

func (x *TelematicsTagSnapshotToConnectionSnapshotEdge) GetKey() *TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type TelematicsTagSnapshotToTagEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *TelematicsTagSnapshotToTagEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *TelematicsTagSnapshotToTagEdge) Reset() {
	*x = TelematicsTagSnapshotToTagEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsTagSnapshotToTagEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsTagSnapshotToTagEdge) ProtoMessage() {}

func (x *TelematicsTagSnapshotToTagEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsTagSnapshotToTagEdge.ProtoReflect.Descriptor instead.
func (*TelematicsTagSnapshotToTagEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{29}
}

func (x *TelematicsTagSnapshotToTagEdge) GetKey() *TelematicsTagSnapshotToTagEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type TelematicsVehicleSnapshotToConnectionSnapshotEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *TelematicsVehicleSnapshotToConnectionSnapshotEdge) Reset() {
	*x = TelematicsVehicleSnapshotToConnectionSnapshotEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleSnapshotToConnectionSnapshotEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleSnapshotToConnectionSnapshotEdge) ProtoMessage() {}

func (x *TelematicsVehicleSnapshotToConnectionSnapshotEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleSnapshotToConnectionSnapshotEdge.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleSnapshotToConnectionSnapshotEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{30}
}

func (x *TelematicsVehicleSnapshotToConnectionSnapshotEdge) GetKey() *TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type TelematicsVehicleSnapshotToTagSnapshotEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *TelematicsVehicleSnapshotToTagSnapshotEdge) Reset() {
	*x = TelematicsVehicleSnapshotToTagSnapshotEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleSnapshotToTagSnapshotEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleSnapshotToTagSnapshotEdge) ProtoMessage() {}

func (x *TelematicsVehicleSnapshotToTagSnapshotEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleSnapshotToTagSnapshotEdge.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleSnapshotToTagSnapshotEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{31}
}

func (x *TelematicsVehicleSnapshotToTagSnapshotEdge) GetKey() *TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type TelematicsVehicleSnapshotToVehicleEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *TelematicsVehicleSnapshotToVehicleEdge) Reset() {
	*x = TelematicsVehicleSnapshotToVehicleEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleSnapshotToVehicleEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleSnapshotToVehicleEdge) ProtoMessage() {}

func (x *TelematicsVehicleSnapshotToVehicleEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleSnapshotToVehicleEdge.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleSnapshotToVehicleEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{32}
}

func (x *TelematicsVehicleSnapshotToVehicleEdge) GetKey() *TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type TelematicsVehicleSnapshotToVINVehicleEdge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key *TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
}

func (x *TelematicsVehicleSnapshotToVINVehicleEdge) Reset() {
	*x = TelematicsVehicleSnapshotToVINVehicleEdge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleSnapshotToVINVehicleEdge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleSnapshotToVINVehicleEdge) ProtoMessage() {}

func (x *TelematicsVehicleSnapshotToVINVehicleEdge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleSnapshotToVINVehicleEdge.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleSnapshotToVINVehicleEdge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{33}
}

func (x *TelematicsVehicleSnapshotToVINVehicleEdge) GetKey() *TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type Edge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Selected:
	//
	//	*Edge_VINDecoderSolutionToProblem
	//	*Edge_VINDecoderProblemToVINVehicle
	//	*Edge_ApplicationToAgency
	//	*Edge_ApplicationToFleet
	//	*Edge_EquipmentListToApplication
	//	*Edge_EquipmentVehicleToVIN
	//	*Edge_EquipmentTimeseriesToList
	//	*Edge_EquipmentVehicleTimeseriesToList
	//	*Edge_EquipmentVehicleTimeseriesToVehicle
	//	*Edge_TelematicsConnectionToFleet
	//	*Edge_TelematicsConnectionSnapshotToConnection
	//	*Edge_TelematicsTagSnapshotToConnectionSnapshot
	//	*Edge_TelematicsTagSnapshotToTag
	//	*Edge_TelematicsVehicleSnapshotToConnectionSnapshot
	//	*Edge_TelematicsVehicleSnapshotToTagSnapshot
	//	*Edge_TelematicsVehicleSnapshotToVehicle
	//	*Edge_TelematicsVehicleSnapshotToVINVehicle
	Selected isEdge_Selected `protobuf_oneof:"Selected"`
}

func (x *Edge) Reset() {
	*x = Edge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Edge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Edge) ProtoMessage() {}

func (x *Edge) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Edge.ProtoReflect.Descriptor instead.
func (*Edge) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{34}
}

func (m *Edge) GetSelected() isEdge_Selected {
	if m != nil {
		return m.Selected
	}
	return nil
}

func (x *Edge) GetVINDecoderSolutionToProblem() *VINDecoderSolutionToProblemEdge {
	if x, ok := x.GetSelected().(*Edge_VINDecoderSolutionToProblem); ok {
		return x.VINDecoderSolutionToProblem
	}
	return nil
}

func (x *Edge) GetVINDecoderProblemToVINVehicle() *VINDecoderProblemToVINVehicleEdge {
	if x, ok := x.GetSelected().(*Edge_VINDecoderProblemToVINVehicle); ok {
		return x.VINDecoderProblemToVINVehicle
	}
	return nil
}

func (x *Edge) GetApplicationToAgency() *ApplicationToAgencyEdge {
	if x, ok := x.GetSelected().(*Edge_ApplicationToAgency); ok {
		return x.ApplicationToAgency
	}
	return nil
}

func (x *Edge) GetApplicationToFleet() *ApplicationToFleetEdge {
	if x, ok := x.GetSelected().(*Edge_ApplicationToFleet); ok {
		return x.ApplicationToFleet
	}
	return nil
}

func (x *Edge) GetEquipmentListToApplication() *EquipmentListToApplicationEdge {
	if x, ok := x.GetSelected().(*Edge_EquipmentListToApplication); ok {
		return x.EquipmentListToApplication
	}
	return nil
}

func (x *Edge) GetEquipmentVehicleToVIN() *EquipmentVehicleToVINEdge {
	if x, ok := x.GetSelected().(*Edge_EquipmentVehicleToVIN); ok {
		return x.EquipmentVehicleToVIN
	}
	return nil
}

func (x *Edge) GetEquipmentTimeseriesToList() *EquipmentTimeseriesToListEdge {
	if x, ok := x.GetSelected().(*Edge_EquipmentTimeseriesToList); ok {
		return x.EquipmentTimeseriesToList
	}
	return nil
}

func (x *Edge) GetEquipmentVehicleTimeseriesToList() *EquipmentVehicleTimeseriesToListEdge {
	if x, ok := x.GetSelected().(*Edge_EquipmentVehicleTimeseriesToList); ok {
		return x.EquipmentVehicleTimeseriesToList
	}
	return nil
}

func (x *Edge) GetEquipmentVehicleTimeseriesToVehicle() *EquipmentVehicleTimeseriesToVehicleEdge {
	if x, ok := x.GetSelected().(*Edge_EquipmentVehicleTimeseriesToVehicle); ok {
		return x.EquipmentVehicleTimeseriesToVehicle
	}
	return nil
}

func (x *Edge) GetTelematicsConnectionToFleet() *TelematicsConnectionToFleetEdge {
	if x, ok := x.GetSelected().(*Edge_TelematicsConnectionToFleet); ok {
		return x.TelematicsConnectionToFleet
	}
	return nil
}

func (x *Edge) GetTelematicsConnectionSnapshotToConnection() *TelematicsConnectionSnapshotToConnectionEdge {
	if x, ok := x.GetSelected().(*Edge_TelematicsConnectionSnapshotToConnection); ok {
		return x.TelematicsConnectionSnapshotToConnection
	}
	return nil
}

func (x *Edge) GetTelematicsTagSnapshotToConnectionSnapshot() *TelematicsTagSnapshotToConnectionSnapshotEdge {
	if x, ok := x.GetSelected().(*Edge_TelematicsTagSnapshotToConnectionSnapshot); ok {
		return x.TelematicsTagSnapshotToConnectionSnapshot
	}
	return nil
}

func (x *Edge) GetTelematicsTagSnapshotToTag() *TelematicsTagSnapshotToTagEdge {
	if x, ok := x.GetSelected().(*Edge_TelematicsTagSnapshotToTag); ok {
		return x.TelematicsTagSnapshotToTag
	}
	return nil
}

func (x *Edge) GetTelematicsVehicleSnapshotToConnectionSnapshot() *TelematicsVehicleSnapshotToConnectionSnapshotEdge {
	if x, ok := x.GetSelected().(*Edge_TelematicsVehicleSnapshotToConnectionSnapshot); ok {
		return x.TelematicsVehicleSnapshotToConnectionSnapshot
	}
	return nil
}

func (x *Edge) GetTelematicsVehicleSnapshotToTagSnapshot() *TelematicsVehicleSnapshotToTagSnapshotEdge {
	if x, ok := x.GetSelected().(*Edge_TelematicsVehicleSnapshotToTagSnapshot); ok {
		return x.TelematicsVehicleSnapshotToTagSnapshot
	}
	return nil
}

func (x *Edge) GetTelematicsVehicleSnapshotToVehicle() *TelematicsVehicleSnapshotToVehicleEdge {
	if x, ok := x.GetSelected().(*Edge_TelematicsVehicleSnapshotToVehicle); ok {
		return x.TelematicsVehicleSnapshotToVehicle
	}
	return nil
}

func (x *Edge) GetTelematicsVehicleSnapshotToVINVehicle() *TelematicsVehicleSnapshotToVINVehicleEdge {
	if x, ok := x.GetSelected().(*Edge_TelematicsVehicleSnapshotToVINVehicle); ok {
		return x.TelematicsVehicleSnapshotToVINVehicle
	}
	return nil
}

type isEdge_Selected interface {
	isEdge_Selected()
}

type Edge_VINDecoderSolutionToProblem struct {
	VINDecoderSolutionToProblem *VINDecoderSolutionToProblemEdge `protobuf:"bytes,1,opt,name=VINDecoderSolutionToProblem,proto3,oneof"`
}

type Edge_VINDecoderProblemToVINVehicle struct {
	VINDecoderProblemToVINVehicle *VINDecoderProblemToVINVehicleEdge `protobuf:"bytes,2,opt,name=VINDecoderProblemToVINVehicle,proto3,oneof"`
}

type Edge_ApplicationToAgency struct {
	ApplicationToAgency *ApplicationToAgencyEdge `protobuf:"bytes,3,opt,name=ApplicationToAgency,proto3,oneof"`
}

type Edge_ApplicationToFleet struct {
	ApplicationToFleet *ApplicationToFleetEdge `protobuf:"bytes,4,opt,name=ApplicationToFleet,proto3,oneof"`
}

type Edge_EquipmentListToApplication struct {
	EquipmentListToApplication *EquipmentListToApplicationEdge `protobuf:"bytes,5,opt,name=EquipmentListToApplication,proto3,oneof"`
}

type Edge_EquipmentVehicleToVIN struct {
	EquipmentVehicleToVIN *EquipmentVehicleToVINEdge `protobuf:"bytes,6,opt,name=EquipmentVehicleToVIN,proto3,oneof"`
}

type Edge_EquipmentTimeseriesToList struct {
	EquipmentTimeseriesToList *EquipmentTimeseriesToListEdge `protobuf:"bytes,7,opt,name=EquipmentTimeseriesToList,proto3,oneof"`
}

type Edge_EquipmentVehicleTimeseriesToList struct {
	EquipmentVehicleTimeseriesToList *EquipmentVehicleTimeseriesToListEdge `protobuf:"bytes,8,opt,name=EquipmentVehicleTimeseriesToList,proto3,oneof"`
}

type Edge_EquipmentVehicleTimeseriesToVehicle struct {
	EquipmentVehicleTimeseriesToVehicle *EquipmentVehicleTimeseriesToVehicleEdge `protobuf:"bytes,9,opt,name=EquipmentVehicleTimeseriesToVehicle,proto3,oneof"`
}

type Edge_TelematicsConnectionToFleet struct {
	TelematicsConnectionToFleet *TelematicsConnectionToFleetEdge `protobuf:"bytes,10,opt,name=TelematicsConnectionToFleet,proto3,oneof"`
}

type Edge_TelematicsConnectionSnapshotToConnection struct {
	TelematicsConnectionSnapshotToConnection *TelematicsConnectionSnapshotToConnectionEdge `protobuf:"bytes,11,opt,name=TelematicsConnectionSnapshotToConnection,proto3,oneof"`
}

type Edge_TelematicsTagSnapshotToConnectionSnapshot struct {
	TelematicsTagSnapshotToConnectionSnapshot *TelematicsTagSnapshotToConnectionSnapshotEdge `protobuf:"bytes,12,opt,name=TelematicsTagSnapshotToConnectionSnapshot,proto3,oneof"`
}

type Edge_TelematicsTagSnapshotToTag struct {
	TelematicsTagSnapshotToTag *TelematicsTagSnapshotToTagEdge `protobuf:"bytes,13,opt,name=TelematicsTagSnapshotToTag,proto3,oneof"`
}

type Edge_TelematicsVehicleSnapshotToConnectionSnapshot struct {
	TelematicsVehicleSnapshotToConnectionSnapshot *TelematicsVehicleSnapshotToConnectionSnapshotEdge `protobuf:"bytes,14,opt,name=TelematicsVehicleSnapshotToConnectionSnapshot,proto3,oneof"`
}

type Edge_TelematicsVehicleSnapshotToTagSnapshot struct {
	TelematicsVehicleSnapshotToTagSnapshot *TelematicsVehicleSnapshotToTagSnapshotEdge `protobuf:"bytes,15,opt,name=TelematicsVehicleSnapshotToTagSnapshot,proto3,oneof"`
}

type Edge_TelematicsVehicleSnapshotToVehicle struct {
	TelematicsVehicleSnapshotToVehicle *TelematicsVehicleSnapshotToVehicleEdge `protobuf:"bytes,16,opt,name=TelematicsVehicleSnapshotToVehicle,proto3,oneof"`
}

type Edge_TelematicsVehicleSnapshotToVINVehicle struct {
	TelematicsVehicleSnapshotToVINVehicle *TelematicsVehicleSnapshotToVINVehicleEdge `protobuf:"bytes,17,opt,name=TelematicsVehicleSnapshotToVINVehicle,proto3,oneof"`
}

func (*Edge_VINDecoderSolutionToProblem) isEdge_Selected() {}

func (*Edge_VINDecoderProblemToVINVehicle) isEdge_Selected() {}

func (*Edge_ApplicationToAgency) isEdge_Selected() {}

func (*Edge_ApplicationToFleet) isEdge_Selected() {}

func (*Edge_EquipmentListToApplication) isEdge_Selected() {}

func (*Edge_EquipmentVehicleToVIN) isEdge_Selected() {}

func (*Edge_EquipmentTimeseriesToList) isEdge_Selected() {}

func (*Edge_EquipmentVehicleTimeseriesToList) isEdge_Selected() {}

func (*Edge_EquipmentVehicleTimeseriesToVehicle) isEdge_Selected() {}

func (*Edge_TelematicsConnectionToFleet) isEdge_Selected() {}

func (*Edge_TelematicsConnectionSnapshotToConnection) isEdge_Selected() {}

func (*Edge_TelematicsTagSnapshotToConnectionSnapshot) isEdge_Selected() {}

func (*Edge_TelematicsTagSnapshotToTag) isEdge_Selected() {}

func (*Edge_TelematicsVehicleSnapshotToConnectionSnapshot) isEdge_Selected() {}

func (*Edge_TelematicsVehicleSnapshotToTagSnapshot) isEdge_Selected() {}

func (*Edge_TelematicsVehicleSnapshotToVehicle) isEdge_Selected() {}

func (*Edge_TelematicsVehicleSnapshotToVINVehicle) isEdge_Selected() {}

type AgencyNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgencyID string `protobuf:"bytes,1,opt,name=AgencyID,proto3" json:"AgencyID,omitempty"`
}

func (x *AgencyNode_PrimaryKey) Reset() {
	*x = AgencyNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgencyNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgencyNode_PrimaryKey) ProtoMessage() {}

func (x *AgencyNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgencyNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*AgencyNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{0, 0}
}

func (x *AgencyNode_PrimaryKey) GetAgencyID() string {
	if x != nil {
		return x.AgencyID
	}
	return ""
}

type FleetNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FleetID string `protobuf:"bytes,1,opt,name=FleetID,proto3" json:"FleetID,omitempty"`
}

func (x *FleetNode_PrimaryKey) Reset() {
	*x = FleetNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FleetNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FleetNode_PrimaryKey) ProtoMessage() {}

func (x *FleetNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FleetNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*FleetNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{1, 0}
}

func (x *FleetNode_PrimaryKey) GetFleetID() string {
	if x != nil {
		return x.FleetID
	}
	return ""
}

type ApplicationNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplicationID string `protobuf:"bytes,1,opt,name=ApplicationID,proto3" json:"ApplicationID,omitempty"`
}

func (x *ApplicationNode_PrimaryKey) Reset() {
	*x = ApplicationNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicationNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicationNode_PrimaryKey) ProtoMessage() {}

func (x *ApplicationNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicationNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*ApplicationNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ApplicationNode_PrimaryKey) GetApplicationID() string {
	if x != nil {
		return x.ApplicationID
	}
	return ""
}

type EquipmentListNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EquipmentListID string `protobuf:"bytes,1,opt,name=EquipmentListID,proto3" json:"EquipmentListID,omitempty"`
}

func (x *EquipmentListNode_PrimaryKey) Reset() {
	*x = EquipmentListNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentListNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentListNode_PrimaryKey) ProtoMessage() {}

func (x *EquipmentListNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentListNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*EquipmentListNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{3, 0}
}

func (x *EquipmentListNode_PrimaryKey) GetEquipmentListID() string {
	if x != nil {
		return x.EquipmentListID
	}
	return ""
}

type EquipmentListTimeseriesNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EquipmentListID string                 `protobuf:"bytes,1,opt,name=EquipmentListID,proto3" json:"EquipmentListID,omitempty"`
	Time            *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *EquipmentListTimeseriesNode_PrimaryKey) Reset() {
	*x = EquipmentListTimeseriesNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentListTimeseriesNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentListTimeseriesNode_PrimaryKey) ProtoMessage() {}

func (x *EquipmentListTimeseriesNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentListTimeseriesNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*EquipmentListTimeseriesNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{4, 0}
}

func (x *EquipmentListTimeseriesNode_PrimaryKey) GetEquipmentListID() string {
	if x != nil {
		return x.EquipmentListID
	}
	return ""
}

func (x *EquipmentListTimeseriesNode_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type EquipmentVehicleNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EquipmentListID string `protobuf:"bytes,1,opt,name=EquipmentListID,proto3" json:"EquipmentListID,omitempty"`
	VIN             string `protobuf:"bytes,2,opt,name=VIN,proto3" json:"VIN,omitempty"`
}

func (x *EquipmentVehicleNode_PrimaryKey) Reset() {
	*x = EquipmentVehicleNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentVehicleNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentVehicleNode_PrimaryKey) ProtoMessage() {}

func (x *EquipmentVehicleNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentVehicleNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*EquipmentVehicleNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{5, 0}
}

func (x *EquipmentVehicleNode_PrimaryKey) GetEquipmentListID() string {
	if x != nil {
		return x.EquipmentListID
	}
	return ""
}

func (x *EquipmentVehicleNode_PrimaryKey) GetVIN() string {
	if x != nil {
		return x.VIN
	}
	return ""
}

type EquipmentVehicleTimeseriesNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EquipmentListID string                 `protobuf:"bytes,1,opt,name=EquipmentListID,proto3" json:"EquipmentListID,omitempty"`
	VIN             string                 `protobuf:"bytes,2,opt,name=VIN,proto3" json:"VIN,omitempty"`
	Time            *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *EquipmentVehicleTimeseriesNode_PrimaryKey) Reset() {
	*x = EquipmentVehicleTimeseriesNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentVehicleTimeseriesNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentVehicleTimeseriesNode_PrimaryKey) ProtoMessage() {}

func (x *EquipmentVehicleTimeseriesNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentVehicleTimeseriesNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*EquipmentVehicleTimeseriesNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{6, 0}
}

func (x *EquipmentVehicleTimeseriesNode_PrimaryKey) GetEquipmentListID() string {
	if x != nil {
		return x.EquipmentListID
	}
	return ""
}

func (x *EquipmentVehicleTimeseriesNode_PrimaryKey) GetVIN() string {
	if x != nil {
		return x.VIN
	}
	return ""
}

func (x *EquipmentVehicleTimeseriesNode_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type VINVehicleNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VIN string `protobuf:"bytes,1,opt,name=VIN,proto3" json:"VIN,omitempty"`
}

func (x *VINVehicleNode_PrimaryKey) Reset() {
	*x = VINVehicleNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINVehicleNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINVehicleNode_PrimaryKey) ProtoMessage() {}

func (x *VINVehicleNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINVehicleNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*VINVehicleNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{7, 0}
}

func (x *VINVehicleNode_PrimaryKey) GetVIN() string {
	if x != nil {
		return x.VIN
	}
	return ""
}

type VINDecoderProblemNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VIN string `protobuf:"bytes,1,opt,name=VIN,proto3" json:"VIN,omitempty"`
}

func (x *VINDecoderProblemNode_PrimaryKey) Reset() {
	*x = VINDecoderProblemNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDecoderProblemNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDecoderProblemNode_PrimaryKey) ProtoMessage() {}

func (x *VINDecoderProblemNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDecoderProblemNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*VINDecoderProblemNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{8, 0}
}

func (x *VINDecoderProblemNode_PrimaryKey) GetVIN() string {
	if x != nil {
		return x.VIN
	}
	return ""
}

type VINDecoderSolutionNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VIN string `protobuf:"bytes,1,opt,name=VIN,proto3" json:"VIN,omitempty"`
}

func (x *VINDecoderSolutionNode_PrimaryKey) Reset() {
	*x = VINDecoderSolutionNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDecoderSolutionNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDecoderSolutionNode_PrimaryKey) ProtoMessage() {}

func (x *VINDecoderSolutionNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDecoderSolutionNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*VINDecoderSolutionNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{9, 0}
}

func (x *VINDecoderSolutionNode_PrimaryKey) GetVIN() string {
	if x != nil {
		return x.VIN
	}
	return ""
}

type TelematicsConnectionNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleID string `protobuf:"bytes,1,opt,name=HandleID,proto3" json:"HandleID,omitempty"`
}

func (x *TelematicsConnectionNode_PrimaryKey) Reset() {
	*x = TelematicsConnectionNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsConnectionNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsConnectionNode_PrimaryKey) ProtoMessage() {}

func (x *TelematicsConnectionNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsConnectionNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsConnectionNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{10, 0}
}

func (x *TelematicsConnectionNode_PrimaryKey) GetHandleID() string {
	if x != nil {
		return x.HandleID
	}
	return ""
}

type TelematicsConnectionSnapshotNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleID string                 `protobuf:"bytes,1,opt,name=HandleID,proto3" json:"HandleID,omitempty"`
	Time     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *TelematicsConnectionSnapshotNode_PrimaryKey) Reset() {
	*x = TelematicsConnectionSnapshotNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsConnectionSnapshotNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsConnectionSnapshotNode_PrimaryKey) ProtoMessage() {}

func (x *TelematicsConnectionSnapshotNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsConnectionSnapshotNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsConnectionSnapshotNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{11, 0}
}

func (x *TelematicsConnectionSnapshotNode_PrimaryKey) GetHandleID() string {
	if x != nil {
		return x.HandleID
	}
	return ""
}

func (x *TelematicsConnectionSnapshotNode_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type TelematicsTagNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleID string `protobuf:"bytes,1,opt,name=HandleID,proto3" json:"HandleID,omitempty"`
	TagID    string `protobuf:"bytes,2,opt,name=TagID,proto3" json:"TagID,omitempty"`
}

func (x *TelematicsTagNode_PrimaryKey) Reset() {
	*x = TelematicsTagNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsTagNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsTagNode_PrimaryKey) ProtoMessage() {}

func (x *TelematicsTagNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsTagNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsTagNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{12, 0}
}

func (x *TelematicsTagNode_PrimaryKey) GetHandleID() string {
	if x != nil {
		return x.HandleID
	}
	return ""
}

func (x *TelematicsTagNode_PrimaryKey) GetTagID() string {
	if x != nil {
		return x.TagID
	}
	return ""
}

type TelematicsTagSnapshotNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleID string                 `protobuf:"bytes,1,opt,name=HandleID,proto3" json:"HandleID,omitempty"`
	TagID    string                 `protobuf:"bytes,2,opt,name=TagID,proto3" json:"TagID,omitempty"`
	Time     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *TelematicsTagSnapshotNode_PrimaryKey) Reset() {
	*x = TelematicsTagSnapshotNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsTagSnapshotNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsTagSnapshotNode_PrimaryKey) ProtoMessage() {}

func (x *TelematicsTagSnapshotNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsTagSnapshotNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsTagSnapshotNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{13, 0}
}

func (x *TelematicsTagSnapshotNode_PrimaryKey) GetHandleID() string {
	if x != nil {
		return x.HandleID
	}
	return ""
}

func (x *TelematicsTagSnapshotNode_PrimaryKey) GetTagID() string {
	if x != nil {
		return x.TagID
	}
	return ""
}

func (x *TelematicsTagSnapshotNode_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type TelematicsVehicleNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleID  string `protobuf:"bytes,1,opt,name=HandleID,proto3" json:"HandleID,omitempty"`
	VehicleID string `protobuf:"bytes,2,opt,name=VehicleID,proto3" json:"VehicleID,omitempty"`
}

func (x *TelematicsVehicleNode_PrimaryKey) Reset() {
	*x = TelematicsVehicleNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleNode_PrimaryKey) ProtoMessage() {}

func (x *TelematicsVehicleNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{14, 0}
}

func (x *TelematicsVehicleNode_PrimaryKey) GetHandleID() string {
	if x != nil {
		return x.HandleID
	}
	return ""
}

func (x *TelematicsVehicleNode_PrimaryKey) GetVehicleID() string {
	if x != nil {
		return x.VehicleID
	}
	return ""
}

type TelematicsVehicleSnapshotNode_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleID  string                 `protobuf:"bytes,1,opt,name=HandleID,proto3" json:"HandleID,omitempty"`
	VehicleID string                 `protobuf:"bytes,2,opt,name=VehicleID,proto3" json:"VehicleID,omitempty"`
	Time      *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *TelematicsVehicleSnapshotNode_PrimaryKey) Reset() {
	*x = TelematicsVehicleSnapshotNode_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleSnapshotNode_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleSnapshotNode_PrimaryKey) ProtoMessage() {}

func (x *TelematicsVehicleSnapshotNode_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleSnapshotNode_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleSnapshotNode_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{15, 0}
}

func (x *TelematicsVehicleSnapshotNode_PrimaryKey) GetHandleID() string {
	if x != nil {
		return x.HandleID
	}
	return ""
}

func (x *TelematicsVehicleSnapshotNode_PrimaryKey) GetVehicleID() string {
	if x != nil {
		return x.VehicleID
	}
	return ""
}

func (x *TelematicsVehicleSnapshotNode_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type VINDecoderSolutionToProblemEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *VINDecoderSolutionNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *VINDecoderProblemNode  `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
}

func (x *VINDecoderSolutionToProblemEdge_PrimaryKey) Reset() {
	*x = VINDecoderSolutionToProblemEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDecoderSolutionToProblemEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDecoderSolutionToProblemEdge_PrimaryKey) ProtoMessage() {}

func (x *VINDecoderSolutionToProblemEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDecoderSolutionToProblemEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*VINDecoderSolutionToProblemEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{17, 0}
}

func (x *VINDecoderSolutionToProblemEdge_PrimaryKey) GetSource() *VINDecoderSolutionNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *VINDecoderSolutionToProblemEdge_PrimaryKey) GetDestination() *VINDecoderProblemNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

type VINDecoderProblemToVINVehicleEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *VINDecoderProblemNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *VINVehicleNode        `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
}

func (x *VINDecoderProblemToVINVehicleEdge_PrimaryKey) Reset() {
	*x = VINDecoderProblemToVINVehicleEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDecoderProblemToVINVehicleEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDecoderProblemToVINVehicleEdge_PrimaryKey) ProtoMessage() {}

func (x *VINDecoderProblemToVINVehicleEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDecoderProblemToVINVehicleEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*VINDecoderProblemToVINVehicleEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{18, 0}
}

func (x *VINDecoderProblemToVINVehicleEdge_PrimaryKey) GetSource() *VINDecoderProblemNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *VINDecoderProblemToVINVehicleEdge_PrimaryKey) GetDestination() *VINVehicleNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

type ApplicationToAgencyEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *ApplicationNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *AgencyNode      `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
}

func (x *ApplicationToAgencyEdge_PrimaryKey) Reset() {
	*x = ApplicationToAgencyEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicationToAgencyEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicationToAgencyEdge_PrimaryKey) ProtoMessage() {}

func (x *ApplicationToAgencyEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicationToAgencyEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*ApplicationToAgencyEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{19, 0}
}

func (x *ApplicationToAgencyEdge_PrimaryKey) GetSource() *ApplicationNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *ApplicationToAgencyEdge_PrimaryKey) GetDestination() *AgencyNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

type ApplicationToFleetEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *ApplicationNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *FleetNode       `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
}

func (x *ApplicationToFleetEdge_PrimaryKey) Reset() {
	*x = ApplicationToFleetEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicationToFleetEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicationToFleetEdge_PrimaryKey) ProtoMessage() {}

func (x *ApplicationToFleetEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicationToFleetEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*ApplicationToFleetEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{20, 0}
}

func (x *ApplicationToFleetEdge_PrimaryKey) GetSource() *ApplicationNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *ApplicationToFleetEdge_PrimaryKey) GetDestination() *FleetNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

type EquipmentListToApplicationEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *EquipmentListNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *ApplicationNode   `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
}

func (x *EquipmentListToApplicationEdge_PrimaryKey) Reset() {
	*x = EquipmentListToApplicationEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentListToApplicationEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentListToApplicationEdge_PrimaryKey) ProtoMessage() {}

func (x *EquipmentListToApplicationEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentListToApplicationEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*EquipmentListToApplicationEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{21, 0}
}

func (x *EquipmentListToApplicationEdge_PrimaryKey) GetSource() *EquipmentListNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *EquipmentListToApplicationEdge_PrimaryKey) GetDestination() *ApplicationNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

type EquipmentVehicleToVINEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *EquipmentVehicleNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *VINVehicleNode       `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
}

func (x *EquipmentVehicleToVINEdge_PrimaryKey) Reset() {
	*x = EquipmentVehicleToVINEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentVehicleToVINEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentVehicleToVINEdge_PrimaryKey) ProtoMessage() {}

func (x *EquipmentVehicleToVINEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentVehicleToVINEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*EquipmentVehicleToVINEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{22, 0}
}

func (x *EquipmentVehicleToVINEdge_PrimaryKey) GetSource() *EquipmentVehicleNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *EquipmentVehicleToVINEdge_PrimaryKey) GetDestination() *VINVehicleNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

type EquipmentTimeseriesToListEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *EquipmentListTimeseriesNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *EquipmentListNode           `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
	Time        *timestamppb.Timestamp       `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *EquipmentTimeseriesToListEdge_PrimaryKey) Reset() {
	*x = EquipmentTimeseriesToListEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentTimeseriesToListEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentTimeseriesToListEdge_PrimaryKey) ProtoMessage() {}

func (x *EquipmentTimeseriesToListEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentTimeseriesToListEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*EquipmentTimeseriesToListEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{23, 0}
}

func (x *EquipmentTimeseriesToListEdge_PrimaryKey) GetSource() *EquipmentListTimeseriesNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *EquipmentTimeseriesToListEdge_PrimaryKey) GetDestination() *EquipmentListNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *EquipmentTimeseriesToListEdge_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type EquipmentVehicleTimeseriesToListEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *EquipmentVehicleTimeseriesNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *EquipmentListTimeseriesNode    `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
	Time        *timestamppb.Timestamp          `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *EquipmentVehicleTimeseriesToListEdge_PrimaryKey) Reset() {
	*x = EquipmentVehicleTimeseriesToListEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentVehicleTimeseriesToListEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentVehicleTimeseriesToListEdge_PrimaryKey) ProtoMessage() {}

func (x *EquipmentVehicleTimeseriesToListEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentVehicleTimeseriesToListEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*EquipmentVehicleTimeseriesToListEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{24, 0}
}

func (x *EquipmentVehicleTimeseriesToListEdge_PrimaryKey) GetSource() *EquipmentVehicleTimeseriesNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *EquipmentVehicleTimeseriesToListEdge_PrimaryKey) GetDestination() *EquipmentListTimeseriesNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *EquipmentVehicleTimeseriesToListEdge_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *EquipmentVehicleTimeseriesNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *EquipmentVehicleNode           `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
	Time        *timestamppb.Timestamp          `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey) Reset() {
	*x = EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey) ProtoMessage() {}

func (x *EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{25, 0}
}

func (x *EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey) GetSource() *EquipmentVehicleTimeseriesNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey) GetDestination() *EquipmentVehicleNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type TelematicsConnectionToFleetEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *TelematicsConnectionNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *FleetNode                `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
}

func (x *TelematicsConnectionToFleetEdge_PrimaryKey) Reset() {
	*x = TelematicsConnectionToFleetEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsConnectionToFleetEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsConnectionToFleetEdge_PrimaryKey) ProtoMessage() {}

func (x *TelematicsConnectionToFleetEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsConnectionToFleetEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsConnectionToFleetEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{26, 0}
}

func (x *TelematicsConnectionToFleetEdge_PrimaryKey) GetSource() *TelematicsConnectionNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *TelematicsConnectionToFleetEdge_PrimaryKey) GetDestination() *FleetNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

type TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *TelematicsConnectionSnapshotNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *TelematicsConnectionNode         `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
	Time        *timestamppb.Timestamp            `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey) Reset() {
	*x = TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey) ProtoMessage() {}

func (x *TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{27, 0}
}

func (x *TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey) GetSource() *TelematicsConnectionSnapshotNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey) GetDestination() *TelematicsConnectionNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *TelematicsTagSnapshotNode        `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *TelematicsConnectionSnapshotNode `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
	Time        *timestamppb.Timestamp            `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey) Reset() {
	*x = TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey) ProtoMessage() {}

func (x *TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{28, 0}
}

func (x *TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey) GetSource() *TelematicsTagSnapshotNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey) GetDestination() *TelematicsConnectionSnapshotNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type TelematicsTagSnapshotToTagEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *TelematicsTagSnapshotNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *TelematicsTagNode         `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
	Time        *timestamppb.Timestamp     `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *TelematicsTagSnapshotToTagEdge_PrimaryKey) Reset() {
	*x = TelematicsTagSnapshotToTagEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsTagSnapshotToTagEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsTagSnapshotToTagEdge_PrimaryKey) ProtoMessage() {}

func (x *TelematicsTagSnapshotToTagEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsTagSnapshotToTagEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsTagSnapshotToTagEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{29, 0}
}

func (x *TelematicsTagSnapshotToTagEdge_PrimaryKey) GetSource() *TelematicsTagSnapshotNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *TelematicsTagSnapshotToTagEdge_PrimaryKey) GetDestination() *TelematicsTagNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *TelematicsTagSnapshotToTagEdge_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *TelematicsVehicleSnapshotNode    `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *TelematicsConnectionSnapshotNode `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
	Time        *timestamppb.Timestamp            `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey) Reset() {
	*x = TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey) ProtoMessage() {}

func (x *TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{30, 0}
}

func (x *TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey) GetSource() *TelematicsVehicleSnapshotNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey) GetDestination() *TelematicsConnectionSnapshotNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *TelematicsVehicleSnapshotNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *TelematicsTagSnapshotNode     `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
	Time        *timestamppb.Timestamp         `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey) Reset() {
	*x = TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey) ProtoMessage() {}

func (x *TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{31, 0}
}

func (x *TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey) GetSource() *TelematicsVehicleSnapshotNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey) GetDestination() *TelematicsTagSnapshotNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *TelematicsVehicleSnapshotNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *TelematicsVehicleNode         `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
	Time        *timestamppb.Timestamp         `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey) Reset() {
	*x = TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey) ProtoMessage() {}

func (x *TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{32, 0}
}

func (x *TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey) GetSource() *TelematicsVehicleSnapshotNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey) GetDestination() *TelematicsVehicleNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

type TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source      *TelematicsVehicleSnapshotNode `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Destination *VINVehicleNode                `protobuf:"bytes,2,opt,name=destination,proto3" json:"destination,omitempty"`
	Time        *timestamppb.Timestamp         `protobuf:"bytes,3,opt,name=Time,proto3" json:"Time,omitempty"`
}

func (x *TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey) Reset() {
	*x = TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_proto_generated_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey) ProtoMessage() {}

func (x *TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_proto_generated_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey.ProtoReflect.Descriptor instead.
func (*TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey) Descriptor() ([]byte, []int) {
	return file_vehicles_service_proto_generated_proto_rawDescGZIP(), []int{33, 0}
}

func (x *TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey) GetSource() *TelematicsVehicleSnapshotNode {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey) GetDestination() *VINVehicleNode {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

var File_vehicles_service_proto_generated_proto protoreflect.FileDescriptor

var file_vehicles_service_proto_generated_proto_rawDesc = []byte{
	0x0a, 0x26, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x93, 0x01, 0x0a, 0x0a,
	0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x39, 0x0a, 0x03, 0x4b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79,
	0x52, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x17, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x1a, 0x28,
	0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x44, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xad, 0x01, 0x0a, 0x09, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x12,
	0x38, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x17, 0x0a, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x4f, 0x54, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x44, 0x4f, 0x54, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x1a, 0x26, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x49, 0x44, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xbb, 0x01, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x3e, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79,
	0x52, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x49,
	0x44, 0x12, 0x18, 0x0a, 0x07, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x49, 0x44, 0x1a, 0x32, 0x0a, 0x0a, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x24, 0x0a, 0x0d, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x22,
	0xb3, 0x01, 0x0a, 0x11, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x40, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x24, 0x0a, 0x0d, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x1a, 0x36, 0x0a,
	0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x28, 0x0a, 0x0f, 0x45,
	0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x49, 0x44, 0x22, 0xd1, 0x01, 0x0a, 0x1b, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x4a, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x38, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x6f, 0x64,
	0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65,
	0x79, 0x1a, 0x66, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12,
	0x28, 0x0a, 0x0f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x44, 0x12, 0x2e, 0x0a, 0x04, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xa5, 0x01, 0x0a, 0x14, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x12, 0x43, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0x48, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x28, 0x0a, 0x0f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x44, 0x12,
	0x10, 0x0a, 0x03, 0x56, 0x49, 0x4e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x56, 0x49,
	0x4e, 0x22, 0xbe, 0x05, 0x0a, 0x1e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x4e, 0x6f, 0x64, 0x65, 0x12, 0x4d, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e,
	0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03,
	0x4b, 0x65, 0x79, 0x12, 0x25, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x4d, 0x61,
	0x6b, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x4d, 0x61, 0x6b, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x02, 0x52, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x17,
	0x0a, 0x04, 0x59, 0x65, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x04,
	0x59, 0x65, 0x61, 0x72, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0c, 0x4d, 0x61, 0x6e, 0x75, 0x66,
	0x61, 0x63, 0x74, 0x75, 0x72, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52,
	0x0c, 0x4d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x17, 0x0a, 0x04, 0x54, 0x72, 0x69, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05,
	0x52, 0x04, 0x54, 0x72, 0x69, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x42, 0x6f, 0x64,
	0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x09,
	0x42, 0x6f, 0x64, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x07, 0x52, 0x0b, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x08, 0x52, 0x0b, 0x57, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0c, 0x53, 0x68,
	0x6f, 0x75, 0x6c, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x09, 0x52, 0x0c, 0x53, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x88, 0x01, 0x01, 0x1a, 0x78, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65,
	0x79, 0x12, 0x28, 0x0a, 0x0f, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x44, 0x12, 0x10, 0x0a, 0x03, 0x56,
	0x49, 0x4e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x56, 0x49, 0x4e, 0x12, 0x2e, 0x0a,
	0x04, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x07, 0x0a,
	0x05, 0x5f, 0x4d, 0x61, 0x6b, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x59, 0x65, 0x61, 0x72, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x4d, 0x61,
	0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72, 0x65, 0x72, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x54,
	0x72, 0x69, 0x6d, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x42, 0x6f, 0x64, 0x79, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x53, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x22, 0x93, 0x04, 0x0a, 0x0e, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x3d, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52,
	0x03, 0x4b, 0x65, 0x79, 0x12, 0x17, 0x0a, 0x04, 0x4d, 0x61, 0x6b, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x4d, 0x61, 0x6b, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a,
	0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x05,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x59, 0x65, 0x61, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x04, 0x59, 0x65, 0x61, 0x72, 0x88, 0x01,
	0x01, 0x12, 0x17, 0x0a, 0x04, 0x54, 0x72, 0x69, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x03, 0x52, 0x04, 0x54, 0x72, 0x69, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0e, 0x52, 0x61,
	0x77, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x04, 0x52, 0x0e, 0x52, 0x61, 0x77, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0c, 0x4d, 0x61, 0x6e, 0x75, 0x66,
	0x61, 0x63, 0x74, 0x75, 0x72, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52,
	0x0c, 0x4d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x21, 0x0a, 0x09, 0x42, 0x6f, 0x64, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x09, 0x42, 0x6f, 0x64, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x0b, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x08, 0x52, 0x0b, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x88, 0x01,
	0x01, 0x1a, 0x1e, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x56, 0x49, 0x4e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x56, 0x49,
	0x4e, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x4d, 0x61, 0x6b, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x59, 0x65, 0x61, 0x72, 0x42, 0x07, 0x0a,
	0x05, 0x5f, 0x54, 0x72, 0x69, 0x6d, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x52, 0x61, 0x77, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x4d, 0x61,
	0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72, 0x65, 0x72, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x42,
	0x6f, 0x64, 0x79, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x57, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x22, 0xeb, 0x01, 0x0a, 0x15, 0x56, 0x49, 0x4e,
	0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x4e, 0x6f,
	0x64, 0x65, 0x12, 0x44, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x62, 0x6c, 0x65, 0x6d, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x10, 0x4e, 0x48, 0x54, 0x53,
	0x41, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x10, 0x4e, 0x48, 0x54, 0x53, 0x41, 0x44, 0x65, 0x63, 0x6f, 0x64,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x06, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x88, 0x01, 0x01, 0x1a, 0x1e, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x4b, 0x65, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x56, 0x49, 0x4e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x56, 0x49, 0x4e, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x4e, 0x48, 0x54, 0x53, 0x41,
	0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x42, 0x09, 0x0a, 0x07, 0x5f,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x22, 0x87, 0x07, 0x0a, 0x16, 0x56, 0x49, 0x4e, 0x44, 0x65,
	0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64,
	0x65, 0x12, 0x45, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x0a, 0x53, 0x68, 0x6f, 0x75,
	0x6c, 0x64, 0x53, 0x6b, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0a,
	0x53, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x53, 0x6b, 0x69, 0x70, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a,
	0x0a, 0x49, 0x73, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x01, 0x52, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x73, 0x6f,
	0x6c, 0x76, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x56, 0x49, 0x4e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x08, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x56, 0x49, 0x4e, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x4d, 0x61, 0x6b, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x04, 0x4d, 0x61, 0x6b, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x19, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x05, 0x52, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04,
	0x59, 0x65, 0x61, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x48, 0x06, 0x52, 0x04, 0x59, 0x65,
	0x61, 0x72, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x42, 0x6f, 0x64, 0x79, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x09, 0x42, 0x6f, 0x64, 0x79,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x08, 0x52,
	0x0b, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x25, 0x0a, 0x0b, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x0b, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x16, 0x49, 0x53, 0x4f, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0a, 0x52, 0x16, 0x49, 0x53, 0x4f, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x16, 0x49, 0x53, 0x4f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x0b, 0x52, 0x16, 0x49, 0x53, 0x4f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x2f, 0x0a, 0x10, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x5a, 0x6f, 0x6e, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0c, 0x52, 0x10, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x5a, 0x6f, 0x6e, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x2b, 0x0a, 0x0e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x5a,
	0x6f, 0x6e, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0d, 0x52, 0x0e, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x5a, 0x6f, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x1a, 0x1e,
	0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x56, 0x49, 0x4e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x56, 0x49, 0x4e, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x53, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x53, 0x6b, 0x69, 0x70, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x49, 0x73, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x49, 0x73, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x56, 0x49, 0x4e, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x4d, 0x61, 0x6b,
	0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x59, 0x65, 0x61, 0x72, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x42, 0x6f, 0x64, 0x79, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x49, 0x53, 0x4f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x42, 0x19, 0x0a,
	0x17, 0x5f, 0x49, 0x53, 0x4f, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x5a, 0x6f, 0x6e, 0x65, 0x42, 0x11, 0x0a,
	0x0f, 0x5f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x5a, 0x6f, 0x6e, 0x65,
	0x22, 0x94, 0x03, 0x0a, 0x18, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x47, 0x0a,
	0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65,
	0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x38, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x3d, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48,
	0x00, 0x52, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12,
	0x18, 0x0a, 0x07, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x50, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x54, 0x53, 0x50, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x54, 0x53, 0x50, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x4f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x1a,
	0x28, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xcd, 0x01, 0x0a, 0x20, 0x54, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x4f, 0x0a, 0x03,
	0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0x58, 0x0a,
	0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x12, 0x2e, 0x0a, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x11, 0x54, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x40, 0x0a,
	0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x2e,
	0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a,
	0x3e, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x61, 0x67,
	0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x61, 0x67, 0x49, 0x44, 0x22,
	0xf7, 0x01, 0x0a, 0x19, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61,
	0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x48, 0x0a,
	0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x17, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x1a, 0x6e, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x1a,
	0x0a, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x61,
	0x67, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x61, 0x67, 0x49, 0x44,
	0x12, 0x2e, 0x0a, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa5, 0x01, 0x0a, 0x15, 0x54, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x12, 0x44, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0x46, 0x0a, 0x0a, 0x50, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x44,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49,
	0x44, 0x22, 0x8b, 0x04, 0x0a, 0x1d, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x12, 0x4c, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3a, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64,
	0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65,
	0x79, 0x12, 0x15, 0x0a, 0x03, 0x56, 0x49, 0x4e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x03, 0x56, 0x49, 0x4e, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0c, 0x43, 0x61, 0x6d, 0x65,
	0x72, 0x61, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01,
	0x52, 0x0c, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x88, 0x01,
	0x01, 0x12, 0x25, 0x0a, 0x0b, 0x4f, 0x64, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x47, 0x50, 0x53,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x02, 0x52, 0x0b, 0x4f, 0x64, 0x6f, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x47, 0x50, 0x53, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x4f, 0x64, 0x6f, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x4f, 0x42, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x48, 0x03, 0x52,
	0x0b, 0x4f, 0x64, 0x6f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4f, 0x42, 0x44, 0x88, 0x01, 0x01, 0x12,
	0x1f, 0x0a, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x04, 0x52, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x88, 0x01, 0x01,
	0x12, 0x21, 0x0a, 0x09, 0x49, 0x73, 0x46, 0x61, 0x6b, 0x65, 0x56, 0x49, 0x4e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x09, 0x49, 0x73, 0x46, 0x61, 0x6b, 0x65, 0x56, 0x49, 0x4e,
	0x88, 0x01, 0x01, 0x1a, 0x76, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65,
	0x79, 0x12, 0x1a, 0x0a, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x12, 0x1c, 0x0a,
	0x09, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x44, 0x12, 0x2e, 0x0a, 0x04, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x5f,
	0x56, 0x49, 0x4e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x53, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x4f, 0x64, 0x6f, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x47, 0x50, 0x53, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x4f, 0x64, 0x6f, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x4f, 0x42, 0x44, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x44, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x49, 0x73, 0x46, 0x61, 0x6b, 0x65, 0x56, 0x49, 0x4e, 0x22,
	0x99, 0x0b, 0x0a, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x41, 0x67, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x67, 0x65, 0x6e,
	0x63, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x06, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x33, 0x0a, 0x05, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x05,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x12, 0x45, 0x0a, 0x0b, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52,
	0x0b, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x0d,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x69, 0x0a, 0x17, 0x45, 0x71, 0x75,
	0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x17, 0x45, 0x71, 0x75,
	0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x54, 0x0a, 0x10, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e,
	0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x10, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x72, 0x0a, 0x1a, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x6f, 0x64, 0x65,
	0x48, 0x00, 0x52, 0x1a, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x42,
	0x0a, 0x0a, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x4e, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x12, 0x57, 0x0a, 0x11, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x62, 0x6c,
	0x65, 0x6d, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x11, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x12, 0x5a, 0x0a, 0x12, 0x56,
	0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65,
	0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64,
	0x65, 0x48, 0x00, 0x52, 0x12, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53,
	0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x60, 0x0a, 0x14, 0x54, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64,
	0x65, 0x48, 0x00, 0x52, 0x14, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x78, 0x0a, 0x1c, 0x54, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x1c, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x12, 0x4b, 0x0a, 0x0d, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x54, 0x61, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x48,
	0x00, 0x52, 0x0d, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67,
	0x12, 0x63, 0x0a, 0x15, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61,
	0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x15,
	0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x57, 0x0a, 0x11, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x11, 0x54, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x6f,
	0x0a, 0x19, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x48, 0x00, 0x52, 0x19, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x42,
	0x0a, 0x0a, 0x08, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0x8d, 0x02, 0x0a, 0x1f,
	0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x6f, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x45, 0x64, 0x67, 0x65, 0x12,
	0x4e, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x6f, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x45, 0x64, 0x67, 0x65, 0x2e,
	0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a,
	0x99, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x40,
	0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x49, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x89, 0x02, 0x0a, 0x21,
	0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65,
	0x6d, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64, 0x67,
	0x65, 0x12, 0x50, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e,
	0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x62,
	0x6c, 0x65, 0x6d, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x45,
	0x64, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03,
	0x4b, 0x65, 0x79, 0x1a, 0x91, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x12, 0x3f, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xeb, 0x01, 0x0a, 0x17, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x45,
	0x64, 0x67, 0x65, 0x12, 0x46, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x34, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x6f, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0x87, 0x01, 0x0a, 0x0a,
	0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x67,
	0x65, 0x6e, 0x63, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe8, 0x01, 0x0a, 0x16, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x45, 0x64, 0x67, 0x65,
	0x12, 0x45, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x46, 0x6c,
	0x65, 0x65, 0x74, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0x86, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x3d, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x80, 0x02, 0x0a, 0x1e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x64, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x64,
	0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b,
	0x65, 0x79, 0x1a, 0x8e, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65,
	0x79, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x43,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xf8, 0x01, 0x0a, 0x19, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e,
	0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x45, 0x64, 0x67,
	0x65, 0x12, 0x48, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0x90, 0x01, 0x0a, 0x0a,
	0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xba,
	0x02, 0x0a, 0x1d, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x64, 0x67, 0x65,
	0x12, 0x4c, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0xca,
	0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x45, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75,
	0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x04, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xd5, 0x02, 0x0a, 0x24,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x45, 0x64, 0x67, 0x65, 0x12, 0x53, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x41, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0xd7, 0x01, 0x0a, 0x0a, 0x50, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x48, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x4f, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70,
	0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0xd4, 0x02, 0x0a, 0x27, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e,
	0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x54, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64, 0x67, 0x65, 0x12,
	0x56, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0xd0, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x48, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x48, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65,
	0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x04, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x83, 0x02, 0x0a, 0x1f, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x45, 0x64, 0x67, 0x65, 0x12, 0x4e,
	0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0x8f,
	0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x42, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x3d, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xe4, 0x02, 0x0a, 0x2c, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x64, 0x67,
	0x65, 0x12, 0x5b, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49,
	0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0xd6,
	0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x4a, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x4c, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xe7, 0x02, 0x0a, 0x2d, 0x54, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x45, 0x64, 0x67, 0x65, 0x12, 0x5c, 0x0a, 0x03, 0x4b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54,
	0x6f, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0xd7, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x54, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2e, 0x0a, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0xba, 0x02, 0x0a, 0x1e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x54, 0x61, 0x67,
	0x45, 0x64, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54,
	0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x54, 0x61, 0x67, 0x45,
	0x64, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03,
	0x4b, 0x65, 0x79, 0x1a, 0xc8, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e,
	0x0a, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xf3,
	0x02, 0x0a, 0x31, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x45, 0x64, 0x67, 0x65, 0x12, 0x60, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x4e, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65,
	0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0xdb, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x54,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0xde, 0x02, 0x0a, 0x2a, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x54, 0x6f, 0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x45,
	0x64, 0x67, 0x65, 0x12, 0x59, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x47, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x54,
	0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0xd4,
	0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x47, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x4d, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x04, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xd2, 0x02, 0x0a, 0x26, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x54, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64, 0x67, 0x65,
	0x12, 0x55, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x52, 0x03, 0x4b, 0x65, 0x79, 0x1a, 0xd0, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x49, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x04, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xd1, 0x02, 0x0a, 0x29, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x45, 0x64, 0x67, 0x65, 0x12, 0x58, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64,
	0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x4b,
	0x65, 0x79, 0x1a, 0xc9, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65,
	0x79, 0x12, 0x47, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e,
	0x0a, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xb8,
	0x11, 0x0a, 0x04, 0x45, 0x64, 0x67, 0x65, 0x12, 0x75, 0x0a, 0x1b, 0x56, 0x49, 0x4e, 0x44, 0x65,
	0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x50,
	0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x6f, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x45, 0x64, 0x67, 0x65, 0x48,
	0x00, 0x52, 0x1b, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x6c,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x12, 0x7b,
	0x0a, 0x1d, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x62,
	0x6c, 0x65, 0x6d, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64, 0x67, 0x65, 0x48, 0x00, 0x52, 0x1d, 0x56, 0x49,
	0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x54,
	0x6f, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x5d, 0x0a, 0x13, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x41, 0x67, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x45,
	0x64, 0x67, 0x65, 0x48, 0x00, 0x52, 0x13, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x6f, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x5a, 0x0a, 0x12, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x46, 0x6c, 0x65, 0x65, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x45, 0x64, 0x67, 0x65,
	0x48, 0x00, 0x52, 0x12, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x6f, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x12, 0x72, 0x0a, 0x1a, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x64, 0x67, 0x65, 0x48, 0x00, 0x52, 0x1a,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x63, 0x0a, 0x15, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x6f,
	0x56, 0x49, 0x4e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75,
	0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x6f, 0x56,
	0x49, 0x4e, 0x45, 0x64, 0x67, 0x65, 0x48, 0x00, 0x52, 0x15, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x12,
	0x6f, 0x0a, 0x19, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x64, 0x67, 0x65, 0x48, 0x00, 0x52, 0x19, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x84, 0x01, 0x0a, 0x20, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45,
	0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x64, 0x67, 0x65, 0x48, 0x00, 0x52, 0x20, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x8d, 0x01, 0x0a, 0x23, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65,
	0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x54, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64, 0x67, 0x65,
	0x48, 0x00, 0x52, 0x23, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x6f,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x75, 0x0a, 0x1b, 0x54, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x6f, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x45, 0x64, 0x67, 0x65, 0x48,
	0x00, 0x52, 0x1b, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x12, 0x9c,
	0x01, 0x0a, 0x28, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54,
	0x6f, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3e, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x64, 0x67,
	0x65, 0x48, 0x00, 0x52, 0x28, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x9f, 0x01,
	0x0a, 0x29, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54,
	0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x45, 0x64,
	0x67, 0x65, 0x48, 0x00, 0x52, 0x29, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12,
	0x72, 0x0a, 0x1a, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61, 0x67,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x54, 0x61, 0x67, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x54, 0x61,
	0x67, 0x45, 0x64, 0x67, 0x65, 0x48, 0x00, 0x52, 0x1a, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f,
	0x54, 0x61, 0x67, 0x12, 0xab, 0x01, 0x0a, 0x2d, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x45, 0x64, 0x67, 0x65,
	0x48, 0x00, 0x52, 0x2d, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x12, 0x96, 0x01, 0x0a, 0x26, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54,
	0x6f, 0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54,
	0x6f, 0x54, 0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x45, 0x64, 0x67, 0x65,
	0x48, 0x00, 0x52, 0x26, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x54,
	0x61, 0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x8a, 0x01, 0x0a, 0x22, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x45, 0x64, 0x67,
	0x65, 0x48, 0x00, 0x52, 0x22, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x25, 0x54, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x45, 0x64, 0x67, 0x65, 0x48, 0x00, 0x52, 0x25, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x54, 0x6f, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x0a, 0x0a,
	0x08, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_vehicles_service_proto_generated_proto_rawDescOnce sync.Once
	file_vehicles_service_proto_generated_proto_rawDescData = file_vehicles_service_proto_generated_proto_rawDesc
)

func file_vehicles_service_proto_generated_proto_rawDescGZIP() []byte {
	file_vehicles_service_proto_generated_proto_rawDescOnce.Do(func() {
		file_vehicles_service_proto_generated_proto_rawDescData = protoimpl.X.CompressGZIP(file_vehicles_service_proto_generated_proto_rawDescData)
	})
	return file_vehicles_service_proto_generated_proto_rawDescData
}

var file_vehicles_service_proto_generated_proto_msgTypes = make([]protoimpl.MessageInfo, 68)
var file_vehicles_service_proto_generated_proto_goTypes = []interface{}{
	(*AgencyNode)(nil),                                        // 0: vehicles_service.AgencyNode
	(*FleetNode)(nil),                                         // 1: vehicles_service.FleetNode
	(*ApplicationNode)(nil),                                   // 2: vehicles_service.ApplicationNode
	(*EquipmentListNode)(nil),                                 // 3: vehicles_service.EquipmentListNode
	(*EquipmentListTimeseriesNode)(nil),                       // 4: vehicles_service.EquipmentListTimeseriesNode
	(*EquipmentVehicleNode)(nil),                              // 5: vehicles_service.EquipmentVehicleNode
	(*EquipmentVehicleTimeseriesNode)(nil),                    // 6: vehicles_service.EquipmentVehicleTimeseriesNode
	(*VINVehicleNode)(nil),                                    // 7: vehicles_service.VINVehicleNode
	(*VINDecoderProblemNode)(nil),                             // 8: vehicles_service.VINDecoderProblemNode
	(*VINDecoderSolutionNode)(nil),                            // 9: vehicles_service.VINDecoderSolutionNode
	(*TelematicsConnectionNode)(nil),                          // 10: vehicles_service.TelematicsConnectionNode
	(*TelematicsConnectionSnapshotNode)(nil),                  // 11: vehicles_service.TelematicsConnectionSnapshotNode
	(*TelematicsTagNode)(nil),                                 // 12: vehicles_service.TelematicsTagNode
	(*TelematicsTagSnapshotNode)(nil),                         // 13: vehicles_service.TelematicsTagSnapshotNode
	(*TelematicsVehicleNode)(nil),                             // 14: vehicles_service.TelematicsVehicleNode
	(*TelematicsVehicleSnapshotNode)(nil),                     // 15: vehicles_service.TelematicsVehicleSnapshotNode
	(*Node)(nil),                                              // 16: vehicles_service.Node
	(*VINDecoderSolutionToProblemEdge)(nil),                   // 17: vehicles_service.VINDecoderSolutionToProblemEdge
	(*VINDecoderProblemToVINVehicleEdge)(nil),                 // 18: vehicles_service.VINDecoderProblemToVINVehicleEdge
	(*ApplicationToAgencyEdge)(nil),                           // 19: vehicles_service.ApplicationToAgencyEdge
	(*ApplicationToFleetEdge)(nil),                            // 20: vehicles_service.ApplicationToFleetEdge
	(*EquipmentListToApplicationEdge)(nil),                    // 21: vehicles_service.EquipmentListToApplicationEdge
	(*EquipmentVehicleToVINEdge)(nil),                         // 22: vehicles_service.EquipmentVehicleToVINEdge
	(*EquipmentTimeseriesToListEdge)(nil),                     // 23: vehicles_service.EquipmentTimeseriesToListEdge
	(*EquipmentVehicleTimeseriesToListEdge)(nil),              // 24: vehicles_service.EquipmentVehicleTimeseriesToListEdge
	(*EquipmentVehicleTimeseriesToVehicleEdge)(nil),           // 25: vehicles_service.EquipmentVehicleTimeseriesToVehicleEdge
	(*TelematicsConnectionToFleetEdge)(nil),                   // 26: vehicles_service.TelematicsConnectionToFleetEdge
	(*TelematicsConnectionSnapshotToConnectionEdge)(nil),      // 27: vehicles_service.TelematicsConnectionSnapshotToConnectionEdge
	(*TelematicsTagSnapshotToConnectionSnapshotEdge)(nil),     // 28: vehicles_service.TelematicsTagSnapshotToConnectionSnapshotEdge
	(*TelematicsTagSnapshotToTagEdge)(nil),                    // 29: vehicles_service.TelematicsTagSnapshotToTagEdge
	(*TelematicsVehicleSnapshotToConnectionSnapshotEdge)(nil), // 30: vehicles_service.TelematicsVehicleSnapshotToConnectionSnapshotEdge
	(*TelematicsVehicleSnapshotToTagSnapshotEdge)(nil),        // 31: vehicles_service.TelematicsVehicleSnapshotToTagSnapshotEdge
	(*TelematicsVehicleSnapshotToVehicleEdge)(nil),            // 32: vehicles_service.TelematicsVehicleSnapshotToVehicleEdge
	(*TelematicsVehicleSnapshotToVINVehicleEdge)(nil),         // 33: vehicles_service.TelematicsVehicleSnapshotToVINVehicleEdge
	(*Edge)(nil),                                                         // 34: vehicles_service.Edge
	(*AgencyNode_PrimaryKey)(nil),                                        // 35: vehicles_service.AgencyNode.PrimaryKey
	(*FleetNode_PrimaryKey)(nil),                                         // 36: vehicles_service.FleetNode.PrimaryKey
	(*ApplicationNode_PrimaryKey)(nil),                                   // 37: vehicles_service.ApplicationNode.PrimaryKey
	(*EquipmentListNode_PrimaryKey)(nil),                                 // 38: vehicles_service.EquipmentListNode.PrimaryKey
	(*EquipmentListTimeseriesNode_PrimaryKey)(nil),                       // 39: vehicles_service.EquipmentListTimeseriesNode.PrimaryKey
	(*EquipmentVehicleNode_PrimaryKey)(nil),                              // 40: vehicles_service.EquipmentVehicleNode.PrimaryKey
	(*EquipmentVehicleTimeseriesNode_PrimaryKey)(nil),                    // 41: vehicles_service.EquipmentVehicleTimeseriesNode.PrimaryKey
	(*VINVehicleNode_PrimaryKey)(nil),                                    // 42: vehicles_service.VINVehicleNode.PrimaryKey
	(*VINDecoderProblemNode_PrimaryKey)(nil),                             // 43: vehicles_service.VINDecoderProblemNode.PrimaryKey
	(*VINDecoderSolutionNode_PrimaryKey)(nil),                            // 44: vehicles_service.VINDecoderSolutionNode.PrimaryKey
	(*TelematicsConnectionNode_PrimaryKey)(nil),                          // 45: vehicles_service.TelematicsConnectionNode.PrimaryKey
	(*TelematicsConnectionSnapshotNode_PrimaryKey)(nil),                  // 46: vehicles_service.TelematicsConnectionSnapshotNode.PrimaryKey
	(*TelematicsTagNode_PrimaryKey)(nil),                                 // 47: vehicles_service.TelematicsTagNode.PrimaryKey
	(*TelematicsTagSnapshotNode_PrimaryKey)(nil),                         // 48: vehicles_service.TelematicsTagSnapshotNode.PrimaryKey
	(*TelematicsVehicleNode_PrimaryKey)(nil),                             // 49: vehicles_service.TelematicsVehicleNode.PrimaryKey
	(*TelematicsVehicleSnapshotNode_PrimaryKey)(nil),                     // 50: vehicles_service.TelematicsVehicleSnapshotNode.PrimaryKey
	(*VINDecoderSolutionToProblemEdge_PrimaryKey)(nil),                   // 51: vehicles_service.VINDecoderSolutionToProblemEdge.PrimaryKey
	(*VINDecoderProblemToVINVehicleEdge_PrimaryKey)(nil),                 // 52: vehicles_service.VINDecoderProblemToVINVehicleEdge.PrimaryKey
	(*ApplicationToAgencyEdge_PrimaryKey)(nil),                           // 53: vehicles_service.ApplicationToAgencyEdge.PrimaryKey
	(*ApplicationToFleetEdge_PrimaryKey)(nil),                            // 54: vehicles_service.ApplicationToFleetEdge.PrimaryKey
	(*EquipmentListToApplicationEdge_PrimaryKey)(nil),                    // 55: vehicles_service.EquipmentListToApplicationEdge.PrimaryKey
	(*EquipmentVehicleToVINEdge_PrimaryKey)(nil),                         // 56: vehicles_service.EquipmentVehicleToVINEdge.PrimaryKey
	(*EquipmentTimeseriesToListEdge_PrimaryKey)(nil),                     // 57: vehicles_service.EquipmentTimeseriesToListEdge.PrimaryKey
	(*EquipmentVehicleTimeseriesToListEdge_PrimaryKey)(nil),              // 58: vehicles_service.EquipmentVehicleTimeseriesToListEdge.PrimaryKey
	(*EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey)(nil),           // 59: vehicles_service.EquipmentVehicleTimeseriesToVehicleEdge.PrimaryKey
	(*TelematicsConnectionToFleetEdge_PrimaryKey)(nil),                   // 60: vehicles_service.TelematicsConnectionToFleetEdge.PrimaryKey
	(*TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey)(nil),      // 61: vehicles_service.TelematicsConnectionSnapshotToConnectionEdge.PrimaryKey
	(*TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey)(nil),     // 62: vehicles_service.TelematicsTagSnapshotToConnectionSnapshotEdge.PrimaryKey
	(*TelematicsTagSnapshotToTagEdge_PrimaryKey)(nil),                    // 63: vehicles_service.TelematicsTagSnapshotToTagEdge.PrimaryKey
	(*TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey)(nil), // 64: vehicles_service.TelematicsVehicleSnapshotToConnectionSnapshotEdge.PrimaryKey
	(*TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey)(nil),        // 65: vehicles_service.TelematicsVehicleSnapshotToTagSnapshotEdge.PrimaryKey
	(*TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey)(nil),            // 66: vehicles_service.TelematicsVehicleSnapshotToVehicleEdge.PrimaryKey
	(*TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey)(nil),         // 67: vehicles_service.TelematicsVehicleSnapshotToVINVehicleEdge.PrimaryKey
	(*timestamppb.Timestamp)(nil),                                        // 68: google.protobuf.Timestamp
}
var file_vehicles_service_proto_generated_proto_depIdxs = []int32{
	35,  // 0: vehicles_service.AgencyNode.Key:type_name -> vehicles_service.AgencyNode.PrimaryKey
	36,  // 1: vehicles_service.FleetNode.Key:type_name -> vehicles_service.FleetNode.PrimaryKey
	37,  // 2: vehicles_service.ApplicationNode.Key:type_name -> vehicles_service.ApplicationNode.PrimaryKey
	38,  // 3: vehicles_service.EquipmentListNode.Key:type_name -> vehicles_service.EquipmentListNode.PrimaryKey
	39,  // 4: vehicles_service.EquipmentListTimeseriesNode.Key:type_name -> vehicles_service.EquipmentListTimeseriesNode.PrimaryKey
	40,  // 5: vehicles_service.EquipmentVehicleNode.Key:type_name -> vehicles_service.EquipmentVehicleNode.PrimaryKey
	41,  // 6: vehicles_service.EquipmentVehicleTimeseriesNode.Key:type_name -> vehicles_service.EquipmentVehicleTimeseriesNode.PrimaryKey
	42,  // 7: vehicles_service.VINVehicleNode.Key:type_name -> vehicles_service.VINVehicleNode.PrimaryKey
	43,  // 8: vehicles_service.VINDecoderProblemNode.Key:type_name -> vehicles_service.VINDecoderProblemNode.PrimaryKey
	44,  // 9: vehicles_service.VINDecoderSolutionNode.Key:type_name -> vehicles_service.VINDecoderSolutionNode.PrimaryKey
	45,  // 10: vehicles_service.TelematicsConnectionNode.Key:type_name -> vehicles_service.TelematicsConnectionNode.PrimaryKey
	68,  // 11: vehicles_service.TelematicsConnectionNode.CreatedAt:type_name -> google.protobuf.Timestamp
	68,  // 12: vehicles_service.TelematicsConnectionNode.DeletedAt:type_name -> google.protobuf.Timestamp
	46,  // 13: vehicles_service.TelematicsConnectionSnapshotNode.Key:type_name -> vehicles_service.TelematicsConnectionSnapshotNode.PrimaryKey
	47,  // 14: vehicles_service.TelematicsTagNode.Key:type_name -> vehicles_service.TelematicsTagNode.PrimaryKey
	48,  // 15: vehicles_service.TelematicsTagSnapshotNode.Key:type_name -> vehicles_service.TelematicsTagSnapshotNode.PrimaryKey
	49,  // 16: vehicles_service.TelematicsVehicleNode.Key:type_name -> vehicles_service.TelematicsVehicleNode.PrimaryKey
	50,  // 17: vehicles_service.TelematicsVehicleSnapshotNode.Key:type_name -> vehicles_service.TelematicsVehicleSnapshotNode.PrimaryKey
	0,   // 18: vehicles_service.Node.Agency:type_name -> vehicles_service.AgencyNode
	1,   // 19: vehicles_service.Node.Fleet:type_name -> vehicles_service.FleetNode
	2,   // 20: vehicles_service.Node.Application:type_name -> vehicles_service.ApplicationNode
	3,   // 21: vehicles_service.Node.EquipmentList:type_name -> vehicles_service.EquipmentListNode
	4,   // 22: vehicles_service.Node.EquipmentListTimeseries:type_name -> vehicles_service.EquipmentListTimeseriesNode
	5,   // 23: vehicles_service.Node.EquipmentVehicle:type_name -> vehicles_service.EquipmentVehicleNode
	6,   // 24: vehicles_service.Node.EquipmentVehicleTimeseries:type_name -> vehicles_service.EquipmentVehicleTimeseriesNode
	7,   // 25: vehicles_service.Node.VINVehicle:type_name -> vehicles_service.VINVehicleNode
	8,   // 26: vehicles_service.Node.VINDecoderProblem:type_name -> vehicles_service.VINDecoderProblemNode
	9,   // 27: vehicles_service.Node.VINDecoderSolution:type_name -> vehicles_service.VINDecoderSolutionNode
	10,  // 28: vehicles_service.Node.TelematicsConnection:type_name -> vehicles_service.TelematicsConnectionNode
	11,  // 29: vehicles_service.Node.TelematicsConnectionSnapshot:type_name -> vehicles_service.TelematicsConnectionSnapshotNode
	12,  // 30: vehicles_service.Node.TelematicsTag:type_name -> vehicles_service.TelematicsTagNode
	13,  // 31: vehicles_service.Node.TelematicsTagSnapshot:type_name -> vehicles_service.TelematicsTagSnapshotNode
	14,  // 32: vehicles_service.Node.TelematicsVehicle:type_name -> vehicles_service.TelematicsVehicleNode
	15,  // 33: vehicles_service.Node.TelematicsVehicleSnapshot:type_name -> vehicles_service.TelematicsVehicleSnapshotNode
	51,  // 34: vehicles_service.VINDecoderSolutionToProblemEdge.Key:type_name -> vehicles_service.VINDecoderSolutionToProblemEdge.PrimaryKey
	52,  // 35: vehicles_service.VINDecoderProblemToVINVehicleEdge.Key:type_name -> vehicles_service.VINDecoderProblemToVINVehicleEdge.PrimaryKey
	53,  // 36: vehicles_service.ApplicationToAgencyEdge.Key:type_name -> vehicles_service.ApplicationToAgencyEdge.PrimaryKey
	54,  // 37: vehicles_service.ApplicationToFleetEdge.Key:type_name -> vehicles_service.ApplicationToFleetEdge.PrimaryKey
	55,  // 38: vehicles_service.EquipmentListToApplicationEdge.Key:type_name -> vehicles_service.EquipmentListToApplicationEdge.PrimaryKey
	56,  // 39: vehicles_service.EquipmentVehicleToVINEdge.Key:type_name -> vehicles_service.EquipmentVehicleToVINEdge.PrimaryKey
	57,  // 40: vehicles_service.EquipmentTimeseriesToListEdge.Key:type_name -> vehicles_service.EquipmentTimeseriesToListEdge.PrimaryKey
	58,  // 41: vehicles_service.EquipmentVehicleTimeseriesToListEdge.Key:type_name -> vehicles_service.EquipmentVehicleTimeseriesToListEdge.PrimaryKey
	59,  // 42: vehicles_service.EquipmentVehicleTimeseriesToVehicleEdge.Key:type_name -> vehicles_service.EquipmentVehicleTimeseriesToVehicleEdge.PrimaryKey
	60,  // 43: vehicles_service.TelematicsConnectionToFleetEdge.Key:type_name -> vehicles_service.TelematicsConnectionToFleetEdge.PrimaryKey
	61,  // 44: vehicles_service.TelematicsConnectionSnapshotToConnectionEdge.Key:type_name -> vehicles_service.TelematicsConnectionSnapshotToConnectionEdge.PrimaryKey
	62,  // 45: vehicles_service.TelematicsTagSnapshotToConnectionSnapshotEdge.Key:type_name -> vehicles_service.TelematicsTagSnapshotToConnectionSnapshotEdge.PrimaryKey
	63,  // 46: vehicles_service.TelematicsTagSnapshotToTagEdge.Key:type_name -> vehicles_service.TelematicsTagSnapshotToTagEdge.PrimaryKey
	64,  // 47: vehicles_service.TelematicsVehicleSnapshotToConnectionSnapshotEdge.Key:type_name -> vehicles_service.TelematicsVehicleSnapshotToConnectionSnapshotEdge.PrimaryKey
	65,  // 48: vehicles_service.TelematicsVehicleSnapshotToTagSnapshotEdge.Key:type_name -> vehicles_service.TelematicsVehicleSnapshotToTagSnapshotEdge.PrimaryKey
	66,  // 49: vehicles_service.TelematicsVehicleSnapshotToVehicleEdge.Key:type_name -> vehicles_service.TelematicsVehicleSnapshotToVehicleEdge.PrimaryKey
	67,  // 50: vehicles_service.TelematicsVehicleSnapshotToVINVehicleEdge.Key:type_name -> vehicles_service.TelematicsVehicleSnapshotToVINVehicleEdge.PrimaryKey
	17,  // 51: vehicles_service.Edge.VINDecoderSolutionToProblem:type_name -> vehicles_service.VINDecoderSolutionToProblemEdge
	18,  // 52: vehicles_service.Edge.VINDecoderProblemToVINVehicle:type_name -> vehicles_service.VINDecoderProblemToVINVehicleEdge
	19,  // 53: vehicles_service.Edge.ApplicationToAgency:type_name -> vehicles_service.ApplicationToAgencyEdge
	20,  // 54: vehicles_service.Edge.ApplicationToFleet:type_name -> vehicles_service.ApplicationToFleetEdge
	21,  // 55: vehicles_service.Edge.EquipmentListToApplication:type_name -> vehicles_service.EquipmentListToApplicationEdge
	22,  // 56: vehicles_service.Edge.EquipmentVehicleToVIN:type_name -> vehicles_service.EquipmentVehicleToVINEdge
	23,  // 57: vehicles_service.Edge.EquipmentTimeseriesToList:type_name -> vehicles_service.EquipmentTimeseriesToListEdge
	24,  // 58: vehicles_service.Edge.EquipmentVehicleTimeseriesToList:type_name -> vehicles_service.EquipmentVehicleTimeseriesToListEdge
	25,  // 59: vehicles_service.Edge.EquipmentVehicleTimeseriesToVehicle:type_name -> vehicles_service.EquipmentVehicleTimeseriesToVehicleEdge
	26,  // 60: vehicles_service.Edge.TelematicsConnectionToFleet:type_name -> vehicles_service.TelematicsConnectionToFleetEdge
	27,  // 61: vehicles_service.Edge.TelematicsConnectionSnapshotToConnection:type_name -> vehicles_service.TelematicsConnectionSnapshotToConnectionEdge
	28,  // 62: vehicles_service.Edge.TelematicsTagSnapshotToConnectionSnapshot:type_name -> vehicles_service.TelematicsTagSnapshotToConnectionSnapshotEdge
	29,  // 63: vehicles_service.Edge.TelematicsTagSnapshotToTag:type_name -> vehicles_service.TelematicsTagSnapshotToTagEdge
	30,  // 64: vehicles_service.Edge.TelematicsVehicleSnapshotToConnectionSnapshot:type_name -> vehicles_service.TelematicsVehicleSnapshotToConnectionSnapshotEdge
	31,  // 65: vehicles_service.Edge.TelematicsVehicleSnapshotToTagSnapshot:type_name -> vehicles_service.TelematicsVehicleSnapshotToTagSnapshotEdge
	32,  // 66: vehicles_service.Edge.TelematicsVehicleSnapshotToVehicle:type_name -> vehicles_service.TelematicsVehicleSnapshotToVehicleEdge
	33,  // 67: vehicles_service.Edge.TelematicsVehicleSnapshotToVINVehicle:type_name -> vehicles_service.TelematicsVehicleSnapshotToVINVehicleEdge
	68,  // 68: vehicles_service.EquipmentListTimeseriesNode.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	68,  // 69: vehicles_service.EquipmentVehicleTimeseriesNode.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	68,  // 70: vehicles_service.TelematicsConnectionSnapshotNode.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	68,  // 71: vehicles_service.TelematicsTagSnapshotNode.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	68,  // 72: vehicles_service.TelematicsVehicleSnapshotNode.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	9,   // 73: vehicles_service.VINDecoderSolutionToProblemEdge.PrimaryKey.source:type_name -> vehicles_service.VINDecoderSolutionNode
	8,   // 74: vehicles_service.VINDecoderSolutionToProblemEdge.PrimaryKey.destination:type_name -> vehicles_service.VINDecoderProblemNode
	8,   // 75: vehicles_service.VINDecoderProblemToVINVehicleEdge.PrimaryKey.source:type_name -> vehicles_service.VINDecoderProblemNode
	7,   // 76: vehicles_service.VINDecoderProblemToVINVehicleEdge.PrimaryKey.destination:type_name -> vehicles_service.VINVehicleNode
	2,   // 77: vehicles_service.ApplicationToAgencyEdge.PrimaryKey.source:type_name -> vehicles_service.ApplicationNode
	0,   // 78: vehicles_service.ApplicationToAgencyEdge.PrimaryKey.destination:type_name -> vehicles_service.AgencyNode
	2,   // 79: vehicles_service.ApplicationToFleetEdge.PrimaryKey.source:type_name -> vehicles_service.ApplicationNode
	1,   // 80: vehicles_service.ApplicationToFleetEdge.PrimaryKey.destination:type_name -> vehicles_service.FleetNode
	3,   // 81: vehicles_service.EquipmentListToApplicationEdge.PrimaryKey.source:type_name -> vehicles_service.EquipmentListNode
	2,   // 82: vehicles_service.EquipmentListToApplicationEdge.PrimaryKey.destination:type_name -> vehicles_service.ApplicationNode
	5,   // 83: vehicles_service.EquipmentVehicleToVINEdge.PrimaryKey.source:type_name -> vehicles_service.EquipmentVehicleNode
	7,   // 84: vehicles_service.EquipmentVehicleToVINEdge.PrimaryKey.destination:type_name -> vehicles_service.VINVehicleNode
	4,   // 85: vehicles_service.EquipmentTimeseriesToListEdge.PrimaryKey.source:type_name -> vehicles_service.EquipmentListTimeseriesNode
	3,   // 86: vehicles_service.EquipmentTimeseriesToListEdge.PrimaryKey.destination:type_name -> vehicles_service.EquipmentListNode
	68,  // 87: vehicles_service.EquipmentTimeseriesToListEdge.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	6,   // 88: vehicles_service.EquipmentVehicleTimeseriesToListEdge.PrimaryKey.source:type_name -> vehicles_service.EquipmentVehicleTimeseriesNode
	4,   // 89: vehicles_service.EquipmentVehicleTimeseriesToListEdge.PrimaryKey.destination:type_name -> vehicles_service.EquipmentListTimeseriesNode
	68,  // 90: vehicles_service.EquipmentVehicleTimeseriesToListEdge.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	6,   // 91: vehicles_service.EquipmentVehicleTimeseriesToVehicleEdge.PrimaryKey.source:type_name -> vehicles_service.EquipmentVehicleTimeseriesNode
	5,   // 92: vehicles_service.EquipmentVehicleTimeseriesToVehicleEdge.PrimaryKey.destination:type_name -> vehicles_service.EquipmentVehicleNode
	68,  // 93: vehicles_service.EquipmentVehicleTimeseriesToVehicleEdge.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	10,  // 94: vehicles_service.TelematicsConnectionToFleetEdge.PrimaryKey.source:type_name -> vehicles_service.TelematicsConnectionNode
	1,   // 95: vehicles_service.TelematicsConnectionToFleetEdge.PrimaryKey.destination:type_name -> vehicles_service.FleetNode
	11,  // 96: vehicles_service.TelematicsConnectionSnapshotToConnectionEdge.PrimaryKey.source:type_name -> vehicles_service.TelematicsConnectionSnapshotNode
	10,  // 97: vehicles_service.TelematicsConnectionSnapshotToConnectionEdge.PrimaryKey.destination:type_name -> vehicles_service.TelematicsConnectionNode
	68,  // 98: vehicles_service.TelematicsConnectionSnapshotToConnectionEdge.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	13,  // 99: vehicles_service.TelematicsTagSnapshotToConnectionSnapshotEdge.PrimaryKey.source:type_name -> vehicles_service.TelematicsTagSnapshotNode
	11,  // 100: vehicles_service.TelematicsTagSnapshotToConnectionSnapshotEdge.PrimaryKey.destination:type_name -> vehicles_service.TelematicsConnectionSnapshotNode
	68,  // 101: vehicles_service.TelematicsTagSnapshotToConnectionSnapshotEdge.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	13,  // 102: vehicles_service.TelematicsTagSnapshotToTagEdge.PrimaryKey.source:type_name -> vehicles_service.TelematicsTagSnapshotNode
	12,  // 103: vehicles_service.TelematicsTagSnapshotToTagEdge.PrimaryKey.destination:type_name -> vehicles_service.TelematicsTagNode
	68,  // 104: vehicles_service.TelematicsTagSnapshotToTagEdge.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	15,  // 105: vehicles_service.TelematicsVehicleSnapshotToConnectionSnapshotEdge.PrimaryKey.source:type_name -> vehicles_service.TelematicsVehicleSnapshotNode
	11,  // 106: vehicles_service.TelematicsVehicleSnapshotToConnectionSnapshotEdge.PrimaryKey.destination:type_name -> vehicles_service.TelematicsConnectionSnapshotNode
	68,  // 107: vehicles_service.TelematicsVehicleSnapshotToConnectionSnapshotEdge.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	15,  // 108: vehicles_service.TelematicsVehicleSnapshotToTagSnapshotEdge.PrimaryKey.source:type_name -> vehicles_service.TelematicsVehicleSnapshotNode
	13,  // 109: vehicles_service.TelematicsVehicleSnapshotToTagSnapshotEdge.PrimaryKey.destination:type_name -> vehicles_service.TelematicsTagSnapshotNode
	68,  // 110: vehicles_service.TelematicsVehicleSnapshotToTagSnapshotEdge.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	15,  // 111: vehicles_service.TelematicsVehicleSnapshotToVehicleEdge.PrimaryKey.source:type_name -> vehicles_service.TelematicsVehicleSnapshotNode
	14,  // 112: vehicles_service.TelematicsVehicleSnapshotToVehicleEdge.PrimaryKey.destination:type_name -> vehicles_service.TelematicsVehicleNode
	68,  // 113: vehicles_service.TelematicsVehicleSnapshotToVehicleEdge.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	15,  // 114: vehicles_service.TelematicsVehicleSnapshotToVINVehicleEdge.PrimaryKey.source:type_name -> vehicles_service.TelematicsVehicleSnapshotNode
	7,   // 115: vehicles_service.TelematicsVehicleSnapshotToVINVehicleEdge.PrimaryKey.destination:type_name -> vehicles_service.VINVehicleNode
	68,  // 116: vehicles_service.TelematicsVehicleSnapshotToVINVehicleEdge.PrimaryKey.Time:type_name -> google.protobuf.Timestamp
	117, // [117:117] is the sub-list for method output_type
	117, // [117:117] is the sub-list for method input_type
	117, // [117:117] is the sub-list for extension type_name
	117, // [117:117] is the sub-list for extension extendee
	0,   // [0:117] is the sub-list for field type_name
}

func init() { file_vehicles_service_proto_generated_proto_init() }
func file_vehicles_service_proto_generated_proto_init() {
	if File_vehicles_service_proto_generated_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_vehicles_service_proto_generated_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgencyNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicationNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentListNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentListTimeseriesNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentVehicleNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentVehicleTimeseriesNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINVehicleNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDecoderProblemNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDecoderSolutionNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsConnectionNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsConnectionSnapshotNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsTagNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsTagSnapshotNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleSnapshotNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Node); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDecoderSolutionToProblemEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDecoderProblemToVINVehicleEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicationToAgencyEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicationToFleetEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentListToApplicationEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentVehicleToVINEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentTimeseriesToListEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentVehicleTimeseriesToListEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentVehicleTimeseriesToVehicleEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsConnectionToFleetEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsConnectionSnapshotToConnectionEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsTagSnapshotToConnectionSnapshotEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsTagSnapshotToTagEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleSnapshotToConnectionSnapshotEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleSnapshotToTagSnapshotEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleSnapshotToVehicleEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleSnapshotToVINVehicleEdge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Edge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgencyNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FleetNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicationNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentListNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentListTimeseriesNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentVehicleNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentVehicleTimeseriesNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINVehicleNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDecoderProblemNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDecoderSolutionNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsConnectionNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsConnectionSnapshotNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsTagNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsTagSnapshotNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleSnapshotNode_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDecoderSolutionToProblemEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDecoderProblemToVINVehicleEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicationToAgencyEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicationToFleetEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentListToApplicationEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentVehicleToVINEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentTimeseriesToListEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentVehicleTimeseriesToListEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquipmentVehicleTimeseriesToVehicleEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsConnectionToFleetEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsConnectionSnapshotToConnectionEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsTagSnapshotToConnectionSnapshotEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsTagSnapshotToTagEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleSnapshotToConnectionSnapshotEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleSnapshotToTagSnapshotEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleSnapshotToVehicleEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_proto_generated_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TelematicsVehicleSnapshotToVINVehicleEdge_PrimaryKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_vehicles_service_proto_generated_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_vehicles_service_proto_generated_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_vehicles_service_proto_generated_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_vehicles_service_proto_generated_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_vehicles_service_proto_generated_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_vehicles_service_proto_generated_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_vehicles_service_proto_generated_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_vehicles_service_proto_generated_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_vehicles_service_proto_generated_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_vehicles_service_proto_generated_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*Node_Agency)(nil),
		(*Node_Fleet)(nil),
		(*Node_Application)(nil),
		(*Node_EquipmentList)(nil),
		(*Node_EquipmentListTimeseries)(nil),
		(*Node_EquipmentVehicle)(nil),
		(*Node_EquipmentVehicleTimeseries)(nil),
		(*Node_VINVehicle)(nil),
		(*Node_VINDecoderProblem)(nil),
		(*Node_VINDecoderSolution)(nil),
		(*Node_TelematicsConnection)(nil),
		(*Node_TelematicsConnectionSnapshot)(nil),
		(*Node_TelematicsTag)(nil),
		(*Node_TelematicsTagSnapshot)(nil),
		(*Node_TelematicsVehicle)(nil),
		(*Node_TelematicsVehicleSnapshot)(nil),
	}
	file_vehicles_service_proto_generated_proto_msgTypes[34].OneofWrappers = []interface{}{
		(*Edge_VINDecoderSolutionToProblem)(nil),
		(*Edge_VINDecoderProblemToVINVehicle)(nil),
		(*Edge_ApplicationToAgency)(nil),
		(*Edge_ApplicationToFleet)(nil),
		(*Edge_EquipmentListToApplication)(nil),
		(*Edge_EquipmentVehicleToVIN)(nil),
		(*Edge_EquipmentTimeseriesToList)(nil),
		(*Edge_EquipmentVehicleTimeseriesToList)(nil),
		(*Edge_EquipmentVehicleTimeseriesToVehicle)(nil),
		(*Edge_TelematicsConnectionToFleet)(nil),
		(*Edge_TelematicsConnectionSnapshotToConnection)(nil),
		(*Edge_TelematicsTagSnapshotToConnectionSnapshot)(nil),
		(*Edge_TelematicsTagSnapshotToTag)(nil),
		(*Edge_TelematicsVehicleSnapshotToConnectionSnapshot)(nil),
		(*Edge_TelematicsVehicleSnapshotToTagSnapshot)(nil),
		(*Edge_TelematicsVehicleSnapshotToVehicle)(nil),
		(*Edge_TelematicsVehicleSnapshotToVINVehicle)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_vehicles_service_proto_generated_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   68,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_vehicles_service_proto_generated_proto_goTypes,
		DependencyIndexes: file_vehicles_service_proto_generated_proto_depIdxs,
		MessageInfos:      file_vehicles_service_proto_generated_proto_msgTypes,
	}.Build()
	File_vehicles_service_proto_generated_proto = out.File
	file_vehicles_service_proto_generated_proto_rawDesc = nil
	file_vehicles_service_proto_generated_proto_goTypes = nil
	file_vehicles_service_proto_generated_proto_depIdxs = nil
}
