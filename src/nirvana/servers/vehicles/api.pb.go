// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: vehicles_service/api.proto

package vehicles

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ROQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query string `protobuf:"bytes,1,opt,name=Query,proto3" json:"Query,omitempty"`
}

func (x *ROQueryRequest) Reset() {
	*x = ROQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ROQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ROQueryRequest) ProtoMessage() {}

func (x *ROQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ROQueryRequest.ProtoReflect.Descriptor instead.
func (*ROQueryRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{0}
}

func (x *ROQueryRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

type ROQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scalars []*Scalar `protobuf:"bytes,1,rep,name=Scalars,proto3" json:"Scalars,omitempty"`
	Nodes   []*Node   `protobuf:"bytes,2,rep,name=Nodes,proto3" json:"Nodes,omitempty"`
	Edges   []*Edge   `protobuf:"bytes,3,rep,name=Edges,proto3" json:"Edges,omitempty"`
}

func (x *ROQueryResponse) Reset() {
	*x = ROQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ROQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ROQueryResponse) ProtoMessage() {}

func (x *ROQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ROQueryResponse.ProtoReflect.Descriptor instead.
func (*ROQueryResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{1}
}

func (x *ROQueryResponse) GetScalars() []*Scalar {
	if x != nil {
		return x.Scalars
	}
	return nil
}

func (x *ROQueryResponse) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *ROQueryResponse) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

type GetNodesEdgesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*Node `protobuf:"bytes,1,rep,name=Nodes,proto3" json:"Nodes,omitempty"`
	Edges []*Edge `protobuf:"bytes,2,rep,name=Edges,proto3" json:"Edges,omitempty"`
}

func (x *GetNodesEdgesRequest) Reset() {
	*x = GetNodesEdgesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodesEdgesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodesEdgesRequest) ProtoMessage() {}

func (x *GetNodesEdgesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodesEdgesRequest.ProtoReflect.Descriptor instead.
func (*GetNodesEdgesRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetNodesEdgesRequest) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *GetNodesEdgesRequest) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

type GetNodesEdgesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*Node `protobuf:"bytes,1,rep,name=Nodes,proto3" json:"Nodes,omitempty"`
	Edges []*Edge `protobuf:"bytes,2,rep,name=Edges,proto3" json:"Edges,omitempty"`
}

func (x *GetNodesEdgesResponse) Reset() {
	*x = GetNodesEdgesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodesEdgesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodesEdgesResponse) ProtoMessage() {}

func (x *GetNodesEdgesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodesEdgesResponse.ProtoReflect.Descriptor instead.
func (*GetNodesEdgesResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetNodesEdgesResponse) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *GetNodesEdgesResponse) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

type UpsertNodesEdgesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*Node `protobuf:"bytes,1,rep,name=Nodes,proto3" json:"Nodes,omitempty"`
	Edges []*Edge `protobuf:"bytes,2,rep,name=Edges,proto3" json:"Edges,omitempty"`
}

func (x *UpsertNodesEdgesRequest) Reset() {
	*x = UpsertNodesEdgesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertNodesEdgesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertNodesEdgesRequest) ProtoMessage() {}

func (x *UpsertNodesEdgesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertNodesEdgesRequest.ProtoReflect.Descriptor instead.
func (*UpsertNodesEdgesRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpsertNodesEdgesRequest) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *UpsertNodesEdgesRequest) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

type UpsertNodesEdgesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*Node `protobuf:"bytes,1,rep,name=Nodes,proto3" json:"Nodes,omitempty"`
	Edges []*Edge `protobuf:"bytes,2,rep,name=Edges,proto3" json:"Edges,omitempty"`
}

func (x *UpsertNodesEdgesResponse) Reset() {
	*x = UpsertNodesEdgesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertNodesEdgesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertNodesEdgesResponse) ProtoMessage() {}

func (x *UpsertNodesEdgesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertNodesEdgesResponse.ProtoReflect.Descriptor instead.
func (*UpsertNodesEdgesResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{5}
}

func (x *UpsertNodesEdgesResponse) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *UpsertNodesEdgesResponse) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

type DeleteNodesEdgesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*Node `protobuf:"bytes,1,rep,name=Nodes,proto3" json:"Nodes,omitempty"`
	Edges []*Edge `protobuf:"bytes,2,rep,name=Edges,proto3" json:"Edges,omitempty"`
}

func (x *DeleteNodesEdgesRequest) Reset() {
	*x = DeleteNodesEdgesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteNodesEdgesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNodesEdgesRequest) ProtoMessage() {}

func (x *DeleteNodesEdgesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNodesEdgesRequest.ProtoReflect.Descriptor instead.
func (*DeleteNodesEdgesRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteNodesEdgesRequest) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *DeleteNodesEdgesRequest) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

type DeleteNodesEdgesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteNodesEdgesResponse) Reset() {
	*x = DeleteNodesEdgesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteNodesEdgesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNodesEdgesResponse) ProtoMessage() {}

func (x *DeleteNodesEdgesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNodesEdgesResponse.ProtoReflect.Descriptor instead.
func (*DeleteNodesEdgesResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{7}
}

type UpsertVINDecoderVehiclesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vehicles  []*VINVehicleNode         `protobuf:"bytes,1,rep,name=Vehicles,proto3" json:"Vehicles,omitempty"`
	Problems  []*VINDecoderProblemNode  `protobuf:"bytes,2,rep,name=Problems,proto3" json:"Problems,omitempty"`
	Solutions []*VINDecoderSolutionNode `protobuf:"bytes,3,rep,name=Solutions,proto3" json:"Solutions,omitempty"`
}

func (x *UpsertVINDecoderVehiclesRequest) Reset() {
	*x = UpsertVINDecoderVehiclesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertVINDecoderVehiclesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertVINDecoderVehiclesRequest) ProtoMessage() {}

func (x *UpsertVINDecoderVehiclesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertVINDecoderVehiclesRequest.ProtoReflect.Descriptor instead.
func (*UpsertVINDecoderVehiclesRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{8}
}

func (x *UpsertVINDecoderVehiclesRequest) GetVehicles() []*VINVehicleNode {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

func (x *UpsertVINDecoderVehiclesRequest) GetProblems() []*VINDecoderProblemNode {
	if x != nil {
		return x.Problems
	}
	return nil
}

func (x *UpsertVINDecoderVehiclesRequest) GetSolutions() []*VINDecoderSolutionNode {
	if x != nil {
		return x.Solutions
	}
	return nil
}

type UpsertVINDecoderVehiclesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpsertVINDecoderVehiclesResponse) Reset() {
	*x = UpsertVINDecoderVehiclesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertVINDecoderVehiclesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertVINDecoderVehiclesResponse) ProtoMessage() {}

func (x *UpsertVINDecoderVehiclesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertVINDecoderVehiclesResponse.ProtoReflect.Descriptor instead.
func (*UpsertVINDecoderVehiclesResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{9}
}

type GetVINDecoderVehiclesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VINs []string `protobuf:"bytes,1,rep,name=VINs,proto3" json:"VINs,omitempty"`
}

func (x *GetVINDecoderVehiclesRequest) Reset() {
	*x = GetVINDecoderVehiclesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVINDecoderVehiclesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVINDecoderVehiclesRequest) ProtoMessage() {}

func (x *GetVINDecoderVehiclesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVINDecoderVehiclesRequest.ProtoReflect.Descriptor instead.
func (*GetVINDecoderVehiclesRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetVINDecoderVehiclesRequest) GetVINs() []string {
	if x != nil {
		return x.VINs
	}
	return nil
}

type VINDecoderVehicle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vehicle  *VINVehicleNode         `protobuf:"bytes,1,opt,name=Vehicle,proto3" json:"Vehicle,omitempty"`
	Problem  *VINDecoderProblemNode  `protobuf:"bytes,2,opt,name=Problem,proto3,oneof" json:"Problem,omitempty"`
	Solution *VINDecoderSolutionNode `protobuf:"bytes,3,opt,name=Solution,proto3,oneof" json:"Solution,omitempty"`
}

func (x *VINDecoderVehicle) Reset() {
	*x = VINDecoderVehicle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VINDecoderVehicle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VINDecoderVehicle) ProtoMessage() {}

func (x *VINDecoderVehicle) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VINDecoderVehicle.ProtoReflect.Descriptor instead.
func (*VINDecoderVehicle) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{11}
}

func (x *VINDecoderVehicle) GetVehicle() *VINVehicleNode {
	if x != nil {
		return x.Vehicle
	}
	return nil
}

func (x *VINDecoderVehicle) GetProblem() *VINDecoderProblemNode {
	if x != nil {
		return x.Problem
	}
	return nil
}

func (x *VINDecoderVehicle) GetSolution() *VINDecoderSolutionNode {
	if x != nil {
		return x.Solution
	}
	return nil
}

type GetVINDecoderVehiclesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vehicles []*VINDecoderVehicle `protobuf:"bytes,1,rep,name=Vehicles,proto3" json:"Vehicles,omitempty"`
}

func (x *GetVINDecoderVehiclesResponse) Reset() {
	*x = GetVINDecoderVehiclesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVINDecoderVehiclesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVINDecoderVehiclesResponse) ProtoMessage() {}

func (x *GetVINDecoderVehiclesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVINDecoderVehiclesResponse.ProtoReflect.Descriptor instead.
func (*GetVINDecoderVehiclesResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{12}
}

func (x *GetVINDecoderVehiclesResponse) GetVehicles() []*VINDecoderVehicle {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

type ListTelematicsConnectionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FleetID string `protobuf:"bytes,1,opt,name=FleetID,proto3" json:"FleetID,omitempty"`
}

func (x *ListTelematicsConnectionsRequest) Reset() {
	*x = ListTelematicsConnectionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTelematicsConnectionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTelematicsConnectionsRequest) ProtoMessage() {}

func (x *ListTelematicsConnectionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTelematicsConnectionsRequest.ProtoReflect.Descriptor instead.
func (*ListTelematicsConnectionsRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{13}
}

func (x *ListTelematicsConnectionsRequest) GetFleetID() string {
	if x != nil {
		return x.FleetID
	}
	return ""
}

type ListTelematicsConnectionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fleet       *FleetNode                  `protobuf:"bytes,1,opt,name=Fleet,proto3" json:"Fleet,omitempty"`
	Connections []*TelematicsConnectionNode `protobuf:"bytes,2,rep,name=Connections,proto3" json:"Connections,omitempty"`
}

func (x *ListTelematicsConnectionsResponse) Reset() {
	*x = ListTelematicsConnectionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTelematicsConnectionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTelematicsConnectionsResponse) ProtoMessage() {}

func (x *ListTelematicsConnectionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTelematicsConnectionsResponse.ProtoReflect.Descriptor instead.
func (*ListTelematicsConnectionsResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{14}
}

func (x *ListTelematicsConnectionsResponse) GetFleet() *FleetNode {
	if x != nil {
		return x.Fleet
	}
	return nil
}

func (x *ListTelematicsConnectionsResponse) GetConnections() []*TelematicsConnectionNode {
	if x != nil {
		return x.Connections
	}
	return nil
}

type UpsertTelematicsConnectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Connection *TelematicsConnectionNode `protobuf:"bytes,1,opt,name=Connection,proto3" json:"Connection,omitempty"`
	Fleet      *FleetNode                `protobuf:"bytes,2,opt,name=Fleet,proto3" json:"Fleet,omitempty"`
}

func (x *UpsertTelematicsConnectionRequest) Reset() {
	*x = UpsertTelematicsConnectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertTelematicsConnectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertTelematicsConnectionRequest) ProtoMessage() {}

func (x *UpsertTelematicsConnectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertTelematicsConnectionRequest.ProtoReflect.Descriptor instead.
func (*UpsertTelematicsConnectionRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{15}
}

func (x *UpsertTelematicsConnectionRequest) GetConnection() *TelematicsConnectionNode {
	if x != nil {
		return x.Connection
	}
	return nil
}

func (x *UpsertTelematicsConnectionRequest) GetFleet() *FleetNode {
	if x != nil {
		return x.Fleet
	}
	return nil
}

type UpsertTelematicsConnectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpsertTelematicsConnectionResponse) Reset() {
	*x = UpsertTelematicsConnectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertTelematicsConnectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertTelematicsConnectionResponse) ProtoMessage() {}

func (x *UpsertTelematicsConnectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertTelematicsConnectionResponse.ProtoReflect.Descriptor instead.
func (*UpsertTelematicsConnectionResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{16}
}

type GetTelematicsSnapshotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Snapshot *TelematicsConnectionSnapshotNode_PrimaryKey `protobuf:"bytes,1,opt,name=Snapshot,proto3" json:"Snapshot,omitempty"`
}

func (x *GetTelematicsSnapshotRequest) Reset() {
	*x = GetTelematicsSnapshotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTelematicsSnapshotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTelematicsSnapshotRequest) ProtoMessage() {}

func (x *GetTelematicsSnapshotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTelematicsSnapshotRequest.ProtoReflect.Descriptor instead.
func (*GetTelematicsSnapshotRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{17}
}

func (x *GetTelematicsSnapshotRequest) GetSnapshot() *TelematicsConnectionSnapshotNode_PrimaryKey {
	if x != nil {
		return x.Snapshot
	}
	return nil
}

type GetTelematicsSnapshotResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fleet          *FleetNode                        `protobuf:"bytes,1,opt,name=Fleet,proto3" json:"Fleet,omitempty"`
	Connection     *TelematicsConnectionNode         `protobuf:"bytes,2,opt,name=Connection,proto3" json:"Connection,omitempty"`
	Snapshot       *TelematicsConnectionSnapshotNode `protobuf:"bytes,3,opt,name=Snapshot,proto3" json:"Snapshot,omitempty"`
	Vehicles       []*TelematicsVehicleSnapshotNode  `protobuf:"bytes,4,rep,name=Vehicles,proto3" json:"Vehicles,omitempty"`
	VINVehicles    []*VINVehicleNode                 `protobuf:"bytes,5,rep,name=VINVehicles,proto3" json:"VINVehicles,omitempty"`
	TaggedVehicles []*TaggedVehicles                 `protobuf:"bytes,6,rep,name=TaggedVehicles,proto3" json:"TaggedVehicles,omitempty"`
}

func (x *GetTelematicsSnapshotResponse) Reset() {
	*x = GetTelematicsSnapshotResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTelematicsSnapshotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTelematicsSnapshotResponse) ProtoMessage() {}

func (x *GetTelematicsSnapshotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTelematicsSnapshotResponse.ProtoReflect.Descriptor instead.
func (*GetTelematicsSnapshotResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{18}
}

func (x *GetTelematicsSnapshotResponse) GetFleet() *FleetNode {
	if x != nil {
		return x.Fleet
	}
	return nil
}

func (x *GetTelematicsSnapshotResponse) GetConnection() *TelematicsConnectionNode {
	if x != nil {
		return x.Connection
	}
	return nil
}

func (x *GetTelematicsSnapshotResponse) GetSnapshot() *TelematicsConnectionSnapshotNode {
	if x != nil {
		return x.Snapshot
	}
	return nil
}

func (x *GetTelematicsSnapshotResponse) GetVehicles() []*TelematicsVehicleSnapshotNode {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

func (x *GetTelematicsSnapshotResponse) GetVINVehicles() []*VINVehicleNode {
	if x != nil {
		return x.VINVehicles
	}
	return nil
}

func (x *GetTelematicsSnapshotResponse) GetTaggedVehicles() []*TaggedVehicles {
	if x != nil {
		return x.TaggedVehicles
	}
	return nil
}

type ListTelematicsSnapshotsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HandleID  string                 `protobuf:"bytes,1,opt,name=HandleID,proto3" json:"HandleID,omitempty"`
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=StartTime,proto3" json:"StartTime,omitempty"`
	EndTime   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=EndTime,proto3" json:"EndTime,omitempty"`
}

func (x *ListTelematicsSnapshotsRequest) Reset() {
	*x = ListTelematicsSnapshotsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTelematicsSnapshotsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTelematicsSnapshotsRequest) ProtoMessage() {}

func (x *ListTelematicsSnapshotsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTelematicsSnapshotsRequest.ProtoReflect.Descriptor instead.
func (*ListTelematicsSnapshotsRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{19}
}

func (x *ListTelematicsSnapshotsRequest) GetHandleID() string {
	if x != nil {
		return x.HandleID
	}
	return ""
}

func (x *ListTelematicsSnapshotsRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ListTelematicsSnapshotsRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type ListTelematicsSnapshotsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Snapshots []*TelematicsConnectionSnapshotNode `protobuf:"bytes,1,rep,name=Snapshots,proto3" json:"Snapshots,omitempty"`
}

func (x *ListTelematicsSnapshotsResponse) Reset() {
	*x = ListTelematicsSnapshotsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTelematicsSnapshotsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTelematicsSnapshotsResponse) ProtoMessage() {}

func (x *ListTelematicsSnapshotsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTelematicsSnapshotsResponse.ProtoReflect.Descriptor instead.
func (*ListTelematicsSnapshotsResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{20}
}

func (x *ListTelematicsSnapshotsResponse) GetSnapshots() []*TelematicsConnectionSnapshotNode {
	if x != nil {
		return x.Snapshots
	}
	return nil
}

type TaggedVehicles struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tag      *TelematicsTagSnapshotNode                  `protobuf:"bytes,1,opt,name=Tag,proto3" json:"Tag,omitempty"`
	Vehicles []*TelematicsVehicleSnapshotNode_PrimaryKey `protobuf:"bytes,2,rep,name=Vehicles,proto3" json:"Vehicles,omitempty"`
}

func (x *TaggedVehicles) Reset() {
	*x = TaggedVehicles{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaggedVehicles) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaggedVehicles) ProtoMessage() {}

func (x *TaggedVehicles) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaggedVehicles.ProtoReflect.Descriptor instead.
func (*TaggedVehicles) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{21}
}

func (x *TaggedVehicles) GetTag() *TelematicsTagSnapshotNode {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *TaggedVehicles) GetVehicles() []*TelematicsVehicleSnapshotNode_PrimaryKey {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

type UpsertTelematicsSnapshotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Connection *TelematicsConnectionSnapshotNode `protobuf:"bytes,1,opt,name=Connection,proto3" json:"Connection,omitempty"`
	Vehicles   []*TelematicsVehicleSnapshotNode  `protobuf:"bytes,2,rep,name=Vehicles,proto3" json:"Vehicles,omitempty"`
	Tags       []*TaggedVehicles                 `protobuf:"bytes,3,rep,name=Tags,proto3" json:"Tags,omitempty"`
}

func (x *UpsertTelematicsSnapshotRequest) Reset() {
	*x = UpsertTelematicsSnapshotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertTelematicsSnapshotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertTelematicsSnapshotRequest) ProtoMessage() {}

func (x *UpsertTelematicsSnapshotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertTelematicsSnapshotRequest.ProtoReflect.Descriptor instead.
func (*UpsertTelematicsSnapshotRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{22}
}

func (x *UpsertTelematicsSnapshotRequest) GetConnection() *TelematicsConnectionSnapshotNode {
	if x != nil {
		return x.Connection
	}
	return nil
}

func (x *UpsertTelematicsSnapshotRequest) GetVehicles() []*TelematicsVehicleSnapshotNode {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

func (x *UpsertTelematicsSnapshotRequest) GetTags() []*TaggedVehicles {
	if x != nil {
		return x.Tags
	}
	return nil
}

type UpsertTelematicsSnapshotResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpsertTelematicsSnapshotResponse) Reset() {
	*x = UpsertTelematicsSnapshotResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertTelematicsSnapshotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertTelematicsSnapshotResponse) ProtoMessage() {}

func (x *UpsertTelematicsSnapshotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertTelematicsSnapshotResponse.ProtoReflect.Descriptor instead.
func (*UpsertTelematicsSnapshotResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{23}
}

type GetEquipmentListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EquipmentList *EquipmentListTimeseriesNode_PrimaryKey `protobuf:"bytes,1,opt,name=EquipmentList,proto3" json:"EquipmentList,omitempty"`
}

func (x *GetEquipmentListRequest) Reset() {
	*x = GetEquipmentListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEquipmentListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEquipmentListRequest) ProtoMessage() {}

func (x *GetEquipmentListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEquipmentListRequest.ProtoReflect.Descriptor instead.
func (*GetEquipmentListRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{24}
}

func (x *GetEquipmentListRequest) GetEquipmentList() *EquipmentListTimeseriesNode_PrimaryKey {
	if x != nil {
		return x.EquipmentList
	}
	return nil
}

type GetEquipmentListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Agency        *AgencyNode                       `protobuf:"bytes,1,opt,name=Agency,proto3" json:"Agency,omitempty"`
	Fleet         *FleetNode                        `protobuf:"bytes,2,opt,name=Fleet,proto3" json:"Fleet,omitempty"`
	Application   *ApplicationNode                  `protobuf:"bytes,3,opt,name=Application,proto3" json:"Application,omitempty"`
	EquipmentList *EquipmentListTimeseriesNode      `protobuf:"bytes,4,opt,name=EquipmentList,proto3" json:"EquipmentList,omitempty"`
	Vehicles      []*EquipmentVehicleTimeseriesNode `protobuf:"bytes,5,rep,name=Vehicles,proto3" json:"Vehicles,omitempty"`
}

func (x *GetEquipmentListResponse) Reset() {
	*x = GetEquipmentListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEquipmentListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEquipmentListResponse) ProtoMessage() {}

func (x *GetEquipmentListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEquipmentListResponse.ProtoReflect.Descriptor instead.
func (*GetEquipmentListResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{25}
}

func (x *GetEquipmentListResponse) GetAgency() *AgencyNode {
	if x != nil {
		return x.Agency
	}
	return nil
}

func (x *GetEquipmentListResponse) GetFleet() *FleetNode {
	if x != nil {
		return x.Fleet
	}
	return nil
}

func (x *GetEquipmentListResponse) GetApplication() *ApplicationNode {
	if x != nil {
		return x.Application
	}
	return nil
}

func (x *GetEquipmentListResponse) GetEquipmentList() *EquipmentListTimeseriesNode {
	if x != nil {
		return x.EquipmentList
	}
	return nil
}

func (x *GetEquipmentListResponse) GetVehicles() []*EquipmentVehicleTimeseriesNode {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

type RefreshEquipmentListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Agency        *AgencyNode_PrimaryKey                  `protobuf:"bytes,1,opt,name=Agency,proto3" json:"Agency,omitempty"`
	Fleet         *FleetNode_PrimaryKey                   `protobuf:"bytes,2,opt,name=Fleet,proto3" json:"Fleet,omitempty"`
	Application   *ApplicationNode_PrimaryKey             `protobuf:"bytes,3,opt,name=Application,proto3" json:"Application,omitempty"`
	EquipmentList *EquipmentListTimeseriesNode_PrimaryKey `protobuf:"bytes,4,opt,name=EquipmentList,proto3" json:"EquipmentList,omitempty"`
	Vehicles      []*EquipmentVehicleTimeseriesNode       `protobuf:"bytes,5,rep,name=Vehicles,proto3" json:"Vehicles,omitempty"`
}

func (x *RefreshEquipmentListRequest) Reset() {
	*x = RefreshEquipmentListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshEquipmentListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshEquipmentListRequest) ProtoMessage() {}

func (x *RefreshEquipmentListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshEquipmentListRequest.ProtoReflect.Descriptor instead.
func (*RefreshEquipmentListRequest) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{26}
}

func (x *RefreshEquipmentListRequest) GetAgency() *AgencyNode_PrimaryKey {
	if x != nil {
		return x.Agency
	}
	return nil
}

func (x *RefreshEquipmentListRequest) GetFleet() *FleetNode_PrimaryKey {
	if x != nil {
		return x.Fleet
	}
	return nil
}

func (x *RefreshEquipmentListRequest) GetApplication() *ApplicationNode_PrimaryKey {
	if x != nil {
		return x.Application
	}
	return nil
}

func (x *RefreshEquipmentListRequest) GetEquipmentList() *EquipmentListTimeseriesNode_PrimaryKey {
	if x != nil {
		return x.EquipmentList
	}
	return nil
}

func (x *RefreshEquipmentListRequest) GetVehicles() []*EquipmentVehicleTimeseriesNode {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

type RefreshEquipmentListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RefreshEquipmentListResponse) Reset() {
	*x = RefreshEquipmentListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshEquipmentListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshEquipmentListResponse) ProtoMessage() {}

func (x *RefreshEquipmentListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshEquipmentListResponse.ProtoReflect.Descriptor instead.
func (*RefreshEquipmentListResponse) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{27}
}

type Scalar struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*Scalar_Int64
	//	*Scalar_String_
	//	*Scalar_Bool
	Value isScalar_Value `protobuf_oneof:"Value"`
}

func (x *Scalar) Reset() {
	*x = Scalar{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vehicles_service_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scalar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scalar) ProtoMessage() {}

func (x *Scalar) ProtoReflect() protoreflect.Message {
	mi := &file_vehicles_service_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scalar.ProtoReflect.Descriptor instead.
func (*Scalar) Descriptor() ([]byte, []int) {
	return file_vehicles_service_api_proto_rawDescGZIP(), []int{28}
}

func (m *Scalar) GetValue() isScalar_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *Scalar) GetInt64() int64 {
	if x, ok := x.GetValue().(*Scalar_Int64); ok {
		return x.Int64
	}
	return 0
}

func (x *Scalar) GetString_() string {
	if x, ok := x.GetValue().(*Scalar_String_); ok {
		return x.String_
	}
	return ""
}

func (x *Scalar) GetBool() bool {
	if x, ok := x.GetValue().(*Scalar_Bool); ok {
		return x.Bool
	}
	return false
}

type isScalar_Value interface {
	isScalar_Value()
}

type Scalar_Int64 struct {
	Int64 int64 `protobuf:"varint,1,opt,name=int64,proto3,oneof"`
}

type Scalar_String_ struct {
	String_ string `protobuf:"bytes,2,opt,name=string,proto3,oneof"`
}

type Scalar_Bool struct {
	Bool bool `protobuf:"varint,3,opt,name=bool,proto3,oneof"`
}

func (*Scalar_Int64) isScalar_Value() {}

func (*Scalar_String_) isScalar_Value() {}

func (*Scalar_Bool) isScalar_Value() {}

var File_vehicles_service_api_proto protoreflect.FileDescriptor

var file_vehicles_service_api_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x26, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x26, 0x0a, 0x0e, 0x52, 0x4f, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x22,
	0xa1, 0x01, 0x0a, 0x0f, 0x52, 0x4f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x52, 0x07,
	0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x4e, 0x6f, 0x64, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05,
	0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x45, 0x64, 0x67, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x64, 0x67, 0x65, 0x52, 0x05, 0x45, 0x64,
	0x67, 0x65, 0x73, 0x22, 0x72, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45,
	0x64, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x05, 0x4e,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x05, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x45, 0x64, 0x67,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x64, 0x67, 0x65,
	0x52, 0x05, 0x45, 0x64, 0x67, 0x65, 0x73, 0x22, 0x73, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x73, 0x45, 0x64, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x2c, 0x0a, 0x05, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x2c,
	0x0a, 0x05, 0x45, 0x64, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x45, 0x64, 0x67, 0x65, 0x52, 0x05, 0x45, 0x64, 0x67, 0x65, 0x73, 0x22, 0x75, 0x0a, 0x17,
	0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x64, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x05, 0x4e, 0x6f, 0x64, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05,
	0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x45, 0x64, 0x67, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x64, 0x67, 0x65, 0x52, 0x05, 0x45, 0x64,
	0x67, 0x65, 0x73, 0x22, 0x76, 0x0a, 0x18, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x64,
	0x65, 0x73, 0x45, 0x64, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2c, 0x0a, 0x05, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x2c, 0x0a,
	0x05, 0x45, 0x64, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x45, 0x64, 0x67, 0x65, 0x52, 0x05, 0x45, 0x64, 0x67, 0x65, 0x73, 0x22, 0x75, 0x0a, 0x17, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x64, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x05, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x4e,
	0x6f, 0x64, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x45, 0x64, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x64, 0x67, 0x65, 0x52, 0x05, 0x45, 0x64, 0x67,
	0x65, 0x73, 0x22, 0x1a, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x73, 0x45, 0x64, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xec,
	0x01, 0x0a, 0x1f, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3c, 0x0a, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x12, 0x43, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x50, 0x72, 0x6f,
	0x62, 0x6c, 0x65, 0x6d, 0x73, 0x12, 0x46, 0x0a, 0x09, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x09, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x22, 0x0a,
	0x20, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65,
	0x72, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x32, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64,
	0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x56, 0x49, 0x4e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x04, 0x56, 0x49, 0x4e, 0x73, 0x22, 0xfb, 0x01, 0x0a, 0x11, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x07,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x46, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x62, 0x6c,
	0x65, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x4e, 0x6f, 0x64,
	0x65, 0x48, 0x00, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x88, 0x01, 0x01, 0x12,
	0x49, 0x0a, 0x08, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x53,
	0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x01, 0x52, 0x08, 0x53,
	0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x50,
	0x72, 0x6f, 0x62, 0x6c, 0x65, 0x6d, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x53, 0x6f, 0x6c, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x60, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x08, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x22, 0x3c, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x46, 0x6c, 0x65,
	0x65, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x46, 0x6c, 0x65, 0x65,
	0x74, 0x49, 0x44, 0x22, 0xa4, 0x01, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x46, 0x6c, 0x65,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x46, 0x6c, 0x65, 0x65,
	0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x12, 0x4c, 0x0a, 0x0b,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa2, 0x01, 0x0a, 0x21, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x4a, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x0a, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x05,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x46,
	0x6c, 0x65, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x22,
	0x24, 0x0a, 0x22, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x79, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x59, 0x0a, 0x08, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x08, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x22, 0xc9, 0x03, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05,
	0x46, 0x6c, 0x65, 0x65, 0x74, 0x12, 0x4a, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x4e, 0x0a, 0x08, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x12, 0x4b, 0x0a, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x4e, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x12, 0x42,
	0x0a, 0x0b, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x56, 0x49, 0x4e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x12, 0x48, 0x0a, 0x0e, 0x54, 0x61, 0x67, 0x67, 0x65, 0x64, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x61,
	0x67, 0x67, 0x65, 0x64, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x0e, 0x54, 0x61,
	0x67, 0x67, 0x65, 0x64, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x22, 0xac, 0x01, 0x0a,
	0x1e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x44, 0x12, 0x38, 0x0a, 0x09, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x73, 0x0a, 0x1f, 0x4c,
	0x69, 0x73, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50,
	0x0a, 0x09, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x73,
	0x22, 0xa7, 0x01, 0x0a, 0x0e, 0x54, 0x61, 0x67, 0x67, 0x65, 0x64, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x54, 0x61,
	0x67, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x03, 0x54,
	0x61, 0x67, 0x12, 0x56, 0x0a, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79,
	0x52, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x22, 0xf8, 0x01, 0x0a, 0x1f, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x52,
	0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x12,
	0x34, 0x0a, 0x04, 0x54, 0x61, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x54, 0x61, 0x67, 0x67, 0x65, 0x64, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52,
	0x04, 0x54, 0x61, 0x67, 0x73, 0x22, 0x22, 0x0a, 0x20, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54,
	0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x79, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x5e, 0x0a, 0x0d, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45,
	0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x0d, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0xeb, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x34, 0x0a, 0x06, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x52,
	0x06, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x46, 0x6c, 0x65, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x05, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x12, 0x43, 0x0a, 0x0b, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x0b, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x53, 0x0a, 0x0d, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0d, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x4c, 0x0a, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x22, 0x9a, 0x03, 0x0a, 0x1b, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3f, 0x0a, 0x06, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x6f, 0x64, 0x65,
	0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x06, 0x41, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x3c, 0x0a, 0x05, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x2e,
	0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b, 0x65, 0x79, 0x52, 0x05, 0x46, 0x6c, 0x65, 0x65,
	0x74, 0x12, 0x4e, 0x0a, 0x0b, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x4b, 0x65, 0x79, 0x52, 0x0b, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x5e, 0x0a, 0x0d, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x4b,
	0x65, 0x79, 0x52, 0x0d, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x4c, 0x0a, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x22,
	0x1e, 0x0a, 0x1c, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x59, 0x0a, 0x06, 0x53, 0x63, 0x61, 0x6c, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x05, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x12, 0x18, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x04, 0x62,
	0x6f, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x04, 0x62, 0x6f, 0x6f,
	0x6c, 0x42, 0x07, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x32, 0x88, 0x0c, 0x0a, 0x0f, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x60,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x64, 0x67, 0x65, 0x73, 0x12,
	0x26, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x64, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x73, 0x45, 0x64, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x69, 0x0a, 0x10, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45,
	0x64, 0x67, 0x65, 0x73, 0x12, 0x29, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x73, 0x45, 0x64, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2a, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x64,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x10, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x64, 0x67, 0x65, 0x73, 0x12,
	0x29, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x64,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x64, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x12, 0x31, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x56, 0x49, 0x4e,
	0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74,
	0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x73, 0x12, 0x2e, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x49, 0x4e, 0x44, 0x65, 0x63,
	0x6f, 0x64, 0x65, 0x72, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x07, 0x52, 0x4f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x20, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x52, 0x4f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x21, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x52, 0x4f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x32, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65,
	0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x1a,
	0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x2e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x34, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6c,
	0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x73,
	0x12, 0x30, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x31, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6c, 0x65,
	0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x2e,
	0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f,
	0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x81, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61,
	0x74, 0x69, 0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x31, 0x2e, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x73,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x32, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x61, 0x74,
	0x69, 0x63, 0x73, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x29, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75,
	0x0a, 0x14, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_vehicles_service_api_proto_rawDescOnce sync.Once
	file_vehicles_service_api_proto_rawDescData = file_vehicles_service_api_proto_rawDesc
)

func file_vehicles_service_api_proto_rawDescGZIP() []byte {
	file_vehicles_service_api_proto_rawDescOnce.Do(func() {
		file_vehicles_service_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_vehicles_service_api_proto_rawDescData)
	})
	return file_vehicles_service_api_proto_rawDescData
}

var file_vehicles_service_api_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_vehicles_service_api_proto_goTypes = []interface{}{
	(*ROQueryRequest)(nil),                              // 0: vehicles_service.ROQueryRequest
	(*ROQueryResponse)(nil),                             // 1: vehicles_service.ROQueryResponse
	(*GetNodesEdgesRequest)(nil),                        // 2: vehicles_service.GetNodesEdgesRequest
	(*GetNodesEdgesResponse)(nil),                       // 3: vehicles_service.GetNodesEdgesResponse
	(*UpsertNodesEdgesRequest)(nil),                     // 4: vehicles_service.UpsertNodesEdgesRequest
	(*UpsertNodesEdgesResponse)(nil),                    // 5: vehicles_service.UpsertNodesEdgesResponse
	(*DeleteNodesEdgesRequest)(nil),                     // 6: vehicles_service.DeleteNodesEdgesRequest
	(*DeleteNodesEdgesResponse)(nil),                    // 7: vehicles_service.DeleteNodesEdgesResponse
	(*UpsertVINDecoderVehiclesRequest)(nil),             // 8: vehicles_service.UpsertVINDecoderVehiclesRequest
	(*UpsertVINDecoderVehiclesResponse)(nil),            // 9: vehicles_service.UpsertVINDecoderVehiclesResponse
	(*GetVINDecoderVehiclesRequest)(nil),                // 10: vehicles_service.GetVINDecoderVehiclesRequest
	(*VINDecoderVehicle)(nil),                           // 11: vehicles_service.VINDecoderVehicle
	(*GetVINDecoderVehiclesResponse)(nil),               // 12: vehicles_service.GetVINDecoderVehiclesResponse
	(*ListTelematicsConnectionsRequest)(nil),            // 13: vehicles_service.ListTelematicsConnectionsRequest
	(*ListTelematicsConnectionsResponse)(nil),           // 14: vehicles_service.ListTelematicsConnectionsResponse
	(*UpsertTelematicsConnectionRequest)(nil),           // 15: vehicles_service.UpsertTelematicsConnectionRequest
	(*UpsertTelematicsConnectionResponse)(nil),          // 16: vehicles_service.UpsertTelematicsConnectionResponse
	(*GetTelematicsSnapshotRequest)(nil),                // 17: vehicles_service.GetTelematicsSnapshotRequest
	(*GetTelematicsSnapshotResponse)(nil),               // 18: vehicles_service.GetTelematicsSnapshotResponse
	(*ListTelematicsSnapshotsRequest)(nil),              // 19: vehicles_service.ListTelematicsSnapshotsRequest
	(*ListTelematicsSnapshotsResponse)(nil),             // 20: vehicles_service.ListTelematicsSnapshotsResponse
	(*TaggedVehicles)(nil),                              // 21: vehicles_service.TaggedVehicles
	(*UpsertTelematicsSnapshotRequest)(nil),             // 22: vehicles_service.UpsertTelematicsSnapshotRequest
	(*UpsertTelematicsSnapshotResponse)(nil),            // 23: vehicles_service.UpsertTelematicsSnapshotResponse
	(*GetEquipmentListRequest)(nil),                     // 24: vehicles_service.GetEquipmentListRequest
	(*GetEquipmentListResponse)(nil),                    // 25: vehicles_service.GetEquipmentListResponse
	(*RefreshEquipmentListRequest)(nil),                 // 26: vehicles_service.RefreshEquipmentListRequest
	(*RefreshEquipmentListResponse)(nil),                // 27: vehicles_service.RefreshEquipmentListResponse
	(*Scalar)(nil),                                      // 28: vehicles_service.Scalar
	(*Node)(nil),                                        // 29: vehicles_service.Node
	(*Edge)(nil),                                        // 30: vehicles_service.Edge
	(*VINVehicleNode)(nil),                              // 31: vehicles_service.VINVehicleNode
	(*VINDecoderProblemNode)(nil),                       // 32: vehicles_service.VINDecoderProblemNode
	(*VINDecoderSolutionNode)(nil),                      // 33: vehicles_service.VINDecoderSolutionNode
	(*FleetNode)(nil),                                   // 34: vehicles_service.FleetNode
	(*TelematicsConnectionNode)(nil),                    // 35: vehicles_service.TelematicsConnectionNode
	(*TelematicsConnectionSnapshotNode_PrimaryKey)(nil), // 36: vehicles_service.TelematicsConnectionSnapshotNode.PrimaryKey
	(*TelematicsConnectionSnapshotNode)(nil),            // 37: vehicles_service.TelematicsConnectionSnapshotNode
	(*TelematicsVehicleSnapshotNode)(nil),               // 38: vehicles_service.TelematicsVehicleSnapshotNode
	(*timestamppb.Timestamp)(nil),                       // 39: google.protobuf.Timestamp
	(*TelematicsTagSnapshotNode)(nil),                   // 40: vehicles_service.TelematicsTagSnapshotNode
	(*TelematicsVehicleSnapshotNode_PrimaryKey)(nil),    // 41: vehicles_service.TelematicsVehicleSnapshotNode.PrimaryKey
	(*EquipmentListTimeseriesNode_PrimaryKey)(nil),      // 42: vehicles_service.EquipmentListTimeseriesNode.PrimaryKey
	(*AgencyNode)(nil),                                  // 43: vehicles_service.AgencyNode
	(*ApplicationNode)(nil),                             // 44: vehicles_service.ApplicationNode
	(*EquipmentListTimeseriesNode)(nil),                 // 45: vehicles_service.EquipmentListTimeseriesNode
	(*EquipmentVehicleTimeseriesNode)(nil),              // 46: vehicles_service.EquipmentVehicleTimeseriesNode
	(*AgencyNode_PrimaryKey)(nil),                       // 47: vehicles_service.AgencyNode.PrimaryKey
	(*FleetNode_PrimaryKey)(nil),                        // 48: vehicles_service.FleetNode.PrimaryKey
	(*ApplicationNode_PrimaryKey)(nil),                  // 49: vehicles_service.ApplicationNode.PrimaryKey
}
var file_vehicles_service_api_proto_depIdxs = []int32{
	28, // 0: vehicles_service.ROQueryResponse.Scalars:type_name -> vehicles_service.Scalar
	29, // 1: vehicles_service.ROQueryResponse.Nodes:type_name -> vehicles_service.Node
	30, // 2: vehicles_service.ROQueryResponse.Edges:type_name -> vehicles_service.Edge
	29, // 3: vehicles_service.GetNodesEdgesRequest.Nodes:type_name -> vehicles_service.Node
	30, // 4: vehicles_service.GetNodesEdgesRequest.Edges:type_name -> vehicles_service.Edge
	29, // 5: vehicles_service.GetNodesEdgesResponse.Nodes:type_name -> vehicles_service.Node
	30, // 6: vehicles_service.GetNodesEdgesResponse.Edges:type_name -> vehicles_service.Edge
	29, // 7: vehicles_service.UpsertNodesEdgesRequest.Nodes:type_name -> vehicles_service.Node
	30, // 8: vehicles_service.UpsertNodesEdgesRequest.Edges:type_name -> vehicles_service.Edge
	29, // 9: vehicles_service.UpsertNodesEdgesResponse.Nodes:type_name -> vehicles_service.Node
	30, // 10: vehicles_service.UpsertNodesEdgesResponse.Edges:type_name -> vehicles_service.Edge
	29, // 11: vehicles_service.DeleteNodesEdgesRequest.Nodes:type_name -> vehicles_service.Node
	30, // 12: vehicles_service.DeleteNodesEdgesRequest.Edges:type_name -> vehicles_service.Edge
	31, // 13: vehicles_service.UpsertVINDecoderVehiclesRequest.Vehicles:type_name -> vehicles_service.VINVehicleNode
	32, // 14: vehicles_service.UpsertVINDecoderVehiclesRequest.Problems:type_name -> vehicles_service.VINDecoderProblemNode
	33, // 15: vehicles_service.UpsertVINDecoderVehiclesRequest.Solutions:type_name -> vehicles_service.VINDecoderSolutionNode
	31, // 16: vehicles_service.VINDecoderVehicle.Vehicle:type_name -> vehicles_service.VINVehicleNode
	32, // 17: vehicles_service.VINDecoderVehicle.Problem:type_name -> vehicles_service.VINDecoderProblemNode
	33, // 18: vehicles_service.VINDecoderVehicle.Solution:type_name -> vehicles_service.VINDecoderSolutionNode
	11, // 19: vehicles_service.GetVINDecoderVehiclesResponse.Vehicles:type_name -> vehicles_service.VINDecoderVehicle
	34, // 20: vehicles_service.ListTelematicsConnectionsResponse.Fleet:type_name -> vehicles_service.FleetNode
	35, // 21: vehicles_service.ListTelematicsConnectionsResponse.Connections:type_name -> vehicles_service.TelematicsConnectionNode
	35, // 22: vehicles_service.UpsertTelematicsConnectionRequest.Connection:type_name -> vehicles_service.TelematicsConnectionNode
	34, // 23: vehicles_service.UpsertTelematicsConnectionRequest.Fleet:type_name -> vehicles_service.FleetNode
	36, // 24: vehicles_service.GetTelematicsSnapshotRequest.Snapshot:type_name -> vehicles_service.TelematicsConnectionSnapshotNode.PrimaryKey
	34, // 25: vehicles_service.GetTelematicsSnapshotResponse.Fleet:type_name -> vehicles_service.FleetNode
	35, // 26: vehicles_service.GetTelematicsSnapshotResponse.Connection:type_name -> vehicles_service.TelematicsConnectionNode
	37, // 27: vehicles_service.GetTelematicsSnapshotResponse.Snapshot:type_name -> vehicles_service.TelematicsConnectionSnapshotNode
	38, // 28: vehicles_service.GetTelematicsSnapshotResponse.Vehicles:type_name -> vehicles_service.TelematicsVehicleSnapshotNode
	31, // 29: vehicles_service.GetTelematicsSnapshotResponse.VINVehicles:type_name -> vehicles_service.VINVehicleNode
	21, // 30: vehicles_service.GetTelematicsSnapshotResponse.TaggedVehicles:type_name -> vehicles_service.TaggedVehicles
	39, // 31: vehicles_service.ListTelematicsSnapshotsRequest.StartTime:type_name -> google.protobuf.Timestamp
	39, // 32: vehicles_service.ListTelematicsSnapshotsRequest.EndTime:type_name -> google.protobuf.Timestamp
	37, // 33: vehicles_service.ListTelematicsSnapshotsResponse.Snapshots:type_name -> vehicles_service.TelematicsConnectionSnapshotNode
	40, // 34: vehicles_service.TaggedVehicles.Tag:type_name -> vehicles_service.TelematicsTagSnapshotNode
	41, // 35: vehicles_service.TaggedVehicles.Vehicles:type_name -> vehicles_service.TelematicsVehicleSnapshotNode.PrimaryKey
	37, // 36: vehicles_service.UpsertTelematicsSnapshotRequest.Connection:type_name -> vehicles_service.TelematicsConnectionSnapshotNode
	38, // 37: vehicles_service.UpsertTelematicsSnapshotRequest.Vehicles:type_name -> vehicles_service.TelematicsVehicleSnapshotNode
	21, // 38: vehicles_service.UpsertTelematicsSnapshotRequest.Tags:type_name -> vehicles_service.TaggedVehicles
	42, // 39: vehicles_service.GetEquipmentListRequest.EquipmentList:type_name -> vehicles_service.EquipmentListTimeseriesNode.PrimaryKey
	43, // 40: vehicles_service.GetEquipmentListResponse.Agency:type_name -> vehicles_service.AgencyNode
	34, // 41: vehicles_service.GetEquipmentListResponse.Fleet:type_name -> vehicles_service.FleetNode
	44, // 42: vehicles_service.GetEquipmentListResponse.Application:type_name -> vehicles_service.ApplicationNode
	45, // 43: vehicles_service.GetEquipmentListResponse.EquipmentList:type_name -> vehicles_service.EquipmentListTimeseriesNode
	46, // 44: vehicles_service.GetEquipmentListResponse.Vehicles:type_name -> vehicles_service.EquipmentVehicleTimeseriesNode
	47, // 45: vehicles_service.RefreshEquipmentListRequest.Agency:type_name -> vehicles_service.AgencyNode.PrimaryKey
	48, // 46: vehicles_service.RefreshEquipmentListRequest.Fleet:type_name -> vehicles_service.FleetNode.PrimaryKey
	49, // 47: vehicles_service.RefreshEquipmentListRequest.Application:type_name -> vehicles_service.ApplicationNode.PrimaryKey
	42, // 48: vehicles_service.RefreshEquipmentListRequest.EquipmentList:type_name -> vehicles_service.EquipmentListTimeseriesNode.PrimaryKey
	46, // 49: vehicles_service.RefreshEquipmentListRequest.Vehicles:type_name -> vehicles_service.EquipmentVehicleTimeseriesNode
	2,  // 50: vehicles_service.VehiclesService.GetNodesEdges:input_type -> vehicles_service.GetNodesEdgesRequest
	4,  // 51: vehicles_service.VehiclesService.UpsertNodesEdges:input_type -> vehicles_service.UpsertNodesEdgesRequest
	6,  // 52: vehicles_service.VehiclesService.DeleteNodesEdges:input_type -> vehicles_service.DeleteNodesEdgesRequest
	8,  // 53: vehicles_service.VehiclesService.UpsertVINDecoderVehicles:input_type -> vehicles_service.UpsertVINDecoderVehiclesRequest
	10, // 54: vehicles_service.VehiclesService.GetVINDecoderVehicles:input_type -> vehicles_service.GetVINDecoderVehiclesRequest
	0,  // 55: vehicles_service.VehiclesService.ROQuery:input_type -> vehicles_service.ROQueryRequest
	13, // 56: vehicles_service.VehiclesService.ListTelematicsConnections:input_type -> vehicles_service.ListTelematicsConnectionsRequest
	15, // 57: vehicles_service.VehiclesService.UpsertTelematicsConnection:input_type -> vehicles_service.UpsertTelematicsConnectionRequest
	19, // 58: vehicles_service.VehiclesService.ListTelematicsSnapshots:input_type -> vehicles_service.ListTelematicsSnapshotsRequest
	17, // 59: vehicles_service.VehiclesService.GetTelematicsSnapshot:input_type -> vehicles_service.GetTelematicsSnapshotRequest
	22, // 60: vehicles_service.VehiclesService.UpsertTelematicsSnapshot:input_type -> vehicles_service.UpsertTelematicsSnapshotRequest
	24, // 61: vehicles_service.VehiclesService.GetEquipmentList:input_type -> vehicles_service.GetEquipmentListRequest
	26, // 62: vehicles_service.VehiclesService.RefreshEquipmentList:input_type -> vehicles_service.RefreshEquipmentListRequest
	3,  // 63: vehicles_service.VehiclesService.GetNodesEdges:output_type -> vehicles_service.GetNodesEdgesResponse
	5,  // 64: vehicles_service.VehiclesService.UpsertNodesEdges:output_type -> vehicles_service.UpsertNodesEdgesResponse
	7,  // 65: vehicles_service.VehiclesService.DeleteNodesEdges:output_type -> vehicles_service.DeleteNodesEdgesResponse
	9,  // 66: vehicles_service.VehiclesService.UpsertVINDecoderVehicles:output_type -> vehicles_service.UpsertVINDecoderVehiclesResponse
	12, // 67: vehicles_service.VehiclesService.GetVINDecoderVehicles:output_type -> vehicles_service.GetVINDecoderVehiclesResponse
	1,  // 68: vehicles_service.VehiclesService.ROQuery:output_type -> vehicles_service.ROQueryResponse
	14, // 69: vehicles_service.VehiclesService.ListTelematicsConnections:output_type -> vehicles_service.ListTelematicsConnectionsResponse
	16, // 70: vehicles_service.VehiclesService.UpsertTelematicsConnection:output_type -> vehicles_service.UpsertTelematicsConnectionResponse
	20, // 71: vehicles_service.VehiclesService.ListTelematicsSnapshots:output_type -> vehicles_service.ListTelematicsSnapshotsResponse
	18, // 72: vehicles_service.VehiclesService.GetTelematicsSnapshot:output_type -> vehicles_service.GetTelematicsSnapshotResponse
	23, // 73: vehicles_service.VehiclesService.UpsertTelematicsSnapshot:output_type -> vehicles_service.UpsertTelematicsSnapshotResponse
	25, // 74: vehicles_service.VehiclesService.GetEquipmentList:output_type -> vehicles_service.GetEquipmentListResponse
	27, // 75: vehicles_service.VehiclesService.RefreshEquipmentList:output_type -> vehicles_service.RefreshEquipmentListResponse
	63, // [63:76] is the sub-list for method output_type
	50, // [50:63] is the sub-list for method input_type
	50, // [50:50] is the sub-list for extension type_name
	50, // [50:50] is the sub-list for extension extendee
	0,  // [0:50] is the sub-list for field type_name
}

func init() { file_vehicles_service_api_proto_init() }
func file_vehicles_service_api_proto_init() {
	if File_vehicles_service_api_proto != nil {
		return
	}
	file_vehicles_service_proto_generated_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_vehicles_service_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ROQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ROQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNodesEdgesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNodesEdgesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertNodesEdgesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertNodesEdgesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteNodesEdgesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteNodesEdgesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertVINDecoderVehiclesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertVINDecoderVehiclesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVINDecoderVehiclesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VINDecoderVehicle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVINDecoderVehiclesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTelematicsConnectionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTelematicsConnectionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertTelematicsConnectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertTelematicsConnectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTelematicsSnapshotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTelematicsSnapshotResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTelematicsSnapshotsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTelematicsSnapshotsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaggedVehicles); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertTelematicsSnapshotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertTelematicsSnapshotResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEquipmentListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEquipmentListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshEquipmentListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshEquipmentListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vehicles_service_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scalar); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_vehicles_service_api_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_vehicles_service_api_proto_msgTypes[28].OneofWrappers = []interface{}{
		(*Scalar_Int64)(nil),
		(*Scalar_String_)(nil),
		(*Scalar_Bool)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_vehicles_service_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_vehicles_service_api_proto_goTypes,
		DependencyIndexes: file_vehicles_service_api_proto_depIdxs,
		MessageInfos:      file_vehicles_service_api_proto_msgTypes,
	}.Build()
	File_vehicles_service_api_proto = out.File
	file_vehicles_service_api_proto_rawDesc = nil
	file_vehicles_service_api_proto_goTypes = nil
	file_vehicles_service_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// VehiclesServiceClient is the client API for VehiclesService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VehiclesServiceClient interface {
	GetNodesEdges(ctx context.Context, in *GetNodesEdgesRequest, opts ...grpc.CallOption) (*GetNodesEdgesResponse, error)
	UpsertNodesEdges(ctx context.Context, in *UpsertNodesEdgesRequest, opts ...grpc.CallOption) (*UpsertNodesEdgesResponse, error)
	DeleteNodesEdges(ctx context.Context, in *DeleteNodesEdgesRequest, opts ...grpc.CallOption) (*DeleteNodesEdgesResponse, error)
	UpsertVINDecoderVehicles(ctx context.Context, in *UpsertVINDecoderVehiclesRequest, opts ...grpc.CallOption) (*UpsertVINDecoderVehiclesResponse, error)
	GetVINDecoderVehicles(ctx context.Context, in *GetVINDecoderVehiclesRequest, opts ...grpc.CallOption) (*GetVINDecoderVehiclesResponse, error)
	ROQuery(ctx context.Context, in *ROQueryRequest, opts ...grpc.CallOption) (*ROQueryResponse, error)
	ListTelematicsConnections(ctx context.Context, in *ListTelematicsConnectionsRequest, opts ...grpc.CallOption) (*ListTelematicsConnectionsResponse, error)
	UpsertTelematicsConnection(ctx context.Context, in *UpsertTelematicsConnectionRequest, opts ...grpc.CallOption) (*UpsertTelematicsConnectionResponse, error)
	ListTelematicsSnapshots(ctx context.Context, in *ListTelematicsSnapshotsRequest, opts ...grpc.CallOption) (*ListTelematicsSnapshotsResponse, error)
	GetTelematicsSnapshot(ctx context.Context, in *GetTelematicsSnapshotRequest, opts ...grpc.CallOption) (*GetTelematicsSnapshotResponse, error)
	UpsertTelematicsSnapshot(ctx context.Context, in *UpsertTelematicsSnapshotRequest, opts ...grpc.CallOption) (*UpsertTelematicsSnapshotResponse, error)
	GetEquipmentList(ctx context.Context, in *GetEquipmentListRequest, opts ...grpc.CallOption) (*GetEquipmentListResponse, error)
	RefreshEquipmentList(ctx context.Context, in *RefreshEquipmentListRequest, opts ...grpc.CallOption) (*RefreshEquipmentListResponse, error)
}

type vehiclesServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVehiclesServiceClient(cc grpc.ClientConnInterface) VehiclesServiceClient {
	return &vehiclesServiceClient{cc}
}

func (c *vehiclesServiceClient) GetNodesEdges(ctx context.Context, in *GetNodesEdgesRequest, opts ...grpc.CallOption) (*GetNodesEdgesResponse, error) {
	out := new(GetNodesEdgesResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/GetNodesEdges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) UpsertNodesEdges(ctx context.Context, in *UpsertNodesEdgesRequest, opts ...grpc.CallOption) (*UpsertNodesEdgesResponse, error) {
	out := new(UpsertNodesEdgesResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/UpsertNodesEdges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) DeleteNodesEdges(ctx context.Context, in *DeleteNodesEdgesRequest, opts ...grpc.CallOption) (*DeleteNodesEdgesResponse, error) {
	out := new(DeleteNodesEdgesResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/DeleteNodesEdges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) UpsertVINDecoderVehicles(ctx context.Context, in *UpsertVINDecoderVehiclesRequest, opts ...grpc.CallOption) (*UpsertVINDecoderVehiclesResponse, error) {
	out := new(UpsertVINDecoderVehiclesResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/UpsertVINDecoderVehicles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) GetVINDecoderVehicles(ctx context.Context, in *GetVINDecoderVehiclesRequest, opts ...grpc.CallOption) (*GetVINDecoderVehiclesResponse, error) {
	out := new(GetVINDecoderVehiclesResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/GetVINDecoderVehicles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) ROQuery(ctx context.Context, in *ROQueryRequest, opts ...grpc.CallOption) (*ROQueryResponse, error) {
	out := new(ROQueryResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/ROQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) ListTelematicsConnections(ctx context.Context, in *ListTelematicsConnectionsRequest, opts ...grpc.CallOption) (*ListTelematicsConnectionsResponse, error) {
	out := new(ListTelematicsConnectionsResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/ListTelematicsConnections", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) UpsertTelematicsConnection(ctx context.Context, in *UpsertTelematicsConnectionRequest, opts ...grpc.CallOption) (*UpsertTelematicsConnectionResponse, error) {
	out := new(UpsertTelematicsConnectionResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/UpsertTelematicsConnection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) ListTelematicsSnapshots(ctx context.Context, in *ListTelematicsSnapshotsRequest, opts ...grpc.CallOption) (*ListTelematicsSnapshotsResponse, error) {
	out := new(ListTelematicsSnapshotsResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/ListTelematicsSnapshots", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) GetTelematicsSnapshot(ctx context.Context, in *GetTelematicsSnapshotRequest, opts ...grpc.CallOption) (*GetTelematicsSnapshotResponse, error) {
	out := new(GetTelematicsSnapshotResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/GetTelematicsSnapshot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) UpsertTelematicsSnapshot(ctx context.Context, in *UpsertTelematicsSnapshotRequest, opts ...grpc.CallOption) (*UpsertTelematicsSnapshotResponse, error) {
	out := new(UpsertTelematicsSnapshotResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/UpsertTelematicsSnapshot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) GetEquipmentList(ctx context.Context, in *GetEquipmentListRequest, opts ...grpc.CallOption) (*GetEquipmentListResponse, error) {
	out := new(GetEquipmentListResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/GetEquipmentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehiclesServiceClient) RefreshEquipmentList(ctx context.Context, in *RefreshEquipmentListRequest, opts ...grpc.CallOption) (*RefreshEquipmentListResponse, error) {
	out := new(RefreshEquipmentListResponse)
	err := c.cc.Invoke(ctx, "/vehicles_service.VehiclesService/RefreshEquipmentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VehiclesServiceServer is the server API for VehiclesService service.
type VehiclesServiceServer interface {
	GetNodesEdges(context.Context, *GetNodesEdgesRequest) (*GetNodesEdgesResponse, error)
	UpsertNodesEdges(context.Context, *UpsertNodesEdgesRequest) (*UpsertNodesEdgesResponse, error)
	DeleteNodesEdges(context.Context, *DeleteNodesEdgesRequest) (*DeleteNodesEdgesResponse, error)
	UpsertVINDecoderVehicles(context.Context, *UpsertVINDecoderVehiclesRequest) (*UpsertVINDecoderVehiclesResponse, error)
	GetVINDecoderVehicles(context.Context, *GetVINDecoderVehiclesRequest) (*GetVINDecoderVehiclesResponse, error)
	ROQuery(context.Context, *ROQueryRequest) (*ROQueryResponse, error)
	ListTelematicsConnections(context.Context, *ListTelematicsConnectionsRequest) (*ListTelematicsConnectionsResponse, error)
	UpsertTelematicsConnection(context.Context, *UpsertTelematicsConnectionRequest) (*UpsertTelematicsConnectionResponse, error)
	ListTelematicsSnapshots(context.Context, *ListTelematicsSnapshotsRequest) (*ListTelematicsSnapshotsResponse, error)
	GetTelematicsSnapshot(context.Context, *GetTelematicsSnapshotRequest) (*GetTelematicsSnapshotResponse, error)
	UpsertTelematicsSnapshot(context.Context, *UpsertTelematicsSnapshotRequest) (*UpsertTelematicsSnapshotResponse, error)
	GetEquipmentList(context.Context, *GetEquipmentListRequest) (*GetEquipmentListResponse, error)
	RefreshEquipmentList(context.Context, *RefreshEquipmentListRequest) (*RefreshEquipmentListResponse, error)
}

// UnimplementedVehiclesServiceServer can be embedded to have forward compatible implementations.
type UnimplementedVehiclesServiceServer struct {
}

func (*UnimplementedVehiclesServiceServer) GetNodesEdges(context.Context, *GetNodesEdgesRequest) (*GetNodesEdgesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNodesEdges not implemented")
}
func (*UnimplementedVehiclesServiceServer) UpsertNodesEdges(context.Context, *UpsertNodesEdgesRequest) (*UpsertNodesEdgesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertNodesEdges not implemented")
}
func (*UnimplementedVehiclesServiceServer) DeleteNodesEdges(context.Context, *DeleteNodesEdgesRequest) (*DeleteNodesEdgesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNodesEdges not implemented")
}
func (*UnimplementedVehiclesServiceServer) UpsertVINDecoderVehicles(context.Context, *UpsertVINDecoderVehiclesRequest) (*UpsertVINDecoderVehiclesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertVINDecoderVehicles not implemented")
}
func (*UnimplementedVehiclesServiceServer) GetVINDecoderVehicles(context.Context, *GetVINDecoderVehiclesRequest) (*GetVINDecoderVehiclesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVINDecoderVehicles not implemented")
}
func (*UnimplementedVehiclesServiceServer) ROQuery(context.Context, *ROQueryRequest) (*ROQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ROQuery not implemented")
}
func (*UnimplementedVehiclesServiceServer) ListTelematicsConnections(context.Context, *ListTelematicsConnectionsRequest) (*ListTelematicsConnectionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTelematicsConnections not implemented")
}
func (*UnimplementedVehiclesServiceServer) UpsertTelematicsConnection(context.Context, *UpsertTelematicsConnectionRequest) (*UpsertTelematicsConnectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertTelematicsConnection not implemented")
}
func (*UnimplementedVehiclesServiceServer) ListTelematicsSnapshots(context.Context, *ListTelematicsSnapshotsRequest) (*ListTelematicsSnapshotsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTelematicsSnapshots not implemented")
}
func (*UnimplementedVehiclesServiceServer) GetTelematicsSnapshot(context.Context, *GetTelematicsSnapshotRequest) (*GetTelematicsSnapshotResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTelematicsSnapshot not implemented")
}
func (*UnimplementedVehiclesServiceServer) UpsertTelematicsSnapshot(context.Context, *UpsertTelematicsSnapshotRequest) (*UpsertTelematicsSnapshotResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertTelematicsSnapshot not implemented")
}
func (*UnimplementedVehiclesServiceServer) GetEquipmentList(context.Context, *GetEquipmentListRequest) (*GetEquipmentListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEquipmentList not implemented")
}
func (*UnimplementedVehiclesServiceServer) RefreshEquipmentList(context.Context, *RefreshEquipmentListRequest) (*RefreshEquipmentListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshEquipmentList not implemented")
}

func RegisterVehiclesServiceServer(s *grpc.Server, srv VehiclesServiceServer) {
	s.RegisterService(&_VehiclesService_serviceDesc, srv)
}

func _VehiclesService_GetNodesEdges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodesEdgesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).GetNodesEdges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/GetNodesEdges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).GetNodesEdges(ctx, req.(*GetNodesEdgesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_UpsertNodesEdges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertNodesEdgesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).UpsertNodesEdges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/UpsertNodesEdges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).UpsertNodesEdges(ctx, req.(*UpsertNodesEdgesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_DeleteNodesEdges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNodesEdgesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).DeleteNodesEdges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/DeleteNodesEdges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).DeleteNodesEdges(ctx, req.(*DeleteNodesEdgesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_UpsertVINDecoderVehicles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertVINDecoderVehiclesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).UpsertVINDecoderVehicles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/UpsertVINDecoderVehicles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).UpsertVINDecoderVehicles(ctx, req.(*UpsertVINDecoderVehiclesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_GetVINDecoderVehicles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVINDecoderVehiclesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).GetVINDecoderVehicles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/GetVINDecoderVehicles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).GetVINDecoderVehicles(ctx, req.(*GetVINDecoderVehiclesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_ROQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ROQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).ROQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/ROQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).ROQuery(ctx, req.(*ROQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_ListTelematicsConnections_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTelematicsConnectionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).ListTelematicsConnections(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/ListTelematicsConnections",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).ListTelematicsConnections(ctx, req.(*ListTelematicsConnectionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_UpsertTelematicsConnection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertTelematicsConnectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).UpsertTelematicsConnection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/UpsertTelematicsConnection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).UpsertTelematicsConnection(ctx, req.(*UpsertTelematicsConnectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_ListTelematicsSnapshots_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTelematicsSnapshotsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).ListTelematicsSnapshots(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/ListTelematicsSnapshots",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).ListTelematicsSnapshots(ctx, req.(*ListTelematicsSnapshotsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_GetTelematicsSnapshot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTelematicsSnapshotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).GetTelematicsSnapshot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/GetTelematicsSnapshot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).GetTelematicsSnapshot(ctx, req.(*GetTelematicsSnapshotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_UpsertTelematicsSnapshot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertTelematicsSnapshotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).UpsertTelematicsSnapshot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/UpsertTelematicsSnapshot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).UpsertTelematicsSnapshot(ctx, req.(*UpsertTelematicsSnapshotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_GetEquipmentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEquipmentListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).GetEquipmentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/GetEquipmentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).GetEquipmentList(ctx, req.(*GetEquipmentListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehiclesService_RefreshEquipmentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshEquipmentListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehiclesServiceServer).RefreshEquipmentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vehicles_service.VehiclesService/RefreshEquipmentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehiclesServiceServer).RefreshEquipmentList(ctx, req.(*RefreshEquipmentListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _VehiclesService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "vehicles_service.VehiclesService",
	HandlerType: (*VehiclesServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNodesEdges",
			Handler:    _VehiclesService_GetNodesEdges_Handler,
		},
		{
			MethodName: "UpsertNodesEdges",
			Handler:    _VehiclesService_UpsertNodesEdges_Handler,
		},
		{
			MethodName: "DeleteNodesEdges",
			Handler:    _VehiclesService_DeleteNodesEdges_Handler,
		},
		{
			MethodName: "UpsertVINDecoderVehicles",
			Handler:    _VehiclesService_UpsertVINDecoderVehicles_Handler,
		},
		{
			MethodName: "GetVINDecoderVehicles",
			Handler:    _VehiclesService_GetVINDecoderVehicles_Handler,
		},
		{
			MethodName: "ROQuery",
			Handler:    _VehiclesService_ROQuery_Handler,
		},
		{
			MethodName: "ListTelematicsConnections",
			Handler:    _VehiclesService_ListTelematicsConnections_Handler,
		},
		{
			MethodName: "UpsertTelematicsConnection",
			Handler:    _VehiclesService_UpsertTelematicsConnection_Handler,
		},
		{
			MethodName: "ListTelematicsSnapshots",
			Handler:    _VehiclesService_ListTelematicsSnapshots_Handler,
		},
		{
			MethodName: "GetTelematicsSnapshot",
			Handler:    _VehiclesService_GetTelematicsSnapshot_Handler,
		},
		{
			MethodName: "UpsertTelematicsSnapshot",
			Handler:    _VehiclesService_UpsertTelematicsSnapshot_Handler,
		},
		{
			MethodName: "GetEquipmentList",
			Handler:    _VehiclesService_GetEquipmentList_Handler,
		},
		{
			MethodName: "RefreshEquipmentList",
			Handler:    _VehiclesService_RefreshEquipmentList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "vehicles_service/api.proto",
}
