// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insured/service/service.proto

package service

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	model "nirvanatech.com/nirvana/insured/model"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_insured_service_service_proto protoreflect.FileDescriptor

var file_insured_service_service_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x1a, 0x1b, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x60, 0x0a, 0x0e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x12, 0x1d, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x65, 0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x29, 0x5a, 0x27, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76, 0x61,
	0x6e, 0x61, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_insured_service_service_proto_goTypes = []interface{}{
	(*model.CreateInsuredRequest)(nil),  // 0: insured.CreateInsuredRequest
	(*model.CreateInsuredResponse)(nil), // 1: insured.CreateInsuredResponse
}
var file_insured_service_service_proto_depIdxs = []int32{
	0, // 0: service.InsuredManager.CreateInsured:input_type -> insured.CreateInsuredRequest
	1, // 1: service.InsuredManager.CreateInsured:output_type -> insured.CreateInsuredResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_insured_service_service_proto_init() }
func file_insured_service_service_proto_init() {
	if File_insured_service_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insured_service_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_insured_service_service_proto_goTypes,
		DependencyIndexes: file_insured_service_service_proto_depIdxs,
	}.Build()
	File_insured_service_service_proto = out.File
	file_insured_service_service_proto_rawDesc = nil
	file_insured_service_service_proto_goTypes = nil
	file_insured_service_service_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// InsuredManagerClient is the client API for InsuredManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type InsuredManagerClient interface {
	CreateInsured(ctx context.Context, in *model.CreateInsuredRequest, opts ...grpc.CallOption) (*model.CreateInsuredResponse, error)
}

type insuredManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewInsuredManagerClient(cc grpc.ClientConnInterface) InsuredManagerClient {
	return &insuredManagerClient{cc}
}

func (c *insuredManagerClient) CreateInsured(ctx context.Context, in *model.CreateInsuredRequest, opts ...grpc.CallOption) (*model.CreateInsuredResponse, error) {
	out := new(model.CreateInsuredResponse)
	err := c.cc.Invoke(ctx, "/service.InsuredManager/CreateInsured", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InsuredManagerServer is the server API for InsuredManager service.
type InsuredManagerServer interface {
	CreateInsured(context.Context, *model.CreateInsuredRequest) (*model.CreateInsuredResponse, error)
}

// UnimplementedInsuredManagerServer can be embedded to have forward compatible implementations.
type UnimplementedInsuredManagerServer struct {
}

func (*UnimplementedInsuredManagerServer) CreateInsured(context.Context, *model.CreateInsuredRequest) (*model.CreateInsuredResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInsured not implemented")
}

func RegisterInsuredManagerServer(s *grpc.Server, srv InsuredManagerServer) {
	s.RegisterService(&_InsuredManager_serviceDesc, srv)
}

func _InsuredManager_CreateInsured_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(model.CreateInsuredRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InsuredManagerServer).CreateInsured(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/service.InsuredManager/CreateInsured",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InsuredManagerServer).CreateInsured(ctx, req.(*model.CreateInsuredRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _InsuredManager_serviceDesc = grpc.ServiceDesc{
	ServiceName: "service.InsuredManager",
	HandlerType: (*InsuredManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateInsured",
			Handler:    _InsuredManager_CreateInsured_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "insured/service/service.proto",
}
