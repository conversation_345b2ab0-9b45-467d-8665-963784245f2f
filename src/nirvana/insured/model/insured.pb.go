// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: insured/model/insured.proto

package model

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	proto1 "nirvanatech.com/nirvana/common-go/proto"
	proto "nirvanatech.com/nirvana/insurance-core/proto"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ContactInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Phone string `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`
}

func (x *ContactInfo) Reset() {
	*x = ContactInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insured_model_insured_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContactInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactInfo) ProtoMessage() {}

func (x *ContactInfo) ProtoReflect() protoreflect.Message {
	mi := &file_insured_model_insured_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactInfo.ProtoReflect.Descriptor instead.
func (*ContactInfo) Descriptor() ([]byte, []int) {
	return file_insured_model_insured_proto_rawDescGZIP(), []int{0}
}

func (x *ContactInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ContactInfo) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type Insured struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string                   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name               *proto.InsuredName       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Address            *proto1.Address          `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	ContactInfo        *ContactInfo             `protobuf:"bytes,4,opt,name=contactInfo,proto3" json:"contactInfo,omitempty"`
	ExternalIdentifier *proto.InsuredIdentifier `protobuf:"bytes,5,opt,name=externalIdentifier,proto3" json:"externalIdentifier,omitempty"`
}

func (x *Insured) Reset() {
	*x = Insured{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insured_model_insured_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Insured) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Insured) ProtoMessage() {}

func (x *Insured) ProtoReflect() protoreflect.Message {
	mi := &file_insured_model_insured_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Insured.ProtoReflect.Descriptor instead.
func (*Insured) Descriptor() ([]byte, []int) {
	return file_insured_model_insured_proto_rawDescGZIP(), []int{1}
}

func (x *Insured) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Insured) GetName() *proto.InsuredName {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Insured) GetAddress() *proto1.Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *Insured) GetContactInfo() *ContactInfo {
	if x != nil {
		return x.ContactInfo
	}
	return nil
}

func (x *Insured) GetExternalIdentifier() *proto.InsuredIdentifier {
	if x != nil {
		return x.ExternalIdentifier
	}
	return nil
}

type CreateInsuredRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Insured *Insured `protobuf:"bytes,1,opt,name=insured,proto3" json:"insured,omitempty"`
}

func (x *CreateInsuredRequest) Reset() {
	*x = CreateInsuredRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insured_model_insured_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateInsuredRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateInsuredRequest) ProtoMessage() {}

func (x *CreateInsuredRequest) ProtoReflect() protoreflect.Message {
	mi := &file_insured_model_insured_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateInsuredRequest.ProtoReflect.Descriptor instead.
func (*CreateInsuredRequest) Descriptor() ([]byte, []int) {
	return file_insured_model_insured_proto_rawDescGZIP(), []int{2}
}

func (x *CreateInsuredRequest) GetInsured() *Insured {
	if x != nil {
		return x.Insured
	}
	return nil
}

type CreateInsuredResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	InsuredAlreadyExisted bool   `protobuf:"varint,2,opt,name=insuredAlreadyExisted,proto3" json:"insuredAlreadyExisted,omitempty"`
}

func (x *CreateInsuredResponse) Reset() {
	*x = CreateInsuredResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_insured_model_insured_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateInsuredResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateInsuredResponse) ProtoMessage() {}

func (x *CreateInsuredResponse) ProtoReflect() protoreflect.Message {
	mi := &file_insured_model_insured_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateInsuredResponse.ProtoReflect.Descriptor instead.
func (*CreateInsuredResponse) Descriptor() ([]byte, []int) {
	return file_insured_model_insured_proto_rawDescGZIP(), []int{3}
}

func (x *CreateInsuredResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CreateInsuredResponse) GetInsuredAlreadyExisted() bool {
	if x != nil {
		return x.InsuredAlreadyExisted
	}
	return false
}

var File_insured_model_insured_proto protoreflect.FileDescriptor

var file_insured_model_insured_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x69, 0x6e,
	0x73, 0x75, 0x72, 0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x39, 0x0a, 0x0b, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x22, 0x80, 0x02, 0x0a, 0x07, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x65, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x36,
	0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x51, 0x0a, 0x12, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x12, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x42, 0x0a, 0x14, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2a, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2e, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x65, 0x64, 0x52, 0x07, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x22, 0x5d, 0x0a,
	0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x41, 0x6c,
	0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x42, 0x27, 0x5a, 0x25,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x6e, 0x69, 0x72, 0x76, 0x61, 0x6e, 0x61, 0x2f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_insured_model_insured_proto_rawDescOnce sync.Once
	file_insured_model_insured_proto_rawDescData = file_insured_model_insured_proto_rawDesc
)

func file_insured_model_insured_proto_rawDescGZIP() []byte {
	file_insured_model_insured_proto_rawDescOnce.Do(func() {
		file_insured_model_insured_proto_rawDescData = protoimpl.X.CompressGZIP(file_insured_model_insured_proto_rawDescData)
	})
	return file_insured_model_insured_proto_rawDescData
}

var file_insured_model_insured_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_insured_model_insured_proto_goTypes = []interface{}{
	(*ContactInfo)(nil),             // 0: insured.ContactInfo
	(*Insured)(nil),                 // 1: insured.Insured
	(*CreateInsuredRequest)(nil),    // 2: insured.CreateInsuredRequest
	(*CreateInsuredResponse)(nil),   // 3: insured.CreateInsuredResponse
	(*proto.InsuredName)(nil),       // 4: insurance_core.InsuredName
	(*proto1.Address)(nil),          // 5: common.Address
	(*proto.InsuredIdentifier)(nil), // 6: insurance_core.InsuredIdentifier
}
var file_insured_model_insured_proto_depIdxs = []int32{
	4, // 0: insured.Insured.name:type_name -> insurance_core.InsuredName
	5, // 1: insured.Insured.address:type_name -> common.Address
	0, // 2: insured.Insured.contactInfo:type_name -> insured.ContactInfo
	6, // 3: insured.Insured.externalIdentifier:type_name -> insurance_core.InsuredIdentifier
	1, // 4: insured.CreateInsuredRequest.insured:type_name -> insured.Insured
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_insured_model_insured_proto_init() }
func file_insured_model_insured_proto_init() {
	if File_insured_model_insured_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_insured_model_insured_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContactInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insured_model_insured_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Insured); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insured_model_insured_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateInsuredRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_insured_model_insured_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateInsuredResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_insured_model_insured_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_insured_model_insured_proto_goTypes,
		DependencyIndexes: file_insured_model_insured_proto_depIdxs,
		MessageInfos:      file_insured_model_insured_proto_msgTypes,
	}.Build()
	File_insured_model_insured_proto = out.File
	file_insured_model_insured_proto_rawDesc = nil
	file_insured_model_insured_proto_goTypes = nil
	file_insured_model_insured_proto_depIdxs = nil
}
