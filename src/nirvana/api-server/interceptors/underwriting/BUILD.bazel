load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "underwriting",
    srcs = [
        "fx.go",
        "helper.go",
        "underwriting.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/interceptors/underwriting",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/handlers/application",
        "//nirvana/api-server/handlers/underwriting",
        "//nirvana/api-server/handlers/utils",
        "//nirvana/api-server/helpers",
        "//nirvana/api-server/interceptors/underwriting/deps",
        "//nirvana/api-server/interceptors/utils",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/openapi-specs/api_server_uw/fleet_uw",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/underwriting",
        "//nirvana/underwriting/app_review/widgets/drivers",
        "//nirvana/underwriting/rule-engine/appetite_factors/appetite_factor",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_labstack_echo_v4//:echo",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
    ],
)
