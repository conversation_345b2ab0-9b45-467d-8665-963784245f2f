package deps

import (
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	ruleengine "nirvanatech.com/nirvana/api-server/rule-engine"
	endorsement_request "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	endorsement_review "nirvanatech.com/nirvana/application/endorsementapp/endorsement-review"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/app_review_widget"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	dbendorsementreview "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
	"nirvanatech.com/nirvana/db-api/db_wrappers/file_upload"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/reports"
	risk_factors_db "nirvanatech.com/nirvana/db-api/db_wrappers/risk_factors"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/emailer"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/feature_store"
	"nirvanatech.com/nirvana/fmcsa/scraper"
	"nirvanatech.com/nirvana/infra/authz/checker"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	"nirvanatech.com/nirvana/pricing/explainability"
	"nirvanatech.com/nirvana/quoting/app_state_machine"
	"nirvanatech.com/nirvana/quoting/clearance"
	"nirvanatech.com/nirvana/quoting/clearance/emails"
	reportingregistry "nirvanatech.com/nirvana/reporting"
	safety_reporter "nirvanatech.com/nirvana/safety/reporter"
	"nirvanatech.com/nirvana/safety/scores/objective_grade/manager"
	"nirvanatech.com/nirvana/servers/telematicsv2"
	"nirvanatech.com/nirvana/servers/vehicles"
	tsp_connections "nirvanatech.com/nirvana/telematics/connections"
	uw_app_review "nirvanatech.com/nirvana/underwriting/app_review"
	actions "nirvanatech.com/nirvana/underwriting/app_review/actions/permission"
	"nirvanatech.com/nirvana/underwriting/app_review_widget_manager"
	"nirvanatech.com/nirvana/underwriting/common"
	"nirvanatech.com/nirvana/underwriting/risk_factors"
	"nirvanatech.com/nirvana/underwriting/scheduler"
	"nirvanatech.com/nirvana/underwriting/state_machine"
	"nirvanatech.com/nirvana/underwriting/task/taskmanager"
)

type Deps struct {
	fx.In

	AppStateMachineWrapper          app_state_machine.AppStateMachineWrapper
	ApplicationWrapper              application.DataWrapper
	AuthWrapper                     auth.DataWrapper
	AgencyWrapper                   agency.DataWrapper
	ApplicationReviewWrapper        uw.ApplicationReviewWrapper
	ApplicationReviewManager        uw_app_review.ReviewManager
	FileUploadManager               file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
	AuthzChecker                    *checker.Checker
	EventsHandler                   events.EventsHandler
	FeatureFlagClient               feature_flag_lib.Client
	SafetyReporter                  *safety_reporter.Reporter
	Emailer                         emailer.Emailer
	UWScheduler                     scheduler.UwScheduler
	Jobber                          quoting_jobber.Client
	AppReviewWrapper                state_machine.AppReviewWrapper
	VehiclesServiceClient           vehicles.VehiclesServiceClient
	FileUploadWrapper               file_upload.DataWrapper
	ParsedLossRunWrapper            pibit.DataWrapper
	AppReviewWidgetManager          app_review_widget_manager.AppReviewWidgetManager
	TspConnectionManager            *tsp_connections.TSPConnManager
	TelematicsDataPlatformClient    telematicsv2.TelematicsPipelineManager
	FMCSAWrapper                    fmcsa.DataWrapper
	FMCSAScraperClient              *scraper.Client
	AppReviewWidgetWrapper          app_review_widget.DataWrapper
	RuleEngine                      *ruleengine.RuleEngine
	Config                          *config.Config
	ObjectiveGradeManager           manager.ObjectiveGradeManager
	ReviewManager                   uw_app_review.ReviewManager
	UWSafetyFetcher                 *common.UWSafetyFetcher
	FormsWrapper                    forms.FormWrapper
	MetricsClient                   statsd.Statter
	S3FileManager                   file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
	ActionPermissionManager         actions.ActionPermissionManager
	ClearanceStateManager           clearance.StateManager
	ClearanceEmailer                emails.EmailWrapper
	FetcherClientFactory            data_fetching.FetcherClientFactory
	ProcessorClientFactory          data_processing.ProcessorClientFactory
	FeatureStore                    feature_store.FeatureStore
	ReviewReadinessTaskManager      taskmanager.TaskManager
	EndorsementReviewManager        endorsement_review.Manager
	InsuranceBundleManagerClient    service.InsuranceBundleManagerClient
	EndorsementRequestManager       endorsement_request.Manager
	AdmittedAppWrapper              nf_app.Wrapper[*admitted_app.AdmittedApp]
	NFApplicationReviewWrapper      nf_app_review.Wrapper
	Clock                           clock.Clock
	ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
	WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
	ReportWrapper                   *reports.ReportWrapper
	ReportRegistry                  reportingregistry.Registry
	DBEndorsementReviewWrapper      dbendorsementreview.Wrapper
	RiskFactorsServiceClient        risk_factors.RiskFactorsServiceClient
	// TODO: remove this dependency from tests as well
	RiskFactorWrapper     risk_factors_db.RiskFactorWrapper
	WorksheetRiskFactor   risk_factors_db.WorksheetRiskFactorsWrapper
	ExplainabilityManager explainability.Manager
}
