package underwriting

import (
	"fmt"
	"net/http"
	"time"

	openapi_types "github.com/oapi-codegen/runtime/types"

	utils2 "nirvanatech.com/nirvana/api-server/handlers/utils"
	"nirvanatech.com/nirvana/common-go/pointer_utils"

	"nirvanatech.com/nirvana/common-go/us_states"

	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/underwriting/app_review/widgets/drivers"
	"nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/appetite_factor"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"

	"nirvanatech.com/nirvana/api-server/common"
	app_handlers "nirvanatech.com/nirvana/api-server/handlers/application"
	uw_handlers "nirvanatech.com/nirvana/api-server/handlers/underwriting"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/api-server/interceptors/underwriting/deps"
	"nirvanatech.com/nirvana/api-server/interceptors/utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/openapi-specs/api_server_uw/fleet_uw"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

const (
	FileString = "file"
)

type UwEchoInterceptor struct {
	Deps deps.Deps
}

// Please ensure that this line is always at the top to avoid inconveniences.
var _ fleet_uw.ServerInterface = &UwEchoInterceptor{}

func (u *UwEchoInterceptor) GetAuthorityRequestsForApplication(ctx echo.Context, applicationReviewId openapi_types.UUID) error {
	// Mock implementation for authority requests list
	req1ID := uuid.New()
	requesterID := uuid.New()
	now := time.Now()

	// Create mock request data using proper typed structures
	requestData1 := oapi_uw.AuthorityDeclineRequestPayload{
		ApplicationReviewId: applicationReviewId,
		RequestType:         oapi_uw.RequestTypeDecline,
		DeclineReasons: []oapi_uw.AuthorityDeclineReason{
			{
				Code:        "RISK_TOO_HIGH",
				Description: "The risk profile is too high for standard approval",
			},
		},
		TargetQuotePrice:   15000.00,
		NoQuoteExplanation: "Unable to provide quote due to excessive risk factors and previous claims history",
	}

	mockRequests := []oapi_uw.AuthorityRequestDetails{
		{
			RequestId:           req1ID,
			ApplicationReviewId: applicationReviewId,
			RequestType:         oapi_uw.RequestTypeDecline,
			State:               oapi_uw.RequestStatePending,
			RequestData:         oapi_uw.AuthorityRequestDetails_RequestData{},
			RequesterId:         requesterID,
			RequesterName:       pointer_utils.ToPointer("John Smith"),
			CreatedAt:           now.Add(-2 * time.Hour),
			UpdatedAt:           now.Add(-1 * time.Hour),
		},
	}

	// Create the properly typed request data unions
	var requestDataUnion1 oapi_uw.AuthorityRequestDetails_RequestData
	err := requestDataUnion1.FromAuthorityDeclineRequestPayload(requestData1)
	if err != nil {
		return err
	}

	// Set the proper request data in the mock requests
	mockRequests[0].RequestData = requestDataUnion1

	response := oapi_uw.AuthorityRequestList{
		Requests: mockRequests,
		Total:    len(mockRequests),
	}

	return ctx.JSON(http.StatusOK, response)
}

func (u *UwEchoInterceptor) CreateAuthorityRequest(ctx echo.Context) error {
	var req oapi_uw.AuthorityRequest
	err := ctx.Bind(&req)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "Request data has wrong format")
	}

	// Mock implementation for creating authority request
	newRequestID := uuid.New()
	now := time.Now()

	response := oapi_uw.AuthorityRequestResponse{
		RequestId:           newRequestID,
		ApplicationReviewId: req.ApplicationReviewId,
		RequestType:         req.RequestType,
		State:               oapi_uw.RequestStateDraft,
		CreatedAt:           now,
	}

	return ctx.JSON(http.StatusCreated, response)
}

func (u *UwEchoInterceptor) GetAuthorityRequest(ctx echo.Context, requestId openapi_types.UUID) error {
	// Mock implementation for getting authority request details
	applicationReviewID := uuid.New()
	requesterID := uuid.New()
	reviewerID := uuid.New()
	now := time.Now()

	// Create mock request data using proper typed structure
	requestData := oapi_uw.AuthorityDeclineRequestPayload{
		ApplicationReviewId: applicationReviewID,
		RequestType:         oapi_uw.RequestTypeDecline,
		DeclineReasons: []oapi_uw.AuthorityDeclineReason{
			{
				Code:        "HIGH_RISK_PROFILE",
				Description: "Application shows indicators of high risk that require authority approval",
			},
		},
		TargetQuotePrice:   18500.00,
		NoQuoteExplanation: "Risk assessment indicates need for specialized underwriting review and authority approval",
	}

	// Create the properly typed request data union
	var requestDataUnion oapi_uw.AuthorityRequestDetails_RequestData
	err := requestDataUnion.FromAuthorityDeclineRequestPayload(requestData)
	if err != nil {
		return err
	}

	mockRequest := oapi_uw.AuthorityRequestDetails{
		RequestId:           requestId,
		ApplicationReviewId: applicationReviewID,
		RequestType:         oapi_uw.RequestTypeDecline,
		State:               oapi_uw.RequestStatePending,
		RequestData:         requestDataUnion,
		RequesterId:         requesterID,
		RequesterName:       pointer_utils.ToPointer("Alice Johnson"),
		CreatedAt:           now.Add(-4 * time.Hour),
		UpdatedAt:           now.Add(-2 * time.Hour),
		LastReviewedBy:      &reviewerID,
		LastReviewedAt:      pointer_utils.ToPointer(now.Add(-2 * time.Hour)),
		ReviewerName:        pointer_utils.ToPointer("Bob Wilson"),
	}

	return ctx.JSON(http.StatusOK, mockRequest)
}

func (u *UwEchoInterceptor) ProcessAuthorityRequest(ctx echo.Context, requestId openapi_types.UUID) error {
	var req oapi_uw.ProcessAuthorityRequest
	err := ctx.Bind(&req)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "Request data has wrong format")
	}

	// Mock implementation for processing authority request
	applicationReviewID := uuid.New()
	requesterID := uuid.New()
	reviewerID := uuid.New()
	now := time.Now()

	// Determine the new state based on the action
	var newState oapi_uw.AuthorityRequestState
	var executedAt *time.Time

	switch req.Action {
	case oapi_uw.ReviewActionApprove:
		newState = oapi_uw.RequestStateExecuted
		executedAt = &now
	case oapi_uw.ReviewActionReject:
		newState = oapi_uw.RequestStateRejected
	default:
		newState = oapi_uw.RequestStatePending
	}

	// Create mock request data using proper typed structure
	requestData := oapi_uw.AuthorityDeclineRequestPayload{
		ApplicationReviewId: applicationReviewID,
		RequestType:         oapi_uw.RequestTypeDecline,
		DeclineReasons: []oapi_uw.AuthorityDeclineReason{
			{
				Code:        "PROCESSED_REQUEST",
				Description: "Authority request has been processed and decision made",
			},
		},
		TargetQuotePrice:   20000.00,
		NoQuoteExplanation: "Request processed with authority decision",
	}

	// Create the properly typed request data union
	var requestDataUnion oapi_uw.AuthorityRequestDetails_RequestData
	err = requestDataUnion.FromAuthorityDeclineRequestPayload(requestData)
	if err != nil {
		return err
	}

	mockProcessedRequest := oapi_uw.AuthorityRequestDetails{
		RequestId:           requestId,
		ApplicationReviewId: applicationReviewID,
		RequestType:         oapi_uw.RequestTypeDecline,
		State:               newState,
		RequestData:         requestDataUnion,
		RequesterId:         requesterID,
		RequesterName:       pointer_utils.ToPointer("Charlie Brown"),
		CreatedAt:           now.Add(-6 * time.Hour),
		UpdatedAt:           now,
		LastReviewedBy:      &reviewerID,
		LastReviewedAt:      &now,
		ReviewerName:        pointer_utils.ToPointer("Diana Prince"),
		ExecutedAt:          executedAt,
	}

	return ctx.JSON(http.StatusOK, mockProcessedRequest)
}

func (u *UwEchoInterceptor) UpdateWorksheet(ctx echo.Context, worksheetID string) error {
	context := ctx.Request().Context()
	var req oapi_uw.UpdateWorksheetRequest
	err := ctx.Bind(&req)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "Request data has wrong format")
	}
	updateReq := uw_handlers.UpdateWorksheetRequest{
		WorksheetId: worksheetID,
		Request:     req,
	}
	authzResp := uw_handlers.HasPermissionOverRiskWorksheet(
		context, u.Deps, authz.UserFromContext(context),
		authz.WriteAction, worksheetID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.UpdateWorksheet(context, u.Deps, updateReq)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (u *UwEchoInterceptor) GetWorksheet(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.GetWorksheet(context, u.Deps, applicationReviewID)
	if response.Error != nil {
		return response.Error
	}

	return ctx.JSON(http.StatusOK, response.Result)
}

func (u *UwEchoInterceptor) CreateRiskWorksheet(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	request := uw_handlers.CreateRiskWorksheetRequest{
		ApplicationReviewID: applicationReviewID,
	}
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.WriteAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.CreateRiskWorksheet(context, u.Deps, request)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusCreated, nil)
}

func (u *UwEchoInterceptor) SuggestRiskFactor(ctx echo.Context, worksheetID string) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverRiskWorksheet(
		context, u.Deps, authz.UserFromContext(context),
		authz.WriteAction, worksheetID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	var req oapi_uw.SuggestedRiskFactor
	err := ctx.Bind(&req)
	if err != nil {
		return err
	}
	suggestRiskFactorRequest := uw_handlers.SuggestRiskFactorRequest{
		SuggestRiskFactorRequest: req,
		WorksheetId:              worksheetID,
	}
	response := uw_handlers.SuggestRiskFactor(context, u.Deps, suggestRiskFactorRequest)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (u *UwEchoInterceptor) UpdateWorksheetPricing(ctx echo.Context, worksheetID string) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverRiskWorksheet(
		context, u.Deps, authz.UserFromContext(context),
		authz.WriteAction, worksheetID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	var req oapi_uw.UpdatePricingRequest
	err := ctx.Bind(&req)
	if err != nil {
		return err
	}
	updatePricingRequest := uw_handlers.UpdatePricingRequest{
		PricingRequest: req,
		WorksheetId:    worksheetID,
	}
	response := uw_handlers.UpdatePricing(context, u.Deps, updatePricingRequest)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (u *UwEchoInterceptor) DeleteWorksheetRiskFactor(ctx echo.Context, worksheetRiskFactorID string) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverWorksheetFactor(
		context, u.Deps, authz.UserFromContext(context),
		authz.WriteAction, worksheetRiskFactorID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.DeleteRiskFactor(context, u.Deps, uw_handlers.DeleteRiskFactorRequest{
		WorksheetRiskFactorID: worksheetRiskFactorID,
	})
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (u *UwEchoInterceptor) UpdateWorksheetRiskFactor(ctx echo.Context, worksheetRiskFactorID string) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverWorksheetFactor(
		context, u.Deps, authz.UserFromContext(context),
		authz.WriteAction, worksheetRiskFactorID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	var req oapi_uw.WorksheetRiskFactorUpdateRequest
	err := ctx.Bind(&req)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "Request data has wrong format")
	}
	updateReq := uw_handlers.UpdateWorksheetRiskFactorRequest{
		WorksheetRiskFactorID:            worksheetRiskFactorID,
		WorksheetRiskFactorUpdateRequest: req,
	}
	response := uw_handlers.UpdateWorksheetRiskFactor(context, u.Deps, updateReq)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (u *UwEchoInterceptor) AddWorksheetRiskFactor(ctx echo.Context, worksheetID string) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverRiskWorksheet(
		context, u.Deps, authz.UserFromContext(context),
		authz.WriteAction, worksheetID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	var req oapi_uw.WorksheetRiskFactorAddRequest
	err := ctx.Bind(&req)
	if err != nil {
		return err
	}
	addReq := uw_handlers.AddWorksheetRiskFactorRequest{
		WorksheetId:                   worksheetID,
		AddWorksheetRiskFactorRequest: &req,
	}
	response := uw_handlers.AddWorksheetRiskFactor(context, u.Deps, addReq)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (u *UwEchoInterceptor) ListRiskFactors(ctx echo.Context) error {
	context := ctx.Request().Context()
	response := uw_handlers.ListRiskFactors(context, u.Deps)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, response.Result)
}

func (u *UwEchoInterceptor) GetMstReferral(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleGetMSTReferralRules(context, u.Deps, applicationReviewID)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, response.Result)
}

func (u *UwEchoInterceptor) UpdateMstReferral(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	var req oapi_uw.PatchApplicationReviewMstReferralRule
	err := ctx.Bind(&req)
	if err != nil {
		return err
	}

	response := uw_handlers.HandlePatchMSTReferralRule(context, u.Deps, applicationReviewID, req)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (u *UwEchoInterceptor) GetVinVisibilityCheckList(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleGetVinVisibilityChecklist(context, u.Deps, string(applicationReviewID))
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, response.Result)
}

func (u *UwEchoInterceptor) UpdateVinVisibilityCheckList(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	var req oapi_uw.ApplicationReviewVinVisibilityChecklistPut
	err := ctx.Bind(&req)
	if err != nil {
		return err
	}
	response := uw_handlers.HandlePutVinVisibilityChecklist(context, u.Deps,
		string(applicationReviewID), req)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, response.Result)
}

func (u *UwEchoInterceptor) GetVinVisibility(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleGetVinVisibility(context,
		u.Deps, applicationReviewID)
	if response.Error != nil {
		log.Error(context,
			"HandleGetVinVisibility: failed to fetch vin visibility", log.Err(response.Error))
		//      return response.Error
	}
	return ctx.JSON(http.StatusOK, response.Result)
}

func (u *UwEchoInterceptor) RegenerateAuthority(ctx echo.Context) error {
	context := ctx.Request().Context()
	response := uw_handlers.HandleRegenerateAuthorityInfo(context, u.Deps)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetMileageEstimateReasons(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	request := uw_handlers.MileageEstimateReasonsRequest{
		ApplicationReviewId: applicationReviewID,
	}
	authzResp := uw_handlers.HasPermissionOverAppReview(
		ctx.Request().Context(), u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleGetMileageEstimateReasons(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) BackfillApplicationReview(ctx echo.Context) error {
	context := ctx.Request().Context()
	var body oapi_uw.ApplicationReviewWidgetBackFillForm
	err := ctx.Bind(&body)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}

	req := uw_handlers.PostBackfillAppReviewWidgetRequest{
		ApplicationReviewIds: body.ApplicationReviewIDs,
		WidgetEnums:          body.WidgetEnums,
		BackfillAll:          body.BackFillAll,
	}

	response := uw_handlers.HandleBackfillAppReviewWidget(context, u.Deps, req)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) RepullApplicationReviewWidget(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var body []oapi_common.ApplicationReviewWidgetEnum
	err := ctx.Bind(&body)
	if err != nil {
		return err
	}
	req := uw_handlers.PostRepullWidgetDataRequest{
		ApplicationReviewId: applicationReviewID,
		WidgetEnums:         body,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandlePostRepullWidgetData(context, u.Deps, req)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetSaferInfo(ctx echo.Context, params fleet_uw.GetSaferInfoParams) error {
	request := uw_handlers.GetSaferDotInfoRequest{
		ForceRefresh:        params.ForceRefresh,
		DotNumber:           params.DotNumber,
		ApplicationReviewId: params.ApplicationID,
	}
	context := ctx.Request().Context()
	user := authz.UserFromContext(context)
	response := uw_handlers.HandleGetSaferDotInfo(context, u.Deps, request, user.ID.String())
	return ctx.JSON(response.StatusCode(), response.Body())
}

// NewUwEchoInterceptor creates a new instance of UwEchoInterceptor.
// The interceptor takes care of echo specific logic, and then delegates the
// handling logic to ApiServerHandler.
func NewUwEchoInterceptor(deps deps.Deps) *UwEchoInterceptor {
	return &UwEchoInterceptor{
		Deps: deps,
	}
}

func (u *UwEchoInterceptor) PutAncillaryCoverages(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var ancillaryCoverages oapi_common.AncillaryCoverages
	err := ctx.Bind(&ancillaryCoverages)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data for UpdateTelematicsReminderEmailInfo has wrong format")
	}
	req := uw_handlers.PutAncillaryCoverageRequest{
		ApplicationReviewId: applicationReviewID,
		AncillaryCoverages:  ancillaryCoverages,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandlePutAncillaryCoverage(context, u.Deps, req)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetAncillaryCoverages(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	req := uw_handlers.GetAncillaryCoverageRequest{
		ApplicationReviewId: applicationReviewID,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleGetAncillaryCoverage(context, u.Deps, req)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationTSPConnectionInfo(ctx echo.Context, applicationID oapi_uw.ApplicationID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HandleGetTelematicsInfoAuthz(context, u.Deps, applicationID)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleGetTelematicsInfo(context, u.Deps, applicationID)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateTelematicsReminderEmailInfo(
	ctx echo.Context, applicationID oapi_uw.ApplicationID,
) error {
	var updateRequest oapi_uw.TelematicsReminderEmailInfo
	err := ctx.Bind(&updateRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data for UpdateTelematicsReminderEmailInfo has wrong format")
	}
	request := uw_handlers.UpdateTelematicsReminderEmailInfoRequest{
		ApplicationID:          applicationID,
		TelematicsReminderInfo: updateRequest,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HandleUpdateTelematicsReminderEmailInfoAuthz(
		context, u.Deps, request,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleUpdateTelematicsReminderEmailInfo(
		context, u.Deps, request,
	)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetUnderwriters(ctx echo.Context) error {
	request := uw_handlers.GetUnderwritersRequest{}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HandleGetUnderwritersAuthz(context, u.Deps, request)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetUnderwriters(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewPermissions(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.GetApplicationReviewPermissionsRequest{
		ApplicationReviewId: applicationReviewID,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewPermissions(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) SetMVRPull(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.PostMVRPullRequest{
		ApplicationReviewId: applicationReviewID,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandlePostMVRPull(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetV2UnderwritingApplicationReviewList(ctx echo.Context, params fleet_uw.GetV2UnderwritingApplicationReviewListParams) error {
	context := ctx.Request().Context()
	var (
		effectiveDateOnOrAfter  *time.Time = nil
		effectiveDateOnOrBefore *time.Time = nil
	)

	if params.EffectiveDateAfter != nil && !params.EffectiveDateAfter.Equal(time.Time{}) {
		effectiveDateOnOrAfter = &params.EffectiveDateAfter.Time
	}

	if params.EffectiveDateBefore != nil && !params.EffectiveDateBefore.Equal(time.Time{}) {
		effectiveDateOnOrBefore = &params.EffectiveDateBefore.Time
	}

	agencyID, err := utils.GetAgencyId(context)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Internal error")
	}

	var recommendedActions []appetite_factor.RecommendedAction
	if params.RecommendedActions != nil && len(*params.RecommendedActions) > 0 {
		parsedActions, err := parseRecommendedActionsFromRequest(*params.RecommendedActions)
		if err != nil {
			return err
		}
		recommendedActions = *parsedActions
	}

	// if either sort by or sort direction is provided without the other, throw bad request
	if (params.SortBy != nil) != (params.SortDirection != nil) {
		return helpers.ErrorMessageJSON(
			ctx,
			http.StatusBadRequest,
			errors.New("either provide both, sort by and sort direction, or none"),
			"bad request",
		)
	}

	nonTerminalStatesFilterFlag := utils2.ShouldFilterNonTerminalAppReviewStates(context, u.Deps.FeatureFlagClient)

	request := uw_handlers.GetApplicationReviewListRequest{
		Options: uw.Options{
			AgencyId:                    agencyID,
			PageSize:                    params.Size,
			Query:                       params.Q,
			Tab:                         params.Tab,
			UnderwriterID:               params.UnderWriterID,
			EffectiveDateOnOrAfter:      effectiveDateOnOrAfter,
			EffectiveDateOnOrBefore:     effectiveDateOnOrBefore,
			RecommendedActions:          recommendedActions,
			SortBy:                      params.SortBy,
			SortDirection:               params.SortDirection,
			FilterNonTerminalStatesFlag: pointer_utils.Bool(nonTerminalStatesFilterFlag),
		},
		CurrentCursor: params.Cursor,
	}

	response := uw_handlers.HandleGetApplicationReviewListV2(context, u.Deps, request)

	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, response.Result)
}

func (u *UwEchoInterceptor) GetUnderwritingApplicationReviewDataCompletionList(ctx echo.Context, params fleet_uw.GetUnderwritingApplicationReviewDataCompletionListParams) error {
	context := ctx.Request().Context()
	agencyID, err := utils.GetAgencyId(context)
	if err != nil {
		return common.NewNirvanaInternalServerWithReason(err, "Error while fetching agency details of user")
	}
	if params.Tab == nil {
		err := errors.New("tab missing in request params")
		return common.NewNirvanaBadRequestErrorWithReason(err, "Tab is mandatory in request")
	}
	pageSize := application.DefaultPageSize
	if params.Size != nil {
		pageSize = *params.Size
	}

	request := uw_handlers.GetDataCompletionApplicationReviewListRequest{
		Options: uw.DataCompletionPaginatedReviewsOptions{
			AgencyId: *agencyID,
			PageSize: pageSize,
			Query:    params.Q,
			Tab:      *params.Tab,
		},
		CurrentCursor: params.Cursor,
	}
	response := uw_handlers.HandleGetDataCompletionAppReviewList(context, u.Deps, request)
	if response.Error != nil {
		log.Error(context, "error while executing HandleGetDataCompletionAppReviewList", log.Err(response.Error))
		return response.Error
	}
	return ctx.JSON(http.StatusOK, response.Result)
}

func (u *UwEchoInterceptor) GetUnderwritingApplicationReviewCount(
	ctx echo.Context,
	params fleet_uw.GetUnderwritingApplicationReviewCountParams,
) error {
	context := ctx.Request().Context()
	var (
		effectiveDateOnOrAfter  *time.Time = nil
		effectiveDateOnOrBefore *time.Time = nil
	)

	if params.EffectiveDateAfter != nil && !params.EffectiveDateAfter.Equal(time.Time{}) {
		effectiveDateOnOrAfter = &params.EffectiveDateAfter.Time
	}

	if params.EffectiveDateBefore != nil && !params.EffectiveDateBefore.Equal(time.Time{}) {
		effectiveDateOnOrBefore = &params.EffectiveDateBefore.Time
	}

	var recommendedActions []appetite_factor.RecommendedAction
	if params.RecommendedActions != nil && len(*params.RecommendedActions) > 0 {
		parsedActions, err := parseRecommendedActionsFromRequest(*params.RecommendedActions)
		if err != nil {
			return err
		}
		recommendedActions = *parsedActions
	}

	agencyID, err := utils.GetAgencyId(context)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Internal error")
	}

	request := uw_handlers.GetApplicationReviewListCountRequest{
		Options: uw.CountOptions{
			AgencyID:                agencyID,
			Query:                   params.Q,
			UnderwriterID:           params.UnderWriterID,
			EffectiveDateOnOrAfter:  effectiveDateOnOrAfter,
			EffectiveDateOnOrBefore: effectiveDateOnOrBefore,
			RecommendedActions:      recommendedActions,
		},
	}

	response := uw_handlers.HandleGetApplicationReviewListCount(context, u.Deps, request)

	return ctx.JSON(response.StatusCode(), response.Body())
}

func parseRecommendedActionsFromRequest(actions []oapi_uw.RecommendedAction) (*[]appetite_factor.RecommendedAction, error) {
	var parsedActions []appetite_factor.RecommendedAction
	for _, action := range actions {
		actionDeserialized, err := ConvertRecommendedActionFromOAPI(action)
		if err != nil {
			return nil, common.NewNirvanaBadRequestErrorWithReason(err, fmt.Sprintf("Invalid recommended action: %s", action))
		}
		parsedActions = append(parsedActions, actionDeserialized)
	}
	return &parsedActions, nil
}

func (u *UwEchoInterceptor) GetApplicationReviewAssignees(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.GetApplicationReviewAssigneesRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)

	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewAssignees(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewAssignees(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var updateRequest oapi_uw.ApplicationReviewAssigneesForm
	err := ctx.Bind(&updateRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateApplicationReviewAssigneesRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                updateRequest,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleUpdateApplicationReviewAssignees(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewById(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetApplicationReviewRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewById(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewAccountGrade(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetApplicationReviewAccountGradeRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewAccountGrade(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewAccountGrade(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var updateRequest oapi_uw.ApplicationReviewAccountGrade
	err := ctx.Bind(&updateRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateAccountGradeRequest{
		ApplicationReviewId: string(applicationReviewID),
		Grade:               updateRequest.Grade,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateAccountGrade(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewActions(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.GetApplicationReviewActionsRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	resp := uw_handlers.HandleGetApplicationActions(context, u.Deps, request)
	if resp.Error != nil {
		return resp.Error
	}

	return ctx.JSON(http.StatusOK, *resp.Data)
}

func (u *UwEchoInterceptor) GetApplicationReviewNotes(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetApplicationReviewNotesRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewNotes(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewNotes(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var notesRequest oapi_uw.ApplicationReviewNotes
	err := ctx.Bind(&notesRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateApplicationReviewNotesRequest{
		ApplicationReviewId: string(applicationReviewID),
		Data:                notesRequest,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateApplicationReviewNotes(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReview(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var patchRequest oapi_uw.UpdateApplicationReviewRequest
	err := ctx.Bind(&patchRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateApplicationReviewRequest{
		ApplicationReviewId: applicationReviewID,
		Request:             patchRequest,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateApplicationReview(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewWidgetSummary(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetApplicationReviewWidgetSummaryRequest{
		ApplicationReviewId: string(applicationReviewID),
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewWidgetSummary(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewSummary(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewSummaryForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateSummaryRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateSummary(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewDocuments(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetApplicationReviewDocumentsRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewDocuments(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) PostApplicationReviewDocuments(
	ctx echo.Context,
	applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	context := ctx.Request().Context()
	// Get the form
	form, err := ctx.MultipartForm()
	if err != nil {
		log.Error(context,
			"Request Content-Type is not multipart/form-data", log.Err(err))
		return helpers.ErrorMessageJSON(
			ctx, http.StatusBadRequest, err,
			"Request Content-Type is not multipart/form-data")
	}
	// Get the form data
	fileType, fileDestinationGroup, err := helpers.GetFileInfoFromForm(context, form)
	if err != nil {
		return helpers.ErrorMessageJSON(
			ctx, http.StatusBadRequest, err, "Form has wrong format")
	}

	// Handle file upload
	filesGetter := app_handlers.NewMultipartFormFilesGetter(*form, FileString)
	request := uw_handlers.NewPostApplicationReviewDocumentRequest(
		applicationReviewID, filesGetter, fileType, fileDestinationGroup)

	authzResponse := uw_handlers.HandlePostApplicationReviewDocumentAuthz(
		ctx.Request().Context(), u.Deps, request)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResponse)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	response := uw_handlers.HandlePostApplicationReviewDocument(
		ctx.Request().Context(), u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewDocumentLink(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID, fileHandle string,
) error {
	context := ctx.Request().Context()
	handle, err := uuid.Parse(fileHandle)
	if err != nil {
		log.Error(context, "unable to parse file handle",
			log.String("handle", fileHandle), log.Err(err))
		return ctx.JSON(http.StatusUnprocessableEntity, "unable to parse file handle")
	}
	request := uw_handlers.GetApplicationReviewDocumentLinkRequest{
		ApplicationReviewId: applicationReviewID,
		FileHandle:          handle,
	}

	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewDocumentLink(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsGaragingLocation(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetGaragingLocationRequest{
		ApplicationReviewId: string(applicationReviewID),
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetGaragingLocation(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewOperationsGaragingLocation(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var updateRequest oapi_uw.ApplicationReviewOperationsGaragingLocationForm
	err := ctx.Bind(&updateRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateGaragingLocationRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                updateRequest,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateGaragingLocation(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsVehicleZones(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetVehicleZonesRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetVehicleZones(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewOperationsVehicleZones(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var updateRequest oapi_uw.ApplicationReviewOperationsVehicleZonesForm
	err := ctx.Bind(&updateRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateVehicleZonesRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                updateRequest,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateVehicleZones(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsTerminalLocations(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetTerminalLocationsRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetTerminalLocations(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) PatchApplicationReviewOperationsTerminalLocations(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var updateRequest oapi_uw.UpdateApplicationReviewOperationsTerminalLocations
	err := ctx.Bind(&updateRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}

	request := uw_handlers.PatchTerminalLocationsRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                updateRequest,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandlePatchTerminalLocations(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) PostApplicationReviewOperationsTerminalLocationSelect(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var postRequest oapi_uw.SelectApplicationReviewOperationsTerminalLocationForm
	err := ctx.Bind(&postRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.SelectRatingAddressRequest{
		ApplicationReviewId: applicationReviewID,
		Location:            postRequest.SelectedLocation,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandlePostSelectRatingAddress(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsProjectedInformation(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetProjectedInformationRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetProjectedInformation(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewOperationsProjectedInformation(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewOperationsProjectedInformationForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateProjectedInformationRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateProjectedInformation(context, u.Deps, request)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, *response.Success)
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsYearsInBusiness(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetYearsInBusinessRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetYearsInBusiness(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewOperationsYearsInBusiness(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewOperationsYearsInBusinessForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateYearsInBusinessRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateYearsInBusiness(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsFleetHistory(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetFleetHistoryRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetFleetHistory(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewOperationsFleetHistory(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewOperationsFleetHistoryForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateFleetHistoryRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateFleetHistory(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsRadiusOfOperation(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetRadiusOfOperationRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetRadiusOfOperation(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewOperationsRadiusOfOperation(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewOperationsRadiusOfOperationForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateRadiusOfOperationRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateRadiusOfOperation(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsOperatingClasses(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetOperatingClassesRequest{
		ApplicationReviewId: string(applicationReviewID),
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetOperatingClasses(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewOperationsOperatingClasses(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewOperationsOperatingClassesForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateOperatingClassesRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateOperatingClasses(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsCustomers(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetCustomersRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetCustomers(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsHazardZones(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetHazardZonesRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetHazardZones(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewOperationsHazardZones(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewOperationsHazardZonesForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateHazardZonesRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateHazardZones(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsCommodities(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.GetCommoditiesRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetCommodities(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewOperationsCommodities(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewOperationsCommoditiesForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateCommoditiesRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateCommodities(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOperationsCommoditiesSupportedOperations(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.GetCommoditiesSupportedOperationsRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetCommoditiesSupportedOperations(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewEquipmentsUnits(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetUnitsRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetUnits(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewEquipmentsUnits(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewEquipmentsUnitsForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateUnitsRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateUnits(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewEquipmentsOwnerOperators(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetOwnerOperatorsRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetOwnerOperators(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewEquipmentsSafetyUsage(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetEquipmentsSafetyUsageRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetEquipmentsSafetyUsage(u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewDriversList(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetDriversListRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetDriversList(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewDriversList(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewDriversListForm
	err := ctx.Bind(&form)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "request data has wrong format")
	}
	request := uw_handlers.UpdateDriversListRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateDriversList(context, u.Deps, request)
	if response.Error != nil {
		return response.Error
	}

	return ctx.JSON(http.StatusOK, response.Data)
}

func (u *UwEchoInterceptor) UpdateApplicationReviewDriver(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var body oapi_uw.ApplicationReviewDriverUpdateRequest
	err := ctx.Bind(&body)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "request data has wrong format")
	}
	request := drivers.UpdateDriverRequest{
		ApplicationReviewId: applicationReviewID,
		DlNumber:            body.DlNumber,
		USStateCode:         body.UsStateCode,
		IsDeletedByUW:       body.IsDeletedByUW,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateDriver(context, u.Deps, request)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, *response.Data)
}

func (u *UwEchoInterceptor) CreateApplicationReviewDriver(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HandleCreateDriverAuthz(context, u.Deps, applicationReviewID)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	var body oapi_uw.ApplicationReviewDriverCreateRequest
	err := ctx.Bind(&body)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "request data has wrong format")
	}

	err = validateDriverCreateRequest(body)
	if err != nil {
		return err
	}

	driver := uw.DriverListRecord{
		DriverListRecord: application.DriverListRecord{
			DriverLicenseNumber: body.DlNumber,
			UsState:             body.UsStateCode,
			DateHired:           body.DateHired.Time,
		},
		IsCreatedByUW: null.BoolFrom(true),
	}
	if body.DateOfBirth != nil {
		driver.DateOfBirth = null.TimeFrom(body.DateOfBirth.Time)
	}
	if body.ExperienceStartDate != nil {
		driver.ExperienceStartDate = null.TimeFrom(body.ExperienceStartDate.Time)
	}
	if body.FirstName != nil {
		driver.FirstName = null.StringFrom(*body.FirstName)
	}
	if body.LastName != nil {
		driver.LastName = null.StringFrom(*body.LastName)
	}
	response := uw_handlers.HandleCreateDriver(context, u.Deps, applicationReviewID, driver)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusCreated, *response.Data)
}

func validateDriverCreateRequest(req oapi_uw.ApplicationReviewDriverCreateRequest) error {
	// verify that state code is valid
	if !us_states.IsValidStateCode(req.UsStateCode) {
		return common.NewNirvanaBadRequestErrorWithReason(
			errors.New("invalid request"),
			fmt.Sprintf("invalid state code: %s", req.UsStateCode),
		)
	}
	return nil
}

func (u *UwEchoInterceptor) GetApplicationReviewSafetyScore(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetSafetyScoreRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetSafetyScore(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewSafetyScore(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewSafetyScoreForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateSafetyScoreRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateSafetyScore(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewSafetyBasicScoreThreshold(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetBasicScoreThresholdRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetBasicScoreThreshold(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewSafetyBasicScoreThreshold(ctx echo.Context,
	applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewSafetyBasicScoreThresholdForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateSafetyBasicScoreThresholdRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateSafetyBasicScoreThreshold(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewSafetyISSScoreTrend(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.GetISSScoreTrendRequest{
		ApplicationReviewId: string(applicationReviewID),
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetISSScoreTrend(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewSafetyISSScoreTrend(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewSafetyISSScoreTrendForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateSafetyISSScoreTrendRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateSafetyISSScoreTrend(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewSafetyBasicScoreTrend(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetBasicScoreTrendRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetBasicScoreTrend(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewSafetyBasicScoreTrend(ctx echo.Context,
	applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewSafetyBasicScoreTrendForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateSafetyBasicScoreTrendRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateSafetyBasicScoreTrend(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewSafetyOOSViolations(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.GetOutOfServiceViolationsRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetOutOfServiceViolations(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewSafetyOOSViolations(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewSafetyOOSViolationsForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateSafetyOutOfServiceViolationsRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateSafetyOutOfServiceViolations(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewSafetySevereViolations(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetSevereViolationsRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetSevereViolations(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewSafetySevereViolations(ctx echo.Context,
	applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewSafetySevereViolationsForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateSafetySevereViolationsRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateSafetySevereViolations(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewSafetyCrashRecord(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.GetCrashRecordRequest{
		ApplicationReviewId: string(applicationReviewID),
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetCrashRecord(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewSafetyCrashRecord(ctx echo.Context, applicationReviewiD oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewSafetyCrashRecordForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateSafetyCrashRecordRequest{
		ApplicationReviewId: applicationReviewiD,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewiD,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateSafetyCrashRecord(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewSafetyDotRating(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetDotRatingRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetDotRating(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewSafetyDotRating(ctx echo.Context,
	applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewSafetyDotRatingForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateSafetyDotRatingRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateSafetyDotRating(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewFinancialsData(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetFinancialsDataRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetFinancialsData(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewFinancialsData(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewFinancialsData
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateFinancialsDataRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateFinancialsData(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewLossSummary(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetLossSummaryRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetLossSummary(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewLossSummary(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewLossSummaryForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateLossSummaryRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateLossSummary(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewLargeLosses(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetLargeLossesRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetLargeLosses(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewLargeLosses(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewLargeLossesForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateLargeLossesRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateLargeLosses(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewLossAverages(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetLossAveragesRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetLossAverages(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewLossAverages(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewLossAveragesForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateLossAveragesRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateLossAverages(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewPackageType(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetApplicationReviewPackageTypeRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewPackageType(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewPackageType(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewPackageType
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdatePackageTypeRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdatePackageType(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewNegotiatedRates(
	ctx echo.Context,
	applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetNegotiatedRatesRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetNegotiatedRates(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewNegotiatedRates(
	ctx echo.Context,
	applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var updateRequest oapi_uw.ApplicationReviewNegotiatedRates
	err := ctx.Bind(&updateRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateNegotiatedRatesRequest{
		ApplicationReviewId: applicationReviewID,
		Details:             updateRequest,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateNegotiatedRates(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewCoverages(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetApplicationReviewCoveragesRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewCoverages(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewCoverages(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewCoverages
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateCoveragesRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateCoverages(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewQuote(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetQuoteRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetQuote(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewQuote(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var patchRequest oapi_uw.UpdateApplicationReviewRequest
	err := ctx.Bind(&patchRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateQuoteRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateQuote(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) RefetchMVR(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.RefetchMVRRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleRefetchMVR(context, u.Deps, request)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusCreated, *response.Data)
}

func (u *UwEchoInterceptor) CreateApplicationReviewBindableQuote(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var patchRequest oapi_uw.UpdateApplicationReviewRequest
	err := ctx.Bind(&patchRequest)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.CreateBindableQuoteRequest{
		ApplicationReviewId: string(applicationReviewID),
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleCreateBindableQuote(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewBindableQuote(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetBindableQuoteRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetBindableQuote(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) ConfirmApplicationReviewBindableQuote(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewConfirmBindableQuoteForm

	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}

	request := uw_handlers.ConfirmBindableQuoteRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleConfirmBindableQuote(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) RollbackApplicaitonReview(
	ctx echo.Context,
	applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var rollbackMetadata oapi_uw.RollbackMetadata
	err := ctx.Bind(&rollbackMetadata)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.RollbackApplicationReviewRequest{
		ApplicationReviewId: applicationReviewID,
		Comment:             rollbackMetadata.Comment,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleRollbackApplicationReview(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) DeclineApplicationReview(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var reasons oapi_uw.ApplicationReviewDeclineReasonsForm

	err := ctx.Bind(&reasons)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}

	request := uw_handlers.DeclineApplicationReviewRequest{
		ApplicationReviewId: applicationReviewID,
		Reasons:             reasons,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleDeclineApplicationReview(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) CloseApplicationReview(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	var form oapi_uw.ApplicationReviewCloseReasonsForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.CloseApplicationReviewRequest{
		ApplicationReviewID:               applicationReviewID,
		CloseApplicationReviewReasonsForm: form,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleCloseApplicationReview(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewBoardsInfo(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.GetApplicationReviewBoardsInfoRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewBoardsInfo(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewBoardsInfo(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewBoardsInfo
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateApplicationReviewBoardsInfoRequest{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateApplicationReviewBoardsInfo(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

// Problem endpoints

func (u *UwEchoInterceptor) GetVinProblems(
	ctx echo.Context, applicationID oapi_uw.ApplicationID,
) error {
	context := ctx.Request().Context()

	user := authz.UserFromContext(context)
	authzResp := uw_handlers.HandleGetVinProblemsAuthz(user)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	req := uw_handlers.GetVinProblemsRequest{ApplicationID: applicationID}
	resp := uw_handlers.HandleGetVinProblems(context, u.Deps, req)
	return ctx.JSON(resp.StatusCode(), resp.Body())
}

func (u *UwEchoInterceptor) PutVinProblems(
	ctx echo.Context, applicationID oapi_uw.ApplicationID,
) error {
	var form oapi_uw.ApplicationVinProblems
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	context := ctx.Request().Context()
	user := authz.UserFromContext(context)
	authzResp := uw_handlers.HandlePutVinProblemsAuthz(user)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	req := uw_handlers.PutVinProblemsRequest{
		AppId: applicationID,
		Form:  form,
	}
	resp := uw_handlers.HandlePutVinProblems(context, u.Deps, req)
	return ctx.JSON(resp.StatusCode(), resp.Body())
}

func (u *UwEchoInterceptor) GetMvrProblems(
	ctx echo.Context, applicationID oapi_uw.ApplicationID,
) error {
	context := ctx.Request().Context()
	req := uw_handlers.GetMvrProblemsRequest{ApplicationID: applicationID}
	authzResp := uw_handlers.HandleGetMvrProblemsAuthz(u.Deps, req)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	resp := uw_handlers.HandleGetMvrProblems(context, u.Deps, req)
	return ctx.JSON(resp.StatusCode(), resp.Body())
}

func (u *UwEchoInterceptor) GetApplicationCloseReasons(
	ctx echo.Context,
	_ oapi_uw.ApplicationReviewID,
) error {
	// Although we are taking the reviewID as a parameter, we are not using it
	// because the endpoint is not scoped to a specific review.
	authzResp := uw_handlers.HandleGetApplicationCloseReasonsAuthz()
	if authzResp.Error != nil {
		return authzResp.Error
	}

	resp := uw_handlers.HandleGetApplicationCloseReasons()
	return ctx.JSON(resp.StatusCode(), resp.Body())
}

func (u *UwEchoInterceptor) GetApplicationDeclineReasons(
	ctx echo.Context,
	_ oapi_uw.ApplicationReviewID,
	params fleet_uw.GetApplicationDeclineReasonsParams,
) error {
	// Although we are taking the reviewID as a parameter, we are not using it
	// because the endpoint is not scoped to a specific review.
	authzResp := uw_handlers.HandleGetApplicationDeclineReasonsAuthz()
	if authzResp.Error != nil {
		return authzResp.Error
	}
	request := uw_handlers.GetApplicationDeclineReasonsRequest{
		ProgramType: params.ProgramType,
		Version:     params.Version,
	}
	resp := uw_handlers.HandleGetApplicationDeclineReasons(request)
	return ctx.JSON(resp.StatusCode(), resp.Body())
}

func (u *UwEchoInterceptor) PutMvrProblems(
	ctx echo.Context, applicationID oapi_uw.ApplicationID,
) error {
	var form oapi_uw.ApplicationMvrProblems
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	context := ctx.Request().Context()

	user := authz.UserFromContext(context)
	authzResp := uw_handlers.HandlePutMvrProblemsAuthz(user)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	req := uw_handlers.PutMvrProblemsRequest{
		AppId: applicationID,
		Form:  form,
	}
	resp := uw_handlers.HandlePutMvrProblems(context, u.Deps, req)
	return ctx.JSON(resp.StatusCode(), resp.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewClaimHistory(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	context := ctx.Request().Context()
	request := uw_handlers.GetClaimHistoryRequest{
		ApplicationReviewId: applicationReviewID,
	}

	authzResp := uw_handlers.HandleGetClaimHistoryAuthz(context, u.Deps, request)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetClaimHistory(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewClaimHistoryDescription(ctx echo.Context,
	applicationReviewID oapi_uw.ApplicationReviewID,
	params fleet_uw.GetApplicationReviewClaimHistoryDescriptionParams,
) error {
	request := uw_handlers.GetClaimHistoryDescriptionRequest{
		ApplicationReviewId: applicationReviewID,
		DocumentId:          params.DocumentId,
		PolicySn:            params.PolicySn,
		ClaimSn:             params.ClaimSn,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetClaimHistoryDescription(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) SetAttractScore(
	ctx echo.Context, appReviewId oapi_uw.ApplicationReviewID,
) error {
	req := uw_handlers.PostSetAttractScoreRequest{ApplicationReviewId: appReviewId}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, appReviewId,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	resp := uw_handlers.HandlePostSetAttractScore(context, u.Deps, req)
	return ctx.JSON(resp.StatusCode(), resp.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewRecommendations(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetApplicationReviewRecommendationsRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleGetApplicationReviewRecommendations(context, u.Deps, request)
	if response.Error != nil {
		return *response.Error
	}
	return ctx.JSON(http.StatusOK, response.Success)
}

func (u *UwEchoInterceptor) UpdateApplicationReviewRecommendationConclusion(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID, experimentId string,
) error {
	var form oapi_uw.ApplicationReviewRecommendationForm
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateApplicationReviewRecommendationRequest{
		ApplicationReviewId: applicationReviewID,
		ExperimentId:        experimentId,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateApplicationReviewRecommendation(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewOverviewRecommendedAction(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetApplicationReviewOverviewRecommendedActionRequest{
		ApplicationReviewId: applicationReviewID,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewOverviewRecommendedAction(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewRecommendedActionTrail(
	ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetApplicationReviewRecommendedActionTrailRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewRecommendedActionTrail(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetRecommendedActionNotification(
	ctx echo.Context,
	applicationReviewID oapi_uw.ApplicationReviewID,
) error {
	request := uw_handlers.GetApplicationReviewRecommendedActionNotificationRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetApplicationReviewRecommendedActionNotification(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) SetRecommendedActionNotificationAcknowledged(
	ctx echo.Context,
	applicationReviewID oapi_uw.ApplicationReviewID,
	notificationId string,
) error {
	request := uw_handlers.SetRecommendedActionNotificationAcknowledgedRequest{
		ApplicationReviewId: applicationReviewID,
		NotificationId:      notificationId,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	resp := uw_handlers.HandleSetRecommendedActionNotificationAcknowledged(context, u.Deps, request)
	return ctx.JSON(resp.StatusCode(), resp.Body())
}

func (u *UwEchoInterceptor) RecalculateTelematicsConnectionState(
	ctx echo.Context,
	params fleet_uw.RecalculateTelematicsConnectionStateParams,
) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HandleRecalculateTelematicsConnectionStateAuthz(context, params)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response, err := uw_handlers.HandleRecalculateTelematicsConnectionState(context, u.Deps, params)
	if err != nil {
		return err
	}
	if response == nil {
		err = errors.New("unexpected error")
		return common.NewNirvanaInternalServerWithReason(err, "received nil response from handler")
	}
	return ctx.JSON(http.StatusOK, response)
}

func (u *UwEchoInterceptor) GetApplicationClearedApplication(ctx echo.Context, params fleet_uw.GetApplicationClearedApplicationParams) error {
	context := ctx.Request().Context()
	if params.DotNumber == nil {
		return common.NewNirvanaBadRequestErrorf(errors.New("DOT number is required"), "DOT number is required", "")
	}

	if params.EffectiveDate == nil {
		return common.NewNirvanaBadRequestErrorf(errors.New("Effective date is required"), "Effective date is required", "")
	}

	request := uw_handlers.ClearedApplicationRequest{
		DOTNumber:     params.DotNumber,
		EffectiveDate: params.EffectiveDate.Time,
	}

	user := authz.UserFromContext(context)
	authzResp := uw_handlers.HandleGetClearedApplicationAuthz(user)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	resp := uw_handlers.HandleGetClearedApplication(context, u.Deps, request)
	if resp.Error != nil {
		return resp.Error
	}

	return ctx.JSON(http.StatusOK, resp.Data)
}

func (u *UwEchoInterceptor) PostApplicationApplicationIDClear(ctx echo.Context, applicationID oapi_uw.ApplicationID) error {
	context := ctx.Request().Context()

	user := authz.UserFromContext(context)
	authzResp := uw_handlers.HandlePostClearApplicationAuthz(user)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	request := uw_handlers.PostClearApplicationRequest{
		ApplicationID: applicationID,
	}
	resp := uw_handlers.HandlePostClearApplication(context, u.Deps, request)
	if resp.Error != nil {
		return resp.Error
	}

	return ctx.JSON(http.StatusOK, resp.Data)
}

func (u *UwEchoInterceptor) GetApplicationReviewEquipmentsAdditionalInfoUnits(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	resp := uw_handlers.HandleGetAdditionalInfoUnits(context, u.Deps, applicationReviewID)
	if resp.Error != nil {
		return resp.Error
	}

	return ctx.JSON(http.StatusOK, resp.Data)
}

func (u *UwEchoInterceptor) UpdateApplicationReviewTargetPrice(echoCtx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	ctx := echoCtx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(ctx, u.Deps, authz.UserFromContext(ctx), authz.ReadAction, applicationReviewID)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	var body oapi_uw.ApplicationReviewTargetPriceOverride
	err := echoCtx.Bind(&body)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "request data has wrong format")
	}

	response := uw_handlers.HandleUpdateTargetPrice(ctx, u.Deps, applicationReviewID, body)
	if response.Error != nil {
		return response.Error
	}
	return echoCtx.JSON(http.StatusOK, *response.Data)
}

func (u *UwEchoInterceptor) GetApplicationReviewTargetPrice(echoCtx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	ctx := echoCtx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(ctx, u.Deps, authz.UserFromContext(ctx), authz.ReadAction, applicationReviewID)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetTargetPrice(ctx, u.Deps, applicationReviewID)
	if response.Error != nil {
		return response.Error
	}
	if response.Data == nil {
		return echoCtx.JSON(http.StatusOK, struct{}{})
	}
	return echoCtx.JSON(http.StatusOK, *response.Data)
}

func (u *UwEchoInterceptor) GetReviewReadinessTaskList(echoCtx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	ctx := echoCtx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(ctx, u.Deps, authz.UserFromContext(ctx), authz.ReadAction, applicationReviewID)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleGetReviewReadinessTasks(ctx, u.Deps, applicationReviewID)
	if response.Error != nil {
		return response.Error
	}
	if response.Data == nil {
		return echoCtx.JSON(http.StatusOK, struct{}{})
	}
	return echoCtx.JSON(http.StatusOK, *response.Data)
}

func (u *UwEchoInterceptor) BulkUpdateReviewReadinessTasks(echoCtx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	ctx := echoCtx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(ctx, u.Deps, authz.UserFromContext(ctx), authz.WriteAction, applicationReviewID)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	var body oapi_uw.BulkUpdateReviewReadinessTaskRequest
	err := echoCtx.Bind(&body)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "request data has wrong format")
	}

	response := uw_handlers.HandleUpdateReviewReadinessTasks(ctx, u.Deps, applicationReviewID, body)
	if response.Error != nil {
		return response.Error
	}
	return echoCtx.JSON(http.StatusOK, *response.Data)
}

func (u *UwEchoInterceptor) ReadyApplicationReview(echoCtx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	ctx := echoCtx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(ctx, u.Deps, authz.UserFromContext(ctx), authz.WriteAction, applicationReviewID)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleReadyApplicationReview(ctx, u.Deps, applicationReviewID)
	if response.Error != nil {
		return response.Error
	}
	return echoCtx.JSON(http.StatusOK, *response.Data)
}

func (u *UwEchoInterceptor) GetMstReferralReview(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleGetMSTReferralReview(context, u.Deps, applicationReviewID)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, response.Result)
}

func (u *UwEchoInterceptor) UpdateMstReferralReview(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	var req oapi_uw.ApplicationReviewMstReferralReviewForm
	err := ctx.Bind(&req)
	if err != nil {
		return err
	}

	response := uw_handlers.HandlePatchMSTReferralReview(context, u.Deps, applicationReviewID, &req)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (u *UwEchoInterceptor) GetCurrentStatus(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}
	response := uw_handlers.HandleGetCurrentStatus(context, u.Deps, applicationReviewID)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, response.Result)
}

func (u *UwEchoInterceptor) UpdateCurrentStatus(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	var req oapi_uw.ApplicationReviewCurrentStatusForm
	err := ctx.Bind(&req)
	if err != nil {
		return err
	}

	response := uw_handlers.HandlePatchCurrentStatus(context, u.Deps, applicationReviewID, &req)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (u *UwEchoInterceptor) AddEquipmentUnitToAppReview(echoCtx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	ctx := echoCtx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(ctx, u.Deps, authz.UserFromContext(ctx), authz.WriteAction, applicationReviewID)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	var body oapi_uw.AppReviewEquipmentAddRequest
	err := echoCtx.Bind(&body)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "request data has wrong format")
	}

	equipmentUnit := uw.EquipmentListRecord{
		EquipmentListRecord: application.EquipmentListRecord{
			VIN:         body.Vin,
			StatedValue: pointer_utils.ToPointer(int32(body.StatedValue)),
		},
		IsCreatedByUW: true,
	}
	response := uw_handlers.HandleAddEquipmentUnit(ctx, u.Deps, applicationReviewID, equipmentUnit)
	if response.Error != nil {
		return response.Error
	}
	return echoCtx.JSON(http.StatusCreated, *response.Data)
}

func (u *UwEchoInterceptor) PostReport(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	contextUser := authz.UserFromContext(context)
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, contextUser,
		authz.WriteAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	var req oapi_uw.ApplicationReviewReportForm
	err := ctx.Bind(&req)
	if err != nil {
		return err
	}

	response := uw_handlers.HandlePostReport(context, u.Deps, applicationReviewID, contextUser.ID, &req)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (u *UwEchoInterceptor) GetFactorRankings(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	contextUser := authz.UserFromContext(context)
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context,
		u.Deps,
		contextUser,
		authz.ReadAction,
		applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response, err := uw_handlers.HandleGetFactorRankings(context, u.Deps, applicationReviewID)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}

func (u *UwEchoInterceptor) GetApplicationReviewSafetyScoreV2(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	request := uw_handlers.GetSafetyScoreRequest{
		ApplicationReviewId: applicationReviewID,
	}

	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleGetSafetyScoreV2(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) UpdateApplicationReviewSafetyScoreV2(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewSafetyScoreFormV2
	err := ctx.Bind(&form)
	if err != nil {
		return helpers.ErrorMessageJSON(ctx, http.StatusBadRequest,
			err, "Request data has wrong format")
	}
	request := uw_handlers.UpdateSafetyScoreRequestV2{
		ApplicationReviewId: applicationReviewID,
		Form:                form,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.WriteAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateSafetyScoreV2(context, u.Deps, request)
	return ctx.JSON(response.StatusCode(), response.Body())
}

func (u *UwEchoInterceptor) GetApplicationReviewPanelNotes(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	request := uw_handlers.GetApplicationReviewPanelNotesRequest{
		ApplicationReviewId: applicationReviewID,
	}

	response := uw_handlers.HandleGetApplicationReviewPanelNotes(context, u.Deps, request)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, response.Result)
}

func (u *UwEchoInterceptor) UpdateApplicationReviewPanelNotes(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	var form oapi_uw.ApplicationReviewPanelNotes
	err := ctx.Bind(&form)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "request data has wrong format")
	}

	request := uw_handlers.UpdateApplicationReviewPanelNotesRequest{
		ApplicationReviewId: applicationReviewID,
		Notes:               &form.Notes,
	}
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.WriteAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	response := uw_handlers.HandleUpdateApplicationReviewPanelNotes(context, u.Deps, request)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, nil)
}

func (u *UwEchoInterceptor) GetApplicationReviewLossSummaryV2(ctx echo.Context, applicationReviewID oapi_uw.ApplicationReviewID) error {
	context := ctx.Request().Context()
	authzResp := uw_handlers.HasPermissionOverAppReview(
		context, u.Deps, authz.UserFromContext(context),
		authz.ReadAction, applicationReviewID,
	)
	if authzResp.Error != nil {
		return authzResp.Error
	}

	var req oapi_uw.ApplicationReviewLossSummaryV2Request
	err := ctx.Bind(&req)
	if err != nil {
		return err
	}

	response := uw_handlers.HandleGetLossSummaryV2(context, u.Deps, applicationReviewID, req)
	if response.Error != nil {
		return response.Error
	}
	return ctx.JSON(http.StatusOK, response.Success)
}

func (u *UwEchoInterceptor) UpdateApplicationReviewLossSummaryV2(ctx echo.Context,
	applicationReviewId oapi_uw.ApplicationReviewID,
) error {
	// TODO implement me
	panic("implement me")
}

var _ fleet_uw.ServerInterface = &UwEchoInterceptor{}
