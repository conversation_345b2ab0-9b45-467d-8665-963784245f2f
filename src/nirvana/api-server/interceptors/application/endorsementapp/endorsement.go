package endorsementapp

import (
	"net/http"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/application/endorsementapp"
	"nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	api_server_endorsement_app "nirvanatech.com/nirvana/openapi-specs/api_server_app/endorsementapp"
	endorsementapp_intake_oapi "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
	iboapi "nirvanatech.com/nirvana/openapi-specs/components/insurance-bundle"
)

// NewEndorsementAppEchoInterceptor creates a new instance of EndorsementAppEchoInterceptor.
// The interceptor takes care of echo specific logic, and then delegates the
// handling logic to ApiServerHandler.
func NewEndorsementAppEchoInterceptor(deps deps.Deps) *EndorsementAppEchoInterceptor {
	return &EndorsementAppEchoInterceptor{
		Deps: deps,
	}
}

type EndorsementAppEchoInterceptor struct {
	Deps deps.Deps
}

func (i *EndorsementAppEchoInterceptor) CancelInsuranceBundle(ctx echo.Context, bundleExternalID iboapi.BundleExternalID) error {
	var requestBody iboapi.CancelInsuranceBundleRequest
	err := ctx.Bind(&requestBody)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "invalid request body")
	}

	err = endorsementapp.HandleCancelInsuranceBundle(
		ctx.Request().Context(),
		i.Deps,
		bundleExternalID,
		&requestBody,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, nil)
}

func (i *EndorsementAppEchoInterceptor) UpdateEndorsementRequestCoverages(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandlePatchEndorsementRequestCoveragesAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	var requestBody endorsementapp_intake_oapi.PatchEndorsementRequestCoveragesRequestBody
	err = ctx.Bind(&requestBody)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "", "")
	}

	err = endorsementapp.HandlePatchEndorsementRequestCoverages(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
		requestBody,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusNoContent, nil)
}

func (i *EndorsementAppEchoInterceptor) UpdateEndorsementRequestMiscellaneous(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandlePatchEndorsementRequestMiscAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	var requestBody endorsementapp_intake_oapi.PatchEndorsementRequestMiscellaneousRequestBody
	err = ctx.Bind(&requestBody)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "", "")
	}

	err = endorsementapp.HandlePatchEndorsementRequestMisc(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
		requestBody,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusNoContent, nil)
}

func (i *EndorsementAppEchoInterceptor) GetInsuranceBundleList(
	ctx echo.Context,
	params api_server_endorsement_app.GetInsuranceBundleListParams,
) error {
	authzResp := endorsementapp.HandleGetInsuranceBundleListAuthz(ctx.Request().Context(), i.Deps)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	response, err := endorsementapp.HandleGetInsuranceBundleList(ctx.Request().Context(), i.Deps, params.ProgramType)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, response)
}

func (i *EndorsementAppEchoInterceptor) GetInsuranceBundle(ctx echo.Context, bundleID iboapi.BundleID) error {
	response, err := endorsementapp.HandleGetInsuranceBundle(ctx.Request().Context(), i.Deps, bundleID)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}

func (i *EndorsementAppEchoInterceptor) CreateEndorsementRequest(
	ctx echo.Context,
	bundleID iboapi.BundleID,
) error {
	var requestBody endorsementapp_intake_oapi.CreateEndorsementRequestBody
	err := ctx.Bind(&requestBody)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "", "")
	}

	authzResp := endorsementapp.HandleCreateEndorsementRequestAuthz(ctx.Request().Context(), i.Deps)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	response, err := endorsementapp.HandleCreateEndorsementRequest(ctx.Request().Context(), i.Deps, bundleID, requestBody)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusCreated, response)
}

func (i *EndorsementAppEchoInterceptor) GetEndorsementRequestCoverages(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandleGetEndorsementRequestCoveragesAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	response, err := endorsementapp.HandleGetEndorsementRequestCoverages(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}

func (i *EndorsementAppEchoInterceptor) GetEndorsementRequestEquipments(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandleGetEndorsementRequestEquipmentsAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	response, err := endorsementapp.HandleGetEndorsementRequestEquipments(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}

func (i *EndorsementAppEchoInterceptor) GetEndorsementRequestDrivers(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandleGetEndorsementRequestDriversAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	response, err := endorsementapp.HandleGetEndorsementRequestDrivers(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}

func (i *EndorsementAppEchoInterceptor) UpdateEndorsementRequestDrivers(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandlePatchEndorsementRequestDriversAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	var requestBody endorsementapp_intake_oapi.PatchEndorsementRequestDriversRequestBody
	err = ctx.Bind(&requestBody)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "", "")
	}

	err = endorsementapp.HandlePatchEndorsementRequestDrivers(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
		requestBody,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusNoContent, nil)
}

func (i *EndorsementAppEchoInterceptor) UpdateEndorsementRequestEquipments(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandlePatchEndorsementRequestEquipmentsAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	var requestBody endorsementapp_intake_oapi.PatchEndorsementRequestEquipmentsRequestBody
	err = ctx.Bind(&requestBody)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "", "")
	}

	err = endorsementapp.HandlePatchEndorsementRequestEquipments(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
		requestBody,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusNoContent, nil)
}

func (i *EndorsementAppEchoInterceptor) SubmitEndorsementRequestForReview(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandleSubmitEndorsementRequestAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	var requestBody endorsementapp_intake_oapi.SubmitEndorsementRequestForReviewRequestBody
	err = ctx.Bind(&requestBody)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "", "")
	}

	err = endorsementapp.HandleSubmitEndorsementRequest(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
		requestBody,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusAccepted, nil)
}

func (i *EndorsementAppEchoInterceptor) GetEndorsementRequestMiscellaneous(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandleGetEndorsementRequestMiscellaneousAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	response, err := endorsementapp.HandleGetEndorsementRequestMiscellaneous(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}

func (i *EndorsementAppEchoInterceptor) GetEndorsementRequest(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandleGetEndorsementRequestAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	response, err := endorsementapp.HandleGetEndorsementRequest(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}

func (i *EndorsementAppEchoInterceptor) GetAllEndorsementRequests(
	ctx echo.Context,
	bundleID iboapi.BundleID,
) error {
	authzResp := endorsementapp.HandleGetEndorsementRequestListAuthz(ctx.Request().Context(), i.Deps)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	response, err := endorsementapp.HandleGetEndorsementListRequest(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}

func (i *EndorsementAppEchoInterceptor) UpdateEndorsementRequest(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandleUpdateEndorsementRequestAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	var requestBody endorsementapp_intake_oapi.UpdateEndorsementRequestRequestBody
	err = ctx.Bind(&requestBody)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "", "")
	}

	err = endorsementapp.HandleUpdateEndorsementRequest(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
		requestBody,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusNoContent, nil)
}

func (i *EndorsementAppEchoInterceptor) GetEndorsementRequestChargesTemp(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	authzResp := endorsementapp.HandleGetEndorsementRequestChargesTempAuthz(ctx.Request().Context(), i.Deps)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	response, err := endorsementapp.HandleGetEndorsementRequestChargesTemp(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}

func (i *EndorsementAppEchoInterceptor) CloseEndorsementRequest(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandleCloseEndorsementRequestAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	err = endorsementapp.HandleCloseEndorsementRequest(ctx.Request().Context(), i.Deps, bundleID, endReqID)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusAccepted, nil)
}

func (i *EndorsementAppEchoInterceptor) GenerateEndorsementQuote(
	ctx echo.Context,
	bundleID iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandleGenerateEndorsementQuoteAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	var requestBody endorsementapp_intake_oapi.GenerateQuoteRequestBody
	err = ctx.Bind(&requestBody)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "", "")
	}

	err = endorsementapp.HandleGenerateEndorsementQuote(
		ctx.Request().Context(),
		i.Deps,
		bundleID,
		endReqID,
		requestBody,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, nil)
}

func (i *EndorsementAppEchoInterceptor) GetEndorsementQuote(
	ctx echo.Context,
	_ iboapi.BundleID,
	endorsementRequestID endorsementapp_intake_oapi.EndorsementRequestID,
) error {
	endReqID, err := uuid.Parse(endorsementRequestID)
	if err != nil {
		return common.NewNirvanaBadRequestErrorf(err, "endorsementRequestID", endorsementRequestID)
	}

	authzResp := endorsementapp.HandleGetEndorsementQuoteAuthz(ctx.Request().Context(), i.Deps, endReqID)
	isAuthorized, code, errResp := common.ExtractAuthorizedResponse(authzResp)
	if !isAuthorized {
		return ctx.JSON(code, errResp)
	}

	response, err := endorsementapp.HandleGetEndorsementQuote(
		ctx.Request().Context(),
		i.Deps,
		endReqID,
	)
	if err != nil {
		return err
	}

	return ctx.JSON(http.StatusOK, response)
}

var _ api_server_endorsement_app.ServerInterface = &EndorsementAppEchoInterceptor{}
