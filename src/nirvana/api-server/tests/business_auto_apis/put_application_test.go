package business_auto_apis

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	openapitypes "github.com/oapi-codegen/runtime/types"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"nirvanatech.com/nirvana/business-auto/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/short_id"
	"nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/business_auto/application"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/api_server_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/biz_auto_app_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	openapi "nirvanatech.com/nirvana/openapi-specs/api_server_app/business_auto"
	businessauto_oapi "nirvanatech.com/nirvana/openapi-specs/components/business-auto"
	nirvana_oapi "nirvanatech.com/nirvana/openapi-specs/components/nirvana"
	"nirvanatech.com/nirvana/rating/rtypes"
)

// PutApplicationTestSuite tests the business auto application update API
type PutApplicationTestSuite struct {
	test_utils.StatsHandler
	suite.Suite
	ctx context.Context

	fxapp              *fxtest.App
	appWrapper         application.Wrapper
	businessAutoClient *openapi.Client

	usersFixture      *users_fixture.UsersFixture
	bizAutoAppFixture *biz_auto_app_fixture.BizAutoAppFixture
}

func TestPutApplicationTestSuite(t *testing.T) {
	t.Parallel()
	suite.Run(t, new(PutApplicationTestSuite))
}

func (s *PutApplicationTestSuite) SetupTest() {
	var env struct {
		fx.In
		ApiServer      api_server_fixture.ApiServer
		AppWrapper     application.Wrapper
		UsersFixture   *users_fixture.UsersFixture
		BizAutoFixture *biz_auto_app_fixture.BizAutoAppFixture
	}

	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &env)
	s.businessAutoClient = env.ApiServer.BusinessAutoClient(&env.UsersFixture.BusinessAutoContractor)
	s.appWrapper = env.AppWrapper
	s.usersFixture = env.UsersFixture
	s.bizAutoAppFixture = env.BizAutoFixture
}

func (s *PutApplicationTestSuite) TearDownTest() {
	s.fxapp.RequireStop()
}

// TestPutApplication_Success tests successful application update with all fields
func (s *PutApplicationTestSuite) TestPutApplication_Success() {
	ctx := context.Background()

	// First create an application that we can update
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("1234576") // Generate unique ShortID
	err := s.appWrapper.Insert(s.ctx, &app)
	s.Require().NoError(err)

	// Create update request body
	req := businessauto_oapi.PutBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Updated Test Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(10)),     // increased from 5
			NoOfEmployees:                   pointer_utils.ToPointer(int32(20)),     // increased from 10
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(75.0)), // increased from 50.0
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			HasWorkCompPolicy:               pointer_utils.ToPointer(true),
			UsState:                         "OH",
			Address: &nirvana_oapi.Address{
				Street: "456 Updated Street",
				City:   "Cleveland",
				State:  "OH",
				Zip:    "44101",
			},
		},
		VehiclesInfo: pointer_utils.ToPointer([]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A123456",
				Year:                             2021,
				Make:                             "Chevrolet",
				Model:                            "Silverado",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Medium, // changed from Light
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageInterstate,        // changed from Intrastate
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100, // keep same as original
				PrincipalGaragingLocationZipCode: "54321",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		}),
		FilingsInfo: &businessauto_oapi.FilingsInfo{
			HasMultiStateFilings:  true,
			HasSingleStateFilings: true, // added new filing
		},
		EffectiveDate: openapitypes.Date{
			Time: time.Now().AddDate(0, 1, 0), // moved 1 month into future
		},
		ProducerId: app.ProducerID,
	}

	// Make the API call
	resp, err := s.businessAutoClient.PutBusinessAutoApplication(ctx, app.ID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, resp.StatusCode)

	// Verify the update by getting the application
	updatedApp, err := s.appWrapper.GetByID(ctx, app.ID)
	s.Require().NoError(err)

	// Verify updated fields
	s.Equal("Updated Test Company", updatedApp.CompanyInfo.Name)
	s.Equal(int32(10), *updatedApp.CompanyInfo.NoOfPowerUnits)
	s.Equal(int32(20), *updatedApp.CompanyInfo.NoOfEmployees)
	s.Equal(float32(75.0), *updatedApp.CompanyInfo.PerOfEmployeesOperatingOwnAutos)
	s.Require().NotNil(updatedApp.CompanyInfo.HasWorkCompPolicy)
	s.True(*updatedApp.CompanyInfo.HasWorkCompPolicy)

	// Verify updated address fields
	s.Require().NotNil(updatedApp.CompanyInfo.Address)
	s.Equal("456 Updated Street", updatedApp.CompanyInfo.Address.Street)
	s.Equal("Cleveland", updatedApp.CompanyInfo.Address.City)
	s.Equal("OH", updatedApp.CompanyInfo.Address.State)
	s.Equal("44101", updatedApp.CompanyInfo.Address.Zip)

	// Verify vehicles info - check for nil first
	s.Require().NotNil(updatedApp.VehiclesInfo, "VehiclesInfo should not be nil")
	s.Require().Len(*updatedApp.VehiclesInfo, 1, "Should have exactly one vehicle")
	s.Equal(enums.WeightClassMedium, (*updatedApp.VehiclesInfo)[0].WeightClass)
	s.Equal(enums.StateUsageInterstate, (*updatedApp.VehiclesInfo)[0].StateUsage)
	s.Equal(enums.RadiusClassification0To100, (*updatedApp.VehiclesInfo)[0].RadiusClassification)
	s.Equal("54321", (*updatedApp.VehiclesInfo)[0].PrincipalGaragingLocationZipCode)

	s.True(updatedApp.FilingsInfo.HasMultiStateFilings)
	s.True(updatedApp.FilingsInfo.HasSingleStateFilings)
}

// TestPutApplication_InvalidState tests application update when in invalid state
func (s *PutApplicationTestSuite) TestPutApplication_InvalidState() {
	ctx := context.Background()

	// Create an application in a state that doesn't allow updates
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("1234577") // Generate unique ShortID
	app.UWState = enums.UWStateApproved       // Only Created and UnderReview states allow updates
	err := s.appWrapper.Insert(s.ctx, &app)
	s.Require().NoError(err)

	// Create update request body
	req := businessauto_oapi.PutBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Updated Test Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(10)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(20)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(75.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			UsState:                         "OH",
		},
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: app.ProducerID,
	}

	// Make the API call - should fail
	resp, err := s.businessAutoClient.PutBusinessAutoApplication(ctx, app.ID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusBadRequest, resp.StatusCode)
}

// TestPutApplication_InvalidCompanyInfo tests application update with invalid company info
func (s *PutApplicationTestSuite) TestPutApplication_InvalidCompanyInfo() {
	ctx := context.Background()

	// Create a valid application first
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("1234578") // Generate unique ShortID
	err := s.appWrapper.Insert(s.ctx, &app)
	s.Require().NoError(err)

	// Create update request with invalid company info
	req := businessauto_oapi.PutBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "",                                                                                                 // Empty name is invalid
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(0)),                                                                  // Zero power units is invalid
			NoOfEmployees:                   pointer_utils.ToPointer(int32(0)),                                                                  // Zero employees is invalid
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(-1)),                                                               // Negative percentage is invalid
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassification("INVALID_CLASSIFICATION")), // Invalid classification
		},
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: app.ProducerID,
	}

	// Make the API call - should fail
	resp, err := s.businessAutoClient.PutBusinessAutoApplication(ctx, app.ID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusBadRequest, resp.StatusCode)
}

// TestPutApplication_NonExistentApp tests application update for non-existent application
func (s *PutApplicationTestSuite) TestPutApplication_NonExistentApp() {
	ctx := context.Background()

	// Create update request body
	req := businessauto_oapi.PutBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Updated Test Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(10)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(20)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(75.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			UsState:                         "OH",
		},
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: s.bizAutoAppFixture.MinimalApp.ProducerID,
	}

	// Make the API call with a non-existent application ID
	resp, err := s.businessAutoClient.PutBusinessAutoApplication(ctx, uuid.New(), req)
	s.Require().NoError(err)
	s.Equal(http.StatusNotFound, resp.StatusCode)
}

// TestPutApplication_UpdateVehicles tests application update with vehicle changes
func (s *PutApplicationTestSuite) TestPutApplication_UpdateVehicles() {
	ctx := context.Background()

	// First create an application that we can update
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("1234579") // Generate unique ShortID
	err := s.appWrapper.Insert(s.ctx, &app)
	s.Require().NoError(err)

	// Create update request with vehicle changes
	req := businessauto_oapi.PutBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Vehicle Update Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(3)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(15)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(60.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationWholesalersManufacturers),
			UsState:                         "OH",
		},
		VehiclesInfo: pointer_utils.ToPointer([]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A123457",
				Year:                             2022,
				Make:                             "Toyota",
				Model:                            "Tacoma",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "98765",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
			{
				Vin:                              "2ABCD82633B98765X",
				Year:                             2022,
				Make:                             "Toyota",
				Model:                            "Tacoma",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageInterstate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN101To300,
				PrincipalGaragingLocationZipCode: "98765",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		}),
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: app.ProducerID,
	}

	// Make the API call
	resp, err := s.businessAutoClient.PutBusinessAutoApplication(ctx, app.ID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, resp.StatusCode)

	// Verify the update by getting the application
	updatedApp, err := s.appWrapper.GetByID(ctx, app.ID)
	s.Require().NoError(err)

	// Verify vehicle updates - check for nil first
	s.Require().NotNil(updatedApp.VehiclesInfo, "VehiclesInfo should not be nil")
	s.Equal(2, len(*updatedApp.VehiclesInfo))
	s.Equal("1HGCM82633A123457", (*updatedApp.VehiclesInfo)[0].VIN)
	s.Equal(int32(2022), (*updatedApp.VehiclesInfo)[0].Year)
	s.Equal("Toyota", (*updatedApp.VehiclesInfo)[0].Make)
	s.Equal("Tacoma", (*updatedApp.VehiclesInfo)[0].Model)
	s.Equal("2ABCD82633B98765X", (*updatedApp.VehiclesInfo)[1].VIN)
	s.Equal(int32(2022), (*updatedApp.VehiclesInfo)[1].Year)
	s.Equal("Toyota", (*updatedApp.VehiclesInfo)[1].Make)
	s.Equal("Tacoma", (*updatedApp.VehiclesInfo)[1].Model)
}

// TestPutApplication_UpdateWithNilVehicles tests application update with nil vehicles info
func (s *PutApplicationTestSuite) TestPutApplication_UpdateWithNilVehicles() {
	ctx := context.Background()

	// First create an application that we can update
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("1234580") // Generate unique ShortID
	err := s.appWrapper.Insert(s.ctx, &app)
	s.Require().NoError(err)

	// Create update request with nil vehicles info
	req := businessauto_oapi.PutBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Nil Vehicles Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(5)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(10)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(50.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			UsState:                         "OH",
		},
		VehiclesInfo: nil, // Explicitly set to nil
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: app.ProducerID,
	}

	// Make the API call
	resp, err := s.businessAutoClient.PutBusinessAutoApplication(ctx, app.ID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, resp.StatusCode)

	// Verify the update by getting the application
	updatedApp, err := s.appWrapper.GetByID(ctx, app.ID)
	s.Require().NoError(err)

	// Verify that VehiclesInfo is nil
	s.Nil(updatedApp.VehiclesInfo, "VehiclesInfo should be nil")
	s.Equal("Nil Vehicles Company", updatedApp.CompanyInfo.Name)
}

// TestPutApplication_DuplicateVINs tests that duplicate VINs are rejected during update
func (s *PutApplicationTestSuite) TestPutApplication_DuplicateVINs() {
	ctx := context.Background()

	// First create an application that we can update
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("1234581") // Generate unique ShortID
	err := s.appWrapper.Insert(s.ctx, &app)
	s.Require().NoError(err)

	// Create update request with duplicate VINs
	req := businessauto_oapi.PutBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Duplicate VIN Update Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(2)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(5)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(30.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			UsState:                         "OH",
		},
		VehiclesInfo: pointer_utils.ToPointer([]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A123456",
				Year:                             2021,
				Make:                             "Chevrolet",
				Model:                            "Silverado",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Medium,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageInterstate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "12345",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
			{
				Vin:                              "1HGCM82633A123456", // Duplicate VIN
				Year:                             2022,
				Make:                             "Ford",
				Model:                            "F-150",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "54321",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		}),
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: app.ProducerID,
	}

	// Make the API call - should fail due to duplicate VINs
	resp, err := s.businessAutoClient.PutBusinessAutoApplication(ctx, app.ID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusBadRequest, resp.StatusCode)

	// Verify the error response contains the expected message
	s.NotNil(resp)
}

// TestPutApplication_EmptyVINs tests that empty VINs are allowed during update (not treated as duplicates)
func (s *PutApplicationTestSuite) TestPutApplication_EmptyVINs() {
	ctx := context.Background()

	// First create an application that we can update
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("1234582") // Generate unique ShortID
	err := s.appWrapper.Insert(s.ctx, &app)
	s.Require().NoError(err)

	// Create update request with empty VINs (should be allowed)
	req := businessauto_oapi.PutBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "Empty VIN Update Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(2)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(5)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(30.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			UsState:                         "OH",
		},
		VehiclesInfo: pointer_utils.ToPointer([]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "", // Empty VIN
				Year:                             2021,
				Make:                             "Chevrolet",
				Model:                            "Silverado",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Medium,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageInterstate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "12345",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
			{
				Vin:                              "", // Another empty VIN (should be allowed)
				Year:                             2022,
				Make:                             "Ford",
				Model:                            "F-150",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "54321",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		}),
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: app.ProducerID,
	}

	// Make the API call - should succeed since empty VINs are not duplicates
	resp, err := s.businessAutoClient.PutBusinessAutoApplication(ctx, app.ID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, resp.StatusCode)

	// Verify the update by getting the application
	updatedApp, err := s.appWrapper.GetByID(ctx, app.ID)
	s.Require().NoError(err)

	// Verify that the update was successful
	s.Equal("Empty VIN Update Company", updatedApp.CompanyInfo.Name)
	s.Require().NotNil(updatedApp.VehiclesInfo, "VehiclesInfo should not be nil")
	s.Equal(2, len(*updatedApp.VehiclesInfo))
	s.Equal("", (*updatedApp.VehiclesInfo)[0].VIN)
	s.Equal("", (*updatedApp.VehiclesInfo)[1].VIN)
}

// TestPutApplication_InitializeNilModelPinConfig tests that nil ModelPinConfig gets initialized during PUT
func (s *PutApplicationTestSuite) TestPutApplication_InitializeNilModelPinConfig() {
	ctx := context.Background()

	// First create an application with nil ModelPinConfig
	app := *s.bizAutoAppFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("1234583") // Generate unique ShortID
	app.ModelPinConfig = nil                  // Explicitly set to nil to simulate older applications
	err := s.appWrapper.Insert(s.ctx, &app)
	s.Require().NoError(err)

	// Verify ModelPinConfig is nil before update
	existingApp, err := s.appWrapper.GetByID(ctx, app.ID)
	s.Require().NoError(err)
	s.Nil(existingApp.ModelPinConfig, "ModelPinConfig should be nil before update")

	// Create update request - keeping same state to test nil initialization (not state change)
	req := businessauto_oapi.PutBusinessAutoAppRequest{
		CompanyInfo: businessauto_oapi.CompanyInfo{
			Name:                            "ModelPinConfig Init Company",
			NoOfPowerUnits:                  pointer_utils.ToPointer(int32(5)),
			NoOfEmployees:                   pointer_utils.ToPointer(int32(10)),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.ToPointer(float32(50.0)),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(businessauto_oapi.PrimaryIndustryClassificationContractorTrucks),
			UsState:                         "OH", // Same state as original to test nil initialization
		},
		VehiclesInfo: pointer_utils.ToPointer([]businessauto_oapi.VehicleInfo{
			{
				Vin:                              "1HGCM82633A098765",
				Year:                             2021,
				Make:                             "Honda",
				Model:                            "Accord",
				VehicleType:                      businessauto_oapi.VehicleTypeTruck,
				WeightClass:                      businessauto_oapi.Light,
				VehicleUse:                       businessauto_oapi.VehicleUseTowingOperations,
				BusinessUse:                      businessauto_oapi.BusinessUseCommercial,
				StateUsage:                       businessauto_oapi.StateUsageIntrastate,
				RadiusClassification:             businessauto_oapi.RadiusClassificationN0To100,
				PrincipalGaragingLocationZipCode: "43215",
				SpecialtyVehicleType:             businessauto_oapi.SpecialtyVehicleTypeArtisanContractors,
			},
		}),
		EffectiveDate: openapitypes.Date{
			Time: time.Now(),
		},
		ProducerId: app.ProducerID,
	}

	// Make the API call
	resp, err := s.businessAutoClient.PutBusinessAutoApplication(ctx, app.ID, req)
	s.Require().NoError(err)
	s.Equal(http.StatusOK, resp.StatusCode)

	// Verify the update by getting the application
	updatedApp, err := s.appWrapper.GetByID(ctx, app.ID)
	s.Require().NoError(err)

	// Verify basic updates were successful
	s.Equal("ModelPinConfig Init Company", updatedApp.CompanyInfo.Name)

	// Verify ModelPinConfig was initialized (the main test)
	s.NotNil(updatedApp.ModelPinConfig, "ModelPinConfig should not be nil after update")
	s.NotNil(updatedApp.ModelPinConfig.RateML, "RateML config should not be nil")
	s.Equal(rtypes.ProviderNico, updatedApp.ModelPinConfig.RateML.Provider)
	s.Equal(us_states.OH, updatedApp.ModelPinConfig.RateML.USState)
	// Note: Version will be whatever GetActiveVersion returns for OH state, we just verify it's not empty
	s.NotEqual(rtypes.InvalidVersion, updatedApp.ModelPinConfig.RateML.Version)
}
