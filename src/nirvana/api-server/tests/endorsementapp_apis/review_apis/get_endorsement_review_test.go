package review_apis

import (
	"context"
	"encoding/json"
	"net/http"
	"reflect"
	"testing"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	helper "nirvanatech.com/nirvana/api-server/tests/endorsementapp_apis"
	"nirvanatech.com/nirvana/common-go/test_utils"
	admittedApplicationBuilder "nirvanatech.com/nirvana/common-go/test_utils/builders/admitted_app"
	admittedSubmissionBuilder "nirvanatech.com/nirvana/common-go/test_utils/builders/admitted_submission"
	endorsementrequest "nirvanatech.com/nirvana/common-go/test_utils/builders/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/common-go/time_utils"
	dbendorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	dbendorsementreview "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	endorsementreview "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	nf_common "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/infra/constants"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/api_server_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fixture_utils"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	ibservice "nirvanatech.com/nirvana/insurance-bundle/service"
	endorsementuwoapi "nirvanatech.com/nirvana/openapi-specs/api_server_uw/endorsementuw"
	"nirvanatech.com/nirvana/openapi-specs/components/common"
	endorsementappoapi "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
	"nirvanatech.com/nirvana/openapi-specs/components/endorsementuw"
)

func TestGetEndorsementReviewTestSuite(t *testing.T) {
	t.Parallel()
	suite.Run(t, new(getEndorsementReviewTestSuite))
}

type getEndorsementReviewTestSuite struct {
	test_utils.StatsHandler
	suite.Suite
	ctx context.Context

	fxapp                      *fxtest.App
	endorsementRequestWrapper  dbendorsementrequest.Wrapper
	endorsementReviewWrapper   dbendorsementreview.Wrapper
	endorsementReviewAPIClient *endorsementuwoapi.Client
	insuranceBundleManager     ibservice.InsuranceBundleManagerClient
	admittedAppReviewWrapper   app_review.Wrapper
	admittedAppWrapper         nf_common.Wrapper[*admitted_app.AdmittedApp]

	usersFixture *users_fixture.UsersFixture
}

func (e *getEndorsementReviewTestSuite) SetupTest() {
	var env struct {
		fx.In
		ApiServer                 api_server_fixture.ApiServer
		InsuranceBundleManager    ibservice.InsuranceBundleManagerClient
		EndorsementRequestWrapper dbendorsementrequest.Wrapper
		EndorsementReviewWrapper  dbendorsementreview.Wrapper
		UsersFixture              *users_fixture.UsersFixture
		AppReviewWrapper          app_review.Wrapper
		AdmittedAppWrapper        nf_common.Wrapper[*admitted_app.AdmittedApp]
	}

	e.ctx = context.Background()

	e.fxapp = testloader.RequireStart(e.T(), &env)
	e.endorsementReviewAPIClient = env.ApiServer.EndorsementReviewClient(&env.UsersFixture.Superuser)
	e.insuranceBundleManager = env.InsuranceBundleManager
	e.endorsementRequestWrapper = env.EndorsementRequestWrapper
	e.endorsementReviewWrapper = env.EndorsementReviewWrapper
	e.usersFixture = env.UsersFixture
	e.admittedAppReviewWrapper = env.AppReviewWrapper
	e.admittedAppWrapper = env.AdmittedAppWrapper
}

func (e *getEndorsementReviewTestSuite) TearDownTest() {
	e.fxapp.RequireStop()
}

func (e *getEndorsementReviewTestSuite) TestGetEndorsementReview() {
	ctx := context.Background()

	baseIDStr, err := helper.InsertIB(ctx, e.insuranceBundleManager)
	e.Require().NoError(err)

	baseID, err := uuid.Parse(baseIDStr)
	e.Require().NoError(err)

	ib, err := e.insuranceBundleManager.GetInsuranceBundle(ctx, &ibservice.GetInsuranceBundleRequest{
		PrimaryFilter: &ibservice.GetInsuranceBundleRequest_PrimaryFilter{
			Identifier: &ibservice.GetInsuranceBundleRequest_PrimaryFilter_InternalId{
				InternalId: baseIDStr,
			},
		},
	})
	e.Require().NoError(err)

	uw := e.usersFixture.Underwriters[0].ID
	agencyID := constants.NirvanaHQAgencyID
	app := admittedApplicationBuilder.New().
		WithDefaultMockData().
		WithID(uuid.MustParse(ib.GetInsuranceBundle().GetMetadata().GetRootApplicationId())).
		WithAgencyID(agencyID).
		WithUnderwriterID(uw).
		Build()
	err = e.admittedAppWrapper.InsertApp(ctx, *app)
	e.Require().NoError(err)

	sub := admittedSubmissionBuilder.New().Build()
	err = e.admittedAppWrapper.InsertSubmission(ctx, *sub)
	e.Require().NoError(err)

	appReviewID := uuid.New()
	err = e.admittedAppReviewWrapper.InsertApplicationReview(ctx, &app_review.AdmittedApplicationReview{
		ID:            appReviewID,
		ApplicationID: app.ID,
		SubmissionID:  sub.ID,
	})
	e.Require().NoError(err)

	endorsementChanges := []*endorsementrequest.ChangeDataWithDescription{
		endorsementrequest.NFAddDriverChangeDataWithDescription,
		endorsementrequest.NFRemoveDriverChangeDataWithDescription,
		endorsementrequest.NFUpdateDriverChangeDataWithDescription,
		endorsementrequest.NFAddVehicleChangeDataWithDescription,
		endorsementrequest.NFUpdateTerminalLocationChangeDataWithDescription,
		endorsementrequest.NFUpdateMailingAddressChangeDataWithDescription,
	}
	request, err := helper.InsertEndorsementRequest(
		ctx,
		e.endorsementRequestWrapper,
		baseID,
		endorsementChanges...)
	e.Require().NoError(err)
	e.Require().NotNil(request)

	review, err := helper.InsertEndorsementReview(
		ctx,
		e.endorsementReviewWrapper,
		request.ID,
		uw,
		nil,
	)
	e.Require().NoError(err)
	e.Require().NotNil(review)

	resp, err := e.endorsementReviewAPIClient.GetEndorsementReview(ctx, review.ID.String())
	e.Require().NoError(err)
	e.Require().Equal(http.StatusOK, resp.StatusCode)

	gotResponse := fixture_utils.ReadResponse[endorsementuw.GetEndorsementReviewResponse](e.T(), resp)
	e.NotNil(gotResponse)

	e.verifyResponse(gotResponse, review, endorsementChanges, appReviewID)
	e.verifyPremium(gotResponse.Price)
}

func (e *getEndorsementReviewTestSuite) verifyResponse(
	gotResponse *endorsementuw.GetEndorsementReviewResponse,
	review endorsementreview.Review,
	endorsementChangesRequested []*endorsementrequest.ChangeDataWithDescription,
	originalAppReviewId uuid.UUID,
) {
	e.Require().Equal(review.ID, gotResponse.Id)
	e.Require().Equal("Nirvana Trucking Solutions", gotResponse.CompanyName)
	e.Require().Equal(endorsementuw.Pending, gotResponse.State)
	e.Require().Equal("Amanda", gotResponse.UnderwriterInfo.FirstName)
	e.Require().Equal("Hensel", gotResponse.UnderwriterInfo.LastName)
	e.Equal(time_utils.NewDate(2024, 10, 1).ToTime(), gotResponse.EffectiveDate.Time)
	e.Equal(6, len(gotResponse.Changes))
	for index, c := range gotResponse.Changes {
		var change interface{}
		var err error
		if c.ChangeType == endorsementappoapi.Drivers {
			change, err = getChange[endorsementappoapi.DriverChange](c.Data)
		}
		if c.ChangeType == endorsementappoapi.Equipments {
			change, err = getChange[endorsementappoapi.VehicleChange](c.Data)
		}
		if c.ChangeType == endorsementappoapi.TerminalLocation {
			change, err = getChange[endorsementappoapi.AddressChange](c.Data)
		}
		if c.ChangeType == endorsementappoapi.MailingAddress {
			change, err = getChange[endorsementappoapi.AddressChange](c.Data)
		}
		require.NoError(e.T(), err)
		reflect.DeepEqual(change, endorsementChangesRequested[index])
	}
	e.Equal(originalAppReviewId, gotResponse.OriginalApplicationReviewID)
}

func getChange[T endorsementappoapi.DriverChange | endorsementappoapi.VehicleChange | endorsementappoapi.AddressChange](
	changeItem endorsementuw.EndorsementChange_Data,
) (*T, error) {
	// Marshal the input `changeItem` to JSON
	c, err := json.Marshal(changeItem)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal endorsement data")
	}

	// Create a new instance of T
	typedChange := new(T)

	// Unmarshal directly into the instance of T
	err = json.Unmarshal(c, typedChange)
	if err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal endorsement data")
	}

	// Return the pointer to T
	return typedChange, nil
}

func (e *getEndorsementReviewTestSuite) verifyPremium(gotPrice *endorsementappoapi.EndorsementPrice) {
	e.Equal(float64(1500), gotPrice.WrittenPremium)
	for _, c := range gotPrice.WrittenPremiumByCoverage {
		switch c.Coverage {
		case common.CoverageAutoLiability:
			e.Equal(float64(500), c.WrittenPremium)
		case common.CoverageAutoPhysicalDamage:
			e.Equal(float64(500), c.WrittenPremium)
		case common.CoverageMotorTruckCargo:
			e.Equal(float64(500), c.WrittenPremium)
		}
	}
}
