load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "endorsement",
    srcs = [
        "endorsement_request.go",
        "errors.go",
        "get_endorsement_request_address_changes.go",
        "get_endorsement_request_coverage_changes.go",
        "get_endorsement_request_driver_changes.go",
        "get_endorsement_request_equipment_changes.go",
        "get_endorsement_request_insured_changes.go",
        "helpers.go",
        "interfaces.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/common/endorsement",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/handlers/application/endorsementapp/converters",
        "//nirvana/api-server/handlers/common/endorsement/impls/fleet",
        "//nirvana/api-server/handlers/common/endorsement/impls/nonfleet",
        "//nirvana/api-server/handlers/common/ib",
        "//nirvana/application/endorsementapp/endorsement-request",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/endorsementapp/intake",
        "//nirvana/openapi-specs/components/insurance-bundle",
        "//nirvana/openapi-specs/components/nirvana",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
    ],
)

go_test(
    name = "endorsement_test",
    srcs = ["get_endorsement_request_driver_changes_test.go"],
    embed = [":endorsement"],
    deps = [
        "//nirvana/application/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
    ],
)
