package endorsement

import (
	"context"
	"google.golang.org/protobuf/types/known/timestamppb"
	endorsement_request "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-core/proto"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	endorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/insurance-bundle/service"
)

func makeMockInsuranceBundleResp() *service.GetInsuranceBundleResponse {
	created1 := timestamppb.New(time.Now())
	return &service.GetInsuranceBundleResponse{
		InsuranceBundle: model.NewInsuranceBundleBuilder(proto.ProgramType_ProgramType_NonFleetAdmitted).
			WithExternalId("bundle1").WithCreatedAt(created1).Build(),
	}
}

func TestGetEndorsementRequestDriverChangesByCDL_Success(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRequestManager := endorsement_request.NewMockManager(ctrl)
	mockInsuranceBundleClient := service.NewMockInsuranceBundleManagerClient(ctrl)

	requestID := uuid.New()
	baseID := uuid.New()

	mockEndorsementRequest := &endorsementrequest.Request{
		Base: endorsementrequest.RequestBase{
			ID: baseID,
		},
	}

	primaryFilter := &service.GetInsuranceBundleRequest_PrimaryFilter{
		Identifier: &service.GetInsuranceBundleRequest_PrimaryFilter_InternalId{
			InternalId: baseID.String(),
		},
	}
	mockRequestManager.EXPECT().GetByID(gomock.Any(), requestID).Return(mockEndorsementRequest, nil)
	mockInsuranceBundleClient.EXPECT().GetInsuranceBundle(gomock.Any(), &service.GetInsuranceBundleRequest{PrimaryFilter: primaryFilter}).Return(makeMockInsuranceBundleResp(), nil)

	result, err := GetEndorsementDriverChangesByCDL(
		ctx,
		mockRequestManager,
		mockInsuranceBundleClient,
		requestID,
	)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.IsType(t, map[string]*endorsementapp.Change{}, result)
}

func TestGetEndorsementRequestDriverChangesByCDL_Failed(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRequestManager := endorsement_request.NewMockManager(ctrl)
	mockInsuranceBundleClient := service.NewMockInsuranceBundleManagerClient(ctrl)

	requestID := uuid.New()
	expectedError := assert.AnError

	mockRequestManager.EXPECT().GetByID(gomock.Any(), requestID).Return(nil, expectedError)

	result, err := GetEndorsementDriverChangesByCDL(
		ctx,
		mockRequestManager,
		mockInsuranceBundleClient,
		requestID,
	)

	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, result)
}
