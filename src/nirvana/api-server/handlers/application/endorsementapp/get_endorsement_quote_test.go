package endorsementapp

import (
	"context"
	"errors"
	"github.com/shopspring/decimal"
	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	appEndorsementApp "nirvanatech.com/nirvana/application/endorsementapp"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	endorsementrequest "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/common-go/test_utils"
	dbrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	endreqenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/authz/checker"
	"nirvanatech.com/nirvana/insurance-core/proto"
	oapi "nirvanatech.com/nirvana/openapi-specs/components/common"
	endorsementapp_intake_oapi "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
)

// Tests for HandleGetEndorsementQuoteAuthz
func TestHandleGetEndorsementQuoteAuthz(t *testing.T) {
	endorsementRequestID := uuid.New()
	bundleId := uuid.New()
	userID := uuid.New()

	superuserRole := test_utils.MustCreateRole(authz.NewNirvanaRole(userID, authz.SuperuserRole, "*"))
	user := authz.User{
		UserInfo: authz.UserInfo{ID: userID},
		Roles:    []authz.Role{superuserRole},
	}

	endReq := &dbrequest.Request{
		ID:          endorsementRequestID,
		ProgramType: proto.ProgramType_ProgramType_NonFleetAdmitted,
		State:       endreqenums.EndorsementRequestStateCreated,
		Base:        dbrequest.RequestBase{ID: bundleId},
	}
	mockChecker, _ := checker.NewChecker()

	t.Run("superuser should be authorized", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockEndorsementRequestManager := endorsementrequest.NewMockManager(ctrl)
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil)
		mockDeps := deps.Deps{
			EndorsementRequestManager: mockEndorsementRequestManager,
			AuthzChecker:              mockChecker,
		}
		ctx := authz.WithUser(context.Background(), user)
		resp := HandleGetEndorsementQuoteAuthz(ctx, mockDeps, endorsementRequestID)
		assert.True(t, resp.IsAuthorized)
		assert.Nil(t, resp.AuthzError)
	})

	t.Run("unauthenticated user should not be authorized", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		user = authz.User{
			UserInfo: authz.UserInfo{},
			Roles:    []authz.Role{},
		}

		mockEndorsementRequestManager := endorsementrequest.NewMockManager(ctrl)
		mockDeps := deps.Deps{
			EndorsementRequestManager: mockEndorsementRequestManager,
			AuthzChecker:              mockChecker,
		}

		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil)
		ctx := authz.WithUser(context.Background(), user)
		resp := HandleGetEndorsementQuoteAuthz(ctx, mockDeps, endorsementRequestID)

		assert.False(t, resp.IsAuthorized)
		assert.NotNil(t, resp.AuthzError)
	})
}

func TestHandleGetEndorsementQuote(t *testing.T) {
	endorsementRequestID := uuid.New()
	ctx := context.Background()

	t.Run("success with nil WrittenPremium", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockEndorsementRequestManager := endorsementrequest.NewMockManager(ctrl)
		mockDeps := deps.Deps{
			EndorsementRequestManager: mockEndorsementRequestManager,
		}

		endReq := &dbrequest.Request{
			ID:    endorsementRequestID,
			State: endreqenums.EndorsementRequestStateCreated,
			Base:  dbrequest.RequestBase{Type: endreqenums.BasedOutOfBundle},
			QuoteGenerationInfo: &dbrequest.QuoteGenerationInfo{
				PricingJobInfo: &dbrequest.PricingJobInfo{
					Status: endreqenums.PricingJobStatusRunning,
				},
			},
			WrittenPremium: nil,
		}

		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil)
		response, err := HandleGetEndorsementQuote(ctx, mockDeps, endorsementRequestID)
		assert.NoError(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, endorsementapp_intake_oapi.EndorsementQuoteRefreshing, response.State)
		assert.Nil(t, response.Price)
	})

	t.Run("success", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockEndorsementRequestManager := endorsementrequest.NewMockManager(ctrl)
		mockDeps := deps.Deps{
			EndorsementRequestManager: mockEndorsementRequestManager,
		}

		writtenPremium := &appEndorsementApp.EndorsementWrittenPremium{
			EndorsementFees: pointer_utils.ToPointer(decimal.NewFromFloat(1500.25)),
			Before: appEndorsementApp.PriceSnapshot{
				TotalPremium: pointer_utils.ToPointer(decimal.NewFromFloat(3000.00)),
			},
			After: appEndorsementApp.PriceSnapshot{
				TotalPremium: pointer_utils.ToPointer(decimal.NewFromFloat(4500.25)),
			},
		}

		endReq := &dbrequest.Request{
			ID:             endorsementRequestID,
			State:          endreqenums.EndorsementRequestStateApproved,
			WrittenPremium: writtenPremium,
			Base:           dbrequest.RequestBase{Type: endreqenums.BasedOutOfBundle},
			QuoteGenerationInfo: &dbrequest.QuoteGenerationInfo{
				PricingJobInfo: &dbrequest.PricingJobInfo{
					Status: endreqenums.PricingJobStatusCompleted,
				},
				MVRPullDetails: &dbrequest.MVRPullDetails{
					Status: oapi.MVRPullStatusSuccess,
				},
			},
		}

		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil)

		response, err := HandleGetEndorsementQuote(ctx, mockDeps, endorsementRequestID)
		assert.NoError(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, endorsementapp_intake_oapi.EndorsementQuoteGenerated, response.State)
		assert.Equal(t, 1500.25, response.Price.WrittenPremium)
	})

	t.Run("get request error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockEndorsementRequestManager := endorsementrequest.NewMockManager(ctrl)
		mockDeps := deps.Deps{
			EndorsementRequestManager: mockEndorsementRequestManager,
		}

		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(nil, errors.New("db error"))

		response, err := HandleGetEndorsementQuote(ctx, mockDeps, endorsementRequestID)
		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Contains(t, err.Error(), endorsement.ErrGetRequest.Error())
	})

	t.Run("not based out of bundle error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockEndorsementRequestManager := endorsementrequest.NewMockManager(ctrl)
		mockDeps := deps.Deps{
			EndorsementRequestManager: mockEndorsementRequestManager,
		}

		// Set a different base type that's not BasedOutOfBundle
		endReq := &dbrequest.Request{
			ID:    endorsementRequestID,
			Base:  dbrequest.RequestBase{Type: endreqenums.BasedOutOfEndorsement},
			State: endreqenums.EndorsementRequestStateCreated,
		}

		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil)

		response, err := HandleGetEndorsementQuote(ctx, mockDeps, endorsementRequestID)
		assert.Error(t, err)
		assert.Nil(t, response)
		assert.Contains(t, err.Error(), endorsement.ErrNotBasedOutOfBundle.Error())
	})
}

func TestToOAPIQuoteGenerationState(t *testing.T) {
	t.Run("refreshing state", func(t *testing.T) {
		endReq := &dbrequest.Request{
			QuoteGenerationInfo: &dbrequest.QuoteGenerationInfo{
				PricingJobInfo: &dbrequest.PricingJobInfo{
					Status: endreqenums.PricingJobStatusRunning,
				},
			},
		}
		state := toOAPIQuoteGenerationState(endReq)
		assert.Equal(t, endorsementapp_intake_oapi.EndorsementQuoteRefreshing, state)
	})

	t.Run("generated state - pricing completed", func(t *testing.T) {
		endReq := &dbrequest.Request{
			QuoteGenerationInfo: &dbrequest.QuoteGenerationInfo{
				PricingJobInfo: &dbrequest.PricingJobInfo{
					Status: endreqenums.PricingJobStatusCompleted,
				},
			},
		}
		state := toOAPIQuoteGenerationState(endReq)
		assert.Equal(t, endorsementapp_intake_oapi.EndorsementQuoteGenerated, state)
	})

	t.Run("generated state - approved request", func(t *testing.T) {
		endReq := &dbrequest.Request{
			State: endreqenums.EndorsementRequestStateApproved,
		}
		state := toOAPIQuoteGenerationState(endReq)
		assert.Equal(t, endorsementapp_intake_oapi.EndorsementQuoteGenerated, state)
	})

	t.Run("generated state - bound request", func(t *testing.T) {
		endReq := &dbrequest.Request{
			State: endreqenums.EndorsementRequestStateBound,
		}
		state := toOAPIQuoteGenerationState(endReq)
		assert.Equal(t, endorsementapp_intake_oapi.EndorsementQuoteGenerated, state)
	})

	t.Run("panic state - MVR error", func(t *testing.T) {
		endReq := &dbrequest.Request{
			QuoteGenerationInfo: &dbrequest.QuoteGenerationInfo{
				MVRPullDetails: &dbrequest.MVRPullDetails{
					Status: oapi.MVRPullStatusError,
				},
			},
		}
		state := toOAPIQuoteGenerationState(endReq)
		assert.Equal(t, endorsementapp_intake_oapi.EndorsementQuotePanic, state)
	})

	t.Run("panic state - pricing failed", func(t *testing.T) {
		endReq := &dbrequest.Request{
			QuoteGenerationInfo: &dbrequest.QuoteGenerationInfo{
				PricingJobInfo: &dbrequest.PricingJobInfo{
					Status: endreqenums.PricingJobStatusFailed,
				},
			},
		}
		state := toOAPIQuoteGenerationState(endReq)
		assert.Equal(t, endorsementapp_intake_oapi.EndorsementQuotePanic, state)
	})

	t.Run("not available state - default", func(t *testing.T) {
		endReq := &dbrequest.Request{
			State: endreqenums.EndorsementRequestStateCreated,
		}
		state := toOAPIQuoteGenerationState(endReq)
		assert.Equal(t, endorsementapp_intake_oapi.EndorsementQuoteNotAvailable, state)
	})

	t.Run("nil quote generation info", func(t *testing.T) {
		endReq := &dbrequest.Request{
			State:               endreqenums.EndorsementRequestStateCreated,
			QuoteGenerationInfo: nil,
		}
		state := toOAPIQuoteGenerationState(endReq)
		assert.Equal(t, endorsementapp_intake_oapi.EndorsementQuoteNotAvailable, state)
	})
}
