package endorsementapp

import (
	"context"
	"errors"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	"nirvanatech.com/nirvana/infra/authz/checker"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-bundle/model/endorsement"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	"nirvanatech.com/nirvana/insurance-core/proto"
	nf_types "nirvanatech.com/nirvana/nonfleet/model/endorsement"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	endorsementrequest "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/common-go/test_utils"
	dbrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	endreqenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/nonfleet/rating"
	endorsementapp_intake "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
)

// Tests for HandleGenerateEndorsementQuoteAuthz
func TestHandleGenerateEndorsementQuoteAuthz(t *testing.T) {
	endorsementRequestID := uuid.New()
	bundleId := uuid.New()
	userID := uuid.New()

	superuserRole := test_utils.MustCreateRole(authz.NewNirvanaRole(userID, authz.SuperuserRole, "*"))
	user := authz.User{
		UserInfo: authz.UserInfo{ID: userID},
		Roles:    []authz.Role{superuserRole},
	}

	endReq := &dbrequest.Request{
		ID:          endorsementRequestID,
		ProgramType: proto.ProgramType_ProgramType_NonFleetAdmitted,
		State:       endreqenums.EndorsementRequestStateCreated,
		Base:        dbrequest.RequestBase{ID: bundleId},
	}
	mockChecker, _ := checker.NewChecker()

	t.Run("superuser should be authorized", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockEndorsementRequestManager := endorsementrequest.NewMockManager(ctrl)
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil)
		mockDeps := deps.Deps{
			EndorsementRequestManager: mockEndorsementRequestManager,
			AuthzChecker:              mockChecker,
		}
		ctx := authz.WithUser(context.Background(), user)
		resp := HandleGenerateEndorsementQuoteAuthz(ctx, mockDeps, endorsementRequestID)
		assert.True(t, resp.IsAuthorized)
		assert.Nil(t, resp.AuthzError)
	})

	t.Run("unauthenticated user should not be authorized", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		user = authz.User{
			UserInfo: authz.UserInfo{},
			Roles:    []authz.Role{},
		}

		mockEndorsementRequestManager := endorsementrequest.NewMockManager(ctrl)
		mockDeps := deps.Deps{
			EndorsementRequestManager: mockEndorsementRequestManager,
			AuthzChecker:              mockChecker,
		}

		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil)
		ctx := authz.WithUser(context.Background(), user)
		resp := HandleGenerateEndorsementQuoteAuthz(ctx, mockDeps, endorsementRequestID)

		assert.False(t, resp.IsAuthorized)
		assert.NotNil(t, resp.AuthzError)
	})
}

func TestHandleGenerateEndorsementQuote(t *testing.T) {
	endorsementRequestID := uuid.New()
	bundleId := uuid.New()

	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockEndorsementRequestManager := endorsementrequest.NewMockManager(ctrl)
	mockInsuranceBundleManagerClient := service.NewMockInsuranceBundleManagerClient(ctrl)
	mockDeps := deps.Deps{
		EndorsementRequestManager:    mockEndorsementRequestManager,
		InsuranceBundleManagerClient: mockInsuranceBundleManagerClient,
	}
	t.Run("success", func(t *testing.T) {
		endReq := &dbrequest.Request{
			ID:          uuid.New(),
			ProgramType: proto.ProgramType_ProgramType_NonFleetAdmitted,
			State:       endreqenums.EndorsementRequestStateCreated,
			Base:        dbrequest.RequestBase{ID: bundleId, Type: endreqenums.BasedOutOfBundle},
		}
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil)
		requestBody := endorsementapp_intake.GenerateQuoteRequestBody{
			RunType: endorsementapp_intake.Indication,
		}
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil)
		mockInsuranceBundleManagerClient.EXPECT().GetInsuranceBundle(gomock.Any(), gomock.Any()).Return(&service.GetInsuranceBundleResponse{
			InsuranceBundle: &model.InsuranceBundle{ProgramType: endReq.ProgramType},
		}, nil)

		mockEndorsementRequestManager.EXPECT().GenerateQuote(gomock.Any(), endorsementRequestID, gomock.Any(), rating.RunTypeIndication).Return(nil)

		err := HandleGenerateEndorsementQuote(ctx, mockDeps, bundleId.String(), endorsementRequestID, requestBody)
		assert.NoError(t, err)
	})

	t.Run("invalid run type", func(t *testing.T) {
		requestBody := endorsementapp_intake.GenerateQuoteRequestBody{
			RunType: "invalid_type",
		}
		err := HandleGenerateEndorsementQuote(ctx, mockDeps, bundleId.String(), endorsementRequestID, requestBody)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "Invalid runType parameter")
	})

	t.Run("get request error", func(t *testing.T) {
		requestBody := endorsementapp_intake.GenerateQuoteRequestBody{
			RunType: endorsementapp_intake.Indication,
		}
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(nil, errors.New("db error"))

		err := HandleGenerateEndorsementQuote(ctx, mockDeps, bundleId.String(), endorsementRequestID, requestBody)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "cannot fetch endorsement request")
	})

	t.Run("invalid request state", func(t *testing.T) {
		requestBody := endorsementapp_intake.GenerateQuoteRequestBody{
			RunType: endorsementapp_intake.Indication,
		}

		endReq := &dbrequest.Request{
			State: endreqenums.EndorsementRequestStateClosed,
			Base: dbrequest.RequestBase{
				ID:   uuid.New(),
				Type: endreqenums.BasedOutOfBundle,
			},
			BundleExternalID: bundleId.String(),
		}

		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil)
		err := HandleGenerateEndorsementQuote(ctx, mockDeps, bundleId.String(), endorsementRequestID, requestBody)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid endorsement request state")
	})

	t.Run("get driver changes error", func(t *testing.T) {
		requestBody := endorsementapp_intake.GenerateQuoteRequestBody{
			RunType: endorsementapp_intake.Indication,
		}

		endReq := &dbrequest.Request{
			State: endreqenums.EndorsementRequestStateCreated,
			Base:  dbrequest.RequestBase{ID: uuid.New(), Type: endreqenums.BasedOutOfBundle},
		}

		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil).Times(2)
		mockInsuranceBundleManagerClient.EXPECT().GetInsuranceBundle(gomock.Any(), gomock.Any()).Return(nil, errors.New("db error"))
		err := HandleGenerateEndorsementQuote(ctx, mockDeps, bundleId.String(), endorsementRequestID, requestBody)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "cannot fetch driver changes")
	})

	t.Run("generate quote error", func(t *testing.T) {
		requestBody := endorsementapp_intake.GenerateQuoteRequestBody{
			RunType: endorsementapp_intake.Indication,
		}
		endReq := &dbrequest.Request{
			ID:    endorsementRequestID,
			State: endreqenums.EndorsementRequestStateCreated,
			Base: dbrequest.RequestBase{
				ID:   uuid.New(),
				Type: endreqenums.BasedOutOfBundle,
			},
			ProgramType: proto.ProgramType_ProgramType_NonFleetAdmitted,
		}
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil).Times(2)
		mockInsuranceBundleManagerClient.EXPECT().GetInsuranceBundle(gomock.Any(), gomock.Any()).Return(&service.GetInsuranceBundleResponse{
			InsuranceBundle: &model.InsuranceBundle{ProgramType: endReq.ProgramType},
		}, nil)

		mockEndorsementRequestManager.EXPECT().GenerateQuote(gomock.Any(), endorsementRequestID, gomock.Any(), rating.RunTypeIndication).Return(errors.New("generate quote error"))
		err := HandleGenerateEndorsementQuote(ctx, mockDeps, bundleId.String(), endorsementRequestID, requestBody)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "cannot generate quote")
	})
}

// Tests for convertToRunTypeFromOAPI
func TestConvertToRunTypeFromOAPI(t *testing.T) {
	t.Run("valid run type", func(t *testing.T) {
		runType, err := convertToRunTypeFromOAPI(endorsementapp_intake.Indication)
		assert.NoError(t, err)
		assert.Equal(t, rating.RunTypeIndication, *runType)
	})

	t.Run("invalid run type", func(t *testing.T) {
		runType, err := convertToRunTypeFromOAPI("invalid_type")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unsupported run type")
		assert.Nil(t, runType)
	})
}

func TestHandleGenerateEndorsementQuoteWithNonFleetDriverChanges(t *testing.T) {
	endorsementRequestID := uuid.New()
	bundleId := "bundle-123-non-fleet"

	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockEndorsementRequestManager := endorsementrequest.NewMockManager(ctrl)
	mockInsuranceBundleManagerClient := service.NewMockInsuranceBundleManagerClient(ctrl)

	mockDeps := deps.Deps{
		EndorsementRequestManager:    mockEndorsementRequestManager,
		InsuranceBundleManagerClient: mockInsuranceBundleManagerClient,
	}
	requestBody := endorsementapp_intake.GenerateQuoteRequestBody{
		RunType: endorsementapp_intake.Indication,
	}

	endReq := &dbrequest.Request{
		ID:    endorsementRequestID,
		State: endreqenums.EndorsementRequestStateCreated,
		Base: dbrequest.RequestBase{
			ID:   uuid.New(),
			Type: endreqenums.BasedOutOfBundle,
		},
		BundleExternalID: bundleId,
		ProgramType:      proto.ProgramType_ProgramType_NonFleetAdmitted,
		Changes: []endorsementapp.Change{{
			Id:       "change-1",
			IsActive: true,
			Data: &endorsement.ChangeData{
				Data: &endorsement.ChangeData_NonFleetChange{
					NonFleetChange: &nf_types.NonFleetChange{
						ChangeType: nf_types.NonFleetChangeType_NonFleetChangeType_Driver,
						Data: &nf_types.NonFleetChange_DriverChange{DriverChange: &nf_types.DriverChange{
							Remove: []string{"F32040056059"},
						}},
					},
				},
			},
		}}}

	mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), endorsementRequestID).Return(endReq, nil).Times(2)
	mockInsuranceBundleManagerClient.EXPECT().GetInsuranceBundle(gomock.Any(), gomock.Any()).Return(&service.GetInsuranceBundleResponse{
		InsuranceBundle: &model.InsuranceBundle{ProgramType: endReq.ProgramType},
	}, nil)
	mockEndorsementRequestManager.EXPECT().GenerateQuote(gomock.Any(), endorsementRequestID, gomock.Any(), rating.RunTypeIndication).Return(nil)

	err := HandleGenerateEndorsementQuote(ctx, mockDeps, bundleId, endorsementRequestID, requestBody)
	assert.NoError(t, err)
}
