package endorsementapp

import (
	"context"

	endorsementapp_intake "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	"nirvanatech.com/nirvana/api-server/handlers/common/ib"
	commonib "nirvanatech.com/nirvana/api-server/handlers/common/ib"
	"nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	_ "nirvanatech.com/nirvana/application/endorsementapp/jobs/pricing"
	"nirvanatech.com/nirvana/common-go/log"
	endorsementreview "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	"nirvanatech.com/nirvana/external_client/jira/endorsementapp"
	"nirvanatech.com/nirvana/infra/authz"
)

func HandleSubmitEndorsementRequestAuthz(
	ctx context.Context,
	deps deps.Deps,
	endorsementRequestID uuid.UUID,
) common.HandlerAuthzResponse {
	return hasPermissionOverEndorsementRequest(
		ctx,
		deps,
		authz.UserFromContext(ctx),
		authz.WriteAction,
		endorsementRequestID,
	)
}

func HandleSubmitEndorsementRequest(
	ctx context.Context,
	deps deps.Deps,
	_ string,
	endorsementRequestID uuid.UUID,
	requestBody endorsementapp_intake.SubmitEndorsementRequestForReviewRequestBody,
) error {
	endReqObj, err := endorsement.GetEndorsementRequest(ctx, deps.EndorsementRequestManager, endorsementRequestID)
	if err != nil {
		return err
	}

	if endReqObj.DefaultEffectiveDate == nil {
		return errors.New("default effective date cannot be empty for endorsement request")
	}

	// Check if there's at least one change in the endorsement request
	if len(endReqObj.Changes) == 0 {
		return common.NewNirvanaBadRequestErrorWithReason(errors.New("no changes found in endorsement request"), "endorsement request must have at least one change to submit")
	}

	bundle, err := ib.GetInsuranceBundleByInternalID(ctx, deps.InsuranceBundleManagerClient, endReqObj.Base.ID.String())
	if err != nil {
		return common.NewNirvanaInternalServerWithReason(err, commonib.ErrGetBundle.Error())
	}

	uwaID := uuid.Nil
	// TODO: Write a proper scheduler to assign UWA. Currently, we are hardcoding the UWA.
	uwa, err := deps.AuthWrapper.FetchUserInfoByName(ctx, "Justine", "Nazarro")
	if err == nil {
		uwaID = uwa.ID
	}

	// Create an endorsement review
	endReqArgs := endorsementreview.CreateEndorsementReviewArgs{
		RequestID:               endorsementRequestID,
		DefaultEffectiveDate:    *endReqObj.DefaultEffectiveDate,
		PrimaryInsuredName:      bundle.GetLastSegment().PrimaryInsured.Name.GetBusinessName(),
		UnderwritingAssistantID: uwaID,
	}
	if endReqObj.WrittenPremium != nil {
		endReqArgs.WrittenPremium = endReqObj.WrittenPremium
	}
	if endReqObj.QuoteGenerationInfo != nil {
		endReqArgs.MVRPullDetails = endReqObj.QuoteGenerationInfo.MVRPullDetails
	}
	endorsementReviewID, err := deps.EndorsementReviewManager.Create(ctx, endReqArgs)
	if err != nil {
		return common.NewNirvanaInternalServerWithReason(err, ErrInsertReview.Error())
	}

	policyProcessor, err := endorsement.GetProcessor[endorsement.PolicyProcessor](bundle.ProgramType)
	if err != nil {
		return common.NewNirvanaInternalServerWithReason(err, endorsement.ErrGetPolicyProcessor.Error())
	}

	agency, err := deps.AgencyWrapper.FetchAgency(ctx, endReqObj.AgencyID)
	if err != nil {
		return common.NewNirvanaInternalServerWithReason(err, endorsement.ErrGetAgency.Error())
	}

	// Create the jira ticket, if the program type is non-fleet admitted & agency is not a test agency
	if bundle.ProgramType == insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted && !agency.IsTestAgency {
		err = endorsementapp.CreateNFEndorsementJiraTicket(ctx, deps, policyProcessor, endReqObj, bundle, *endorsementReviewID, requestBody.IsQuoteRequested)
		if err != nil {
			log.Error(ctx, "failed to create jira ticket", log.Err(err),
				log.String("endorsement_request_id", endorsementRequestID.String()))
		}
	}

	// Submit the endorsement request for UW review
	err = deps.EndorsementRequestManager.SubmitForUWReview(ctx, endorsementRequestID, *endorsementReviewID)
	if err != nil {
		return common.NewNirvanaInternalServerWithReason(err, ErrSubmitUWReview.Error())
	}

	return nil
}
