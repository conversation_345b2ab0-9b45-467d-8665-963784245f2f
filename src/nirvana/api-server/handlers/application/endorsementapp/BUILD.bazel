load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "endorsementapp",
    srcs = [
        "cancel_insurance_bundle.go",
        "close_endorsement_request.go",
        "create_endorsement_request.go",
        "errors.go",
        "generate_endorsement_quote.go",
        "get_endorsement_quote.go",
        "get_endorsement_request.go",
        "get_endorsement_request_charges_temp.go",
        "get_endorsement_request_coverages.go",
        "get_endorsement_request_drivers.go",
        "get_endorsement_request_equipments.go",
        "get_endorsement_request_list.go",
        "get_endorsement_request_miscellaneous.go",
        "get_insurance_bundle.go",
        "get_insurance_bundle_list.go",
        "helpers.go",
        "patch_endorsement_request.go",
        "patch_endorsement_request_coverages.go",
        "patch_endorsement_request_drivers.go",
        "patch_endorsement_request_equipments.go",
        "patch_endorsement_request_miscellaneous.go",
        "post_endorsement_request_submit.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/application/endorsementapp",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/handlers/application/endorsementapp/converters",
        "//nirvana/api-server/handlers/application/endorsementapp/converters/nonfleet",
        "//nirvana/api-server/handlers/application/endorsementapp/validators",
        "//nirvana/api-server/handlers/common",
        "//nirvana/api-server/handlers/common/application",
        "//nirvana/api-server/handlers/common/endorsement",
        "//nirvana/api-server/handlers/common/endorsement/converters",
        "//nirvana/api-server/handlers/common/endorsement/impls/nonfleet",
        "//nirvana/api-server/handlers/common/ib",
        "//nirvana/api-server/interceptors/application/deps",
        "//nirvana/api-server/interceptors/utils",
        "//nirvana/application/endorsementapp/charges",
        "//nirvana/application/endorsementapp/jobs/pricing",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/quoting",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/external_client/jira/endorsementapp",
        "//nirvana/infra/authz",
        "//nirvana/insurance-bundle/config",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/model/charges",
        "//nirvana/insurance-bundle/model/endorsement",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/nonfleet/rating",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/endorsementapp/intake",
        "//nirvana/openapi-specs/components/insurance-bundle",
        "//nirvana/openapi-specs/components/nirvana",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "endorsementapp_test",
    srcs = [
        "generate_endorsement_quote_test.go",
        "get_endorsement_quote_test.go",
        "helpers_test.go",
    ],
    embed = [":endorsementapp"],
    deps = [
        "//nirvana/api-server/handlers/common/endorsement",
        "//nirvana/api-server/interceptors/application/deps",
        "//nirvana/application/endorsementapp",
        "//nirvana/application/endorsementapp/endorsement-request",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/test_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums",
        "//nirvana/infra/authz",
        "//nirvana/infra/authz/checker",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/model/endorsement",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/nonfleet/model/endorsement",
        "//nirvana/nonfleet/rating",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/endorsementapp/intake",
        "//nirvana/openapi-specs/components/insurance-bundle",
        "//nirvana/openapi-specs/components/nirvana",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_shopspring_decimal//:decimal",
        "@com_github_stretchr_testify//assert",
        "@org_uber_go_mock//gomock",
    ],
)
