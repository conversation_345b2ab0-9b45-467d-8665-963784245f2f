package endorsementapp

import (
	"context"
	"fmt"

	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"

	"nirvanatech.com/nirvana/common-go/log"
	endorsementapp_intake "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	"nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/nonfleet/rating"
)

func HandleGenerateEndorsementQuoteAuthz(
	ctx context.Context,
	deps deps.Deps,
	endorsementRequestID uuid.UUID,
) common.HandlerAuthzResponse {
	return hasPermissionOverEndorsementRequest(
		ctx,
		deps,
		authz.UserFromContext(ctx),
		authz.WriteAction,
		endorsementRequestID,
	)
}

func HandleGenerateEndorsementQuote(
	ctx context.Context,
	deps deps.Deps,
	bundleId string,
	endorsementRequestID uuid.UUID,
	requestBody endorsementapp_intake.GenerateQuoteRequestBody,
) error {
	ctx = log.ContextWithFields(ctx,
		log.Stringer("endorsementRequestID", endorsementRequestID),
		log.String("bundleId", bundleId),
		log.String("runType", string(requestBody.RunType)))

	log.Info(ctx, "Starting quote generation for endorsement request")

	runType, err := convertToRunTypeFromOAPI(requestBody.RunType)
	if err != nil {
		return common.NewNirvanaBadRequestErrorWithReason(err, "Invalid runType parameter")
	}

	endReqObj, err := endorsement.GetEndorsementRequest(ctx, deps.EndorsementRequestManager, endorsementRequestID)
	if err != nil {
		return common.NewNirvanaInternalServerWithReason(err, ErrGetRequest.Error())
	}

	if endReqObj.State != enums.EndorsementRequestStateCreated {
		return common.NewNirvanaBadRequestErrorWithReason(err, ErrInvalidRequestState.Error())
	}

	driverChanges, err := endorsement.GetEndorsementDriverChangesByCDL(
		ctx,
		deps.EndorsementRequestManager,
		deps.InsuranceBundleManagerClient,
		endorsementRequestID,
	)
	if err != nil {
		log.Error(ctx, "failed to get driver changes", log.String("bundleId", bundleId), log.Err(err))
		return common.NewNirvanaInternalServerWithReason(err, ErrGetDriverChanges.Error())
	}

	// Trigger Endorsement Request Pricing job
	err = deps.EndorsementRequestManager.GenerateQuote(ctx, endorsementRequestID, driverChanges, *runType)
	if err != nil {
		return common.NewNirvanaInternalServerWithReason(err, ErrGenerateQuote.Error())
	}

	log.Info(ctx, "Successfully triggered quote generation for endorsement request")
	return nil
}

// convertRunType converts the API run type to internal run type
func convertToRunTypeFromOAPI(apiRunType endorsementapp_intake.RunType) (*rating.RunType, error) {
	switch apiRunType {
	case endorsementapp_intake.Indication:
		return pointer_utils.ToPointer(rating.RunTypeIndication), nil
	default:
		return nil, fmt.Errorf("unsupported run type: %s", apiRunType)
	}
}
