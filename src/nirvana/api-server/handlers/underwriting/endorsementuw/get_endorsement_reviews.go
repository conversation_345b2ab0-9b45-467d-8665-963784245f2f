package endorsementuw

import (
	"context"
	"github.com/cockroachdb/errors"
	endorsement_request "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"

	endorsementrequest "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"

	endorsement_review "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"

	"github.com/google/uuid"
	oapi_types "github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/underwriting/endorsementuw/converters"
	"nirvanatech.com/nirvana/api-server/interceptors/underwriting/deps"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/openapi-specs/components/endorsementuw"
	"nirvanatech.com/nirvana/openapi-specs/components/nirvana"
)

func HandleGetEndorsementReviewsAuthz(
	_ context.Context,
	_ deps.Deps,
) common.HandlerAuthzResponse {
	return common.HandlerAuthzResponse{IsAuthorized: true}
}

func HandleGetEndorsementReviews(
	ctx context.Context,
	deps deps.Deps,
) (*endorsementuw.GetEndorsementReviewsResponse, error) {
	endReviews, err := deps.EndorsementReviewManager.GetAll(ctx)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, ErrGetEndorsementReviews.Error())
	}

	endorsementReviewToRequestMap, err := fetchEndorsementRequests(ctx, deps.EndorsementRequestManager, endReviews)
	if err != nil {
		return nil, err
	}

	// Prepare the response in one iteration.
	retval := make([]endorsementuw.EndorsementReviewItem, 0, len(endReviews))
	for _, review := range endReviews {
		uwaInfo, gErr := getUnderwritingAssistantInfo(ctx, deps.AuthWrapper, review.UnderwritingAssistantID)
		if gErr != nil {
			return nil, gErr
		}

		_, exists := endorsementReviewToRequestMap[review.ID]
		if !exists {
			return nil, errors.Newf("Endorsement request not found for review %s", review.ID.String())
		}

		// Append to the response.
		retval = append(retval, endorsementuw.EndorsementReviewItem{
			Id:          review.ID,
			CompanyName: review.PrimaryInsuredName,
			EffectiveDate: oapi_types.Date{
				Time: review.DefaultEffectiveDate,
			},
			State: converters.ToOAPIState(review.State, &endorsementReviewToRequestMap[review.ID].State),
			UnderwriterAssistantInfo: &nirvana.UserInfo{
				FirstName: uwaInfo.FirstName,
				LastName:  uwaInfo.LastName,
			},
			EndorsementNumber: endorsementReviewToRequestMap[review.ID].ProvisionalEndorsementNumber,
		})
	}

	return &endorsementuw.GetEndorsementReviewsResponse{
		Reviews: retval,
	}, nil
}

func getUnderwritingAssistantInfo(
	ctx context.Context,
	authWrapper auth.DataWrapper,
	underwritingAssistantID uuid.UUID,
) (*authz.UserInfo, error) {
	var uwaInfo *authz.UserInfo
	var err error
	if underwritingAssistantID != uuid.Nil {
		uwaInfo, err = authWrapper.FetchUserInfo(ctx, underwritingAssistantID)
		if err != nil {
			return nil, common.NewNirvanaInternalServerWithReason(err, ErrGetUnderwriterAssistantInfo.Error())
		}
	} else {
		uwaInfo = &authz.UserInfo{}
	}
	return uwaInfo, nil
}

// fetchEndorsementRequests fetches endorsement requests for the given review IDs.
func fetchEndorsementRequests(
	ctx context.Context,
	endorsementRequestManager endorsementrequest.Manager,
	reviews []*endorsement_review.Review,
) (map[uuid.UUID]*endorsement_request.Request, error) {
	requestIDToReviewID := make(map[uuid.UUID]uuid.UUID)
	for _, review := range reviews {
		requestIDToReviewID[review.RequestID] = review.ID
	}

	// Collect the request IDs for fetching endorsement requests.
	requestIDs := make([]string, 0, len(requestIDToReviewID))
	for reqID := range requestIDToReviewID {
		requestIDs = append(requestIDs, reqID.String())
	}

	// Fetch the endorsement requests based on the collected request IDs.
	endReqs, err := endorsementRequestManager.GetAll(ctx, endorsement_request.IdIn(requestIDs...), endorsement_request.IncludeActiveChanges())
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, ErrGetEndorsementRequests.Error())
	}

	// Map review IDs to endorsement request
	reviewIDToRequest := make(map[uuid.UUID]*endorsement_request.Request, len(endReqs))
	for _, req := range endReqs {
		if reviewID, exists := requestIDToReviewID[req.ID]; exists {
			reviewIDToRequest[reviewID] = req
		}
	}

	return reviewIDToRequest, nil
}
