package endorsementuw

import (
	"context"
	"fmt"
	"time"

	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	openapitypes "github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/common/application"
	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	commonib "nirvanatech.com/nirvana/api-server/handlers/common/ib"
	"nirvanatech.com/nirvana/api-server/handlers/underwriting/endorsementuw/converters"
	"nirvanatech.com/nirvana/api-server/interceptors/underwriting/deps"
	endorsementrequest "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	nfapp "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	endorsementapp_intake "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
	"nirvanatech.com/nirvana/openapi-specs/components/endorsementuw"
	"nirvanatech.com/nirvana/openapi-specs/components/nirvana"
)

func HandleGetEndorsementReviewAuthz(
	_ context.Context,
	_ deps.Deps,
) common.HandlerAuthzResponse {
	return common.HandlerAuthzResponse{IsAuthorized: true}
}

func HandleGetEndorsementReview(
	ctx context.Context,
	deps deps.Deps,
	endorsementReviewID uuid.UUID,
) (*endorsementuw.GetEndorsementReviewResponse, error) {
	ctx = log.ContextWithFields(ctx, log.Stringer("endorsementReviewID", endorsementReviewID))
	endRevObj, err := deps.EndorsementReviewManager.GetByID(ctx, endorsementReviewID)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, ErrGetEndorsementReview.Error())
	}

	endReqObj, err := endorsement.GetEndorsementRequest(ctx, deps.EndorsementRequestManager, endRevObj.RequestID)
	if err != nil {
		return nil, err
	}

	ib, err := commonib.GetInsuranceBundleByInternalID(
		ctx, deps.InsuranceBundleManagerClient, endReqObj.Base.ID.String())
	if err != nil {
		return nil, err
	}

	// We use the last segment of the insurance bundle as that
	// represents the current state of the policy as in the minds of agents
	ibLastSegment := ib.GetLastSegment()
	if ibLastSegment == nil {
		return nil, common.NewNirvanaInternalServerWithReason(nil, commonib.ErrGetLastSegment.Error())
	}
	originalApplicationIDStr := ib.GetMetadata().RootApplicationId
	originalApplicationID, err := uuid.Parse(originalApplicationIDStr)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, application.ErrParseApplicationID.Error())
	}

	underwriterID, err := application.GetUnderwriterID(
		ctx,
		deps.ApplicationWrapper,
		deps.AdmittedAppWrapper,
		originalApplicationID,
		ib.GetProgramType())
	if err != nil {
		return nil, err
	}

	uwInfo, err := deps.AuthWrapper.FetchUserInfo(ctx, underwriterID)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, "failed to get underwriter info")
	}

	endorsementPrice := endRevObj.WrittenPremium.ToOAPIEndorsementPrice()
	endorsementChanges, err := getChangesForEndorsementReview(ctx, deps, endRevObj.RequestID)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, "failed to get endorsement changes")
	}

	originalAppReviewID, err := getOriginalAppReviewId(ctx, deps, ib)
	if err != nil {
		return nil, err
	}

	var mvrDetailsPtr *[]endorsementuw.DriverMVRDetails
	if deps.EndorsementReviewManager.IsMVRPulled(ctx, endorsementReviewID) {
		usState, pErr := ibLastSegment.PrimaryInsured.GetUSState()
		if pErr != nil {
			return nil, common.NewNirvanaInternalServerWithReason(pErr, "failed to get us state from insured")
		}
		mvrDetails, gErr := getMVRDetails(ctx, deps, endRevObj.RequestID, endRevObj.DefaultEffectiveDate, usState)
		if gErr != nil {
			return nil, common.NewNirvanaInternalServerWithReason(gErr, "failed to get MVR details")
		}
		mvrDetailsPtr = &mvrDetails
	}

	var policyChangeForms []endorsementuw.PolicyChangeForm
	if len(endRevObj.PolicyChangeFormHandleIds) > 0 {
		for _, policyChangeForm := range endRevObj.PolicyChangeFormHandleIds {
			fileName, gErr := deps.FileUploadManager.GetFileNameByHandleID(ctx, policyChangeForm.HandleId)
			if gErr != nil {
				return nil, common.NewNirvanaInternalServerWithReason(gErr,
					fmt.Sprintf("unable to get file name by file handle: %s", policyChangeForm.HandleId))
			}
			policyChangeForms = append(policyChangeForms, endorsementuw.PolicyChangeForm{
				PolicyNumber: policyChangeForm.PolicyNumber,
				HandleID:     policyChangeForm.HandleId,
				FileName:     fileName,
			})
		}
	}

	return &endorsementuw.GetEndorsementReviewResponse{
		Id:          endRevObj.ID,
		CompanyName: ibLastSegment.PrimaryInsured.Name.GetBusinessName(),
		EffectiveDate: openapitypes.Date{
			Time: endRevObj.DefaultEffectiveDate,
		},
		State: converters.ToOAPIState(endRevObj.State, &endReqObj.State),
		UnderwriterInfo: nirvana.UserInfo{
			FirstName: uwInfo.FirstName,
			LastName:  uwInfo.LastName,
		},
		Changes:                     endorsementChanges,
		Price:                       endorsementPrice,
		OriginalApplicationReviewID: *originalAppReviewID,
		MvrDetails:                  mvrDetailsPtr,
		EndorsementNumber:           endReqObj.ProvisionalEndorsementNumber,
		PolicyChangeForms:           &policyChangeForms,
	}, nil
}

type changeGetter func(
	ctx context.Context,
	erm endorsementrequest.Manager,
	ibm service.InsuranceBundleManagerClient,
	endorsementReqId uuid.UUID,
) ([]endorsementuw.EndorsementChange, error)

func getChangesForEndorsementReview(
	ctx context.Context,
	deps deps.Deps,
	endorsementReqId uuid.UUID,
) ([]endorsementuw.EndorsementChange, error) {
	changeGetters := []struct {
		getter changeGetter
		errMsg string
	}{
		{getOAPIDriverEndorsementChangesForRequest, "driver"},
		{getOAPIVehicleEndorsementChangesForRequest, "vehicle"},
		{getOAPITerminalLocationEndorsementChangesForRequest, "terminal location"},
		{getOAPIMailingAddressEndorsementChangesForRequest, "mailing address"},
		{getOAPIAdditionalInsuredEndorsementChangesForRequest, "additional insured"},
	}

	var endorsementChanges []endorsementuw.EndorsementChange

	for _, cg := range changeGetters {
		changes, err := cg.getter(
			ctx,
			deps.EndorsementRequestManager,
			deps.InsuranceBundleManagerClient,
			endorsementReqId,
		)
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("failed to get %s endorsement changes", cg.errMsg))
		}
		endorsementChanges = append(endorsementChanges, changes...)
	}

	return endorsementChanges, nil
}

func getOriginalAppReviewId(
	ctx context.Context,
	deps deps.Deps,
	insuranceBundle *model.InsuranceBundle,
) (*uuid.UUID, error) {
	// nolint:exhaustive
	switch insuranceBundle.GetProgramType() {
	case insurancecoreproto.ProgramType_ProgramType_Fleet:
		originalAppReview, err := deps.ApplicationReviewWrapper.GetReviewFromSub(
			ctx, insuranceBundle.GetMetadata().GetRootBindableSubmissionId())
		if err != nil {
			return nil, common.NewNirvanaInternalServerWithReason(err, ErrGetApplicationReview.Error())
		}
		originalAppReviewID, err := uuid.Parse(originalAppReview.Id)
		if err != nil {
			return nil, common.NewNirvanaInternalServerWithReason(err, ErrParseApplicationReviewID.Error())
		}
		return &originalAppReviewID, nil
	case insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted:
		applicationID, err := uuid.Parse(insuranceBundle.GetMetadata().GetRootApplicationId())
		if err != nil {
			return nil, common.NewNirvanaInternalServerWithReason(err, application.ErrParseApplicationID.Error())
		}

		originalAppReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByAppID(
			ctx, applicationID)
		if err != nil || originalAppReview == nil {
			return nil, common.NewNirvanaInternalServerWithReason(err, ErrGetApplicationReview.Error())
		}

		return pointer_utils.ToPointer(originalAppReview.GetID()), nil
	default:
		return nil, errors.Newf("unsupported program type: %s", insuranceBundle.GetProgramType())
	}
}

// NOTE: This function is a bit specific to the nonfleet program type
// TODO: Refactor this function to be more generic
func getMVRDetails(
	ctx context.Context,
	deps deps.Deps,
	endorsementReqId uuid.UUID,
	effectiveDate time.Time,
	usState us_states.USState,
) ([]endorsementuw.DriverMVRDetails, error) {
	driverChanges, err := endorsement.GetEndorsementRequestDriverChanges(
		ctx,
		deps.EndorsementRequestManager,
		deps.InsuranceBundleManagerClient,
		endorsementReqId,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get driver endorsement changes")
	}

	drivers := make([]nfapp.DriverBasicDetails, 0)
	for _, d := range driverChanges {
		if d.ChangeType != endorsementapp_intake.DriverChangeChangeTypeUnchanged {
			drivers = append(drivers, nfapp.DriverBasicDetails{
				FirstName:     d.Driver.FirstName,
				LastName:      d.Driver.LastName,
				LicenseState:  d.Driver.LicenseState,
				LicenseNumber: d.Driver.LicenseNumber,
				DateOfBirth:   d.Driver.DateOfBirth.Time,
				DateOfHire:    d.Driver.DateOfHire.Time,
			})
		}
	}

	retval := make([]endorsementuw.DriverMVRDetails, 0)

	mvrDetails := driver.GetMVRDetails(
		ctx,
		drivers,
		effectiveDate,
		enums.ProgramTypeNonFleetAdmitted,
		usState,
		deps.FetcherClientFactory,
	)

	for _, d := range mvrDetails {
		retval = append(retval, endorsementuw.DriverMVRDetails{
			CdlClass:        d.CdlClass,
			CdlNumber:       d.CdlNumber,
			CdlStatus:       d.CdlStatus,
			ExpirationDate:  d.ExpirationDate,
			IssueDate:       d.IssueDate,
			MvrPullError:    d.MvrPullError,
			MvrPullStatus:   d.MvrPullStatus,
			ViolationPoints: pointer_utils.Int(d.ViolationPoints),
			ViolationCount:  pointer_utils.Int(d.ViolationCount),
			Violations:      d.Violations,
			YearIssued:      GetYearIssued(d.IssueDate),
		})
	}

	return retval, nil
}

func GetYearIssued(issueDate *openapitypes.Date) *int {
	if issueDate == nil {
		return nil
	}
	year := issueDate.Year()
	return &year
}
