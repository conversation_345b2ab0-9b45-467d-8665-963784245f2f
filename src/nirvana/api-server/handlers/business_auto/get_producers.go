package business_auto

import (
	"context"
	"encoding/json"
	"sort"
	"strings"

	"github.com/launchdarkly/go-sdk-common/v3/ldvalue"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/interceptors/business_auto/deps"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/authz"
	business_auto "nirvanatech.com/nirvana/openapi-specs/components/business-auto"
)

// HandleGetAvailableProducersAuthz handles authorization for the get available producers endpoint.
// This operation checks if the user has read permission to view available producers.
func HandleGetAvailableProducersAuthz(
	ctx context.Context,
	deps deps.Deps,
) common.HandlerAuthzResponse {
	return common.HandlerAuthzResponse{IsAuthorized: true}
}

func HandleGetAvailableProducers(
	ctx context.Context,
	deps deps.Deps,
) (*business_auto.GetAvailableProducersResponse, error) {
	// Get the feature flag value for producer allowlist
	user := authz.UserFromContext(ctx)
	allowlistValue, err := deps.FeatureFlagClient.JSONVariation(
		feature_flag_lib.BuildLookupAttributes(user),
		feature_flag_lib.FeatureBusinessAutoProducerAllowlist,
		ldvalue.ArrayOf(),
	)
	if err != nil {
		log.Error(
			ctx, "HandleGetAvailableProducers: Failed to fetch feature flag",
			log.Err(err),
		)
		// Return empty response if we can't fetch the feature flag
		return &business_auto.GetAvailableProducersResponse{}, nil
	}
	// Parse the allowlist from the feature flag (array of email strings)
	var allowedEmails []string
	if !allowlistValue.IsNull() {
		allowlistJSON := allowlistValue.JSONString()
		if allowlistJSON != "" {
			if err := json.Unmarshal([]byte(allowlistJSON), &allowedEmails); err != nil {
				log.Error(
					ctx, "HandleGetAvailableProducers: Failed to unmarshal feature flag value",
					log.Err(err),
				)
				// Return empty response if we can't parse the feature flag
				return &business_auto.GetAvailableProducersResponse{}, nil
			}
		}
	}
	// If no emails in allowlist, return empty response
	if len(allowedEmails) == 0 {
		log.Info(
			ctx, "HandleGetAvailableProducers: Empty allowlist, returning empty response",
		)
		return &business_auto.GetAvailableProducersResponse{}, nil
	}

	// Fetch users by email from the allowlist
	var usersByEmail []authz.User
	for _, email := range allowedEmails {
		// Normalize email to lowercase for consistent matching
		normalizedEmail := strings.ToLower(strings.TrimSpace(email))
		user, err := deps.AuthWrapper.FetchAuthzUserByEmail(ctx, normalizedEmail)
		if err != nil {
			log.Warn(
				ctx, "HandleGetAvailableProducers: Failed to fetch user by email",
				log.String("email", normalizedEmail), log.Err(err),
			)
			continue // Skip this user but continue with others
		}
		if user != nil {
			usersByEmail = append(usersByEmail, *user)
		}
	}
	// Process users from allowlist and build response directly
	var producerResp business_auto.GetAvailableProducersResponse
	for _, user := range usersByEmail {
		// Skip users that aren't activated
		if user.ActivatedAt == nil {
			continue
		}

		// Filter to only include users with producer or admin role
		userWithRoles := authz.User{UserInfo: user.UserInfo, Roles: user.Roles}
		if !userWithRoles.IsAgencyProducer() && !userWithRoles.IsAgencyAdmin() {
			continue
		}

		// Build producer response directly from the user data
		producer := business_auto.Producer{
			Id:    user.ID,
			Name:  user.FullName(),
			Email: &user.Email,
		}
		producerResp = append(producerResp, producer)
	}

	// Sort producers by name
	sort.Slice(producerResp, func(i, j int) bool {
		return producerResp[i].Name < producerResp[j].Name
	})

	return &producerResp, nil
}
