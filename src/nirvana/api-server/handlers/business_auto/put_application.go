package business_auto

import (
	"context"
	"database/sql"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/interceptors/business_auto/deps"
	"nirvanatech.com/nirvana/business-auto/enums"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/infra/authz"
	openapi "nirvanatech.com/nirvana/openapi-specs/components/business-auto"
	"nirvanatech.com/nirvana/rating/models/models_release"
	"nirvanatech.com/nirvana/rating/rtypes"
)

func HandlePutApplicationAuthz(
	ctx context.Context,
	_ deps.Deps,
) common.HandlerAuthzResponse {
	return CheckOperatorOrUW(ctx)
}

func HandlePutApplication(
	ctx context.Context,
	deps deps.Deps,
	appID uuid.UUID,
	req openapi.PutBusinessAutoAppRequest,
) error {
	user := authz.UserFromContext(ctx)
	log.Info(ctx, "HandlePutApplication: updating business auto application",
		log.Any("request", req),
		log.Any("updatedBy", user.ID))

	// Get existing application to check state and preserve created fields
	existingApp, err := deps.AppWrapper.GetByID(ctx, appID)
	if err != nil {
		log.Error(ctx, "HandlePutApplication: failed to get existing application",
			log.Err(err))
		if errors.Is(err, sql.ErrNoRows) {
			return common.NewNirvanaNotFoundErrorf(err, common.EntityApplication, appID.String())
		}
		return common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, appID.String())
	}

	// Do not allow update if the state is not Created
	if existingApp.UWState != enums.UWStateCreated {
		log.Error(ctx, "HandlePutApplication: application can only be updated in unsubmitted state",
			log.Any("currentState", existingApp.UWState))
		return common.NewNirvanaBadRequestErrorWithReason(nil, "application can only be updated in unsubmitted state")
	}

	// Convert company info
	companyInfo, err := convertCompanyInfo(req.CompanyInfo)
	if err != nil {
		log.Error(ctx, "HandlePutApplication: invalid company info",
			log.Err(err),
			log.Any("companyInfo", req.CompanyInfo))
		return common.NewNirvanaBadRequestErrorWithReason(err, "invalid company info")
	}

	vehiclesInfo, err := convertVehiclesInfo(req.VehiclesInfo, req.CoveragesInfo)
	if err != nil {
		log.Error(ctx, "HandlePutApplication: invalid vehicles info",
			log.Err(err),
			log.Any("vehiclesInfo", req.VehiclesInfo))
		return common.NewNirvanaBadRequestErrorWithReason(err, "invalid vehicles info")
	}

	// Convert coverages info
	coveragesInfo, err := convertCoveragesInfoFromOAPI(
		req.EffectiveDate.Time,
		companyInfo.USState,
		req.CoveragesInfo,
		vehiclesInfo,
	)
	if err != nil {
		log.Error(ctx, "HandlePutApplication: invalid coverages info",
			log.Err(err),
			log.Any("coveragesInfo", req.CoveragesInfo))
		return common.NewNirvanaBadRequestErrorWithReason(err, "invalid coverages info")
	}

	agencyId, err := getAgencyIdFromProducerId(ctx, deps.AuthWrapper, req.ProducerId)
	if err != nil {
		log.Error(ctx, "HandlePutApplication: failed to get agency ID from producer ID",
			log.Err(err),
			log.Any("producerId", req.ProducerId))
		return common.NewNirvanaBadRequestErrorWithReason(err, "invalid producer ID")
	}

	// Update application using a function that returns the updated app
	if err := deps.AppWrapper.UpdateApp(ctx, appID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		app.EffectiveDurationStart = req.EffectiveDate.Time
		app.EffectiveDurationEnd = req.EffectiveDate.AddDate(1, 0, 0) // 1 year duration
		app.CompanyInfo = *companyInfo
		app.VehiclesInfo = vehiclesInfo
		app.CoveragesInfo = coveragesInfo
		app.FilingsInfo = convertFilingsInfo(req.FilingsInfo)
		app.ProducerID = req.ProducerId
		app.UpdatedAt = time.Now()
		app.AgencyID = *agencyId

		// Check if ModelPinConfig was originally nil before we potentially initialize it
		wasModelPinConfigNil := app.ModelPinConfig == nil

		// If ModelPinConfig is nil, attempt to initialize it
		// This handles cases where older applications or those created when GetActiveVersion
		// failed don't have a ModelPinConfig set
		if wasModelPinConfigNil {
			if version, err := models_release.GetActiveVersion(
				ctx,
				deps.FeatureFlagClient,
				&user,
				rtypes.ProviderNico,
				app.CompanyInfo.USState,
				app.EffectiveDurationStart,
			); err == nil {
				app.ModelPinConfig = model.NewModelPinConfigInfo(rtypes.ProviderNico, app.CompanyInfo.USState, version)
				log.Info(ctx, "HandlePutApplication: initialized missing ModelPinConfig",
					log.String("state", app.CompanyInfo.USState.String()),
					log.String("version", version.String()))
			} else {
				log.Warn(ctx, "HandlePutApplication: could not initialize missing ModelPinConfig; proceeding without model pin", log.Err(err))
			}
		}

		// If the US state has changed, attempt to refresh the pinned RateML version.
		// Only do this if ModelPinConfig was not nil originally (to avoid updating
		// a config we just initialized) and the state actually changed.
		// -------------------------------------------------------------
		// GetActiveVersion is deterministic for a given (provider, state,
		// effectiveDate, feature-flag snapshot).  By repinning ONLY when the
		// state actually changes, we guarantee that an application keeps the
		// same model version for its lifetime, even if new releases land later.
		// If version lookup fails (e.g. rollout gap, flag issues) we log a warning
		// but keep the existing pin, so PUT never errors purely due to model
		// availability.
		// -------------------------------------------------------------
		oldState := existingApp.CompanyInfo.USState
		newState := app.CompanyInfo.USState
		if oldState != newState && !wasModelPinConfigNil {
			version, err := models_release.GetActiveVersion(
				ctx,
				deps.FeatureFlagClient,
				&user,
				rtypes.ProviderNico,
				newState,
				app.EffectiveDurationStart,
			)
			if err == nil {
				app.ModelPinConfig = model.NewModelPinConfigInfo(rtypes.ProviderNico, newState, version)
			} else {
				// Do not fail the PUT; just log the warning.
				log.Warn(ctx, "HandlePutApplication: could not determine active RateML version after state change; keeping existing model pin", log.Err(err))
			}
		}

		// If state unchanged, keep existing ModelPinConfig to preserve determinism.

		return app, nil
	}); err != nil {
		log.Error(ctx, "HandlePutApplication: failed to update application",
			log.Err(err))
		return common.NewNirvanaInternalServerErrorf(err, common.EntityApplication, appID.String())
	}

	log.Info(ctx, "HandlePutApplication: successfully updated application")
	return nil
}
