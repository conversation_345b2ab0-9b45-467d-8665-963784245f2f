package client_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"nirvanatech.com/nirvana/common-go/pointer_utils"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/application/endorsementapp"
	endorsementrequestmanager "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	"nirvanatech.com/nirvana/application/endorsementapp/endorsement-review/internal"
	"nirvanatech.com/nirvana/application/endorsementapp/endorsement-review/internal/client"
	mockRequestBuilder "nirvanatech.com/nirvana/common-go/test_utils/builders/endorsementapp/endorsement-request"
	mockReviewBuilder "nirvanatech.com/nirvana/common-go/test_utils/builders/endorsementapp/endorsement-review"
	dbendorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	endreqenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"
	endorsementreview "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	endreviewenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums"
	legacywritegateway "nirvanatech.com/nirvana/endorsement/legacy-write-gateway"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/openapi-specs/components/common"
)

func TestCloseDeclineEndorsementReview(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	ctrl, ctx := gomock.WithContext(ctx, t)

	mockEndorsementReviewWrapper := endorsementreview.NewMockWrapper(ctrl)
	mockLegacyEndorsementWriteGateway := legacywritegateway.NewMockLegacyWriteGateway(ctrl)
	mockEndorsementRequestManager := endorsementrequestmanager.NewMockManager(ctrl)

	reviewId := uuid.New()
	requestId := uuid.New()
	closeReason := "Closed by Nirvana"
	review := mockReviewBuilder.NewEndorsementReviewBuilder().
		WithID(reviewId).
		WithRequestID(requestId).
		WithState(endreviewenums.EndorsementReviewStatePending).
		Build()
	request := mockRequestBuilder.NewEndorsementRequestBuilder().
		WithID(requestId).
		WithState(endreqenums.EndorsementRequestStateUnderUWReview).
		WithProgramType(insurancecoreproto.ProgramType_ProgramType_Fleet).
		Build()
	requestNonFleet := mockRequestBuilder.NewEndorsementRequestBuilder().
		WithID(requestId).
		WithState(endreqenums.EndorsementRequestStateUnderUWReview).
		WithProgramType(insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted).
		Build()

	var env struct {
		fx.In

		Jobber quoting_jobber.Client
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	deps := internal.Deps{
		EndorsementReviewWrapper:      mockEndorsementReviewWrapper,
		LegacyEndorsementWriteGateway: mockLegacyEndorsementWriteGateway,
		EndorsementRequestManager:     mockEndorsementRequestManager,
		Jobber:                        env.Jobber,
	}
	impl := client.New(deps)

	t.Run("Close Endorsement Review", func(t *testing.T) {
		mockLegacyEndorsementWriteGateway.EXPECT().HandleDeclineOrCloseEndorsementReview(gomock.Any(), reviewId, closeReason).
			Return(nil).Times(1)
		mockEndorsementReviewWrapper.EXPECT().Update(gomock.Any(), reviewId, gomock.Any()).Return(nil).Times(2)
		mockEndorsementReviewWrapper.EXPECT().GetByID(gomock.Any(), reviewId).Return(&review, nil).Times(3)
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), requestId).Return(&request, nil).Times(1)
		mockEndorsementRequestManager.EXPECT().Close(gomock.Any(), review.RequestID).Return(nil).Times(1)
		err := impl.Close(ctx, reviewId, &closeReason)
		assert.NoError(t, err)
	})

	t.Run("Close Endorsement Review - no legacy decline call for non-fleet", func(t *testing.T) {
		mockEndorsementReviewWrapper.EXPECT().Update(gomock.Any(), reviewId, gomock.Any()).Return(nil).Times(2)
		mockEndorsementReviewWrapper.EXPECT().GetByID(gomock.Any(), reviewId).Return(&review, nil).Times(3)
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), requestId).Return(&requestNonFleet, nil).Times(1)
		mockEndorsementRequestManager.EXPECT().Close(gomock.Any(), review.RequestID).Return(nil).Times(1)
		err := impl.Close(ctx, reviewId, &closeReason)
		assert.NoError(t, err)
	})

	t.Run("Close Endorsement Review - error during legacy decline review", func(t *testing.T) {
		mockLegacyEndorsementWriteGateway.EXPECT().
			HandleDeclineOrCloseEndorsementReview(gomock.Any(), reviewId, closeReason).
			Return(assert.AnError).Times(1)
		mockEndorsementReviewWrapper.EXPECT().Update(gomock.Any(), reviewId, gomock.Any()).Return(nil).Times(2)
		mockEndorsementReviewWrapper.EXPECT().GetByID(gomock.Any(), reviewId).Return(&review, nil).Times(2)
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), requestId).Return(&request, nil).Times(1)
		err := impl.Close(ctx, reviewId, &closeReason)
		assert.ErrorContains(
			t, err,
			fmt.Sprintf(
				"failed to Close endorsement app review to the legacy system for endorsement reviewId %s",
				reviewId,
			),
		)
	})

	t.Run("Close Endorsement Review - no close reason provided", func(t *testing.T) {
		mockEndorsementReviewWrapper.EXPECT().Update(gomock.Any(), reviewId, gomock.Any()).Return(nil).Times(1)
		mockEndorsementReviewWrapper.EXPECT().GetByID(gomock.Any(), reviewId).Return(&review, nil).Times(1)
		err := impl.Close(ctx, reviewId, nil)
		assert.ErrorContains(
			t, err, "close reason cannot be nil",
		)
	})

	t.Run("Close Endorsement Review - error fetching endorsement request", func(t *testing.T) {
		mockEndorsementReviewWrapper.EXPECT().GetByID(gomock.Any(), reviewId).Return(&review, nil).Times(2)
		mockEndorsementReviewWrapper.EXPECT().Update(gomock.Any(), reviewId, gomock.Any()).Return(nil).Times(2)
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), requestId).Return(nil, assert.AnError).Times(1)
		err := impl.Close(ctx, reviewId, &closeReason)
		assert.ErrorContains(
			t, err,
			fmt.Sprintf(
				"failed to get endorsement request with id %s",
				requestId,
			),
		)
	})

	t.Run("Decline Endorsement Review", func(t *testing.T) {
		mockLegacyEndorsementWriteGateway.EXPECT().HandleDeclineOrCloseEndorsementReview(gomock.Any(), reviewId, closeReason).
			Return(nil).Times(1)
		mockEndorsementReviewWrapper.EXPECT().Update(gomock.Any(), reviewId, gomock.Any()).Return(nil).Times(2)
		mockEndorsementReviewWrapper.EXPECT().GetByID(gomock.Any(), reviewId).Return(&review, nil).Times(3)
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), requestId).Return(&request, nil).Times(1)
		mockEndorsementRequestManager.EXPECT().Decline(gomock.Any(), review.RequestID).Return(nil).Times(1)
		err := impl.Decline(ctx, reviewId, &closeReason)
		assert.NoError(t, err)
	})

	t.Run("Decline Endorsement Review - no legacy decline call for non-fleet", func(t *testing.T) {
		mockEndorsementReviewWrapper.EXPECT().Update(gomock.Any(), reviewId, gomock.Any()).Return(nil).Times(2)
		mockEndorsementReviewWrapper.EXPECT().GetByID(gomock.Any(), reviewId).Return(&review, nil).Times(3)
		mockEndorsementRequestManager.EXPECT().GetByID(gomock.Any(), requestId).Return(&requestNonFleet, nil).Times(1)
		mockEndorsementRequestManager.EXPECT().Decline(gomock.Any(), review.RequestID).Return(nil).Times(1)
		err := impl.Decline(ctx, reviewId, &closeReason)
		assert.NoError(t, err)
	})
}

func TestCreateEndorsementReview(t *testing.T) {
	// Test that the Create method properly sets WrittenPremium when provided

	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockEndorsementReviewWrapper := endorsementreview.NewMockWrapper(ctrl)
	deps := internal.Deps{
		EndorsementReviewWrapper: mockEndorsementReviewWrapper,
	}
	impl := client.New(deps)

	requestID := uuid.New()
	uwaID := uuid.New()
	effectiveDate := time.Now().Add(24 * time.Hour)
	mvrPullTime := time.Now().Add(-2 * time.Hour)

	writtenPremium := &endorsementapp.EndorsementWrittenPremium{
		EndorsementFees: pointer_utils.ToPointer(decimal.NewFromFloat(1500.25)),
		Before: endorsementapp.PriceSnapshot{
			TotalPremium: pointer_utils.ToPointer(decimal.NewFromFloat(3000.00)),
		},
		After: endorsementapp.PriceSnapshot{
			TotalPremium: pointer_utils.ToPointer(decimal.NewFromFloat(4500.25)),
		},
	}
	mockEndorsementReviewWrapper.EXPECT().GetByID(gomock.Any(), gomock.Any()).Return(&endorsementreview.Review{ID: uuid.New()}, nil)
	mockEndorsementReviewWrapper.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	args := endorsementreview.CreateEndorsementReviewArgs{
		RequestID:               requestID,
		DefaultEffectiveDate:    effectiveDate,
		PrimaryInsuredName:      "Test Company Inc",
		UnderwritingAssistantID: uwaID,
		WrittenPremium:          writtenPremium,
		MVRPullDetails: &dbendorsementrequest.MVRPullDetails{
			Status:         common.MVRPullStatusSuccess,
			LatestPullTime: mvrPullTime,
		},
	}

	// Set up mock expectations for Insert
	mockEndorsementReviewWrapper.EXPECT().
		Insert(ctx, gomock.Any()).
		DoAndReturn(func(ctx context.Context, review *endorsementreview.Review) error {
			require.NotNil(t, review.WrittenPremium)
			assert.Equal(t, *writtenPremium, review.WrittenPremium)

			assert.Equal(t, requestID, review.RequestID)
			assert.Equal(t, effectiveDate, review.DefaultEffectiveDate)
			assert.Equal(t, "Test Company Inc", review.PrimaryInsuredName)
			assert.Equal(t, uwaID, review.UnderwritingAssistantID)

			require.Len(t, review.Actions, 1)
			action := review.Actions[0]
			assert.Equal(t, endreviewenums.EndorsementReviewActionTypePullMVR, action.Action)
			assert.Equal(t, mvrPullTime, action.LastModifiedAt)

			return nil
		})

	result, err := impl.Create(ctx, args)

	require.NoError(t, err)
	assert.NotNil(t, result)
}
