package pricing

import (
	"github.com/benbjohnson/clock"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/common-go/crypto_utils"
	endorsement_request "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"

	endorsementrequest "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request"
	endorsementreview "nirvanatech.com/nirvana/application/endorsementapp/endorsement-review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting"
	endorsement_review "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/external_data_management/clients_management"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	_ "nirvanatech.com/nirvana/rating/pricing/api"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

type Deps struct {
	fx.In

	PricingServer                ptypes.PricingServer
	PricingWrapper               quoting.PricingWrapper
	EndorsementRequestManager    endorsementrequest.Manager
	EndorsementReviewManager     endorsementreview.Manager
	InsuranceBundleManagerClient service.InsuranceBundleManagerClient
	AdmittedAppWrapper           nf_app.Wrapper[*admitted_app.AdmittedApp]
	MetricsClient                statsd.Statter
	EndorsementReviewWrapper     endorsement_review.Wrapper
	EndorsementRequestWrapper    endorsement_request.Wrapper
	ClientsManager               clients_management.ClientsManager
	Clk                          clock.Clock
	CryptoClient                 *crypto_utils.Client
}
