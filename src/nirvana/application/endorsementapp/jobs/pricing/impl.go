package pricing

import (
	"context"
	"time"

	endreq_enums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"

	endorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"github.com/cockroachdb/errors"
	"github.com/goccy/go-json"
	"github.com/google/uuid"
	"google.golang.org/protobuf/encoding/protojson"

	"nirvanatech.com/nirvana/application/endorsementapp"
	"nirvanatech.com/nirvana/application/endorsementapp/charges"
	"nirvanatech.com/nirvana/application/endorsementapp/jobs"
	"nirvanatech.com/nirvana/application/endorsementapp/jobs/messages"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/external_data_management/clients_management"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/nonfleet/pricing_api"
	post_bind "nirvanatech.com/nirvana/nonfleet/pricing_api/request/post-bind"
	"nirvanatech.com/nirvana/nonfleet/rating"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

const contextLookupDepth = 20

func NewEndorsementPricingJob(deps Deps) (
	*jtypes.Job[*messages.EndorsementPricingMessage],
	error,
) {
	return jtypes.NewJob(
		jobs.EndorsementPricingJob,
		[]jtypes.TaskCreator[*messages.EndorsementPricingMessage]{
			func() jtypes.Task[*messages.EndorsementPricingMessage] {
				return &EndorsementPricingTask{deps: deps}
			},
		},
		messages.EndorsementPricingMessageUnmarshalFn,
	)
}

type EndorsementPricingTask struct {
	job_utils.NonRetryableTask[*messages.EndorsementPricingMessage]
	job_utils.NoopUndoTask[*messages.EndorsementPricingMessage]

	deps Deps
}

func (t *EndorsementPricingTask) ID() string {
	return jobs.EndorsementPricingJob
}

func (t *EndorsementPricingTask) Run(ctx jtypes.Context, msg *messages.EndorsementPricingMessage) (err error) {
	ctx = ctx.WithUpdatedBaseCtx(func(ctx context.Context) context.Context {
		return log.ContextWithFields(ctx, log.String("endorsement_request_id", msg.EndorsementRequestID.String()))
	})

	defer func() {
		if err != nil {
			log.Error(ctx, "EndorsementPricingTask failed", log.Err(err))
			if msg.EndorsementReviewID != nil {
				sErr := t.deps.EndorsementReviewManager.SetPanic(ctx, *msg.EndorsementReviewID)
				if sErr != nil {
					log.Error(ctx, "failed to set panic for endorsement review", log.Err(sErr))
				}
			} else {
				sErr := t.handleEndorsementRequestPricingJobFailure(ctx, msg.EndorsementRequestID)
				if sErr != nil {
					log.Error(ctx, "failed to update endorsement request pricing job", log.Err(sErr))
				}
			}
		}
	}()

	// 1. apply endorsement request to get the updated insurance bundle
	// This IB is not persisted in the database & computed on the fly
	draftIB, err := applyEndorsementRequest(
		ctx,
		t.deps.EndorsementRequestManager,
		t.deps.EndorsementReviewWrapper,
		t.deps.InsuranceBundleManagerClient,
		msg.EndorsementRequestID,
		msg.EndorsementReviewID,
	)
	if err != nil {
		return errors.Wrap(err, "failed to create segments from endorsement request")
	}

	ibInternalID, err := uuid.Parse(draftIB.InternalId)
	if err != nil {
		return errors.Wrap(err, "failed to parse internal ID")
	}

	if draftIB.ProgramType != insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted {
		return errors.New("program type not supported for endorsement pricing")
	}

	// 2. Write interceptors config to store
	rootBindableSubID, err := uuid.Parse(draftIB.Metadata.RootBindableSubmissionId)
	if err != nil {
		return errors.Wrap(err, "failed to parse bindable submission ID")
	}

	rootBindableSub, err := t.deps.AdmittedAppWrapper.GetSubmissionById(ctx, rootBindableSubID)
	if err != nil {
		return errors.Wrap(err, "failed to get root bindable submission")
	}

	if rootBindableSub.DataContextId == nil {
		return errors.New("submission data context id is nil")
	}
	contextID := *rootBindableSub.DataContextId

	err = t.writeClientsConfigToStore(ctx, contextID)
	if err != nil {
		return errors.Wrap(err, "failed to write interceptors config to store")
	}

	// 3. Get fetcher and processor clients
	fetcherClient, processorClient, closer, err := t.deps.ClientsManager.BuildClientsFromContext(
		ctx,
		contextID,
	)
	if err != nil {
		return errors.Wrap(err, "failed to build data fetcher and data processor clients")
	}
	defer func() { _ = closer() }()

	// 4. Get pricing request for non-fleet
	req, err := t.getNFPricingRequest(ctx, fetcherClient, processorClient, msg, draftIB, rootBindableSub)
	if err != nil {
		return errors.Wrap(err, "failed to create pricing request")
	}

	// 5. Get pricing response by sending pricing request to pricing server
	pricingResp, err := t.getNFPricingResponse(ctx, req)
	if err != nil {
		return errors.Wrap(err, "failed to get price for non fleet")
	}

	// 6. persist pricing contexts
	pricingContextUUIDs, err := t.storeNFPricingContext(ctx, req, pricingResp, msg)
	if err != nil {
		return errors.Wrap(err, "failed to store charges info")
	}

	// 7. calculate endorsement written premium with breakdown
	endorsementWrittenPremium, err := charges.GetEndorsementWrittenPremium(
		ctx,
		t.deps.InsuranceBundleManagerClient,
		t.deps.PricingWrapper,
		ibInternalID,
		pricingContextUUIDs,
	)
	if err != nil {
		return errors.Wrap(err, "failed to compute endorsement pricing details")
	}

	// 8. update endorsement review with pricing context IDs & written premium
	if msg.EndorsementReviewID != nil && msg.PricingType != rating.RunTypeIndication {
		err = t.deps.EndorsementReviewManager.UpdatePricingDetails(
			ctx,
			*msg.EndorsementReviewID,
			pricingContextUUIDs,
			*endorsementWrittenPremium,
		)
		if err != nil {
			return errors.Wrapf(err, "failed to update endorsement review %s with pricing context IDs", msg.EndorsementReviewID)
		}

		// state machine event to end price update
		err = t.deps.EndorsementReviewManager.EndPriceUpdate(ctx, *msg.EndorsementReviewID)
		if err != nil {
			return errors.Wrapf(err, "failed to end price update for endorsement review %s", msg.EndorsementReviewID)
		}
	}

	// Persist the written premium in endorsement request for indication runs without review
	if msg.PricingType == rating.RunTypeIndication && msg.EndorsementReviewID == nil {
		err = t.persistWrittenPremiumInEndorsementRequest(ctx, msg.EndorsementRequestID, *endorsementWrittenPremium)
		if err != nil {
			return errors.Wrapf(err, "failed to persist written premium in endorsement request %s", msg.EndorsementRequestID)
		}
		return nil
	}

	return nil
}

// getNFPricingResponse sends the pricing request to the pricing server and returns the response
// It also emits the latency metric for the pricing API
// It currently only supports non-fleet pricing
func (t *EndorsementPricingTask) getNFPricingResponse(
	ctx jtypes.Context,
	req *ptypes.Request,
) (*ptypes.Response, error) {
	log.Info(ctx, "pricing request created", log.Any("pricing_request", req))
	startTime := time.Now()
	pricingResp, err := t.deps.PricingServer.GetPrice(ctx, req)
	if err != nil {
		pricingReqJSON, marshalErr := protojson.Marshal(req)
		if marshalErr != nil {
			log.Error(
				ctx, "failed to marshal pricing request",
				log.Err(marshalErr), log.Any("pricingRequest", req),
			)
		} else {
			log.Error(
				ctx, "unable to execute charges computation",
				log.String("pricingRequest", string(pricingReqJSON)),
			)
		}
		return nil, errors.Wrap(err, "unable to get price for non fleet")
	}

	latencyMetricName := "pricing_api_latency"
	latency := time.Since(startTime)
	metricsClientErr := t.deps.MetricsClient.TimingDuration(latencyMetricName, latency, 1)
	if metricsClientErr != nil {
		log.Error(
			ctx, "failed to emit latency metric for Pricing API",
			log.String("metricName", latencyMetricName), log.Err(metricsClientErr),
		)
	}
	return pricingResp, nil
}

// getNFPricingRequest creates a pricing request for non-fleet
func (t *EndorsementPricingTask) getNFPricingRequest(
	ctx jtypes.Context,
	fetcherClient data_fetching.FetcherClient,
	processorClient data_processing.ProcessorClient,
	msg *messages.EndorsementPricingMessage,
	ib *model.InsuranceBundle,
	rootBindableSub *application.Submission[*admitted_app.AdmittedApp],
) (*ptypes.Request, error) {
	req, err := post_bind.GetPricingRequest(
		ctx,
		t.deps.Clk,
		t.deps.CryptoClient,
		fetcherClient,
		processorClient,
		ib,
		rootBindableSub,
		msg.PricingType,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create pricing request")
	}
	return req, nil
}

// storeNFPricingContext stores the pricing context for the endorsement request
func (t *EndorsementPricingTask) storeNFPricingContext(
	ctx context.Context,
	req *ptypes.Request,
	chargesOut *ptypes.Response,
	msg *messages.EndorsementPricingMessage,
) ([]uuid.UUID, error) {
	var pricingContexts []quoting.PricingContext
	pricingRespGroupedByChunkID := pricing_api.GroupByChunkID(chargesOut.GetPolicyOutputs())
	bundleSpec := req.GetBundleSpec()
	for _, c := range bundleSpec.GetChunkSpecs() {
		pricingInput, err := json.Marshal(&quoting.NFPricingInput{
			ChunkData:     c,
			PoliciesSpecs: req.GetPolicySpecs(),
		})
		if err != nil {
			return nil, errors.Wrapf(err, "failed to marshal pricing input for request %s", msg.EndorsementRequestID)
		}

		pricingContexts = append(pricingContexts, quoting.PricingContext{
			ID:           c.ChunkId,
			RequestType:  enums.RequestTypeEndorsement,
			RequestID:    msg.EndorsementRequestID.String(),
			BasisID:      c.GetChunkId(),
			BasisType:    enums.BasisTypePricingRequest,
			PricingType:  msg.PricingType,
			ProgramType:  policyenums.ProgramTypeNonFleetAdmitted,
			StartTime:    c.Dates.Start.AsTime(),
			EndTime:      c.Dates.End.AsTime(),
			Charges:      pricingRespGroupedByChunkID[c.ChunkId],
			PricingInput: pricingInput,
		})
	}

	for _, p := range pricingContexts {
		err := t.deps.PricingWrapper.InsertQuotingPricingContext(ctx, p)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to store pricing context for request %s with chunk id %s", msg.EndorsementRequestID, p.ID)
		}
	}

	pricingContextIDs := slice_utils.Map(pricingContexts, func(p quoting.PricingContext) string {
		return p.ID
	})
	var pricingContextUUIDs []uuid.UUID
	for _, id := range pricingContextIDs {
		pricingContextUUID, pErr := uuid.Parse(id)
		if pErr != nil {
			return nil, errors.Wrapf(pErr, "failed to convert pricing context ID %s to UUID", id)
		}
		pricingContextUUIDs = append(pricingContextUUIDs, pricingContextUUID)
	}

	return pricingContextUUIDs, nil
}

func (t *EndorsementPricingTask) writeClientsConfigToStore(
	ctx context.Context,
	ContextID uuid.UUID,
) error {
	log.Info(ctx, "writing clients config to store", log.String("ContextID", ContextID.String()))
	clientsConfig := clients_management.ClientsConfig{
		FetcherConfig: &clients_management.ClientConfig{
			InterceptorsConfigs: []*clients_management.InterceptorConfig{
				{
					ConcreteConfig: &clients_management.InterceptorConfig_ReadFromStoreInterceptorConfig{
						ReadFromStoreInterceptorConfig: read_from_store_interceptor.NewStoreFirstConfig(ContextID, contextLookupDepth),
					},
				},
				{
					ConcreteConfig: &clients_management.InterceptorConfig_WriteToStoreInterceptorConfig{
						WriteToStoreInterceptorConfig: write_to_store_interceptor.NewEnabledConfig(ContextID),
					},
				},
			},
		},
		ProcessorConfig: &clients_management.ClientConfig{
			InterceptorsConfigs: []*clients_management.InterceptorConfig{
				{
					ConcreteConfig: &clients_management.InterceptorConfig_ReadFromStoreInterceptorConfig{
						ReadFromStoreInterceptorConfig: read_from_store_interceptor.NewStoreFirstConfig(ContextID, contextLookupDepth),
					},
				},
				{
					ConcreteConfig: &clients_management.InterceptorConfig_WriteToStoreInterceptorConfig{
						WriteToStoreInterceptorConfig: write_to_store_interceptor.NewEnabledConfig(ContextID),
					},
				},
			},
		},
	}

	return t.deps.ClientsManager.WriteClientsConfigToContext(ctx, ContextID, &clientsConfig)
}

// persistWrittenPremiumInEndorsementRequest persists the written premium in the endorsement request
func (t *EndorsementPricingTask) persistWrittenPremiumInEndorsementRequest(
	ctx jtypes.Context,
	endorsementRequestID uuid.UUID,
	writtenPremium endorsementapp.EndorsementWrittenPremium,
) error {
	err := t.deps.EndorsementRequestWrapper.Update(ctx, endorsementRequestID, func(request *endorsementrequest.Request) *endorsementrequest.Request {
		request.WrittenPremium = &writtenPremium
		info := request.QuoteGenerationInfo
		if info != nil && info.PricingJobInfo != nil {
			info.PricingJobInfo.Status = endreq_enums.PricingJobStatusCompleted
		}
		return request
	})
	if err != nil {
		return errors.Wrapf(err, "failed to update written premium for endorsement request %s", endorsementRequestID)
	}

	log.Info(ctx, "Successfully persisted written premium in endorsement request",
		log.Stringer("endorsementRequestID", endorsementRequestID))

	return nil
}

func (t *EndorsementPricingTask) handleEndorsementRequestPricingJobFailure(ctx context.Context, requestId uuid.UUID) error {
	err := t.deps.EndorsementRequestWrapper.Update(
		ctx,
		requestId,
		func(endReq *endorsementrequest.Request) *endorsementrequest.Request {
			endReq.WrittenPremium = nil
			info := endReq.QuoteGenerationInfo
			if info != nil && info.PricingJobInfo != nil {
				info.PricingJobInfo.Status = endreq_enums.PricingJobStatusFailed
			}
			return endReq
		})
	return err
}
