load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "pricing",
    srcs = [
        "deps.go",
        "fx.go",
        "impl.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/application/endorsementapp/jobs/pricing",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/application/endorsementapp",
        "//nirvana/application/endorsementapp/charges",
        "//nirvana/application/endorsementapp/common",
        "//nirvana/application/endorsementapp/endorsement-request",
        "//nirvana/application/endorsementapp/endorsement-review",
        "//nirvana/application/endorsementapp/jobs",
        "//nirvana/application/endorsementapp/jobs/messages",
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/log",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api/db_wrappers/application/quoting",
        "//nirvana/db-api/db_wrappers/application/quoting/enums",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/external_data_management/clients_management",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "//nirvana/jobber/registry",
        "//nirvana/nonfleet/pricing_api",
        "//nirvana/nonfleet/pricing_api/request/post-bind",
        "//nirvana/nonfleet/rating",
        "//nirvana/rating/pricing/api",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_goccy_go_json//:go-json",
        "@com_github_google_uuid//:uuid",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_uber_go_fx//:fx",
    ],
)
