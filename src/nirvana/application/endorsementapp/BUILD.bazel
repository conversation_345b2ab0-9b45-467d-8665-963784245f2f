load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "endorsementapp",
    srcs = [
        "change_container_grouping.go",
        "object_defs.go",
    ],
    importpath = "nirvanatech.com/nirvana/application/endorsementapp",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/insurance-bundle/model/endorsement",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/endorsementapp/intake",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_shopspring_decimal//:decimal",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "endorsementapp_test",
    srcs = ["change_container_grouping_test.go"],
    embed = [":endorsementapp"],
    deps = [
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/insurance-bundle/model/endorsement",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
