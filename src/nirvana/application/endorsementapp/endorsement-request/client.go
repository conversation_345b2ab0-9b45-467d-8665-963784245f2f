package endorsement_request

import (
	"context"
	"time"

	"nirvanatech.com/nirvana/nonfleet/rating"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/application/endorsementapp"
	"nirvanatech.com/nirvana/common-go/proto"
	dbendorsementapp "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	endorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
)

//go:generate go run go.uber.org/mock/mockgen -destination mock_manager.go -package endorsement_request -typed nirvanatech.com/nirvana/application/endorsementapp/endorsement-request Manager
type Manager interface {
	// Create creates a new endorsement request
	Create(ctx context.Context, args endorsementrequest.CreateEndorsementRequestArgs) (uuid.UUID, error)

	// GetByID returns the endorsement request for the given request id
	GetByID(ctx context.Context, requestID uuid.UUID) (*endorsementrequest.Request, error)

	// SubmitForUWReview submits an endorsement request for underwriting review
	SubmitForUWReview(ctx context.Context, requestID, reviewID uuid.UUID) error

	// UpdateChanges updates the changes for an endorsement request by deactivating
	// specified changes and adding new ones. It also updates the effective interval
	UpdateChanges(
		ctx context.Context,
		endorsementRequestID uuid.UUID,
		changeIDsToRemove []string,
		changesToAdd []*dbendorsementapp.Change,
		effectiveInterval *proto.Interval,
	) error

	// GetAll returns all endorsement requests with the given filters
	GetAll(ctx context.Context, filters ...endorsementrequest.Filter) ([]*endorsementrequest.Request, error)

	// UpdateDefaultEffectiveDate updates the default effective date for an endorsement request
	UpdateDefaultEffectiveDate(
		ctx context.Context,
		endorsementRequestID uuid.UUID,
		defaultEffectiveDate time.Time,
		bundleExpiryDate time.Time,
	) error

	// Close closes an endorsement request
	Close(ctx context.Context, requestID uuid.UUID) error

	// Bind binds the endorsement request
	Bind(ctx context.Context, requestID uuid.UUID, notes *string, supportingDocsHandles []uuid.UUID) error

	// Approve approves an endorsement request
	Approve(ctx context.Context, requestID uuid.UUID, premium *endorsementapp.EndorsementWrittenPremium) error

	// Decline declines an endorsement request
	Decline(ctx context.Context, requestID uuid.UUID) error

	// MarkOutOfSync marks an endorsement request as out of sync
	MarkOutOfSync(ctx context.Context, requestID uuid.UUID) error

	// PullMVR pulls MVR data for newly added drivers in the endorsement request
	// This is used during instant pricing to fetch driver violation data
	PullMVR(ctx context.Context, endorsementRequestID uuid.UUID, driverChanges map[string]*dbendorsementapp.Change) error

	// GenerateQuote triggers the pricing job for an endorsement request to refresh price
	GenerateQuote(ctx context.Context, endorsementRequestID uuid.UUID, driverChanges map[string]*dbendorsementapp.Change, runType rating.RunType) error
}
