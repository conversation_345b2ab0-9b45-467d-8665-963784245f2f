// Code generated by MockGen. DO NOT EDIT.
// Source: nirvanatech.com/nirvana/application/endorsementapp/endorsement-request (interfaces: Manager)
//
// Generated by this command:
//
//	mockgen -destination mock_manager.go -package endorsement_request -typed nirvanatech.com/nirvana/application/endorsementapp/endorsement-request Manager
//

// Package endorsement_request is a generated GoMock package.
package endorsement_request

import (
	context "context"
	reflect "reflect"
	time "time"

	uuid "github.com/google/uuid"
	gomock "go.uber.org/mock/gomock"
	endorsementapp "nirvanatech.com/nirvana/application/endorsementapp"
	proto "nirvanatech.com/nirvana/common-go/proto"
	endorsementapp0 "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	endorsement_request "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	rating "nirvanatech.com/nirvana/nonfleet/rating"
)

// MockManager is a mock of Manager interface.
type MockManager struct {
	ctrl     *gomock.Controller
	recorder *MockManagerMockRecorder
	isgomock struct{}
}

// MockManagerMockRecorder is the mock recorder for MockManager.
type MockManagerMockRecorder struct {
	mock *MockManager
}

// NewMockManager creates a new mock instance.
func NewMockManager(ctrl *gomock.Controller) *MockManager {
	mock := &MockManager{ctrl: ctrl}
	mock.recorder = &MockManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockManager) EXPECT() *MockManagerMockRecorder {
	return m.recorder
}

// Approve mocks base method.
func (m *MockManager) Approve(ctx context.Context, requestID uuid.UUID, premium *endorsementapp.EndorsementWrittenPremium) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Approve", ctx, requestID, premium)
	ret0, _ := ret[0].(error)
	return ret0
}

// Approve indicates an expected call of Approve.
func (mr *MockManagerMockRecorder) Approve(ctx, requestID, premium any) *MockManagerApproveCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Approve", reflect.TypeOf((*MockManager)(nil).Approve), ctx, requestID, premium)
	return &MockManagerApproveCall{Call: call}
}

// MockManagerApproveCall wrap *gomock.Call
type MockManagerApproveCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerApproveCall) Return(arg0 error) *MockManagerApproveCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerApproveCall) Do(f func(context.Context, uuid.UUID, *endorsementapp.EndorsementWrittenPremium) error) *MockManagerApproveCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerApproveCall) DoAndReturn(f func(context.Context, uuid.UUID, *endorsementapp.EndorsementWrittenPremium) error) *MockManagerApproveCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// Bind mocks base method.
func (m *MockManager) Bind(ctx context.Context, requestID uuid.UUID, notes *string, supportingDocsHandles []uuid.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Bind", ctx, requestID, notes, supportingDocsHandles)
	ret0, _ := ret[0].(error)
	return ret0
}

// Bind indicates an expected call of Bind.
func (mr *MockManagerMockRecorder) Bind(ctx, requestID, notes, supportingDocsHandles any) *MockManagerBindCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Bind", reflect.TypeOf((*MockManager)(nil).Bind), ctx, requestID, notes, supportingDocsHandles)
	return &MockManagerBindCall{Call: call}
}

// MockManagerBindCall wrap *gomock.Call
type MockManagerBindCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerBindCall) Return(arg0 error) *MockManagerBindCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerBindCall) Do(f func(context.Context, uuid.UUID, *string, []uuid.UUID) error) *MockManagerBindCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerBindCall) DoAndReturn(f func(context.Context, uuid.UUID, *string, []uuid.UUID) error) *MockManagerBindCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// Close mocks base method.
func (m *MockManager) Close(ctx context.Context, requestID uuid.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close", ctx, requestID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockManagerMockRecorder) Close(ctx, requestID any) *MockManagerCloseCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockManager)(nil).Close), ctx, requestID)
	return &MockManagerCloseCall{Call: call}
}

// MockManagerCloseCall wrap *gomock.Call
type MockManagerCloseCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerCloseCall) Return(arg0 error) *MockManagerCloseCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerCloseCall) Do(f func(context.Context, uuid.UUID) error) *MockManagerCloseCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerCloseCall) DoAndReturn(f func(context.Context, uuid.UUID) error) *MockManagerCloseCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// Create mocks base method.
func (m *MockManager) Create(ctx context.Context, args endorsement_request.CreateEndorsementRequestArgs) (uuid.UUID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, args)
	ret0, _ := ret[0].(uuid.UUID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockManagerMockRecorder) Create(ctx, args any) *MockManagerCreateCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockManager)(nil).Create), ctx, args)
	return &MockManagerCreateCall{Call: call}
}

// MockManagerCreateCall wrap *gomock.Call
type MockManagerCreateCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerCreateCall) Return(arg0 uuid.UUID, arg1 error) *MockManagerCreateCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerCreateCall) Do(f func(context.Context, endorsement_request.CreateEndorsementRequestArgs) (uuid.UUID, error)) *MockManagerCreateCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerCreateCall) DoAndReturn(f func(context.Context, endorsement_request.CreateEndorsementRequestArgs) (uuid.UUID, error)) *MockManagerCreateCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// Decline mocks base method.
func (m *MockManager) Decline(ctx context.Context, requestID uuid.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Decline", ctx, requestID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Decline indicates an expected call of Decline.
func (mr *MockManagerMockRecorder) Decline(ctx, requestID any) *MockManagerDeclineCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Decline", reflect.TypeOf((*MockManager)(nil).Decline), ctx, requestID)
	return &MockManagerDeclineCall{Call: call}
}

// MockManagerDeclineCall wrap *gomock.Call
type MockManagerDeclineCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerDeclineCall) Return(arg0 error) *MockManagerDeclineCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerDeclineCall) Do(f func(context.Context, uuid.UUID) error) *MockManagerDeclineCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerDeclineCall) DoAndReturn(f func(context.Context, uuid.UUID) error) *MockManagerDeclineCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GenerateQuote mocks base method.
func (m *MockManager) GenerateQuote(ctx context.Context, endorsementRequestID uuid.UUID, driverChanges map[string]*endorsementapp0.Change, runType rating.RunType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateQuote", ctx, endorsementRequestID, driverChanges, runType)
	ret0, _ := ret[0].(error)
	return ret0
}

// GenerateQuote indicates an expected call of GenerateQuote.
func (mr *MockManagerMockRecorder) GenerateQuote(ctx, endorsementRequestID, driverChanges, runType any) *MockManagerGenerateQuoteCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateQuote", reflect.TypeOf((*MockManager)(nil).GenerateQuote), ctx, endorsementRequestID, driverChanges, runType)
	return &MockManagerGenerateQuoteCall{Call: call}
}

// MockManagerGenerateQuoteCall wrap *gomock.Call
type MockManagerGenerateQuoteCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerGenerateQuoteCall) Return(arg0 error) *MockManagerGenerateQuoteCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerGenerateQuoteCall) Do(f func(context.Context, uuid.UUID, map[string]*endorsementapp0.Change, rating.RunType) error) *MockManagerGenerateQuoteCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerGenerateQuoteCall) DoAndReturn(f func(context.Context, uuid.UUID, map[string]*endorsementapp0.Change, rating.RunType) error) *MockManagerGenerateQuoteCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetAll mocks base method.
func (m *MockManager) GetAll(ctx context.Context, filters ...endorsement_request.Filter) ([]*endorsement_request.Request, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx}
	for _, a := range filters {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAll", varargs...)
	ret0, _ := ret[0].([]*endorsement_request.Request)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockManagerMockRecorder) GetAll(ctx any, filters ...any) *MockManagerGetAllCall {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx}, filters...)
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockManager)(nil).GetAll), varargs...)
	return &MockManagerGetAllCall{Call: call}
}

// MockManagerGetAllCall wrap *gomock.Call
type MockManagerGetAllCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerGetAllCall) Return(arg0 []*endorsement_request.Request, arg1 error) *MockManagerGetAllCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerGetAllCall) Do(f func(context.Context, ...endorsement_request.Filter) ([]*endorsement_request.Request, error)) *MockManagerGetAllCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerGetAllCall) DoAndReturn(f func(context.Context, ...endorsement_request.Filter) ([]*endorsement_request.Request, error)) *MockManagerGetAllCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// GetByID mocks base method.
func (m *MockManager) GetByID(ctx context.Context, requestID uuid.UUID) (*endorsement_request.Request, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, requestID)
	ret0, _ := ret[0].(*endorsement_request.Request)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockManagerMockRecorder) GetByID(ctx, requestID any) *MockManagerGetByIDCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockManager)(nil).GetByID), ctx, requestID)
	return &MockManagerGetByIDCall{Call: call}
}

// MockManagerGetByIDCall wrap *gomock.Call
type MockManagerGetByIDCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerGetByIDCall) Return(arg0 *endorsement_request.Request, arg1 error) *MockManagerGetByIDCall {
	c.Call = c.Call.Return(arg0, arg1)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerGetByIDCall) Do(f func(context.Context, uuid.UUID) (*endorsement_request.Request, error)) *MockManagerGetByIDCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerGetByIDCall) DoAndReturn(f func(context.Context, uuid.UUID) (*endorsement_request.Request, error)) *MockManagerGetByIDCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// MarkOutOfSync mocks base method.
func (m *MockManager) MarkOutOfSync(ctx context.Context, requestID uuid.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkOutOfSync", ctx, requestID)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkOutOfSync indicates an expected call of MarkOutOfSync.
func (mr *MockManagerMockRecorder) MarkOutOfSync(ctx, requestID any) *MockManagerMarkOutOfSyncCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkOutOfSync", reflect.TypeOf((*MockManager)(nil).MarkOutOfSync), ctx, requestID)
	return &MockManagerMarkOutOfSyncCall{Call: call}
}

// MockManagerMarkOutOfSyncCall wrap *gomock.Call
type MockManagerMarkOutOfSyncCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerMarkOutOfSyncCall) Return(arg0 error) *MockManagerMarkOutOfSyncCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerMarkOutOfSyncCall) Do(f func(context.Context, uuid.UUID) error) *MockManagerMarkOutOfSyncCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerMarkOutOfSyncCall) DoAndReturn(f func(context.Context, uuid.UUID) error) *MockManagerMarkOutOfSyncCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// PullMVR mocks base method.
func (m *MockManager) PullMVR(ctx context.Context, endorsementRequestID uuid.UUID, driverChanges map[string]*endorsementapp0.Change) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PullMVR", ctx, endorsementRequestID, driverChanges)
	ret0, _ := ret[0].(error)
	return ret0
}

// PullMVR indicates an expected call of PullMVR.
func (mr *MockManagerMockRecorder) PullMVR(ctx, endorsementRequestID, driverChanges any) *MockManagerPullMVRCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PullMVR", reflect.TypeOf((*MockManager)(nil).PullMVR), ctx, endorsementRequestID, driverChanges)
	return &MockManagerPullMVRCall{Call: call}
}

// MockManagerPullMVRCall wrap *gomock.Call
type MockManagerPullMVRCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerPullMVRCall) Return(arg0 error) *MockManagerPullMVRCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerPullMVRCall) Do(f func(context.Context, uuid.UUID, map[string]*endorsementapp0.Change) error) *MockManagerPullMVRCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerPullMVRCall) DoAndReturn(f func(context.Context, uuid.UUID, map[string]*endorsementapp0.Change) error) *MockManagerPullMVRCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// SubmitForUWReview mocks base method.
func (m *MockManager) SubmitForUWReview(ctx context.Context, requestID, reviewID uuid.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitForUWReview", ctx, requestID, reviewID)
	ret0, _ := ret[0].(error)
	return ret0
}

// SubmitForUWReview indicates an expected call of SubmitForUWReview.
func (mr *MockManagerMockRecorder) SubmitForUWReview(ctx, requestID, reviewID any) *MockManagerSubmitForUWReviewCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitForUWReview", reflect.TypeOf((*MockManager)(nil).SubmitForUWReview), ctx, requestID, reviewID)
	return &MockManagerSubmitForUWReviewCall{Call: call}
}

// MockManagerSubmitForUWReviewCall wrap *gomock.Call
type MockManagerSubmitForUWReviewCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerSubmitForUWReviewCall) Return(arg0 error) *MockManagerSubmitForUWReviewCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerSubmitForUWReviewCall) Do(f func(context.Context, uuid.UUID, uuid.UUID) error) *MockManagerSubmitForUWReviewCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerSubmitForUWReviewCall) DoAndReturn(f func(context.Context, uuid.UUID, uuid.UUID) error) *MockManagerSubmitForUWReviewCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// UpdateChanges mocks base method.
func (m *MockManager) UpdateChanges(ctx context.Context, endorsementRequestID uuid.UUID, changeIDsToRemove []string, changesToAdd []*endorsementapp0.Change, effectiveInterval *proto.Interval) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChanges", ctx, endorsementRequestID, changeIDsToRemove, changesToAdd, effectiveInterval)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateChanges indicates an expected call of UpdateChanges.
func (mr *MockManagerMockRecorder) UpdateChanges(ctx, endorsementRequestID, changeIDsToRemove, changesToAdd, effectiveInterval any) *MockManagerUpdateChangesCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChanges", reflect.TypeOf((*MockManager)(nil).UpdateChanges), ctx, endorsementRequestID, changeIDsToRemove, changesToAdd, effectiveInterval)
	return &MockManagerUpdateChangesCall{Call: call}
}

// MockManagerUpdateChangesCall wrap *gomock.Call
type MockManagerUpdateChangesCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerUpdateChangesCall) Return(arg0 error) *MockManagerUpdateChangesCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerUpdateChangesCall) Do(f func(context.Context, uuid.UUID, []string, []*endorsementapp0.Change, *proto.Interval) error) *MockManagerUpdateChangesCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerUpdateChangesCall) DoAndReturn(f func(context.Context, uuid.UUID, []string, []*endorsementapp0.Change, *proto.Interval) error) *MockManagerUpdateChangesCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}

// UpdateDefaultEffectiveDate mocks base method.
func (m *MockManager) UpdateDefaultEffectiveDate(ctx context.Context, endorsementRequestID uuid.UUID, defaultEffectiveDate, bundleExpiryDate time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDefaultEffectiveDate", ctx, endorsementRequestID, defaultEffectiveDate, bundleExpiryDate)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDefaultEffectiveDate indicates an expected call of UpdateDefaultEffectiveDate.
func (mr *MockManagerMockRecorder) UpdateDefaultEffectiveDate(ctx, endorsementRequestID, defaultEffectiveDate, bundleExpiryDate any) *MockManagerUpdateDefaultEffectiveDateCall {
	mr.mock.ctrl.T.Helper()
	call := mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDefaultEffectiveDate", reflect.TypeOf((*MockManager)(nil).UpdateDefaultEffectiveDate), ctx, endorsementRequestID, defaultEffectiveDate, bundleExpiryDate)
	return &MockManagerUpdateDefaultEffectiveDateCall{Call: call}
}

// MockManagerUpdateDefaultEffectiveDateCall wrap *gomock.Call
type MockManagerUpdateDefaultEffectiveDateCall struct {
	*gomock.Call
}

// Return rewrite *gomock.Call.Return
func (c *MockManagerUpdateDefaultEffectiveDateCall) Return(arg0 error) *MockManagerUpdateDefaultEffectiveDateCall {
	c.Call = c.Call.Return(arg0)
	return c
}

// Do rewrite *gomock.Call.Do
func (c *MockManagerUpdateDefaultEffectiveDateCall) Do(f func(context.Context, uuid.UUID, time.Time, time.Time) error) *MockManagerUpdateDefaultEffectiveDateCall {
	c.Call = c.Call.Do(f)
	return c
}

// DoAndReturn rewrite *gomock.Call.DoAndReturn
func (c *MockManagerUpdateDefaultEffectiveDateCall) DoAndReturn(f func(context.Context, uuid.UUID, time.Time, time.Time) error) *MockManagerUpdateDefaultEffectiveDateCall {
	c.Call = c.Call.DoAndReturn(f)
	return c
}
