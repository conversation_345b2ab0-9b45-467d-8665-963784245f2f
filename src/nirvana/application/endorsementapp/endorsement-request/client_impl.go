package endorsement_request

import (
	"context"
	"time"

	"nirvanatech.com/nirvana/nonfleet/rating"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/application/endorsementapp"
	"nirvanatech.com/nirvana/application/endorsementapp/endorsement-request/internal/client"
	"nirvanatech.com/nirvana/common-go/proto"
	dbendorsementapp "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	endorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
)

type endorsementRequestClient struct {
	impl *client.Impl
}

func NewClient(impl *client.Impl) Manager {
	return &endorsementRequestClient{
		impl: impl,
	}
}

func (e *endorsementRequestClient) Create(
	ctx context.Context,
	args endorsementrequest.CreateEndorsementRequestArgs,
) (uuid.UUID, error) {
	return e.impl.Create(ctx, args)
}

func (e *endorsementRequestClient) SubmitForUWReview(ctx context.Context, requestID, reviewID uuid.UUID) error {
	return e.impl.SubmitForUWReview(ctx, requestID, reviewID)
}

func (e *endorsementRequestClient) GetByID(ctx context.Context, requestID uuid.UUID) (*endorsementrequest.Request, error) {
	return e.impl.GetByID(ctx, requestID)
}

func (e *endorsementRequestClient) UpdateChanges(
	ctx context.Context,
	endorsementRequestID uuid.UUID,
	changeIDsToRemove []string,
	changesToAdd []*dbendorsementapp.Change,
	effectiveInterval *proto.Interval,
) error {
	return e.impl.UpdateChanges(ctx, endorsementRequestID, changeIDsToRemove, changesToAdd, effectiveInterval)
}

func (e *endorsementRequestClient) GetAll(ctx context.Context, filters ...endorsementrequest.Filter) ([]*endorsementrequest.Request, error) {
	return e.impl.GetAll(ctx, filters...)
}

func (e *endorsementRequestClient) UpdateDefaultEffectiveDate(
	ctx context.Context,
	endorsementRequestID uuid.UUID,
	defaultEffectiveDate time.Time,
	bundleExpirationDate time.Time,
) error {
	return e.impl.UpdateDefaultEffectiveDate(ctx, endorsementRequestID, defaultEffectiveDate, bundleExpirationDate)
}

func (e *endorsementRequestClient) Close(ctx context.Context, requestID uuid.UUID) error {
	return e.impl.Close(ctx, requestID)
}

func (e *endorsementRequestClient) Bind(
	ctx context.Context,
	requestID uuid.UUID,
	notes *string,
	supportingDocsHandles []uuid.UUID,
) error {
	return e.impl.Bind(ctx, requestID, notes, supportingDocsHandles)
}

func (e *endorsementRequestClient) Approve(
	ctx context.Context,
	requestID uuid.UUID,
	premium *endorsementapp.EndorsementWrittenPremium,
) error {
	return e.impl.Approve(ctx, requestID, premium)
}

func (e *endorsementRequestClient) Decline(ctx context.Context, requestID uuid.UUID) error {
	return e.impl.Decline(ctx, requestID)
}

func (e *endorsementRequestClient) MarkOutOfSync(ctx context.Context, requestID uuid.UUID) error {
	return e.impl.MarkOutOfSync(ctx, requestID)
}

func (e *endorsementRequestClient) PullMVR(ctx context.Context, endorsementRequestID uuid.UUID, driverChanges map[string]*dbendorsementapp.Change) error {
	return e.impl.PullMVR(ctx, endorsementRequestID, driverChanges)
}

func (e *endorsementRequestClient) GenerateQuote(ctx context.Context, endorsementRequestID uuid.UUID, driverChanges map[string]*dbendorsementapp.Change, runType rating.RunType) error {
	return e.impl.GenerateQuote(ctx, endorsementRequestID, driverChanges, runType)
}
