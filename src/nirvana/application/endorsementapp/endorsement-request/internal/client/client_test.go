package client_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	nf_types "nirvanatech.com/nirvana/nonfleet/model/endorsement"

	insurancecoreproto "nirvanatech.com/nirvana/insurance-core/proto"

	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/proto"
	mockBuilder "nirvanatech.com/nirvana/common-go/test_utils/builders/endorsementapp/endorsement-request"
	mockReviewBuilder "nirvanatech.com/nirvana/common-go/test_utils/builders/endorsementapp/endorsement-review"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting"
	endreviewenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums"
	"nirvanatech.com/nirvana/insurance-bundle/model/charges"
	"nirvanatech.com/nirvana/insurance-bundle/model/endorsement"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/fx"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/application/endorsementapp/endorsement-request/internal"
	"nirvanatech.com/nirvana/application/endorsementapp/endorsement-request/internal/client"
	_ "nirvanatech.com/nirvana/application/endorsementapp/jobs/pricing"
	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	endorsement_request "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	endreqenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"
	endorsement_review "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	legacy_write_gateway "nirvanatech.com/nirvana/endorsement/legacy-write-gateway"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	"nirvanatech.com/nirvana/nonfleet/rating"
)

func TestSubmitForUWReviewForLegacyWrites(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	ctrl, ctx := gomock.WithContext(ctx, t)
	mockEndorsementRequestWrapper := endorsement_request.NewMockWrapper(ctrl)
	mockEndorsementReviewWrapper := endorsement_review.NewMockWrapper(ctrl)
	mockLegacyWriteGateway := legacy_write_gateway.NewMockLegacyWriteGateway(ctrl)

	fleetEndReq := &endorsement_request.Request{
		ID:          uuid.New(),
		ProgramType: insurancecoreproto.ProgramType_ProgramType_Fleet,
		State:       endreqenums.EndorsementRequestStateCreated,
	}
	nfEndReq := &endorsement_request.Request{
		ID:          uuid.New(),
		ProgramType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
		State:       endreqenums.EndorsementRequestStateCreated,
	}
	endReviewID := uuid.New()

	var env struct {
		fx.In

		Jobber quoting_jobber.Client
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	deps := internal.Deps{
		EndorsementRequestWrapper:     mockEndorsementRequestWrapper,
		LegacyEndorsementWriteGateway: mockLegacyWriteGateway,
		EndorsementReviewWrapper:      mockEndorsementReviewWrapper,
		Jobber:                        env.Jobber,
	}
	impl := client.New(deps)

	// Default expectations
	mockEndorsementRequestWrapper.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockEndorsementReviewWrapper.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	t.Run("fleet endorsement request: gateway success", func(t *testing.T) {
		// Expect two calls to GetByID for the fleet endorsement request: one for getting the state machine and the other
		// to determine its program type in the Submit callback.
		mockEndorsementRequestWrapper.EXPECT().GetByID(gomock.Any(), fleetEndReq.ID).Return(fleetEndReq, nil).
			Times(2)
		// Expect one call to HandleSubmitEndorsementAppRequest: in the Submit callback.
		mockLegacyWriteGateway.EXPECT().HandleSubmitEndorsementAppRequest(gomock.Any(), fleetEndReq, endReviewID).Return(nil)

		err := impl.SubmitForUWReview(ctx, fleetEndReq.ID, endReviewID)
		assert.NoError(t, err)
	})

	t.Run("fleet endorsement request: gateway failure", func(t *testing.T) {
		// Expect two calls to GetByID for the fleet endorsement request: one for getting the state machine and the other
		// to determine its program type in the Submit callback.
		mockEndorsementRequestWrapper.EXPECT().GetByID(gomock.Any(), fleetEndReq.ID).Return(fleetEndReq, nil).
			Times(2)
		// Expect one call to HandleSubmitEndorsementAppRequest: in the Submit callback.
		mockLegacyWriteGateway.EXPECT().HandleSubmitEndorsementAppRequest(gomock.Any(), fleetEndReq, endReviewID).Return(assert.AnError)

		err := impl.SubmitForUWReview(ctx, fleetEndReq.ID, endReviewID)
		assert.ErrorContains(
			t, err,
			fmt.Sprintf(
				"failed to write submit endorsement app request to the legacy system for endorsement request %s",
				fleetEndReq.ID,
			),
		)
	})

	t.Run("non-fleet endorsement request: no gateway call", func(t *testing.T) {
		// Expect two calls to GetByID for the non-fleet endorsement request: one for getting the state machine and the other
		// to determine its program type in the Submit callback.
		mockEndorsementRequestWrapper.EXPECT().GetByID(gomock.Any(), nfEndReq.ID).Return(nfEndReq, nil).
			Times(2)
		// Expect no calls to HandleSubmitEndorsementAppRequest: in the Submit callback.
		mockLegacyWriteGateway.EXPECT().HandleSubmitEndorsementAppRequest(gomock.Any(), nfEndReq, endReviewID).Times(0)

		err := impl.SubmitForUWReview(ctx, nfEndReq.ID, endReviewID)
		assert.NoError(t, err)
	})
}

func TestBindForUWReviewForLegacyWrites(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	ctrl, ctx := gomock.WithContext(ctx, t)
	mockEndorsementRequestWrapper := endorsement_request.NewMockWrapper(ctrl)
	mockEndorsementReviewWrapper := endorsement_review.NewMockWrapper(ctrl)
	mockLegacyWriteGateway := legacy_write_gateway.NewMockLegacyWriteGateway(ctrl)
	mockInsuranceBundleServiceClient := service.NewMockInsuranceBundleManagerClient(ctrl)
	pricingWrapper := quoting.NewMockPricingWrapper(ctrl)

	var env struct {
		fx.In

		Jobber quoting_jobber.Client
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	deps := internal.Deps{
		EndorsementRequestWrapper:     mockEndorsementRequestWrapper,
		LegacyEndorsementWriteGateway: mockLegacyWriteGateway,
		EndorsementReviewWrapper:      mockEndorsementReviewWrapper,
		InsuranceBundleManagerClient:  mockInsuranceBundleServiceClient,
		PricingWrapper:                pricingWrapper,
		Jobber:                        env.Jobber,
	}
	impl := client.New(deps)

	reviewId := uuid.New()
	requestId := uuid.New()
	requestIDFilter := endorsement_review.RequestIDsIn([]string{requestId.String()})
	reason := "Closed by Nirvana"
	supportingHandles := []uuid.UUID{uuid.New(), uuid.New(), uuid.New()}
	review := mockReviewBuilder.NewEndorsementReviewBuilder().
		WithID(reviewId).
		WithRequestID(requestId).
		WithState(endreviewenums.EndorsementReviewStateApproved).
		Build()
	reviewStatesInFilter := endorsement_review.ReviewStatesIn(review.State.String())
	request := mockBuilder.NewEndorsementRequestBuilder().
		WithBaseID(uuid.New()).
		WithID(requestId).
		WithState(endreqenums.EndorsementRequestStateApproved).
		WithProgramType(insurancecoreproto.ProgramType_ProgramType_Fleet).
		WithChanges(
			mockBuilder.NFAddDriverChangeDataWithDescription,
			mockBuilder.NFRemoveDriverChangeDataWithDescription,
			mockBuilder.NFAddVehicleChangeDataWithDescription,
		).Build()
	interval1 := &proto.Interval{
		Start: timestamppb.New(time_utils.NewDate(2024, 12, 1).ToTime()),
		End:   timestamppb.New(time_utils.NewDate(2025, 5, 1).ToTime()),
	}
	executeEndorsementResponse := service.ExecuteEndorsementResponse{
		ExecuteEndorsementRequestID: "a4ebecd3-bedd-4ae5-85f2-0222fabdbe8d",
		InsuranceBundle: model.NewInsuranceBundleBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).
			WithExternalId("NNFTK0012345-24").
			WithInternalId("test-internal-id").
			WithSegments([]*model.InsuranceBundleSegment{model.NewInsuranceBundleSegmentBuilder(insurancecoreproto.ProgramType_ProgramType_Fleet).WithInterval(interval1).Build()}).
			Build(),
		InsuranceBundleSegmentIDToChangeContainerIDMap: map[string]string{
			"segment-1": "change-container-1",
			"segment-2": "change-container-2",
		},
	}
	pricingContexts := []quoting.PricingContext{
		{
			StartTime: time_utils.NewDate(2024, 12, 1).ToTime(),
			EndTime:   time_utils.NewDate(2025, 5, 1).ToTime(),
			Charges: &charges.PolicyCharges{
				PolicyCharges: map[string]*charges.ChargeList{
					"NNFTK0012345-24": {
						Charges: []*ptypes.Charge{
							ptypes.NewChargeBuilder().
								WithAmountBasedBillingDetails("120", nil).
								WithBaseChargeTypeWithoutExtraInfo().
								WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
								Build(),
							ptypes.NewChargeBuilder().
								WithAmountBasedBillingDetails("200", nil).
								WithBaseChargeTypeWithoutExtraInfo().
								WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
								Build(),
							ptypes.NewChargeBuilder().
								WithAmountBasedBillingDetails("500", nil).
								WithBaseChargeTypeWithoutExtraInfo().
								WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Collision).
								Build(),
						},
					},
				},
			},
		},
	}

	// Default expectations
	mockEndorsementRequestWrapper.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockEndorsementReviewWrapper.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	t.Run("Close Endorsement Review - legacy bind gateway success", func(t *testing.T) {
		mockEndorsementReviewWrapper.EXPECT().Get(gomock.Any(), requestIDFilter, reviewStatesInFilter).
			Return([]*endorsement_review.Review{&review}, nil).Times(1)
		mockEndorsementReviewWrapper.EXPECT().GetByID(gomock.Any(), reviewId).Return(&review, nil).Times(3)
		mockEndorsementRequestWrapper.EXPECT().GetByID(gomock.Any(), requestId).Return(&request, nil).
			Times(5)
		mockEndorsementRequestWrapper.EXPECT().Get(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
		mockInsuranceBundleServiceClient.EXPECT().ExecuteEndorsement(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&executeEndorsementResponse, nil).Times(2)
		pricingWrapper.EXPECT().GetQuotingPricingContextByIds(gomock.Any(), gomock.Any()).
			Return(pricingContexts, nil).
			Times(1)
		mockLegacyWriteGateway.EXPECT().
			HandleBindEndorsementAppReview(gomock.Any(),
				reviewId, &reason, []uuid.UUID{}, supportingHandles).Return(nil).Times(1)
		err := impl.Bind(ctx, requestId, &reason, supportingHandles)
		assert.NoError(t, err)
	})

	t.Run("Close Endorsement Review - legacy bind gateway failure", func(t *testing.T) {
		mockEndorsementReviewWrapper.EXPECT().Get(gomock.Any(), requestIDFilter, reviewStatesInFilter).
			Return([]*endorsement_review.Review{&review}, nil).Times(1)
		mockEndorsementReviewWrapper.EXPECT().GetByID(gomock.Any(), reviewId).Return(&review, nil).Times(3)
		mockEndorsementRequestWrapper.EXPECT().GetByID(gomock.Any(), requestId).Return(&request, nil).
			Times(4)
		mockInsuranceBundleServiceClient.EXPECT().ExecuteEndorsement(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&executeEndorsementResponse, nil).Times(2)
		pricingWrapper.EXPECT().GetQuotingPricingContextByIds(gomock.Any(), gomock.Any()).
			Return(pricingContexts, nil).
			Times(1)
		mockLegacyWriteGateway.EXPECT().
			HandleBindEndorsementAppReview(gomock.Any(),
				reviewId, &reason, []uuid.UUID{}, supportingHandles).Return(assert.AnError).Times(1)
		err := impl.Bind(ctx, requestId, &reason, supportingHandles)
		assert.ErrorContains(
			t, err,
			fmt.Sprintf(
				"failed to approve endorsement app review to the legacy system for endorsementAppReviewId %s",
				reviewId,
			),
		)
	})
}

func TestGenerateQuoteWithNonFleetDriverChanges(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockEndorsementRequestWrapper := endorsement_request.NewMockWrapper(ctrl)
	mockInsuranceBundleManagerClient := service.NewMockInsuranceBundleManagerClient(ctrl)

	var env struct {
		fx.In
		Jobber quoting_jobber.Client
	}
	defer testloader.RequireStart(t, &env).RequireStop()

	endorsementRequestID := uuid.New()
	mockEndorsementRequest := &endorsement_request.Request{
		ID:          endorsementRequestID,
		ProgramType: insurancecoreproto.ProgramType_ProgramType_NonFleetAdmitted,
		State:       endreqenums.EndorsementRequestStateCreated,
		Base: endorsement_request.RequestBase{
			ID:   uuid.New(),
			Type: endreqenums.BasedOutOfBundle,
		},
		BundleExternalID: "test-bundle-id",
	}

	driverChanges := map[string]*endorsementapp.Change{
		"123": {
			Data: &endorsement.ChangeData{Data: &endorsement.ChangeData_NonFleetChange{
				NonFleetChange: &nf_types.NonFleetChange{
					ChangeType: nf_types.NonFleetChangeType_NonFleetChangeType_Driver,
					Data: &nf_types.NonFleetChange_DriverChange{DriverChange: &nf_types.DriverChange{
						Remove: []string{"F32040056059"},
					}},
				},
			}},
		},
	}
	runType := rating.RunTypeIndication

	deps := internal.Deps{
		Jobber:                       env.Jobber,
		EndorsementRequestWrapper:    mockEndorsementRequestWrapper,
		InsuranceBundleManagerClient: mockInsuranceBundleManagerClient,
	}
	impl := client.New(deps)

	mockEndorsementRequestWrapper.EXPECT().
		GetByID(gomock.Any(), endorsementRequestID).
		Return(mockEndorsementRequest, nil).
		AnyTimes()

	mockEndorsementRequestWrapper.EXPECT().
		Update(gomock.Any(), endorsementRequestID, gomock.Any()).
		Return(nil).Times(2)

	caState := "CA"
	mockInsuranceBundleManagerClient.EXPECT().
		GetInsuranceBundle(gomock.Any(), gomock.Any()).
		Return(&service.GetInsuranceBundleResponse{
			InsuranceBundle: &model.InsuranceBundle{
				ExternalId: "test-bundle-external-id",
				InternalId: "test-bundle-internal-id",
				Segments: []*model.InsuranceBundleSegment{
					{
						Id: "test-segment-id",
						Interval: &proto.Interval{
							Start: timestamppb.New(time.Now()),
							End:   timestamppb.New(time.Now().AddDate(1, 0, 0)),
						},
						PrimaryInsured: &insurancecoreproto.Insured{
							Address: &proto.Address{State: &caState}, // California
						},
					},
				},
			},
		}, nil).
		AnyTimes()

	err := impl.GenerateQuote(ctx, endorsementRequestID, driverChanges, runType)
	assert.NoError(t, err)
}
