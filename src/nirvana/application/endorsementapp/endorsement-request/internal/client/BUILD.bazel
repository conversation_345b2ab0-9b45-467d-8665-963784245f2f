load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "client",
    srcs = [
        "client.go",
        "fx.go",
    ],
    importpath = "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request/internal/client",
    visibility = ["//nirvana/application/endorsementapp/endorsement-request:__subpackages__"],
    deps = [
        "//nirvana/api-server/handlers/common/ib",
        "//nirvana/application/endorsementapp",
        "//nirvana/application/endorsementapp/endorsement-request/internal",
        "//nirvana/application/endorsementapp/endorsement-request/internal/state-machine",
        "//nirvana/application/endorsementapp/jobs",
        "//nirvana/application/endorsementapp/jobs/messages",
        "//nirvana/common-go/log",
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/jobber",
        "//nirvana/jobber/jtypes",
        "//nirvana/nonfleet/model",
        "//nirvana/nonfleet/rating",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/rating/mvr",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_looplab_fsm//:fsm",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "client_test",
    srcs = ["client_test.go"],
    deps = [
        ":client",
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/application/endorsementapp/endorsement-request/internal",
        "//nirvana/application/endorsementapp/jobs/pricing",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/test_utils/builders/endorsementapp/endorsement-request",
        "//nirvana/common-go/test_utils/builders/endorsementapp/endorsement-review",
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/application/quoting",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums",
        "//nirvana/endorsement/legacy-write-gateway",
        "//nirvana/infra/fx/testloader",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/model/charges",
        "//nirvana/insurance-bundle/model/endorsement",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/nonfleet/model/endorsement",
        "//nirvana/nonfleet/rating",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)
