package client

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	commonib "nirvanatech.com/nirvana/api-server/handlers/common/ib"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels/driver"

	"go.uber.org/zap"
	"nirvanatech.com/nirvana/application/endorsementapp/jobs"
	"nirvanatech.com/nirvana/application/endorsementapp/jobs/messages"
	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/nonfleet/model"
	"nirvanatech.com/nirvana/nonfleet/rating"
	"nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/rating/mvr"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/looplab/fsm"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/application/endorsementapp"
	"nirvanatech.com/nirvana/application/endorsementapp/endorsement-request/internal"
	statemachine "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request/internal/state-machine"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	dbendorsementapp "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp"
	endorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	endreqenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"
	endorsementreview "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	endreviewenums "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
)

type Impl struct {
	Deps          internal.Deps
	stateMachines map[uuid.UUID]*statemachine.EndorsementRequestStateMachine
	mu            sync.Mutex
}

func New(deps internal.Deps) *Impl {
	return &Impl{
		Deps:          deps,
		stateMachines: make(map[uuid.UUID]*statemachine.EndorsementRequestStateMachine),
		mu:            sync.Mutex{},
	}
}

// getStateMachine returns the state machine for a given request id. If
// the state machine does not exist, it creates a new one. It maintains a map of
// state machines for all requests. This is done to ensure that we don't create
// multiple state machines for the same request id.
func (i *Impl) getStateMachine(
	ctx context.Context,
	endorsementRequestID uuid.UUID,
) (*fsm.FSM, error) {
	i.mu.Lock()
	defer i.mu.Unlock()
	if i.stateMachines == nil {
		i.stateMachines = make(map[uuid.UUID]*statemachine.EndorsementRequestStateMachine)
	}

	endReqObj, err := i.Deps.EndorsementRequestWrapper.GetByID(ctx, endorsementRequestID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get endorsement request %s", endorsementRequestID)
	}
	stateMachine, err := statemachine.New(ctx, endReqObj.State, i.Deps)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to create state machine for endorsement request %s", endorsementRequestID)
	}

	retval := &statemachine.EndorsementRequestStateMachine{
		EndorsementRequestID: endorsementRequestID,
		StateMachine:         stateMachine,
	}
	i.stateMachines[endorsementRequestID] = retval
	return stateMachine, nil
}

func (i *Impl) SubmitForUWReview(ctx context.Context, requestID, reviewID uuid.UUID) error {
	machine, err := i.getStateMachine(ctx, requestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement request %s", requestID)
	}

	err = machine.Event(ctx, statemachine.EventSubmit.String(),
		statemachine.EventArgs{
			EndorsementRequestID: pointer_utils.ToPointer(requestID),
			EndorsementReviewID:  pointer_utils.ToPointer(reviewID),
		})
	if err != nil {
		return errors.Wrapf(err, "failed to submit endorsement request %s", requestID)
	}

	return nil
}

func (i *Impl) Create(ctx context.Context, args endorsementrequest.CreateEndorsementRequestArgs) (uuid.UUID, error) {
	endorsementRequestID := uuid.New()
	now := time.Now()

	provisionalEndorsementNumber, err := i.getProvisionalEndorsementNumber(ctx, args)
	if err != nil {
		return uuid.Nil, errors.Wrapf(err, "failed to get provisional endorsement number")
	}
	endorsementRequest := &endorsementrequest.Request{
		ID:                           endorsementRequestID,
		BundleExternalID:             args.BundleExternalID,
		ProvisionalEndorsementNumber: provisionalEndorsementNumber,
		Base: endorsementrequest.RequestBase{
			ID:   args.BaseID,
			Type: endreqenums.BasedOutOfBundle,
		},
		ProgramType: args.ProgramType,
		ProducerID:  args.ProducerID,
		MarketerID:  args.MarketerID,
		CreatedBy:   args.CreatedBy,
		AgencyID:    args.AgencyID,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	err = i.Deps.EndorsementRequestWrapper.Insert(ctx, endorsementRequest)
	if err != nil {
		return uuid.Nil, errors.Wrapf(err, "failed to insert endorsement request: %v", endorsementRequest.ID)
	}

	requestID := endorsementRequest.ID
	machine, err := i.getStateMachine(ctx, endorsementRequest.ID)
	if err != nil {
		return uuid.Nil, errors.Wrapf(err, "failed to get state machine for endorsement request %v", requestID)
	}

	err = machine.Event(ctx, statemachine.EventCreate.String(),
		statemachine.EventArgs{
			EndorsementRequestID: pointer_utils.ToPointer(endorsementRequest.ID),
		})
	if err != nil {
		return uuid.Nil, errors.Wrapf(err, "failed to create endorsement request %v", requestID)
	}

	return endorsementRequestID, nil
}

func (i *Impl) GetByID(ctx context.Context, requestID uuid.UUID) (*endorsementrequest.Request, error) {
	return i.Deps.EndorsementRequestWrapper.GetByID(ctx, requestID)
}

func (i *Impl) UpdateChanges(
	ctx context.Context,
	endorsementRequestID uuid.UUID,
	changeIDsToRemove []string,
	changesToAdd []*dbendorsementapp.Change,
	effectiveInterval *proto.Interval,
) error {
	machine, err := i.getStateMachine(ctx, endorsementRequestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement request %v", endorsementRequestID)
	}

	if !machine.Is(endreqenums.EndorsementRequestStateCreated.String()) {
		return errors.Newf("cannot update changes for endorsement request %v in state %v", endorsementRequestID, machine.Current())
	}

	updaterFunc := func(request *endorsementrequest.Request) *endorsementrequest.Request {
		for i := range request.Changes {
			if slice_utils.Contains(changeIDsToRemove, request.Changes[i].Id) {
				request.Changes[i].IsActive = false
			}
		}
		request.Changes = append(request.Changes, slice_utils.FromSliceOfPointers(changesToAdd)...)

		// Modify the effective interval of the changes
		for i := range request.Changes {
			request.Changes[i].EffectiveInterval = effectiveInterval
		}
		return request
	}
	return i.Deps.EndorsementRequestWrapper.Update(ctx, endorsementRequestID, updaterFunc)
}

func (i *Impl) GetAll(ctx context.Context, filters ...endorsementrequest.Filter) ([]*endorsementrequest.Request, error) {
	return i.Deps.EndorsementRequestWrapper.Get(ctx, filters...)
}

func (i *Impl) Close(ctx context.Context, requestID uuid.UUID) error {
	machine, err := i.getStateMachine(ctx, requestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement request %s", requestID)
	}

	err = machine.Event(ctx, statemachine.EventClose.String(),
		statemachine.EventArgs{
			EndorsementRequestID: pointer_utils.ToPointer(requestID),
		})
	if err != nil {
		return errors.Wrapf(err, "failed to close endorsement request %s", requestID)
	}

	pendingReviewIDs, err := i.getAllPendingReviewIDs(ctx, requestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get pending review for endorsement request %s", requestID)
	}

	// Close the endorsement reviews associated with the endorsement request as well
	for _, reviewID := range pendingReviewIDs {
		err = i.updateReviewWithState(ctx, &reviewID, endreviewenums.EndorsementReviewStateClosed)
		if err != nil {
			return errors.Wrapf(err, "failed to close endorsement review %s", reviewID)
		}
	}

	return nil
}

func (i *Impl) updateReviewWithState(
	ctx context.Context,
	endorsementReviewID *uuid.UUID,
	state endreviewenums.EndorsementReviewState,
) error {
	// Close the endorsement review associated with the endorsement request as well
	if endorsementReviewID != nil {
		err := i.Deps.EndorsementReviewWrapper.Update(
			ctx,
			*endorsementReviewID,
			func(review *endorsementreview.Review) *endorsementreview.Review {
				review.State = state
				return review
			})
		if err != nil {
			return errors.Wrapf(err, "failed to close endorsement review %s", *endorsementReviewID)
		}
	}
	return nil
}

func (i *Impl) getAllPendingReviewIDs(ctx context.Context, requestID uuid.UUID) ([]uuid.UUID, error) {
	// Get all pending endorsement reviews for the request
	// Approving, declining, or closing an endorsement review requires the ability
	// to approve, decline, or close an endorsement request.
	// Since endorsementRequestManager is used in endorsementReviewManager,
	// a cyclic dependency would occur if we directly used endorsementReviewManager here.
	// To avoid this, we use endorsementReviewWrapper instead of endorsementReviewManager.
	endReviews, err := i.Deps.EndorsementReviewWrapper.Get(
		ctx,
		endorsementreview.RequestIDsIn([]string{requestID.String()}),
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get endorsement reviews for request %s", requestID)
	}

	targetStates := []endreviewenums.EndorsementReviewState{
		endreviewenums.EndorsementReviewStateApproved,
		endreviewenums.EndorsementReviewStateDeclined,
		endreviewenums.EndorsementReviewStateClosed,
	}

	reviewStates := slice_utils.Map(endReviews, func(review *endorsementreview.Review) endreviewenums.EndorsementReviewState {
		return review.State
	})

	if slice_utils.IntersectionExists(reviewStates, targetStates) {
		log.Info(ctx, "found approved, declined or closed endorsement review for request %s", log.Stringer("requestID", requestID))
	}

	reviewIDs := slice_utils.Map(endReviews, func(review *endorsementreview.Review) uuid.UUID {
		return review.ID
	})
	return reviewIDs, nil
}

func (i *Impl) UpdateDefaultEffectiveDate(
	ctx context.Context,
	endorsementRequestID uuid.UUID,
	defaultEffectiveDate time.Time,
	bundleExpirationDate time.Time,
) error {
	updaterFunc := func(request *endorsementrequest.Request) *endorsementrequest.Request {
		request.DefaultEffectiveDate = &defaultEffectiveDate

		// Modify the effective interval of the changes as well
		for i := range request.Changes {
			request.Changes[i].EffectiveInterval = &proto.Interval{
				Start: timestamppb.New(defaultEffectiveDate),
				End:   timestamppb.New(bundleExpirationDate),
			}
		}
		return request
	}
	return i.Deps.EndorsementRequestWrapper.Update(ctx, endorsementRequestID, updaterFunc)
}

// getProvisionalEndorsementNumber returns the next provisional endorsement number for the given bundle.
// It is calculated as the next version of the insurance bundle + the number of pending endorsement requests.
// Here the pending endorsement requests are the ones which are in the states Created, UnderUWReview, Approved.
// Bound, Closed & Declined requests are not considered.
// TODO: When a request gets bound, we can either update the provisional number of all pending requests
// have a new column in the request table to store the final endorsement number of that request.
// For other requests, we will need to update the provisional number of all pending requests, using the same logic.
// This is apart from updating their baseID as well.
func (i *Impl) getProvisionalEndorsementNumber(
	ctx context.Context,
	args endorsementrequest.CreateEndorsementRequestArgs,
) (string, error) {
	endReqs, err := i.GetAll(ctx,
		endorsementrequest.RequestStateIn(
			endreqenums.EndorsementRequestStateCreated.String(),
			endreqenums.EndorsementRequestStateUnderUWReview.String(),
			endreqenums.EndorsementRequestStateApproved.String(),
		),
		endorsementrequest.BundleExternalIDIs(args.BundleExternalID),
		endorsementrequest.IncludeActiveChanges(),
	)
	if err != nil {
		return "", errors.Wrapf(err, "failed to get endorsement requests for bundle %s which are in-progress", args.BundleExternalID)
	}

	pendingRequests := len(endReqs)

	nextIBVersion := args.InsuranceBundleVersion + 1

	provisionalNumber := fmt.Sprintf("%d.%d", nextIBVersion, pendingRequests+1)

	return provisionalNumber, nil
}

func (i *Impl) Bind(
	ctx context.Context,
	requestID uuid.UUID,
	notes *string,
	supportingDocsHandles []uuid.UUID,
) error {
	machine, err := i.getStateMachine(ctx, requestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement request %s", requestID)
	}

	// Get all pending endorsement reviews for the request
	// Approving, declining, or closing an endorsement review requires the ability
	// to approve, decline, or close an endorsement request.
	// Since endorsementRequestManager is used in endorsementReviewManager,
	// a cyclic dependency would occur if we directly used endorsementReviewManager here.
	// To avoid this, we use endorsementReviewWrapper instead of endorsementReviewManager.
	endReviews, err := i.Deps.EndorsementReviewWrapper.Get(ctx,
		endorsementreview.RequestIDsIn([]string{requestID.String()}),
		endorsementreview.ReviewStatesIn(endreviewenums.EndorsementReviewStateApproved.String()),
	)
	if err != nil {
		return errors.Wrapf(err, "failed to get endorsement reviews for request %s", requestID)
	}

	// If there are multiple approved reviews, return an error
	if len(endReviews) > 1 {
		return errors.Newf("found multiple approved endorsement reviews for request %s", requestID)
	}

	// If there is a pending review, set the endorsement review ID
	var endorsementReviewID *uuid.UUID
	if len(endReviews) != 0 {
		endorsementReviewID = &endReviews[0].ID
	}

	err = machine.Event(ctx, statemachine.EventBind.String(),
		statemachine.EventArgs{
			EndorsementRequestID:  pointer_utils.ToPointer(requestID),
			EndorsementReviewID:   endorsementReviewID,
			SupportingDocsHandles: supportingDocsHandles,
			Notes:                 notes,
		})
	if err != nil {
		return errors.Wrapf(err, "failed to bind endorsement request %s", requestID)
	}

	// Mark all conflicting requests as out of sync, as	the current request is bound
	// The conflicting requests are the now based out of bundle which is stale
	err = i.markConflictingRequestsOutOfSync(ctx, requestID)
	if err != nil {
		return errors.Wrap(err, "failed to mark conflicting requests as out of sync")
	}

	// Update the notes for the endorsement review
	err = i.Deps.EndorsementReviewWrapper.Update(
		ctx,
		endReviews[0].ID,
		func(review *endorsementreview.Review) *endorsementreview.Review {
			review.Notes = notes
			return review
		})
	if err != nil {
		return errors.Wrapf(err, "failed to update notes for endorsement review %s", endReviews[0].ID)
	}

	// Update the endorsement request with the bound at time
	err = i.Deps.EndorsementRequestWrapper.Update(
		ctx,
		requestID,
		func(request *endorsementrequest.Request) *endorsementrequest.Request {
			request.BoundAt = pointer_utils.ToPointer(time.Now())
			return request
		},
	)
	if err != nil {
		return errors.Wrapf(err, "failed to update bound at time for endorsement request %s", requestID)
	}

	return nil
}

func (i *Impl) markConflictingRequestsOutOfSync(ctx context.Context, requestID uuid.UUID) error {
	endReqObj, err := i.Deps.EndorsementRequestWrapper.GetByID(ctx, requestID)
	if err != nil {
		return errors.Wrap(err, "failed to fetch endorsement request")
	}
	staleReqs, err := i.Deps.EndorsementRequestWrapper.Get(
		ctx,
		endorsementrequest.BaseIDIs(endReqObj.Base.ID.String()),
		endorsementrequest.RequestStateIn(
			endreqenums.EndorsementRequestStateCreated.String(),
			endreqenums.EndorsementRequestStateUnderUWReview.String(),
			endreqenums.EndorsementRequestStateApproved.String(),
		),
		endorsementrequest.IncludeActiveChanges(),
	)
	if err != nil {
		return errors.Wrap(err, "failed to fetch stale endorsement requests")
	}

	for _, staleReq := range staleReqs {
		err = i.MarkOutOfSync(ctx, staleReq.ID)
		if err != nil {
			return errors.Wrap(err, "failed to mark endorsement request as out of sync")
		}
	}
	return nil
}

func (i *Impl) Approve(
	ctx context.Context,
	requestID uuid.UUID,
	premium *endorsementapp.EndorsementWrittenPremium,
) error {
	machine, err := i.getStateMachine(ctx, requestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement request %s", requestID)
	}

	err = machine.Event(ctx, statemachine.EventApprove.String(),
		statemachine.EventArgs{
			EndorsementRequestID: pointer_utils.ToPointer(requestID),
		})
	if err != nil {
		return errors.Wrapf(err, "failed to approve endorsement request %s", requestID)
	}

	err = i.Deps.EndorsementRequestWrapper.Update(ctx, requestID,
		func(request *endorsementrequest.Request) *endorsementrequest.Request {
			request.WrittenPremium = premium
			return request
		})
	if err != nil {
		return errors.Wrapf(err, "failed to update written premium for endorsement request %s", requestID)
	}

	return nil
}

func (i *Impl) Decline(ctx context.Context, requestID uuid.UUID) error {
	machine, err := i.getStateMachine(ctx, requestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement request %s", requestID)
	}

	err = machine.Event(ctx, statemachine.EventDecline.String(),
		statemachine.EventArgs{
			EndorsementRequestID: pointer_utils.ToPointer(requestID),
		})
	if err != nil {
		return errors.Wrapf(err, "failed to decline endorsement request %s", requestID)
	}

	return nil
}

func (i *Impl) MarkOutOfSync(ctx context.Context, requestID uuid.UUID) error {
	machine, err := i.getStateMachine(ctx, requestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get state machine for endorsement request %s", requestID)
	}

	err = machine.Event(ctx, statemachine.EventOutOfSync.String(),
		statemachine.EventArgs{
			EndorsementRequestID: pointer_utils.ToPointer(requestID),
		})
	if err != nil {
		return errors.Wrapf(err, "failed to mark endorsement request %s as out of sync", requestID)
	}

	pendingReviewIDs, err := i.getAllPendingReviewIDs(ctx, requestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get pending review for endorsement request %s", requestID)
	}

	// Close the endorsement reviews associated with the endorsement request as well
	for _, reviewID := range pendingReviewIDs {
		err = i.updateReviewWithState(ctx, &reviewID, endreviewenums.EndorsementReviewStateStale)
		if err != nil {
			return errors.Wrapf(err, "failed to close endorsement review %s", reviewID)
		}
	}

	return nil
}

// PullMVR pulls MVR data for newly added drivers in the endorsement request
func (i *Impl) PullMVR(ctx context.Context, endorsementRequestID uuid.UUID, driverChangesByCDL map[string]*dbendorsementapp.Change) error {
	// Get the endorsement request to extract effective date
	endReqObj, err := i.Deps.EndorsementRequestWrapper.GetByID(ctx, endorsementRequestID)
	if err != nil {
		return errors.Wrapf(err, "failed to get endorsement request: %v", err)
	}

	usState, err := getUSStateFromIB(ctx, i.Deps, endReqObj.Base.ID)
	if err != nil {
		log.Error(ctx, "failed to get US state for IB", log.String("bundleId", endReqObj.Base.ID.String()), log.Err(err))
		return errors.Wrapf(err, commonib.ErrGetUSState.Error())
	}
	// Extract newly added drivers
	newDrivers := make([]application.DriverBasicDetails, 0)
	for _, change := range driverChangesByCDL {
		driverChanges := change.Data.GetNonFleetChange().GetDriverChange()
		if driverChanges == nil {
			return nil
		}

		for _, driverToAdd := range driverChanges.Add {
			newDrivers = append(newDrivers, application.DriverBasicDetails{
				FirstName:     driverToAdd.FirstName,
				LastName:      driverToAdd.LastName,
				LicenseState:  driverToAdd.LicenseState,
				LicenseNumber: driverToAdd.LicenseNumber,
				DateOfBirth:   driverToAdd.DateOfBirth.AsTime(),
				DateOfHire:    driverToAdd.DateOfHire.AsTime(),
			})
		}
	}

	// Return early if no new drivers to pull MVR for
	if len(newDrivers) == 0 {
		log.Info(ctx, "No new drivers found, skipping MVR pull")
		return nil
	}

	log.Info(ctx, fmt.Sprintf("Pulling MVR for %d new drivers", len(newDrivers)))
	violationRecords, err := i.getDriverViolationRecords(ctx, newDrivers, endReqObj.DefaultEffectiveDate, usState)
	if err != nil {
		_ = i.Deps.EndorsementRequestWrapper.Update(
			ctx,
			endorsementRequestID,
			func(endReq *endorsementrequest.Request) *endorsementrequest.Request {
				endReq.QuoteGenerationInfo = &endorsementrequest.QuoteGenerationInfo{
					MVRPullDetails: &endorsementrequest.MVRPullDetails{
						Status:         common.MVRPullStatusError,
						LatestPullTime: time.Now(),
					},
				}
				return endReq
			})
		return errors.Wrapf(err, "failed to get mvr for endorsement request %s", endorsementRequestID)
	}

	changesToBeUpdated := i.getEndorsementChangesToBeUpdated(violationRecords, driverChangesByCDL)

	err = i.Deps.EndorsementRequestWrapper.Update(
		ctx,
		endorsementRequestID,
		func(endReq *endorsementrequest.Request) *endorsementrequest.Request {
			endReq.QuoteGenerationInfo = &endorsementrequest.QuoteGenerationInfo{
				MVRPullDetails: &endorsementrequest.MVRPullDetails{
					Status:         common.MVRPullStatusSuccess,
					LatestPullTime: time.Now(),
				},
			}
			endReq.Changes = append(endReq.Changes, slice_utils.FromSliceOfPointers(changesToBeUpdated)...)
			return endReq
		})
	if err != nil {
		return errors.Wrapf(err, "failed to update mvr for endorsement request %s", endorsementRequestID)
	}
	// Log successful MVR data pull
	log.Info(ctx, fmt.Sprintf("Successfully pulled MVR data for drivers for instant pricing: %v", violationRecords))
	return nil
}

func getUSStateFromIB(ctx context.Context, deps internal.Deps, bundleId uuid.UUID) (us_states.USState, error) {
	ib, err := commonib.GetInsuranceBundleByInternalID(
		ctx, deps.InsuranceBundleManagerClient, bundleId.String())
	if err != nil {
		log.Error(ctx, "Failed to fetch IB bundle for endorsement request", log.Err(err))
		return nil, err
	}

	ibLastSegment := ib.GetLastSegment()
	if ibLastSegment == nil {
		log.Error(ctx, "Failed to fetch last segment IB bundle", log.String("bundleId", ib.InternalId), log.Err(err))
		return nil, err
	}
	usState, err := ibLastSegment.PrimaryInsured.GetUSState()
	if err != nil {
		log.Error(ctx, "Failed to fetch US state for IB", log.Err(err))
		return nil, err
	}
	return usState, nil
}

func (i *Impl) getDriverViolationRecords(ctx context.Context, newDrivers []application.DriverBasicDetails, effectiveDate *time.Time, usState us_states.USState) (*driver.DriverViolationOverrides, error) {
	// Pull MVR data directly without overrides (we only need raw data for pricing)
	var drivers []admitted_app.DriverDetails
	for _, d := range newDrivers {
		drivers = append(drivers, admitted_app.DriverDetails{
			DriverBasicDetails: d,
		})
	}
	mvrViolations, reports, errs := mvr.GetMovingViolationCountsForDrivers(
		ctx,
		drivers,
		*effectiveDate,
		policy_enums.ProgramTypeNonFleetAdmitted,
		usState,
		i.Deps.FetcherClientFactory,
	)

	// Check for any MVR pull errors - since MVR is mandatory, fail if any driver's MVR pull failed
	for _, err := range errs {
		if err != nil {
			log.Error(ctx, "Failed to get MVR violations for drivers", zap.Error(err))
			return nil, errors.Wrapf(err, "error fetching driver violations: %v", err)
		}
	}
	log.Info(ctx, fmt.Sprintf("Successfully fetched MVR violations for drivers: %+v %+v", reports, mvrViolations))

	violationRecords, _, err := driver.GetDriverVioOverridesAndInfo(
		ctx,
		*effectiveDate,
		newDrivers,
		policy_enums.ProgramTypeNonFleetAdmitted,
		driver.DriverViolationDetail{
			Records:    mvrViolations,
			MVRReports: reports,
			Errors:     errs,
		},
	)
	if err != nil {
		log.Error(ctx, "MVR pull failed for new drivers", log.Err(err))
		return nil, errors.Wrapf(err, "failed to pull MVR for drivers for endorsementRequest quote generation")
	}
	return &violationRecords, nil
}

func (i *Impl) getEndorsementChangesToBeUpdated(violationRecords *driver.DriverViolationOverrides, driverChangesByCDL map[string]*dbendorsementapp.Change) []*dbendorsementapp.Change {
	var changesToBeUpdated []*dbendorsementapp.Change
	for _, dv := range *violationRecords.DriverViolations {
		for dl, ch := range driverChangesByCDL {
			if strings.EqualFold(dv.LicenseNumber, dl) {
				data := ch.Data
				driverChangeData := data.GetNonFleetChange().GetDriverChange().Add[0]
				driverChangeData.Violations = &model.NFAdmittedViolationDataV1{
					ViolationPoints: int32(dv.ViolationPoints),
					ClassCounts: map_utils.Transform(
						dv.ClassCounts,
						func(k string, v int64) int32 {
							return int32(v)
						}),
				}
				changesToBeUpdated = append(changesToBeUpdated, ch)
			}
		}
	}
	return changesToBeUpdated
}

func (i *Impl) GenerateQuote(ctx context.Context, endorsementRequestID uuid.UUID, driverChanges map[string]*dbendorsementapp.Change, runType rating.RunType) error {
	err := i.resetQuoteGenerationInfo(ctx, endorsementRequestID)
	if err != nil {
		log.Error(ctx, "error resetting quote generation info", log.String("endReqId", endorsementRequestID.String()), log.Err(err))
		return err
	}

	err = i.PullMVR(ctx, endorsementRequestID, driverChanges)
	if err != nil {
		log.Error(ctx, "Failed to pull MVR for new drivers - quote generation cannot proceed", log.Err(err))
		return err
	}

	jobRunID, err := i.Deps.Jobber.AddJobRun(ctx, jobber.NewAddJobRunParams(
		jobs.EndorsementPricingJob,
		&messages.EndorsementPricingMessage{
			EndorsementRequestID: endorsementRequestID,
			PricingType:          runType,
		},
		jtypes.NewMetadata(jtypes.Immediate),
	))
	if err != nil {
		return fmt.Errorf("failed to add pricing job run: %w", err)
	}
	log.Info(ctx, fmt.Sprintf("Added job run with ID: %v", jobRunID))
	err = i.Deps.EndorsementRequestWrapper.Update(
		ctx,
		endorsementRequestID,
		func(endReq *endorsementrequest.Request) *endorsementrequest.Request {
			info := endReq.QuoteGenerationInfo
			if info == nil {
				info = &endorsementrequest.QuoteGenerationInfo{}
			}
			info.PricingJobInfo = &endorsementrequest.PricingJobInfo{
				JobRunID: jobRunID,
				Status:   endreqenums.PricingJobStatusRunning,
			}
			endReq.QuoteGenerationInfo = info
			return endReq
		})
	if err != nil {
		log.Error(ctx, "failed to add pricing job run", log.Err(err))
		return nil
	}
	log.Info(ctx, "Successfully triggered endorsement pricing job",
		log.Stringer("jobRunID", jobRunID),
		log.String("runType", runType.String()))
	return nil
}

func (i *Impl) resetQuoteGenerationInfo(ctx context.Context, endorsementRequestID uuid.UUID) error {
	err := i.Deps.EndorsementRequestWrapper.Update(
		ctx,
		endorsementRequestID,
		func(endReq *endorsementrequest.Request) *endorsementrequest.Request {
			endReq.QuoteGenerationInfo = nil
			endReq.WrittenPremium = nil
			return endReq
		})
	if err != nil {
		log.Error(ctx, "failed to add pricing job run", log.Err(err))
		return nil
	}
	return nil
}
