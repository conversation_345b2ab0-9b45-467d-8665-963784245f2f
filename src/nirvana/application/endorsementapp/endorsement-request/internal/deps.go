package internal

import (
	"github.com/benbjohnson/clock"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	endorsementrequest "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	endorsement_review "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-review"
	insurancebundle "nirvanatech.com/nirvana/db-api/db_wrappers/insurance-bundle/insurance-bundle"
	legacy_write_gateway "nirvanatech.com/nirvana/endorsement/legacy-write-gateway"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	"nirvanatech.com/nirvana/pdffill"
)

// Deps contains the dependencies of the state machine.
type Deps struct {
	fx.In

	EndorsementRequestWrapper endorsementrequest.Wrapper
	// Approving, declining, or closing an endorsement review requires the ability
	// to approve, decline, or close an endorsement request.
	// Since endorsementRequestManager is used in endorsementReviewManager,
	// a cyclic dependency would occur if we directly used endorsementReviewManager here.
	// To avoid this, we use endorsementReviewWrapper instead of endorsementReviewManager.
	EndorsementReviewWrapper      endorsement_review.Wrapper
	Jobber                        quoting_jobber.Client
	InsuranceBundleManagerClient  service.InsuranceBundleManagerClient
	PricingWrapper                quoting.PricingWrapper
	AuthWrapper                   auth.DataWrapper
	InsuranceBundleManager        insurancebundle.Wrapper
	FeatureFlagClient             feature_flag_lib.Client
	FileUploadManager             file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
	PdffillClient                 pdffill.Client `name:"unipdf"`
	LegacyEndorsementWriteGateway legacy_write_gateway.LegacyWriteGateway
	FetcherClientFactory          data_fetching.FetcherClientFactory
	Clock                         clock.Clock
}
