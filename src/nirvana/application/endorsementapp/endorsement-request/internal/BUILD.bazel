load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "internal",
    srcs = ["deps.go"],
    importpath = "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request/internal",
    visibility = ["//nirvana/application/endorsementapp/endorsement-request:__subpackages__"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/db-api/db_wrappers/application/quoting",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-review",
        "//nirvana/db-api/db_wrappers/insurance-bundle/insurance-bundle",
        "//nirvana/endorsement/legacy-write-gateway",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/insurance-bundle/service",
        "//nirvana/pdffill",
        "@com_github_ben<PERSON><PERSON><PERSON><PERSON>_clock//:clock",
        "@org_uber_go_fx//:fx",
    ],
)
