load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "endorsement-request",
    srcs = [
        "client.go",
        "client_impl.go",
        "fx.go",
        "mock_manager.go",
    ],
    importpath = "nirvanatech.com/nirvana/application/endorsementapp/endorsement-request",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/application/endorsementapp",
        "//nirvana/application/endorsementapp/endorsement-request/internal/client",
        "//nirvana/common-go/proto",
        "//nirvana/db-api/db_wrappers/endorsementapp",
        "//nirvana/db-api/db_wrappers/endorsementapp/endorsement-request",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/rating",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)
