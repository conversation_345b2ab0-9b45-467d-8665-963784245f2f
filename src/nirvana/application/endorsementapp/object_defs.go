package endorsementapp

import (
	"github.com/shopspring/decimal"
	appEnums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	commonoapi "nirvanatech.com/nirvana/openapi-specs/components/common"
	endorsementapp_intake "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"
)

type PriceSnapshot struct {
	TotalPremium      *decimal.Decimal
	PremiumByCoverage map[appEnums.Coverage]decimal.Decimal
	StateSurcharge    *decimal.Decimal
	FlatCharge        *decimal.Decimal
}

type EndorsementWrittenPremium struct {
	// EndorsementFees is the total fees for the endorsement.
	EndorsementFees *decimal.Decimal
	// EndorsementFeeBreakdownByCoverage is the fees for the endorsement broken down by coverage.
	FeeBreakdownByCoverage map[appEnums.Coverage]decimal.Decimal
	// EndorsementStateSurchargeFees is the state surcharge for the endorsement.
	EndorsementStateSurchargeFees *decimal.Decimal
	// EndorsementFlatChargeFees is the flat charge for the endorsement.
	EndorsementFlatChargeFees *decimal.Decimal
	// Before & After PriceSnapshot are the price snapshots before and after the endorsement.
	Before, After PriceSnapshot
}

func (writtenPremium *EndorsementWrittenPremium) ToOAPIEndorsementPrice() *endorsementapp_intake.EndorsementPrice {
	if writtenPremium == nil {
		return nil
	}
	flatChargesFees := 0.0
	if writtenPremium.EndorsementFlatChargeFees != nil {
		flatChargesFees, _ = writtenPremium.EndorsementFlatChargeFees.Float64()
	}
	stateSurchargeFees := 0.0
	if writtenPremium.EndorsementStateSurchargeFees != nil {
		stateSurchargeFees, _ = writtenPremium.EndorsementStateSurchargeFees.Float64()
	}

	endorsementFees := 0.0
	if writtenPremium.EndorsementFees != nil {
		endorsementFees, _ = writtenPremium.EndorsementFees.Float64()
	}

	var writtenPremiumByCoverage []endorsementapp_intake.WrittenPremiumByCoverage
	for coverage, premium := range writtenPremium.FeeBreakdownByCoverage {
		covType := commonoapi.CoverageType(coverage.String())
		writtenPremiumForCoverage, _ := premium.Float64()
		writtenPremiumByCoverage = append(writtenPremiumByCoverage, endorsementapp_intake.WrittenPremiumByCoverage{
			Coverage:       covType,
			WrittenPremium: writtenPremiumForCoverage,
		})
	}
	return &endorsementapp_intake.EndorsementPrice{
		FlatCharge:               &flatChargesFees,
		StateSurcharge:           &stateSurchargeFees,
		WrittenPremium:           endorsementFees,
		WrittenPremiumByCoverage: writtenPremiumByCoverage,
	}
}
