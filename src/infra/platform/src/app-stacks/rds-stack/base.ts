import { Token } from 'cdktf'
import { Construct } from 'constructs'

import {
  secretsmanagerSecret,
  secretsmanagerSecretVersion,
} from '@cdktf/provider-aws'
import { DataAwsSecretsmanagerSecret } from '@cdktf/provider-aws/lib/data-aws-secretsmanager-secret'
import { DataAwsSecretsmanagerSecretVersion } from '@cdktf/provider-aws/lib/data-aws-secretsmanager-secret-version'
import { DbInstance } from '@cdktf/provider-aws/lib/db-instance'
import { PasswordConfig } from '@cdktf/provider-random/lib/password'
import { Password as RandomPassword } from '@cdktf/provider-random/lib/password'

import {
  DatabaseCredentials,
  DatabaseInfo,
  Network,
  newDatabaseInfo,
} from '@nvinfra/common/interfaces'
import { provisionRandomPasswordAsSecret } from '@nvinfra/common/lib'

export interface BaseDBProps {
  network: Network

  identifier: string

  instanceClass: string

  // Complete engine version (including minor version). Eg: 12.17
  engineVersion: string

  storage: {
    allocated: number
    maxAllocated: number
    iops?: number
  }

  /**
   * Configuration options for DB operations
   */
  opsProps: {
    // Feature flags
    multiAz?: boolean
    performanceInsightsEnabled?: boolean

    // Maintenance
    backupRetentionPeriod?: number
    skipFinalSnapshot: boolean
    applyImmediately: boolean
    autoMinorVersionUpgrade: boolean
    deletionProtection: boolean
    maintenanceWindow: string
  }
}

export abstract class BaseRDSConstruct extends Construct {
  readonly network: Network

  constructor(
    scope: Construct,
    id: string,
    props: Pick<BaseDBProps, 'network'>,
  ) {
    super(scope, id)
    this.network = props.network
  }

  protected _provisionRandomMasterPassword(
    instanceName: string,
    config: PasswordConfig,
  ) {
    const password = new RandomPassword(this, `Password`, config)
    const passwordSecret = new secretsmanagerSecret.SecretsmanagerSecret(
      this,
      `Secret`,
      {
        name: instanceName,
      },
    )
    const passwordSecretVersion =
      new secretsmanagerSecretVersion.SecretsmanagerSecretVersion(
        this,
        `SecretValue`,
        {
          secretId: Token.asString(passwordSecret.id),
          secretString: Token.asString(password.result),
        },
      )
    return {
      arn: Token.asString(passwordSecretVersion.arn),
      value: Token.asString(passwordSecretVersion.secretString),
    }
  }

  protected _provisionRandomPassword(
    config: PasswordConfig,
    secretName: string,
  ) {
    /**
     * RDS Strategy for Secret Provisioning
     *
     * This strategy implements the RDS naming conventions:
     *
     * **Secret Naming:**
     * - Uses the secret name directly without any prefix
     * - Example: secretName "my-db-password" → AWS Secret name "my-db-password"
     *
     * **Construct ID Naming:**
     * - Converts the base construct type to PascalCase, then appends the secret identifier with underscore
     * - PascalCase conversion splits by underscores, capitalizes first letter of each segment,
     *   lowercases remaining letters, then joins segments together
     * - Final format: {PascalCaseBase}_{secretName}
     *
     * **Examples:**
     * | Base          | Secret Name     | Construct ID           |
     * |---------------|-----------------|------------------------|
     * | password      | my-db-secret    | Password_my-db-secret  |
     * | secret        | my-db-secret    | Secret_my-db-secret    |
     * | secret_value  | my-db-secret    | SecretValue_my-db-secret |
     */
    const strategy = {
      formatSecretName: (name: string) => name,
      formatConstructId: (base: string, id: string) => {
        const pascalBase = base
          .split('_')
          .map(
            (word) =>
              word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(),
          )
          .join('')
        return `${pascalBase}_${id}`
      },
    }
    return provisionRandomPasswordAsSecret(this, {
      secretName,
      config,
      strategy,
    })
  }

  /**
   * Loads the legacy password from the given secret name.
   * Because these resources were attached to the parent
   * construct, this method also requires the parent as a
   * parameter.
   */
  protected _loadLegacyPassword(
    parent: Construct,
    secretName: string,
    secretConstructId: string,
    secretVersionConstructId: string,
  ) {
    const passwordSecret = new DataAwsSecretsmanagerSecret(
      parent,
      secretConstructId,
      {
        name: secretName,
      },
    )
    const passwordSecretVersion = new DataAwsSecretsmanagerSecretVersion(
      parent,
      secretVersionConstructId,
      {
        secretId: passwordSecret.id,
      },
    )
    return {
      arn: Token.asString(passwordSecretVersion.arn),
      value: Token.asString(passwordSecretVersion.secretString),
    }
  }
}

/**
 * A helper function for DB constructs to implement the `Info` method
 * to return the database information.
 */
export const getDatabaseInfo = (obj: {
  instance: DbInstance
  network: Network
  masterPasswordARN: string
  readonlyReplica?: {
    instance: DbInstance
    securityGroup: string
  }
  additionalUserCredentials?: DatabaseCredentials
}): DatabaseInfo => {
  return newDatabaseInfo({
    id: Token.asString(obj.instance.id),
    name: Token.asString(obj.instance.dbName),
    arn: Token.asString(obj.instance.arn),
    host: Token.asString(obj.instance.address),
    port: Token.asNumber(obj.instance.port),
    dbName: Token.asString(obj.instance.dbName),
    masterUserDetails: {
      username: Token.asString(obj.instance.username),
      password: {
        arn: Token.asString(obj.masterPasswordARN),
        value: Token.asString(obj.instance.password),
      },
    },
    credentials: obj.additionalUserCredentials,
    securityGroupId: obj.network.SecurityGroups().default.id,
    readonlyReplica:
      obj.readonlyReplica != null
        ? {
            host: Token.asString(obj.readonlyReplica.instance.address),
            securityGroupId: Token.asString(obj.readonlyReplica.securityGroup),
          }
        : undefined,
  })
}
