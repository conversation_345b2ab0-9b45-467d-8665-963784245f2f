import { Token } from 'cdktf'
import { Construct } from 'constructs'

import {
  dbInstance,
  dbParameterGroup,
  dbSubnetGroup,
} from '@cdktf/provider-aws/'
import { BackupPlan, BackupPlanRule } from '@cdktf/provider-aws/lib/backup-plan'
import { BackupSelection } from '@cdktf/provider-aws/lib/backup-selection'
import { BackupVault } from '@cdktf/provider-aws/lib/backup-vault'
import { DbInstance } from '@cdktf/provider-aws/lib/db-instance'
import { IamRole } from '@cdktf/provider-aws/lib/iam-role'
import { PostgresqlProvider } from '@cdktf/provider-postgresql/lib/provider'

import { DatabaseCredentials, DatabaseInfo } from '@nvinfra/common/interfaces'
import {
  createRole,
  MemoryUnit,
  MemoryUnitHelper,
  PostgresRoleConfigs,
} from '@nvinfra/common/lib'

import { RDS<PERSON>lerts, RDSAlertsProps } from './alerts/rds-alerts'
import { BaseDBProps, BaseRDSConstruct, getDatabaseInfo } from './base'

const defaultApplicationDBConfig = {
  storageType: 'gp3',
  username: 'postgres',
  dbName: 'postgres',
  engine: 'postgres',
  port: 5432,
  masterPasswordCharacteristics: {
    length: 40,
    special: true,
    minSpecial: 5,
    overrideSpecial: '!#$%^&*()-_=+[]{}<>:?',
    keepers: {
      pass_version: '1',
    },
  },
}

interface RDSAlertProps {
  snsTopic: string
  config: {
    cpuUtilisation: number
    freeableMemoryGB: number
    freeStorageGB: number
    connections: number
    diskQueueDepth: number
    totalIOPS: {
      warningThreshold: number
      criticalThreshold: number
    }
  }
}

export enum AppDBRoles {
  Migrator = 'migrator',

  // Roles created through migrations outside terraform
  Readonly = 'readonly',
  Readwrite = 'readwrite',
  DsUser = 'ds_user',
}

export interface ApplicationDBProps extends BaseDBProps {
  parameterGroupfamily: string

  // Read-only instance is optional.
  readonlyReplica?: {
    identifier: string
    instanceClass: string
  }

  // Backup configuration
  backup: {
    vault: BackupVault
    role: IamRole
    plan: {
      name: string
      rules: Pick<
        BackupPlanRule,
        'enableContinuousBackup' | 'lifecycle' | 'ruleName' | 'schedule'
      >[]
    }
    selectionName: string
  }

  legacyOnlyPropsForBackwardsCompatibility?: {
    // The AWS Secrets Manager key for the password. In all other
    // environments, we'll generate a random password within this stack.
    passwordSecretName: string

    // Name of the DB subnet group. By default, we'll use the identifier
    // of the main instance.
    subnetGroupName: string
  }

  // Alerts are only supported for the production environment
  // right now. That should change once we have a way to provide
  // a custom SNS topic here.
  alerts?: RDSAlertProps
}

export class ApplicationDB extends BaseRDSConstruct {
  readonly instance: dbInstance.DbInstance
  readonly readonlyReplica?: {
    instance: dbInstance.DbInstance
    securityGroup: string
  }
  readonly masterPasswordARN: string
  readonly additionalUserCredentials: DatabaseCredentials = {}

  constructor(scope: Construct, props: ApplicationDBProps) {
    super(scope, 'ApplicationDB', props)
    const subnetGroup = new dbSubnetGroup.DbSubnetGroup(this, 'SubnetGroup', {
      name:
        props.legacyOnlyPropsForBackwardsCompatibility?.subnetGroupName ??
        props.identifier,
      subnetIds: props.network.PrivateSubnets(),
    })

    // parameter group for postgres 16
    const parameterGroup = new dbParameterGroup.DbParameterGroup(
      this,
      `ParameterGroup-${props.parameterGroupfamily}-${props.identifier}`,
      {
        name: `${props.identifier}-${props.parameterGroupfamily}`,
        family: props.parameterGroupfamily,
        // We log everything but roll over after 24 hours
        // TODO: This behaviour is no longer desired, remove this.
        parameter: [
          {
            name: 'log_min_duration_statement',
            value: '100',
          },
          {
            name: 'rds.log_retention_period',
            value: '10080',
          },
          {
            name: 'log_statement',
            value: 'ddl',
          },
          {
            name: 'rds.force_ssl',
            // default is 1, we do not want to force ssl for now since it will require changes on the application side
            value: '0',
          },
        ],
      },
    )

    const { arn: dbPasswordARN, value: dbPasswordString } =
      props.legacyOnlyPropsForBackwardsCompatibility
        ? this._loadLegacyPassword(
            scope, // For legacy reasons, we need to provide the parent (RDSStack) here.
            props.legacyOnlyPropsForBackwardsCompatibility.passwordSecretName,
            'LegacyAppDBPasswordSecret',
            'LegacyAppDBPasswordSecretValue',
          )
        : this._provisionRandomMasterPassword(
            props.identifier,
            defaultApplicationDBConfig.masterPasswordCharacteristics,
          )
    this.masterPasswordARN = dbPasswordARN

    this.instance = new DbInstance(this, 'ApplicationDB', {
      // if an identifier is not specified, terraform assigns a random name
      identifier: `${props.identifier}`,
      allocatedStorage: props.storage.allocated,
      maxAllocatedStorage: props.storage.maxAllocated,
      dbSubnetGroupName: subnetGroup.name,
      parameterGroupName: parameterGroup.name,
      storageType: defaultApplicationDBConfig.storageType,
      engine: defaultApplicationDBConfig.engine,
      engineVersion: props.engineVersion,
      instanceClass: props.instanceClass,
      // FIXME: This should be set as new security group for non-prod envs
      // vpcSecurityGroupIds: [this._securityGroup.id],
      dbName: defaultApplicationDBConfig.dbName,
      username: defaultApplicationDBConfig.username,
      password: Token.asString(dbPasswordString),
      port: defaultApplicationDBConfig.port,
      enabledCloudwatchLogsExports: ['postgresql'],
      ...props.opsProps,
    })

    this.configureRoles()

    if (props.alerts != null) {
      this.setupAlerts(props.alerts)
    }

    if (props.readonlyReplica != null) {
      this.readonlyReplica = {
        instance: new dbInstance.DbInstance(
          this,
          'ApplicationDBReadonlyReplica',
          {
            identifier: `${props.readonlyReplica.identifier}`,
            instanceClass: props.readonlyReplica.instanceClass,
            allocatedStorage: props.storage.allocated,
            maxAllocatedStorage: props.storage.maxAllocated,
            storageType: defaultApplicationDBConfig.storageType,
            replicateSourceDb: this.instance.identifier,
            // FIXME: See comment on rds instance for this
            // vpcSecurityGroupIds: [securityGroup.id],
            parameterGroupName: this.instance.parameterGroupName,
            skipFinalSnapshot: true,
          },
        ),
        securityGroup: this.network.SecurityGroups().default.id,
      }

      const backupPlan = new BackupPlan(this, 'plan', {
        name: props.backup.plan.name,
        rule: props.backup.plan.rules.map((rule) => {
          return {
            ...rule,
            targetVaultName: props.backup.vault.name,
          }
        }),
      })
      new BackupSelection(this, 'selection', {
        iamRoleArn: props.backup.role.arn,
        name: props.backup.selectionName,
        planId: backupPlan.id,
        resources: [this.instance.arn],
      })
    }
  }

  Info = (): DatabaseInfo => {
    return getDatabaseInfo(this)
  }

  private setupAlerts(alertProps: RDSAlertProps) {
    const alertsProps: RDSAlertsProps = {
      snsTopicName: alertProps.snsTopic,
      config: {
        cpu: { thresholdPercent: alertProps.config.cpuUtilisation },
        freeableMemory: {
          minThresholdBytes: MemoryUnitHelper.toBytes(
            alertProps.config.freeableMemoryGB,
            MemoryUnit.GB,
          ),
        },
        freeStorage: {
          minThresholdBytes: MemoryUnitHelper.toBytes(
            alertProps.config.freeStorageGB,
            MemoryUnit.GB,
          ),
        },
        connections: { maxConnections: alertProps.config.connections },
        diskQueueDepth: { maxDepth: alertProps.config.diskQueueDepth },
        totalIOPS: [
          {
            maxIOPS: alertProps.config.totalIOPS.warningThreshold,
            uniqueID: 'total_iops_warning_alert',
          },
          {
            maxIOPS: alertProps.config.totalIOPS.criticalThreshold,
            uniqueID: 'total_iops_critical_alert',
          },
        ],
      },
    }

    new RDSAlerts(this, this.instance, alertsProps)
  }

  private configureRoles() {
    const pgProvider = new PostgresqlProvider(this, `pg-provider`, {
      host: this.instance.address,
      port: this.instance.port,
      username: this.instance.username,
      password: this.instance.password,
      database: this.instance.dbName,
      sslmode: 'disable',
      superuser: false,
    })

    // Create migrator role
    const migratorRolePassword = this._provisionRandomPassword(
      defaultApplicationDBConfig.masterPasswordCharacteristics,
      'app-db-migrator-password',
    )
    const migratorRole = createRole(
      this,
      pgProvider,
      PostgresRoleConfigs.migrator(
        this.instance.dbName,
        migratorRolePassword.value,
      ),
    )
    this.additionalUserCredentials[AppDBRoles.Migrator] = {
      username: migratorRole.name,
      password: {
        arn: migratorRolePassword.arn,
        value: migratorRolePassword.value,
      },
    }

    // create passwords for readonly and readwrite roles, these roles are created in migrations
    const readonlyRolePassword = this._provisionRandomPassword(
      defaultApplicationDBConfig.masterPasswordCharacteristics,
      'app-db-readonly-password',
    )
    this.additionalUserCredentials[AppDBRoles.Readonly] = {
      username: AppDBRoles.Readonly,
      password: {
        arn: readonlyRolePassword.arn,
        value: readonlyRolePassword.value,
      },
    }

    const readwriteRolePassword = this._provisionRandomPassword(
      defaultApplicationDBConfig.masterPasswordCharacteristics,
      'app-db-readwrite-password',
    )
    this.additionalUserCredentials[AppDBRoles.Readwrite] = {
      username: AppDBRoles.Readwrite,
      password: {
        arn: readwriteRolePassword.arn,
        value: readwriteRolePassword.value,
      },
    }

    // create password for ds_user. This role is used by airbyte for reverse etl pipeline.
    const dsUserRoleName = 'ds_user' // created through migrations outside terraform
    const dsUserRolePassword = this._provisionRandomPassword(
      defaultApplicationDBConfig.masterPasswordCharacteristics,
      'app-db-ds-user-password',
    )
    this.additionalUserCredentials[dsUserRoleName] = {
      username: dsUserRoleName,
      password: {
        arn: dsUserRolePassword.arn,
        value: dsUserRolePassword.value,
      },
    }
  }
}
