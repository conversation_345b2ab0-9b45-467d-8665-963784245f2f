import { Construct } from 'constructs'
import { Statement } from 'iam-floyd'

import { dbInstance, dbOptionGroup, dbSubnetGroup } from '@cdktf/provider-aws'
import { DataAwsIamPolicyDocument } from '@cdktf/provider-aws/lib/data-aws-iam-policy-document'

import { Role } from '@nvinfra/common/constructs'
import { DatabaseInfo } from '@nvinfra/common/interfaces'
import { MemoryUnit, MemoryUnitHelper } from '@nvinfra/common/lib'

import { RDSAlerts, RDSAlertsProps } from './alerts/rds-alerts'
import { BaseDBProps, BaseRDSConstruct, getDatabaseInfo } from './base'

const defaultNhtsaDBConfig = {
  port: 1433,
  username: 'sa',
  storageType: 'gp2',
  engine: 'sqlserver-ex',
  masterPasswordCharacteristics: {
    length: 40,
    special: true,
    minLower: 2,
    minUpper: 2,
    minNumeric: 2,
    minSpecial: 2,
    overrideSpecial: '!#$%^&*()-_=+[]{}<>:?',
    keepers: {
      pass_version: '1',
    },
  },
}

interface RDSAlertProps {
  snsTopic: string
  config: {
    cpuUtilisation: number
    freeableMemoryGB: number
    freeStorageGB: number
    connections: number
    diskQueueDepth: number
    totalIOPS: number
  }
}

export interface NhtsaDBProps extends BaseDBProps {
  /**
   * The NHTSA DB is a SQL Server RDS instance created from a SQL Server backup file
   * which is periodically downloaded from https://vpic.nhtsa.dot.gov/api/, and stored
   * in an S3 bucket. The RDS instance is restored from this backup file.
   */
  source: {
    // Name of the cloud bucket where the sql server backup files are stored
    bucketName: string

    // Path to the sql server backup files in the bucket, can include wildcards
    // Eg: private/nhtsa-db-backup/*
    path: string
  }

  rdsAssumeRolePolicy: DataAwsIamPolicyDocument

  majorEngineVersion: string

  alerts?: RDSAlertProps
}

/**
 * NhtsaDB construct allows for the creation of a RDS instance of sql server and this instance
 * is expected to be restored monthly by support-eng from a sql server backup S3 file
 * that is provided by nhtsa. As the rds instance does not support backup/restore by default, here we
 * use an option group to add this functionality to the sql server being instantiated.
 * The option group assumes a role that contains two different policies which have
 * the read/write and list permissions on the S3 bucket.
 *
 * The entities involved in the creation of nhtsa db are listed below and to keep things
 * simple all of them hold the same "name" identifier:
 * 1: subnetGroup
 * 2: securityGroup
 * 3. role (for reading and listing from S3)
 * 4. secret (nhtsa db password)
 * 5. db instance (sql rds instance)
 */
export class NhtsaDB extends BaseRDSConstruct {
  readonly instance: dbInstance.DbInstance
  readonly masterPasswordARN: string

  constructor(scope: Construct, props: NhtsaDBProps) {
    super(scope, 'NhtsaDB', props)

    const subnetGroup = new dbSubnetGroup.DbSubnetGroup(this, 'SubnetGroup', {
      name: props.identifier,
      subnetIds: this.network.PrivateSubnets(),
    })

    // Create option group to allow restore from S3
    const optionGroup = new dbOptionGroup.DbOptionGroup(this, 'OptionGroup', {
      name: props.identifier,
      engineName: defaultNhtsaDBConfig.engine,
      majorEngineVersion: props.majorEngineVersion,
      option: [
        {
          optionName: 'SQLSERVER_BACKUP_RESTORE',
          optionSettings: [
            {
              name: 'IAM_ROLE_ARN',
              value: this._generateRoleArn(props.identifier, props),
            },
          ],
        },
      ],
    })

    const { arn: dbPasswordARN, value: dbPasswordString } =
      this._provisionRandomMasterPassword(
        props.identifier,
        defaultNhtsaDBConfig.masterPasswordCharacteristics,
      )
    this.masterPasswordARN = dbPasswordARN

    // Create sql server instance
    this.instance = new dbInstance.DbInstance(this, 'NhtsaDB', {
      identifier: props.identifier,

      engine: defaultNhtsaDBConfig.engine,
      engineVersion: props.engineVersion,
      licenseModel: 'license-included',

      instanceClass: props.instanceClass,
      storageType: defaultNhtsaDBConfig.storageType,
      allocatedStorage: props.storage.allocated,
      maxAllocatedStorage: props.storage.maxAllocated,
      dbSubnetGroupName: subnetGroup.name,

      vpcSecurityGroupIds: [this.network.SecurityGroups().default.id],

      username: defaultNhtsaDBConfig.username,
      password: dbPasswordString,
      port: defaultNhtsaDBConfig.port,

      optionGroupName: optionGroup.name,
      enabledCloudwatchLogsExports: ['error'],

      ...props.opsProps,
    })

    if (props.alerts) {
      this.setupAlerts(props.alerts)
    }
  }

  Info = (): DatabaseInfo => {
    return getDatabaseInfo(this)
  }

  private _generateRoleArn(instanceName: string, props: NhtsaDBProps): string {
    // Create policy to allow listing cloud s3 bucket
    const s3ListBucketPolicy = new Statement.S3()
      .allow()
      .toListBucket()
      .toGetBucketLocation()
      .onBucket(props.source.bucketName)

    // Create policy to allow read/write of sql server backup files in s3
    const s3ReadWriteObjectsPolicy = new Statement.S3()
      .allow()
      .toGetObject()
      .toPutObject()
      .toListMultipartUploadParts()
      .toAbortMultipartUpload()
      .onObject(props.source.bucketName, props.source.path)

    // Create a role with the above policies
    return new Role(this, {
      name: instanceName,
      assumeRolePolicy: props.rdsAssumeRolePolicy,
      statements: [s3ListBucketPolicy, s3ReadWriteObjectsPolicy],
    }).arn
  }

  private setupAlerts(alertProps: RDSAlertProps) {
    const alertsProps: RDSAlertsProps = {
      snsTopicName: alertProps.snsTopic,
      config: {
        cpu: { thresholdPercent: alertProps.config.cpuUtilisation },
        freeableMemory: {
          minThresholdBytes: MemoryUnitHelper.toBytes(
            alertProps.config.freeableMemoryGB,
            MemoryUnit.GB,
          ),
        },
        freeStorage: {
          minThresholdBytes: MemoryUnitHelper.toBytes(
            alertProps.config.freeStorageGB,
            MemoryUnit.GB,
          ),
        },
        connections: { maxConnections: alertProps.config.connections },
        diskQueueDepth: { maxDepth: alertProps.config.diskQueueDepth },
        totalIOPS: { maxIOPS: alertProps.config.totalIOPS },
      },
    }

    new RDSAlerts(this, this.instance, alertsProps)
  }
}
