import { Token } from 'cdktf'
import { Construct } from 'constructs'

import { DbSubnetGroup } from '@cdktf/provider-aws/lib/db-subnet-group'
import { RdsCluster } from '@cdktf/provider-aws/lib/rds-cluster'
import { RdsClusterInstance } from '@cdktf/provider-aws/lib/rds-cluster-instance'

import {
  DatabaseInfo,
  Network,
  newDatabaseInfo,
} from '@nvinfra/common/interfaces'

import { BaseRDSConstruct } from './base'

const defaultFmcsaDBConfig = {
  username: 'postgres',
  dbName: 'postgres',
  engine: 'aurora-postgresql',
  engineMode: 'provisioned',
  masterPasswordCharacteristics: {
    length: 40,
    special: true,
    minSpecial: 5,
    overrideSpecial: '!#$%^&*()-_=+[]{}<>:?',
    keepers: {
      pass_version: '1',
    },
  },
  tags: {
    ManagedBy: 'InsuredEng',
  },
}

export interface FmcsaDBProps {
  network: Network

  clusterIdentifier: string

  // Complete engine version (including minor version). Eg: 16.8
  engineVersion: string
  parameterGroupName: string

  numberOfReadReplicas: number

  // Database capacity is measured in Aurora Capacity Units (ACUs). 1 ACU provides 2 GiB of memory and corresponding compute and networking.
  capacitySettings: {
    minCapacity: number
    maxCapacity: number
  }

  legacyOnlyPropsForBackwardsCompatibility?: {
    // The AWS Secrets Manager key for the password. In all other
    // environments, we'll generate a random password within this stack.
    passwordSecretName?: string

    // Name of the DB subnet group. By default, we'll use the identifier
    // of the main instance.
    subnetGroupName?: string

    // Name of the DB instances, one for read-write instance and one for each read replica.
    // If not provided, we'll use generic identifiers like fmcsa-db-instance-1, fmcsa-db-instance-2, etc.
    dbInstanceIdentifiers?: string[]
  }
}

export class FmcsaDb extends BaseRDSConstruct {
  private readonly cluster: RdsCluster
  private readonly masterPasswordARN: string

  constructor(scope: Construct, props: FmcsaDBProps) {
    super(scope, 'FmcsaDB', props)
    const { arn: dbPasswordARN, value: dbPasswordString } = props
      .legacyOnlyPropsForBackwardsCompatibility?.passwordSecretName
      ? this._loadLegacyPassword(
          this,
          props.legacyOnlyPropsForBackwardsCompatibility.passwordSecretName,
          'FmcsaDBPasswordSecret',
          'FmcsaDBPasswordSecretVersion',
        )
      : this._provisionRandomMasterPassword(
          props.clusterIdentifier,
          defaultFmcsaDBConfig.masterPasswordCharacteristics,
        )
    this.masterPasswordARN = dbPasswordARN

    const subnetGroup = new DbSubnetGroup(this, 'SubnetGroup', {
      name:
        props.legacyOnlyPropsForBackwardsCompatibility?.subnetGroupName ??
        props.clusterIdentifier,
      subnetIds: props.network.PrivateSubnets(),
      tags: {
        Name: props.clusterIdentifier,
      },
      description: 'Subnet group for FMCSA aurora DB',
    })

    this.cluster = new RdsCluster(this, 'FmcsaDBCluster', {
      clusterIdentifier: props.clusterIdentifier,
      engine: defaultFmcsaDBConfig.engine,
      engineMode: defaultFmcsaDBConfig.engineMode,
      engineVersion: props.engineVersion,
      databaseName: defaultFmcsaDBConfig.dbName,
      dbClusterParameterGroupName: props.parameterGroupName,
      dbSubnetGroupName: subnetGroup.name,
      masterUsername: defaultFmcsaDBConfig.username,
      masterPassword: Token.asString(dbPasswordString),
      skipFinalSnapshot: false,
      tags: defaultFmcsaDBConfig.tags,
      serverlessv2ScalingConfiguration: {
        minCapacity: props.capacitySettings.minCapacity,
        maxCapacity: props.capacitySettings.maxCapacity,
      },
      vpcSecurityGroupIds: [props.network.SecurityGroups().default.id],
    })

    const totalDBInstances = props.numberOfReadReplicas + 1 // +1 for the read-write instance

    if (props.legacyOnlyPropsForBackwardsCompatibility?.dbInstanceIdentifiers) {
      if (
        props.legacyOnlyPropsForBackwardsCompatibility.dbInstanceIdentifiers
          .length !== totalDBInstances
      ) {
        throw new Error(
          'Number of DB instance names must be equal to number of read replicas + 1, +1 for the read-write instance',
        )
      }
    }

    const dbInstanceIdentifiers =
      props.legacyOnlyPropsForBackwardsCompatibility?.dbInstanceIdentifiers ??
      Array.from(
        { length: totalDBInstances },
        (_, i) => `fmcsa-db-instance-${i + 1}`,
      )

    for (let idx = 0; idx < totalDBInstances; idx++) {
      const instanceID = `FmcsaDBInstance${idx + 1}`
      new RdsClusterInstance(this, instanceID, {
        identifier: dbInstanceIdentifiers[idx],
        clusterIdentifier: this.cluster.clusterIdentifier,
        engine: 'aurora-postgresql',
        instanceClass: 'db.serverless',
        tags: defaultFmcsaDBConfig.tags,
        promotionTier: 1,
      })
    }
  }

  Info = (): DatabaseInfo => {
    return newDatabaseInfo({
      id: this.cluster.clusterIdentifier,
      name: this.cluster.databaseName,
      arn: this.cluster.arn,
      host: this.cluster.endpoint,
      port: this.cluster.port,
      dbName: this.cluster.databaseName,
      masterUserDetails: {
        username: this.cluster.masterUsername,
        password: {
          arn: this.masterPasswordARN,
          value: Token.asString(this.cluster.masterPassword),
        },
      },
      securityGroupId: this.network.SecurityGroups().default.id,
      readonlyReplica: {
        host: this.cluster.readerEndpoint,
        securityGroupId: this.network.SecurityGroups().default.id,
      },
    })
  }
}
