import { Construct } from 'constructs'

import { BackupVault } from '@cdktf/provider-aws/lib/backup-vault'
import { DataAwsIamPolicyDocument } from '@cdktf/provider-aws/lib/data-aws-iam-policy-document'
import { IamRole } from '@cdktf/provider-aws/lib/iam-role'
import { IamRolePolicyAttachment } from '@cdktf/provider-aws/lib/iam-role-policy-attachment'
import { LambdaFunction } from '@cdktf/provider-aws/lib/lambda-function'
import { RandomProvider } from '@cdktf/provider-random/lib/provider'

import { BaseStack, BaseStackProps } from '@nvinfra/common/constructs/stacks'
import { assumeRolePolicyStatement } from '@nvinfra/common/deps'
import { Network } from '@nvinfra/common/interfaces'

import { ApplicationDB, ApplicationDBProps } from './app'
import { FmcsaDb, FmcsaDBProps } from './fmcsa'
import { NhtsaDB, NhtsaDBProps } from './nhtsa'

export interface RDSStackProps extends BaseStackProps {
  /**
   * The network where we'll create the databases (and other resources).
   */
  network: Network

  /**
   * The ECR repositories needed for RDS stack. Note that we
   * only need the `repositoryUrl` for a repository here.
   */
  ecrRepositories: {
    DbMigrateLambda: {
      repositoryUrl: string
    }
  }

  /**
   * Configuration options for the application DB.
   * We omit network and backup from ApplicationDBProps because:
   * - network is captured above.
   * - backup is captured separately in the backup prop below.
   */
  app: Omit<ApplicationDBProps, 'network' | 'backup'>

  /**
   * Configuration options for the NHTSA DB.
   * We omit network and rdsAssumeRolePolicy from NhtsaDBProps because:
   * - network is captured above.
   * - rdsAssumeRolePolicy is supplied by the RDSStack itself.
   */
  nhtsa: Omit<NhtsaDBProps, 'network' | 'rdsAssumeRolePolicy'>

  /**
   * Configuration options for the FMCS DB.
   * We omit network from FmcsaDBProps because:
   * - network is captured above.
   */
  fmcsa: Omit<FmcsaDBProps, 'network'>

  /**
   * Configuration options for the DB migration lambda.
   * This is optional right now because we haven't imported these resources
   * for the production environment into CDKTF yet.
   */
  dbMigrationLambda?: {
    /**
     * The name of the lambda function to be created.
     */
    name: string

    /**
     * The tag of the Docker image to use for the lambda.
     *
     * @todo Get this from a variable, or a data resource.
     *
     * Note that this is only really needed for the first deployment.
     * Redeployments happen through the run migrations github workflow.
     * See .github/workflows/run_migrations.yml.
     */
    imageTag: string
  }

  backup: {
    vaultName: string
    roleName: string
    // Because vault & role are created in the RDSStack and supplied to the ApplicationDB,
    // we omit them from the ApplicationDBProps backup prop.
    appDb: Omit<ApplicationDBProps['backup'], 'vault' | 'role'>
  }
}

export class RDSStack extends BaseStack {
  public applicationDB: ApplicationDB
  public nhtsaDB: NhtsaDB
  public fmcsaDB: FmcsaDb
  public dbMigrationLambda?: LambdaFunction // TODO: make this required

  constructor(scope: Construct, props: RDSStackProps) {
    super(scope, 'rds-stack', props)

    // This provider is required for random password generation
    new RandomProvider(this, 'random')

    const backupVault = new BackupVault(this, 'backup_vault', {
      name: props.backup.vaultName,
    })

    const backupRole = new IamRole(this, 'backup_role', {
      name: props.backup.roleName,
      assumeRolePolicy: new DataAwsIamPolicyDocument(this, 'backup_policy', {
        statement: [assumeRolePolicyStatement('backup')],
      }).json,
    })
    new IamRolePolicyAttachment(this, 'backup_role_policy_attachment', {
      policyArn:
        'arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup',
      role: backupRole.name,
    })

    // Needed by NHTSA DBs for S3 access
    const rdsAssumeRolePolicy = new DataAwsIamPolicyDocument(
      this,
      'rds_assume_role_policy',
      { statement: [assumeRolePolicyStatement('rds')] },
    )

    this.applicationDB = new ApplicationDB(this, {
      ...props.app,
      network: props.network,
      backup: {
        vault: backupVault,
        role: backupRole,
        plan: props.backup.appDb.plan,
        selectionName: props.backup.appDb.selectionName,
      },
    })

    this.nhtsaDB = new NhtsaDB(this, {
      ...props.nhtsa,
      network: props.network,
      rdsAssumeRolePolicy: rdsAssumeRolePolicy,
    })

    this.fmcsaDB = new FmcsaDb(this, {
      ...props.fmcsa,
      network: props.network,
    })

    if (props.dbMigrationLambda) {
      this.dbMigrationLambda = this._createMigrationLambda({
        name: props.dbMigrationLambda.name,
        network: props.network,
        imageUri: `${props.ecrRepositories.DbMigrateLambda.repositoryUrl}:${props.dbMigrationLambda.imageTag}`,
      })
    }
  }

  /**
   * Creates the Lambda function and required IAM role to run DB
   * migrations. This is only needed for the first deployment.
   * Redeployments happen through the run migrations github workflow.
   * See .github/workflows/run_migrations.yml.
   *
   * This function is a port of the terraform code in
   * deployment/app/migration_lambda.tf.
   */
  private _createMigrationLambda(props: {
    name: string
    network: Network
    imageUri: string
  }): LambdaFunction {
    const role = new IamRole(this, 'migration_lambda_role', {
      name: props.name,
      assumeRolePolicy: new DataAwsIamPolicyDocument(
        this,
        'migration_lambda_assume_role_policy',
        {
          statement: [assumeRolePolicyStatement('lambda')],
        },
      ).json,
    })
    // AWS-managed policy to allow lambda to create network interfaces in a
    // VPC. Required if the lambda needs access to VPC resource, which is the
    // case here.
    // Note: this policy includes LambdaBasic (hence "plus"), so no need to
    // double-attach.
    new IamRolePolicyAttachment(this, 'migration_lambda_vpc_access_plus', {
      role: role.name,
      policyArn:
        'arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole',
    })

    return new LambdaFunction(this, 'migration_lambda', {
      functionName: props.name,
      description: 'Lambda for managing migrations & postups',
      role: role.arn,

      packageType: 'Image',
      imageUri: props.imageUri,

      memorySize: 512, // 512 MB, CPU automatically scales with memory
      reservedConcurrentExecutions: 1,
      timeout: 900, // 15 minutes (to accomodate long-running migrations). Is 300 in prod. TODO: set this to 300.

      vpcConfig: {
        subnetIds: props.network.PrivateSubnets(),
        securityGroupIds: [props.network.SecurityGroups().default.id],
      },

      environment: {
        variables: {
          ENV: 'prod', // TODO: Set this from env
          DATABASES_NIRVANA_HOST: this.applicationDB.Info().host,
          DATABASES_NIRVANA_NAME: this.applicationDB.Info().name,
          DATABASES_NIRVANA_PORT: this.applicationDB.Info().port.toString(),
          DATABASES_NIRVANA_USERNAME: this.applicationDB.Info().username,
          DATABASES_NIRVANA_PASSWORD: this.applicationDB.Info().password,

          DATABASES_FMCSA_HOST: this.fmcsaDB.Info().host,
          DATABASES_FMCSA_PORT: this.fmcsaDB.Info().port.toString(),
          DATABASES_FMCSA_NAME: this.fmcsaDB.Info().dbName,
          DATABASES_FMCSA_USERNAME: this.fmcsaDB.Info().username,
          DATABASES_FMCSA_PASSWORD: this.fmcsaDB.Info().password,

          DATABASES_FMCSAREADONLY_HOST:
            this.fmcsaDB.Info().readonlyReplica?.host ??
            this.fmcsaDB.Info().host,
          DATABASES_FMCSAREADONLY_PORT: this.fmcsaDB.Info().port.toString(),
          DATABASES_FMCSAREADONLY_NAME: this.fmcsaDB.Info().dbName,
          DATABASES_FMCSAREADONLY_USERNAME: this.fmcsaDB.Info().username,
          DATABASES_FMCSAREADONLY_PASSWORD: this.fmcsaDB.Info().password,

          DATABASES_FMCSAWRITE_HOST: this.fmcsaDB.Info().host,
          DATABASES_FMCSAWRITE_PORT: this.fmcsaDB.Info().port.toString(),
          DATABASES_FMCSAWRITE_NAME: this.fmcsaDB.Info().dbName,
          DATABASES_FMCSAWRITE_USERNAME: this.fmcsaDB.Info().username,
          DATABASES_FMCSAWRITE_PASSWORD: this.fmcsaDB.Info().password,
        },
      },

      lifecycle: {
        ignoreChanges: ['image_uri'],
      },
    })
  }
}
