import { Statement } from 'iam-floyd'

import {
  FargateAppServiceConfig,
  FargateAppTaskDefConfig,
  FargateAppTaskDefConfiguratorProps,
} from '../../../lib/application-stack/configurators'
import { LegacyContainerComputeClusters } from '../../supported-ecs-clusters'

export namespace QuotingJobProcessorConfig {
  export const taskDef = quotingJobProcessorTaskDefConfig
  export const logging = {
    logGroup: {
      name: 'default-quoting-job-processor-logs',
      tags: {
        Environment: 'default',
        Application: 'quoting_job_processor',
      },
    },
  }
  export const service: FargateAppServiceConfig<LegacyContainerComputeClusters> =
    {
      namePrefix: 'quoting-job-processor',
      cluster: 'app',
      useServiceDiscoveryNamespaceOf: 'app',
      deployment: {
        desiredCount: 1,
        deploymentMaximumPercent: 300,
        deploymentMinimumHealthyPercent: 100,
      },
      tags: {
        Environment: 'default',
        Application: 'quoting-job-processor',
      },
    }
}

function quotingJobProcessorTaskDefConfig(
  props: FargateAppTaskDefConfiguratorProps<{
    s3Buckets: 'GoServiceProfiles'
    databases: 'applicationDB' | 'nhtsaDB' | 'fmcsaDB'
    secrets: 'LaunchDarklyApiKey'
  }>,
): FargateAppTaskDefConfig {
  return {
    taskRole: {
      name: 'quoting_job_processor_task',
      policyStatements: [
        new Statement.S3('')
          .allow()
          .toListBucket()
          .onBucket(props.s3Buckets.GoServiceProfiles.name),
        new Statement.S3('')
          .allow()
          .toGetObject()
          .toPutObject()
          .onObject(props.s3Buckets.GoServiceProfiles.name, '*'),
      ],
    },
    taskDefinition: {
      family: 'quoting-job-processor-td',
      containerName: 'quoting-job-processor',
      taskMemory: 512,
      taskCpu: 256,
      //   taskMemory: 16384,
      //   taskCpu: 2048,
      essential: true,
      portMappings: {
        app: {
          containerPort: 56224,
          hostPort: 56224,
          protocol: 'tcp',
        },
        pprof: {
          containerPort: 6060,
          hostPort: 6060,
          protocol: 'tcp',
        },
      },
      mapEnvironment: {
        ENV: 'prod',
        DATABASES_NIRVANA_HOST: props.databases.applicationDB.host,
        DATABASES_NIRVANA_NAME: 'postgres',
        DATABASES_NIRVANA_PORT: props.databases.applicationDB.port.toString(),
        DATABASES_NIRVANA_USERNAME: props.databases.applicationDB.username,
        DATABASES_NIRVANA_PASSWORD: props.databases.applicationDB.password,

        DATABASES_FMCSA_HOST: props.databases.fmcsaDB.host,
        DATABASES_FMCSA_PORT: props.databases.fmcsaDB.port.toString(),
        DATABASES_FMCSA_NAME: props.databases.fmcsaDB.dbName,
        DATABASES_FMCSA_USERNAME: props.databases.fmcsaDB.username,

        DATABASES_FMCSAREADONLY_HOST:
          props.databases.fmcsaDB.readonlyReplica?.host ??
          props.databases.fmcsaDB.host,
        DATABASES_FMCSAREADONLY_PORT: props.databases.fmcsaDB.port.toString(),
        DATABASES_FMCSAREADONLY_NAME: props.databases.fmcsaDB.dbName,
        DATABASES_FMCSAREADONLY_USERNAME: props.databases.fmcsaDB.username,

        DATABASES_FMCSAWRITE_HOST: props.databases.fmcsaDB.host,
        DATABASES_FMCSAWRITE_PORT: props.databases.fmcsaDB.port.toString(),
        DATABASES_FMCSAWRITE_NAME: props.databases.fmcsaDB.dbName,
        DATABASES_FMCSAWRITE_USERNAME: props.databases.fmcsaDB.username,

        DATABASES_NHTSA_HOST: props.databases.nhtsaDB.host,
        DATABASES_NHTSA_PASSWORD: props.databases.nhtsaDB.password,

        // Secrets
        PRODUCTTOOLS_LAUNCHDARKLYAPIKEY: props.secrets.LaunchDarklyApiKey.value,
        DATABASES_FMCSA_PASSWORD: props.databases.fmcsaDB.password,
        DATABASES_FMCSAREADONLY_PASSWORD: props.databases.fmcsaDB.password,
        DATABASES_FMCSAWRITE_PASSWORD: props.databases.fmcsaDB.password,
      },
      logConfiguration: {
        options: {
          awslogsStreamPrefix: 'awslogs-quoting-job-processor',
        },
      },
    },
  }
}
