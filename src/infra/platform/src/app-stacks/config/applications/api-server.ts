import { Statement } from 'iam-floyd'

import { getDBCreds } from '@nvinfra/common/interfaces'

import {
  FargateAppServiceConfig,
  FargateAppTaskDefConfig,
  FargateAppTaskDefConfiguratorProps,
} from '../../../lib/application-stack/configurators'
import { AppDBRoles } from '../../rds-stack/app'
import { LegacyContainerComputeClusters } from '../../supported-ecs-clusters'

export namespace ApiServerConfig {
  export const taskDef = apiServerTaskDefConfig
  export const logging = {
    logGroup: {
      name: 'default-api-server-logs',
      tags: {
        Environment: 'default',
        Application: 'api_server',
      },
    },
  }
  export const service: FargateAppServiceConfig<LegacyContainerComputeClusters> =
    {
      namePrefix: 'default-api_server',
      cluster: 'app',
      useServiceDiscoveryNamespaceOf: 'app',
      deployment: {
        desiredCount: 1,
        http: {
          loadBalancer: {
            namePrefix: 'api-server',
            targetGroup: {
              healthCheck: {
                interval: 30,
                path: '/health',
                timeout: 25,
                healthyThreshold: 3,
                unhealthyThreshold: 3,
                matcher: '200,301,302',
              },
            },
          },
          endpoint: 'api.staging.nirvanatech.com',
        },
      },
      tags: {
        Application: 'api_server',
      },
    }
}

function apiServerTaskDefConfig(
  props: FargateAppTaskDefConfiguratorProps<{
    s3Buckets: 'GoServiceProfiles'
    databases: 'applicationDB' | 'nhtsaDB' | 'fmcsaDB'
    secrets: 'LaunchDarklyApiKey'
  }>,
): FargateAppTaskDefConfig {
  return {
    taskRole: {
      name: 'api_server_task_role',
      policyStatements: [
        new Statement.S3('')
          .allow()
          .toListBucket()
          .onBucket(props.s3Buckets.GoServiceProfiles.name),
        new Statement.S3('')
          .allow()
          .toGetObject()
          .toPutObject()
          .onObject(props.s3Buckets.GoServiceProfiles.name, '*'),
      ],
    },
    taskDefinition: {
      family: 'api_server',
      containerName: 'api_server',
      taskMemory: 512,
      taskCpu: 256,
      essential: true,
      portMappings: {
        app: {
          containerPort: 8080,
          hostPort: 8080,
          protocol: 'tcp',
        },
        pprof: {
          containerPort: 6060,
          hostPort: 6060,
          protocol: 'tcp',
        },
      },
      mapEnvironment: {
        ENV: 'prod',
        DATABASES_NIRVANA_HOST: props.databases.applicationDB.host,
        DATABASES_NIRVANA_NAME: 'postgres',
        DATABASES_NIRVANA_PORT: props.databases.applicationDB.port.toString(),
        DATABASES_NIRVANA_USERNAME: props.databases.applicationDB.username,

        DATABASES_FMCSA_HOST: props.databases.fmcsaDB.host,
        DATABASES_FMCSA_PORT: props.databases.fmcsaDB.port.toString(),
        DATABASES_FMCSA_NAME: props.databases.fmcsaDB.dbName,
        DATABASES_FMCSA_USERNAME:
          props.databases.fmcsaDB.masterUserDetails.username,

        DATABASES_FMCSAREADONLY_HOST:
          props.databases.fmcsaDB.readonlyReplica?.host ??
          props.databases.fmcsaDB.host,
        DATABASES_FMCSAREADONLY_PORT: props.databases.fmcsaDB.port.toString(),
        DATABASES_FMCSAREADONLY_NAME: props.databases.fmcsaDB.dbName,
        DATABASES_FMCSAREADONLY_USERNAME:
          props.databases.fmcsaDB.masterUserDetails.username,

        DATABASES_FMCSAWRITE_HOST: props.databases.fmcsaDB.host,
        DATABASES_FMCSAWRITE_PORT: props.databases.fmcsaDB.port.toString(),
        DATABASES_FMCSAWRITE_NAME: props.databases.fmcsaDB.dbName,
        DATABASES_FMCSAWRITE_USERNAME:
          props.databases.fmcsaDB.masterUserDetails.username,

        DATABASES_NHTSA_HOST: props.databases.nhtsaDB.host,
      },
      mapSecrets: {
        PRODUCTTOOLS_LAUNCHDARKLYAPIKEY: props.secrets.LaunchDarklyApiKey.arn,
        DATABASES_NIRVANA_PASSWORD: getDBCreds(
          props.databases.applicationDB,
          AppDBRoles.Readwrite,
        ).password.arn,
        DATABASES_FMCSA_PASSWORD:
          props.databases.fmcsaDB.masterUserDetails.password.arn,
        DATABASES_FMCSAREADONLY_PASSWORD:
          props.databases.fmcsaDB.masterUserDetails.password.arn,
        DATABASES_FMCSAWRITE_PASSWORD:
          props.databases.fmcsaDB.masterUserDetails.password.arn,
        DATABASES_NHTSA_PASSWORD:
          props.databases.nhtsaDB.masterUserDetails.password.arn,
      },
      logConfiguration: {
        options: {
          awslogsStreamPrefix: 'awslogs-api-server',
        },
      },
      tags: {
        Application: 'api_server',
      },
    },
  }
}
