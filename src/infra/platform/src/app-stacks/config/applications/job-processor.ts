import { Statement } from 'iam-floyd'

import {
  FargateAppServiceConfig,
  FargateAppTaskDefConfig,
  FargateAppTaskDefConfiguratorProps,
} from '../../../lib/application-stack/configurators'
import { LegacyContainerComputeClusters } from '../../supported-ecs-clusters'

export namespace JobProcessorConfig {
  export const taskDef = jobProcessorTaskDefConfig
  export const logging = {
    logGroup: {
      name: 'default-job-processor-logs',
      tags: {
        Environment: 'default',
        Application: 'job_processor',
      },
    },
  }
  export const service: FargateAppServiceConfig<LegacyContainerComputeClusters> =
    {
      namePrefix: 'job-processor',
      cluster: 'internal_tools',
      // All services user the app cluster's private DNS namespace right now
      useServiceDiscoveryNamespaceOf: 'app',
      deployment: {
        desiredCount: 1,
        deploymentMaximumPercent: 300,
        deploymentMinimumHealthyPercent: 100,
      },
      tags: {
        Environment: 'default',
        Application: 'job-processor',
      },
    }
}

function jobProcessorTaskDefConfig(
  props: FargateAppTaskDefConfiguratorProps<{
    s3Buckets:
      | 'GoServiceProfiles'
      | 'JobberSchedules'
      | 'FmcsaPublicFiles'
      | 'FmcsaSftpDownloads'
      | 'Datagov'
    databases: 'applicationDB' | 'nhtsaDB' | 'fmcsaDB'
    secrets: 'LaunchDarklyApiKey'
  }>,
): FargateAppTaskDefConfig {
  return {
    taskRole: {
      name: 'job_processor_task_role',
      policyStatements: [
        new Statement.S3()
          .allow()
          .toListBucket()
          .onBucket(props.s3Buckets.FmcsaPublicFiles.name),
        new Statement.S3()
          .allow()
          .toGetObject()
          .toPutObject()
          .onObject(props.s3Buckets.FmcsaPublicFiles.name, '*'),
        new Statement.S3()
          .allow()
          .toListBucket()
          .onBucket(props.s3Buckets.GoServiceProfiles.name),
        new Statement.S3()
          .allow()
          .toGetObject()
          .toPutObject()
          .onObject(props.s3Buckets.GoServiceProfiles.name, '*'),
        new Statement.S3()
          .allow()
          .toListBucket()
          .onBucket(props.s3Buckets.FmcsaSftpDownloads.name),
        new Statement.S3()
          .allow()
          .toGetObject()
          .toPutObject()
          .onObject(props.s3Buckets.FmcsaSftpDownloads.name, '*'),
        new Statement.S3()
          .allow()
          .toListBucket()
          .onBucket(props.s3Buckets.JobberSchedules.name),
        new Statement.S3()
          .allow()
          .toGetObject()
          .toPutObject()
          .onObject(props.s3Buckets.JobberSchedules.name, '*'),
        new Statement.S3()
          .allow()
          .toListBucket()
          .onBucket(props.s3Buckets.Datagov.name),
        new Statement.S3()
          .allow()
          .toGetObject()
          .toPutObject()
          .onObject(props.s3Buckets.Datagov.name, '*'),

        // ECS task protection permissions
        new Statement.Ecs()
          .allow()
          .toGetTaskProtection()
          .toUpdateTaskProtection()
          .onTask('*' /* clusterName */, '*' /* taskId */, props.awsAccountId),
      ],
    },
    taskDefinition: {
      family: 'job-processor-td',
      containerName: 'job-processor',
      taskMemory: 512,
      taskCpu: 256,
      essential: true,
      portMappings: {
        app: {
          containerPort: 56223,
          hostPort: 56223,
          protocol: 'tcp',
        },
        pprof: {
          containerPort: 6065,
          hostPort: 6065,
          protocol: 'tcp',
        },
      },
      mapEnvironment: {
        ENV: 'prod',
        // Application database
        DATABASES_NIRVANA_HOST: props.databases.applicationDB.host,
        DATABASES_NIRVANA_NAME: 'postgres',
        DATABASES_NIRVANA_PORT: props.databases.applicationDB.port.toString(),
        DATABASES_NIRVANA_USERNAME: props.databases.applicationDB.username,
        DATABASES_NIRVANA_PASSWORD: props.databases.applicationDB.password,

        // NHTSA database
        DATABASES_NHTSA_HOST: props.databases.nhtsaDB.host,
        DATABASES_NHTSA_PASSWORD: props.databases.nhtsaDB.password,

        // FMCSA database
        DATABASES_FMCSA_HOST: props.databases.fmcsaDB.host,
        DATABASES_FMCSA_PORT: props.databases.fmcsaDB.port.toString(),
        DATABASES_FMCSA_NAME: props.databases.fmcsaDB.dbName,
        DATABASES_FMCSA_USERNAME: props.databases.fmcsaDB.username,

        DATABASES_FMCSAREADONLY_HOST:
          props.databases.fmcsaDB.readonlyReplica?.host ??
          props.databases.fmcsaDB.host,
        DATABASES_FMCSAREADONLY_PORT: props.databases.fmcsaDB.port.toString(),
        DATABASES_FMCSAREADONLY_NAME: props.databases.fmcsaDB.dbName,
        DATABASES_FMCSAREADONLY_USERNAME: props.databases.fmcsaDB.username,

        DATABASES_FMCSAWRITE_HOST: props.databases.fmcsaDB.host,
        DATABASES_FMCSAWRITE_PORT: props.databases.fmcsaDB.port.toString(),
        DATABASES_FMCSAWRITE_NAME: props.databases.fmcsaDB.dbName,
        DATABASES_FMCSAWRITE_USERNAME: props.databases.fmcsaDB.username,

        // Secrets
        PRODUCTTOOLS_LAUNCHDARKLYAPIKEY: props.secrets.LaunchDarklyApiKey.value,
        DATABASES_FMCSA_PASSWORD: props.databases.fmcsaDB.password,
        DATABASES_FMCSAREADONLY_PASSWORD: props.databases.fmcsaDB.password,
        DATABASES_FMCSAWRITE_PASSWORD: props.databases.fmcsaDB.password,
      },
      logConfiguration: {
        options: {
          awslogsStreamPrefix: 'awslogs-job-processor',
        },
      },
    },
  }
}
