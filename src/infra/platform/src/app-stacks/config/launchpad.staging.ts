import { readFileSync } from 'fs'

import { Environment } from '@nvinfra/common'
import { AWSAccount } from '@nvinfra/common/constants'

import { LaunchPadStacksProps } from '../launchpad'
import { prodLaunchPadCfg } from './launchpad.prod'

/**
 * Configuration for the staging launchpad.
 */
export const stagingLaunchPadCfg: LaunchPadStacksProps = {
  accountId: AWSAccount.Staging.Id,
  assumeRoleArn: AWSAccount.Staging.Role.Administrator,
  environment: Environment.Staging,
  region: 'us-east-2',
  network: {},
  accountEssentials: {
    ecs: {
      crossAccountEcrPullThroughCache: true,
    },
    dns: {
      domainName: 'staging.nirvanatech.com',
      parent: {
        zone: {
          name: 'nirvanatech.com',
        },
        providerProps: {
          accountId: AWSAccount.Management.Id,
        },
      },
    },
    // Email of #infra-alerts slack channel
    healthAlertsEmail:
      '<EMAIL>',
  },
  bastion: {
    amiImageName:
      'ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-********',
    size: {
      rootVolume: 20,
      homeVolume: 200,
    },
    instanceType: 't3.2xlarge',
    instanceName: 'staging-bastion',
    userData: readFileSync(
      // This is different from the production bastion user data script.
      // The file is too large to be passed in as a user data parameter,
      // and fails during deployment with the following error:
      // Error: expected length of user_data to be in the range (1 - 16384), got ..
      '../../deployment/app/data/init_staging_bastion.sh',
      'utf-8',
    ),
  },
  containerCompute: {
    app: {
      clusterName: 'default_app_cluster',
      enableContainerInsights: true,
      privateDNSNamespace: 'default.app.nirvana.internal',
    },
    internal_tools: {
      clusterName: 'default_internal_tools',
      enableContainerInsights: false,
      capacityProviders: ['FARGATE_SPOT', 'FARGATE'],
      privateDNSNamespace: 'default.internal_tools.nirvana.internal',
    },
    jobber_singletons: {
      clusterName: 'default_jobber_singletons',
      enableContainerInsights: false,
      capacityProviders: ['FARGATE_SPOT', 'FARGATE'],
      privateDNSNamespace: 'default.jobber_singletons.nirvana.internal',
    },
  },
  rds: {
    app: {
      identifier: 'staging-application-database',
      instanceClass: 'db.t3.medium',
      // Use the same engine version as prod
      engineVersion: prodLaunchPadCfg.rds.app.engineVersion,
      parameterGroupfamily: prodLaunchPadCfg.rds.app.parameterGroupfamily,
      storage: {
        allocated: 200,
        maxAllocated: 500,
      },
      readonlyReplica: {
        identifier: 'readonly-staging-application-database',
        instanceClass: 'db.t3.medium',
      },
      opsProps: {
        // opsProps same as prod, except for multiAz
        ...prodLaunchPadCfg.rds.app.opsProps,
        multiAz: false,
      },
    },
    nhtsa: {
      identifier: 'staging-nhtsa-database',
      instanceClass: 'db.t3.small',
      // Use the same engine version as prod
      engineVersion: prodLaunchPadCfg.rds.nhtsa.engineVersion,
      majorEngineVersion: prodLaunchPadCfg.rds.nhtsa.majorEngineVersion,
      storage: {
        allocated: 20,
        maxAllocated: 100,
      },
      source: {
        bucketName: 'staging-nirvana-nhtsa-dumps',
        path: '*',
      },
      // opsProps same as prod
      opsProps: prodLaunchPadCfg.rds.nhtsa.opsProps,
    },
    fmcsa: {
      clusterIdentifier: 'fmcsa-aurora-db-cluster',
      engineVersion: '16.8',
      parameterGroupName: 'default.aurora-postgresql16',
      numberOfReadReplicas: 0,
      capacitySettings: {
        minCapacity: 1,
        maxCapacity: 2,
      },
    },

    dbMigrationLambda: {
      name: 'db-migrate-lambda',
      imageTag: 'a5df0d9b',
    },
    backup: {
      vaultName: 'rds_cont_backup_vault',
      roleName: 'rds-cont-backup-role',
      appDb: {
        plan: {
          name: 'app_db_backup_plan',
          rules: [
            {
              enableContinuousBackup: true,
              lifecycle: {
                deleteAfter: 30,
              },
              ruleName: 'app_db_hourly_backup_rule',
              schedule: 'cron(0 * ? * * *)',
            },
          ],
        },
        selectionName: 'app_db_backup_selection',
      },
    },
  },
}
