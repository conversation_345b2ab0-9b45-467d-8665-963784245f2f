import { readFileSync } from 'fs'

import { Environment } from '@nvinfra/common'
import {
  AWSAccount,
  CW_TO_PD_INTERNAL_INFRA_SNS_NAME,
} from '@nvinfra/common/constants'

import { LaunchPadStacksProps } from '../launchpad'

/**
 * Configuration for the production launchpad, currently
 * deployed in the Management account.
 */
export const prodLaunchPadCfg: LaunchPadStacksProps = {
  accountId: AWSAccount.Management.Id,
  environment: Environment.Production,
  region: 'us-east-2',
  network: {
    alertConfig: {
      snsTopic: CW_TO_PD_INTERNAL_INFRA_SNS_NAME,
    },
  },
  accountEssentials: {
    ecs: {
      // We don't need this for prod since <PERSON><PERSON> is in the same account.
      crossAccountEcrPullThroughCache: false,
    },
    // Email of #infra-alerts slack channel
    healthAlertsEmail:
      '<EMAIL>',
  },
  bastion: {
    amiImageName:
      'ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-********',
    size: {
      rootVolume: 50,
      homeVolume: 200,
    },
    instanceType: 't3.2xlarge',
    instanceName: 'prod-production-bastion',
    userData: readFileSync(
      '../../deployment/app/data/init_bastion.sh',
      'utf-8',
    ),
  },
  containerCompute: {
    app: {
      clusterName: 'default_app_cluster',
      enableContainerInsights: true,
      privateDNSNamespace: 'default.app.nirvana.internal',
    },
    internal_tools: {
      clusterName: 'default_internal_tools',
      enableContainerInsights: false,
      capacityProviders: ['FARGATE_SPOT', 'FARGATE'],
      privateDNSNamespace: 'default.internal_tools.nirvana.internal',
    },
    jobber_singletons: {
      clusterName: 'default_jobber_singletons',
      enableContainerInsights: false,
      capacityProviders: ['FARGATE_SPOT', 'FARGATE'],
      privateDNSNamespace: 'default.jobber_singletons.nirvana.internal',
    },
  },
  rds: {
    app: {
      identifier: 'terraform-20210203043436823600000002',
      instanceClass: 'db.m5.xlarge',
      engineVersion: '16.6',
      parameterGroupfamily: 'postgres16',
      storage: {
        allocated: 750,
        maxAllocated: 2500,
      },
      opsProps: {
        multiAz: true,
        skipFinalSnapshot: true,
        applyImmediately: true, // Should be flipped back to false. See PR #11319 for context.
        autoMinorVersionUpgrade: false,
        deletionProtection: true,
        maintenanceWindow: 'Mon:07:00-Mon:08:00', // Monday afternoon IST
      },
      readonlyReplica: {
        identifier: 'default-postgres-replica-20220913061706610700000001',
        instanceClass: 'db.t3.medium',
      },
      legacyOnlyPropsForBackwardsCompatibility: {
        passwordSecretName: 'postgres-pass-default',
        subnetGroupName: 'terraform-20210203043434932700000001',
      },
      alerts: {
        snsTopic: CW_TO_PD_INTERNAL_INFRA_SNS_NAME,
        config: {
          cpuUtilisation: 80,
          freeableMemoryGB: 4,
          freeStorageGB: 100,
          connections: 200,
          diskQueueDepth: 15, // Disk queue depth reaches 11-12 during airbyte sync (every 3 hours)
          totalIOPS: {
            warningThreshold: 8000, // based on current usage
            criticalThreshold: 10500, // 90% of provision IOPs (12000)
          },
        },
      },
    },
    nhtsa: {
      identifier: 'prod-production-nhtsa',
      instanceClass: 'db.t3.small',
      engineVersion: '15.00.4073.23.v1',
      majorEngineVersion: '15.00',
      storage: {
        allocated: 20,
        maxAllocated: 100,
      },
      source: {
        bucketName: 'cloud.nirvanatech.com',
        path: 'private/nhtsa-db-backup/*',
      },
      opsProps: {
        skipFinalSnapshot: true,
        applyImmediately: false,
        autoMinorVersionUpgrade: false,
        deletionProtection: true,
        maintenanceWindow: 'Mon:07:00-Mon:08:00', // Monday afternoon IST
      },
      alerts: {
        snsTopic: CW_TO_PD_INTERNAL_INFRA_SNS_NAME,
        config: {
          cpuUtilisation: 80,
          freeableMemoryGB: 0.25,
          freeStorageGB: 10,
          connections: 100,
          diskQueueDepth: 5,
          totalIOPS: 500,
        },
      },
    },
    fmcsa: {
      clusterIdentifier: 'fmcsa-aurora-db-cluster',
      engineVersion: '16.8',
      parameterGroupName: 'default.aurora-postgresql16',
      numberOfReadReplicas: 1,
      capacitySettings: {
        minCapacity: 4,
        maxCapacity: 12,
      },
      legacyOnlyPropsForBackwardsCompatibility: {
        passwordSecretName: 'fmcsa-db-pass-default',
        subnetGroupName: 'terraform-20220801185629523200000001',
        // DB instances were created outside terraform and renaming required resource recreation.
        dbInstanceIdentifiers: [
          'fmcsa-aurora-db',
          'fmcsa-aurora-db-external-services',
        ],
      },
    },

    backup: {
      vaultName: 'rds_cont_backup_vault',
      roleName: 'rds-cont-backup-role',
      appDb: {
        plan: {
          name: 'rds_cont_backup_plan',
          rules: [
            {
              enableContinuousBackup: true,
              lifecycle: {
                deleteAfter: 30,
              },
              ruleName: 'rds_cont_backup_rule',
              schedule: 'cron(0 * ? * * *)',
            },
          ],
        },
        selectionName: 'rds_cont_backup_selection',
      },
    },
  },
  // For backwards compatibility with the legacy prod stacks.
  // These will be left undefined for all non-legacy stacks.
  legacyOnlyPropsForBackwardsCompatibility: {
    bastion: {
      keyPairName: 'bastion-default-prod-key-pair',
    },
  },
}
