import { Environment } from '@nvinfra/common'
import { AWSAccount } from '@nvinfra/common/constants'
import { EcrRepo } from '@nvinfra/common/constructs/stacks'

import { ApplicationStackProps } from '../../lib/application-stack'
import { AccountEssentialsStack } from '../account-essentials-stack'
import { LaunchPadStacks } from '../launchpad'
import { LegacyContainerComputeClusters } from '../supported-ecs-clusters'
import { EnvScopedBuckets } from '../supported-s3-buckets'
import { AppImageTagVarNames } from './applications'
import { ApiServerConfig } from './applications/api-server'
import { DistsemServerConfig } from './applications/distsem-server'
import { EventJobProcessorConfig } from './applications/event-job-processor'
import { FeatureStoreServerConfig } from './applications/feature-store'
import { GqlApiServerConfig } from './applications/gql-api-server'
import { JobProcessorConfig } from './applications/job-processor'
import { JobberMonitorConfig } from './applications/jobber-monitor'
import { PdfgenServerConfig } from './applications/pdfgen-server'
import { QuotingJobProcessorConfig } from './applications/quoting-job-processor'
import { SafetyJobProcessorConfig } from './applications/safety-job-processor'

type StagingApplications =
  | 'ApiServer'
  | 'GqlApiServer'
  | 'PdfgenServer'
  | 'FeatureStoreServer'
  | 'QuotingJobProcessor'
  | 'JobberMonitor'
  | 'DistsemServer'
  | 'EventJobProcessor'
  | 'JobProcessor'
  | 'SafetyJobProcessor'

type StagingTaskDependencies = {
  s3Buckets: EnvScopedBuckets
  databases: 'applicationDB' | 'nhtsaDB' | 'fmcsaDB'
  secrets: 'LaunchDarklyApiKey'
}

export function stagingApplicationStackConfig(
  ecrRepositories: Record<StagingApplications, EcrRepo>,
  s3Buckets: Record<StagingTaskDependencies['s3Buckets'], { name: string }>,
  launchPadStacks: LaunchPadStacks,
): ApplicationStackProps<
  StagingApplications,
  LegacyContainerComputeClusters,
  StagingTaskDependencies
> {
  return {
    additionalProviderProps: {
      aws: {
        allowedAccountIds: [AWSAccount.Staging.Id],
        assumeRole: [
          {
            roleArn: AWSAccount.Staging.Role.Administrator,
          },
        ],
      },
    },
    region: 'us-east-2',
    network: launchPadStacks.network,
    environment: Environment.Staging,
    group: 'backend',
    dns: launchPadStacks.accountEssentials.subdomain!.zone,

    ecsTaskExecutionRoleArn: AccountEssentialsStack.ecsTaskExecutionRoleArn(
      AWSAccount.Staging.Id,
    ),
    repositories: ecrRepositories,
    imageTagVarNames: AppImageTagVarNames,
    ecsClusters: launchPadStacks.containerCompute.clusters,
    taskDefConfiguratorProps: {
      awsAccountId: AWSAccount.Staging.Id,
      databases: {
        applicationDB: launchPadStacks.rds.applicationDB.Info(),
        nhtsaDB: launchPadStacks.rds.nhtsaDB.Info(),
        fmcsaDB: launchPadStacks.rds.fmcsaDB.Info(),
      },
      s3Buckets: s3Buckets,
      secrets: {
        LaunchDarklyApiKey: {
          name: 'launchdarkly-api-key',
        },
      },
    },
    appConfigs: {
      JobberMonitor: JobberMonitorConfig,
      FeatureStoreServer: FeatureStoreServerConfig,
      PdfgenServer: PdfgenServerConfig,
      ApiServer: ApiServerConfig,
      GqlApiServer: GqlApiServerConfig,
      QuotingJobProcessor: QuotingJobProcessorConfig,
      DistsemServer: DistsemServerConfig,
      EventJobProcessor: EventJobProcessorConfig,
      JobProcessor: JobProcessorConfig,
      SafetyJobProcessor: SafetyJobProcessorConfig,
    },
  }
}
