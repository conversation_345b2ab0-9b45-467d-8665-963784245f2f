import { App } from 'cdktf'

import { Environment } from '@nvinfra/common'
import {
  ContainerComputeStack,
  EcsClusterConfig,
} from '@nvinfra/common/constructs/stacks'

import {
  AccountEssentialsStack,
  AccountEssentialsStackProps,
} from './account-essentials-stack'
import { BastionStack } from './bastion-stack'
import {
  LegacyNetworkStack,
  LegacyNetworkStackProps,
} from './legacy-network-stack'
import { writeOutputsForHCLApp } from './outputs-for-hcl-app'
import { RDSStack, RDSStackProps } from './rds-stack'
import { AllSupportedECRRepos } from './supported-ecr-repos'
import { LegacyContainerComputeClusters } from './supported-ecs-clusters'

const bastionPublicKey =
  'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDPBaKbqcFjA9OEAocGIZUSv5+4KWf5Q2DezIwGfSTO4bbnGCTclPTIhu1MrE5X+mMKVDMFAhtseffW5t1Eb/A031sOZo7W1eg61j9+koBlLPrnz5MFvAwOXMGwCUutWyq/diWf/eW2yHWMjUkeFQoPKiOLT4Jo9pZc3uIObCWvNo6QgK7kxYW4Qqg0d/qqqmbT4nDe7KAOGrmgydwaYsaWT3BUk1NBTLCbUIQNbOCVL+0irAObHzb0ohS4lolmO56Kfhdv4vA3uSTt7fdK985hs4yEqpwivC7n7xMwO6MTsqvyRPJ6Kpcj81R/L799QSgz3VhM9gN8J6Ulko02uiwM43jhJms3Ns/vmJFUozWE8UjGpv/ZntRMAmUKk7daruRJ1q3WwhswgFkKdQbxVHcsFC1u4mvsFO0Q4FjSEyehoCsO+Vvz49+zNkkb5Dyhvg8LLamhO2fq6ganyq1lRkngRGLypalp25Zk50xfyceVlV0mDTMtOKvHRmjj+T8AWsjdtVz9l8GnfKYa+nJ5iuj5aw51FaRw3VWdu+/oYgOCxhHlBsyZ/hSsPn+/EDm72u93cdvLSgunE/xBq69fBIAodpqc4oxO84zP5bYua6JltT/p9j2qBGNUKLYTZuUtwJi9T4sHSKmqrAwS+k9awUyjhwkcXhsHSyI5NIbIqXZ3pQ== <EMAIL>'

export interface LaunchPadStacksProps {
  // The AWS account ID to deploy the stacks to.
  accountId: string

  // The ARN of the IAM role to assume prior to making API calls.
  // This is useful to deploy stacks in a different account. The
  // user making the call must have permissions to assume the role.
  assumeRoleArn?: string

  // The AWS region to deploy the stacks in
  region: string

  // The environment in which the stacks will be deployed.
  environment: Environment

  accountEssentials: Pick<
    AccountEssentialsStackProps,
    'ecs' | 'dns' | 'healthAlertsEmail'
  >

  // Props for the network stack.
  network: Pick<LegacyNetworkStackProps, 'alertConfig'>

  // Configuration options for the bastion host.
  bastion: {
    amiImageName: string
    instanceType: string
    instanceName: string
    size: {
      rootVolume: number
      homeVolume: number
    }
    userData: string
  }

  //Configuration options for the container compute clusters.
  containerCompute: Record<LegacyContainerComputeClusters, EcsClusterConfig>

  rds: Pick<
    RDSStackProps,
    'app' | 'fmcsa' | 'nhtsa' | 'backup' | 'dbMigrationLambda'
  >

  // These are only needed for backwards compatibility and must be left undefined
  // for all non-legacy stacks.
  legacyOnlyPropsForBackwardsCompatibility?: {
    bastion: {
      keyPairName: string
    }
  }
}

export interface LaunchPadStacks {
  accountEssentials: AccountEssentialsStack
  network: LegacyNetworkStack
  containerCompute: ContainerComputeStack<LegacyContainerComputeClusters>
  bastion: BastionStack
  rds: RDSStack
}

/**
 * A helper function to create _launchpad_ stacks with sane defaults for
 * most parameters. This function creates the following stacks:
 * - AccountEssentialsStack
 * - LegacyNetworkStack
 * - ContainerComputeStack
 * - BastionStack
 * - RDSStack
 *
 * For the "legacy" production stacks, use the `legacyOnlyPropsForBackwardsCompatibility`
 * parameter to provide the necessary configuration options.
 */
export const createLaunchPadStacks = (
  app: App,
  // The ECR repositories needed for these stacks.
  ecrRepositories: Record<AllSupportedECRRepos, { repositoryUrl: string }>,
  props: LaunchPadStacksProps,
) => {
  const baseProps = {
    environment: props.environment,
    region: props.region,
    additionalProviderProps: {
      aws: {
        allowedAccountIds: [props.accountId],
        assumeRole: props.assumeRoleArn
          ? [
              {
                roleArn: props.assumeRoleArn,
              },
            ]
          : undefined,
      },
    },
  }

  const accountEssentialsStack = new AccountEssentialsStack(app, {
    ...baseProps,
    group: 'coreinfra',
    // Account Essentials are not region-specific
    region: undefined,
    ...props.accountEssentials,
  })
  const legacyNetworkStack = new LegacyNetworkStack(app, {
    ...baseProps,
    group: 'coreinfra',
    bastionSGName: `${props.bastion.instanceName}-sg`,
    ...props.network,
  })
  const containerComputeStack =
    new ContainerComputeStack<LegacyContainerComputeClusters>(app, {
      ...baseProps,
      group: 'coreinfra',
      vpc: legacyNetworkStack.VPC(),
      clustersConfig: props.containerCompute,
    })
  const bastionStack = new BastionStack(app, {
    ...baseProps,
    group: 'coreinfra',
    network: legacyNetworkStack,
    ami: {
      owner: '************',
      imageName: props.bastion.amiImageName,
    },
    size: props.bastion.size,
    instanceType: props.bastion.instanceType,
    instanceName: props.bastion.instanceName,
    userData: props.bastion.userData,
    publicKey: bastionPublicKey,
    keyPairName:
      props.legacyOnlyPropsForBackwardsCompatibility?.bastion.keyPairName,
  })
  const rdsStack = new RDSStack(app, {
    ...baseProps,
    group: 'storage',
    network: legacyNetworkStack,
    ecrRepositories: ecrRepositories,
    ...props.rds,
  })

  // Write the terraform outputs needed for `hcl:app` to reference these values.
  writeOutputsForHCLApp(legacyNetworkStack, containerComputeStack)
  return {
    accountEssentials: accountEssentialsStack,
    network: legacyNetworkStack,
    containerCompute: containerComputeStack,
    bastion: bastionStack,
    rds: rdsStack,
  }
}
