/**
 * @fileoverview Supabase Infrastructure Stack for LLM Agents
 *
 * This module provisions a managed Supabase project in production for the LLM Agents application.
 * It creates a complete PostgreSQL database infrastructure with proper user management,
 * security controls, and migration support.
 *
 * ## Architecture Overview
 *
 * The LLM Agents project uses a dual-environment architecture:
 *
 * **Local Development:**
 * - Uses Docker Compose with local PostgreSQL container
 * - Full Supabase stack runs locally via `docker-compose.supabase.yml`
 * - Environment: `SUPABASE_DB_URL=postgresql+psycopg://postgres:${POSTGRES_PASSWORD}@db:5432/postgres`
 *
 * **Production:**
 * - Uses this CDKTF stack to provision a managed Supabase project
 * - Database passwords stored in AWS Secrets Manager
 * - Migrator role created with specific permissions for schema management
 * - Environment: `SUPABASE_DB_URL=postgresql+psycopg://migrator:${PASSWORD}@db.${PROJECT_ID}.supabase.co:5432/postgres`
 *
 * ## Database Migration Strategy
 *
 * This infrastructure creates the exact components needed for the migration execution pattern:
 *
 * 1. **Infrastructure Side (this file):**
 *    - Creates a 'migrator' database role with schema modification permissions
 *    - Generates and stores migrator password in AWS Secrets Manager
 *    - Provisions Supabase project with proper configuration
 *
 * 2. **Migration Execution Side (src/llm_agents/scripts/supabase_migrations.py):**
 *    - Fetches migrator password from AWS Secrets Manager
 *    - Connects as 'migrator' user to run dbmate commands
 *    - Executes migrations from `src/llm_agents/db/migrations/`
 *
 * 3. **Application Side (claims_agent):**
 *    - Uses standard PostgreSQL connection for runtime operations
 *    - Connects with application-specific credentials
 *    - Leverages database schema created by migrations
 *
 * ## LLM Agents Application Context
 *
 * The LLM Agents project is a Python workspace that includes:
 * - `claims_agent/`: FastAPI service for insurance claims processing
 * - `mcp_servers/`: Model Context Protocol servers providing tools
 * - `nirvana_rest_api/`: Client library for Nirvana backend services
 *
 * This database supports:
 * - **Claims Processing**: Stores coverage determination results
 * - **Workflow State**: Tracks Temporal workflow execution
 * - **Async Operations**: Manages background job processing
 * - **Tool Results**: Caches MCP server tool outputs
 *
 * ## Security Model
 *
 * - **Supabase Access Token**: Stored in AWS Secrets Manager (`init-ai/llm-agents/supabase-access-token`)
 * - **Organization ID**: Stored in AWS Secrets Manager (`init-ai/llm-agents/supabase-organization-id`)
 * - **Database Passwords**: Generated randomly and stored in AWS Secrets Manager
 * - **Migrator Role**: Limited permissions for schema changes only
 * - **Row Level Security**: Enabled on application tables
 *
 * ## Migration Execution Examples
 *
 * ```bash
 * # Check migration status
 * uv run supabase-migrations --host db.PROJECT_ID.supabase.co --database postgres status
 *
 * # Apply pending migrations
 * uv run supabase-migrations --host db.PROJECT_ID.supabase.co --database postgres up
 *
 * # Rollback last migration
 * uv run supabase-migrations --host db.PROJECT_ID.supabase.co --database postgres down
 * ```
 *
 * ## Dependencies
 *
 * This stack requires:
 * - AWS Secrets Manager access for credential storage
 * - Supabase provider configured with organization access
 * - PostgreSQL provider for database role management
 * - Random provider for secure password generation
 *
 * ## Related Files
 *
 * - `src/llm_agents/scripts/supabase_migrations.py`: Migration execution script
 * - `src/llm_agents/db/migrations/`: SQL migration files
 * - `src/llm_agents/docker-compose.supabase.yml`: Local development stack
 */
import { Fn } from 'cdktf'
import { Construct } from 'constructs'

import { DataAwsSecretsmanagerSecret } from '@cdktf/provider-aws/lib/data-aws-secretsmanager-secret'
import { DataAwsSecretsmanagerSecretVersion } from '@cdktf/provider-aws/lib/data-aws-secretsmanager-secret-version'
import { SecretsmanagerSecret } from '@cdktf/provider-aws/lib/secretsmanager-secret'
import { SecretsmanagerSecretVersion } from '@cdktf/provider-aws/lib/secretsmanager-secret-version'
import { PostgresqlProvider } from '@cdktf/provider-postgresql/lib/provider'
import { Role } from '@cdktf/provider-postgresql/lib/role'
import { PasswordConfig } from '@cdktf/provider-random/lib/password'
import { RandomProvider } from '@cdktf/provider-random/lib/provider'

import { BaseStack, BaseStackProps } from '@nvinfra/common/constructs/stacks'
import {
  createRole,
  PostgresPermissions,
  provisionRandomPasswordAsSecret,
} from '@nvinfra/common/lib'

import { DataSupabasePooler } from '../../generated/providers/supabase/data-supabase-pooler'
import { Project } from '../../generated/providers/supabase/project'
import { SupabaseProvider } from '../../generated/providers/supabase/provider'

// Identifier for the **NirvanaTech** organization in Supabase
const NIRVANATECH_ORGANIZATION_ID = 'wncivzhzlcezbbelnabh'

/**
 * Password configuration for secure database authentication.
 *
 * These characteristics ensure strong passwords for database access:
 * - 40 characters long for high entropy
 * - Includes special characters but avoids problematic ones
 * - Version tracking for password rotation
 */
const PASSWORD_CHARACTERISTICS = {
  length: 40,
  special: true,
  minSpecial: 5,
  overrideSpecial: '!#$%^&*()-_=+[]{}<>:?',
  keepers: {
    pass_version: '1',
  },
}

/**
 * Properties for configuring the Supabase infrastructure stack.
 */
export interface SupabaseStackProps extends BaseStackProps {
  /** AWS region where the infrastructure will be deployed */
  region: string
}

/**
 * Infrastructure stack for provisioning Supabase database for LLM Agents.
 *
 * This stack creates a complete database infrastructure including:
 * - Managed Supabase project with PostgreSQL database
 * - Database roles with appropriate permissions for migrations
 * - Secure password management via AWS Secrets Manager
 * - PostgreSQL provider configuration for role management
 *
 * ## Key Components Created
 *
 * 1. **Supabase Project**: Managed PostgreSQL database with Supabase tooling
 * 2. **Migrator Role**: Database user with schema modification permissions
 * 3. **Secret Management**: Secure storage of database credentials
 * 4. **Provider Configuration**: PostgreSQL provider for ongoing management
 *
 * ## Migration Integration
 *
 * This infrastructure creates the exact components needed by the migration system:
 * - The 'migrator' database role matches what `supabase_migrations.py` expects
 * - Password storage location matches migration script's default secret name
 * - Database permissions allow schema modifications required by dbmate
 *
 * ## Usage in LLM Agents
 *
 * The database supports multiple application components:
 * - **Claims Agent**: Stores coverage determination results and workflow state
 * - **MCP Servers**: Caches tool results and configuration
 * - **Background Workers**: Manages async job processing via Temporal
 * - **API Layer**: Provides data persistence for FastAPI endpoints
 */
export class SupabaseStack extends BaseStack {
  /**
   * Database role created for schema migrations.
   *
   * This role has the necessary permissions to create tables, modify schemas,
   * and manage database structure. It's used by the migration scripts to apply
   * database changes in production.
   */
  readonly databaseRole: Role

  /**
   * The name of the secret that contains the connection URL.
   */
  readonly connectionUrlSecretName: string

  constructor(scope: Construct, props: SupabaseStackProps) {
    super(scope, 'supabase', props)

    // Random provider is required for secure password generation
    new RandomProvider(this, 'random')

    // Retrieve Supabase access token from AWS Secrets Manager
    // This token allows the Terraform provider to manage Supabase resources
    const supabaseAccessToken = new DataAwsSecretsmanagerSecretVersion(
      this,
      `access_token_value`,
      {
        secretId: new DataAwsSecretsmanagerSecret(this, `access_token`, {
          name: 'init-ai/llm-agents/supabase-access-token',
        }).id,
      },
    )

    // Initialize the Supabase provider with the access token
    // This provider manages the Supabase project and its configuration
    const supabaseProvider = new SupabaseProvider(this, `supabase-provider`, {
      accessToken: supabaseAccessToken.secretString,
    })

    // Generate a secure random password for the main database user
    // This password is used by the Supabase project's default postgres user
    const databasePassword = this._provisionRandomPassword(
      'supabase-db-password',
      PASSWORD_CHARACTERISTICS,
    )

    // Create the main Supabase project
    // This provisions a managed PostgreSQL database with Supabase tooling
    const supabaseProject = new Project(this, `project`, {
      name: 'init-ai/llm-agents',
      databasePassword: databasePassword,
      // Instance size cannot be specified for free plan organizations
      // instanceSize: 'nano',
      region: props.region,
      organizationId: NIRVANATECH_ORGANIZATION_ID,
      provider: supabaseProvider,
      lifecycle: {
        ignoreChanges: ['database_password'],
      },
    })

    // Configure PostgreSQL provider for direct database management
    // This allows us to create database roles and manage permissions
    const pgProvider = new PostgresqlProvider(this, `pg-provider`, {
      host: `db.${supabaseProject.id}.supabase.co`,
      port: 5432,
      username: `postgres`,
      password: databasePassword,
      database: `postgres`,
      sslmode: 'require',
      superuser: false,
    })

    // Generate a secure password for the migrator role
    // This password is used by the migration scripts to apply schema changes
    const migratorPassword = this._provisionRandomPassword(
      'supabase-db-migrator-password',
      PASSWORD_CHARACTERISTICS,
    )

    // Create the migrator database role with schema modification permissions
    // This role is used by src/llm_agents/scripts/supabase_migrations.py
    this.databaseRole = createRole(this, pgProvider, {
      name: 'migrator',
      password: migratorPassword,
      permissions: [
        // Allow reading all data for validation and checks
        PostgresPermissions.READ_ALL_DATA,
        // Note: WRITE_ALL_DATA is not granted to limit migrator scope
        // The migrator should only modify schema, not application data

        // Allow creating databases and roles for advanced migration scenarios
        PostgresPermissions.CREATE_DATABASE,
        PostgresPermissions.CREATE_ROLE,

        // Allow creating objects in the main database
        PostgresPermissions.databaseGrant('create_on_db', 'postgres', [
          'CREATE',
        ]),

        // Allow creating objects in the public schema
        PostgresPermissions.schemaGrant(
          'create_on_public',
          'postgres',
          'public',
          ['CREATE'],
        ),
      ],
      // Grant admin option to allow role management if needed
      withAdminOption: true,
    })

    const pooler = new DataSupabasePooler(this, 'pooler', {
      projectRef: supabaseProject.id,
    })

    const parsedUrl = parseDatabaseUrl(Fn.lookup(pooler.url, 'transaction'))

    // Store the connection URL in AWS Secrets Manager
    // We do all the heavy lifting here, so the AWS services that need this can directly
    // reference the secret ARN (through the name.)
    // Apart from parsing & formatting the URL, we also `urlencode` the password
    // so that it can be used in the claims agent container.
    this.connectionUrlSecretName = 'init-ai/llm-agents/supabase-connection-url'
    new SecretsmanagerSecretVersion(this, 'connection_url_secret_version', {
      secretId: new SecretsmanagerSecret(this, 'connection_url_secret', {
        name: this.connectionUrlSecretName,
      }).id,
      secretString: Fn.format(
        'postgresql+psycopg://%s:%s@%s:%s/%s?sslmode=require',
        [
          parsedUrl.username,
          Fn.urlencode(databasePassword),
          parsedUrl.host,
          parsedUrl.port,
          parsedUrl.database,
        ],
      ),
    })
  }

  /**
   * Provisions a random password and stores it in AWS Secrets Manager.
   *
   * This method creates a secure random password, stores it in AWS Secrets Manager,
   * and returns a reference that can be used by other Terraform resources.
   *
   * ## Security Features
   *
   * - **Random Generation**: Uses cryptographically secure random generation
   * - **Secrets Manager**: Stores passwords in encrypted AWS Secrets Manager
   * - **No Plaintext**: Passwords never appear in Terraform state files
   * - **Version Tracking**: Supports password rotation via version tracking
   *
   * ## Migration Script Integration
   *
   * The migration script `src/llm_agents/scripts/supabase_migrations.py` retrieves
   * passwords from the same AWS Secrets Manager locations, creating a seamless
   * bridge between infrastructure provisioning and application deployment.
   *
   * @param secretName - Name suffix for the secret (will be prefixed with 'init-ai/llm-agents/')
   * @param config - Password generation configuration
   * @returns Token reference to the password for use in other resources
   */
  protected _provisionRandomPassword(
    secretName: string,
    config: PasswordConfig,
  ): string {
    /**
     * Supabase Strategy for Secret Provisioning
     *
     * This strategy implements the Supabase naming conventions:
     *
     * **Secret Naming:**
     * - Uses a standardized prefix for all LLM Agents secrets
     * - Prefix: "init-ai/llm-agents/"
     * - Example: secretName "db-password" → AWS Secret name "init-ai/llm-agents/db-password"
     *
     * **Construct ID Naming:**
     * - Uses lowercase base construct type with underscore separator
     * - No case conversion applied to the base type
     * - Final format: {base}_{secretName}
     *
     * **Examples:**
     * | Base          | Secret Name     | AWS Secret Name                        | Construct ID           |
     * |---------------|-----------------|----------------------------------------|------------------------|
     * | password      | db-password     | init-ai/llm-agents/db-password         | password_db-password   |
     * | secret        | db-password     | init-ai/llm-agents/db-password         | secret_db-password     |
     * | secret_value  | db-password     | init-ai/llm-agents/db-password         | secret_value_db-password |
     */
    const strategy = {
      formatSecretName: (name: string) => `init-ai/llm-agents/${name}`,
      formatConstructId: (base: string, id: string) => `${base}_${id}`,
    }
    return provisionRandomPasswordAsSecret(this, {
      secretName,
      config,
      strategy,
    }).value
  }
}

/**
 * Parses a Supabase pooler database URL into its components.
 *
 * @param databaseUrl - The Supabase pooler URL
 * @returns An object containing the parsed database connection components
 *
 * @remarks
 * The Supabase pooler URL map contains different connection modes (transaction, session).
 * The URL format is: postgresql://{username}:{password}@{host}:{port}/{database}
 *
 * Important notes:
 * - The username typically has the format: {username}.{project_id}
 * - The password in the URL is a placeholder '[YOUR_PASSWORD]' that needs to be replaced
 * - For actual connections, you'll need to provide the real database password
 * - The 'transaction' mode URL is used for connection pooling
 * - The port is returned as a string token; use Token.asNumber() if you need a numeric value
 *
 * @example
 * ```typescript
 * const database = parseDatabaseUrl(supabasePooler)
 *
 * // Use in PostgreSQL provider:
 * const pgProvider = new PostgresqlProvider(this, 'pg-provider', {
 *   host: database.host,
 *   port: Token.asNumber(database.port), // Convert to number if needed
 *   username: database.username,
 *   password: actualPassword, // This should be the real password
 *   database: database.database,
 *   sslmode: 'require',
 * })
 * ```
 */
function parseDatabaseUrl(databaseUrl: string): {
  host: string
  port: string
  username: string
  database: string
} {
  // Use regexall to extract URL components
  // The regex pattern captures: protocol, username, password, host, port, database
  //
  // IMPORTANT: Terraform's regexall with unnamed capture groups returns a list of lists
  // containing ONLY the capture groups (not the full match). This is documented at:
  // https://developer.hashicorp.com/terraform/language/functions/regexall
  //
  // For the regex: ^(postgresql://)([^:]+):([^@]+)@([^:]+):([0-9]+)/([^?]+)
  // The capture groups are:
  //   Group 1: (postgresql://)  - protocol
  //   Group 2: ([^:]+)          - username
  //   Group 3: ([^@]+)          - password placeholder
  //   Group 4: ([^:]+)          - host
  //   Group 5: ([0-9]+)         - port
  //   Group 6: ([^?]+)          - database
  //
  // regexall returns: [["postgresql://", "username", "[YOUR_PASSWORD]", "host", "5432", "database"]]
  // So match[0] = "postgresql://", match[1] = "username", match[2] = "[YOUR_PASSWORD]", etc.
  const urlMatches = Fn.regexall(
    '^(postgresql://)([^:]+):([^@]+)@([^:]+):([0-9]+)/([^?]+)',
    databaseUrl,
  )

  // Extract the first (and only) match from the results
  // regexall returns a list of lists, so we get the first match
  const match = Fn.element(urlMatches, 0)

  // Extract individual components from the match
  // Index 0: full protocol (postgresql://), skipped.
  // Index 1: username
  // Index 2: password placeholder, skipped.
  // Index 3: host
  // Index 4: port (as string)
  // Index 5: database name
  return {
    host: Fn.element(match, 3),
    port: Fn.element(match, 4), // Keep as string token - convert to number when needed
    username: Fn.element(match, 1),
    database: Fn.element(match, 5),
  }
}
