// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`ApplicationStack Snapshot Tests Scenario: Deployment Settings configuration synthesizes correctly 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    }
  },
  "module": {
    "ecs-app-api-server_service_AB043BF8": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "deployment_maximum_percent": 250,
      "deployment_minimum_healthy_percent": 50,
      "desired_count": 3,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "name_prefix": "api-server-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "api-server",
        "CreatedBy": "Factory"
      },
      "task_definition_arn": "\${module.ecs-app-api-server_task-definition_50564BD5.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-api-server_task-definition_50564BD5": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/api-server:\${var.apiserverImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-api-server_log-group_11B1A4AD.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "output": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image currently deployed",
      "value": "\${var.apiserverImageTag}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-api-server_log-group_11B1A4AD": {
        "name": "/ecs/api-server-test",
        "tags": {
          "Application": "api-server",
          "Environment": "dev"
        }
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  },
  "variable": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image to be used to deploy",
      "nullable": false,
      "type": "string"
    }
  }
}"
`;

exports[`ApplicationStack Snapshot Tests Scenario: Different Clusters configuration synthesizes correctly 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    }
  },
  "module": {
    "ecs-app-api-server_service_AB043BF8": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "name_prefix": "api-server-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "api-server",
        "CreatedBy": "Factory"
      },
      "task_definition_arn": "\${module.ecs-app-api-server_task-definition_50564BD5.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-api-server_task-definition_50564BD5": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/api-server:\${var.apiserverImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-api-server_log-group_11B1A4AD.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ecs-app-job-processor_service_3B0E6B48": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "name_prefix": "job-processor-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "job-processor",
        "CreatedBy": "Factory"
      },
      "task_definition_arn": "\${module.ecs-app-job-processor_task-definition_18CDBD73.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-job-processor_task-definition_18CDBD73": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/job-processor:\${var.jobprocessorImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-job-processor_log-group_D554FF6E.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "output": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image currently deployed",
      "value": "\${var.apiserverImageTag}"
    },
    "jobprocessorImageTag": {
      "description": "The tag of the job-processor image currently deployed",
      "value": "\${var.jobprocessorImageTag}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-api-server_log-group_11B1A4AD": {
        "name": "/ecs/api-server-test",
        "tags": {
          "Application": "api-server",
          "Environment": "dev"
        }
      },
      "ecs-app-job-processor_log-group_D554FF6E": {
        "name": "/ecs/job-processor-test",
        "tags": {
          "Application": "job-processor",
          "Environment": "dev"
        }
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  },
  "variable": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image to be used to deploy",
      "nullable": false,
      "type": "string"
    },
    "jobprocessorImageTag": {
      "description": "The tag of the job-processor image to be used to deploy",
      "nullable": false,
      "type": "string"
    }
  }
}"
`;

exports[`ApplicationStack Snapshot Tests Scenario: Explicit Image Tag configuration synthesizes correctly 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    }
  },
  "module": {
    "ecs-app-api-server_service_AB043BF8": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "name_prefix": "api-server-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "api-server",
        "CreatedBy": "Factory"
      },
      "task_definition_arn": "\${module.ecs-app-api-server_task-definition_50564BD5.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-api-server_task-definition_50564BD5": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/api-server:v1.2.3",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-api-server_log-group_11B1A4AD.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-api-server_log-group_11B1A4AD": {
        "name": "/ecs/api-server-test",
        "tags": {
          "Application": "api-server",
          "Environment": "dev"
        }
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  }
}"
`;

exports[`ApplicationStack Snapshot Tests Scenario: HTTP Endpoint configuration synthesizes correctly 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    }
  },
  "module": {
    "ecs-app-http-app_load-balancer_0D5D03A7": {
      "default_certificate_arn": "\${module.ssl-certificate.arn}",
      "http_ports": {
        "force_https": {
          "host": "#{host}",
          "listener_port": 80,
          "path": "/#{path}",
          "protocol": "HTTPS",
          "query": "#{query}",
          "status_code": "HTTP_301",
          "target_port": "443"
        }
      },
      "https_ports": {
        "force_https": {
          "listener_port": 443
        }
      },
      "load_balancing_algorithm_type": "round_robin",
      "name_prefix": "dev-http-app-lb",
      "public_subnets": [
        "\${aws_subnet.mock-network-public-subnet-0.id}",
        "\${aws_subnet.mock-network-public-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "source": "./../../deployment/common-modules/nv_svc_alb",
      "ssl_policy": "ELBSecurityPolicy-2016-08",
      "target_group_health_check_enabled": true,
      "target_group_health_check_healthy_threshold": 2,
      "target_group_health_check_interval": 30,
      "target_group_health_check_matcher": "200",
      "target_group_health_check_path": "/healthz",
      "target_group_health_check_timeout": 5,
      "target_group_health_check_unhealthy_threshold": 2,
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-http-app_service_0965E987": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "create_lb_access_sg_rule": true,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-web-cluster.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-web-cluster.name}",
      "lb_access_sg": "\${module.ecs-app-http-app_load-balancer_0D5D03A7.access_sg_id}",
      "lb_http_tg_arns": "\${module.ecs-app-http-app_load-balancer_0D5D03A7.http_tgs_arns}",
      "lb_http_tg_ports": "\${module.ecs-app-http-app_load-balancer_0D5D03A7.http_tgs_ports}",
      "name_prefix": "http-app-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-web-cluster.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "http-app",
        "ServiceType": "HTTP"
      },
      "task_definition_arn": "\${module.ecs-app-http-app_task-definition_53C19C50.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-http-app_task-definition_53C19C50": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/http-app:\${var.httpappImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-http-app_log-group_D762E708.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "output": {
    "httpappImageTag": {
      "description": "The tag of the http-app image currently deployed",
      "value": "\${var.httpappImageTag}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-http-app_log-group_D762E708": {
        "name": "/ecs/http-app-http",
        "tags": {
          "Application": "http-app",
          "Environment": "dev"
        }
      }
    },
    "aws_route53_record": {
      "ecs-app-http-app_dns_record_C766D09B": {
        "alias": {
          "evaluate_target_health": true,
          "name": "\${module.ecs-app-http-app_load-balancer_0D5D03A7.dns_name}",
          "zone_id": "\${module.ecs-app-http-app_load-balancer_0D5D03A7.zone_id}"
        },
        "name": "http-app.dev.example.com",
        "type": "A",
        "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
      }
    },
    "aws_security_group_rule": {
      "ecs-app-http-app_allow_alb_ingress_default_8E3F3C07": {
        "from_port": 0,
        "protocol": "-1",
        "security_group_id": "\${aws_security_group.mock-network-sg-default.id}",
        "source_security_group_id": "\${module.ecs-app-http-app_load-balancer_0D5D03A7.access_sg_id}",
        "to_port": 0,
        "type": "ingress"
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  },
  "variable": {
    "httpappImageTag": {
      "description": "The tag of the http-app image to be used to deploy",
      "nullable": false,
      "type": "string"
    }
  }
}"
`;

exports[`ApplicationStack Snapshot Tests Scenario: Image Tag from Variable synthesizes correctly (default case) 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    }
  },
  "module": {
    "ecs-app-api-server_service_AB043BF8": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "name_prefix": "api-server-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "api-server",
        "CreatedBy": "Factory"
      },
      "task_definition_arn": "\${module.ecs-app-api-server_task-definition_50564BD5.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-api-server_task-definition_50564BD5": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/api-server:\${var.apiserverImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-api-server_log-group_11B1A4AD.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "output": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image currently deployed",
      "value": "\${var.apiserverImageTag}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-api-server_log-group_11B1A4AD": {
        "name": "/ecs/api-server-test",
        "tags": {
          "Application": "api-server",
          "Environment": "dev"
        }
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  },
  "variable": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image to be used to deploy",
      "nullable": false,
      "type": "string"
    }
  }
}"
`;

exports[`ApplicationStack Snapshot Tests Scenario: Load Balancer Config configuration synthesizes correctly 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    }
  },
  "module": {
    "ecs-app-api-server_load-balancer_80AE35A6": {
      "default_certificate_arn": "\${module.ssl-certificate.arn}",
      "http_ports": {
        "force_https": {
          "host": "#{host}",
          "listener_port": 80,
          "path": "/#{path}",
          "protocol": "HTTPS",
          "query": "#{query}",
          "status_code": "HTTP_301",
          "target_port": "443"
        }
      },
      "https_ports": {
        "force_https": {
          "listener_port": 443
        }
      },
      "load_balancing_algorithm_type": "round_robin",
      "name_prefix": "dev-api-server-lb",
      "public_subnets": [
        "\${aws_subnet.mock-network-public-subnet-0.id}",
        "\${aws_subnet.mock-network-public-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "source": "./../../deployment/common-modules/nv_svc_alb",
      "ssl_policy": "ELBSecurityPolicy-2016-08",
      "target_group_deregistration_delay": 60,
      "target_group_health_check_enabled": true,
      "target_group_health_check_healthy_threshold": 2,
      "target_group_health_check_interval": 30,
      "target_group_health_check_matcher": "200",
      "target_group_health_check_path": "/healthz",
      "target_group_health_check_timeout": 5,
      "target_group_health_check_unhealthy_threshold": 2,
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-api-server_service_AB043BF8": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "create_lb_access_sg_rule": true,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "lb_access_sg": "\${module.ecs-app-api-server_load-balancer_80AE35A6.access_sg_id}",
      "lb_http_tg_arns": "\${module.ecs-app-api-server_load-balancer_80AE35A6.http_tgs_arns}",
      "lb_http_tg_ports": "\${module.ecs-app-api-server_load-balancer_80AE35A6.http_tgs_ports}",
      "name_prefix": "api-server-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "api-server",
        "CreatedBy": "Factory",
        "ServiceType": "HTTP"
      },
      "task_definition_arn": "\${module.ecs-app-api-server_task-definition_50564BD5.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-api-server_task-definition_50564BD5": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/api-server:\${var.apiserverImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-api-server_log-group_11B1A4AD.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "output": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image currently deployed",
      "value": "\${var.apiserverImageTag}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-api-server_log-group_11B1A4AD": {
        "name": "/ecs/api-server-test",
        "tags": {
          "Application": "api-server",
          "Environment": "dev"
        }
      }
    },
    "aws_route53_record": {
      "ecs-app-api-server_dns_record_8EC2E278": {
        "alias": {
          "evaluate_target_health": true,
          "name": "\${module.ecs-app-api-server_load-balancer_80AE35A6.dns_name}",
          "zone_id": "\${module.ecs-app-api-server_load-balancer_80AE35A6.zone_id}"
        },
        "name": "api-server.\${aws_route53_zone.mock-zone.name}",
        "type": "A",
        "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
      }
    },
    "aws_security_group_rule": {
      "ecs-app-api-server_allow_alb_ingress_default_AC531936": {
        "from_port": 0,
        "protocol": "-1",
        "security_group_id": "\${aws_security_group.mock-network-sg-default.id}",
        "source_security_group_id": "\${module.ecs-app-api-server_load-balancer_80AE35A6.access_sg_id}",
        "to_port": 0,
        "type": "ingress"
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  },
  "variable": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image to be used to deploy",
      "nullable": false,
      "type": "string"
    }
  }
}"
`;

exports[`ApplicationStack Snapshot Tests Scenario: Logging configuration synthesizes correctly 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    }
  },
  "module": {
    "ecs-app-api-server_service_AB043BF8": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "name_prefix": "api-server-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "api-server",
        "CreatedBy": "Factory"
      },
      "task_definition_arn": "\${module.ecs-app-api-server_task-definition_50564BD5.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-api-server_task-definition_50564BD5": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/api-server:\${var.apiserverImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-api-server_log-group_11B1A4AD.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "output": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image currently deployed",
      "value": "\${var.apiserverImageTag}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-api-server_log-group_11B1A4AD": {
        "name": "/ecs/custom-api-server-logs",
        "tags": {
          "Application": "api-server",
          "Environment": "dev"
        }
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  },
  "variable": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image to be used to deploy",
      "nullable": false,
      "type": "string"
    }
  }
}"
`;

exports[`ApplicationStack Snapshot Tests Scenario: Minimal configuration synthesizes correctly 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    }
  },
  "module": {
    "ecs-app-api-server_service_AB043BF8": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "name_prefix": "api-server-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "api-server",
        "CreatedBy": "Factory"
      },
      "task_definition_arn": "\${module.ecs-app-api-server_task-definition_50564BD5.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-api-server_task-definition_50564BD5": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/api-server:\${var.apiserverImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-api-server_log-group_11B1A4AD.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "output": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image currently deployed",
      "value": "\${var.apiserverImageTag}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-api-server_log-group_11B1A4AD": {
        "name": "/ecs/api-server-test",
        "tags": {
          "Application": "api-server",
          "Environment": "dev"
        }
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  },
  "variable": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image to be used to deploy",
      "nullable": false,
      "type": "string"
    }
  }
}"
`;

exports[`ApplicationStack Snapshot Tests Scenario: Multiple Apps configuration synthesizes correctly 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    }
  },
  "module": {
    "ecs-app-api-server_service_AB043BF8": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "name_prefix": "api-server-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "api-server",
        "ServiceType": "Minimal"
      },
      "task_definition_arn": "\${module.ecs-app-api-server_task-definition_50564BD5.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-api-server_task-definition_50564BD5": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/api-server:\${var.apiserverImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-api-server_log-group_11B1A4AD.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ecs-app-http-app_service_0965E987": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "name_prefix": "http-app-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "http-app",
        "CreatedBy": "Factory"
      },
      "task_definition_arn": "\${module.ecs-app-http-app_task-definition_53C19C50.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-http-app_task-definition_53C19C50": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/http-app:\${var.httpappImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-http-app_log-group_D762E708.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ecs-app-job-processor_service_3B0E6B48": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-batch-jobs.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-batch-jobs.name}",
      "name_prefix": "job-processor-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-batch-jobs.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "job-processor",
        "ServiceType": "Minimal"
      },
      "task_definition_arn": "\${module.ecs-app-job-processor_task-definition_18CDBD73.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-job-processor_task-definition_18CDBD73": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/job-processor:\${var.jobprocessorImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-job-processor_log-group_D554FF6E.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "output": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image currently deployed",
      "value": "\${var.apiserverImageTag}"
    },
    "httpappImageTag": {
      "description": "The tag of the http-app image currently deployed",
      "value": "\${var.httpappImageTag}"
    },
    "jobprocessorImageTag": {
      "description": "The tag of the job-processor image currently deployed",
      "value": "\${var.jobprocessorImageTag}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-api-server_log-group_11B1A4AD": {
        "name": "/ecs/api-server-minimal",
        "tags": {
          "Application": "api-server",
          "Environment": "dev"
        }
      },
      "ecs-app-http-app_log-group_D762E708": {
        "name": "/ecs/http-app-test",
        "tags": {
          "Application": "http-app",
          "Environment": "dev"
        }
      },
      "ecs-app-job-processor_log-group_D554FF6E": {
        "name": "/ecs/job-processor-minimal",
        "tags": {
          "Application": "job-processor",
          "Environment": "dev"
        }
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  },
  "variable": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image to be used to deploy",
      "nullable": false,
      "type": "string"
    },
    "httpappImageTag": {
      "description": "The tag of the http-app image to be used to deploy",
      "nullable": false,
      "type": "string"
    },
    "jobprocessorImageTag": {
      "description": "The tag of the job-processor image to be used to deploy",
      "nullable": false,
      "type": "string"
    }
  }
}"
`;

exports[`ApplicationStack Snapshot Tests Scenario: Secrets/Dependencies configuration synthesizes correctly 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    },
    "aws_secretsmanager_secret": {
      "secret-externalApiKey": {
        "name": "mock/secret/name/or/arn/for/api-key"
      },
      "secret-rdsCredentials": {
        "name": "mock/secret/name/or/arn/for/rds"
      }
    },
    "aws_secretsmanager_secret_version": {
      "secret-version-externalApiKey": {
        "secret_id": "\${data.aws_secretsmanager_secret.secret-externalApiKey.id}"
      },
      "secret-version-rdsCredentials": {
        "secret_id": "\${data.aws_secretsmanager_secret.secret-rdsCredentials.id}"
      }
    }
  },
  "module": {
    "ecs-app-api-server_service_AB043BF8": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "name_prefix": "api-server-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "api-server",
        "CreatedBy": "Factory"
      },
      "task_definition_arn": "\${module.ecs-app-api-server_task-definition_50564BD5.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-api-server_task-definition_50564BD5": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/api-server:\${var.apiserverImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-api-server_log-group_11B1A4AD.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "map_environment": {
        "API_KEY_ARN_ENV": "\${data.aws_secretsmanager_secret.secret-externalApiKey.arn}",
        "SECRET_ARN_ENV": "\${data.aws_secretsmanager_secret.secret-rdsCredentials.arn}"
      },
      "map_secrets": {
        "API_KEY_VALUE_ENV": "\${data.aws_secretsmanager_secret_version.secret-version-externalApiKey.secret_string}",
        "SECRET_VALUE_ENV": "\${data.aws_secretsmanager_secret_version.secret-version-rdsCredentials.secret_string}"
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "output": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image currently deployed",
      "value": "\${var.apiserverImageTag}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-api-server_log-group_11B1A4AD": {
        "name": "/ecs/api-server-test",
        "tags": {
          "Application": "api-server",
          "Environment": "dev"
        }
      }
    },
    "aws_iam_policy": {
      "ecs-exec-role-secrets-policy": {
        "name": "ecs-exec-role-secrets-policy",
        "policy": "{\\n  \\"Version\\": \\"2012-10-17\\",\\n  \\"Statement\\": [\\n    {\\n      \\"Action\\": \\"secretsmanager:GetSecretValue\\",\\n      \\"Resource\\": [\\n        \\"\${data.aws_secretsmanager_secret.secret-rdsCredentials.arn}\\",\\n        \\"\${data.aws_secretsmanager_secret.secret-externalApiKey.arn}\\"\\n      ],\\n      \\"Effect\\": \\"Allow\\"\\n    }\\n  ]\\n}"
      }
    },
    "aws_iam_role_policy_attachment": {
      "ecs-exec-role-secrets-policy-attachment": {
        "policy_arn": "\${aws_iam_policy.ecs-exec-role-secrets-policy.arn}",
        "role": "ecsTaskExecutionRole"
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  },
  "variable": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image to be used to deploy",
      "nullable": false,
      "type": "string"
    }
  }
}"
`;

exports[`ApplicationStack Snapshot Tests Scenario: Service Tags configuration synthesizes correctly 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    }
  },
  "module": {
    "ecs-app-api-server_service_AB043BF8": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-general-apps.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-general-apps.name}",
      "name_prefix": "api-server-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-general-apps.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "api-server",
        "CostCenter": "Platform",
        "CreatedBy": "Factory",
        "Project": "AppStackRefactor"
      },
      "task_definition_arn": "\${module.ecs-app-api-server_task-definition_50564BD5.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-api-server_task-definition_50564BD5": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/api-server:\${var.apiserverImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-api-server_log-group_11B1A4AD.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "output": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image currently deployed",
      "value": "\${var.apiserverImageTag}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-api-server_log-group_11B1A4AD": {
        "name": "/ecs/api-server-test",
        "tags": {
          "Application": "api-server",
          "Environment": "dev"
        }
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  },
  "variable": {
    "apiserverImageTag": {
      "description": "The tag of the api-server image to be used to deploy",
      "nullable": false,
      "type": "string"
    }
  }
}"
`;

exports[`ApplicationStack Snapshot Tests Scenario: Task Role configuration synthesizes correctly 1`] = `
"{
  "data": {
    "aws_iam_policy_document": {
      "ecs-tasks-assume-role-policy": {
        "statement": [
          {
            "actions": [
              "sts:AssumeRole"
            ],
            "principals": [
              {
                "identifiers": [
                  "ecs-tasks.amazonaws.com"
                ],
                "type": "Service"
              }
            ]
          }
        ]
      }
    }
  },
  "module": {
    "ecs-app-job-processor_service_3B0E6B48": {
      "container_name": "minimal-app-container",
      "container_port": 8080,
      "ecs_cluster_arn": "\${aws_ecs_cluster.mock-cluster-batch-jobs.arn}",
      "ecs_cluster_name": "\${aws_ecs_cluster.mock-cluster-batch-jobs.name}",
      "name_prefix": "job-processor-svc",
      "private_subnets": [
        "\${aws_subnet.mock-network-private-subnet-0.id}",
        "\${aws_subnet.mock-network-private-subnet-1.id}"
      ],
      "security_groups": [
        "\${aws_security_group.mock-network-sg-default.id}"
      ],
      "service_discovery_namespace_id": "\${aws_service_discovery_private_dns_namespace.mock-ns-batch-jobs.id}",
      "source": "./../../deployment/common-modules/nv_fg_svc",
      "tags": {
        "Application": "job-processor",
        "CreatedBy": "Factory"
      },
      "task_definition_arn": "\${module.ecs-app-job-processor_task-definition_18CDBD73.arn}",
      "vpc_id": "\${aws_vpc.mock-network-vpc.id}"
    },
    "ecs-app-job-processor_task-definition_18CDBD73": {
      "container_image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/job-processor:\${var.jobprocessorImageTag}",
      "container_name": "minimal-app-container",
      "family": "minimal-app-family",
      "log_configuration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "\${aws_cloudwatch_log_group.ecs-app-job-processor_log-group_D554FF6E.name}",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "minimal-app"
        }
      },
      "port_mappings": [
        {
          "containerPort": 8080,
          "hostPort": 8080,
          "protocol": "tcp"
        }
      ],
      "source": "./../../deployment/common-modules/ecs_task_definition",
      "task_execution_role_arn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole",
      "task_role_arn": "\${aws_iam_role.ecs-app-job-processor_task-role_FEE2352C.arn}"
    },
    "ssl-certificate": {
      "domain_name": "\${aws_route53_zone.mock-zone.name}",
      "source": "./../../deployment/common-modules/acm",
      "subject_alternative_names": [
        "*.\${aws_route53_zone.mock-zone.name}"
      ],
      "validation_method": "DNS",
      "wait_for_validation": false,
      "zone_id": "\${aws_route53_zone.mock-zone.zone_id}"
    }
  },
  "output": {
    "jobprocessorImageTag": {
      "description": "The tag of the job-processor image currently deployed",
      "value": "\${var.jobprocessorImageTag}"
    }
  },
  "provider": {
    "aws": [
      {
        "default_tags": [
          {
            "tags": {
              "environment": "dev",
              "group": "test-group",
              "infraWorkspace": "default",
              "region": "us-east-1",
              "stackName": "application-stack"
            }
          }
        ],
        "region": "us-east-1"
      }
    ]
  },
  "resource": {
    "aws_cloudwatch_log_group": {
      "ecs-app-job-processor_log-group_D554FF6E": {
        "name": "/ecs/job-processor-test",
        "tags": {
          "Application": "job-processor",
          "Environment": "dev"
        }
      }
    },
    "aws_iam_policy": {
      "ecs-app-job-processor_task-role-policy_B149C663": {
        "policy": "{\\n  \\"Version\\": \\"2012-10-17\\",\\n  \\"Statement\\": [\\n    {\\n      \\"Action\\": \\"s3:GetObject\\",\\n      \\"Resource\\": \\"arn:aws:s3:::my-data-bucket/*\\",\\n      \\"Effect\\": \\"Allow\\"\\n    }\\n  ]\\n}"
      }
    },
    "aws_iam_role": {
      "ecs-app-job-processor_task-role_FEE2352C": {
        "assume_role_policy": "\${data.aws_iam_policy_document.ecs-tasks-assume-role-policy.json}",
        "name": "my-app-task-role"
      }
    },
    "aws_iam_role_policy_attachment": {
      "ecs-app-job-processor_task-role-policy-attachment_EAE6E970": {
        "policy_arn": "\${aws_iam_policy.ecs-app-job-processor_task-role-policy_B149C663.arn}",
        "role": "\${aws_iam_role.ecs-app-job-processor_task-role_FEE2352C.name}"
      }
    }
  },
  "terraform": {
    "backend": {
      "s3": {
        "bucket": "cloud.nirvanatech.com",
        "key": "private/deployment/cdktf/default/dev/default-dev-test-group-application-stack-us-east-1.json",
        "region": "us-east-2"
      }
    },
    "required_providers": {
      "aws": {
        "source": "aws",
        "version": "5.88.0"
      }
    },
    "required_version": "1.7.5"
  },
  "variable": {
    "jobprocessorImageTag": {
      "description": "The tag of the job-processor image to be used to deploy",
      "nullable": false,
      "type": "string"
    }
  }
}"
`;
