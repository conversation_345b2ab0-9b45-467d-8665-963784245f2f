Run all tests using `npm run test`.

1.  **Centralized Testing Infrastructure (`platform/src/testing/`):**

    - `cdktf-helpers.ts` to provide standardized functions for creating isolated test `App` instances (`createTestApp`), `TerraformStack` scopes (`createTestScope`), and synthesizing stacks (`synthesizeStack`, `synthesizeAndParseStack`).
    - `factories.ts` containing factory classes (`ApplicationStackPropsFactory`, `NetworkFactory`, `EcsClusterFactory`, etc.) to generate consistent mock props and resources, significantly reducing boilerplate and improving maintainability.
    - `constants.ts` for shared test constants (account ID, region).

2.  **Multi-Layered Testing for `ApplicationStack` (`platform/src/lib/application-stack/__tests__/`):**
    - **Snapshot Tests (`index.test.ts`):** Verify the overall synthesized JSON for major scenarios (minimal config, HTTP endpoint, multiple apps, etc.) using `toMatchSnapshot()`. Leverages the new factories and helpers.
      - https://jestjs.io/docs/snapshot-testing
      - Use command `npm run test -- --update-snapshot` to update stale snapshots
    - **Unit Tests (`unit-tests.test.ts`):** Target complex internal logic (e.g., `_createLoadBalancerConfig`, `_loadSecrets`) in isolation using specific Jest assertions (`expect().toEqual()`, etc.).
    - **Contract Tests (`contract-tests.test.ts`):** Verify specific interface behaviors and interactions (e.g., conditional `TerraformVariable` creation for image tags) using specific assertions and focused synthesis checks.
    - **Fixtures (`fixtures/`):** Retained for complex mock configurator functions not suitable for factories.
