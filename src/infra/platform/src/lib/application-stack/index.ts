import { TerraformOutput, TerraformVariable, Token } from 'cdktf'
import { Construct } from 'constructs'
import { Statement } from 'iam-floyd'

import { CloudwatchLogGroup } from '@cdktf/provider-aws/lib/cloudwatch-log-group'
import { DataAwsIamPolicyDocument } from '@cdktf/provider-aws/lib/data-aws-iam-policy-document'
import { DataAwsSecretsmanagerSecret } from '@cdktf/provider-aws/lib/data-aws-secretsmanager-secret'
import { DataAwsSecretsmanagerSecretVersion } from '@cdktf/provider-aws/lib/data-aws-secretsmanager-secret-version'
import { IamPolicy } from '@cdktf/provider-aws/lib/iam-policy'
import { IamRole } from '@cdktf/provider-aws/lib/iam-role'
import { IamRolePolicyAttachment } from '@cdktf/provider-aws/lib/iam-role-policy-attachment'
import { Route53Record } from '@cdktf/provider-aws/lib/route53-record'
import { Route53Zone } from '@cdktf/provider-aws/lib/route53-zone'
import { SecurityGroupRule } from '@cdktf/provider-aws/lib/security-group-rule'

import { Environment, generateIAMPolicyFromStatements } from '@nvinfra/common'
import { EcsClusterWithDNS } from '@nvinfra/common/constructs'
import {
  BaseStack,
  BaseStackProps,
  EcrRepo,
} from '@nvinfra/common/constructs/stacks'
import { assumeRolePolicyStatement } from '@nvinfra/common/deps'
import { DatabaseInfo, Network } from '@nvinfra/common/interfaces'
import { ArnUtils } from '@nvinfra/common/lib'

import { HclAcm } from '../../../generated/modules/hcl-acm'
import { HclEcsTaskDefinition } from '../../../generated/modules/hcl-ecs-task-definition'
import {
  HclNvFgSvc,
  HclNvFgSvcConfig,
} from '../../../generated/modules/hcl-nv-fg-svc'
import {
  HclNvSvcAlb,
  HclNvSvcAlbConfig,
} from '../../../generated/modules/hcl-nv-svc-alb'
import {
  FargateAppConfig,
  FargateAppTaskDefConfig,
  FargateAppTaskDefConfiguratorProps,
  ServiceLoadBalancerConfig,
  TaskDefDeps,
} from './configurators'

export interface ApplicationStackProps<
  ECSApps extends string,
  Clusters extends string,
  Deps extends TaskDefDeps,
> extends BaseStackProps {
  // Infrastructure configuration
  region: string
  environment: Environment
  network: Network
  ecsTaskExecutionRoleArn: string
  dns: Route53Zone

  /**
   * Map of ECS applications to the URL of the repository where the
   * image is hosted.
   */
  repositories: Record<ECSApps, EcrRepo>

  /**
   * Clusters available to this stack
   */
  ecsClusters: Record<Clusters, EcsClusterWithDNS>

  /**
   * Input parameters for the taskdef configurators
   */
  taskDefConfiguratorProps: Omit<
    FargateAppTaskDefConfiguratorProps<Deps>,
    // FargateAppTaskDefConfiguratorProps expects secrets to be an object
    // containing the ARN & value of the secret. We fetch that within the
    // ApplicationStack and pass that to the configurator functions instead.
    'secrets'
  > & {
    secrets: {
      [K in NonNullable<Deps['secrets']>]: { name: string }
    }
  }

  /**
   * A map of ECS applications to their configuration.
   */
  appConfigs: Record<ECSApps, FargateAppConfig<Clusters, Deps>>

  /**
   * This is used to create a variable for the image tag for our
   * service-only deployments. This also creates a corresponding output
   * so that the image tag can be used in redeployments.
   */
  imageTagVarNames: Record<ECSApps, string>
}

export class ApplicationStack<
  ECSApps extends string,
  Clusters extends string,
  Deps extends TaskDefDeps,
> extends BaseStack {
  public readonly ecsApplications: Record<ECSApps, FargateApp>

  constructor(
    scope: Construct,
    props: ApplicationStackProps<ECSApps, Clusters, Deps>,
  ) {
    super(scope, 'application-stack', props)

    // TODO: Move this to AccountEssentialsStack
    const certificate = new HclAcm(this, 'ssl-certificate', {
      domainName: props.dns.name,
      zoneId: props.dns.zoneId,
      validationMethod: 'DNS',
      waitForValidation: false,
      subjectAlternativeNames: [`*.${props.dns.name}`],
    })

    this.ecsApplications = this._createECSApps(props, certificate.arnOutput)
  }

  private _createECSApps(
    props: ApplicationStackProps<ECSApps, Clusters, Deps>,
    sslCertificateArn: string,
  ): Record<ECSApps, FargateApp> {
    const ecsTaskAssumeRolePolicy = new DataAwsIamPolicyDocument(
      this,
      'ecs-tasks-assume-role-policy',
      {
        statement: [assumeRolePolicyStatement('ecs-tasks')],
      },
    )

    const ecsAppCollector: Partial<Record<ECSApps, FargateApp>> = {}
    const outputNameCollector: Partial<
      Record<
        string,
        {
          outputName: string
          value: string
        }
      >
    > = {}

    const taskConfiguratorProps: FargateAppTaskDefConfiguratorProps<Deps> = {
      ...props.taskDefConfiguratorProps,
      secrets: this._loadSecrets(props.taskDefConfiguratorProps.secrets),
    } as FargateAppTaskDefConfiguratorProps<Deps>

    // Grant the ECS task execution role access to read all secrets referenced by apps
    // TODO:(garvit): Get rid of global task execution role, make task specific execution role and give access only to needed secrets
    this._grantSecretsReadAccessToEcsExecutionRole(props, taskConfiguratorProps)

    for (const ecsAppName of Object.keys(props.appConfigs) as ECSApps[]) {
      const serviceConfig = props.appConfigs[ecsAppName].service
      const taskDefConfig = props.appConfigs[ecsAppName].taskDef(
        taskConfiguratorProps,
      )
      // If taskDefConfig does not define an image tag, we create a variable
      // so that the image tag can be set at deploy time.
      const imageTag =
        taskDefConfig.imageTag ??
        new TerraformVariable(this, props.imageTagVarNames[ecsAppName], {
          type: 'string',
          nullable: false,
          description: `The tag of the ${ecsAppName} image to be used to deploy`,
        }).stringValue

      // If we create a variable, we also create a corresponding output so it
      // can be used in redeployments.
      if (!taskDefConfig.imageTag) {
        outputNameCollector[ecsAppName] = {
          outputName: props.imageTagVarNames[ecsAppName],
          value: imageTag,
        }
      }

      ecsAppCollector[ecsAppName] = new FargateApp(
        this,
        `ecs-app-${ecsAppName}`,
        {
          region: props.region,
          taskDef: {
            taskExecutionRoleArn: props.ecsTaskExecutionRoleArn,
            assumeRolePolicy: ecsTaskAssumeRolePolicy,
            containerImage: `${props.repositories[ecsAppName].repositoryUrl}:${imageTag}`,
            taskRole: taskDefConfig.taskRole,
            taskDefinition: taskDefConfig.taskDefinition,
          },
          logGroup: props.appConfigs[ecsAppName].logging.logGroup,
          service: {
            ...serviceConfig,
            containerName: taskDefConfig.taskDefinition.containerName,
            containerPort:
              taskDefConfig.taskDefinition.portMappings.app.containerPort,
            ecsClusterArn: props.ecsClusters[serviceConfig.cluster].arn,
            ecsClusterName: props.ecsClusters[serviceConfig.cluster].name,
            serviceDiscoveryNamespaceId:
              props.ecsClusters[serviceConfig.useServiceDiscoveryNamespaceOf]
                .privateDNSNamespace.id,
            desiredCount: serviceConfig.deployment?.desiredCount,
            deploymentMaximumPercent:
              serviceConfig.deployment?.deploymentMaximumPercent,
            deploymentMinimumHealthyPercent:
              serviceConfig.deployment?.deploymentMinimumHealthyPercent,
            namePrefix: serviceConfig.namePrefix,
            privateSubnets: props.network.PrivateSubnets(),
            vpcId: props.network.VPC().id,
            securityGroups: [props.network.SecurityGroups().default.id],
            tags: serviceConfig.tags,
          },
          http: serviceConfig.deployment?.http
            ? {
                loadBalancer: this._createLoadBalancerConfig(
                  props.environment,
                  props.network,
                  sslCertificateArn,
                  serviceConfig.deployment.http.loadBalancer,
                ),
                endpoint: serviceConfig.deployment.http.endpoint,
                zone: props.dns,
              }
            : undefined,
        },
      )
    }

    new ApplicationStackOutput(
      this,
      outputNameCollector as Record<
        string,
        {
          outputName: string
          value: string
        }
      >,
    )

    return ecsAppCollector as Record<ECSApps, FargateApp>
  }

  /**
   * Loads secrets from AWS Secrets Manager and returns a map of secrets to
   * their arn and value.
   *
   * @param names - A map of secret symbol to their names in Secrets Manager
   * @returns A map of secrets to their arn and value
   */
  private _loadSecrets(
    names: Record<string, { name: string }>,
  ): Record<string, { arn: string; value: string }> {
    const collector: Partial<Record<string, { arn: string; value: string }>> =
      {}

    for (const [secretName, { name }] of Object.entries(names) as [
      string,
      { name: string },
    ][]) {
      const secret = new DataAwsSecretsmanagerSecret(
        this,
        `secret-${String(secretName)}`,
        {
          name,
        },
      )

      collector[secretName] = {
        arn: secret.arn,
        value: new DataAwsSecretsmanagerSecretVersion(
          this,
          `secret-version-${String(secretName)}`,
          {
            secretId: secret.id,
          },
        ).secretString,
      }
    }

    return collector as Record<string, { arn: string; value: string }>
  }

  /**
   * Grants the ECS task execution role permission to read all AWS Secrets Manager
   * secrets referenced by the configured applications. This includes:
   * - Secrets provided via `secrets` in task configurator props
   * - Database credential secrets referenced via `databases` in task configurator props
   *
   * Throws an error if the execution role name cannot be derived from the provided ARN.
   */
  private _grantSecretsReadAccessToEcsExecutionRole(
    props: ApplicationStackProps<ECSApps, Clusters, Deps>,
    taskConfiguratorProps: FargateAppTaskDefConfiguratorProps<Deps>,
  ): void {
    const secrets: Record<string, { arn: string; value: string }> | undefined =
      taskConfiguratorProps.secrets
    if (!secrets) {
      return
    }

    const secretArns: string[] = Object.values(secrets).map((s) => s.arn)

    // Include Secrets Manager ARNs for database credentials
    if (taskConfiguratorProps.databases) {
      const databases: Record<string, DatabaseInfo> =
        taskConfiguratorProps.databases
      const dbSecretArns = Object.values(databases)
        .map((dbInfo) => {
          const credentialSecretArns = Object.values(
            dbInfo.credentials ?? {},
          ).map((cred) => cred.password.arn)
          return [
            ...credentialSecretArns,
            dbInfo.masterUserDetails.password.arn,
          ]
        })
        .flat()
      secretArns.push(...dbSecretArns)
    }

    if (secretArns.length === 0) {
      return
    }

    const ecsExecRoleName = ArnUtils.getIamRoleNameFromArn(
      props.ecsTaskExecutionRoleArn,
    )

    const allowGetSecretValues = new Statement.Secretsmanager()
      .allow()
      .toGetSecretValue()
    for (const arn of secretArns) {
      allowGetSecretValues.on(arn)
    }

    new IamRolePolicyAttachment(
      this,
      'ecs-exec-role-secrets-policy-attachment',
      {
        role: ecsExecRoleName,
        policyArn: new IamPolicy(this, 'ecs-exec-role-secrets-policy', {
          name: 'ecs-exec-role-secrets-policy',
          policy: generateIAMPolicyFromStatements(allowGetSecretValues),
        }).arn,
      },
    )
  }

  private _createLoadBalancerConfig(
    environment: Environment,
    network: Network,
    sslCertificateArn: string,
    cfg: ServiceLoadBalancerConfig,
  ): HclNvSvcAlbConfig {
    return {
      namePrefix:
        // For legacy reasons, production alb names are just the
        // service name prefix, but for other environments, we
        // prefix the service name prefix with the environment.
        environment == Environment.Production
          ? cfg.namePrefix
          : `${environment}-${cfg.namePrefix}`,
      vpcId: network.VPC().id,
      securityGroups: [network.SecurityGroups().default.id],
      publicSubnets: network.PublicSubnets(),
      httpPorts: {
        force_https: {
          listener_port: 80,
          host: '#{host}',
          path: '/#{path}',
          target_port: '443',
          protocol: 'HTTPS',
          query: '#{query}',
          status_code: 'HTTP_301',
        },
      },
      httpsPorts: {
        force_https: {
          listener_port: 443,
        },
      },
      sslPolicy: 'ELBSecurityPolicy-2016-08',
      defaultCertificateArn: sslCertificateArn,
      loadBalancingAlgorithmType: 'round_robin',
      targetGroupDeregistrationDelay: cfg.targetGroup.deregistrationDelay,
      targetGroupHealthCheckEnabled: true,
      targetGroupHealthCheckInterval: cfg.targetGroup.healthCheck.interval,
      targetGroupHealthCheckPath: cfg.targetGroup.healthCheck.path,
      targetGroupHealthCheckTimeout: cfg.targetGroup.healthCheck.timeout,
      targetGroupHealthCheckHealthyThreshold:
        cfg.targetGroup.healthCheck.healthyThreshold,
      targetGroupHealthCheckUnhealthyThreshold:
        cfg.targetGroup.healthCheck.unhealthyThreshold,
      targetGroupHealthCheckMatcher: cfg.targetGroup.healthCheck.matcher,
    }
  }
}

interface FargateAppProps {
  region: string
  logGroup: {
    name: string
    tags: {
      Environment: string
      Application: string
    }
  }
  taskDef: Omit<FargateAppTaskDefConfig, 'imageTag'> & {
    containerImage: string
    taskExecutionRoleArn: string
    assumeRolePolicy: DataAwsIamPolicyDocument
  }
  service: Omit<
    HclNvFgSvcConfig,
    | 'taskDefinitionArn'
    | 'defaultCertificateArn'
    | 'createLbAccessSgRule'
    | 'lbAccessSg'
    | 'lbHttpTgArns'
    | 'lbHttpTgPorts'
  > & {
    // Ensure at least one security group is provided
    securityGroups: [string, ...string[]]
  }
  http?: {
    loadBalancer: HclNvSvcAlbConfig
    endpoint: string
    zone: Route53Zone
  }
}

class FargateApp extends Construct {
  constructor(scope: Construct, id: string, props: FargateAppProps) {
    super(scope, id)

    const logGroup = new CloudwatchLogGroup(this, 'log-group', props.logGroup)

    const taskRole = props.taskDef.taskRole
      ? new IamRole(this, 'task-role', {
          name: props.taskDef.taskRole.name,
          assumeRolePolicy: props.taskDef.assumeRolePolicy.json,
        })
      : undefined

    if (taskRole) {
      new IamRolePolicyAttachment(this, 'task-role-policy-attachment', {
        role: taskRole.name,
        policyArn: new IamPolicy(this, 'task-role-policy', {
          policy: generateIAMPolicyFromStatements(
            ...props.taskDef.taskRole!.policyStatements,
          ),
        }).arn,
      })
    }

    const taskDefinition = new HclEcsTaskDefinition(this, 'task-definition', {
      ...props.taskDef.taskDefinition,
      containerImage: props.taskDef.containerImage,
      portMappings: Object.values(
        props.taskDef.taskDefinition.portMappings,
      ).filter(Boolean),
      logConfiguration: {
        logDriver: 'awslogs',
        options: {
          'awslogs-group': logGroup.name,
          'awslogs-region': props.region,
          'awslogs-stream-prefix':
            props.taskDef.taskDefinition.logConfiguration.options
              .awslogsStreamPrefix,
        },
      },
      taskExecutionRoleArn: props.taskDef.taskExecutionRoleArn,
      taskRoleArn: taskRole?.arn,
    })
    const loadBalancer = props.http
      ? new HclNvSvcAlb(this, 'load-balancer', props.http.loadBalancer)
      : undefined

    new HclNvFgSvc(this, 'service', {
      taskDefinitionArn: taskDefinition.arnOutput,
      ...props.service,
      createLbAccessSgRule: loadBalancer ? true : undefined,
      lbAccessSg: loadBalancer?.accessSgIdOutput,
      lbHttpTgArns: loadBalancer
        ? Token.asList(loadBalancer.httpTgsArnsOutput)
        : undefined,
      lbHttpTgPorts: loadBalancer
        ? Token.asList(loadBalancer.httpTgsPortsOutput)
        : undefined,
    })

    // Add a security group rule for ALB to access the service.
    // For now, we only create it for the first security group in the service
    // which is the default security group right now.
    if (props.http) {
      if (!loadBalancer) {
        throw new Error(
          'Impossible state: load balancer must have been initialized since http props are defined',
        )
      }
      new SecurityGroupRule(this, 'allow_alb_ingress_default', {
        type: 'ingress',
        securityGroupId: props.service.securityGroups[0], // Using the first security group from the service
        protocol: '-1', // All protocols
        fromPort: 0, // All ports
        toPort: 0, // All ports
        sourceSecurityGroupId: loadBalancer.accessSgIdOutput,
      })
      new Route53Record(this, 'dns_record', {
        name: props.http.endpoint,
        zoneId: props.http.zone.zoneId,
        type: 'A',
        alias: {
          name: loadBalancer.dnsNameOutput,
          zoneId: loadBalancer.zoneIdOutput,
          evaluateTargetHealth: true,
        },
      })
    }
  }
}

class ApplicationStackOutput extends Construct {
  constructor(
    scope: Construct,
    // map of service name to an object containing the value and the name
    // to use for the output
    imageTagVarNames: Record<
      string,
      {
        outputName: string
        value: string
      }
    >,
  ) {
    super(scope, 'outputs')

    for (const [serviceName, { outputName, value }] of Object.entries(
      imageTagVarNames,
    )) {
      new TerraformOutput(this, outputName, {
        value,
        description: `The tag of the ${serviceName} image currently deployed`,
        staticId: true,
      })
    }
  }
}
