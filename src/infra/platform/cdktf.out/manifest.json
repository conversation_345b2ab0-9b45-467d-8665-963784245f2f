{"stacks": {"default-dev-metaflow-common": {"annotations": [], "constructPath": "default-dev-metaflow-common", "dependencies": [], "name": "default-dev-metaflow-common", "stackMetadataPath": "stacks/default-dev-metaflow-common/metadata.json", "synthesizedStackPath": "stacks/default-dev-metaflow-common/cdk.tf.json", "workingDirectory": "stacks/default-dev-metaflow-common"}, "default-dev-metaflow-computation": {"annotations": [], "constructPath": "default-dev-metaflow-computation", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2"], "name": "default-dev-metaflow-computation", "stackMetadataPath": "stacks/default-dev-metaflow-computation/metadata.json", "synthesizedStackPath": "stacks/default-dev-metaflow-computation/cdk.tf.json", "workingDirectory": "stacks/default-dev-metaflow-computation"}, "default-dev-metaflow-datastore": {"annotations": [], "constructPath": "default-dev-metaflow-datastore", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2", "default-dev-metaflow-common"], "name": "default-dev-metaflow-datastore", "stackMetadataPath": "stacks/default-dev-metaflow-datastore/metadata.json", "synthesizedStackPath": "stacks/default-dev-metaflow-datastore/cdk.tf.json", "workingDirectory": "stacks/default-dev-metaflow-datastore"}, "default-dev-metaflow-metadata-service": {"annotations": [], "constructPath": "default-dev-metaflow-metadata-service", "dependencies": ["default-dev-metaflow-datastore", "default-dev-metaflow-common", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-dev-metaflow-metadata-service", "stackMetadataPath": "stacks/default-dev-metaflow-metadata-service/metadata.json", "synthesizedStackPath": "stacks/default-dev-metaflow-metadata-service/cdk.tf.json", "workingDirectory": "stacks/default-dev-metaflow-metadata-service"}, "default-dev-metaflow-observability": {"annotations": [], "constructPath": "default-dev-metaflow-observability", "dependencies": ["default-dev-metaflow-computation", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-dev-metaflow-observability", "stackMetadataPath": "stacks/default-dev-metaflow-observability/metadata.json", "synthesizedStackPath": "stacks/default-dev-metaflow-observability/cdk.tf.json", "workingDirectory": "stacks/default-dev-metaflow-observability"}, "default-dev-metaflow-outputs": {"annotations": [], "constructPath": "default-dev-metaflow-outputs", "dependencies": ["default-dev-metaflow-computation", "default-dev-metaflow-datastore", "default-dev-metaflow-common", "default-dev-metaflow-metadata-service", "default-dev-metaflow-step-functions"], "name": "default-dev-metaflow-outputs", "stackMetadataPath": "stacks/default-dev-metaflow-outputs/metadata.json", "synthesizedStackPath": "stacks/default-dev-metaflow-outputs/cdk.tf.json", "workingDirectory": "stacks/default-dev-metaflow-outputs"}, "default-dev-metaflow-step-functions": {"annotations": [], "constructPath": "default-dev-metaflow-step-functions", "dependencies": ["default-dev-metaflow-common", "default-dev-metaflow-computation", "default-dev-metaflow-datastore"], "name": "default-dev-metaflow-step-functions", "stackMetadataPath": "stacks/default-dev-metaflow-step-functions/metadata.json", "synthesizedStackPath": "stacks/default-dev-metaflow-step-functions/cdk.tf.json", "workingDirectory": "stacks/default-dev-metaflow-step-functions"}, "default-dev-metaflow-ui": {"annotations": [], "constructPath": "default-dev-metaflow-ui", "dependencies": ["default-dev-metaflow-datastore", "default-dev-metaflow-common", "default-dev-metaflow-metadata-service", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-dev-metaflow-ui", "stackMetadataPath": "stacks/default-dev-metaflow-ui/metadata.json", "synthesizedStackPath": "stacks/default-dev-metaflow-ui/cdk.tf.json", "workingDirectory": "stacks/default-dev-metaflow-ui"}, "default-dev-nirvanamq-events": {"annotations": [], "constructPath": "default-dev-nirvanamq-events", "dependencies": ["default-root-nirvanamq-ecr", "default-production-coreinfra-container-compute-us-east-2", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-dev-nirvanamq-events", "stackMetadataPath": "stacks/default-dev-nirvanamq-events/metadata.json", "synthesizedStackPath": "stacks/default-dev-nirvanamq-events/cdk.tf.json", "workingDirectory": "stacks/default-dev-nirvanamq-events"}, "default-dev-spark-telematics": {"annotations": [], "constructPath": "default-dev-spark-telematics", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2"], "name": "default-dev-spark-telematics", "stackMetadataPath": "stacks/default-dev-spark-telematics/metadata.json", "synthesizedStackPath": "stacks/default-dev-spark-telematics/cdk.tf.json", "workingDirectory": "stacks/default-dev-spark-telematics"}, "default-production-airbyte-airbyte": {"annotations": [], "constructPath": "default-production-airbyte-airbyte", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2"], "name": "default-production-airbyte-airbyte", "stackMetadataPath": "stacks/default-production-airbyte-airbyte/metadata.json", "synthesizedStackPath": "stacks/default-production-airbyte-airbyte/cdk.tf.json", "workingDirectory": "stacks/default-production-airbyte-airbyte"}, "default-production-coreinfra-account-essentials": {"annotations": [], "constructPath": "default-production-coreinfra-account-essentials", "dependencies": [], "name": "default-production-coreinfra-account-essentials", "stackMetadataPath": "stacks/default-production-coreinfra-account-essentials/metadata.json", "synthesizedStackPath": "stacks/default-production-coreinfra-account-essentials/cdk.tf.json", "workingDirectory": "stacks/default-production-coreinfra-account-essentials"}, "default-production-coreinfra-bastion-us-east-2": {"annotations": [], "constructPath": "default-production-coreinfra-bastion-us-east-2", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2"], "name": "default-production-coreinfra-bastion-us-east-2", "stackMetadataPath": "stacks/default-production-coreinfra-bastion-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-production-coreinfra-bastion-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-production-coreinfra-bastion-us-east-2"}, "default-production-coreinfra-container-compute-us-east-2": {"annotations": [], "constructPath": "default-production-coreinfra-container-compute-us-east-2", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2"], "name": "default-production-coreinfra-container-compute-us-east-2", "stackMetadataPath": "stacks/default-production-coreinfra-container-compute-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-production-coreinfra-container-compute-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-production-coreinfra-container-compute-us-east-2"}, "default-production-coreinfra-legacy-network-us-east-2": {"annotations": [], "constructPath": "default-production-coreinfra-legacy-network-us-east-2", "dependencies": [], "name": "default-production-coreinfra-legacy-network-us-east-2", "stackMetadataPath": "stacks/default-production-coreinfra-legacy-network-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-production-coreinfra-legacy-network-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-production-coreinfra-legacy-network-us-east-2"}, "default-production-llm-agents-supabase-us-east-2": {"annotations": [], "constructPath": "default-production-llm-agents-supabase-us-east-2", "dependencies": [], "name": "default-production-llm-agents-supabase-us-east-2", "stackMetadataPath": "stacks/default-production-llm-agents-supabase-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-production-llm-agents-supabase-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-production-llm-agents-supabase-us-east-2"}, "default-production-metaflow-common": {"annotations": [], "constructPath": "default-production-metaflow-common", "dependencies": [], "name": "default-production-metaflow-common", "stackMetadataPath": "stacks/default-production-metaflow-common/metadata.json", "synthesizedStackPath": "stacks/default-production-metaflow-common/cdk.tf.json", "workingDirectory": "stacks/default-production-metaflow-common"}, "default-production-metaflow-computation": {"annotations": [], "constructPath": "default-production-metaflow-computation", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2"], "name": "default-production-metaflow-computation", "stackMetadataPath": "stacks/default-production-metaflow-computation/metadata.json", "synthesizedStackPath": "stacks/default-production-metaflow-computation/cdk.tf.json", "workingDirectory": "stacks/default-production-metaflow-computation"}, "default-production-metaflow-datastore": {"annotations": [], "constructPath": "default-production-metaflow-datastore", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2", "default-production-metaflow-common"], "name": "default-production-metaflow-datastore", "stackMetadataPath": "stacks/default-production-metaflow-datastore/metadata.json", "synthesizedStackPath": "stacks/default-production-metaflow-datastore/cdk.tf.json", "workingDirectory": "stacks/default-production-metaflow-datastore"}, "default-production-metaflow-metadata-service": {"annotations": [], "constructPath": "default-production-metaflow-metadata-service", "dependencies": ["default-production-metaflow-datastore", "default-production-metaflow-common", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-production-metaflow-metadata-service", "stackMetadataPath": "stacks/default-production-metaflow-metadata-service/metadata.json", "synthesizedStackPath": "stacks/default-production-metaflow-metadata-service/cdk.tf.json", "workingDirectory": "stacks/default-production-metaflow-metadata-service"}, "default-production-metaflow-observability": {"annotations": [], "constructPath": "default-production-metaflow-observability", "dependencies": ["default-production-metaflow-computation", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-production-metaflow-observability", "stackMetadataPath": "stacks/default-production-metaflow-observability/metadata.json", "synthesizedStackPath": "stacks/default-production-metaflow-observability/cdk.tf.json", "workingDirectory": "stacks/default-production-metaflow-observability"}, "default-production-metaflow-outputs": {"annotations": [], "constructPath": "default-production-metaflow-outputs", "dependencies": ["default-production-metaflow-computation", "default-production-metaflow-datastore", "default-production-metaflow-common", "default-production-metaflow-metadata-service", "default-production-metaflow-step-functions"], "name": "default-production-metaflow-outputs", "stackMetadataPath": "stacks/default-production-metaflow-outputs/metadata.json", "synthesizedStackPath": "stacks/default-production-metaflow-outputs/cdk.tf.json", "workingDirectory": "stacks/default-production-metaflow-outputs"}, "default-production-metaflow-pipeline-manager": {"annotations": [], "constructPath": "default-production-metaflow-pipeline-manager", "dependencies": ["default-production-metaflow-datastore", "default-production-storage-rds-stack-us-east-2", "default-production-metaflow-common", "default-production-coreinfra-container-compute-us-east-2", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-production-metaflow-pipeline-manager", "stackMetadataPath": "stacks/default-production-metaflow-pipeline-manager/metadata.json", "synthesizedStackPath": "stacks/default-production-metaflow-pipeline-manager/cdk.tf.json", "workingDirectory": "stacks/default-production-metaflow-pipeline-manager"}, "default-production-metaflow-step-functions": {"annotations": [], "constructPath": "default-production-metaflow-step-functions", "dependencies": ["default-production-metaflow-common", "default-production-metaflow-computation", "default-production-metaflow-datastore"], "name": "default-production-metaflow-step-functions", "stackMetadataPath": "stacks/default-production-metaflow-step-functions/metadata.json", "synthesizedStackPath": "stacks/default-production-metaflow-step-functions/cdk.tf.json", "workingDirectory": "stacks/default-production-metaflow-step-functions"}, "default-production-metaflow-ui": {"annotations": [], "constructPath": "default-production-metaflow-ui", "dependencies": ["default-production-metaflow-datastore", "default-production-metaflow-common", "default-production-metaflow-metadata-service", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-production-metaflow-ui", "stackMetadataPath": "stacks/default-production-metaflow-ui/metadata.json", "synthesizedStackPath": "stacks/default-production-metaflow-ui/cdk.tf.json", "workingDirectory": "stacks/default-production-metaflow-ui"}, "default-production-ml-models": {"annotations": [], "constructPath": "default-production-ml-models", "dependencies": [], "name": "default-production-ml-models", "stackMetadataPath": "stacks/default-production-ml-models/metadata.json", "synthesizedStackPath": "stacks/default-production-ml-models/cdk.tf.json", "workingDirectory": "stacks/default-production-ml-models"}, "default-production-nirvanamq-events": {"annotations": [], "constructPath": "default-production-nirvanamq-events", "dependencies": ["default-root-nirvanamq-ecr", "default-production-coreinfra-container-compute-us-east-2", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-production-nirvanamq-events", "stackMetadataPath": "stacks/default-production-nirvanamq-events/metadata.json", "synthesizedStackPath": "stacks/default-production-nirvanamq-events/cdk.tf.json", "workingDirectory": "stacks/default-production-nirvanamq-events"}, "default-production-runs-on-bastion": {"annotations": [], "constructPath": "default-production-runs-on-bastion", "dependencies": ["default-production-runs-on-runs-on-network"], "name": "default-production-runs-on-bastion", "stackMetadataPath": "stacks/default-production-runs-on-bastion/metadata.json", "synthesizedStackPath": "stacks/default-production-runs-on-bastion/cdk.tf.json", "workingDirectory": "stacks/default-production-runs-on-bastion"}, "default-production-runs-on-runs-on-ecs": {"annotations": [], "constructPath": "default-production-runs-on-runs-on-ecs", "dependencies": ["default-production-runs-on-runs-on-network"], "name": "default-production-runs-on-runs-on-ecs", "stackMetadataPath": "stacks/default-production-runs-on-runs-on-ecs/metadata.json", "synthesizedStackPath": "stacks/default-production-runs-on-runs-on-ecs/cdk.tf.json", "workingDirectory": "stacks/default-production-runs-on-runs-on-ecs"}, "default-production-runs-on-runs-on-network": {"annotations": [], "constructPath": "default-production-runs-on-runs-on-network", "dependencies": [], "name": "default-production-runs-on-runs-on-network", "stackMetadataPath": "stacks/default-production-runs-on-runs-on-network/metadata.json", "synthesizedStackPath": "stacks/default-production-runs-on-runs-on-network/cdk.tf.json", "workingDirectory": "stacks/default-production-runs-on-runs-on-network"}, "default-production-spark-telematics": {"annotations": [], "constructPath": "default-production-spark-telematics", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2"], "name": "default-production-spark-telematics", "stackMetadataPath": "stacks/default-production-spark-telematics/metadata.json", "synthesizedStackPath": "stacks/default-production-spark-telematics/cdk.tf.json", "workingDirectory": "stacks/default-production-spark-telematics"}, "default-production-storage-rds-stack-us-east-2": {"annotations": [], "constructPath": "default-production-storage-rds-stack-us-east-2", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2"], "name": "default-production-storage-rds-stack-us-east-2", "stackMetadataPath": "stacks/default-production-storage-rds-stack-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-production-storage-rds-stack-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-production-storage-rds-stack-us-east-2"}, "default-root-athens-nirvana": {"annotations": [], "constructPath": "default-root-athens-nirvana", "dependencies": ["default-production-runs-on-runs-on-network", "default-production-runs-on-runs-on-ecs"], "name": "default-root-athens-nirvana", "stackMetadataPath": "stacks/default-root-athens-nirvana/metadata.json", "synthesizedStackPath": "stacks/default-root-athens-nirvana/cdk.tf.json", "workingDirectory": "stacks/default-root-athens-nirvana"}, "default-root-coreinfra-alt-domains-us-east-2": {"annotations": [], "constructPath": "default-root-coreinfra-alt-domains-us-east-2", "dependencies": [], "name": "default-root-coreinfra-alt-domains-us-east-2", "stackMetadataPath": "stacks/default-root-coreinfra-alt-domains-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-root-coreinfra-alt-domains-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-root-coreinfra-alt-domains-us-east-2"}, "default-root-coreinfra-ecr-us-east-2": {"annotations": [], "constructPath": "default-root-coreinfra-ecr-us-east-2", "dependencies": [], "name": "default-root-coreinfra-ecr-us-east-2", "stackMetadataPath": "stacks/default-root-coreinfra-ecr-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-root-coreinfra-ecr-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-root-coreinfra-ecr-us-east-2"}, "default-root-coreinfra-llm-agent-web-servers-us-east-2": {"annotations": [], "constructPath": "default-root-coreinfra-llm-agent-web-servers-us-east-2", "dependencies": [], "name": "default-root-coreinfra-llm-agent-web-servers-us-east-2", "stackMetadataPath": "stacks/default-root-coreinfra-llm-agent-web-servers-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-root-coreinfra-llm-agent-web-servers-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-root-coreinfra-llm-agent-web-servers-us-east-2"}, "default-root-coreinfra-mcp-servers-us-east-2": {"annotations": [], "constructPath": "default-root-coreinfra-mcp-servers-us-east-2", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2"], "name": "default-root-coreinfra-mcp-servers-us-east-2", "stackMetadataPath": "stacks/default-root-coreinfra-mcp-servers-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-root-coreinfra-mcp-servers-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-root-coreinfra-mcp-servers-us-east-2"}, "default-root-nirvanamq-ecr": {"annotations": [], "constructPath": "default-root-nirvanamq-ecr", "dependencies": [], "name": "default-root-nirvanamq-ecr", "stackMetadataPath": "stacks/default-root-nirvanamq-ecr/metadata.json", "synthesizedStackPath": "stacks/default-root-nirvanamq-ecr/cdk.tf.json", "workingDirectory": "stacks/default-root-nirvanamq-ecr"}, "default-root-snowflake-s3stages": {"annotations": [], "constructPath": "default-root-snowflake-s3stages", "dependencies": [], "name": "default-root-snowflake-s3stages", "stackMetadataPath": "stacks/default-root-snowflake-s3stages/metadata.json", "synthesizedStackPath": "stacks/default-root-snowflake-s3stages/cdk.tf.json", "workingDirectory": "stacks/default-root-snowflake-s3stages"}, "default-root-snowflake-snowpipes": {"annotations": [], "constructPath": "default-root-snowflake-snowpipes", "dependencies": ["default-root-snowflake-s3stages"], "name": "default-root-snowflake-snowpipes", "stackMetadataPath": "stacks/default-root-snowflake-snowpipes/metadata.json", "synthesizedStackPath": "stacks/default-root-snowflake-snowpipes/cdk.tf.json", "workingDirectory": "stacks/default-root-snowflake-snowpipes"}, "default-staging-backend-application-stack-us-east-2": {"annotations": [], "constructPath": "default-staging-backend-application-stack-us-east-2", "dependencies": ["default-staging-coreinfra-account-essentials", "default-staging-storage-rds-stack-us-east-2", "default-root-coreinfra-ecr-us-east-2", "default-staging-coreinfra-container-compute-us-east-2", "default-staging-coreinfra-legacy-network-us-east-2", "default-staging-backend-env-scoped-buckets-us-east-2"], "name": "default-staging-backend-application-stack-us-east-2", "stackMetadataPath": "stacks/default-staging-backend-application-stack-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-staging-backend-application-stack-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-staging-backend-application-stack-us-east-2"}, "default-staging-backend-env-scoped-buckets-us-east-2": {"annotations": [], "constructPath": "default-staging-backend-env-scoped-buckets-us-east-2", "dependencies": [], "name": "default-staging-backend-env-scoped-buckets-us-east-2", "stackMetadataPath": "stacks/default-staging-backend-env-scoped-buckets-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-staging-backend-env-scoped-buckets-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-staging-backend-env-scoped-buckets-us-east-2"}, "default-staging-coreinfra-account-essentials": {"annotations": [], "constructPath": "default-staging-coreinfra-account-essentials", "dependencies": [], "name": "default-staging-coreinfra-account-essentials", "stackMetadataPath": "stacks/default-staging-coreinfra-account-essentials/metadata.json", "synthesizedStackPath": "stacks/default-staging-coreinfra-account-essentials/cdk.tf.json", "workingDirectory": "stacks/default-staging-coreinfra-account-essentials"}, "default-staging-coreinfra-bastion-us-east-2": {"annotations": [], "constructPath": "default-staging-coreinfra-bastion-us-east-2", "dependencies": ["default-staging-coreinfra-legacy-network-us-east-2"], "name": "default-staging-coreinfra-bastion-us-east-2", "stackMetadataPath": "stacks/default-staging-coreinfra-bastion-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-staging-coreinfra-bastion-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-staging-coreinfra-bastion-us-east-2"}, "default-staging-coreinfra-container-compute-us-east-2": {"annotations": [], "constructPath": "default-staging-coreinfra-container-compute-us-east-2", "dependencies": ["default-staging-coreinfra-legacy-network-us-east-2"], "name": "default-staging-coreinfra-container-compute-us-east-2", "stackMetadataPath": "stacks/default-staging-coreinfra-container-compute-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-staging-coreinfra-container-compute-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-staging-coreinfra-container-compute-us-east-2"}, "default-staging-coreinfra-legacy-network-us-east-2": {"annotations": [], "constructPath": "default-staging-coreinfra-legacy-network-us-east-2", "dependencies": [], "name": "default-staging-coreinfra-legacy-network-us-east-2", "stackMetadataPath": "stacks/default-staging-coreinfra-legacy-network-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-staging-coreinfra-legacy-network-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-staging-coreinfra-legacy-network-us-east-2"}, "default-staging-storage-rds-stack-us-east-2": {"annotations": [], "constructPath": "default-staging-storage-rds-stack-us-east-2", "dependencies": ["default-staging-coreinfra-legacy-network-us-east-2", "default-root-coreinfra-ecr-us-east-2"], "name": "default-staging-storage-rds-stack-us-east-2", "stackMetadataPath": "stacks/default-staging-storage-rds-stack-us-east-2/metadata.json", "synthesizedStackPath": "stacks/default-staging-storage-rds-stack-us-east-2/cdk.tf.json", "workingDirectory": "stacks/default-staging-storage-rds-stack-us-east-2"}, "default-training-metaflow-common": {"annotations": [], "constructPath": "default-training-metaflow-common", "dependencies": [], "name": "default-training-metaflow-common", "stackMetadataPath": "stacks/default-training-metaflow-common/metadata.json", "synthesizedStackPath": "stacks/default-training-metaflow-common/cdk.tf.json", "workingDirectory": "stacks/default-training-metaflow-common"}, "default-training-metaflow-computation": {"annotations": [], "constructPath": "default-training-metaflow-computation", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2"], "name": "default-training-metaflow-computation", "stackMetadataPath": "stacks/default-training-metaflow-computation/metadata.json", "synthesizedStackPath": "stacks/default-training-metaflow-computation/cdk.tf.json", "workingDirectory": "stacks/default-training-metaflow-computation"}, "default-training-metaflow-datastore": {"annotations": [], "constructPath": "default-training-metaflow-datastore", "dependencies": ["default-production-coreinfra-legacy-network-us-east-2", "default-training-metaflow-common"], "name": "default-training-metaflow-datastore", "stackMetadataPath": "stacks/default-training-metaflow-datastore/metadata.json", "synthesizedStackPath": "stacks/default-training-metaflow-datastore/cdk.tf.json", "workingDirectory": "stacks/default-training-metaflow-datastore"}, "default-training-metaflow-metadata-service": {"annotations": [], "constructPath": "default-training-metaflow-metadata-service", "dependencies": ["default-training-metaflow-datastore", "default-training-metaflow-common", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-training-metaflow-metadata-service", "stackMetadataPath": "stacks/default-training-metaflow-metadata-service/metadata.json", "synthesizedStackPath": "stacks/default-training-metaflow-metadata-service/cdk.tf.json", "workingDirectory": "stacks/default-training-metaflow-metadata-service"}, "default-training-metaflow-observability": {"annotations": [], "constructPath": "default-training-metaflow-observability", "dependencies": ["default-training-metaflow-computation", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-training-metaflow-observability", "stackMetadataPath": "stacks/default-training-metaflow-observability/metadata.json", "synthesizedStackPath": "stacks/default-training-metaflow-observability/cdk.tf.json", "workingDirectory": "stacks/default-training-metaflow-observability"}, "default-training-metaflow-outputs": {"annotations": [], "constructPath": "default-training-metaflow-outputs", "dependencies": ["default-training-metaflow-computation", "default-training-metaflow-datastore", "default-training-metaflow-common", "default-training-metaflow-metadata-service", "default-training-metaflow-step-functions"], "name": "default-training-metaflow-outputs", "stackMetadataPath": "stacks/default-training-metaflow-outputs/metadata.json", "synthesizedStackPath": "stacks/default-training-metaflow-outputs/cdk.tf.json", "workingDirectory": "stacks/default-training-metaflow-outputs"}, "default-training-metaflow-step-functions": {"annotations": [], "constructPath": "default-training-metaflow-step-functions", "dependencies": ["default-training-metaflow-common", "default-training-metaflow-computation", "default-training-metaflow-datastore"], "name": "default-training-metaflow-step-functions", "stackMetadataPath": "stacks/default-training-metaflow-step-functions/metadata.json", "synthesizedStackPath": "stacks/default-training-metaflow-step-functions/cdk.tf.json", "workingDirectory": "stacks/default-training-metaflow-step-functions"}, "default-training-metaflow-ui": {"annotations": [], "constructPath": "default-training-metaflow-ui", "dependencies": ["default-training-metaflow-datastore", "default-training-metaflow-common", "default-training-metaflow-metadata-service", "default-production-coreinfra-legacy-network-us-east-2"], "name": "default-training-metaflow-ui", "stackMetadataPath": "stacks/default-training-metaflow-ui/metadata.json", "synthesizedStackPath": "stacks/default-training-metaflow-ui/cdk.tf.json", "workingDirectory": "stacks/default-training-metaflow-ui"}}, "version": "0.20.10"}