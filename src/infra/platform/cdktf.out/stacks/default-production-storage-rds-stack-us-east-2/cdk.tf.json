{"//": {"metadata": {"backend": "s3", "overrides": {"stack": ["terraform"]}, "stackName": "default-production-storage-rds-stack-us-east-2", "version": "0.20.10"}, "outputs": {"default-production-storage-rds-stack-us-east-2": {"cross-stack-output-aws_db_instance.ApplicationDB_49B5873C.address": "cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress", "cross-stack-output-aws_db_instance.ApplicationDB_49B5873C.db_name": "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cdb_name", "cross-stack-output-aws_db_instance.ApplicationDB_49B5873C.password": "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword", "cross-stack-output-aws_db_instance.ApplicationDB_49B5873C.port": "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport", "cross-stack-output-aws_db_instance.ApplicationDB_49B5873C.username": "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername"}}}, "data": {"aws_iam_policy_document": {"backup_policy": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/backup_policy", "uniqueId": "backup_policy"}}, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "principals": [{"identifiers": ["backup.amazonaws.com"], "type": "Service"}]}]}, "rds_assume_role_policy": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/rds_assume_role_policy", "uniqueId": "rds_assume_role_policy"}}, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "principals": [{"identifiers": ["rds.amazonaws.com"], "type": "Service"}]}]}}, "aws_secretsmanager_secret": {"FmcsaDB_FmcsaDBPasswordSecret_73C99AED": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/FmcsaDB/FmcsaDBPasswordSecret", "uniqueId": "FmcsaDB_FmcsaDBPasswordSecret_73C99AED"}}, "name": "fmcsa-db-pass-default"}, "LegacyAppDBPasswordSecret": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/LegacyAppDBPasswordSecret", "uniqueId": "LegacyAppDBPasswordSecret"}}, "name": "postgres-pass-default"}}, "aws_secretsmanager_secret_version": {"FmcsaDB_FmcsaDBPasswordSecretVersion_DA59D801": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/FmcsaDB/FmcsaDBPasswordSecretVersion", "uniqueId": "FmcsaDB_FmcsaDBPasswordSecretVersion_DA59D801"}}, "secret_id": "${data.aws_secretsmanager_secret.FmcsaDB_FmcsaDBPasswordSecret_73C99AED.id}"}, "LegacyAppDBPasswordSecretValue": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/LegacyAppDBPasswordSecretValue", "uniqueId": "LegacyAppDBPasswordSecretValue"}}, "secret_id": "${data.aws_secretsmanager_secret.LegacyAppDBPasswordSecret.id}"}}, "aws_sns_topic": {"ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/rds_alerts/rds_alert_sns_topic", "uniqueId": "ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D"}}, "name": "CW2PD-InternalInfra"}, "NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/rds_alerts/rds_alert_sns_topic", "uniqueId": "NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC"}}, "name": "CW2PD-InternalInfra"}}, "terraform_remote_state": {"cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2": {"backend": "s3", "config": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/production/default-production-coreinfra-legacy-network-us-east-2.json", "region": "us-east-2"}, "workspace": "${terraform.workspace}"}}}, "output": {"cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress": {"sensitive": true, "value": "${aws_db_instance.ApplicationDB_49B5873C.address}"}, "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cdb_name": {"sensitive": true, "value": "${aws_db_instance.ApplicationDB_49B5873C.db_name}"}, "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword": {"sensitive": true, "value": "${aws_db_instance.ApplicationDB_49B5873C.password}"}, "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport": {"sensitive": true, "value": "${aws_db_instance.ApplicationDB_49B5873C.port}"}, "cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername": {"sensitive": true, "value": "${aws_db_instance.ApplicationDB_49B5873C.username}"}}, "provider": {"aws": [{"allowed_account_ids": ["************"], "default_tags": [{"tags": {"environment": "production", "group": "storage", "infraWorkspace": "default", "region": "us-east-2", "stackName": "rds-stack"}}], "region": "us-east-2"}], "postgresql": [{"database": "${aws_db_instance.ApplicationDB_49B5873C.db_name}", "host": "${aws_db_instance.ApplicationDB_49B5873C.address}", "password": "${aws_db_instance.ApplicationDB_49B5873C.password}", "port": "${aws_db_instance.ApplicationDB_49B5873C.port}", "sslmode": "disable", "superuser": false, "username": "${aws_db_instance.ApplicationDB_49B5873C.username}"}], "random": [{}]}, "resource": {"aws_backup_plan": {"ApplicationDB_plan_C9EDBF75": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/plan", "uniqueId": "ApplicationDB_plan_C9EDBF75"}}, "name": "rds_cont_backup_plan", "rule": [{"enable_continuous_backup": true, "lifecycle": {"delete_after": 30}, "rule_name": "rds_cont_backup_rule", "schedule": "cron(0 * ? * * *)", "target_vault_name": "${aws_backup_vault.backup_vault.name}"}]}}, "aws_backup_selection": {"ApplicationDB_selection_CFFF8A13": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/selection", "uniqueId": "ApplicationDB_selection_CFFF8A13"}}, "iam_role_arn": "${aws_iam_role.backup_role.arn}", "name": "rds_cont_backup_selection", "plan_id": "${aws_backup_plan.ApplicationDB_plan_C9EDBF75.id}", "resources": ["${aws_db_instance.ApplicationDB_49B5873C.arn}"]}}, "aws_backup_vault": {"backup_vault": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/backup_vault", "uniqueId": "backup_vault"}}, "name": "rds_cont_backup_vault"}}, "aws_cloudwatch_metric_alarm": {"ApplicationDB_rds_alerts_connections_alert_A2BD6A4B": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/rds_alerts/connections_alert", "uniqueId": "ApplicationDB_rds_alerts_connections_alert_A2BD6A4B"}}, "alarm_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "alarm_description": "Alerts that connections are above the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "connections_alert ${aws_db_instance.ApplicationDB_49B5873C.identifier}-high-connections", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 5, "dimensions": {"DBInstanceIdentifier": "${aws_db_instance.ApplicationDB_49B5873C.identifier}"}, "evaluation_periods": 5, "metric_name": "DatabaseConnections", "namespace": "AWS/RDS", "ok_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "period": 60, "statistic": "Average", "threshold": 200}, "ApplicationDB_rds_alerts_cpu_alert_66800237": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/rds_alerts/cpu_alert", "uniqueId": "ApplicationDB_rds_alerts_cpu_alert_66800237"}}, "alarm_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "alarm_description": "Alerts that CPU utilization has passed the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "cpu_alert ${aws_db_instance.ApplicationDB_49B5873C.identifier}-high-cpu-utilization", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 5, "dimensions": {"DBInstanceIdentifier": "${aws_db_instance.ApplicationDB_49B5873C.identifier}"}, "evaluation_periods": 5, "metric_name": "CPUUtilization", "namespace": "AWS/RDS", "ok_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "period": 60, "statistic": "Average", "threshold": 80}, "ApplicationDB_rds_alerts_disk_queue_depth_alert_B2F1E02B": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/rds_alerts/disk_queue_depth_alert", "uniqueId": "ApplicationDB_rds_alerts_disk_queue_depth_alert_B2F1E02B"}}, "alarm_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "alarm_description": "Alerts that disk queue depth is above the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "disk_queue_depth_alert ${aws_db_instance.ApplicationDB_49B5873C.identifier}-high-disk-queue-depth", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 5, "dimensions": {"DBInstanceIdentifier": "${aws_db_instance.ApplicationDB_49B5873C.identifier}"}, "evaluation_periods": 5, "metric_name": "<PERSON>sk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "AWS/RDS", "ok_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "period": 60, "statistic": "Average", "threshold": 15}, "ApplicationDB_rds_alerts_free_storage_alert_69D74F80": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/rds_alerts/free_storage_alert", "uniqueId": "ApplicationDB_rds_alerts_free_storage_alert_69D74F80"}}, "alarm_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "alarm_description": "Alerts that free storage is below the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "free_storage_alert ${aws_db_instance.ApplicationDB_49B5873C.identifier}-low-storage", "comparison_operator": "LessThanThreshold", "datapoints_to_alarm": 5, "dimensions": {"DBInstanceIdentifier": "${aws_db_instance.ApplicationDB_49B5873C.identifier}"}, "evaluation_periods": 5, "metric_name": "FreeStorageSpace", "namespace": "AWS/RDS", "ok_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "period": 60, "statistic": "Average", "threshold": 107374182400}, "ApplicationDB_rds_alerts_freeable_memory_alert_B2286655": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/rds_alerts/freeable_memory_alert", "uniqueId": "ApplicationDB_rds_alerts_freeable_memory_alert_B2286655"}}, "alarm_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "alarm_description": "Alerts that freeable memory is below the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "freeable_memory_alert ${aws_db_instance.ApplicationDB_49B5873C.identifier}-low-memory", "comparison_operator": "LessThanThreshold", "datapoints_to_alarm": 5, "dimensions": {"DBInstanceIdentifier": "${aws_db_instance.ApplicationDB_49B5873C.identifier}"}, "evaluation_periods": 5, "metric_name": "Freeable<PERSON><PERSON><PERSON>", "namespace": "AWS/RDS", "ok_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "period": 60, "statistic": "Average", "threshold": 4294967296}, "ApplicationDB_rds_alerts_total_iops_critical_alert_588C0921": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/rds_alerts/total_iops_critical_alert", "uniqueId": "ApplicationDB_rds_alerts_total_iops_critical_alert_588C0921"}}, "alarm_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "alarm_description": "Alerts that Total IOPs is above the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "total_iops_critical_alert ${aws_db_instance.ApplicationDB_49B5873C.identifier}-high-TotalIOPS", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 5, "evaluation_periods": 5, "metric_query": [{"id": "read_iops", "metric": {"dimensions": {"DBInstanceIdentifier": "${aws_db_instance.ApplicationDB_49B5873C.identifier}"}, "metric_name": "ReadIOPS", "namespace": "AWS/RDS", "period": 60, "stat": "Average"}, "return_data": false}, {"id": "write_iops", "metric": {"dimensions": {"DBInstanceIdentifier": "${aws_db_instance.ApplicationDB_49B5873C.identifier}"}, "metric_name": "WriteIOPS", "namespace": "AWS/RDS", "period": 60, "stat": "Average"}, "return_data": false}, {"expression": "read_iops + write_iops", "id": "total_iops", "return_data": true}], "ok_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "threshold": 10500}, "ApplicationDB_rds_alerts_total_iops_warning_alert_2AC1C40D": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/rds_alerts/total_iops_warning_alert", "uniqueId": "ApplicationDB_rds_alerts_total_iops_warning_alert_2AC1C40D"}}, "alarm_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "alarm_description": "Alerts that Total IOPs is above the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "total_iops_warning_alert ${aws_db_instance.ApplicationDB_49B5873C.identifier}-high-TotalIOPS", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 5, "evaluation_periods": 5, "metric_query": [{"id": "read_iops", "metric": {"dimensions": {"DBInstanceIdentifier": "${aws_db_instance.ApplicationDB_49B5873C.identifier}"}, "metric_name": "ReadIOPS", "namespace": "AWS/RDS", "period": 60, "stat": "Average"}, "return_data": false}, {"id": "write_iops", "metric": {"dimensions": {"DBInstanceIdentifier": "${aws_db_instance.ApplicationDB_49B5873C.identifier}"}, "metric_name": "WriteIOPS", "namespace": "AWS/RDS", "period": 60, "stat": "Average"}, "return_data": false}, {"expression": "read_iops + write_iops", "id": "total_iops", "return_data": true}], "ok_actions": ["${data.aws_sns_topic.ApplicationDB_rds_alerts_rds_alert_sns_topic_9119D76D.arn}"], "threshold": 8000}, "NhtsaDB_rds_alerts_connections_alert_904A65CC": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/rds_alerts/connections_alert", "uniqueId": "NhtsaDB_rds_alerts_connections_alert_904A65CC"}}, "alarm_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "alarm_description": "Alerts that connections are above the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "connections_alert ${aws_db_instance.NhtsaDB_F7136F88.identifier}-high-connections", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 5, "dimensions": {"DBInstanceIdentifier": "${aws_db_instance.NhtsaDB_F7136F88.identifier}"}, "evaluation_periods": 5, "metric_name": "DatabaseConnections", "namespace": "AWS/RDS", "ok_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "period": 60, "statistic": "Average", "threshold": 100}, "NhtsaDB_rds_alerts_cpu_alert_9F53DC29": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/rds_alerts/cpu_alert", "uniqueId": "NhtsaDB_rds_alerts_cpu_alert_9F53DC29"}}, "alarm_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "alarm_description": "Alerts that CPU utilization has passed the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "cpu_alert ${aws_db_instance.NhtsaDB_F7136F88.identifier}-high-cpu-utilization", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 5, "dimensions": {"DBInstanceIdentifier": "${aws_db_instance.NhtsaDB_F7136F88.identifier}"}, "evaluation_periods": 5, "metric_name": "CPUUtilization", "namespace": "AWS/RDS", "ok_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "period": 60, "statistic": "Average", "threshold": 80}, "NhtsaDB_rds_alerts_disk_queue_depth_alert_D18100F1": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/rds_alerts/disk_queue_depth_alert", "uniqueId": "NhtsaDB_rds_alerts_disk_queue_depth_alert_D18100F1"}}, "alarm_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "alarm_description": "Alerts that disk queue depth is above the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "disk_queue_depth_alert ${aws_db_instance.NhtsaDB_F7136F88.identifier}-high-disk-queue-depth", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 5, "dimensions": {"DBInstanceIdentifier": "${aws_db_instance.NhtsaDB_F7136F88.identifier}"}, "evaluation_periods": 5, "metric_name": "<PERSON>sk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "AWS/RDS", "ok_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "period": 60, "statistic": "Average", "threshold": 5}, "NhtsaDB_rds_alerts_free_storage_alert_56130ED5": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/rds_alerts/free_storage_alert", "uniqueId": "NhtsaDB_rds_alerts_free_storage_alert_56130ED5"}}, "alarm_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "alarm_description": "Alerts that free storage is below the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "free_storage_alert ${aws_db_instance.NhtsaDB_F7136F88.identifier}-low-storage", "comparison_operator": "LessThanThreshold", "datapoints_to_alarm": 5, "dimensions": {"DBInstanceIdentifier": "${aws_db_instance.NhtsaDB_F7136F88.identifier}"}, "evaluation_periods": 5, "metric_name": "FreeStorageSpace", "namespace": "AWS/RDS", "ok_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "period": 60, "statistic": "Average", "threshold": 10737418240}, "NhtsaDB_rds_alerts_freeable_memory_alert_2900FB16": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/rds_alerts/freeable_memory_alert", "uniqueId": "NhtsaDB_rds_alerts_freeable_memory_alert_2900FB16"}}, "alarm_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "alarm_description": "Alerts that freeable memory is below the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "freeable_memory_alert ${aws_db_instance.NhtsaDB_F7136F88.identifier}-low-memory", "comparison_operator": "LessThanThreshold", "datapoints_to_alarm": 5, "dimensions": {"DBInstanceIdentifier": "${aws_db_instance.NhtsaDB_F7136F88.identifier}"}, "evaluation_periods": 5, "metric_name": "Freeable<PERSON><PERSON><PERSON>", "namespace": "AWS/RDS", "ok_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "period": 60, "statistic": "Average", "threshold": 268435456}, "NhtsaDB_rds_alerts_total_iops_alert_84180F0A": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/rds_alerts/total_iops_alert", "uniqueId": "NhtsaDB_rds_alerts_total_iops_alert_84180F0A"}}, "alarm_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "alarm_description": "Alerts that Total IOPs is above the defined threshold \nRunbook: https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Runbook-Database-alerts-debugging_suempUOd", "alarm_name": "total_iops_alert ${aws_db_instance.NhtsaDB_F7136F88.identifier}-high-TotalIOPS", "comparison_operator": "GreaterThanOrEqualToThreshold", "datapoints_to_alarm": 5, "evaluation_periods": 5, "metric_query": [{"id": "read_iops", "metric": {"dimensions": {"DBInstanceIdentifier": "${aws_db_instance.NhtsaDB_F7136F88.identifier}"}, "metric_name": "ReadIOPS", "namespace": "AWS/RDS", "period": 60, "stat": "Average"}, "return_data": false}, {"id": "write_iops", "metric": {"dimensions": {"DBInstanceIdentifier": "${aws_db_instance.NhtsaDB_F7136F88.identifier}"}, "metric_name": "WriteIOPS", "namespace": "AWS/RDS", "period": 60, "stat": "Average"}, "return_data": false}, {"expression": "read_iops + write_iops", "id": "total_iops", "return_data": true}], "ok_actions": ["${data.aws_sns_topic.NhtsaDB_rds_alerts_rds_alert_sns_topic_35151DFC.arn}"], "threshold": 500}}, "aws_db_instance": {"ApplicationDB_49B5873C": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/ApplicationDB", "uniqueId": "ApplicationDB_49B5873C"}}, "allocated_storage": 750, "apply_immediately": true, "auto_minor_version_upgrade": false, "db_name": "postgres", "db_subnet_group_name": "${aws_db_subnet_group.ApplicationDB_SubnetGroup_18AD770F.name}", "deletion_protection": true, "enabled_cloudwatch_logs_exports": ["postgresql"], "engine": "postgres", "engine_version": "16.6", "identifier": "terraform-20210203043436823600000002", "instance_class": "db.m5.xlarge", "maintenance_window": "Mon:07:00-Mon:08:00", "max_allocated_storage": 2500, "multi_az": true, "parameter_group_name": "${aws_db_parameter_group.ApplicationDB_ParameterGroup-postgres16-terraform-20210203043436823600000002_A3F17461.name}", "password": "${data.aws_secretsmanager_secret_version.LegacyAppDBPasswordSecretValue.secret_string}", "port": 5432, "skip_final_snapshot": true, "storage_type": "gp3", "username": "postgres"}, "ApplicationDB_ApplicationDBReadonlyReplica_CF83BAF4": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/ApplicationDBReadonlyReplica", "uniqueId": "ApplicationDB_ApplicationDBReadonlyReplica_CF83BAF4"}}, "allocated_storage": 750, "identifier": "default-postgres-replica-20220913061706610700000001", "instance_class": "db.t3.medium", "max_allocated_storage": 2500, "parameter_group_name": "${aws_db_instance.ApplicationDB_49B5873C.parameter_group_name}", "replicate_source_db": "${aws_db_instance.ApplicationDB_49B5873C.identifier}", "skip_final_snapshot": true, "storage_type": "gp3"}, "NhtsaDB_F7136F88": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/NhtsaDB", "uniqueId": "NhtsaDB_F7136F88"}}, "allocated_storage": 20, "apply_immediately": false, "auto_minor_version_upgrade": false, "db_subnet_group_name": "${aws_db_subnet_group.NhtsaDB_SubnetGroup_4E1F527E.name}", "deletion_protection": true, "enabled_cloudwatch_logs_exports": ["error"], "engine": "sqlserver-ex", "engine_version": "15.00.4073.23.v1", "identifier": "prod-production-nhtsa", "instance_class": "db.t3.small", "license_model": "license-included", "maintenance_window": "Mon:07:00-Mon:08:00", "max_allocated_storage": 100, "option_group_name": "${aws_db_option_group.NhtsaDB_OptionGroup_D2222933.name}", "password": "${aws_secretsmanager_secret_version.NhtsaDB_SecretValue_EBD7DE9E.secret_string}", "port": 1433, "skip_final_snapshot": true, "storage_type": "gp2", "username": "sa", "vpc_security_group_ids": ["${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"]}}, "aws_db_option_group": {"NhtsaDB_OptionGroup_D2222933": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/OptionGroup", "uniqueId": "NhtsaDB_OptionGroup_D2222933"}}, "engine_name": "sqlserver-ex", "major_engine_version": "15.00", "name": "prod-production-nhtsa", "option": [{"option_name": "SQLSERVER_BACKUP_RESTORE", "option_settings": [{"name": "IAM_ROLE_ARN", "value": "${aws_iam_role.NhtsaDB_prod-production-nhtsa_Role_59AF32E4.arn}"}]}]}}, "aws_db_parameter_group": {"ApplicationDB_ParameterGroup-postgres16-terraform-20210203043436823600000002_A3F17461": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/ParameterGroup-postgres16-terraform-20210203043436823600000002", "uniqueId": "ApplicationDB_ParameterGroup-postgres16-terraform-20210203043436823600000002_A3F17461"}}, "family": "postgres16", "name": "terraform-20210203043436823600000002-postgres16", "parameter": [{"name": "log_min_duration_statement", "value": "100"}, {"name": "rds.log_retention_period", "value": "10080"}, {"name": "log_statement", "value": "ddl"}, {"name": "rds.force_ssl", "value": "0"}]}}, "aws_db_subnet_group": {"ApplicationDB_SubnetGroup_18AD770F": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/SubnetGroup", "uniqueId": "ApplicationDB_SubnetGroup_18AD770F"}}, "name": "terraform-20210203043434932700000001", "subnet_ids": ["${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"]}, "FmcsaDB_SubnetGroup_65A2DF90": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/FmcsaDB/SubnetGroup", "uniqueId": "FmcsaDB_SubnetGroup_65A2DF90"}}, "description": "Subnet group for FMCSA aurora DB", "name": "terraform-20220801185629523200000001", "subnet_ids": ["${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"], "tags": {"Name": "fmcsa-aurora-db-cluster"}}, "NhtsaDB_SubnetGroup_4E1F527E": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/SubnetGroup", "uniqueId": "NhtsaDB_SubnetGroup_4E1F527E"}}, "name": "prod-production-nhtsa", "subnet_ids": ["${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"]}}, "aws_iam_policy": {"NhtsaDB_Policy_CF40B012": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/Policy", "uniqueId": "NhtsaDB_Policy_CF40B012"}}, "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": [\n        \"s3:GetBucketLocation\",\n        \"s3:ListBucket\"\n      ],\n      \"Resource\": \"arn:aws:s3:::cloud.nirvanatech.com\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:AbortMultipartUpload\",\n        \"s3:GetObject\",\n        \"s3:ListMultipartUploadParts\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::cloud.nirvanatech.com/private/nhtsa-db-backup/*\",\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}}, "aws_iam_role": {"NhtsaDB_prod-production-nhtsa_Role_59AF32E4": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/prod-production-nhtsa/Role", "uniqueId": "NhtsaDB_prod-production-nhtsa_Role_59AF32E4"}}, "assume_role_policy": "${data.aws_iam_policy_document.rds_assume_role_policy.json}", "name": "prod-production-nhtsa"}, "backup_role": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/backup_role", "uniqueId": "backup_role"}}, "assume_role_policy": "${data.aws_iam_policy_document.backup_policy.json}", "name": "rds-cont-backup-role"}}, "aws_iam_role_policy_attachment": {"NhtsaDB_PolicyAttachment_87D866C2": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/PolicyAttachment", "uniqueId": "NhtsaDB_PolicyAttachment_87D866C2"}}, "policy_arn": "${aws_iam_policy.NhtsaDB_Policy_CF40B012.arn}", "role": "${aws_iam_role.NhtsaDB_prod-production-nhtsa_Role_59AF32E4.name}"}, "backup_role_policy_attachment": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/backup_role_policy_attachment", "uniqueId": "backup_role_policy_attachment"}}, "policy_arn": "arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup", "role": "${aws_iam_role.backup_role.name}"}}, "aws_rds_cluster": {"FmcsaDB_FmcsaDBCluster_72173478": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/FmcsaDB/FmcsaDBCluster", "uniqueId": "FmcsaDB_FmcsaDBCluster_72173478"}}, "cluster_identifier": "fmcsa-aurora-db-cluster", "database_name": "postgres", "db_cluster_parameter_group_name": "default.aurora-postgresql16", "db_subnet_group_name": "${aws_db_subnet_group.FmcsaDB_SubnetGroup_65A2DF90.name}", "engine": "aurora-postgresql", "engine_mode": "provisioned", "engine_version": "16.8", "master_password": "${data.aws_secretsmanager_secret_version.FmcsaDB_FmcsaDBPasswordSecretVersion_DA59D801.secret_string}", "master_username": "postgres", "serverlessv2_scaling_configuration": {"max_capacity": 12, "min_capacity": 4}, "skip_final_snapshot": false, "tags": {"ManagedBy": "InsuredEng"}, "vpc_security_group_ids": ["${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"]}}, "aws_rds_cluster_instance": {"FmcsaDB_FmcsaDBInstance1_7F19EEF2": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/FmcsaDB/FmcsaDBInstance1", "uniqueId": "FmcsaDB_FmcsaDBInstance1_7F19EEF2"}}, "cluster_identifier": "${aws_rds_cluster.FmcsaDB_FmcsaDBCluster_72173478.cluster_identifier}", "engine": "aurora-postgresql", "identifier": "fmcsa-aurora-db", "instance_class": "db.serverless", "promotion_tier": 1, "tags": {"ManagedBy": "InsuredEng"}}, "FmcsaDB_FmcsaDBInstance2_2E92F8F0": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/FmcsaDB/FmcsaDBInstance2", "uniqueId": "FmcsaDB_FmcsaDBInstance2_2E92F8F0"}}, "cluster_identifier": "${aws_rds_cluster.FmcsaDB_FmcsaDBCluster_72173478.cluster_identifier}", "engine": "aurora-postgresql", "identifier": "fmcsa-aurora-db-external-services", "instance_class": "db.serverless", "promotion_tier": 1, "tags": {"ManagedBy": "InsuredEng"}}}, "aws_secretsmanager_secret": {"ApplicationDB_Secret_app-db-ds-user-password_A8D8F226": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/Secret_app-db-ds-user-password", "uniqueId": "ApplicationDB_Secret_app-db-ds-user-password_A8D8F226"}}, "name": "app-db-ds-user-password"}, "ApplicationDB_Secret_app-db-migrator-password_3AAB7A7F": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/Secret_app-db-migrator-password", "uniqueId": "ApplicationDB_Secret_app-db-migrator-password_3AAB7A7F"}}, "name": "app-db-migrator-password"}, "ApplicationDB_Secret_app-db-readonly-password_761953E6": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/Secret_app-db-readonly-password", "uniqueId": "ApplicationDB_Secret_app-db-readonly-password_761953E6"}}, "name": "app-db-readonly-password"}, "ApplicationDB_Secret_app-db-readwrite-password_AFF5A8C5": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/Secret_app-db-readwrite-password", "uniqueId": "ApplicationDB_Secret_app-db-readwrite-password_AFF5A8C5"}}, "name": "app-db-readwrite-password"}, "NhtsaDB_Secret_D38DDBBB": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/Secret", "uniqueId": "NhtsaDB_Secret_D38DDBBB"}}, "name": "prod-production-nhtsa"}}, "aws_secretsmanager_secret_version": {"ApplicationDB_SecretValue_app-db-ds-user-password_4F3AA105": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/SecretValue_app-db-ds-user-password", "uniqueId": "ApplicationDB_SecretValue_app-db-ds-user-password_4F3AA105"}}, "secret_id": "${aws_secretsmanager_secret.ApplicationDB_Secret_app-db-ds-user-password_A8D8F226.id}", "secret_string": "${random_password.ApplicationDB_Password_app-db-ds-user-password_1AC329F6.result}"}, "ApplicationDB_SecretValue_app-db-migrator-password_3807CF8B": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/SecretValue_app-db-migrator-password", "uniqueId": "ApplicationDB_SecretValue_app-db-migrator-password_3807CF8B"}}, "secret_id": "${aws_secretsmanager_secret.ApplicationDB_Secret_app-db-migrator-password_3AAB7A7F.id}", "secret_string": "${random_password.ApplicationDB_Password_app-db-migrator-password_66F9C8A7.result}"}, "ApplicationDB_SecretValue_app-db-readonly-password_D8C00E55": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/SecretValue_app-db-readonly-password", "uniqueId": "ApplicationDB_SecretValue_app-db-readonly-password_D8C00E55"}}, "secret_id": "${aws_secretsmanager_secret.ApplicationDB_Secret_app-db-readonly-password_761953E6.id}", "secret_string": "${random_password.ApplicationDB_Password_app-db-readonly-password_128EBAB7.result}"}, "ApplicationDB_SecretValue_app-db-readwrite-password_47B2BD30": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/SecretValue_app-db-readwrite-password", "uniqueId": "ApplicationDB_SecretValue_app-db-readwrite-password_47B2BD30"}}, "secret_id": "${aws_secretsmanager_secret.ApplicationDB_Secret_app-db-readwrite-password_AFF5A8C5.id}", "secret_string": "${random_password.ApplicationDB_Password_app-db-readwrite-password_30F635AA.result}"}, "NhtsaDB_SecretValue_EBD7DE9E": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/SecretValue", "uniqueId": "NhtsaDB_SecretValue_EBD7DE9E"}}, "secret_id": "${aws_secretsmanager_secret.NhtsaDB_Secret_D38DDBBB.id}", "secret_string": "${random_password.NhtsaDB_Password_BB580154.result}"}}, "postgresql_grant": {"ApplicationDB_migrator_role_grant_create_on_db_47E3D13F": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/migrator_role_grant_create_on_db", "uniqueId": "ApplicationDB_migrator_role_grant_create_on_db_47E3D13F"}}, "database": "${aws_db_instance.ApplicationDB_49B5873C.db_name}", "object_type": "database", "privileges": ["CREATE"], "provider": "postgresql", "role": "${postgresql_role.ApplicationDB_migrator_role_CF52D44D.name}"}, "ApplicationDB_migrator_role_grant_create_on_public_F3C04CBD": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/migrator_role_grant_create_on_public", "uniqueId": "ApplicationDB_migrator_role_grant_create_on_public_F3C04CBD"}}, "database": "${aws_db_instance.ApplicationDB_49B5873C.db_name}", "object_type": "schema", "privileges": ["CREATE"], "provider": "postgresql", "role": "${postgresql_role.ApplicationDB_migrator_role_CF52D44D.name}", "schema": "public"}}, "postgresql_grant_role": {"ApplicationDB_migrator_role_grant_read_55714E9C": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/migrator_role_grant_read", "uniqueId": "ApplicationDB_migrator_role_grant_read_55714E9C"}}, "grant_role": "pg_read_all_data", "provider": "postgresql", "role": "${postgresql_role.ApplicationDB_migrator_role_CF52D44D.name}", "with_admin_option": true}, "ApplicationDB_migrator_role_grant_write_72CBFEDC": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/migrator_role_grant_write", "uniqueId": "ApplicationDB_migrator_role_grant_write_72CBFEDC"}}, "grant_role": "pg_write_all_data", "provider": "postgresql", "role": "${postgresql_role.ApplicationDB_migrator_role_CF52D44D.name}", "with_admin_option": true}}, "postgresql_role": {"ApplicationDB_migrator_role_CF52D44D": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/migrator_role", "uniqueId": "ApplicationDB_migrator_role_CF52D44D"}}, "create_database": true, "create_role": true, "lifecycle": {"ignore_changes": ["roles"]}, "login": true, "name": "migrator", "password": "${aws_secretsmanager_secret_version.ApplicationDB_SecretValue_app-db-migrator-password_3807CF8B.secret_string}", "provider": "postgresql"}}, "random_password": {"ApplicationDB_Password_app-db-ds-user-password_1AC329F6": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/Password_app-db-ds-user-password", "uniqueId": "ApplicationDB_Password_app-db-ds-user-password_1AC329F6"}}, "keepers": {"pass_version": "1"}, "length": 40, "min_special": 5, "override_special": "!#$%^&*()-_=+[]{}<>:?", "special": true}, "ApplicationDB_Password_app-db-migrator-password_66F9C8A7": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/Password_app-db-migrator-password", "uniqueId": "ApplicationDB_Password_app-db-migrator-password_66F9C8A7"}}, "keepers": {"pass_version": "1"}, "length": 40, "min_special": 5, "override_special": "!#$%^&*()-_=+[]{}<>:?", "special": true}, "ApplicationDB_Password_app-db-readonly-password_128EBAB7": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/Password_app-db-readonly-password", "uniqueId": "ApplicationDB_Password_app-db-readonly-password_128EBAB7"}}, "keepers": {"pass_version": "1"}, "length": 40, "min_special": 5, "override_special": "!#$%^&*()-_=+[]{}<>:?", "special": true}, "ApplicationDB_Password_app-db-readwrite-password_30F635AA": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/ApplicationDB/Password_app-db-readwrite-password", "uniqueId": "ApplicationDB_Password_app-db-readwrite-password_30F635AA"}}, "keepers": {"pass_version": "1"}, "length": 40, "min_special": 5, "override_special": "!#$%^&*()-_=+[]{}<>:?", "special": true}, "NhtsaDB_Password_BB580154": {"//": {"metadata": {"path": "default-production-storage-rds-stack-us-east-2/NhtsaDB/Password", "uniqueId": "NhtsaDB_Password_BB580154"}}, "keepers": {"pass_version": "1"}, "length": 40, "min_lower": 2, "min_numeric": 2, "min_special": 2, "min_upper": 2, "override_special": "!#$%^&*()-_=+[]{}<>:?", "special": true}}}, "terraform": {"backend": {"s3": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/production/default-production-storage-rds-stack-us-east-2.json", "region": "us-east-2"}}, "required_providers": {"aws": {"source": "aws", "version": "5.88.0"}, "postgresql": {"source": "cyrilgdn/postgresql", "version": "1.25.0"}, "random": {"source": "hashicorp/random", "version": "3.6.0"}}, "required_version": "1.7.5"}}