// src/infra/common/lib/passwords.ts
import { Token } from 'cdktf'
import { Construct } from 'constructs'

import { SecretsmanagerSecret } from '@cdktf/provider-aws/lib/secretsmanager-secret'
import { SecretsmanagerSecretVersion } from '@cdktf/provider-aws/lib/secretsmanager-secret-version'
import { PasswordConfig } from '@cdktf/provider-random/lib/password'
import { Password as RandomPassword } from '@cdktf/provider-random/lib/password'

/**
 * Strategy for formatting secret names and construct IDs.
 */
export interface SecretProvisioningStrategy {
  /**
   * Formats the secret name for AWS Secrets Manager.
   */
  formatSecretName(name: string): string

  /**
   * Formats construct IDs for Terraform resources.
   * @param base - Base name (e.g., 'password', 'secret', 'secret_value')
   * @param identifier - Unique identifier for this secret
   */
  formatConstructId(base: string, identifier: string): string
}

/**
 * Options for provisioning a random password as a secret.
 */
export interface ProvisionPasswordOptions {
  /** Name identifier for the secret */
  secretName: string
  /** Password generation configuration */
  config: PasswordConfig
  /** Optional strategy for naming conventions */
  strategy?: SecretProvisioningStrategy
}

/**
 * Predefined strategies for common naming patterns.
 */
const DEFAULT_PROVISION_STRATEGY = {
  formatSecretName: (name: string) => name,
  formatConstructId: (base: string, id: string) => `${base}_${id}`,
}

/**
 * Provisions a random password and stores it in AWS Secrets Manager.
 *
 * This function creates a secure random password, stores it in AWS Secrets Manager,
 * and returns a reference that can be used by other Terraform resources.
 *
 * ## Security Features
 *
 * - **Random Generation**: Uses cryptographically secure random generation
 * - **Secrets Manager**: Stores passwords in encrypted AWS Secrets Manager
 * - **No Plaintext**: Passwords never appear in Terraform state files
 * - **Version Tracking**: Supports password rotation via keepers in config
 *
 * ## Strategy-Based Naming
 *
 * The function uses strategies to handle different naming conventions:
 * - **Default**: Simple naming for general use cases
 * - **Supabase**: Prefixed naming for organized secret management
 * - **RDS**: PascalCase construct IDs for consistency with existing patterns
 *
 * @param scope - The construct scope
 * @param options - Configuration options
 * @returns Token reference to the password for use in other resources
 */
export function provisionRandomPasswordAsSecret(
  scope: Construct,
  options: ProvisionPasswordOptions,
) {
  const strategy = options.strategy ?? DEFAULT_PROVISION_STRATEGY

  // Generate a cryptographically secure random password
  const password = new RandomPassword(
    scope,
    strategy.formatConstructId('password', options.secretName),
    options.config,
  )

  // Create a secret in AWS Secrets Manager
  const passwordSecret = new SecretsmanagerSecret(
    scope,
    strategy.formatConstructId('secret', options.secretName),
    {
      name: strategy.formatSecretName(options.secretName),
    },
  )

  // Store the password value in the secret
  const passwordSecretVersion = new SecretsmanagerSecretVersion(
    scope,
    strategy.formatConstructId('secret_value', options.secretName),
    {
      secretId: Token.asString(passwordSecret.id),
      secretString: Token.asString(password.result),
    },
  )

  return {
    arn: Token.asString(passwordSecretVersion.arn),
    value: Token.asString(passwordSecretVersion.secretString),
  }
}
