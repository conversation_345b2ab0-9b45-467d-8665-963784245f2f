/**
 * Utility functions for working with AWS ARNs.
 */
export class ArnUtils {
  /**
   * Extracts the IAM role name from a full IAM role ARN.
   *
   * Throws if the role name cannot be derived.
   */
  public static getIamRoleNameFromArn(arn: string): string {
    const roleName = arn.split('/').pop()
    if (!roleName) {
      throw new Error('Invalid ARN, Unable to extract IAM role name from ARN')
    }
    return roleName
  }
}
