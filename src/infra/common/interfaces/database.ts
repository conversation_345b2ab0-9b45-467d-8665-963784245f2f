export interface DatabaseReadonlyReplicaInfo {
  host: string
  securityGroupId: string
}

export interface Password {
  arn: string
  // TODO:(garvit): Remove `value` once all usages are migrated to arn
  /**
   * @deprecated Kept for backward compatibility. Do not use plaintext password values,
   * use the `arn` to reference the secret instead.
   */
  value: string
}

export interface DatabaseCredential {
  username: string
  password: Password
}

// A flexible map of credentials keyed by username.
// Examples: "readwrite", "readonly", "migrator", or any custom key per database.
export type DatabaseCredentials = Record<string, DatabaseCredential>

export interface DatabaseInfo {
  id: string
  name: string
  arn: string
  host: string
  port: number
  dbName: string
  // TODO:(garvit): Remove username and password once all usages are migrated to masterUserDetails
  /**
   * @deprecated Use `masterUserDetails.username` instead.
   * Represents the legacy primary/master username. For other users, use `credentials`.
   */
  username: string
  /**
   * @deprecated Use `masterUserDetails.password` instead.
   * Represents the legacy primary/master password. For other users, use `credentials`.
   */
  password: string
  masterUserDetails: {
    username: string
    password: Password
  }
  securityGroupId: string
  readonlyReplica?: DatabaseReadonlyReplicaInfo

  // Optional map of credentials for different users apart from the master user.
  credentials?: DatabaseCredentials
}

/**
 * Builds a `DatabaseInfo`, copying legacy `username` and `password` from
 * `masterUserDetails` for backward compatibility.
 */
export function newDatabaseInfo(
  info: Omit<DatabaseInfo, 'username' | 'password'>,
): DatabaseInfo {
  return {
    ...info,
    username: info.masterUserDetails.username,
    password: info.masterUserDetails.password.value,
  }
}

/**
 * Returns credentials for the given `username` from `dbInfo.credentials`.
 * @throws Error if no credentials exist for the username.
 */
export function getDBCreds(
  dbInfo: DatabaseInfo,
  username: string,
): DatabaseCredential {
  const creds = dbInfo.credentials?.[username]
  if (creds != null) {
    return creds
  }
  throw new Error(`No credentials found for username: ${username}`)
}
